{"openapi": "3.0.0", "paths": {"/inbox/v3/workspaces/{workspaceId}/notifications/bundles/search": {"post": {"operationId": "getNotificationBundles", "summary": "", "description": "Returns a list of notification bundles for a user in the given workspace.", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetNotificationBundlesRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetNotificationBundlesResponse"}}}}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/bundles/notifications": {"post": {"operationId": "getNotificationsInBundle", "summary": "", "description": "Returns the notifications within a bundle (up to a limit)", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetNotificationsInBundleRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetNotificationsInBundleResponse"}}}}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/bundles/{bundleSnapshotId}/clear": {"put": {"operationId": "clearNotificationBundle", "summary": "", "description": "Marks all the notifications within a bundle as cleared", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "bundleSnapshotId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/delete-all-workspace-data": {"delete": {"operationId": "deleteAllInboxDataForWorkspace", "summary": "", "description": "Deletes all inbox data for a given workspace", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/bundles/clear": {"put": {"operationId": "clearAllNotifications", "summary": "", "description": "Marks given bundles(s) as cleared", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClearAllNotificationsRequest"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/bundles/{bundleSnapshotId}/unclear": {"put": {"operationId": "unclearNotificationBundle", "summary": "", "description": "Marks all the notifications within a bundle as uncleared", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "bundleSnapshotId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/bundles/unclear": {"put": {"operationId": "unclearNotificationBundles", "summary": "", "description": "Marks all the notifications within a list of bundles as uncleared", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnclearNotificationsRequest"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/bundles/read": {"put": {"operationId": "markNotificationBundleRead", "summary": "", "description": "Marks all the notifications within a bundle as read", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNotificationBundleReadStatusRequest"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/bundles/unread": {"put": {"operationId": "markNotificationBundleUnread", "summary": "", "description": "Marks all the notifications within a bundle as unread", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNotificationBundleReadStatusRequest"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/bundles/snooze": {"put": {"operationId": "snoozeNotificationBundle", "summary": "", "description": "Snoozes the given notification bundle", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SnoozeNotificationBundleRequest"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/bundles/unsnooze": {"put": {"operationId": "unsnoozeNotificationBundle", "summary": "", "description": "Unsnoozes the given notification bundle", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnsnoozeNotificationBundleRequest"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications": {"post": {"operationId": "createNotificationAsync", "summary": "", "description": "Enqueues a request to creates a new notification for a user in a workspace", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNotificationRequestInput"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/legacy-to-inbox/sync": {"post": {"operationId": "syncLegacyToInboxNotificationUpdate", "summary": "", "description": "Enqueues a request to sync a legacy to inbox notification update", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LegacyToInboxNotificationUpdateRequestInput"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/bundles/stats/fetch": {"post": {"operationId": "getNotificationBundleStats", "summary": "", "description": "Retrieves the notification bundle stats for a user", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetNotificationBundleStatsRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationBundleStats"}}}}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/bundles/stats/bulk-fetch": {"post": {"operationId": "getNotificationBundleStatsBulk", "summary": "", "description": "Retrieves the notification bundle stats for a list of users", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetNotificationBundleStatsBulkRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetNotificationBundleStatsBulkResponse"}}}}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/comments/fetch": {"post": {"operationId": "getCommentsById", "summary": "", "description": "Enables fetching a collection of comments by ID", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCommentsByIdRequest"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Notifications"]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/stats/pushNotifications/received": {"post": {"operationId": "collectReceivedPushNotificationStats", "summary": "", "description": "Collect stats of received push notifications", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushNotificationStatsRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushNotificationStatsResponse"}}}}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/stats/pushNotifications/log": {"post": {"operationId": "logPushNotificationStats", "summary": "", "description": "Helper endpoint to log push notification metrics and messages", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushNotificationLogRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushNotificationStatsResponse"}}}}}, "tags": ["Notifications"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notificationResources": {"post": {"operationId": "createOrUpdateNotificationResourceStoreAsync", "summary": "", "description": "Enqueues a request to update the notification resource store for a given ern", "parameters": [{"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNotificationResourceStoreRequestInput"}}}}, "responses": {"200": {"description": ""}}, "tags": ["NotificationResources"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/comment-engagement": {"post": {"operationId": "getCommentEngagements", "summary": "", "description": "Returns a list of comment engagements for the current user.", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCommentEngagementsRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCommentEngagementsResponse"}}}}}, "tags": ["Comment Engagement"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/comment-engagement/dismiss": {"post": {"operationId": "dismissCommentEngagement", "summary": "", "description": "Dismiss a single comment engagement.", "parameters": [{"name": "x-client-request-token", "in": "header", "description": "To<PERSON> passed by the client to correlate server events with client requests.", "schema": {"type": "string"}}, {"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DismissCommentEngagementRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["Comment Engagement"], "security": [{"bearer": []}]}}, "/inbox/v3/workspaces/{workspaceId}/notifications/customization": {"post": {"operationId": "getNotificationCustomization", "summary": "", "description": "Returns the current user's notification customization(s) for a given workspace.", "parameters": [{"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetNotificationCustomizationResponse"}}}}}, "tags": ["Notification Customization"], "security": [{"bearer": []}]}, "put": {"operationId": "updateNotificationCustomization", "summary": "", "description": "Updates the current user's notification customization(s) for a given workspace.", "parameters": [{"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNotificationCustomizationRequest"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Notification Customization"], "security": [{"bearer": []}]}, "delete": {"operationId": "deleteNotificationCustomization", "summary": "", "description": "Deletes the current user's notification customization(s) for a given workspace.", "parameters": [{"name": "workspaceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteNotificationCustomizationRequest"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Notification Customization"], "security": [{"bearer": []}]}}, "/inbox/v3/health": {"get": {"operationId": "HealthController_health", "parameters": [], "responses": {"200": {"description": "", "content": {"text/html": {"schema": {"type": "string"}}}}}, "tags": ["Health"]}}, "/inbox/v3/health/detail": {"get": {"operationId": "HealthController_healthDetail", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthDetailResponse"}}}}}, "tags": ["Health"]}}}, "info": {"title": "Inbox Service", "description": "Handles notifications and notification bundles for Inbox 3.0", "version": "1.0", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"NotificationStatus": {"type": "string", "enum": ["cleared", "uncleared", "snoozed", "unauthorized"]}, "BundleStrategy": {"type": "string", "default": "re", "enum": ["re", "at"]}, "BundleType": {"type": "string", "description": "The type of the bundle.  Note that the bundle type can change after notifications are added/removed. If the bundle contains any notifications related to messages, it will have a bundleType of 'messages'; otherwise, the bundleType is 'activity'", "enum": ["messages", "activity", "all"]}, "DateTimeRange": {"type": "object", "properties": {"start": {"type": "string"}, "end": {"type": "string"}}}, "ResourceType": {"type": "string", "enum": ["access-request", "approval", "attachment", "card", "category", "chat-view", "checklist-item", "clip", "comment", "custom-field", "dashboard", "doc", "github-commit", "page", "project", "reminder", "subcategory", "tag", "task", "timesheet", "user", "user-group", "view", "whiteboard", "workspace"]}, "FilterParameters": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/NotificationStatus"}, "bundleStrategy": {"$ref": "#/components/schemas/BundleStrategy"}, "bundleType": {"$ref": "#/components/schemas/BundleType"}, "dateRange": {"description": "A non-inclusive date range", "allOf": [{"$ref": "#/components/schemas/DateTimeRange"}]}, "assignedToMe": {"type": "boolean", "description": "If true, results will only include notification (bundles) that include an assignment to the user"}, "mentioned": {"type": "boolean", "description": "If true, results will only include notification (bundles) that include a mention of the user"}, "unread": {"type": "boolean", "description": "If true, results will only include notification (bundles) that have at least one unread notification"}, "saved": {"type": "boolean", "description": "If true, results will only saved bundle (Remind<PERSON> with no due date)"}, "priority": {"type": "number"}, "entityTypes": {"type": "array", "items": {"$ref": "#/components/schemas/ResourceType"}}, "rootEntityResourceName": {"type": "string", "description": "The root entity to search for bundles", "example": "clickup:task:333:a1b2c3"}, "wasMentionedBy": {"description": "Results only include bundles where the request userId has been @mentioned by a userId in this list", "type": "array", "items": {"type": "string"}}, "commentedBy": {"description": "Results only include bundles where the request userId has received a comment notification by a userId in this list", "type": "array", "items": {"type": "string"}}, "assignedByUserIds": {"description": "Array of userIds that assigned comment or task to requester", "type": "array", "items": {"type": "string"}}}}, "SortDirection": {"type": "string", "enum": ["ascending", "descending"]}, "SortParameters": {"type": "object", "properties": {"direction": {"$ref": "#/components/schemas/SortDirection"}}, "required": ["direction"]}, "PaginationParameters": {"type": "object", "properties": {"nextCursor": {"type": "string"}, "limit": {"type": "number"}}}, "GetNotificationBundlesRequest": {"type": "object", "properties": {"filteredBy": {"$ref": "#/components/schemas/FilterParameters"}, "sortedBy": {"$ref": "#/components/schemas/SortParameters"}, "groupedBy": {"type": "string", "enum": ["time-range"]}, "pagination": {"$ref": "#/components/schemas/PaginationParameters"}, "needsMemberMap": {"type": "boolean"}}}, "CommentEntity": {"type": "object", "properties": {"entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "id": {"type": "string"}, "type": {"type": "string"}, "permissions": {"type": "object"}, "name": {"type": "string"}, "author": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "createdAt": {"format": "date-time", "type": "string"}, "commentPreview": {"type": "array", "items": {"type": "string"}}, "commentThreadParent": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "via": {"type": "string"}, "emailSubject": {"type": "string"}, "commentByEmail": {"type": "string"}}, "required": ["entityResourceName", "id", "type", "author", "createdAt", "commentPreview"]}, "ResourceStatus": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "color": {"type": "string", "nullable": true}}, "required": ["id", "status", "color"]}, "Category": {"type": "object", "properties": {"entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "id": {"type": "string"}, "type": {"type": "string", "enum": ["category"]}, "permissions": {"type": "object"}, "name": {"type": "string"}}, "required": ["entityResourceName", "id", "type"]}, "Project": {"type": "object", "properties": {"entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "id": {"type": "string"}, "type": {"type": "string", "enum": ["project"]}, "permissions": {"type": "object"}, "name": {"type": "string"}}, "required": ["entityResourceName", "id", "type"]}, "Subcategory": {"type": "object", "properties": {"entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "id": {"type": "string"}, "type": {"type": "string", "enum": ["subcategory"]}, "permissions": {"type": "object"}, "name": {"type": "string"}, "category": {"$ref": "#/components/schemas/Category"}, "project": {"$ref": "#/components/schemas/Project"}}, "required": ["entityResourceName", "id", "type", "category", "project"]}, "ResourceLocation": {"type": "object", "properties": {"subcategory": {"$ref": "#/components/schemas/Subcategory"}, "category": {"$ref": "#/components/schemas/Category"}, "project": {"$ref": "#/components/schemas/Project"}}, "required": ["subcategory", "category", "project"]}, "TaskLocation": {"type": "object", "properties": {"subcategory": {"$ref": "#/components/schemas/Subcategory"}, "category": {"$ref": "#/components/schemas/Category"}, "project": {"$ref": "#/components/schemas/Project"}, "additionalLists": {"type": "array", "items": {"$ref": "#/components/schemas/Subcategory"}}}, "required": ["subcategory", "category", "project"]}, "TaskStatus": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "color": {"type": "string", "nullable": true}}, "required": ["id", "status", "color"]}, "TaskEntity": {"type": "object", "properties": {"entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "id": {"type": "string"}, "type": {"type": "string"}, "permissions": {"type": "object"}, "name": {"type": "string"}, "status": {"$ref": "#/components/schemas/ResourceStatus"}, "location": {"$ref": "#/components/schemas/ResourceLocation"}, "dueDate": {"format": "date-time", "type": "string"}, "isSubtask": {"type": "boolean"}, "customType": {"type": "number"}, "parentTaskId": {"type": "string"}}, "required": ["entityResourceName", "id", "type", "status", "location"]}, "DocumentLocation": {"type": "object", "properties": {"parent": {"oneOf": [{"$ref": "#/components/schemas/Subcategory"}, {"$ref": "#/components/schemas/HierarchyItem"}]}}, "required": ["parent"]}, "DocumentEntity": {"type": "object", "properties": {"entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "id": {"type": "string"}, "type": {"type": "string"}, "permissions": {"type": "object"}, "name": {"type": "string"}, "location": {"$ref": "#/components/schemas/DocumentLocation"}}, "required": ["entityResourceName", "id", "type", "location"]}, "PageEntity": {"type": "object", "properties": {"entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "id": {"type": "string"}, "type": {"type": "string"}, "permissions": {"type": "object"}, "name": {"type": "string"}, "document": {"$ref": "#/components/schemas/DocumentEntity"}}, "required": ["entityResourceName", "id", "type", "document"]}, "HierarchyItem": {"type": "object", "properties": {"entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "id": {"type": "string"}, "type": {"type": "string"}, "permissions": {"type": "object"}, "name": {"type": "string"}}, "required": ["entityResourceName", "id", "type"]}, "EntityResourceName": {"type": "object", "properties": {}}, "ClickUpResourceName": {"type": "object", "properties": {}}, "AccessRequestApproved": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["access_request_approved"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "resourceToAccess": {"type": "string", "example": "clickup:task:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "resourceToAccess"]}, "AccessRequested": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["access_requested"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "resourceToAccess": {"type": "string", "example": "clickup:task:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "resourceToAccess"]}, "UpgradeAccessRequested": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["upgrade_access_requested"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "resourceToAccess": {"type": "string", "example": "clickup:task:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "resourceToAccess"]}, "UpgradeAccessRequestApproved": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["upgrade_access_request_approved"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "resourceToAccess": {"type": "string", "example": "clickup:task:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "resourceToAccess"]}, "AddedToSubcategory": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["added_to_subcategory"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "subcategory": {"type": "object"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "subcategory"]}, "AssigneeAdded": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["assignee_add"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "assignee": {"type": "string", "example": "clickup:user::a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "assignee"]}, "AssigneeRemoved": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["assignee_rem"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "assignee": {"type": "string", "example": "clickup:user::a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "assignee"]}, "Attachments": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["attachments"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "attachments": {"type": "array", "items": {"type": "string"}}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "attachments"]}, "BitBucketCommit": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["bb_commit"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "branchName": {"type": "string"}, "repoName": {"type": "string"}, "commitUrl": {"type": "string"}, "author": {"type": "string"}, "message": {"type": "string"}, "sha": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "branchName", "repoName", "commitUrl", "author", "message", "sha"]}, "BitBucketPullRequest": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["bb_pull_request"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "pullRequestLink": {"type": "string"}, "author": {"type": "string"}, "action": {"type": "string"}, "title": {"type": "string"}, "isLinked": {"type": "boolean"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "pullRequestLink", "author", "action", "title", "isLinked"]}, "BitBucketPullUpdated": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["bb_pull_updated"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "pullRequestLink": {"type": "string"}, "author": {"type": "string"}, "action": {"type": "string"}, "title": {"type": "string"}, "isLinked": {"type": "boolean"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "pullRequestLink", "author", "action", "title", "isLinked"]}, "ChatThreadLink": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["chat_thread_link"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "roomId": {"type": "string"}, "threadId": {"type": "string"}, "chatThreadLink": {"type": "string"}, "isLinked": {"type": "boolean"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "roomId", "threadId", "chatThreadLink", "isLinked"]}, "ChecklistItemAssignee": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["checklist_item_assignee"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "checklistItemName": {"type": "string"}, "assignee": {"type": "string", "example": "clickup:user::a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "checklistItemName", "assignee"]}, "ChecklistItemResolved": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["checklist_item_resolved"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "checklistItemName": {"type": "string"}, "isResolved": {"type": "boolean"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "checklistItemName", "isResolved"]}, "ChecklistItemsAdded": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["checklist_items_added"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "checklistName": {"type": "string"}, "checklistItemNames": {"type": "array", "items": {"type": "string"}}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "checklistName", "checklistItemNames"]}, "Comment": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["comment"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "author": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "userMentions": {"example": "clickup:task:333:a1b2c3", "type": "array", "items": {"type": "string"}}, "groupMentions": {"example": "clickup:task:333:a1b2c3", "type": "array", "items": {"type": "string"}}, "assignee": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "groupAssignee": {"type": "string", "example": "clickup:task:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "author", "userMentions", "groupMentions", "assignee", "groupAssignee"]}, "CommentAssigned": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["comment_assigned"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "author": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "userMentions": {"example": "clickup:task:333:a1b2c3", "type": "array", "items": {"type": "string"}}, "groupMentions": {"example": "clickup:task:333:a1b2c3", "type": "array", "items": {"type": "string"}}, "assignee": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "groupAssignee": {"type": "string", "example": "clickup:task:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "author", "userMentions", "groupMentions", "assignee", "groupAssignee"]}, "CommentResolved": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["comment_resolved"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "author": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "userMentions": {"example": "clickup:task:333:a1b2c3", "type": "array", "items": {"type": "string"}}, "groupMentions": {"example": "clickup:task:333:a1b2c3", "type": "array", "items": {"type": "string"}}, "assignee": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "groupAssignee": {"type": "string", "example": "clickup:task:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "author", "userMentions", "groupMentions", "assignee", "groupAssignee"]}, "Content": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["content"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId"]}, "CustomField": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["custom_field"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "customField": {"type": "object"}, "values": {"type": "object"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "customField", "values"]}, "CustomType": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["custom_type"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "customType": {"type": "object"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "customType"]}, "DependencyOf": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["dependency_of"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "relatedTask": {"type": "string", "example": "clickup:task:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "relatedTask"]}, "DependsOn": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["depends_on"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "relatedTask": {"type": "string", "example": "clickup:task:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "relatedTask"]}, "DescriptionGroupTag": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["description_group_tag"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "tagId": {"type": "string"}, "content": {"type": "string"}, "groupAssignee": {"type": "string", "example": "clickup:user-group:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "tagId", "content", "groupAssignee"]}, "DescriptionTag": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["description_tag"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "tagId": {"type": "string"}, "content": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "tagId", "content"]}, "DueDate": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["due_date"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "dueDate": {"format": "date-time", "type": "string"}, "priorDueDate": {"format": "date-time", "type": "string"}, "isDueDateADateTime": {"type": "boolean"}, "isPriorDueDateADateTime": {"type": "boolean"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId"]}, "DueDateMissed": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["due_date_missed"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "taskDueDate": {"format": "date-time", "type": "string"}, "isTaskDueDateADateTime": {"type": "boolean", "description": "If the taskDueDate is a DateTime instead of just a Date"}, "taskStartDate": {"format": "date-time", "type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "taskDueDate", "isTaskDueDateADateTime", "taskStartDate"]}, "DueDateReminder": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["due_date_reminder"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "taskDueDate": {"format": "date-time", "type": "string"}, "isTaskDueDateADateTime": {"type": "boolean", "description": "If the taskDueDate is a DateTime instead of just a Date"}, "minutesAfter": {"type": "number", "description": "Minutes to wait before attempting to notify the user"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "taskDueDate", "isTaskDueDateADateTime"]}, "Duplicate": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["duplicate"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "mergedTaskId": {"type": "string"}, "mergedTaskName": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "mergedTaskId", "mergedTaskName"]}, "Duration": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["duration"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "durationInMinutes": {"type": "number"}, "priorDurationInMinutes": {"type": "number"}, "durationString": {"type": "string"}, "priorDurationString": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "durationInMinutes"]}, "FollowerAdded": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["follower_add"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "follower": {"type": "string", "example": "clickup:user::a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "follower"]}, "FollowerRemoved": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["follower_rem"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "follower": {"type": "string", "example": "clickup:user::a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "follower"]}, "GitHubCommit": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["gh_commit"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "branchName": {"type": "string"}, "commitUrl": {"type": "string"}, "author": {"type": "string"}, "sha": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "branchName", "commitUrl", "author", "sha"]}, "GitHubPullRequest": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["github_pull_request"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "pullRequestLink": {"type": "string"}, "author": {"type": "string"}, "action": {"type": "string"}, "title": {"type": "string"}, "isLinked": {"type": "boolean"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "pullRequestLink", "author", "action", "title", "isLinked"]}, "GitHubTaskBranch": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["gh_task_branch"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "branchName": {"type": "string"}, "repoOwner": {"type": "string"}, "repoName": {"type": "string"}, "hasBranchAttached": {"type": "boolean"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "branchName", "repoOwner", "repoName", "hasBranchAttached"]}, "GitLabCommit": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["gl_commit"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "branchName": {"type": "string"}, "repoName": {"type": "string"}, "commitUrl": {"type": "string"}, "author": {"type": "string"}, "message": {"type": "string"}, "sha": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "branchName", "repoName", "commitUrl", "author", "message", "sha"]}, "GitLabMergeRequest": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["gl_merge_request"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "pullRequestLink": {"type": "string"}, "author": {"type": "string"}, "action": {"type": "string"}, "title": {"type": "string"}, "isLinked": {"type": "boolean"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "pullRequestLink", "author", "action", "title", "isLinked"]}, "GitLabTaskBranch": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["gl_task_branch"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "branchName": {"type": "string"}, "repoOwner": {"type": "string"}, "repoName": {"type": "string"}, "hasBranchAttached": {"type": "boolean"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "branchName", "repoOwner", "repoName", "hasBranchAttached"]}, "GroupAssigneeAdded": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["group_assignee_add"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "groupAssignee": {"type": "string", "example": "clickup:user-group:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "groupAssignee"]}, "GroupAssigneeRemoved": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["group_assignee_rem"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "groupAssignee": {"type": "string", "example": "clickup:user-group:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "groupAssignee"]}, "LinkedTask": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["linked_task"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "relatedTask": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "isUnlinked": {"type": "boolean"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "relatedTask", "isUnlinked"]}, "Name": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["name"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "name": {"type": "string"}, "priorName": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "name"]}, "NewSubtask": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["new_subtask"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "relatedTask": {"type": "string", "example": "clickup:task:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "relatedTask"]}, "Points": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["points"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "points": {"type": "number"}, "priorPoints": {"type": "number"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "points"]}, "Priority": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["priority"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "priority": {"type": "number"}, "priorPriority": {"type": "number"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "priority"]}, "Reaction": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["reaction"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "author": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "userMentions": {"example": "clickup:task:333:a1b2c3", "type": "array", "items": {"type": "string"}}, "groupMentions": {"example": "clickup:task:333:a1b2c3", "type": "array", "items": {"type": "string"}}, "assignee": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "groupAssignee": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "reaction": {"type": "string"}, "actorIds": {"type": "array", "items": {"type": "string"}}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "author", "userMentions", "groupMentions", "assignee", "groupAssignee", "reaction"]}, "Reminder": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["reminder"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "reminderId": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "initialDueDate": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "reminderId", "initialDueDate"]}, "ReminderV2": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["reminder_v2"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "associatedEntity": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "associatedParentEntity": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "associatedRootEntity": {"type": "string", "example": "clickup:task:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId"]}, "ReminderV2Notification": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["reminder_v2_notification"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "associatedEntity": {"type": "string", "example": "clickup:task:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId"]}, "RemovedFromSubcategory": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["removed_from_subcategory"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "subcategory": {"type": "object"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "subcategory"]}, "ResolvedItems": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["resolved_items"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "commentCount": {"type": "number"}, "checklistItemCount": {"type": "number"}, "subtaskCount": {"type": "number"}, "subtasks": {"type": "array", "items": {"type": "string"}}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "commentCount", "checklistItemCount", "subtaskCount", "subtasks"]}, "SectionMoved": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["section_moved"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "section": {"type": "object"}, "priorSection": {"type": "object"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "section"]}, "SharedWithMe": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["shared_with_me"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId"]}, "StartDate": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["start_date"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "startDate": {"format": "date-time", "type": "string"}, "priorStartDate": {"format": "date-time", "type": "string"}, "isStartDateADateTime": {"type": "boolean"}, "isPriorStartDateADateTime": {"type": "boolean"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId"]}, "StartDateReminder": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["start_date_reminder"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "taskStartDate": {"format": "date-time", "type": "string"}, "isTaskStartDateADateTime": {"type": "boolean", "description": "If the taskStartDate is a DateTime instead of just a Date"}, "minutesAfter": {"type": "number", "description": "Minutes to wait before attempting to notify the user"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "taskStartDate", "isTaskStartDateADateTime"]}, "Status": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["status"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "status": {"type": "object"}, "priorStatus": {"type": "object"}, "initiator": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "status"]}, "Tag": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["tag"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "tags"]}, "TagRemoved": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["tag_removed"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "priorTags": {"type": "array", "items": {"type": "string"}}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "priorTags"]}, "TaskCreation": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["task_creation"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "wasCreatedFromRecurring": {"type": "boolean"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "wasCreatedFromRecurring"]}, "TemplateMerged": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["template_merged"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "templateName": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "templateName"]}, "TimeEstimate": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["time_estimate"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "timeEstimate": {"type": "string"}, "priorTimeEstimate": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "timeEstimate"]}, "TimeSpent": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["time_spent"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "timeSpentInMilliseconds": {"type": "number"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId"]}, "TimesheetApprovalReminder": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["timesheet_approval_reminder"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId"]}, "Unblocked": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["unblocked"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "relatedTask": {"type": "string", "example": "clickup:task:333:a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "relatedTask"]}, "ZoomMeeting": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["zoom_meeting"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "meetingInfo": {"type": "object"}, "participants": {"type": "object"}, "recordings": {"type": "object"}, "isFromEmail": {"type": "boolean"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "meetingInfo", "participants", "recordings", "isFromEmail"]}, "ZoomMeetingStarted": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["zoom_meeting_started"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "meetingId": {"type": "string"}, "url": {"type": "string"}, "topic": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "meetingId", "url", "topic"]}, "ZoomMeetingEnded": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["zoom_meeting_ended"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "meetingId": {"type": "string"}, "url": {"type": "string"}, "topic": {"type": "string"}, "duration": {"type": "number"}, "participantCount": {"type": "number"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "meetingId", "url", "topic", "duration", "participantCount"]}, "ZoomRecordingCompleted": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["zoom_recording_completed"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "meetingId": {"type": "string"}, "url": {"type": "string"}, "topic": {"type": "string"}, "recordingUrl": {"type": "string"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "meetingId", "url", "topic", "recordingUrl"]}, "AddedToPriorities": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["added_to_priorities"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "assignee": {"type": "string", "example": "clickup:user::a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "assignee"]}, "RemovedFromPriorities": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["removed_from_priorities"]}, "occurredAt": {"format": "date-time", "type": "string"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "actorId": {"type": "string", "example": "clickup:user::a1b2c3"}, "automationTriggerId": {"type": "string"}, "assignee": {"type": "string", "example": "clickup:user::a1b2c3"}}, "required": ["id", "type", "occurredAt", "entityResourceName", "rootEntityResourceName", "actorId", "assignee"]}, "NotificationType": {"type": "string", "enum": ["access_request_approved", "access_requested", "upgrade_access_requested", "upgrade_access_request_approved", "added_to_subcategory", "archived", "assignee_add", "assignee_rem", "attachment_comment", "attachments", "bb_commit", "bb_pull_request", "bb_pull_updated", "chat_thread_link", "checklist_item_assignee", "checklist_item_resolved", "checklist_items_added", "comment_assigned", "comment_resolved", "comment", "content", "custom_field", "dependency_of", "depends_on", "description_group_tag", "description_tag", "due_date", "due_date_missed", "due_date_reminder", "duplicate", "duration", "follower_add", "follower_rem", "gh_commit", "github_pull_request", "gh_task_branch", "gl_commit", "gl_merge_request", "gl_task_branch", "group_assignee_add", "group_assignee_rem", "linked_task", "name", "new_subtask", "points", "priority", "private", "reaction", "reminder", "reminder_v2", "reminder_v2_notification", "removed_from_subcategory", "resolved_items", "section_moved", "shared_with_me", "start_date_reminder", "start_date", "status", "tag_removed", "tag", "task_creation", "template_merged", "time_estimate", "timesheet_approval_reminder", "time_spent", "unblocked", "zoom_meeting", "zoom_meeting_started", "zoom_meeting_ended", "zoom_recording_completed", "custom_type", "coverimage", "html_content", "parent", "recur", "recur_2.0", "recurrence_removed", "recurrence_set_2.0", "remapped_due_date", "remapped_start_date", "summary_task", "bb_issue", "bb_issue_updated", "bb_pr_approved", "bb_pr_created", "bb_pr_fulfilled", "bb_pr_rejected", "bb_pr_unapproved", "bb_pr_updated", "added_to_priorities", "removed_from_priorities"]}, "Notification": {"type": "object", "properties": {"id": {"type": "string"}, "workspaceId": {"type": "string"}, "userId": {"type": "string"}, "type": {"$ref": "#/components/schemas/NotificationType"}, "historyItem": {"description": "The payload corresponding to the change that occurred.  Note that this payload will often be the same for every user that this notification applies to, because it's the historical change (agnostic of users)", "oneOf": [{"$ref": "#/components/schemas/AccessRequestApproved"}, {"$ref": "#/components/schemas/AccessRequested"}, {"$ref": "#/components/schemas/UpgradeAccessRequested"}, {"$ref": "#/components/schemas/UpgradeAccessRequestApproved"}, {"$ref": "#/components/schemas/AddedToSubcategory"}, {"$ref": "#/components/schemas/AssigneeAdded"}, {"$ref": "#/components/schemas/AssigneeRemoved"}, {"$ref": "#/components/schemas/Attachments"}, {"$ref": "#/components/schemas/BitBucketCommit"}, {"$ref": "#/components/schemas/BitBucketPullRequest"}, {"$ref": "#/components/schemas/BitBucketPullUpdated"}, {"$ref": "#/components/schemas/ChatThreadLink"}, {"$ref": "#/components/schemas/ChecklistItemAssignee"}, {"$ref": "#/components/schemas/ChecklistItemResolved"}, {"$ref": "#/components/schemas/ChecklistItemsAdded"}, {"$ref": "#/components/schemas/Comment"}, {"$ref": "#/components/schemas/CommentAssigned"}, {"$ref": "#/components/schemas/CommentResolved"}, {"$ref": "#/components/schemas/Content"}, {"$ref": "#/components/schemas/CustomField"}, {"$ref": "#/components/schemas/CustomType"}, {"$ref": "#/components/schemas/DependencyOf"}, {"$ref": "#/components/schemas/DependsOn"}, {"$ref": "#/components/schemas/DescriptionGroupTag"}, {"$ref": "#/components/schemas/DescriptionTag"}, {"$ref": "#/components/schemas/DueDate"}, {"$ref": "#/components/schemas/DueDateMissed"}, {"$ref": "#/components/schemas/DueDateReminder"}, {"$ref": "#/components/schemas/Duplicate"}, {"$ref": "#/components/schemas/Duration"}, {"$ref": "#/components/schemas/FollowerAdded"}, {"$ref": "#/components/schemas/FollowerRemoved"}, {"$ref": "#/components/schemas/GitHubCommit"}, {"$ref": "#/components/schemas/GitHubPullRequest"}, {"$ref": "#/components/schemas/GitHubTaskBranch"}, {"$ref": "#/components/schemas/GitLabCommit"}, {"$ref": "#/components/schemas/GitLabMergeRequest"}, {"$ref": "#/components/schemas/GitLabTaskBranch"}, {"$ref": "#/components/schemas/GroupAssigneeAdded"}, {"$ref": "#/components/schemas/GroupAssigneeRemoved"}, {"$ref": "#/components/schemas/LinkedTask"}, {"$ref": "#/components/schemas/Name"}, {"$ref": "#/components/schemas/NewSubtask"}, {"$ref": "#/components/schemas/Points"}, {"$ref": "#/components/schemas/Priority"}, {"$ref": "#/components/schemas/Reaction"}, {"$ref": "#/components/schemas/Reminder"}, {"$ref": "#/components/schemas/ReminderV2"}, {"$ref": "#/components/schemas/ReminderV2Notification"}, {"$ref": "#/components/schemas/RemovedFromSubcategory"}, {"$ref": "#/components/schemas/ResolvedItems"}, {"$ref": "#/components/schemas/SectionMoved"}, {"$ref": "#/components/schemas/SharedWithMe"}, {"$ref": "#/components/schemas/StartDate"}, {"$ref": "#/components/schemas/StartDateReminder"}, {"$ref": "#/components/schemas/Status"}, {"$ref": "#/components/schemas/Tag"}, {"$ref": "#/components/schemas/TagRemoved"}, {"$ref": "#/components/schemas/TaskCreation"}, {"$ref": "#/components/schemas/TemplateMerged"}, {"$ref": "#/components/schemas/TimeEstimate"}, {"$ref": "#/components/schemas/TimeSpent"}, {"$ref": "#/components/schemas/TimesheetApprovalReminder"}, {"$ref": "#/components/schemas/Unblocked"}, {"$ref": "#/components/schemas/ZoomMeeting"}, {"$ref": "#/components/schemas/ZoomMeetingStarted"}, {"$ref": "#/components/schemas/ZoomMeetingEnded"}, {"$ref": "#/components/schemas/ZoomRecordingCompleted"}, {"$ref": "#/components/schemas/AddedToPriorities"}, {"$ref": "#/components/schemas/RemovedFromPriorities"}], "discriminator": {"propertyName": "type"}}, "read": {"type": "boolean", "description": "Set if/when the notification is read", "deprecated": true}, "cleared": {"type": "boolean", "description": "Set if/when the notification is cleared", "deprecated": true}, "status": {"$ref": "#/components/schemas/NotificationStatus"}, "priority": {"type": "number"}}, "required": ["id", "workspaceId", "userId", "type", "historyItem"]}, "ThreadCommentDetails": {"type": "object", "properties": {"author": {"type": "string"}, "commentId": {"type": "string"}}, "required": ["author", "commentId"]}, "NotificationBundle": {"type": "object", "properties": {"bundleType": {"$ref": "#/components/schemas/BundleType"}, "mostRecentCommentNotification": {"$ref": "#/components/schemas/Notification"}, "previewNotification": {"$ref": "#/components/schemas/Notification"}, "id": {"type": "string", "description": "An identifier for the bundle.  When bundled by root entity, the id will include the ERN of the root entity; for bundles by activity type, it will include an activity type; the ID also contains the start/end range of the bundle"}, "hasAssignment": {"type": "boolean", "description": "True if the root entity, or any entity referenced by the bundle, is currently assigned to the user"}, "hasMention": {"type": "boolean"}, "unreadCount": {"type": "number"}, "nextNotificationCursor": {"type": "string"}, "countByNotificationType": {"type": "object"}, "status": {"$ref": "#/components/schemas/NotificationStatus"}, "recentNotifications": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}, "mostRecentNotificationTime": {"type": "string", "description": "ISO8601 timestamp of the most recent notification in the bundle"}, "clearedAt": {"type": "string", "description": "ISO8601 timestamp of when the bundle was cleared"}, "threadCommentDetailsByParentCommentId": {"type": "object", "description": "A mapping of parent comment ID to an array of all the threaded comments under that parent for which the user has uncleared notifications.  This is used by the frontend to more efficiently display the threaded comments. The ordering of each array should be the same as the order in which the notification(s) was/were received for each comment"}, "snoozedUntil": {"type": "string", "description": "ISO8601 timestamp of when the bundle will be snoozed until"}, "unsnoozedAt": {"type": "string", "description": "ISO8601 timestamp of when the bundle was unsnoozed"}, "unsnoozedBy": {"description": "The notification that caused the bundle to be unsnoozed, if applicable", "allOf": [{"$ref": "#/components/schemas/Notification"}]}, "snoozedAt": {"type": "string", "description": "ISO8601 timestamp of when the bundle was snoozed"}}, "required": ["bundleType", "id", "unreadCount", "nextNotificationCursor", "countByNotificationType", "status", "recentNotifications", "mostRecentNotificationTime", "threadCommentDetailsByParentCommentId"]}, "NotificationBundleByRootEntity": {"type": "object", "properties": {"bundleType": {"$ref": "#/components/schemas/BundleType"}, "mostRecentCommentNotification": {"$ref": "#/components/schemas/Notification"}, "previewNotification": {"$ref": "#/components/schemas/Notification"}, "id": {"type": "string", "description": "An identifier for the bundle.  When bundled by root entity, the id will include the ERN of the root entity; for bundles by activity type, it will include an activity type; the ID also contains the start/end range of the bundle"}, "hasAssignment": {"type": "boolean", "description": "True if the root entity, or any entity referenced by the bundle, is currently assigned to the user"}, "hasMention": {"type": "boolean"}, "unreadCount": {"type": "number"}, "nextNotificationCursor": {"type": "string"}, "countByNotificationType": {"type": "object"}, "status": {"$ref": "#/components/schemas/NotificationStatus"}, "recentNotifications": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}, "mostRecentNotificationTime": {"type": "string", "description": "ISO8601 timestamp of the most recent notification in the bundle"}, "clearedAt": {"type": "string", "description": "ISO8601 timestamp of when the bundle was cleared"}, "threadCommentDetailsByParentCommentId": {"type": "object", "description": "A mapping of parent comment ID to an array of all the threaded comments under that parent for which the user has uncleared notifications.  This is used by the frontend to more efficiently display the threaded comments. The ordering of each array should be the same as the order in which the notification(s) was/were received for each comment"}, "snoozedUntil": {"type": "string", "description": "ISO8601 timestamp of when the bundle will be snoozed until"}, "unsnoozedAt": {"type": "string", "description": "ISO8601 timestamp of when the bundle was unsnoozed"}, "unsnoozedBy": {"description": "The notification that caused the bundle to be unsnoozed, if applicable", "allOf": [{"$ref": "#/components/schemas/Notification"}]}, "snoozedAt": {"type": "string", "description": "ISO8601 timestamp of when the bundle was snoozed"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2"}}, "required": ["bundleType", "id", "unreadCount", "nextNotificationCursor", "countByNotificationType", "status", "recentNotifications", "mostRecentNotificationTime", "threadCommentDetailsByParentCommentId", "rootEntityResourceName"]}, "NotificationBundleByActivityType": {"type": "object", "properties": {"bundleType": {"$ref": "#/components/schemas/BundleType"}, "mostRecentCommentNotification": {"$ref": "#/components/schemas/Notification"}, "previewNotification": {"$ref": "#/components/schemas/Notification"}, "id": {"type": "string", "description": "An identifier for the bundle.  When bundled by root entity, the id will include the ERN of the root entity; for bundles by activity type, it will include an activity type; the ID also contains the start/end range of the bundle"}, "hasAssignment": {"type": "boolean", "description": "True if the root entity, or any entity referenced by the bundle, is currently assigned to the user"}, "hasMention": {"type": "boolean"}, "unreadCount": {"type": "number"}, "nextNotificationCursor": {"type": "string"}, "countByNotificationType": {"type": "object"}, "status": {"$ref": "#/components/schemas/NotificationStatus"}, "recentNotifications": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}, "mostRecentNotificationTime": {"type": "string", "description": "ISO8601 timestamp of the most recent notification in the bundle"}, "clearedAt": {"type": "string", "description": "ISO8601 timestamp of when the bundle was cleared"}, "threadCommentDetailsByParentCommentId": {"type": "object", "description": "A mapping of parent comment ID to an array of all the threaded comments under that parent for which the user has uncleared notifications.  This is used by the frontend to more efficiently display the threaded comments. The ordering of each array should be the same as the order in which the notification(s) was/were received for each comment"}, "snoozedUntil": {"type": "string", "description": "ISO8601 timestamp of when the bundle will be snoozed until"}, "unsnoozedAt": {"type": "string", "description": "ISO8601 timestamp of when the bundle was unsnoozed"}, "unsnoozedBy": {"description": "The notification that caused the bundle to be unsnoozed, if applicable", "allOf": [{"$ref": "#/components/schemas/Notification"}]}, "snoozedAt": {"type": "string", "description": "ISO8601 timestamp of when the bundle was snoozed"}, "activityType": {"type": "string"}}, "required": ["bundleType", "id", "unreadCount", "nextNotificationCursor", "countByNotificationType", "status", "recentNotifications", "mostRecentNotificationTime", "threadCommentDetailsByParentCommentId", "activityType"]}, "GroupingType": {"type": "string", "enum": ["time-range"]}, "NotificationBundleGroupByDateTimeRange": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/GroupingType"}, "notificationBundles": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/NotificationBundleByRootEntity"}, {"$ref": "#/components/schemas/NotificationBundleByActivityType"}]}}, "nextCursor": {"type": "string"}, "dateTimeRange": {"$ref": "#/components/schemas/DateTimeRange"}}, "required": ["type", "notificationBundles", "nextCursor", "dateTimeRange"]}, "GetNotificationBundlesResponse": {"type": "object", "properties": {"notificationBundleGroups": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationBundleGroupByDateTimeRange"}}, "resources": {"type": "object", "description": "Mapping of entity resource names to resource/entity objects (e.g. tasks, comments, etc.)", "additionalProperties": {"oneOf": [{"$ref": "#/components/schemas/CommentEntity"}, {"$ref": "#/components/schemas/TaskEntity"}, {"$ref": "#/components/schemas/PageEntity"}, {"$ref": "#/components/schemas/DocumentEntity"}, {"$ref": "#/components/schemas/Subcategory"}, {"$ref": "#/components/schemas/HierarchyItem"}]}}, "nextCursor": {"type": "string"}, "users": {"type": "object"}}, "required": ["notificationBundleGroups", "resources", "nextCursor"]}, "GetNotificationsInBundleRequest": {"type": "object", "properties": {"bundleSnapshotId": {"type": "object"}, "pagination": {"$ref": "#/components/schemas/PaginationParameters"}, "filteredBy": {"$ref": "#/components/schemas/FilterParameters"}, "needsMemberMap": {"type": "boolean"}}, "required": ["bundleSnapshotId"]}, "GetNotificationsInBundleResponse": {"type": "object", "properties": {"notifications": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}, "resources": {"type": "object", "description": "Mapping of entity resource names to resource/entity objects (e.g. tasks, comments, etc.)", "additionalProperties": {"oneOf": [{"$ref": "#/components/schemas/TaskEntity"}, {"$ref": "#/components/schemas/CommentEntity"}]}}, "nextCursor": {"type": "string"}, "users": {"type": "object"}}, "required": ["notifications", "resources", "nextCursor"]}, "BundleSnapshotIdentifier": {"type": "object", "properties": {}}, "ClearAllNotificationsRequest": {"type": "object", "properties": {"bundleSnapshotIds": {"type": "array", "items": {"$ref": "#/components/schemas/BundleSnapshotIdentifier"}}, "mostRecentNotificationBundleSnapshotId": {"type": "object"}, "filteredBy": {"$ref": "#/components/schemas/FilterParameters"}}}, "UnclearNotificationsRequest": {"type": "object", "properties": {"bundleSnapshotIds": {"type": "array", "items": {"$ref": "#/components/schemas/BundleSnapshotIdentifier"}}}, "required": ["bundleSnapshotIds"]}, "UpdateNotificationBundleReadStatusRequest": {"type": "object", "properties": {"bundleSnapshotId": {"type": "object"}}, "required": ["bundleSnapshotId"]}, "SnoozeNotificationBundleRequest": {"type": "object", "properties": {"bundleSnapshotId": {"$ref": "#/components/schemas/BundleSnapshotIdentifier"}, "snoozedUntil": {"type": "string"}, "unsnoozeOnNewNotification": {"type": "boolean"}, "unsnoozeOnAtMention": {"type": "boolean"}}, "required": ["bundleSnapshotId", "snoozedUntil"]}, "UnsnoozeNotificationBundleRequest": {"type": "object", "properties": {"bundleSnapshotId": {"$ref": "#/components/schemas/BundleSnapshotIdentifier"}}, "required": ["bundleSnapshotId"]}, "CreateNotificationRequestInput": {"type": "object", "properties": {"workspaceId": {"type": "string"}, "userId": {"type": "string"}, "historyItem": {"description": "The payload corresponding to the change that occurred.  Note that this payload will be the same for every user that this notification applies to, because it's the historical change (agnostic of users)", "oneOf": [{"$ref": "#/components/schemas/AccessRequestApproved"}, {"$ref": "#/components/schemas/AccessRequested"}, {"$ref": "#/components/schemas/UpgradeAccessRequested"}, {"$ref": "#/components/schemas/UpgradeAccessRequestApproved"}, {"$ref": "#/components/schemas/AddedToSubcategory"}, {"$ref": "#/components/schemas/AssigneeAdded"}, {"$ref": "#/components/schemas/AssigneeRemoved"}, {"$ref": "#/components/schemas/Attachments"}, {"$ref": "#/components/schemas/BitBucketCommit"}, {"$ref": "#/components/schemas/BitBucketPullRequest"}, {"$ref": "#/components/schemas/BitBucketPullUpdated"}, {"$ref": "#/components/schemas/ChatThreadLink"}, {"$ref": "#/components/schemas/ChecklistItemAssignee"}, {"$ref": "#/components/schemas/ChecklistItemResolved"}, {"$ref": "#/components/schemas/ChecklistItemsAdded"}, {"$ref": "#/components/schemas/Comment"}, {"$ref": "#/components/schemas/CommentAssigned"}, {"$ref": "#/components/schemas/CommentResolved"}, {"$ref": "#/components/schemas/Content"}, {"$ref": "#/components/schemas/CustomField"}, {"$ref": "#/components/schemas/CustomType"}, {"$ref": "#/components/schemas/DependencyOf"}, {"$ref": "#/components/schemas/DependsOn"}, {"$ref": "#/components/schemas/DescriptionGroupTag"}, {"$ref": "#/components/schemas/DescriptionTag"}, {"$ref": "#/components/schemas/DueDate"}, {"$ref": "#/components/schemas/DueDateMissed"}, {"$ref": "#/components/schemas/DueDateReminder"}, {"$ref": "#/components/schemas/Duplicate"}, {"$ref": "#/components/schemas/Duration"}, {"$ref": "#/components/schemas/FollowerAdded"}, {"$ref": "#/components/schemas/FollowerRemoved"}, {"$ref": "#/components/schemas/GitHubCommit"}, {"$ref": "#/components/schemas/GitHubPullRequest"}, {"$ref": "#/components/schemas/GitHubTaskBranch"}, {"$ref": "#/components/schemas/GitLabCommit"}, {"$ref": "#/components/schemas/GitLabMergeRequest"}, {"$ref": "#/components/schemas/GitLabTaskBranch"}, {"$ref": "#/components/schemas/GroupAssigneeAdded"}, {"$ref": "#/components/schemas/GroupAssigneeRemoved"}, {"$ref": "#/components/schemas/LinkedTask"}, {"$ref": "#/components/schemas/Name"}, {"$ref": "#/components/schemas/NewSubtask"}, {"$ref": "#/components/schemas/Points"}, {"$ref": "#/components/schemas/Priority"}, {"$ref": "#/components/schemas/Reaction"}, {"$ref": "#/components/schemas/Reminder"}, {"$ref": "#/components/schemas/ReminderV2"}, {"$ref": "#/components/schemas/ReminderV2Notification"}, {"$ref": "#/components/schemas/RemovedFromSubcategory"}, {"$ref": "#/components/schemas/ResolvedItems"}, {"$ref": "#/components/schemas/SectionMoved"}, {"$ref": "#/components/schemas/SharedWithMe"}, {"$ref": "#/components/schemas/StartDate"}, {"$ref": "#/components/schemas/StartDateReminder"}, {"$ref": "#/components/schemas/Status"}, {"$ref": "#/components/schemas/Tag"}, {"$ref": "#/components/schemas/TagRemoved"}, {"$ref": "#/components/schemas/TaskCreation"}, {"$ref": "#/components/schemas/TemplateMerged"}, {"$ref": "#/components/schemas/TimeEstimate"}, {"$ref": "#/components/schemas/TimeSpent"}, {"$ref": "#/components/schemas/TimesheetApprovalReminder"}, {"$ref": "#/components/schemas/Unblocked"}, {"$ref": "#/components/schemas/ZoomMeeting"}, {"$ref": "#/components/schemas/ZoomMeetingStarted"}, {"$ref": "#/components/schemas/ZoomMeetingEnded"}, {"$ref": "#/components/schemas/ZoomRecordingCompleted"}, {"$ref": "#/components/schemas/AddedToPriorities"}, {"$ref": "#/components/schemas/RemovedFromPriorities"}], "discriminator": {"propertyName": "type"}}, "rootEntityResourceName": {"type": "string", "description": "The root entity that the historyItem is associated with.  For example, for a threaded commenton a task, the root entity would be the task", "example": "clickup:task:333:a1b2c3"}, "isMention": {"type": "boolean", "description": "True if the notification includes a mention of the user, either directly or indirectly (via a user group, assignees, or watchers @mention"}, "overwriteIfExists": {"type": "boolean"}, "hydrationSentTimestamp": {"type": "number"}, "clearedAt": {"type": "number"}}, "required": ["workspaceId", "userId", "historyItem"]}, "LegacyToInboxNotificationUpdateRequestInput": {"type": "object", "properties": {"workspaceId": {"type": "string"}, "userId": {"type": "string"}, "actionData": {"type": "object"}, "action": {"type": "object"}}, "required": ["workspaceId", "userId", "actionData", "action"]}, "GetNotificationBundleStatsRequest": {"type": "object", "properties": {"forceRefresh": {"type": "boolean"}, "includeUnreadFromAllWorkspaces": {"type": "boolean"}}}, "NotificationBundleStats": {"type": "object", "properties": {"activityCount": {"type": "number"}, "messagesCount": {"type": "number"}, "mentionMessagesCount": {"type": "number"}, "assignedActivityCount": {"type": "number"}, "assignedMessagesCount": {"type": "number"}, "lastUpdatedAt": {"format": "date-time", "type": "string"}, "lastRefreshedAt": {"format": "date-time", "type": "string"}, "allWorkspaces": {"type": "object"}}, "required": ["activityCount", "messagesCount", "mentionMessagesCount", "assignedActivityCount", "assignedMessagesCount", "lastUpdatedAt", "lastRefreshedAt", "allWorkspaces"]}, "GetNotificationBundleStatsBulkRequest": {"type": "object", "properties": {"workspaceId": {"type": "string"}, "userIds": {"type": "array", "items": {"type": "string"}}}, "required": ["workspaceId", "userIds"]}, "NotificationBundleStatsMinimal": {"type": "object", "properties": {"activityCount": {"type": "number"}, "messagesCount": {"type": "number"}}, "required": ["activityCount", "messagesCount"]}, "Map": {"type": "object", "properties": {}}, "GetNotificationBundleStatsBulkResponse": {"type": "object", "properties": {"bundleStatsByUserId": {"description": "Dictionary object with string keys and values of type NotificationBundleStatsMinimal.", "example": {"123": {"messagesCount": 2, "activityCount": 0}, "456": {"messagesCount": 0, "activityCount": 0}}, "additionalProperties": {"$ref": "#/components/schemas/NotificationBundleStatsMinimal"}, "allOf": [{"$ref": "#/components/schemas/Map"}]}}, "required": ["bundleStatsByUserId"]}, "GetCommentsByIdRequest": {"type": "object", "properties": {"rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "threadedCommentsByParentCommentId": {"type": "object", "description": "A mapping of thread comment details/IDs by their parent comment IDs; note this is the same shape as the threadCommentDetailsByParentCommentId mapping returned from the getNotificationBundles endpoint"}, "commentIds": {"type": "array", "items": {"type": "string"}}}, "required": ["rootEntityResourceName"]}, "PushNotificationStats": {"type": "object", "properties": {"platform": {"type": "string", "description": "The platform of the application", "enum": ["ios", "android", "browser", "desktop"]}, "notificationType": {"type": "string", "description": "The value of field **field** in the push notification payload"}, "traceId": {"type": "string", "description": "The Datadog trace ID in the push notification payload"}, "uuid": {"type": "string", "description": "The uuid in the push notification payload"}}, "required": ["platform", "notificationType", "traceId", "uuid"]}, "PushNotificationStatsRequest": {"type": "object", "properties": {"stats": {"description": "The list of push notification stats", "type": "array", "items": {"$ref": "#/components/schemas/PushNotificationStats"}}}, "required": ["stats"]}, "PushNotificationStatsResponse": {"type": "object", "properties": {}}, "PushNotificationLog": {"type": "object", "properties": {"platform": {"type": "string", "description": "The platform of the application", "enum": ["ios", "android", "browser", "desktop"]}, "trace_id": {"type": "string", "description": "The Datadog trace ID in the push notification payload"}, "uuid": {"type": "string", "description": "The uuid in the push notification payload"}, "notification_id": {"type": "string", "description": "The notification ID in the push notification payload"}, "msg": {"type": "string", "description": "The message to log"}, "metric": {"type": "string", "description": "The name of the metric to increment"}, "timestamp": {"type": "number", "description": "The timestamp in milliseconds"}, "props": {"type": "object", "description": "The extra fields to log"}}, "required": ["platform", "trace_id", "uuid", "notification_id", "msg", "metric", "timestamp", "props"]}, "PushNotificationLogRequest": {"type": "object", "properties": {"logs": {"description": "The list of push notification logs", "type": "array", "items": {"$ref": "#/components/schemas/PushNotificationLog"}}}, "required": ["logs"]}, "UpdateNotificationResourceStoreRequestInput": {"type": "object", "properties": {"data": {"type": "object"}, "deleted": {"type": "boolean"}, "entityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "workspaceId": {"type": "string"}, "rootEntityResourceName": {"type": "string", "example": "clickup:task:333:a1b2c3"}, "fetchedAt": {"type": "number"}, "hydrationSentTimestamp": {"type": "number"}}, "required": ["data", "entityResourceName", "workspaceId"]}, "GetCommentEngagementsRequest": {"type": "object", "properties": {"engagementNeededBy": {"type": "string", "enum": ["me", "others"]}, "cursor": {"type": "string"}, "excludeResources": {"type": "boolean"}}, "required": ["engagementNeededBy"]}, "GetCommentEngagementsResponse": {"type": "object", "properties": {"resources": {"type": "object", "description": "Mapping of entity resource names to resource/entity objects (e.g. tasks, comments, etc.)", "additionalProperties": {"oneOf": [{"$ref": "#/components/schemas/TaskEntity"}, {"$ref": "#/components/schemas/CommentEntity"}]}}, "nextCursor": {"type": "string"}}, "required": ["nextCursor"]}, "DismissCommentEngagementRequest": {"type": "object", "properties": {"commentEntityResourceName": {"type": "string"}, "parentCommentResourceName": {"type": "string"}}, "required": ["commentEntityResourceName"]}, "GetNotificationCustomizationResponse": {"type": "object", "properties": {"importanceCustomization": {"type": "object"}, "previewCustomization": {"type": "object"}}}, "UpdateNotificationCustomizationRequest": {"type": "object", "properties": {"importanceCustomization": {"type": "object"}, "previewCustomization": {"type": "object"}}}, "CustomizationType": {"type": "string", "enum": ["preview", "importance"]}, "DeleteNotificationCustomizationRequest": {"type": "object", "properties": {"customizationType": {"example": "importance", "$ref": "#/components/schemas/CustomizationType"}}, "required": ["customizationType"]}, "HealthDetailResponse": {"type": "object", "properties": {"status": {"type": "string", "description": "The overall status of the Health Check"}, "info": {"type": "object", "description": "The info object contains information of each health indicator which is of status \"up\""}, "error": {"type": "object", "description": "The error object contains information of each health indicator which is of status \"down\""}, "details": {"type": "object", "description": "The details object contains information of every health indicator"}}, "required": ["status"]}}}, "externalDocs": {"description": "Visit our doc", "url": "https://app.datadoghq.com/dashboard/kjd-2fg-597"}}