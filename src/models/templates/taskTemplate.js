import uuid from 'node-uuid';
import config from 'config';
import { ObjectRelationshipType, ObjectType, OperationType } from '@time-loop/ovm-object-version';
import _, { uniqBy } from 'lodash';
import { CustomType } from '@clickup/task/extensions';
import { getLogger } from '@clickup/shared/utils-logging';
import {
    loadTemplatesUserUsageData,
    applyTemplatesPaginationByUsedCountUsingPage,
    applyTemplatesPaginationByUsedCountUsingName,
} from '@clickup-legacy/models/templates/templateUserUsedCountHelper';
import { sendWebhookInBatchOnTaskCreation } from '@clickup-legacy/models/task/helpers/webhookHelper';
import { addPressureToFunctionParams, OperationPrefix, withSemaphoreLimit } from '@clickup-legacy/utils/semaphoreUtils';
import { getInsertNewlineOp } from '@clickup/quill';
import * as sqsWs from '@clickup-legacy/utils/v1-ws-message-sending';
import * as sqsNotif from '@clickup-legacy/utils/sqs-notif';
import { postTaskCreatedNotifications } from '@clickup-legacy/models/integrations/notifications/notifications';
import { workScheduleClient } from '@clickup-legacy/clients/WorkScheduleClient';
import { getDefaultWorkWeekSettings } from '@clickup/scheduler/common';
import {
    emitTaskCreatedForNestedSubtasks,
    getSemaphoreCopyOptions,
    shouldRunPreMergeEntitlementChecks,
    useSemaphoresForMergeTemplateIntoTask,
    useRemapDateOfTaskV2ForTaskTemplateMerging,
} from '@clickup-legacy/models/integrations/split/squadTreatments/templatesTreatments';
import { _getItems } from '../task/CRUD/getTask/get-items';
import { promiseGetSubtasksWithDueDates } from '../task/CRUD/getTask/get-subtasks';
import { DurationMetricObject, EntityType, TemplateAction, TemplateSource } from './templateMetricsCommons';
import { TemplateTracer } from './templateTracer/templateTracer';
import { getOrderindexStats } from '../ordering/task/services/copyService';
import { getOldFieldToValueMap } from '../task/datastores/fieldValue';
import { metricsClient } from '../../metrics/metricsClient';
import { validateSubtaskLimitForTaskTemplateMerging } from './validation/subtaskLimitForTaskTemplateMerging';

import { TaskCreatedVia } from '../task/interfaces/Task';
import * as async from '../../utils/asyncHelper';

import * as genericHelpers from '../helpers';
import * as nestedHelpers from '../task/nestedHelpers';
import * as access from '../../utils/access2';
import * as assigneeMod from '../assignee';
import { _getDependencies } from '../dependencies/taskDependencies.service';
import { getTaskWithSubtasks } from '../task/taskHelpersTS';
import * as checklistMod from '../checklist'; // eslint-disable-line import/no-cycle
import { ClickUpError, makeSmartCallback } from '../../utils/errors';
import { codoxSync } from '../integrations/codox/codoxHelpers';
import * as db from '../../utils/db';
import * as db2 from '../../utils/db2';
import * as input from '../../utils/input_validation';
import * as tagsv2Mod from '../tagsv2';
import * as groupMod from '../groups';
import { copyAttachmentDocs, copyAttachments } from '../attachment/attachmentCopy';
import * as copyTask from '../task/services/copyTaskService'; // eslint-disable-line import/no-cycle
import * as copyComments from '../comment/services/copyComments';
import * as copyDependencies from '../dependencies/copyDependencies.service';
import * as copyLinks from '../link/services/copyLinks';
import * as taskMember from '../task/taskMember'; // eslint-disable-line import/no-cycle
import * as taskHelpers from '../task/taskHelpers';
import * as fieldHelper from '../field/helpers/helpers'; // eslint-disable-line import/no-cycle
import * as quill from '../../utils/quill';
import { replaceSyncBlocksInContent, replaceAttachmentsInContent } from '../../utils/quill';
import * as webhook from '../../utils/webhook';
import * as templateHelpers from './templateHelpers';
import * as templateTags from './templateTags';
import * as json from '../../utils/json';
import * as recurrence from '../recurrence';
import { remapSubtasksDates } from '../views/gantt/remapDates';
import { formatUser } from '../user/userProvider';
import * as attachmentMod from '../attachment/attachment';
import * as groupSharingPaywall from '../group_sharing/paywall';
import { deleteItemsAsync } from '../task/services/deleteTaskService';
import { PublicSharingCodes, StatusErrorCodes } from '../../utils/errors/constants';
import { getObjectVersionManager } from '../../utils/version/ObjectVersionManagerFactory';
import { TransactionClientImpl } from '../../utils/transaction/TransactionClientImpl';
import { updateObjectVersionUpdatesFromHistoryRows } from '../../utils/version/helpers/HistoryTableHelpers';
import { queryProxyFactory } from '../../utils/queryProxy/queryProxyFactory';
import { CachedTemplatesQueryProxyHttpClient } from '../../utils/queryProxy/CachedQueryProxyHttpClient';
import { ItemQueryKey } from './queryMap/queryClasses/ItemQueries';
import { QueryFamily } from './queryMap/queryProxyMap';
import { AttachmentQueryKey } from './queryMap/queryClasses/AttachmentQueries';
import { TemplateQueryKey } from './queryMap/queryClasses/TemplateQueries';
import { promiseMapSeries } from '../../utils/promises';
import { coeditorClientInstance } from '../../clients/CoeditorClient';
import { EntityType as CoEditorEntityType } from '../coeditor/constants';
import {
    createScheduledDueDateTriggers,
    createScheduledStartDateTriggers,
} from '../../automation/services/automationScheduledTriggersService';
import { getTeamId } from '../../utils/entities/services/entitiesService';
import { getTeamForTemplatesUsingTaskIds } from '../../utils/entities/datastores/entitiesDatastore';
import { prepareUpdateYdocQueries } from '../../utils/content/contentQueryFactory';
import { ContentEntityType } from '../../utils/content/interfaces';
import { entitlementService, EntitlementName } from '../entitlements/entitlementService';
import { saveTaskTemplateCustomTaskType } from '../custom_items/templateHelpers';
import { assertCorrectChallengeTokenOrThrow, getSecurePublicKey } from './publicKey';
import { deduplicateTemplateIdsByPermanentTemplateId, TemplateType } from './templateIdHelpers';
import { getSyncBlocksByParentIds } from '../sync_blocks/syncBlockService';
import { prepareSyncBlockQueries } from '../sync_blocks/syncBlockQueryFactory';
import { getTaskFollowersToMergeFromTemplateQueryFactory } from './dataAccess/queryFactories/get-task-followers-to-merge-from-template.query-factory';
import { getTaskAssigneesToMergeFromTemplateQueryFactory } from './dataAccess/queryFactories/get-task-assignees-to-merge-from-template.query-factory';
import { CuPublicTemplatesRepository } from './publicTemplates/publicTemplateRepository';
import { getTaskTemplateAssigneesToCopyIntoTaskQueryFactory } from './dataAccess/queryFactories/get-task-template-assignees-to-copy-into-task-query.factory';
import { _getItem } from '../task/CRUD/getTask'; // eslint-disable-line import/no-cycle
import { getNewStartDateUsingGantt } from '../scheduling/gantt/ganttCalculateDates';
import { getUsersInfo } from '../user/datastores/CRUD/getUsers';
import { remapDateOfTaskV2 } from '../task/dateHelpers';

import { checkTemplateV1 as _checkTemplateV1 } from './checkTemplateV1';
import { sendNotificationForAssignees } from './taskNotificationsUtils';
import { checkEntitlements as checkTemplateEntitlements } from './templateEntitlements';

export { _checkTemplateV1 };

const logger = getLogger('subcategoryTemplates');
const TemplateError = ClickUpError.makeNamedError('subcategoryTemplates');
const logTemplateError = ClickUpError.getIsolatedErrorHandler('subcategoryTemplates');
const smartCallback = makeSmartCallback(TemplateError);
const TaskError = ClickUpError.makeNamedError('task');
const AccessError = ClickUpError.makeNamedError('access');

async function promiseAccessTaskTemplateUpdate(userid, task_id, template_id, options = {}) {
    const [task, template] = await Promise.all([
        access.promiseCheckAccessTask(userid, task_id, options),
        access.promiseAccessTaskTemplate(userid, template_id),
    ]);

    if (task.team_id !== template.team_id) {
        logger.warn({ msg: 'Unauthorized task template edit attempt by user', template_id, task_id });
        throw new AccessError('You do not have permission to edit this template', 'ACCESS_040', 403);
    }
    return task;
}

export const metricNames = {
    getTemplatesV1: {
        executionCount: {
            total: 'task_templates.get_templates_v1.execution_count.total',
            subcategory: 'task_templates.get_templates_v1.execution_count.subcategory',
            category: 'task_templates.get_templates_v1.execution_count.category',
            project: 'task_templates.get_templates_v1.execution_count.project',
            team: 'task_templates.get_templates_v1.execution_count.team',
            generateQuery: 'task_templates.get_templates_v1.execution_count.generate_query',
        },
    },
};

function generateV1TemplateQuery(userid, options, in_params = []) {
    metricsClient.increment(metricNames.getTemplatesV1.executionCount.generateQuery);

    const params = [...in_params];
    const valid_permission_levels = [];
    Object.keys(config.permission_levels).forEach(key => {
        if (config.permission_levels[key].template) {
            valid_permission_levels.push(key);
        }
    });

    const useridIdx = params.push(userid);
    const validPermissionsIndex = params.push(valid_permission_levels);

    let tagsJoin = ``;
    if (options.tags && options.tags.length) {
        tagsJoin = `
            LEFT OUTER JOIN task_mgmt.template_tags
                ON template_tags.template_id = items.permanent_template_id
                AND template_tags.template_type = 'task'
                AND template_tags.deleted IS NOT TRUE `;
    }

    let query = `
        SELECT
                ${
                    options.only_counts
                        ? `count(*)::INT`
                        : `
                items.id,
                items.id as original_id,
                coalesce(template_options.name, items.name) as name,
                template_options.description,
                template_options.settings_counts as counts,
                items.public_sharing,
                items.date_updated,
                items.date_created,
                items.custom_type,
                items.permissions AS template_visibility,
                template_options.options,
                used_task_templates.date_used,
                items.permanent_template_id,
                items.due_date,
                1 AS version,
                users.id as userid,
                users.email,
                users.username,
                users.color,
                users.profile_picture_key,
                template_options.template_id`
                }
        FROM task_mgmt.team_members
            JOIN task_mgmt.items
                ON team_members.team_id = items.workspace_id
                AND team_members.userid = $${useridIdx}
            JOIN task_mgmt.template_options
                ON items.id = template_options.template_id
                AND template_options.type = 'task'
            JOIN task_mgmt.projects
                ON projects.team = items.workspace_id
            JOIN task_mgmt.categories
                ON categories.project_id = projects.id
            JOIN task_mgmt.subcategories
                ON subcategories.category = categories.id
            LEFT OUTER JOIN task_mgmt.users
                ON users.id = items.creator
            LEFT OUTER JOIN task_mgmt.items AS parent
                ON items.parent = parent.id
            LEFT OUTER JOIN task_mgmt.task_members
                ON
                    tasK_members.task_id = coalesce(items.parent, items.id)
                    AND task_members.userid = $${useridIdx}
                    AND task_members.permission_level = ANY($${validPermissionsIndex})
            LEFT OUTER JOIN task_mgmt.subcategory_members
                ON
                    subcategory_members.subcategory = subcategories.id
                    AND subcategory_members.userid = $${useridIdx}
                    AND subcategory_members.permission_level = ANY($${validPermissionsIndex})
            LEFT OUTER JOIN task_mgmt.category_members
                ON
                    category_members.category = categories.id
                    AND category_members.userid = $${useridIdx}
                    AND category_members.permission_level = ANY($${validPermissionsIndex})
            LEFT OUTER JOIN task_mgmt.project_members
                ON
                    project_members.project_id = projects.id
                    AND project_members.userid = $${useridIdx}
                    AND project_members.permission_level = ANY($${validPermissionsIndex})
            LEFT OUTER JOIN task_mgmt.used_task_templates
                ON  used_task_templates.userid = $${useridIdx}
                    AND items.id = used_task_templates.id
            ${tagsJoin}
        WHERE   items.template = true
                AND items.deleted = false
                AND items.team_id IS NULL
                AND items.subcategory=subcategories.id
                AND (
                    task_members.userid IS NOT NULL
                    OR (
                        (
                            (items.PRIVATE = FALSE AND items.parent IS NULL)
                            OR parent.private = false
                        )
                        AND (
                            subcategory_members.userid IS NOT NULL
                            OR (
                                subcategories.PRIVATE = FALSE
                                AND (
                                    category_members.userid IS NOT NULL
                                    OR (
                                        categories.PRIVATE = FALSE
                                        AND (
                                            project_members.userid IS NOT NULL
                                            OR (
                                                team_members.ROLE != 4
                                                AND projects.PRIVATE = FALSE
                                            )
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
    `;

    if (options.date_created) {
        params.push(options.date_created);
        query += ` AND items.date_created > $${params.length}`;
    }

    if (options.subcategory_id) {
        params.push(options.subcategory_id);
        query += ` AND items.subcategory = $${params.length}`;
    }

    if (options.category_id) {
        params.push(options.category_id);
        query += ` AND categories.id = $${params.length}`;
    }

    if (options.project_id) {
        params.push(options.project_id);
        query += ` AND projects.id = $${params.length}`;
    }

    if (options.team_id) {
        params.push(options.team_id);
        query += ` AND items.workspace_id = $${params.length}`;
    }

    if (options.creator) {
        params.push(options.creator);
        query += ` AND items.creator = $${params.length}`;
    }

    if (options.tags && options.tags.length) {
        params.push(options.tags);
        query += ` AND template_tags.tag_id = ANY($${params.length})`;
    }

    if (options.name) {
        if (options.desc) {
            params.push(`%${options.name.toLowerCase()}%`);
            params.push(`%${options.desc.toLowerCase()}%`);
            query += ` AND (LOWER(items.name) ILIKE $${
                params.length - 1
            } OR LOWER(template_options.description) ILIKE $${params.length})`;
        } else {
            params.push(`%${options.name.toLowerCase()}%`);
            query += ` AND LOWER(items.name) ILIKE $${params.length}`;
        }
    }

    if (options.public_tags && options.public_tags.length) {
        params.push(options.public_tags);
        query += ` AND public_template_to_tag.public_template_tag_id = ANY($${params.length})`;
    }

    if (options.recently_used) {
        query += ` AND used_task_templates.date_used IS NOT NULL `;

        if (!options.skip_order) {
            query += ` ORDER BY used_task_templates.date_used DESC LIMIT 20 `;
        }
    } else if (options.paging) {
        if (options.start) {
            params.push(options.start_id, options.start);
            query += ` AND (items.name > $${params.length} OR (items.name = $${
                params.length
            } AND template_options.template_id < $${params.length - 1}))`;
        }
    }

    return { query, params };
}

export function _getTemplates(userid, options, cb) {
    metricsClient.increment(metricNames.getTemplatesV1.executionCount.total);

    if (!options.category_id && !options.project_id && !options.team_id && !options.subcategory_id) {
        cb({
            err: 'Must provide a filter',
            status: 400,
            ECODE: 'TSKTEMP_003',
        });
        return;
    }
    async.parallel(
        {
            accessSubcategory(para_cb) {
                if (!options.subcategory_id) {
                    para_cb();
                    return;
                }
                metricsClient.increment(metricNames.getTemplatesV1.executionCount.subcategory);

                access.checkAccessSubcategory(userid, options.subcategory_id, { permissions: [] }, para_cb);
            },
            accessCategory(para_cb) {
                if (!options.category_id) {
                    para_cb();
                    return;
                }
                metricsClient.increment(metricNames.getTemplatesV1.executionCount.category);

                access.checkAccessCategory(userid, options.category_id, { permissions: [] }, para_cb);
            },
            accessProject(para_cb) {
                if (!options.project_id) {
                    para_cb();
                    return;
                }
                metricsClient.increment(metricNames.getTemplatesV1.executionCount.project);

                access.checkAccessProject(userid, options.project_id, { permissions: [] }, para_cb);
            },
            accessTeam(para_cb) {
                if (!options.team_id) {
                    para_cb();
                    return;
                }
                metricsClient.increment(metricNames.getTemplatesV1.executionCount.team);

                access.checkAccessTeam(userid, options.team_id, { permissions: [] }, para_cb);
            },
            templates(para_cb) {
                const { query, params } = generateV1TemplateQuery(userid, options);
                db.readQuery(query, params, (error, result) => {
                    if (error || options.only_counts) {
                        para_cb(error, result);
                        return;
                    }
                    const templates = result.rows;
                    loadTemplatesUserUsageData(userid, 'task', templates)
                        .then(UserUsageDataResult => para_cb(null, { rows: UserUsageDataResult }))
                        .catch(UserUsageDataErr => para_cb(UserUsageDataErr));
                });
            },
        },
        (err, result) => {
            if (err) {
                if (err.ECODE) {
                    cb(err);
                } else {
                    logger.error({
                        msg: 'Failed to look up templates',
                        err,
                        ECODE: 'TSKTEMP_002',
                        status: 500,
                    });
                    cb({
                        err: 'Internal server error',
                        status: 500,
                        ECODE: 'TSKTEMP_002',
                    });
                }
                return;
            }
            const { result: templateRows } =
                options.paging && !options.only_counts && !options.skip_order
                    ? applyTemplatesPaginationByUsedCountUsingPage(result.templates.rows, options.page || 0)
                    : { result: result.templates.rows };
            const deduplicatedTemplates = deduplicateTemplateIdsByPermanentTemplateId(
                TemplateType.Task,
                templateRows,
                userid
            );
            const task_ids = [];
            const options_map = {};
            const id_to_permanent_map = new Map();
            for (const template of deduplicatedTemplates) {
                task_ids.push(template.id);
                options_map[template.id] = templateRows.find(row => row.id === template.id).options;
                id_to_permanent_map.set(template.id, template.permanent_template_id || template.id);
            }
            const { fields } = options;
            // TODO: Remove {replica: true} once safe https://app.clickup-stg.com/t/333/CLK-569460
            _getItems(task_ids, { userid, fields, replica: true }, (err2, result2) => {
                if (err2) {
                    cb(err2);
                    return;
                }

                const templates = result2.map(_task => {
                    const task = _task;
                    task.options = options_map[task.id];
                    task.id = id_to_permanent_map.get(task.id);
                    return task;
                });

                cb(err, { templates });
            });
        }
    );
}

export function getTemplates(req, resp, next) {
    const userid = req.decoded_token.user;
    const { team_id, project_id, category_id, subcategory_id, recently_used, fields, paging, start, start_id, name } =
        req.query;

    _getTemplates(
        userid,
        {
            team_id,
            project_id,
            category_id,
            subcategory_id,
            recently_used,
            fields,
            paging,
            start,
            start_id,
            name,
        },
        (err, result) => {
            if (err) {
                next(err);
            } else {
                resp.status(200).send(result);
            }
        }
    );
}

async function getTemplateObject(userid, template_id, use_master) {
    const params = [template_id];

    const query = `
        SELECT
            items.id,
            items.id as original_id,
            coalesce(template_options.name, items.name) as name,
            template_options.description,
            template_options.settings_counts as counts,
            items.public_sharing,
            items.permissions AS template_visibility,
            items.date_updated,
            items.date_created,
            template_options.options,
            used_task_templates.date_used,
            items.permanent_template_id,
            items.due_date,
            2 AS version,
            users.id as userid,
            users.email,
            users.username,
            users.color,
            users.profile_picture_key,
            template_options.template_id
        FROM task_mgmt.items
        JOIN task_mgmt.template_options
            ON
                items.id = template_options.template_id
                AND template_options.type = 'task'
        LEFT OUTER JOIN task_mgmt.used_task_templates
            ON
                used_task_templates.userid = $${params.push(userid)}
                AND items.id = used_task_templates.id
        LEFT OUTER JOIN task_mgmt.task_template_members
            ON task_template_members.task_id = items.id
            AND task_template_members.userid = $${params.push(userid)}
        LEFT OUTER JOIN task_mgmt.users
            ON users.id = items.creator
        WHERE
            items.template = TRUE
            AND items.deleted = FALSE
            AND items.id = $1
    `;

    try {
        const permanent_id = await templateHelpers._idByPermanentId(template_id, config.hierarchy_tables.tasks);
        await access.promiseAccessTaskTemplate(userid, permanent_id);

        let result;
        if (use_master) {
            result = await db.promiseReadQuery(query, params);
        } else {
            result = await db.promiseReplicaQuery(query, params);
        }
        const [template] = result.rows;

        const queryMembers = `
            SELECT task_id, userid
            FROM task_mgmt.task_template_members
            WHERE task_id = $1
        `;

        const response = await db.promiseReplicaQuery(queryMembers, [template_id]);

        template.template_members = [];
        if (template.userid) {
            template.creator = formatUser({
                id: template.userid,
                username: template.username,
                email: template.email,
                color: template.color,
                profile_picture_key: template.profile_picture_key,
            });
        } else {
            template.creator = null;
        }

        delete template.userid;
        delete template.username;
        delete template.email;
        delete template.color;
        delete template.profile_picture_key;

        if (template.template_visibility === config.template_permissions.members) {
            template.template_members = response.rows.filter(row => row.task_id === template.id).map(row => row.userid);
        }

        const [subtask_map, attachments, tags] = await Promise.all([
            promiseGetSubtasksWithDueDates([template_id], {}),
            attachmentMod.getAttachmentsOfParents([template.permanent_template_id], config.attachments.types.task),
            templateTags.getTemplateTags([template.permanent_template_id], 'task'),
        ]);

        template.tags = tags[template.permanent_template_id] || [];
        template.attachments = attachments.attachments[template.permanent_template_id] || [];
        template.subtasks_with_due_dates = subtask_map[template.id]
            ? subtask_map[template.id].subtasks_with_due_dates
            : 0;
        template.id = template.permanent_template_id || template.id;
        delete template.permanent_template_id;
        template.public_key = template.public_sharing ? getSecurePublicKey(template.id) : null;

        return template;
    } catch (e) {
        throw new TemplateError(e, 'TSKTEMP_777', 500);
    }
}

export async function saveTaskTemplatesMigrated(team_id) {
    await db.promiseWriteQuery(
        `INSERT INTO task_mgmt.team_task_templates_migrated(team_id, date_migrated)
        VALUES ($1, $2)
        ON CONFLICT (team_id) DO NOTHING`,
        [team_id, Date.now()]
    );
}

async function checkV1TemplatesMigrated(team_id) {
    const result = await db.promiseReplicaQuery(
        `SELECT 1 FROM task_mgmt.team_task_templates_migrated WHERE team_id = $1`,
        [team_id]
    );

    return result.rows.length > 0;
}

/**
 * The world famous Get Template, better then ever!
 * @param {number} userid
 * @param {number} team_id
 * @param {object} options - Options list is not complete
 * @param {boolean} [options.recently_used] - Include ONLY recently user Templates
 * @param {string} [options.start] - Name of the last previously paged template
 * @param {string} [options.start_id] - Id of the last previously paged template
 * @param {number} [options.start_used_count] - Used count of the last previously paged template
 *
 * @returns {ret_val}
 *
 * @typedef {Object} ret_val
 * @property {[Object]} templates
 */
export async function _getTemplatesV2(userid, team_id, options = {}) {
    let include_v1 = !!options.include_v1; // default to false if not present
    try {
        const accessResponse = await access.promiseAccessTeam(userid, team_id, {});

        if (include_v1) {
            const v1_all_migrated = await checkV1TemplatesMigrated(team_id);
            if (v1_all_migrated) {
                include_v1 = false;
            }
        }

        let params = [];
        let last_template;
        let nameWhere = '';
        let searchForNameInTagsJoin = '';
        if (options.name) {
            if (options.desc) {
                params.push(`%${options.name.toLowerCase()}%`);
                params.push(`%${options.desc.toLowerCase()}%`);
                nameWhere = ` AND (LOWER(items.name) ILIKE $${
                    params.length - 1
                } OR LOWER(template_options.description) ILIKE $${params.length} OR team_template_tags.name ILIKE $${
                    params.length - 1
                })`;
            } else {
                params.push(`%${options.name.toLowerCase()}%`);
                nameWhere = ` AND ( LOWER(items.name) ILIKE $${params.length} OR team_template_tags.name ILIKE $${params.length} )`;
            }

            searchForNameInTagsJoin = `
                    LEFT OUTER JOIN task_mgmt.template_tags
                        ON template_tags.template_id = items.permanent_template_id
                        AND template_tags.template_type = 'task'
                        AND template_tags.deleted IS NOT TRUE
                    LEFT OUTER JOIN task_mgmt.team_template_tags
                    ON template_tags.tag_id = team_template_tags.id `;
        }

        let date_created_where = ``;
        if (options.date_created) {
            params.push(options.date_created);
            date_created_where = `AND items.date_created > $${params.length}`;
        }

        let adminWhere = '';
        if ([config.team_roles.owner, config.team_roles.admin].includes(accessResponse.role)) {
            adminWhere = `
            OR items.permissions = $${params.push(config.template_permissions.admin)}
        `;
        }

        let tagsJoin = ``;
        let tagsWhere = ``;
        if (options.tags && options.tags.length) {
            tagsJoin = `
                LEFT OUTER JOIN task_mgmt.template_tags
                    ON template_tags.template_id = items.permanent_template_id
                    AND template_tags.template_type = 'task'
                    AND template_tags.deleted IS NOT TRUE `;
            params.push(options.tags);
            tagsWhere = ` AND template_tags.tag_id = ANY($${params.length})`;
        }
        let query = `
            SELECT
                ${
                    options.only_counts
                        ? `count(*)::INT`
                        : `
                items.id,
                items.id as original_id,
                coalesce(template_options.name, items.name) as name,
                template_options.description,
                template_options.settings_counts as counts,
                items.public_sharing,
                items.permissions AS template_visibility,
                items.date_updated,
                items.date_created,
                items.custom_type,
                template_options.options,
                NULL AS date_used,
                items.permanent_template_id,
                items.due_date,
                2 AS version,
                users.id as userid,
                users.email,
                users.username,
                users.color,
                users.profile_picture_key,
                template_options.template_id`
                }
            FROM task_mgmt.team_members
            JOIN task_mgmt.items
            ON items.team_id = team_members.team_id
            JOIN task_mgmt.template_options
                ON
                    items.id = template_options.template_id
                    AND template_options.type = 'task'
            LEFT OUTER JOIN task_mgmt.task_template_members
                ON task_template_members.task_id = items.id
                AND task_template_members.userid = $${params.push(userid)}

            LEFT JOIN LATERAL (
                SELECT
                    task_template_group_members.task_id
                FROM
                    task_mgmt.group_members
                    JOIN task_mgmt.task_template_group_members ON
                        task_template_group_members.group_id = group_members.group_id AND
                        task_template_group_members.task_id = items.id
                WHERE
                    group_members.userid = $${params.push(userid)}
                LIMIT 1
            ) AS task_template_group_member ON task_template_group_member.task_id = items.id

            LEFT OUTER JOIN task_mgmt.users
                ON users.id = items.creator
            ${searchForNameInTagsJoin || tagsJoin}
            WHERE
                team_members.userid = $${params.push(userid)}
                AND items.template = TRUE
                AND items.deleted = FALSE
                ${nameWhere}
                ${tagsWhere}
                ${date_created_where}
                AND items.team_id = $${params.push(team_id)}
                AND (
                    (
                        items.permissions = $${params.push(config.template_permissions.public)}
                        OR items.permissions IS NULL
                    )
                    OR
                    (
                        items.permissions = $${params.push(config.template_permissions.team_members)}
                        AND team_members.role != $${params.push(config.team_roles.guest)}
                    )
                    ${adminWhere}
                    OR (
                        items.permissions = $${params.push(config.template_permissions.members)}
                        AND task_template_members.task_id IS NOT NULL
                    )
                    OR (
                        items.permissions = $${params.push(config.template_permissions.members)}
                        AND task_template_group_member.task_id IS NOT NULL
                    )
                    OR (
                        items.permissions = $${params.push(config.template_permissions.private)}
                        AND items.owner = $${params.push(userid)}
                    )
                )`;

        if (options.creator) {
            params.push(options.creator);
            query += ` AND items.creator = $${params.length}`;
        }

        if (include_v1 && !config.v2_migrated_template_teams.includes(Number(team_id))) {
            // @todo
            // once we confirm that v1 are no longer used on prod, we can remove related code.
            logger.debug({
                msg: 'Getting task v1 templates!',
                workspace_id: team_id,
            });
            const union_options = { ...options, skip_order: true };
            const v1_union = generateV1TemplateQuery(userid, union_options, params);
            query += ` UNION ${v1_union.query}`;
            params = v1_union.params;
        }

        let last_page = true;
        let { rows: templates } = await db.promiseReplicaQuery(query, params);

        if (!options.only_counts) {
            templates = await loadTemplatesUserUsageData(userid, 'task', [...templates]);
            if (options.recently_used) {
                templates = _.filter(templates, 'date_used');
                templates = _.sortBy(templates, [({ date_used }) => -date_used]);
            } else if (options.paging) {
                ({ result: templates, isLastPage: last_page } = options.page
                    ? applyTemplatesPaginationByUsedCountUsingPage([...templates], options.page)
                    : applyTemplatesPaginationByUsedCountUsingName([...templates], options.start));
            }
        }

        if (!last_page) {
            last_template = templates[templates.length - 1];
        }

        if (!options.only_counts) {
            const deduplicatedTemplateIds = deduplicateTemplateIdsByPermanentTemplateId(
                TemplateType.Task,
                templates,
                userid
            ).map(template => template.id);
            templates = uniqBy(
                templates.filter(row => deduplicatedTemplateIds.includes(row.id)),
                'id'
            );
        }

        const v2_templates = templates.filter(row => row.version === 2);

        const [{ rows: template_members }, { rows: template_group_members }] = await Promise.all([
            db.promiseReplicaQuery(
                `
                SELECT task_id, userid
                FROM task_mgmt.task_template_members
                WHERE task_id = ANY( $1 )
            `,
                [v2_templates.map(template => template.id)]
            ),
            db.promiseReplicaQuery(
                `
                SELECT task_id, group_id
                FROM task_mgmt.task_template_group_members
                WHERE task_id = ANY( $1 )
            `,
                [v2_templates.map(template => template.id)]
            ),
        ]);

        for (const template of v2_templates) {
            template.template_members = [];

            if (template.userid) {
                template.creator = formatUser({
                    id: template.userid,
                    username: template.username,
                    email: template.email,
                    color: template.color,
                    profile_picture_key: template.profile_picture_key,
                });
            } else {
                template.creator = null;
            }

            delete template.userid;
            delete template.username;
            delete template.email;
            delete template.color;
            delete template.profile_picture_key;

            template.template_visibility = Number(template.template_visibility);
            if (template.template_visibility === config.template_permissions.members) {
                template.template_members = template_members
                    .filter(row => row.task_id === template.id)
                    .map(row => row.userid);

                template.template_group_members = template_group_members
                    .filter(row => row.task_id === template.id)
                    .map(row => row.group_id);

                const { groups = [] } = await groupMod.getUserGroupsAsync(null, null, {
                    skipAccess: true,
                    group_ids: template.template_group_members,
                });

                template.template_group_members = groups;
            }
            template.used_count = Number(template.used_count);
        }

        if (include_v1 && !config.v2_migrated_template_teams.includes(Number(team_id))) {
            const v1_templates = templates.filter(row => row.version === 1);

            if (!v1_templates.length) {
                saveTaskTemplatesMigrated(team_id);
            } else {
                // kick off conversion in the background
                const templateIds = v1_templates.map(template => template.id);
                logger.info({ message: 'Converting templates from v1 to v2', templateIds, team_id, userid });

                _convertV1ToV2(userid, templateIds, { do_not_throw: true });
            }

            const { members: all_members, group_members: all_group_members } =
                await taskMember._promiseHierarchyMembers(
                    userid,
                    v1_templates.map(template => template.id),
                    { includeTeamMembers: true, v2: true }
                );

            for (const template of v1_templates) {
                template.template_members = [];
                template.public_sharing = false;
                template.template_visibility = 0;
                const members = Object.values(all_members[template.id])
                    .reduce((acc, group) => [...acc, ...group], []) // eslint-disable-line no-loop-func
                    .map(member => member.user.id);
                const group_members = Object.values(all_group_members[template.id]).reduce((acc, group) => {
                    group = group.map(grp => grp.group);
                    return [...acc, ...group];
                }, []);

                template.template_members = members;
                template.template_group_members = group_members;

                if (template.userid) {
                    template.creator = formatUser({
                        id: template.userid,
                        username: template.username,
                        email: template.email,
                        color: template.color,
                        profile_picture_key: template.profile_picture_key,
                    });
                } else {
                    template.creator = null;
                }

                delete template.userid;
                delete template.username;
                delete template.email;
                delete template.color;
                delete template.profile_picture_key;
                delete template.date_used;
            }
        }

        const permanent_template_ids = templates.map(template => template.permanent_template_id);
        const [subtask_map, attachments, tags] = await Promise.all([
            promiseGetSubtasksWithDueDates(
                templates.map(template => template.id),
                {}
            ),
            attachmentMod.getAttachmentsOfParents(permanent_template_ids, config.attachments.types.task),
            templateTags.getTemplateTags(permanent_template_ids, 'task'),
        ]);

        templates.forEach(template => {
            template.tags = tags[template.permanent_template_id] || [];
            template.attachments = attachments.attachments[template.permanent_template_id] || [];
            template.subtasks_with_due_dates = subtask_map[template.id]
                ? subtask_map[template.id].subtasks_with_due_dates
                : 0;
            template.id = template.permanent_template_id || template.id;
            delete template.permanent_template_id;
            template.public_key = template.public_sharing ? getSecurePublicKey(template.id) : null;
        });

        const ret_val = {
            templates: options.only_counts ? [] : templates,
            last_page,
            paging: {
                ...(last_template && {
                    start: last_template.name,
                    start_id: last_template.template_id,
                    start_used_count: last_template.used_count,
                }),
            },
            full_count: options.only_counts ? templates.reduce((acc, b) => acc + b.count, 0) : null,
        };

        return ret_val;
    } catch (e) {
        throw new TemplateError(e, 'TSKTEMP_018', 500);
    }
}

export async function promiseGetTemplatesV2(user_id, team_id, options = {}) {
    return _getTemplatesV2(user_id, team_id, options);
}
export async function getTemplatesV2(req, res, next) {
    const userid = req.decoded_token.user;

    const { team_id, recently_used, fields, paging, start, start_id, name, task_id } = req.query;

    const options = {
        fields,
        include_v1: true,
        name,
        recently_used,
        paging,
        start,
        start_id,
        task_id,
        team_id,
    };

    try {
        const results = await _getTemplatesV2(userid, team_id, options);
        res.status(200).send(results);
    } catch (err) {
        next(err);
    }
}

async function _getTemplate(template_id, userid, options = {}) {
    const public_key = !!options.public_key; // default to false if not provided
    const { proxyClient = queryProxyFactory() } = options;

    try {
        const { return_template } = options;
        const { rows } = await proxyClient.readQueryAsync({
            queryFamily: QueryFamily.Item,
            queryKey: ItemQueryKey.SelectTaskTemplate,
            queryParams: [template_id],
            options: { return_template },
        });

        if (!rows.length) {
            throw new TemplateError('Could not find template', 'TSKTEMP_013', 404);
        }
        const template = rows[0];

        if (!template.public_sharing) {
            await access.promiseAccessTaskTemplate(userid, template_id);
        }

        if (!options.return_template) {
            template.id = template.permanent_template_id || template_id;
            delete template.permanent_template_id;
            delete template.public_sharing;
        }

        if (public_key) {
            template.public_key = getSecurePublicKey(template.id);
        }
        return template;
    } catch (err) {
        throw new TemplateError(err, 'TSKTEMP_012', 500);
    }
}

/**
 * Create Task template
 * @param {number} userid
 * @param {object} options - Options list is not complete
 * @param {boolean} [options.relationships] - To include Task Relationship ( List relationship is not applicable)
 * @param {boolean} [options.custom_fields] - To include Custom Fields, but having own options ( relationships )
 * @param {boolean} [options.custom_type] - To include custom task type or not.
 * @param {function} cb
 */
export function _createTemplate(userid, options, cb) {
    const now = Date.now();
    let date_created;
    let settings_counts;
    let count_details;
    let team_id;

    const { proxyClient = queryProxyFactory() } = options;

    // FE use options.relationships to manage task relationship and list relationship
    options.task_relationships = options.relationships;

    const is_template_update = !!options.template_id;
    const TASK_TEMPLATE_TYPE = config.get('template_types.task');

    async.parallel(
        {
            async checkAccess(para_cb) {
                if (!is_template_update && options.public_template) {
                    para_cb();
                    return;
                }
                const permissions = [config.permission_constants.template];
                try {
                    let result;
                    if (is_template_update) {
                        result = await promiseAccessTaskTemplateUpdate(userid, options.task_id, options.template_id, {
                            permissions,
                        });
                    } else {
                        result = await access.promiseCheckAccessTask(userid, options.task_id, { permissions });
                    }
                    para_cb(null, result);
                } catch (err) {
                    para_cb(err);
                }
            },

            validateName(para_cb) {
                if (options.template_v2) {
                    para_cb();
                    return;
                }

                let query = `
                    SELECT id
                    FROM task_mgmt.items
                    WHERE LOWER(name) = LOWER($1)
                        AND deleted = false
                        AND template = true
                        AND subcategory = `;
                const params = [options.name];

                if (options.subcategory_id) {
                    query += ' $2';
                    params.push(options.subcategory_id);
                } else {
                    query += ' (SELECT subcategory FROM task_mgmt.items WHERE id = $2)';
                    params.push(options.task_id);
                }

                if (options.template_id) {
                    params.push(options.template_id);
                    query += ` AND id != $${params.length}`;
                }

                db.readQuery(query, params, (err, result) => {
                    if (err) {
                        logger.error({
                            msg: 'Failed to check if task name is taken',
                            err,
                            status: 500,
                            ECODE: 'TSKTEMP_008',
                        });
                        para_cb({
                            err: 'Internal server error',
                            status: 500,
                            ECODE: 'TSKTEMP_008',
                        });
                    } else if (result.rows.length > 0) {
                        para_cb({
                            err: 'Template name taken',
                            status: 400,
                            ECODE: 'TSKTEMP_009',
                        });
                    } else {
                        para_cb();
                    }
                });
            },
            async getPermanentTemplateId(para_cb) {
                if (!options.template_id) {
                    para_cb();
                    return;
                }
                try {
                    options.old_permanent_template_id = await templateHelpers._getPermanentTemplateId(
                        options.template_id,
                        options.team_id
                    );
                    para_cb();
                } catch (err) {
                    para_cb({
                        err,
                        status: 500,
                        ECODE: 'TSKTEMP_059',
                    });
                }
            },
            async getTeamId(para_cb) {
                try {
                    if (options.team_id) {
                        team_id = options.team_id;
                    } else if (options.template_id) {
                        const { rows } = await getTeamForTemplatesUsingTaskIds([options.template_id]);
                        team_id = rows[0]?.team_id;
                    } else {
                        team_id = await getTeamId({ task_ids: [options.task_id] }, {});
                    }
                    para_cb();
                } catch (teamErr) {
                    para_cb(teamErr);
                }
            },
        },
        err => {
            if (err) {
                cb(err);
            } else {
                let template_id = null;
                let permanent_template_id = null;
                let from_private;
                let old_to_new;

                options.return_immediately = true;

                async.series(
                    [
                        series_cb => {
                            const _options = Object.assign({}, options);

                            _options.custom_fields = true; // saving everything
                            _options.list_relationships = true;
                            _options.old_subtask_assignees = true;
                            _options.orderindex_offset = Math.random();
                            _options.ret_from_private = true;
                            _options.graceful_shutdown = options.return_immediately;
                            _options.return_immediately = true;
                            _options.set_deleted = true;
                            _options.skip_privacy = true;
                            _options.task_activity = false;
                            _options.task_relationships = true; // saving everything
                            _options.top = true;
                            _options.via = TaskCreatedVia.Template;
                            _options.proxyClient = proxyClient;

                            if (!_options.template_v2) {
                                _options.template = true;
                            }

                            options[TemplateTracer.KEY]?.suspendFinishProcessing();
                            copyTask._copyTask(
                                userid,
                                options.task_id,
                                _options,
                                (cb1_err, id_result) => {
                                    options[TemplateTracer.KEY]?.restoreFinishProcessing();
                                    cb(cb1_err, id_result);
                                },
                                (copy_error, result) => {
                                    options[TemplateTracer.KEY]?.restoreFinishProcessing();

                                    if (copy_error) {
                                        series_cb(new TemplateError(copy_error, 'TSKTEMP_014'));
                                    } else {
                                        template_id = result.id;
                                        permanent_template_id = result.permanent_template_id;
                                        from_private = result.from_private;
                                        old_to_new = result.old_to_new;

                                        series_cb();
                                    }
                                }
                            );
                        },
                        async series_cb => {
                            // get settings counts
                            ({ counts: settings_counts, count_details } = await templateHelpers.getSettingsCounts(
                                template_id,
                                'task',
                                null,
                                {
                                    task_id: options.task_id,
                                    proxyClient,
                                }
                            ));
                            series_cb();
                        },
                        series_cb => {
                            const _options = {
                                archived: options.archived,
                                assigned_comment: options.assigned_comment,
                                assignees: options.assignees,
                                attachments: options.attachments,
                                comment: options.comment,
                                comment_attachments: options.comment_attachments,
                                content: options.content,
                                count_details,
                                custom_fields: options.custom_fields,
                                custom_type: options.custom_type,
                                due_date: options.due_date,
                                due_date_time: options.due_date_time,
                                external_dependencies: options.external_dependencies,
                                followers: options.followers,
                                from_private,
                                internal_dependencies: options.internal_dependencies,
                                old_assignees: options.old_assignees,
                                old_checklists: options.old_checklists,
                                old_due_date: options.old_due_date,
                                old_followers: options.old_followers,
                                old_orderindex: options.old_orderindex,
                                old_start_date: options.old_start_date,
                                old_status: options.old_status,
                                old_statuses: options.old_statuses,
                                old_subtask_assignees: options.old_subtask_assignees,
                                old_tags: options.old_tags,
                                old_duration: options.old_duration,
                                parent: -1,
                                priority: options.priority,
                                recur_settings: options.recur_settings,
                                relationships: options.task_relationships,
                                remap_start_date: options.remap_start_date,
                                subcategory_id: options.subcategory_id,
                                subtasks: options.subtasks,
                                subtask_assignees: options.subtask_assignees,
                                subtask_parent: -1,
                                start_date: options.start_date,
                                start_date_time: options.start_date_time,
                                status: options.status,
                                tags: options.tags,
                                time_estimate: options.time_estimate,
                                include_location: options.include_location ?? false,
                            };

                            db.writeQuery(
                                `INSERT INTO task_mgmt.template_options
                                    (type, template_id, options, settings_counts, description, workspace_id)
                                    VALUES('task', $1, $2, $3, $4, $5)`,
                                [template_id, _options, settings_counts, options.description, team_id],
                                series_cb
                            );
                        },
                        async series_cb => {
                            if (options.template_id) {
                                try {
                                    if (options.template_v2) {
                                        const result = await db2.writeAsync(
                                            `UPDATE task_mgmt.items
                                            SET deleted = true, date_deleted = $2, permanent_template_id = NULL
                                            WHERE id = $1 RETURNING date_created`,
                                            [options.template_id, Date.now()],
                                            [
                                                {
                                                    object_type: ObjectType.TASK,
                                                    object_id: options.template_id,
                                                    workspace_id: team_id,
                                                    operation: OperationType.DELETE,
                                                    data: {
                                                        context: { ws_key: options.ws_key },
                                                    },
                                                },
                                            ]
                                        );
                                        if (result.rows.length) {
                                            [{ date_created }] = result.rows;
                                        }
                                        if (permanent_template_id) {
                                            CachedTemplatesQueryProxyHttpClient.invalidateCachedQueryResult(
                                                { templateType: 'task', templateId: permanent_template_id },
                                                {
                                                    queryFamily: QueryFamily.Templates,
                                                    queryKey: TemplateQueryKey.IdByPermanentId,
                                                    queryParams: [permanent_template_id],
                                                    options: { table: 'items' },
                                                }
                                            );
                                        }
                                    } else {
                                        await deleteItemsAsync(userid, [options.template_id], {
                                            workspace_id: team_id,
                                            ws_key: options.ws_key,
                                        });
                                    }
                                    series_cb();
                                } catch (error) {
                                    series_cb(error);
                                }
                            } else {
                                series_cb();
                            }
                        },
                        async series_cb => {
                            try {
                                await db2.writeAsync(
                                    `UPDATE task_mgmt.items
                                    SET deleted = false, date_deleted = null, date_created = $2, date_updated = $3
                                    WHERE id = $1`,
                                    [template_id, date_created || now, now],
                                    [
                                        {
                                            object_type: ObjectType.TASK,
                                            object_id: template_id,
                                            workspace_id: team_id,
                                            operation: OperationType.UPDATE,
                                            data: {
                                                context: { ws_key: options.ws_key },
                                            },
                                        },
                                    ]
                                );
                                series_cb();
                            } catch (error) {
                                series_cb(error);
                            }
                        },
                        series_cb => {
                            if (!options.tags || !options.tags.length || !permanent_template_id) {
                                series_cb();
                                return;
                            }
                            let values = `VALUES`;
                            const params = [permanent_template_id, 'task', team_id];
                            options.tags.forEach((tag, idx) => {
                                if (idx > 0) {
                                    values += `,`;
                                }
                                params.push(tag.id);
                                values += `($1, $2, $${params.length}, false, $3)`;
                            });
                            db.writeQuery(
                                `
                                INSERT INTO task_mgmt.template_tags
                                    (template_id, template_type, tag_id, deleted, workspace_id)
                                    ${values}
                                    ON CONFLICT (template_id, template_type, tag_id)
                                        DO UPDATE SET
                                            deleted = FALSE, deleted_by = NULL, date_deleted = NULL`,
                                params,
                                series_cb
                            );
                        },
                        series_cb => {
                            // copy images
                            proxyClient.replicaQuery(
                                {
                                    queryFamily: QueryFamily.Attachment,
                                    queryKey: AttachmentQueryKey.GetAttachmentsByParent,
                                    queryParams: [options.permanent_template_id],
                                },
                                (attErr, attResult) => {
                                    if (attErr) {
                                        series_cb(attErr);
                                        return;
                                    }

                                    if (!attResult || !attResult.rows.length) {
                                        series_cb();
                                        return;
                                    }

                                    const { rows: attachments } = attResult;

                                    attachmentMod.copyAttachments(
                                        userid,
                                        attachments,
                                        `t-${template_id}`,
                                        'task',
                                        { for_template: true, team_id },
                                        series_cb
                                    );
                                }
                            );
                        },
                    ],
                    err2 => {
                        if (err2) {
                            let returnedError;

                            if (err2.ECODE) {
                                if (err2.ECODE === 'ITEM_076') {
                                    sqsWs.sendWSMessage(
                                        options.permanent_template_id
                                            ? 'sendTemplateUpdateFailed'
                                            : 'sendTemplateCreateFailed',
                                        [
                                            userid,
                                            { ...err2, templateName: options.name },
                                            TASK_TEMPLATE_TYPE,
                                            options.ws_key,
                                            options[TemplateTracer.KEY]?.getHistoryTracingId(),
                                        ]
                                    );
                                }

                                returnedError = err2;
                                if (!options.return_immediately) {
                                    cb(returnedError);
                                }
                            } else {
                                logger.error({
                                    msg: 'Failed to create template',
                                    err: err2,
                                    ECODE: 'TSKTEMP_001',
                                });

                                returnedError = {
                                    err: 'Internal server error',
                                    status: 500,
                                    ECODE: 'TSKTEMP_001',
                                };
                                if (!options.return_immediately) {
                                    cb(returnedError);
                                }
                            }

                            if (options.return_immediately) {
                                options[TemplateTracer.KEY]?.finishTracing(returnedError);
                            }

                            return;
                        }

                        if (options.return_template) {
                            getTemplateObject(userid, template_id, true)
                                .then(template => {
                                    if (options.template_id) {
                                        template.updating = true;
                                    }

                                    template.templateName = options.name;
                                    template.send_template = true;
                                    sqsWs.sendWSMessage(
                                        is_template_update ? 'sendTemplateUpdated' : 'sendTemplateCreated',
                                        [
                                            userid,
                                            template,
                                            TASK_TEMPLATE_TYPE,
                                            options.ws_key,
                                            options[TemplateTracer.KEY]?.getHistoryTracingId(),
                                        ]
                                    );
                                })
                                .catch(() => {});
                        } else if (!options.return_immediately) {
                            cb(null, { template_id });
                        } else if (options.template_v2) {
                            _getTemplate(template_id, userid)
                                .then(template => {
                                    if (!options.send_notifs_to_user) {
                                        sqsWs.sendWSMessage(
                                            is_template_update ? 'sendTemplateUpdated' : 'sendTemplateCreated',
                                            [
                                                userid,
                                                template,
                                                TASK_TEMPLATE_TYPE,
                                                options.ws_key,
                                                options[TemplateTracer.KEY]?.getHistoryTracingId(),
                                            ]
                                        );
                                    }
                                })
                                .catch(() => {});
                        } else {
                            _getItem(userid, template_id, {}, (getErr, taskResult) => {
                                if (getErr) {
                                    return;
                                }
                                if (!options.send_notifs_to_user) {
                                    sqsWs.sendWSMessage('sendTaskCopyComplete', [
                                        userid,
                                        taskResult.id,
                                        options.ws_key,
                                        true,
                                        options[TemplateTracer.KEY]?.getHistoryTracingId(),
                                        true,
                                    ]);
                                }
                            });
                        }

                        options[TemplateTracer.KEY]?.finishTracing();
                    }
                );
            }
        }
    );
}

export async function getPublicTemplate(req, res) {
    const userid = req.decoded_token.user;
    let { template_id } = req.params;
    const token = req.query ? req.query.token : null;

    try {
        assertCorrectChallengeTokenOrThrow(token, template_id);

        const options = {
            proxyClient: await templateHelpers.createProxyClient(template_id, 'task', userid),
        };

        template_id = await templateHelpers._idByPermanentId(template_id, config.hierarchy_tables.tasks, options);
        const template = await _getTemplate(template_id, userid, options);
        res.status(200).send(template);
    } catch (err) {
        res.status(err.status).send(err);
    }
}

async function _promiseCreateTemplate(userid, options) {
    return new Promise((res, rej) => {
        _createTemplate(userid, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}

export async function createTemplate(req, resp, next) {
    const userid = req.decoded_token.user;
    const { task_id } = req.params;
    const options = req.body;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    options.workspace_id = req.headers['x-workspace-id'];

    const templateTracer = new TemplateTracer(EntityType.TASK, TemplateAction.TEMPLATE_SAVING, {
        userId: userid,
        sourceId: task_id,
        destTemplateId: options.template_id,
    });
    options[TemplateTracer.KEY] = templateTracer;

    await templateTracer.withTracing(async () => {
        if (options.template_id) {
            try {
                options.template_id = await templateHelpers._idByPermanentId(
                    options.template_id,
                    config.hierarchy_tables.tasks
                );
            } catch (err) {
                const returnedError = new TemplateError(err, 'TSKTEMP_059', 500);
                templateTracer.finishTracing(returnedError);
                throw returnedError;
            }
        }

        options.task_id = task_id;
        options.default_skip_access = false;
        options.ws_key = ws_key;
        options.template_v2 = req.v2;
        options.return_template = true;

        _createTemplate(userid, options, (err, result) => {
            if (err) {
                next(err);
                templateTracer.finishTracing(err);
            } else {
                resp.status(200).send(result);
            }
        });
    });
}

async function _deleteTemplate(userid, options) {
    const isV1 = await _checkTemplateV1(userid, options.template_id);

    if (!isV1) {
        const accessCheckOptions = {
            permissions: [config.permission_constants.delete],
        };

        await access.promiseAccessTaskTemplate(userid, options.template_id, accessCheckOptions);
    }
    const response = await deleteItemsAsync(userid, [options.template_id], {
        skipAccess: !isV1,
        workspace_id: options.team_id,
        ws_key: options.ws_key,
        template: true,
    });

    return response;
}

export async function deleteTemplate(req, resp, next) {
    const userid = req.decoded_token.user;
    let { template_id } = req.params;

    try {
        template_id = await templateHelpers._idByPermanentId(template_id, config.hierarchy_tables.tasks);
        const {
            rows: [{ team_id }],
        } = await getTeamForTemplatesUsingTaskIds([template_id]);
        const result = await _deleteTemplate(userid, { template_id, team_id });
        resp.status(200).send(result);
    } catch (err) {
        next(err);
    }
}

function fixParentOptionsForPublicTemplateCopy(options) {
    if (options.parent !== -1 && options.subtask_parent !== -1) {
        return options;
    }

    options = { ...options };

    if (options.parent === -1) {
        delete options.parent;
    }

    if (options.subtask_parent === -1) {
        delete options.subtask_parent;
    }

    return options;
}

async function _copyPublicTemplate(userid, template_id, options) {
    options = fixParentOptionsForPublicTemplateCopy(options);

    if (
        options.template_visibility != null &&
        !Object.values(config.template_permissions).includes(options.template_visibility)
    ) {
        throw new TemplateError('Invalid permission type', 'TSKTEMP_034', 400);
    }

    const { proxyClient = queryProxyFactory() } = options;

    template_id = await templateHelpers._idByPermanentId(template_id, config.hierarchy_tables.tasks, options);

    let result;
    try {
        result = await proxyClient.readQueryAsync({
            queryFamily: QueryFamily.Item,
            queryKey: ItemQueryKey.TemplateOptions,
            queryParams: [template_id],
        });
    } catch (err) {
        throw new TemplateError(err, 'TSKTEMP_030');
    }

    if (!result.rows.length) {
        throw new TemplateError('Page not found', 'TSKTEMP_031', 404);
    }

    const [{ public_sharing, description, permanent_template_id }] = result.rows;
    const approval_status = permanent_template_id
        ? await CuPublicTemplatesRepository.loadPublicTemplateApprovalStatus(permanent_template_id, 'task')
        : null;

    if (!options.accept_submitted_template && !public_sharing && !approval_status) {
        throw new TemplateError('Page not found', 'TSKTEMP_032', 404);
    }

    const template_options = { ...result.rows[0].options };
    delete template_options.parent;
    const copy_options = {
        ...options,
        ...template_options,
        default_skip_access: true,
        set_deleted: true,
        set_undeleted: true,
        create_notifications: true,
        from_template: true,
        public_template: true,
        return_immediately: true,
    };

    copy_options.external_dependencies = false;

    let response;
    if (options.copy !== false) {
        response = await copyTask.copyTask(userid, template_id, copy_options);
    }
    if (options.template_visibility === undefined) {
        return response;
    }

    const temp_options = {
        ...options,
        template_v2: true,
        name: options.template_name,
        default_skip_access: true,
        public_template: true,
        task_id: template_id,
        description: options.description || description,
        return_template: options.return_template,
    };
    delete temp_options.template_id;

    const template_response = await _promiseCreateTemplate(userid, temp_options);

    if (options.accept_submitted_template) {
        return template_response;
    }

    return response || template_response;
}

export async function copyPublicTemplate(req, resp, next) {
    const userid = req.decoded_token.user;
    let { template_id } = req.params;
    const options = req.body;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    options.ws_key = ws_key;
    options.middleware_queries = req.middleware_queries;
    /** saving permanent_template_id for template usage */
    options.permanent_template_id = template_id;
    options.return_template = true;

    options[DurationMetricObject.KEY] = new DurationMetricObject(EntityType.TASK, TemplateSource.PUBLIC_TEMPLATE);

    const templateTracer = new TemplateTracer(EntityType.TASK, TemplateAction.TEMPLATE_COPYING, {
        userId: userid,
        sourceTemplateId: template_id,
        templateSource: TemplateSource[TemplateSource.PUBLIC_TEMPLATE],
    });
    options[TemplateTracer.KEY] = templateTracer;

    await templateTracer.withTracing(async () => {
        try {
            assertCorrectChallengeTokenOrThrow(options.public_key, template_id);

            options.proxyClient = await templateHelpers.createProxyClient(template_id, 'task', userid);
            templateTracer.proxyClientForSource = options.proxyClient;

            template_id = await templateHelpers._idByPermanentId(template_id, config.hierarchy_tables.tasks, options);
            options.template_id = template_id;

            const result = await _copyPublicTemplate(userid, template_id, options);
            resp.status(200).send(result);
        } catch (err) {
            next(err);
            templateTracer.finishTracing(err);
        }
    });
}

export function storeTaskTemplateUsed(userid, template_id, options = {}) {
    const { proxyClient = queryProxyFactory() } = options;

    proxyClient.writeQuery(
        {
            queryFamily: QueryFamily.Item,
            queryKey: ItemQueryKey.UpsertTaskTemplateUsed,
            queryParams: [userid, template_id, new Date().getTime()],
        },
        err => {
            if (err) {
                logger.error({
                    msg: 'Failed to store task templates used',
                    err,
                });
            }
        }
    );
}

function checkWillHaveAccessToTask(userid, category_id, cb) {
    db.replicaQuery(
        `
        SELECT
            items.private, task_members.userid
        FROM
            tasK_mgmt.items
            LEFT JOIN task_mgmt.task_members
            ON task_members.userid = $2 AND task_members.task_id = items.id
        WHERE
            items.id = $1
    `,
        [category_id, userid],
        (err, result) => {
            if (err) {
                cb(err);
            } else if (result.rows && result.rows[0] && result.rows[0].private && !result.rows[0].userid) {
                cb({
                    err: 'You would not have access to the task created by this template',
                    status: 400,
                    ECODE: 'TSKTEMP_056',
                });
            } else {
                cb();
            }
        }
    );
}

export function _createTaskFromTemplate(userid, options, cb) {
    checkWillHaveAccessToTask(userid, options.template_id, access_err => {
        if (access_err) {
            cb(access_err);
            return;
        }
        copyTask._copyTask(userid, options.template_id, options, (err, result) => {
            cb(err, result);
            if (!err) {
                storeTaskTemplateUsed(userid, options.template_id, options);
            }
        });
    });
}

export function createTaskFromTemplateAndImportSettings(userid, template_id, options, cb) {
    let template_options;
    let ret_val;

    async.series(
        [
            function getTemplateOptions(series_cb) {
                const query = `SELECT * FROM task_mgmt.template_options WHERE template_id = $1`;
                const params = [template_id];

                db.readQuery(query, params, (err, result) => {
                    if (err) {
                        series_cb(new TemplateError(err, 'FORM_008'));
                        return;
                    }

                    if (!result.rows[0]) {
                        series_cb(new TemplateError('Template not found', 'FORM_009', 404));
                        return;
                    }

                    template_options = result.rows[0].options;

                    series_cb();
                });
            },

            function createFromTemplate(series_cb) {
                const override_parent = options.parent;
                const override_subcategory_id = options.subcategory_id;

                const opts = Object.assign({}, options, template_options, {
                    template_id,
                    set_deleted: true,
                    set_undeleted: true,
                    from_form: true,
                    from_template: true,
                    auto_id: options.auto_id,
                });

                opts.parent = override_parent;

                if (override_subcategory_id) {
                    opts.subcategory_id = override_subcategory_id;
                }

                _createTaskFromTemplate(userid, opts, (err, result) => {
                    if (err) {
                        series_cb(err);
                    } else {
                        ret_val = result;
                        series_cb();
                    }
                });
            },
        ],
        err => {
            if (err) {
                cb(err);
            } else {
                cb(null, ret_val);
            }
        }
    );
}

export function promiseCreateTaskFromTemplateAndImportSettings(userid, template_id, options) {
    return new Promise((resolve, reject) => {
        createTaskFromTemplateAndImportSettings(userid, template_id, options, (err, result) => {
            if (err) {
                reject(err);
            } else {
                resolve(result);
            }
        });
    });
}

export async function createTaskFromTemplate(req, resp, next) {
    const userid = req.decoded_token.user;
    const { template_id } = req.params;
    const options = req.body;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;

    options.ws_key = ws_key;
    options.template_id = template_id;
    options.middleware_queries = req.middleware_queries;
    options.default_skip_access = false;
    options.set_deleted = true;
    options.set_undeleted = true;
    options.create_notifications = true;
    options.from_template = true;
    options.v2 = req.v2;

    options[DurationMetricObject.KEY] = new DurationMetricObject(EntityType.TASK, TemplateSource.TEMPLATE_CENTER);

    const templateTracer = new TemplateTracer(EntityType.TASK, TemplateAction.TEMPLATE_APPLICATION, {
        userId: userid,
        sourceTemplateId: template_id,
        templateSource: TemplateSource[TemplateSource.TEMPLATE_CENTER],
    });
    options[TemplateTracer.KEY] = templateTracer;

    await templateTracer.withTracing(async () => {
        options.proxyClient = req.proxyClient || (await templateHelpers.createProxyClient(template_id, 'task'));
        templateTracer.proxyClientForSource = options.proxyClient;

        if (options.form) {
            options.skip_access = userid === config.get('clickbot_assignee');

            try {
                const result = await promiseCreateTaskFromTemplateAndImportSettings(userid, template_id, options);
                resp.status(200).send(result);
            } catch (err) {
                next(err);
                templateTracer.finishTracing(err);
            }

            return;
        }

        options.from_task_template = true;
        _createTaskFromTemplate(userid, options, (err, result) => {
            if (err) {
                next(err);
                templateTracer.finishTracing(err);
            } else {
                resp.status(200).send(result);
            }
        });
    });
}

async function _editTemplateName(userid, options = {}) {
    const name = options.template_name || options.name;
    if (!input.validateCategoryName(name)) {
        throw new TemplateError('Template name invalid', 'TSKTEMP_005', 400);
    }
    try {
        let workspace_id;
        if (options.team_id) {
            workspace_id = options.team_id;
        } else {
            const { rows } = await getTeamForTemplatesUsingTaskIds([options.template_id]);
            workspace_id = rows[0]?.team_id;
        }
        const versionUpdateRequest = {
            object_type: ObjectType.TASK,
            object_id: options.template_id,
            workspace_id,
            operation: OperationType.UPDATE,
        };

        if (options?.ws_key) {
            versionUpdateRequest.data = {
                context: { ws_key: options.ws_key },
            };
        }

        await db2.batchQueriesAsync(
            [
                {
                    query: 'UPDATE task_mgmt.items SET name = $1 WHERE id = $2',
                    params: [name, options.template_id],
                },
                {
                    query: `
                    UPDATE task_mgmt.template_options
                    SET name = $1, description = $2
                    WHERE template_id = $3 AND type = 'task'`,
                    params: [name, options.description, options.template_id],
                },
            ],
            undefined,
            [versionUpdateRequest]
        );
    } catch (err) {
        throw new TemplateError(err, 'TSKTEMP_004', 500);
    }
}

async function editTemplatePermissions(template_id, options) {
    if (!template_id) {
        return;
    }

    if (
        options.template_visibility != null &&
        !Object.values(config.template_permissions).includes(options.template_visibility)
    ) {
        throw new TemplateError('Invalid permission type', 'TSKTEMP_022', 400);
    }
    if (options.public_sharing != null && typeof options.public_sharing !== 'boolean') {
        throw new TemplateError('Public sharing option must be of type BOOLEAN', 'TSKTEMP_027', 400);
    }

    let response;
    try {
        response = await db.promiseReadQuery(
            `
                SELECT
                    permissions,
                    coalesce(teams.disable_template_pub_sharing, false) as disable_template_pub_sharing,
                    teams.disable_public_sharing,
                    teams.admin_public_share_override,
                    team_members.role,
                    teams.id as workspace_id
                FROM
                    task_mgmt.items
                    JOIN task_mgmt.teams ON teams.id = items.team_id
                    JOIN task_mgmt.team_members ON team_members.team_id = teams.id AND team_members.userid = $2
                WHERE
                    items.id = $1
        `,
            [template_id, options.userid]
        );
    } catch (err) {
        throw new TemplateError(err, 'TSKTEMP_020', 500);
    }
    if (!response.rows || !response.rows.length) {
        throw new TemplateError('Could not find template', 'TSKTEMP_021', 404);
    }
    const prev_permissions = response.rows[0].permissions;
    const [
        {
            disable_template_pub_sharing = false,
            disable_public_sharing,
            admin_public_share_override,
            role,
            workspace_id,
        },
    ] = response.rows || [{}];
    const queries = [];
    const versionUpdates = [];

    const template_updates = [];
    const template_updates_params = [];
    if (options.template_visibility != null) {
        template_updates.push(`
            permissions = $${template_updates_params.push(options.template_visibility)}
        `);
        if (
            prev_permissions !== options.template_visibility &&
            prev_permissions === config.template_permissions.members
        ) {
            queries.push({
                query: `
                    DELETE FROM task_mgmt.task_template_members
                    WHERE task_id = $1
                `,
                params: [template_id],
            });
        }
    }

    if (
        disable_template_pub_sharing &&
        options.public_sharing &&
        (await entitlementService.checkEntitlement(workspace_id, EntitlementName.PublicLinkRestrictions))
    ) {
        throw new TemplateError('Do not have permission to perform that action', 'TSKTEMP_099', 400);
    } else if (
        options.public_sharing &&
        disable_public_sharing === true &&
        (!admin_public_share_override ||
            (Number(role) !== config.get('team_roles.owner') && Number(role) !== config.get('team_roles.admin')))
    ) {
        throw new TemplateError(
            'Cannot make task template public',
            PublicSharingCodes.DisabledPublicSharingError,
            StatusErrorCodes.Unauthorized
        );
    }

    if (options.public_sharing !== undefined) {
        template_updates.push(`
            public_sharing = $${template_updates_params.push(options.public_sharing)}
        `);
    }
    if (template_updates.length) {
        queries.push({
            query: `
                UPDATE task_mgmt.items
                SET
                    ${template_updates.join(', ')}
                WHERE id = $${template_updates_params.push(template_id)}
            `,
            params: template_updates_params,
        });
        versionUpdates.push({
            object_type: ObjectType.TASK,
            object_id: template_id,
            workspace_id,
            operation: OperationType.UPDATE,
        });
    }
    if (
        options.template_visibility === config.template_permissions.members ||
        // using prev_permissions if not provided in options
        ((options.template_visibility === null || options.template_visibility === undefined) &&
            prev_permissions === config.template_permissions.members &&
            (options.template_members || options.template_group_members))
    ) {
        const { template_members, template_group_members = {} } = options;
        if (!template_members || !template_members.add || !template_members.rem) {
            throw new TemplateError('Malformed template_members field', 'TSKTEMP_023', 400);
        }
        if (template_members.add.some(user => template_members.rem.includes(user))) {
            throw new TemplateError('User cannot appear in both add and remove field', 'TSKTEMP_024', 400);
        }
        let prev_members = [];
        if (prev_permissions === config.template_permissions.members) {
            let member_response;
            try {
                member_response = await db.promiseReadQuery(
                    `
                        SELECT userid
                        FROM task_mgmt.task_template_members
                        WHERE task_id = $1
                `,
                    [template_id]
                );
            } catch (err) {
                throw new TemplateError(err, 'TSKTEMP_022', 500);
            }
            prev_members = member_response.rows.map(row => row.userid);
        }
        const all_members = [...prev_members, ...template_members.add]
            .filter((user, i, arr) => arr.indexOf(user) === i)
            .filter(user => !template_members.rem.includes(user));
        if (!all_members.length) {
            throw new TemplateError('Cannot create a template with no members', 'TSKTEMP_025', 500);
        }

        if (template_group_members.add && template_group_members.add.length) {
            try {
                await groupSharingPaywall.checkGroupSharingPaywall(template_id, 'task_template');

                const grp_inserts = [];
                const grp_params = [template_id, workspace_id];
                template_group_members.add.forEach(grp_id => {
                    grp_params.push(grp_id);
                    grp_inserts.push(`($1, $${grp_params.length}, $2)`);
                });

                if (grp_inserts.length) {
                    queries.push({
                        query: `
                            INSERT INTO task_mgmt.task_template_group_members
                                (task_id, group_id, workspace_id)
                            VALUES ${grp_inserts.join(',')}`,
                        params: grp_params,
                    });
                }
            } catch (e) {
                throw new TemplateError(e, 'TSKTEMP_068', 500);
            }
        }

        if (template_group_members.rem && template_group_members.rem.length) {
            try {
                await groupSharingPaywall.checkGroupSharingPaywall(template_id, 'task_template');

                const query = `
                    SELECT DISTINCT userid
                    FROM task_mgmt.group_members
                    WHERE group_id = ANY($1) AND userid != $2`;
                const params = [template_group_members.rem, options.userid];
                const result = await db.promiseReplicaQuery(query, params);
                if (result.rows && result.rows.length) {
                    const userids = result.rows.map(row => row.userid);
                    const combined_members = [...new Set([...template_members.rem, ...userids])];
                    template_members.rem = combined_members;
                }
                queries.push({
                    query: `
                        DELETE FROM task_mgmt.task_template_group_members
                        WHERE task_id = $1 AND group_id = ANY($2)`,
                    params: [template_id, template_group_members.rem],
                });
            } catch (e) {
                throw new TemplateError(e, 'TSKTEMP_067', 500);
            }
        }

        if (template_members.add.length) {
            await Promise.all(
                template_members.add.map(member =>
                    access.promiseAccessTaskTemplate(member, template_id, { skip_permissions: true })
                )
            );
            const add_params = [];
            let add_query = `
                INSERT INTO task_mgmt.task_template_members
                (task_id, userid, workspace_id)
                VALUES
            `;
            template_members.add.forEach(member => {
                add_query += `(
                    $${add_params.push(template_id)},
                    $${add_params.push(member)},
                    $${add_params.push(workspace_id)}
                ), `;
            });
            add_query = add_query.slice(0, -2);
            add_query += `
                ON CONFLICT (task_id, userid)
                DO NOTHING
            `;
            queries.push({ query: add_query, params: add_params });
        }
        if (template_members.rem.length) {
            queries.push({
                query: `
                    DELETE FROM task_mgmt.task_template_members
                    WHERE
                        task_id = $1
                        AND userid = ANY ($2)
            `,
                params: [template_id, template_members.rem],
            });
        }
    }
    try {
        await db2.batchQueriesAsync(queries, undefined, versionUpdates);
        if (options.public_sharing !== undefined) {
            CachedTemplatesQueryProxyHttpClient.invalidateCachedQueryResult(
                { templateType: 'task', templateId: template_id },
                {
                    queryFamily: QueryFamily.Item,
                    queryKey: ItemQueryKey.TemplateOptions,
                    queryParams: [template_id],
                }
            );
        }
    } catch (err) {
        throw new TemplateError(err, 'TSKTEMP_026', 500);
    }
}

export async function _convertV1ToV2(userid, template_ids, options = {}) {
    try {
        const {
            rows: [{ team_id: workspace_id }],
        } = await getTeamForTemplatesUsingTaskIds(template_ids);
        if (!workspace_id) {
            throw new TaskError('Cannot find team_id from task template ids', 'TSKTEMP_011');
        }

        const queries = [];
        const versionUpdates = [];

        const generateQueries = async template_id => {
            let response;
            try {
                response = await taskMember._promiseHierarchyMembers(userid, template_id, {
                    ...options,
                    includeTeamMembers: true,
                });
            } catch (err) {
                throw new TemplateError(err, 'TSKTEMP_055');
            }

            const members = Object.values(response.members)
                .reduce((acc, group) => [...acc, ...group], []) // eslint-disable-line no-loop-func
                .map(member => member.user.id);
            const update_params = [];

            if (members.length) {
                const insert_params = [];
                const inserts = members.map(
                    m => `
                    (
                        $${insert_params.push(template_id)},
                        $${insert_params.push(m)},
                        $${insert_params.push(workspace_id)}
                    )
                `
                );
                queries.push({
                    query: `
                    INSERT INTO task_mgmt.task_template_members
                    (task_id, userid, workspace_id) VALUES ${inserts.join(', ')}
                `,
                    params: insert_params,
                });
            }

            queries.push({
                query: `
                UPDATE task_mgmt.items
                SET
                    subcategory = NULL,
                    permissions = 0,
                    team_id = (
                        SELECT projects.team
                        FROM task_mgmt.items
                        JOIN task_mgmt.subcategories
                            ON items.subcategory = subcategories.id
                        JOIN task_mgmt.categories
                            ON categories.id = subcategories.category
                        JOIN task_mgmt.projects
                            ON projects.id = categories.project_id
                        WHERE
                            items.id = $${update_params.push(template_id)}
                    )
                WHERE
                    id = $${update_params.push(template_id)}
            `,
                params: update_params,
            });
            versionUpdates.push({
                object_type: ObjectType.TASK,
                object_id: template_id,
                workspace_id,
                operation: OperationType.UPDATE,
            });
        };
        try {
            await promiseMapSeries(template_ids, generateQueries);
            await db2.batchQueriesAsync(queries, undefined, versionUpdates);
        } catch (err) {
            throw new TemplateError(err, 'TSKTEMP_045', 500);
        }
    } catch (error) {
        if (!options.do_not_throw) {
            throw error;
        } else {
            logger.error({ error });
        }
    }
}

export async function editTemplate(req, resp, next) {
    const userid = req.decoded_token.user;
    let { template_id } = req.params;
    const options = req.body;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;

    try {
        if (template_id) {
            template_id = await templateHelpers._idByPermanentId(template_id, config.hierarchy_tables.tasks);
        }

        options.ws_key = ws_key;
        options.template_id = template_id;
        options.template_v2 = req.v2;

        const templateIsV1 = await _checkTemplateV1(userid, template_id);

        if (templateIsV1 && req.v2) {
            await _convertV1ToV2(userid, [template_id]);
        }

        const result = await _editTemplateName(userid, options);

        if (!req.v2) {
            resp.status(200).send(result);
            return;
        }
        options.userid = userid;

        await editTemplatePermissions(template_id, options);
    } catch (err) {
        next(err);
        return;
    }

    try {
        options.public_key = true;
        const template = await _getTemplate(template_id, userid, options);
        resp.status(200).send(template);
    } catch (err) {
        next(err);
    }
}
/**
 * @param {number} userid
 * @param {string} task_id
 * @param {string} template_id
 * @param {Object} options - Options list are not complete
 * @param {boolean} [options.custom_fields] - Copy CF excluding Relationships
 * @param {boolean} [options.custom_type] - Copy Custom type or not
 * @param {number} [options.due_date] - Set this Due date. Provided when remapping Subtasks Dates
 * @param {boolean} [options.due_date_time] - Set this Due date time. Provided when remapping Subtasks Dates.
 * @param {boolean} [options.old_due_date] - Apply Template Due Date
 * @param {boolean} [options.old_start_date] - Apply Template Start Date
 * @param {boolean} [options.points] - To apply points. Not implemented in FE yet. Default to TRUE.
 * @param {boolean} [options.comment] - Include comments. Default to TRUE for compatibility with templates before FE implementation.
 * @param {boolean} [options.assigned_comment] - Include only assigned comments. Not implemented in FE. Default to FALSE
 * @param {string} [options.permanent_template_id] - For template usage.
 * @param {boolean} [options.relationships] - Copy Relationships, only Task Relationships applicable
 * @param {boolean} [options.remap_start_date]
 * @param {boolean} [options.from_preset_task_template] - Indicates that the merging has been initiated because of a preset task template
 * @param {boolean} [options.prevent_status_change] - There should be no status change on the task even if old_status is set to true
 * @param {boolean} [options.prevent_start_date_change] - There should be no start date change on the task even if prevent_start_date_change is set to true
 * @param {boolean} [options.prevent_due_date_change] - There should be no due date change on the task even if prevent_due_date_change is set to true
 * @param {function} cb
 */
export function _mergeTemplateIntoTask(userid, task_id, template_id, options, cb) {
    const templateTracer = new TemplateTracer(EntityType.TASK, TemplateAction.TEMPLATE_MERGING, {
        userId: userid,
        sourceTemplateId: template_id,
        destId: task_id,
    });
    options[TemplateTracer.KEY] = templateTracer;

    return templateTracer.withTracing(() => {
        const mergingFunction = useSemaphoresForMergeTemplateIntoTask()
            ? _mergeTemplateIntoTaskWithoutTracingWithSemaphoreLimit
            : _mergeTemplateIntoTaskWithoutTracing;

        mergingFunction(userid, task_id, template_id, options, (err, result) => {
            cb(err, result);
            templateTracer.finishTracing(err);
        });
    });
}

const _mergeTemplateIntoTaskWithoutTracingWithSemaphoreLimit = withSemaphoreLimit(
    OperationPrefix.COPY_TASK,
    userid => {
        const splitConfig = getSemaphoreCopyOptions(userid);
        return {
            ...splitConfig.taskCopySemaphoreOptions,
            perTraceIdConcurrencyEnabled: splitConfig.perTraceIdConcurrencyEnabled,
            perTraceIdConcurrencyLimit: splitConfig.perTraceIdConcurrencyLimit,
        };
    },
    _mergeTemplateIntoTaskWithoutTracing,
    addPressureToFunctionParams([OperationPrefix.COPY_TASK])
);

function _mergeTemplateIntoTaskWithoutTracing(userid, task_id, template_id, options, cb) {
    options.assigned_comment = genericHelpers.toBooleanDefault(options.assigned_comment, false);
    options.comment = genericHelpers.toBooleanDefault(options.comment, true);
    options.points = genericHelpers.toBooleanDefault(options.points, true);

    let client;
    let content_updated = false;
    let done;
    let editor_token = '';
    let mergedContent = '';
    let mergedTextContent = '';
    let mergedLowerTextContent = '';
    let isSubtask = false;
    let parent_id;
    let hist_id; // main hist_id - template merged
    const hist_ids = []; // all hist_id
    let old_due_date = null;
    let old_due_date_time;
    let old_start_date = null;
    let old_start_date_time = null;
    let old_duration = null;
    let old_duration_is_elapsed = null;
    let old_custom_type = null;
    let customType = null;
    // copied task id to new task id map that will be passed onto
    // later promise calls
    let old_to_new = { [template_id]: task_id };
    let task_start_date;
    let task_subcategory_id;
    let template_v2;
    let template_obj = {};
    let nested_subtasks_level;
    let ydoc;
    let team_id;

    let estimates_per_assignee;
    let points_per_assignee;
    let nested_subtasks;

    let customFieldsInCorrectOrder;
    const added_assignees = [];
    let group_assignees;
    const assignee_hist_ids = [];
    let subtasks_count = 0;
    const cf_hist_ids = [];

    // internally list_relationships and task_relationships handled independently
    // externally - controlled by one option: relationships
    options.task_relationships = options.relationships;
    delete options.relationships;

    const old_dependencies = {};
    const old_links = {};
    const queries = [];

    const now = new Date().getTime();

    const recur_settings_id = uuid.v4();

    let events;
    const ovm = getObjectVersionManager();
    const versionUpdates = [];
    const historyRows = [];

    let attachmentMap = {};
    const docAttachmentMap = { view: {}, page: {} };

    const syncBlocks = [];
    const sync_blocks_old_to_new = {};

    let templateWorkspaceId;
    let targetTaskWorkspaceId;

    const undeletedSubtaskIds = [];

    async.series(
        [
            // Check task access
            series_cb => {
                if (options.skipAccess) {
                    series_cb();
                    return;
                }

                access.checkAccessTask(
                    userid,
                    task_id,
                    { permissions: [config.permission_constants.template] },
                    series_cb
                );
            },

            // Check template access
            series_cb => {
                if (options.skipAccess) {
                    series_cb();
                    return;
                }

                access.checkAccessTask(
                    userid,
                    template_id,
                    { permissions: [config.permission_constants.template] },
                    async err => {
                        if (err) {
                            try {
                                await access.promiseAccessTaskTemplate(userid, template_id);
                            } catch (access_err) {
                                series_cb(access_err);
                                return;
                            }
                        }
                        series_cb();
                    }
                );
            },

            // check points and time per assignee clickapps
            series_cb => {
                const query = `
                    SELECT
                        COALESCE(teams.estimates_per_assignee, false) as estimates_per_assignee,
                        COALESCE(teams.points_per_assignee, false) as points_per_assignee,
                        nested_subtasks,
                        nested_subtasks_level,
                        teams.id as team_id
                    FROM
                        task_mgmt.items
                        JOIN task_mgmt.teams ON teams.id = items.team_id
                    WHERE
                        items.id = $1
                `;

                db.replicaQuery(query, [template_id], (err, result) => {
                    if (err) {
                        series_cb(err);
                        return;
                    }

                    if (!result || !result.rows || !result.rows.length) {
                        series_cb();
                        return;
                    }

                    [{ estimates_per_assignee, points_per_assignee, nested_subtasks, nested_subtasks_level, team_id }] =
                        result.rows;
                    series_cb();
                });
            },

            // Validate subtasks limit
            async series_cb => {
                try {
                    await validateSubtaskLimitForTaskTemplateMerging({
                        taskId: task_id,
                        templateId: template_id,
                        teamId: team_id,
                    });
                    series_cb();
                } catch (err) {
                    series_cb(err);
                }
            },

            // create trees
            // find depth of task template
            // find nested level of target task
            series_cb => {
                if (!nested_subtasks || !options.subtasks) {
                    series_cb();
                    return;
                }
                let templateHeight;
                let taskLevel;

                async.parallel(
                    {
                        templateTree(para_cb) {
                            db.readQuery(
                                `SELECT items.id, items.parent, items.subtask_parent
                                FROM task_mgmt.items
                                WHERE parent = $1 OR id = $1`,
                                [template_id],
                                (err, result) => {
                                    if (err) {
                                        para_cb(err);
                                    } else {
                                        const map = nestedHelpers.treeifyMap(result.rows);
                                        templateHeight = nestedHelpers.longestPath(map, map[template_id]);
                                        para_cb();
                                    }
                                }
                            );
                        },
                        taskTree(para_cb) {
                            db.readQuery(
                                `SELECT coalesce(parent, id) AS id FROM task_mgmt.items WHERE id = $1`,
                                [task_id],
                                (err, result) => {
                                    if (err) {
                                        para_cb(err);
                                    } else {
                                        db.readQuery(
                                            `SELECT items.id, items.parent, items.subtask_parent
                                            FROM task_mgmt.items
                                            WHERE (id = $1 OR parent = $1) AND deleted = FALSE`,
                                            [result.rows[0].id],
                                            (err2, result2) => {
                                                if (err2) {
                                                    para_cb(err2);
                                                } else {
                                                    const map = nestedHelpers.treeifyMap(result2.rows);
                                                    const subtask_parent = result2.rows.find(row => row.parent == null);
                                                    if (!subtask_parent) {
                                                        para_cb(
                                                            new TemplateError(
                                                                `Cannot build task tree for task: ${task_id}, template: ${template_id} no parent found`,
                                                                'TSKTEMP_058'
                                                            )
                                                        );
                                                        return;
                                                    }
                                                    taskLevel = nestedHelpers.findLevel(
                                                        map[subtask_parent.id],
                                                        task_id
                                                    );
                                                    para_cb();
                                                }
                                            }
                                        );
                                    }
                                }
                            );
                        },
                    },
                    err => {
                        if (err) {
                            series_cb(err);
                        } else if (templateHeight + taskLevel > nested_subtasks_level) {
                            series_cb(
                                new TemplateError(
                                    `Level of nested subtasks is limited to ${nested_subtasks_level}`,
                                    'TSKTEMP_038',
                                    400
                                )
                            );
                        } else {
                            series_cb();
                        }
                    }
                );
            },

            series_cb => {
                if (!options.get_settings) {
                    series_cb();
                    return;
                }

                const query = `SELECT * FROM task_mgmt.template_options WHERE template_id = $1`;
                const params = [template_id];

                db.readQuery(query, params, (err, result) => {
                    if (err) {
                        series_cb(new TemplateError(err, 'FORM_008'));
                        return;
                    }

                    if (!result.rows[0]) {
                        series_cb(new TemplateError('Template not found', 'FORM_009', 404));
                        return;
                    }

                    options = Object.assign({}, options, result.rows[0].options);

                    delete options.parent;

                    series_cb();
                });
            },

            // Load top level task data
            series_cb => {
                db.readQuery(
                    `SELECT parent, subcategory, start_date, workspace_id
                    FROM task_mgmt.items
                    WHERE id = $1`,
                    [task_id],
                    (err, result) => {
                        if (err) {
                            series_cb({ err: 'Internal server error', status: 500, ECODE: 'TSKTEMP_035' });
                            return;
                        }

                        if (!result.rows?.length) {
                            series_cb(new TemplateError('Task not found', 'TSKTEMP_054', 500));
                            return;
                        }

                        const [task] = result.rows;

                        task_start_date = parseInt(task.start_date, 10);

                        targetTaskWorkspaceId = task.workspace_id;

                        if (task.parent) {
                            isSubtask = true;
                            parent_id = task.parent;
                        }
                        if (task.subcategory) {
                            task_subcategory_id = task.subcategory;
                        }

                        if (!task_subcategory_id) {
                            series_cb(new TemplateError('No task_subcategory_id found', 'TSKTEMP_039', 500));
                            return;
                        }

                        series_cb();
                    }
                );
            },

            // Check template entitlements
            async series_cb => {
                if (!shouldRunPreMergeEntitlementChecks()) {
                    series_cb();
                    return;
                }

                if (!targetTaskWorkspaceId) {
                    logger.warn({
                        msg: 'No target team id provided, skipping merge entitlement checks',
                        taskId: task_id,
                        templateId: template_id,
                    });
                    series_cb();
                    return;
                }

                try {
                    await checkTemplateEntitlements({
                        team_id: targetTaskWorkspaceId,
                        template_id,
                        template_type: 'task',
                        ...(options.custom_type && options.callOrigin === 'automations'
                            ? { entitlements_to_skip: new Set([EntitlementName.CustomItems]) }
                            : {}),
                    });
                    series_cb();
                } catch (error) {
                    series_cb(error);
                }
            },

            // Load template data for merging
            series_cb => {
                db.readQuery(
                    `
                    SELECT
                        name,
                        due_date,
                        start_date,
                        start_date_time,
                        team_id,
                        workspace_id,
                        recurring,
                        recur_settings AS settings_id,
                        recur_type,
                        recur_next,
                        recur_new_status,
                        recur_due_date,
                        recur_data,
                        recur_rule,
                        recur_on,
                        recur_on_status,
                        recur_skip_missed,
                        recur_copy_original,
                        recur_task,
                        recur_immediately,
                        recur_time,
                        orderindex,
                        due_date,
                        due_date_time,
                        recur_dst,
                        items.recur_tz as timezone,
                        items.recur_tz_offset as timezone_offset,
                        recur_daily,
                        recur_ignore_today,
                        custom_type,
                        duration,
                        duration_is_elapsed
                    FROM
                        task_mgmt.items WHERE id = $1 ${options.exclude_deleted ? ' AND deleted = false' : ''}`,
                    [template_id],
                    (templateErr, templateResult) => {
                        if (templateErr) {
                            series_cb({ err: 'Internal server error', status: 500, ECODE: 'TSKTEMP_037' });
                            return;
                        }

                        if (templateResult.rows.length === 0) {
                            series_cb({ err: 'Template not found', status: 404, ECODE: 'TSKTEMP_037' });
                            return;
                        }

                        [template_obj] = templateResult.rows;

                        let templateName;
                        if (templateResult.rows[0]) {
                            templateName = templateResult.rows[0].name;
                        }

                        templateWorkspaceId = templateResult.rows[0].workspace_id;

                        options[TemplateTracer.KEY]?.processSourceAndTargetVerifiedData({
                            sourceId: template_id,
                            savedId: task_id,
                            workspaceId: templateWorkspaceId,
                        });

                        template_v2 = Boolean(templateResult.rows[0].team_id);

                        old_due_date = templateResult.rows[0].due_date;
                        old_due_date_time = templateResult.rows[0].due_date_time;
                        old_start_date = templateResult.rows[0].start_date;
                        old_start_date_time = templateResult.rows[0].start_date_time;
                        old_custom_type = templateResult.rows[0].custom_type;
                        old_duration = templateResult.rows[0].duration;
                        old_duration_is_elapsed = templateResult.rows[0].duration_is_elapsed;

                        queries.push({
                            query: `
                                INSERT INTO task_mgmt.task_history
                                    (task_id, field, date, before, after, userid, data)
                                VALUES ($1, $2, $3, $4, $5, $6, $7)
                                RETURNING id, task_id, TRUE AS main_event, id as cdc_history_id, field as cdc_field, task_id as cdc_object_id, 'task' as cdc_object_type`,
                            params: [
                                task_id,
                                'template_merged',
                                new Date().getTime(),
                                null,
                                template_id,
                                userid,
                                { name: templateName },
                            ],
                        });

                        series_cb();
                    }
                );
            },

            series_cb => {
                if (!options.custom_fields || template_v2) {
                    series_cb();
                    return;
                }

                (options.middleware_queries || []).forEach(query => {
                    queries.push({ query: query.query, params: query.params });
                    versionUpdates.push(...(query.versionUpdates ?? []));
                });

                series_cb();
            },

            series_cb => {
                if (!(options.custom_fields || options.task_relationships)) {
                    series_cb();
                    return;
                }

                async.series(
                    [
                        function copyTemplateFields(cf_cb) {
                            if (!template_v2) {
                                cf_cb();
                                return;
                            }

                            const copyTemplateFieldsOrder_cb = (err, result) => {
                                if (err) {
                                    cf_cb(err);
                                    return;
                                }

                                customFieldsInCorrectOrder = result?.field_ids ?? [];
                                cf_cb(err, result);
                            };

                            const _options = {
                                custom_fields: options.custom_fields,
                                task_relationships: options.task_relationships,
                            };

                            fieldHelper.copyTaskTemplateFields(
                                userid,
                                template_id,
                                task_subcategory_id,
                                _options,
                                smartCallback(copyTemplateFieldsOrder_cb, 'TSKTEMP_053')
                            );
                        },

                        function copyCustomFieldsOrder(cf_cb) {
                            if (!customFieldsInCorrectOrder?.length) {
                                cf_cb();
                                return;
                            }

                            customFieldsInCorrectOrder.forEach((fieldId, idx) => {
                                queries.push({
                                    query: `
                                            UPDATE task_mgmt.field_parents
                                            SET orderindex = existing.maxindex + $3
                                            FROM
                                                (
                                                    SELECT COALESCE(MAX(field_parents.orderindex), 0) as maxindex
                                                    FROM task_mgmt.field_parents
                                                    WHERE parent_id = $2 AND field_id != ALL($4)
                                                ) AS existing
                                            WHERE field_id = $1 AND parent_id = $2`,
                                    params: [fieldId, task_subcategory_id, idx + 1, customFieldsInCorrectOrder],
                                });
                            });

                            cf_cb();
                        },

                        function doValues(cf_cb) {
                            const query = `
                            SELECT
                                field_values.field_id,
                                field_values.item_id,
                                field_values.value,
                                fields.type AS field_type,
                                fields.team_id,
                                array_agg(drop_down_options.id ORDER BY drop_down_options.orderindex) AS dd_options,
                                array_agg(label_options.id ORDER BY label_options.orderindex) AS ll_options
                            FROM task_mgmt.field_values
                            INNER JOIN task_mgmt.fields
                                ON fields.id = field_values.field_id
                            LEFT JOIN task_mgmt.drop_down_options
                                ON fields.id = drop_down_options.field_id
                            LEFT JOIN task_mgmt.label_options
                                ON fields.id = label_options.field_id
                            WHERE item_id = $1
                                AND field_values.deleted = FALSE
                            GROUP BY
                                field_values.field_id,
                                field_values.item_id,
                                field_values.value,
                                fields.type,
                                fields.team_id`;
                            const params = [template_id];

                            db.replicaQuery(query, params, async (err, result) => {
                                if (err) {
                                    cf_cb(new TemplateError(err, 'TSKTEMP_010'));
                                    return;
                                }

                                const field_ids = result.rows.map(r => r.field_id);

                                let old_field_value_map;

                                try {
                                    old_field_value_map = await getOldFieldToValueMap(task_id, field_ids);
                                } catch (e) {
                                    metricsClient.increment('automation.action.cf_old_field_value_failure');

                                    logTemplateError(e, 'Failed to get old field values', 'TSKTEMP_756');

                                    old_field_value_map = {};
                                }

                                async.map(
                                    result.rows,
                                    (row, map_cb) => {
                                        const {
                                            field_id,
                                            value,
                                            field_type,
                                            dd_options,
                                            ll_options,
                                            team_id: templateTeamId,
                                        } = row;

                                        fieldHelper.findLikeField(
                                            field_id,
                                            task_subcategory_id,
                                            {},
                                            (err1, field_match) => {
                                                if (err1) {
                                                    map_cb(err1);
                                                    return;
                                                }

                                                if (!field_match) {
                                                    map_cb();
                                                    return;
                                                }

                                                let new_value;
                                                let field_err;

                                                if (Number(field_type) === config.field_types.drop_down) {
                                                    const option_idx = dd_options.indexOf(value.value);
                                                    const option_id =
                                                        field_match.type_config.options[option_idx] &&
                                                        field_match.type_config.options[option_idx].id;

                                                    if (!option_id) {
                                                        field_err = true;
                                                    }

                                                    new_value = { value: option_id };
                                                } else if (Number(field_type) === config.field_types.labels) {
                                                    const option_idxs = value.value.map(id => ll_options.indexOf(id));
                                                    const option_ids = option_idxs
                                                        .map(
                                                            idx =>
                                                                field_match.type_config.options[idx] &&
                                                                field_match.type_config.options[idx].id
                                                        )
                                                        .filter(Boolean);

                                                    if (!option_ids.length) {
                                                        field_err = true;
                                                    }

                                                    new_value = { value: option_ids };
                                                } else {
                                                    new_value = value;
                                                }

                                                if (field_err) {
                                                    map_cb();
                                                    return;
                                                }

                                                const history_data = {};

                                                if (options.trigger_id) {
                                                    history_data.trigger_id = options.trigger_id;
                                                }
                                                if (options.auto_id) {
                                                    history_data.auto_id = options.auto_id;
                                                }
                                                if (options.callOrigin) {
                                                    history_data.callOrigin = options.callOrigin;
                                                }

                                                history_data.createTask = options.createTask ?? false;

                                                queries.push(
                                                    {
                                                        query: `
                                                            UPDATE task_mgmt.field_values
                                                            SET value = $3, deleted = FALSE
                                                            WHERE field_id = $1
                                                                AND item_id = $2`,
                                                        params: [field_match.id, task_id, new_value],
                                                    },
                                                    {
                                                        query: `
                                                            INSERT INTO task_mgmt.field_values(field_id, item_id, value, deleted, workspace_id)
                                                            VALUES ($1, $2, $3, false, $4)
                                                            ON CONFLICT (item_id, field_id)
                                                            DO NOTHING`,
                                                        params: [field_id, task_id, new_value, templateTeamId],
                                                    },
                                                    {
                                                        query: `
                                                            INSERT INTO task_mgmt.field_task_links
                                                                (field_id, task_id, link_id, workspace_id)
                                                                (
                                                                    SELECT $2, $3, link_id, $4
                                                                    FROM task_mgmt.field_task_links
                                                                    WHERE field_id = $1 AND task_id = $5
                                                                )
                                                            ON CONFLICT (field_id, task_id, link_id) DO NOTHING`,
                                                        params: [
                                                            field_id,
                                                            field_match.id,
                                                            task_id,
                                                            template_obj.workspace_id,
                                                            template_id,
                                                        ],
                                                    }
                                                );

                                                const old_value = old_field_value_map[field_id];

                                                if (old_value?.value !== new_value?.value) {
                                                    queries.push({
                                                        query: `
                                                            INSERT INTO task_mgmt.task_history(
                                                                userid, task_id, field, date, before, after, data
                                                            )
                                                            VALUES ($1, $2, $3, $4, $5, $6, $7)
                                                            RETURNING id as cf_history_id, task_id`,
                                                        params: [
                                                            userid,
                                                            task_id,
                                                            `cf_${field_id}`,
                                                            new Date().getTime(),
                                                            old_value?.value,
                                                            new_value.value,
                                                            history_data,
                                                        ],
                                                    });
                                                }

                                                if (template_obj?.workspace_id) {
                                                    versionUpdates.push({
                                                        object_type: ObjectType.TASK,
                                                        object_id: field_match.id,
                                                        operation: OperationType.UPDATE,
                                                        workspace_id: template_obj.workspace_id,
                                                        data: {
                                                            context: { ws_key: options.ws_key },
                                                        },
                                                    });
                                                } else {
                                                    logger.error({
                                                        msg: 'Failed to write OVM update',
                                                        ECODE: 'OVM_WS_322',
                                                        OVM_CRITICAL: true,
                                                    });
                                                }

                                                map_cb();
                                            }
                                        );
                                    },
                                    cf_cb
                                );
                            });
                        },
                    ],
                    series_cb
                );
            },

            series_cb => {
                // add followers
                if (!options.old_followers) {
                    series_cb();
                    return;
                }

                getTaskFollowersToMergeFromTemplateQueryFactory()
                    .buildQueryObject({
                        taskId: task_id,
                        templateId: template_id,
                        userId: userid,
                        workspaceId: targetTaskWorkspaceId,
                    })
                    .then(({ query, params }) => {
                        queries.push({
                            query: `
                                INSERT INTO
                                task_mgmt.followers(userid, task_id, workspace_id)
                                (${query})
                                ON CONFLICT (task_id, userid)
                                DO NOTHING
                            `,
                            params,
                        });

                        series_cb();
                    })
                    .catch(err => series_cb(err));
            },
            series_cb => {
                // remove old assignees
                if (!options.old_assignees) {
                    series_cb();
                    return;
                }

                queries.push({
                    query: 'DELETE FROM TasK_mgmt.assignees WHERE task_id = $1',
                    params: [task_id],
                });

                series_cb();
            },
            series_cb => {
                // add new assignees to followers
                if (!options.old_assignees) {
                    series_cb();
                    return;
                }

                getTaskAssigneesToMergeFromTemplateQueryFactory()
                    .buildQueryObject({
                        taskId: task_id,
                        templateId: template_id,
                        userId: userid,
                        workspaceId: targetTaskWorkspaceId,
                    })
                    .then(({ query, params }) => {
                        queries.push({
                            query: `
                                INSERT INTO
                                task_mgmt.followers(userid, task_id, workspace_id)
                                (${query})
                                ON CONFLICT (task_id, userid)
                                DO NOTHING
                            `,
                            params,
                        });

                        series_cb();
                    })
                    .catch(err => series_cb(err));
            },
            series_cb => {
                // add new assignees
                if (!options.old_assignees) {
                    series_cb();
                    return;
                }

                getTaskTemplateAssigneesToCopyIntoTaskQueryFactory()
                    .buildQueryObject({
                        taskId: task_id,
                        templateId: template_id,
                        userId: userid,
                        workspaceId: template_obj.workspace_id,
                        estimatesPerAssignee: estimates_per_assignee,
                        pointsPerAssignee: points_per_assignee,
                    })
                    .then(({ query, params }) => {
                        queries.push({
                            query: `
                                INSERT INTO
                                task_mgmt.assignees(userid, task_id, date_assigned, workspace_id, time_estimate, points_float)
                                (${query})
                                ON CONFLICT (task_id, userid)
                                DO NOTHING RETURNING userid as assignee
                            `,
                            params,
                        });

                        series_cb();
                    })
                    .catch(err => series_cb(err));
            },
            async series_cb => {
                // add new tags
                if (!options.old_tags) {
                    series_cb();
                    return;
                }
                if (template_v2) {
                    const tag_queries = tagsv2Mod.makeTaskTemplateTagsQueries(template_id, task_id);

                    queries.push(...tag_queries);
                } else {
                    const query = `
                        INSERT INTO task_mgmt.tags(name, task_id, workspace_id) (
                            SELECT name, $2, (SELECT workspace_id FROM task_mgmt.tasks WHERE id = $2)
                            FROM task_mgmt.tags
                            WHERE task_id = $1
                                AND name NOT IN (
                                    SELECT name
                                    FROM task_mgmt.tags
                                    WHERE task_id = $2
                                )
                        )`;
                    const params = [template_id, task_id];
                    queries.push({ query, params });
                }

                series_cb();
            },
            series_cb => {
                if (!options.content) {
                    series_cb();
                    return;
                }

                db.readQuery(
                    `SELECT content, text_content, lower_text_content, id, editor_token, ydoc
                    FROM task_mgmt.items
                    WHERE id = ANY ( $1 ) `,
                    [[task_id, template_id]],
                    async (err, result) => {
                        if (err) {
                            series_cb(err);
                        } else {
                            let task1;
                            let task2;

                            result.rows.forEach(row => {
                                if (row.id === template_id) {
                                    task2 = row;
                                } else {
                                    task1 = row;
                                }
                            });

                            editor_token = task1.editor_token;
                            ydoc = task1.ydoc;

                            if (task1.content && task2.content) {
                                try {
                                    task1.content = await json.parse(task1.content);
                                } catch (e) {
                                    logTemplateError(e, 'TSKTEMP_052', 400, {
                                        task_id,
                                        template_id,
                                        content_1: task1.content,
                                        content_2: task2.content,
                                    });
                                    task1.content = { ops: [{ insert: `${task1.content}\n` }] };
                                }

                                try {
                                    task2.content = await json.parse(task2.content);
                                } catch (e) {
                                    logTemplateError(e, 'TSKTEMP_050', 400, {
                                        task_id,
                                        template_id,
                                        content_1: task1.content,
                                        content_2: task2.content,
                                    });
                                    task2.content = { ops: [{ insert: `${task2.content}\n` }] };
                                }

                                task1.content.ops = task1.content.ops || [];
                                const isEmptyContent = task =>
                                    task.content.ops.length === 0 || // empty content
                                    (task.content.ops.length === 1 && task.content.ops[0].insert === '\n'); // only a newline
                                if (isEmptyContent(task1)) {
                                    task1.content.ops = task2.content.ops;
                                } else {
                                    // Resolves https://github.com/time-loop/clickup/blob/master/test/jest/models/templates/taskTemplates/taskTemplates.fvt.ts#L66
                                    const lastOp = task1.content.ops[task1.content.ops.length - 1];
                                    const hasNewlineAtEnd = lastOp && lastOp.insert === '\n';
                                    task1.content.ops = task1.content.ops.concat(
                                        hasNewlineAtEnd ? [] : [getInsertNewlineOp()],
                                        task2.content.ops
                                    );
                                }

                                try {
                                    task1.text_content = await quill.toPlainTextAsync(task1.content.ops);
                                } catch (e) {
                                    logTemplateError(e, 'TSKTEMP_051', 500, {
                                        task_id,
                                        template_id,
                                        content_1: task1.content,
                                        content_2: task2.content,
                                    });
                                }

                                const text_content_limit = config.tasks.limits.text_content_size;
                                if (task1.text_content && task1.text_content.length > text_content_limit) {
                                    logger.info({
                                        msg: 'Task description is too long during template merge',
                                        task_ids: [task_id, template_id],
                                        text_content_limit,
                                        text_content_length: task1.text_content.length,
                                        ECODE: 'TSKTEMP_061',
                                    });
                                    series_cb({
                                        err: 'Task description is too long',
                                        status: 413,
                                        ECODE: 'TSKTEMP_061',
                                    });
                                    return;
                                }

                                task1.lower_text_content = task1.text_content.toLowerCase();

                                content_updated = true;
                            } else if (task2.content) {
                                task1.content = task2.content;
                                task1.text_content = task2.text_content;
                                if (task2.text_content) {
                                    task1.lower_text_content = task2.text_content.toLowerCase();
                                }
                                content_updated = true;
                            }

                            mergedContent = task1.content;
                            mergedTextContent = task1.text_content;
                            mergedLowerTextContent = task1.lower_text_content;
                            series_cb();
                        }
                    }
                );
            },

            async series_cb => {
                if (!options.content) {
                    series_cb();
                    return;
                }

                try {
                    syncBlocks.push(...(await getSyncBlocksByParentIds([template_id], ObjectType.TASK)));
                    syncBlocks.forEach(syncBlock => {
                        sync_blocks_old_to_new[syncBlock.id] = uuid.v4();
                    });
                    if (syncBlocks.length) {
                        mergedContent = replaceSyncBlocksInContent(mergedContent, sync_blocks_old_to_new);
                    }
                    series_cb();
                } catch (syncBlockErr) {
                    series_cb(syncBlockErr);
                }
            },

            // copy attachments
            series_cb => {
                if (!options.attachments) {
                    series_cb();
                    return;
                }
                options.team_id = template_obj.workspace_id;
                options.copy_attachments_from_s3 = true;

                copyAttachments(template_id, task_id, client, options, (attach_err, attach_result) => {
                    if (attach_result?.attachmentMap) {
                        attachmentMap = attach_result.attachmentMap;
                    }
                    if (!_.isEmpty(attachmentMap)) {
                        mergedContent = replaceAttachmentsInContent(mergedContent, attachmentMap);
                    }

                    series_cb(attach_err, attach_result);
                });
            },

            async series_cb => {
                let updateQuery = `UPDATE task_mgmt.items SET `;
                const updateParams = [task_id];
                const queryParts = [];

                if (options.priority) {
                    queryParts.push(`
                        priority = (
                            SELECT priority
                            FROM task_mgmt.items
                            WHERE id = $${updateParams.push(template_id)}
                        )`);
                }

                if (options.old_status && !options.prevent_status_change) {
                    try {
                        const statusData = await getStatusDataForMerge({ task_id, template_id });
                        if (statusData) {
                            queryParts.push(`
                    status = $${updateParams.push(statusData.status)},
                    status_id = $${updateParams.push(statusData.status_id)}`);
                        }
                    } catch (err) {
                        series_cb(err);
                        return;
                    }
                }

                if (options.custom_type) {
                    customType = new CustomType(old_custom_type, config);
                    if (customType.isUsableCustomType()) {
                        try {
                            const targetWorkspaceId = Number(template_obj.workspace_id);
                            const allowCustomType = await entitlementService.checkEntitlement(
                                targetWorkspaceId,
                                EntitlementName.CustomItems,
                                { throwOnDeny: !(options.callOrigin === 'automations') }
                            );
                            if (!allowCustomType) {
                                customType = null;
                            }
                        } catch (e) {
                            series_cb({
                                err: 'Extended custom task type usage limit',
                                status: 400,
                                ECODE: e?.ECODE ?? 'TSKTEMP_060',
                            });
                            return;
                        }
                    }

                    queryParts.push(`custom_type = $${updateParams.push(customType.value)}`);
                }

                if (options.time_estimate) {
                    updateParams.push(template_id);
                    queryParts.push(`
                    time_estimate = (
                        SELECT time_estimate
                        FROM task_mgmt.items
                        WHERE id = $${updateParams.length}
                    ),
                    time_estimate_string = (
                        SELECT time_estimate_string
                        FROM task_mgmt.items
                        WHERE id = $${updateParams.length}
                    )`);
                }

                if (options.points) {
                    queryParts.push(`
                    points = (
                        SELECT points
                        FROM task_mgmt.items
                        WHERE id = $${updateParams.push(template_id)}
                    )`);
                }

                // rescheduling
                if (options.due_date && !options.prevent_due_date_change) {
                    if (useRemapDateOfTaskV2ForTaskTemplateMerging()) {
                        const { new_start_date, new_due_date } = remapDateOfTaskV2(
                            {
                                due_date: old_due_date,
                                due_date_time: old_due_date_time,
                                start_date: old_start_date,
                                start_date_time: old_start_date_time,
                                duration: old_duration,
                                duration_is_elapsed: old_duration_is_elapsed,
                            },
                            options,
                            userid
                        );

                        queryParts.push(`
                            due_date = $${updateParams.push(new_due_date)},
                            due_date_time = $${updateParams.push(options.due_date_time || false)}`);

                        queryParts.push(`
                            start_date = $${updateParams.push(new_start_date)},
                            start_date_time = $${updateParams.push(options.start_date_time)}`);
                    } else {
                        queryParts.push(`
                            due_date = $${updateParams.push(options.due_date)},
                            due_date_time = $${updateParams.push(options.due_date_time || false)}`);

                        const newStartDateData = await calculateNewStartDate(
                            options,
                            old_start_date,
                            old_start_date_time,
                            old_due_date,
                            old_due_date_time,
                            task_start_date,
                            old_duration,
                            old_duration_is_elapsed,
                            userid,
                            team_id
                        );
                        if (newStartDateData) {
                            queryParts.push(`
                                    start_date = $${updateParams.push(newStartDateData.start_date)},
                                    start_date_time = $${updateParams.push(newStartDateData.start_date_time)}`);
                        }
                    }
                } else {
                    if (options.old_start_date && !options.prevent_start_date_change) {
                        queryParts.push(`
                start_date = $${updateParams.push(old_start_date)},
                start_date_time = $${updateParams.push(old_start_date_time)}`);
                    }

                    if (options.old_due_date && !options.prevent_due_date_change) {
                        queryParts.push(`
                due_date = $${updateParams.push(old_due_date)},
                due_date_time = $${updateParams.push(old_due_date_time)}`);
                    }
                }

                if (options.old_duration) {
                    queryParts.push(`
                        duration = $${updateParams.push(old_duration)},
                        duration_is_elapsed = $${updateParams.push(old_duration_is_elapsed)}`);
                }

                if (options.content) {
                    queryParts.push(`
            content = $${updateParams.push(mergedContent)},
            text_content = $${updateParams.push(mergedTextContent)},
            lower_text_content = $${updateParams.push(mergedLowerTextContent)},
            date_updated = $${updateParams.push(now)}`);

                    if (mergedContent) {
                        queries.push(prepareUpdateYdocQueries(ContentEntityType.TASK, task_id, ydoc, mergedContent));
                    }
                }

                if (queryParts.length === 0) {
                    series_cb();
                    return;
                }

                updateQuery += `${queryParts.join(', ')} WHERE id = $1`;

                queries.push({
                    query: updateQuery,
                    params: updateParams,
                });

                series_cb();
            },

            // Copy all top level subtasks from template
            async series_cb => {
                if (!options.subtasks || (isSubtask && !nested_subtasks)) {
                    series_cb();
                    return;
                }

                try {
                    const orderindexStatsForSubtasks = await getOrderindexStats(
                        template_id,
                        config.get('views.parent_types.task'),
                        options.from_template || options.from_parent_template,
                        {
                            userid,
                            fetchSubtasksForTaskType: true,
                        }
                    );

                    const task_w_all_subtasks = await getTaskWithSubtasks(template_id);

                    const top_level_subtasks = task_w_all_subtasks.filter(
                        ({ parent, subtask_parent }) => parent && !subtask_parent
                    );

                    let gantt_dates;
                    if (options.remap_subtask_due_dates && options.due_date) {
                        gantt_dates = await remapSubtasksDates(
                            team_id,
                            userid,
                            template_id,
                            task_w_all_subtasks,
                            options.due_date,
                            !!options.due_date_time
                        );
                    }
                    const orderindex_offset = Math.random();

                    if (top_level_subtasks.length > 500) {
                        logger.error({
                            msg: 'Merge template into task trying to copy too many subtasks',
                            subtask_count: top_level_subtasks.length,
                        });
                    }

                    const tasksToUndelete = [];

                    async.eachSeries(
                        top_level_subtasks,
                        (row, each_cb) => {
                            const parent = isSubtask ? parent_id : task_id;
                            const subtask_parent =
                                nested_subtasks && ((row.parent && row.subtask_parent) || isSubtask) ? task_id : null;

                            const _options = {
                                archived: options.archived,
                                assigned_comment: options.assigned_comment,
                                assignees: options.subtask_assignees,
                                attachments: options.attachments,
                                auto_id: options.auto_id,
                                comment_attachments: options.comment_attachments,
                                comment: options.comment,
                                content: options.content,
                                create_notifications: false,
                                custom_fields: options.custom_fields,
                                custom_type: options.custom_type,
                                done,
                                earliest_due_date: options.earliest_due_date,
                                external_dependencies: options.external_dependencies,
                                followers: options.followers,
                                from_template: true,
                                from_v2_template_child: template_v2,
                                internal_dependencies: options.internal_dependencies,
                                nested_subtasks,
                                old_assignees: options.old_subtask_assignees,
                                old_subtask_assignees: options.old_subtask_assignees,
                                old_checklists: options.old_checklists,
                                old_due_date: options.old_due_date,
                                old_followers: options.old_followers,
                                old_orderindex: !!orderindexStatsForSubtasks,
                                old_start_date: options.old_start_date,
                                old_status: options.old_status,
                                old_statuses: options.old_statuses,
                                old_tags: options.old_tags,
                                old_duration: options.old_duration,
                                orderindex_offset,
                                parent,
                                from_parent_task: true,
                                permanent_template_id: options.permanent_template_id,
                                recur_settings: options.recur_settings,
                                remap_start_date: options.remap_start_date,
                                skip_access: options.skipAccess,
                                skip_task_created_trigger: options.skip_task_created_trigger,
                                status: options.subtask_status || options.status,
                                subtask_parent,
                                subtasks: nested_subtasks ? options.subtasks : false,
                                subcategory_id: task_subcategory_id,
                                tags: options.tags,
                                task_relationships: options.relationships,
                                trigger_id: options.trigger_id,
                                estimates_per_assignee,
                                set_deleted: true,
                                set_deleted_for_outside_transaction: true,
                                webhook_payload: options.webhook_payload,
                                orderindexStats: orderindexStatsForSubtasks,
                                sync_blocks_old_to_new,
                            };

                            if (options.due_date && options.remap_subtask_due_dates && gantt_dates) {
                                _options.apply_gantt_dates = true;
                                _options.gantt_dates = gantt_dates;
                            }

                            copyTask._copyTask(userid, row.id, _options, (copyErr, copyResult) => {
                                if (copyErr) {
                                    each_cb(copyErr);
                                } else {
                                    old_to_new = { ...old_to_new, ...copyResult.old_to_new };
                                    Object.keys(copyResult.old_dependencies).forEach(key => {
                                        if (!old_dependencies[key]) {
                                            old_dependencies[key] = copyResult.old_dependencies[key];
                                        } else {
                                            old_dependencies[key] = copyResult.old_dependencies[key].concat(
                                                old_dependencies[key]
                                            );
                                        }
                                    });

                                    Object.keys(copyResult.old_links).forEach(key => {
                                        old_links[key] = [
                                            ...new Set([...(old_links[key] || []), ...copyResult.old_links[key]]),
                                        ];
                                    });

                                    subtasks_count += 1;

                                    const hist_query = taskHelpers.simpleHistoryQuery(
                                        userid,
                                        parent,
                                        'new_subtask',
                                        copyResult.id
                                    );

                                    tasksToUndelete.push(copyResult.id);
                                    if (emitTaskCreatedForNestedSubtasks(userid)) {
                                        copyResult.subtask_ids?.forEach(subtaskId => tasksToUndelete.push(subtaskId));
                                    }

                                    queries.push(hist_query);

                                    each_cb();
                                }
                            });
                        },
                        err => {
                            if (err) {
                                series_cb(err);
                            } else {
                                queries.push({
                                    query: 'UPDATE task_mgmt.items SET deleted = false, date_deleted = NULL WHERE id = ANY($1) RETURNING id as undeleted_subtask_id',
                                    params: [tasksToUndelete],
                                });
                                undeletedSubtaskIds.push(...tasksToUndelete);
                                series_cb();
                            }
                        }
                    );
                } catch (err) {
                    series_cb(new TemplateError(err, 'TSKTEMP_057'));
                }
            },
            // Get all internal dependencies from template
            series_cb => {
                if (!options.internal_dependencies) {
                    series_cb();
                } else {
                    const _options = {
                        task_id: template_id,
                        skip_access: true,
                        only_ids: true,
                    };
                    _getDependencies(userid, _options, (err, { dependencies }) => {
                        if (err) {
                            series_cb(new TemplateError(err, 'TSKTEMP_047'));
                            return;
                        }
                        if (dependencies.length === 0) {
                            series_cb();
                            return;
                        }

                        dependencies.forEach(({ task_id: taskId, depends_on }) => {
                            if (!old_dependencies[taskId]) {
                                old_dependencies[taskId] = [];
                            }
                            old_dependencies[taskId].push(template_id === taskId ? depends_on : template_id);
                        });

                        old_to_new[template_id] = task_id;
                        series_cb();
                    });
                }
            },
            series_cb => {
                db.getCopyConn(null, { label: '_mergeTemplateIntoTask' }, (err, _client, _done) => {
                    if (err) {
                        series_cb({ err: 'Internal server error', status: 500, ECODE: 'TSKTEMP_036' });
                    } else {
                        client = _client;
                        done = _done;
                        series_cb();
                    }
                });
            },
            series_cb => {
                client.query('BEGIN', err => {
                    if (err) {
                        series_cb(err);
                    } else {
                        client.versionUpdates = [];
                        series_cb();
                    }
                });
            },
            // copy dependencies
            series_cb => {
                if ((!options.top && options.client) || !options.internal_dependencies) {
                    series_cb();
                } else {
                    copyDependencies.copyDependencies({ client, old_dependencies, old_to_new, userid }, series_cb);
                }
            },
            // copy external dependencies
            series_cb => {
                if ((!options.top && options.client) || !options.external_dependencies) {
                    series_cb();
                } else {
                    copyDependencies.copyExternalDependencies(
                        {
                            client,
                            old_dependencies,
                            old_to_new,
                            userid,
                            internal_dependencies: options.internal_dependencies,
                        },
                        series_cb
                    );
                }
            },
            // copy internal dependencies
            series_cb => {
                if (!options.internal_dependencies) {
                    series_cb();
                } else {
                    db.readQuery(
                        'SELECT * FROM task_mgmt.task_links WHERE task_id = $1 OR link_id = $1',
                        [template_id],
                        (err, { rows }) => {
                            if (err) {
                                series_cb(new TemplateError(err, 'TSKTEMP_048'));
                            } else if (!rows.length) {
                                series_cb();
                            } else {
                                old_links[template_id] = [];

                                rows.forEach(({ task_id: taskId, link_id }) => {
                                    const link = template_id === taskId ? link_id : taskId;
                                    if (old_links[template_id].indexOf(link) < 0) {
                                        old_links[template_id].push(link);
                                    }
                                });
                                series_cb();
                            }
                        }
                    );
                }
            },
            // copy links
            series_cb => {
                if ((!options.top && options.client) || !options.internal_dependencies) {
                    series_cb();
                } else {
                    if (!old_to_new[template_id]) {
                        old_to_new[template_id] = task_id;
                    }

                    copyLinks.copyLinks({ old_links, old_to_new, userid, client, link_old_elements: true }, series_cb);
                }
            },
            // copy checklists
            series_cb => {
                if (!options.checklists && !options.old_checklists) {
                    series_cb();
                    return;
                }

                checklistMod._copyChecklists(template_id, task_id, client, { ...options, userid }, series_cb);
            },
            // copy attachment docs
            series_cb => {
                if (!options.attachments) {
                    series_cb();
                    return;
                }
                copyAttachmentDocs(template_id, task_id, client, options, (attach_err, attach_result) => {
                    if (attach_result?.viewMap && attach_result?.pageMap) {
                        docAttachmentMap.view = attach_result.viewMap;
                        docAttachmentMap.page = attach_result.pageMap;
                    }

                    series_cb(attach_err, attach_result);
                });
            },

            function copy_comments(series_cb) {
                if (!options.comment) {
                    series_cb();
                    return;
                }

                const _options = {
                    comment: !options.assigned_comment,
                    exclude_attachment: !options.attachments,
                    fake_user: false,
                    unresolve_comments: false,
                    type: config.get('comments.types.task'),
                    workspace_id: template_obj.workspace_id,
                    attachmentMap,
                    docAttachmentMap,
                };

                copyComments.copyComments(template_id, task_id, null, _options, series_cb);
            },
            series_cb => {
                if (!options.recur_settings) {
                    series_cb();
                    return;
                }
                client.query('DELETE FROM task_mgmt.task_recur_settings WHERE last_task = $1', [task_id], series_cb);
            },
            series_cb => {
                if (!options.recur_settings) {
                    series_cb();
                    return;
                }
                // Don't need to add new version since we do immediately above

                client.query(
                    `INSERT INTO task_mgmt.task_recur_settings
                    (   settings_id, timezone,
                        rrule, set_time,
                        recur_until_count,
                        recur_until, last_task,
                        hours, minutes,
                        status_type, create_new_task,
                        periodically, periodically_days,
                        simple_settings, ignore_weekends,
                        month_setting, recur_data,
                        last_recur_output,
                        recur_count, recur_next,
                        recur_on_schedule, on_schedule_date,
                        recur_new_status, recur_new_status_id,
                        workspace_id
                    )
                    (
                        SELECT
                            $1, timezone,
                            rrule, set_time,
                            recur_until_count,
                            task_recur_settings.recur_until, $3,
                            hours, minutes,
                            status_type, create_new_task,
                            periodically, periodically_days,
                            simple_settings, ignore_weekends,
                            month_setting, task_recur_settings.recur_data,
                            last_recur_output, task_recur_settings.recur_count,
                            task_recur_settings.recur_next,
                            recur_on_schedule, on_schedule_date,
                            task_recur_settings.recur_new_status, recur_new_status_id,
                            (SELECT tasks.workspace_id FROM task_mgmt.tasks WHERE tasks.id = $3)
                        FROM task_mgmt.task_recur_settings, task_mgmt.items
                        WHERE
                            task_recur_settings.last_task = $2
                            AND items.id = $2
                            AND items.recur_settings = task_recur_settings.settings_id
                    )`,
                    [recur_settings_id, template_id, task_id],
                    series_cb
                );
            },
            series_cb => {
                // Update for task with V2 Recurring
                if (!options.recur_settings && !template_obj.settings_id) {
                    series_cb();
                    return;
                }

                client.query(
                    `UPDATE task_mgmt.items
                     SET recurring = $1, recur_settings = $2
                     WHERE id = $3`,
                    [template_obj.recurring, recur_settings_id, task_id],
                    series_cb
                );
            },
            series_cb => {
                if (!options.recur_settings || !template_obj.recurring) {
                    series_cb();
                    return;
                }
                const recur_opts = {
                    recurring: template_obj.recurring,
                    recur_next: template_obj.recur_next,
                    recur_on: template_obj.recur_on,
                    recur_type: template_obj.recur_type,
                    recur_on_status: template_obj.recur_on_status,
                    new_status: template_obj.recur_new_status,
                    recur_rule: template_obj.recur_rule,
                    recur_time: template_obj.recur_time,
                    due_date: template_obj.due_date,
                    recur_skip_missed: template_obj.recur_skip_missed,
                    recur_task: template_obj.recur_task,
                    recur_data: template_obj.recur_data,
                    copy_original: template_obj.copy_original,
                    recur_daily: template_obj.recur_daily,
                    recur_immediately: template_obj.recur_immediately,
                    recur_ignore_today: template_obj.recur_ignore_today,
                    skip_access: true,
                    returnQuery: true,
                    set_due_date: false,
                };

                recurrence._setRecurring(userid, task_id, recur_opts, (recurErr, recurResult) => {
                    if (recurErr) {
                        series_cb(recurErr);
                        return;
                    }

                    const { query } = recurResult;
                    const { params } = recurResult;

                    queries.push({ query, params });
                    series_cb();
                });
            },

            series_cb => {
                if (!options.content || syncBlocks.length === 0) {
                    series_cb();
                    return;
                }

                syncBlocks.forEach(syncBlock => {
                    syncBlock.parent_id = task_id;
                    syncBlock.id = sync_blocks_old_to_new[syncBlock.id];
                });
                queries.push(...prepareSyncBlockQueries(userid, targetTaskWorkspaceId, syncBlocks));
                series_cb();
            },

            // execute all the queries
            series_cb => {
                if (template_obj?.workspace_id) {
                    versionUpdates.push({
                        object_type: ObjectType.TASK,
                        object_id: task_id,
                        workspace_id: template_obj.workspace_id,
                        operation: OperationType.UPDATE,
                        data: {
                            context: { ws_key: options.ws_key },
                        },
                    });
                } else {
                    logger.error({
                        msg: 'Failed to write OVM update',
                        ECODE: 'OVM_WS_322',
                        OVM_CRITICAL: true,
                    });
                }
                db.batchQueriesWithClient(queries, { client, done, create_transaction: false }, (err, result) => {
                    if (err) {
                        series_cb(err);
                        return;
                    }

                    result.forEach(row => {
                        if (row.id && row.task_id) {
                            hist_ids.push(row.id);
                            if (row.main_event) {
                                hist_id = row.id;
                            }

                            if (row.updated_task_id && template_obj?.workspace_id) {
                                versionUpdates.push({
                                    object_type: ObjectType.TASK,
                                    object_id: row.updated_task_id,
                                    workspace_id: template_obj.workspace_id,
                                    operation: OperationType.UPDATE,
                                    data: {
                                        context: { ws_key: options.ws_key },
                                    },
                                });
                            }
                        }
                        if (row.undeleted_subtask_id && template_obj?.workspace_id) {
                            versionUpdates.push(
                                {
                                    object_type: ObjectType.TASK,
                                    object_id: row.undeleted_subtask_id,
                                    workspace_id: template_obj?.workspace_id,
                                    operation: OperationType.CREATE,
                                    data: {
                                        relationships: [
                                            {
                                                type: ObjectRelationshipType.TASK_PARENT,
                                                object_type: ObjectType.TASK,
                                                object_id: task_id,
                                                workspace_id: template_obj?.workspace_id,
                                            },
                                        ],
                                        context: { ws_key: options.ws_key },
                                    },
                                },
                                {
                                    object_type: ObjectType.TASK_ACCESS,
                                    object_id: row.undeleted_subtask_id,
                                    workspace_id: template_obj?.workspace_id,
                                    operation: OperationType.CREATE,
                                }
                            );
                        }
                        if (row.assignee) {
                            added_assignees.push(row.assignee);
                        }

                        if (row.cf_history_id) {
                            cf_hist_ids.push(row.cf_history_id);
                        }
                    });

                    if (result?.rows?.length) {
                        historyRows.push(...result.rows);
                    }

                    series_cb();
                });
            },
            // update OVM version
            async series_cb => {
                // grab back any version update requests the client could have collected along the way
                versionUpdates.push(...client.versionUpdates);
                if (versionUpdates.length > 0) {
                    try {
                        const txClient = new TransactionClientImpl(client, ovm);
                        updateObjectVersionUpdatesFromHistoryRows(versionUpdates, historyRows);
                        events = await ovm.updateVersions(txClient, versionUpdates);
                    } catch (versionerr) {
                        logger.error({
                            msg: 'Failed to write OVM update',
                            ECODE: 'OVM_WS_315',
                            OVM_CRITICAL: true,
                        });

                        series_cb(versionerr);
                        return;
                    }
                }
                client.query('COMMIT', async err => {
                    if (err) {
                        series_cb(err);
                    } else {
                        done();
                        series_cb();
                        // eslint-disable-next-line @typescript-eslint/no-empty-function
                        await ovm.notifyChanges(events).catch(() => {});
                        await sendWebhookInBatchOnTaskCreation(
                            undeletedSubtaskIds,
                            template_obj.workspace_id,
                            options
                        ).catch(error => {
                            logger.error({
                                msg: 'Failed to send webhook in batch',
                                err: error,
                            });
                        });
                        // Notification for parent task will be sent separately.
                        postTaskCreatedNotifications(userid, undeletedSubtaskIds);
                    }
                });
            },
            async series_cb => {
                if (!options.custom_type) {
                    series_cb();
                    return;
                }

                if (!customType.isCustomType() || customType.isPredefinedCustomTypeRange()) {
                    series_cb();
                    return;
                }

                try {
                    await saveTaskTemplateCustomTaskType(userid, template_id, task_id, {
                        targetWorkspaceId: Number(targetTaskWorkspaceId),
                        proxyClient: queryProxyFactory(),
                    });
                } catch (e) {
                    series_cb(e);
                    return;
                }
                series_cb();
            },
            // post content update to coediting service
            series_cb => {
                if (!options.content) {
                    series_cb();
                    return;
                }

                coeditorClientInstance
                    .postContentUpdate(task_id, CoEditorEntityType.TASK, mergedContent, template_obj.team_id)
                    .catch(() => {});
                series_cb();
            },
            async series_cb => {
                if (!options.old_assignees) {
                    series_cb();
                    return;
                }
                try {
                    group_assignees = await assigneeMod._validGroupAssignees(
                        null,
                        true,
                        task_subcategory_id,
                        template_id
                    );
                    await assigneeMod._saveGroupAssignees(task_id, group_assignees, template_obj.workspace_id);
                } catch (err) {
                    series_cb(err);
                    return;
                }
                series_cb();
            },
            async series_cb => {
                if (!options.old_followers) {
                    series_cb();
                    return;
                }
                try {
                    const group_followers = await assigneeMod._validGroupFollowers(
                        template_id,
                        task_subcategory_id,
                        null
                    );
                    await assigneeMod._saveGroupFollowers(task_id, group_followers, template_obj.workspace_id);
                } catch (err) {
                    series_cb(err);
                    return;
                }
                series_cb();
            },
            async series_cb => {
                // insert assignees into task history
                const history_data = {};

                if (options.trigger_id) {
                    history_data.trigger_id = options.trigger_id;
                }
                if (options.auto_id) {
                    history_data.auto_id = options.auto_id;
                }
                if (options.callOrigin) {
                    history_data.callOrigin = options.callOrigin;
                }
                // makes infinite loop checker to be more forgiving and allows automations
                // to be run on all subtasks and the parent task
                if (subtasks_count > 0) {
                    history_data.subtaskCount =
                        (subtasks_count + 1) *
                        ((added_assignees ? added_assignees.length : 0) +
                            (group_assignees ? group_assignees.length : 0) +
                            1);
                }
                const params = [task_id, userid, history_data, now];

                let query = 'INSERT INTO task_mgmt.task_history(task_id, userid, data, date, field, after) VALUES ';

                const insert_values_query = [];

                added_assignees.forEach(assignee => {
                    insert_values_query.push(`($1, $2, $3, $4, 'assignee', $${params.push(assignee)})`);
                });

                group_assignees?.forEach(group_assignee => {
                    insert_values_query.push(`($1, $2, $3, $4, 'group_assignee', $${params.push(group_assignee)})`);
                });

                if (!insert_values_query.length) {
                    series_cb();
                    return;
                }

                query += insert_values_query.join(',');

                query += ` RETURNING id, after, id as cdc_history_id, field as cdc_field, task_id as cdc_object_id, 'task' as cdc_object_type`;

                db.writeQuery(query, params, (assigneeErr, result) => {
                    if (assigneeErr) {
                        series_cb(assigneeErr);
                        return;
                    }
                    result.rows?.forEach(row => {
                        assignee_hist_ids.push(row.id);
                    });
                    if (result.rows?.length) {
                        historyRows.push(...result.rows);
                    }
                    // helper function to send notifications for task assignees
                    sendNotificationForAssignees({
                        user_id: userid,
                        id: task_id,
                        assignee_hist_items: result.rows,
                        team_id,
                    });
                    series_cb();
                });
            },
            series_cb => {
                if (!options.due_date && !options.old_start_date && !options.remap_start_date) {
                    series_cb();
                    return;
                }

                const query = `SELECT due_date, due_date_time, start_date, start_date_time FROM task_mgmt.items WHERE id = $1`;
                const params = [task_id];

                db.readQuery(query, params, (err, result) => {
                    if (err) {
                        series_cb(new TemplateError(err, 'TSKTEMP_046'));
                        return;
                    }

                    const [task] = result.rows;

                    if (task) {
                        options.trigger_due_date = task.due_date;
                        options.trigger_due_date_time = task.due_date_time;
                        options.trigger_start_date = task.start_date;
                        options.trigger_start_date_time = task.start_date_time;
                    }

                    series_cb();
                });
            },
        ],
        async err => {
            if (err) {
                if (!err.ECODE) {
                    logger.error({ msg: 'Failed to merge template into task', err });
                    cb({ err: 'Internal server error', status: 500, ECODE: 'TSKTEMP_044' });
                } else {
                    cb(err);
                }
                if (client) {
                    db.rollback(client, done);
                }
            } else {
                // don't return task object if template is being merged via automation or from preset task template
                if (options.auto_id || options.from_preset_task_template) {
                    cb();
                } else {
                    _getItem(userid, task_id, { skipAccess: true }, cb);
                }

                // Send task updated notification to creator
                sqsNotif.sendNotifMessage('createTaskNotifications', [userid, task_id, hist_id, {}]);

                sqsWs.sendWSMessage('sendTaskUpdated', [
                    task_id,
                    {
                        userid,
                        hist_ids,
                        ws_key: options.ws_key,
                        date_updated: new Date().getTime(),
                    },
                ]);

                const all_hist_ids = [...assignee_hist_ids, ...cf_hist_ids, hist_id];

                webhook.sendWebhookMessage('taskUpdated', {
                    task_id,
                    hist_ids: all_hist_ids,
                    auto_id: options.auto_id,
                    trigger_id: options.trigger_id,
                    team_id: options.team_id,
                    userid,
                });

                if (options.trigger_due_date) {
                    try {
                        await createScheduledDueDateTriggers(
                            [
                                {
                                    task_id,
                                    due_date: options.trigger_due_date,
                                    due_date_time: options.trigger_due_date_time,
                                    workspace_id: template_obj.workspace_id,
                                },
                            ],
                            {
                                trigger_id: options.trigger_id,
                                auto_id: options.auto_id,
                                scheduled_from: 'task_template',
                            }
                        );
                    } catch (err1) {
                        logTemplateError(err1, 'TASKTEMP_061');
                    }
                }

                if (options.trigger_start_date) {
                    try {
                        await createScheduledStartDateTriggers(
                            [
                                {
                                    task_id,
                                    start_date: options.trigger_start_date,
                                    start_date_time: options.trigger_start_date_time,
                                    workspace_id: template_obj.workspace_id,
                                },
                            ],
                            {
                                trigger_id: options.trigger_id,
                                auto_id: options.auto_id,
                                scheduled_from: 'task_template',
                            }
                        );
                    } catch (err1) {
                        logTemplateError(err1, 'TASKTEMP_062');
                    }
                }

                // Help to sync content if destination task is opened during merge,
                // no need to sync if destination doesn't have editor_token
                if (content_updated && editor_token) {
                    try {
                        codoxSync(editor_token, mergedContent, now);
                    } catch (e) {
                        // do nothing
                    }
                }

                templateHelpers.storeTemplateUsed(
                    userid,
                    options.permanent_template_id,
                    'task',
                    null,
                    template_obj.team_id
                );

                options[TemplateTracer.KEY]?.addTags({
                    copiedTasksAndSubtasksCount: Object.keys(old_to_new).length,
                });
            }
        }
    );
}

async function getStatusDataForMerge({ task_id, template_id }) {
    const query = `
        SELECT statuses.id AS status_id, statuses.status
        FROM
            task_mgmt.statuses
            INNER JOIN task_mgmt.subcategories ON subcategories.status_group = statuses.status_group
        WHERE
            subcategories.id = (
                SELECT subcategory FROM task_mgmt.tasks WHERE id = $1
            )
            AND statuses.status = (
                SELECT status FROM task_mgmt.tasks WHERE id = $2
            )
        LIMIT 1
    `;
    const params = [task_id, template_id];
    const result = await db.replicaQueryAsync(query, params);

    return result?.rows?.[0] ?? null;
}

export function mergeTemplateIntoTask(req, resp, next) {
    const userid = req.decoded_token.user;
    const { template_id, task_id } = req.params;
    const options = req.body;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;

    options.middleware_queries = req.middleware_queries;
    options.ws_key = ws_key;
    options.top = true;
    options.skipAccess = userid === config.clickbot_assignee;

    _mergeTemplateIntoTask(userid, task_id, template_id, options, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    });
}

export async function getTaskTemplateName(template_id) {
    const templateResult = await db.readQueryAsync(
        `
        SELECT
            name
        FROM
            task_mgmt.items WHERE id = $1`,
        [template_id]
    );

    if (templateResult.rows.length === 0) {
        logger.error({
            msg: 'Template not found',
            ECODE: 'TSKTEMP_040',
            status: 404,
        });
        return undefined;
    }

    return templateResult.rows[0].name;
}

/**
 * Helper function to calculate the new start date.
 * @param options
 * @param old_start_date
 * @param old_start_date_time
 * @param old_due_date
 * @param old_due_date_time
 * @param task_start_date
 * @param duration
 * @param duration_is_elapsed
 * @param userid
 * @param team_id
 * @returns {{start_date_time, start_date: number}|null}
 */
async function calculateNewStartDate(
    options,
    old_start_date,
    old_start_date_time,
    old_due_date,
    old_due_date_time,
    task_start_date,
    duration,
    duration_is_elapsed,
    userid,
    team_id
) {
    const remap_start_date = !!options.old_start_date;
    let new_start_date;
    let new_start_date_time;
    let is_start_date_updated = false;

    const has_duration_set = duration != null && Object.keys(duration).length !== 0;

    if (old_start_date && old_due_date && remap_start_date) {
        if (!has_duration_set || duration_is_elapsed === true) {
            const date_diff = parseInt(options.due_date, 10) - parseInt(old_due_date, 10);
            new_start_date = parseInt(old_start_date, 10) + date_diff;
            new_start_date_time = options.due_date_time;
            is_start_date_updated = true;
        } else {
            // Calculate new start date based on when duration_is_elapsed = false
            // When duration_is_elapsed = false, then we skip non-working days when calculating the date_diff and new_start_date
            let workWeekSettings = null;
            try {
                workWeekSettings = await workScheduleClient.getWorkWeekSettings(userid, team_id);
            } catch (err) {
                logger.warn({
                    msg: 'Failed to get work week settings from work schedule service, defaulting to standard work week',
                    err,
                });
                workWeekSettings = getDefaultWorkWeekSettings();
            }
            new_start_date = await calculateStartDateWithDuration(
                old_start_date,
                old_start_date_time,
                options.due_date,
                options.due_date_time,
                old_due_date,
                old_due_date_time,
                duration_is_elapsed,
                userid,
                workWeekSettings
            );
            new_start_date_time = options.due_date_time;
            is_start_date_updated = true;
        }
    } else if (old_start_date && !old_due_date) {
        if (old_start_date <= options.due_date) {
            new_start_date = old_start_date;
            new_start_date_time = old_start_date_time;
        } else {
            new_start_date = options.due_date;
            new_start_date_time = options.due_date_time;
        }
        is_start_date_updated = true;
    } else if (!old_start_date && task_start_date > options.due_date) {
        new_start_date = options.due_date;
        new_start_date_time = options.due_date_time;
        is_start_date_updated = true;
    }

    if (is_start_date_updated) {
        return { start_date: new_start_date, start_date_time: new_start_date_time };
    }

    return null;
}

async function calculateStartDateWithDuration(
    old_start_date,
    old_start_date_time,
    new_due_date,
    new_due_date_time,
    old_due_date,
    old_due_date_time,
    duration_is_elapsed,
    userid,
    workWeekSettings
) {
    let new_start_date;

    const { rows: users } = await getUsersInfo([userid], ['timezone'], true);
    if (!users || !users.length || !users[0].timezone) {
        // If null, then we need to calculate the date_diff based on the old_due_date and old_start_date
        const date_diff = parseInt(new_due_date, 10) - parseInt(old_due_date, 10);
        new_start_date = parseInt(old_start_date, 10) + date_diff;
        return new_start_date;
    }
    const { timezone } = users[0];

    new_start_date = await getNewStartDateUsingGantt(
        old_start_date,
        old_start_date_time,
        new_due_date,
        new_due_date_time,
        old_due_date,
        old_due_date_time,
        duration_is_elapsed,
        timezone,
        workWeekSettings
    );

    return new_start_date;
}
