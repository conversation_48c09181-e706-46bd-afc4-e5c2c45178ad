import config from 'config';

import { getLogger } from '@clickup/shared/utils-logging';
import { DbPool } from '@clickup/data-platform/pg-pools';
import { isTest as isTestingEnv } from '@clickup-legacy/utils/environment';
import { EnableChatVia } from '@clickup-legacy/libs/integrations/segment/interfaces/enableChat';
import {
    trackProductivityAddonChange,
    trackTypedEvent,
} from '@clickup-legacy/libs/integrations/segment/segmentService';
import { SegmentEvent } from '@clickup-legacy/libs/integrations/segment/enums/eventEnums';
import type { DbQueryResult, QueryObject, SimpleClient } from '@clickup/utils/db-types';
import {
    AddonPurchaseOperation,
    AddonType,
    BillingAddress,
    BillingEventType,
    ChoosePlanInputAddon,
    PlanComparison,
    PlanIds,
    PlanIdToTier,
    PlanNames,
    PlanProductCodes,
    SpecificAddonMap,
    SpecificAddonType,
    TaxInfo,
    WorkspaceSalesType,
    WorkspacePaymentsCycle,
    PlanTransactionLineItemType,
    parseWorkspaceTaxInfoToBillingAddress,
    PlanTiers,
} from '@clickup/billing/types';
import { planIdToTier } from '@clickup-legacy/libs/billing/plan/utils';
import { promiseAccessTeam } from '../../../utils/access2';
import { ClickUpTracer } from '../../../utils/tracer';
import { TeamId, TeamRoleLabels, TeamRoles, UserId, WorkspaceServiceStatus } from '../../../libs/common/types';
import ClickappsService from '../../clickapps/clickapps.service';
import {
    BillingLineItem,
    RequestedTransactionInfo,
    UnifiedTransactionReason,
    WorkspaceBillingState,
} from '../../../libs/billing/unified/types';
import {
    comparePlans,
    isPaidPlan,
    isPlanTierBusiness,
    isPlanTierBusinessPlus,
    isPlanTierDowngrade,
    isPlanTierFreeForever,
    isPlanTierUnlimited,
    isPlanTierUpgrade,
} from '../../../libs/billing/plan/utils';
import { computeUnifiedPricing, getUnifiedPlanInfo, validateIfSalesAssisted } from '../unified/unifiedComputations';
import { Addon } from '../../../libs/billing/addons/interface';
import { isAddonIdOfType } from '../../../libs/billing/addons/utils/validation';
import { ClickUpError } from '../../../utils/errors';
import { TransactionEventType, TransactionHistoryLineItemData } from '../../../libs/billing/unified/transactions/types';
import { BraintreeSaleData } from '../../../libs/billing/braintree/braintree.types';
import { asyncCreateTransaction, voidTransaction } from '../braintreeAsync';
import { readAsync, writeAsync, writeAsyncFunction } from '../../../utils/db2';
import { StatusErrorCodes } from '../../../utils/errors/constants';
import { CuMultilineTaxService } from '../tax/cuMultilineTaxService';
import { CuTaxService } from '../tax/cuTaxService';
import { isChatV3Qualified, shouldEnableChat } from '../../integrations/split/squadTreatments/chatTreatments';
import { cuWorkspaceBillingStateCollector } from '../unified/cuWorkspaceBillingStateCollector';
import type { WorkspaceUpdateData } from '../../../libs/billing/team/workspaceRepository';
import { CuWorkspaceService } from '../../team/datastores/workspaceService';
import { CuPromoCodeHistoryService } from '../promoCode/promoCodeHistoryService';
import { CuCreditUsedService } from '../credit/creditUsedService';
import { CuTeamAuditService } from '../audit/teamAuditRepository';
import {
    PlanChangeAuditAction,
    PlanChangeAuditData,
} from '../../../libs/billing/audit/taskMgmtTeamAudit/types/planChangeAudit';
import { FailedPlanChangeBillingAction } from '../../../libs/billing/audit/taskMgmtTeamAudit/types/failedPlanChangeBillingAudit';
import { EntitlementName, entitlementService } from '../../entitlements/entitlementService';
import { CuAddonsService } from '../addonsService';
import { TransactionHistoryRecord } from '../../../libs/billing/unified/transactions/TransactionHistoryRecord';
import { CuTransactionHistoryService } from '../unified/cuTransactionHistoryService';
import { ClickAppName } from '../../../libs/clickapps/types/clickapp-name.type';
import { ChatEnablementWorkspaceClickapp } from '../../clickapps/definitions/chat-enablement-workspace.clickapp';
import {
    sendPlanUpgradeToBusinessEmail,
    sendPlanUpgradeToBusinessPlusEmail,
    sendPlanUpgradeToProEmail,
} from '../../email/email';
import { sendMultipleAddonsMailing } from '../addonsMailing';
import { transaction as gAnalyticsTransaction } from '../../../utils/google-analytics-helper';
import { deleteNylasAccountsIfNecessary } from '../../../../libs/customer-email/src';
import { globalWriteQueryAsync, readQueryAsync, writeQueryAsync } from '../../../utils/db';
import { GlobalTableMigrationBatchId } from '../../../utils/pg/interfaces/GlobalDatabaseByBatchConfig';
import { MetricNames } from '../../../libs/billing/metricNames';
import { planChangeMetrics } from '../planChangeMetrics';
import { metricsClient } from '../../../metrics/metricsClient';
import { CuTeamPromoCodeService } from '../promoCode/teamPromoCodeService';
import { PlanPromoProductType } from '../../../libs/billing/promoCode/teamPromoCode/types';
import {
    RollupDuringPlanChangeAction,
    RollupDuringPlanChangeAuditData,
} from '../../../libs/billing/audit/taskMgmtTeamAudit/types/rollupDuringPlanChangeAudit';
import { cuOfferService } from '../offerService';
import sdTouchWorkspace from '../../sd/api/sdTouchWorkspace';
import { OfferWorkspaceState } from '../../../libs/billing/offer/types';
import { shouldStoreTransactionInTaxSystem } from '../../../libs/billing/tax/taxHelpers';
import { shouldEnableSchedulingDowngradeRequests } from '../../integrations/split/squadTreatments/crmBillingTreatments';
import { cancelDowngradeRequest, scheduleOrUpdateDowngradeRequest } from '../downgrades/utils';
import { isTeamInRenewalGracePeriod } from '../../../libs/billing/team/utils/teamUtils';
import {
    getEntitlementsChangeQueries,
    getPlanChangeQueries,
} from '../../../libs/billing/plan/planChange/planChangeUtils';
import { reportAddonPurchase } from '../../../libs/billing/addons/utils/addonsTrackerUtils';
import { CuAddonsReportingService } from '../addonsReportingService';
import { WorkspaceTaxInfoDto } from '../../../libs/billing/tax/workspaceTaxInfo/workspaceTaxInfo.types';

const logger = getLogger('unifiedPlanChange');

const tracer = new ClickUpTracer();

const PlanChangeError = ClickUpError.makeNamedError('PlanChangeError');

export type PlanChangeResponse = {
    price_difference: number;
    bill_amount: number;
    chat_enabled: boolean;
    transaction_id?: string;
    // Execution of downgrade (since it's scheduled)
    executionDate?: Date;
    /**
     * Information about the SKU that it's going to be changed
     * source: from which SKU (current)
     * target: to which SKU (desired)
     */
    skuInfo: {
        sourceSku: PlanTiers;
        targetSku: PlanTiers;
    };
};

// TODO: This type will be removed, once running rollup during plan change on new unified logic
export type ChoosePlanOptions = {
    inviter?: UserId;
    plan_id?: PlanIds | null;
    free_forever_plan?: boolean;
    cycles: WorkspacePaymentsCycle;
    promo_code?: string;
    remove_promo?: boolean;
    next_bill_date?: number;
    promo?: string;
    reason?: string;
    addons?: ChoosePlanInputAddon[];
    enable_chat?: boolean;
    custom_debounce_time?: number;
    use_stored_offer?: boolean;
    immediate_downgrade?: boolean;
    taxInfo?: WorkspaceTaxInfoDto;
};

export type PlanChangeParams = {
    planId: PlanIds;
    cycles: WorkspacePaymentsCycle;
    promoCode?: string;
    removePromoCode?: boolean;
    addons?: ChoosePlanInputAddon[];
    enableChat?: boolean;
    inviter?: UserId;
    /** To match legacy behaviour, there can be any field provided to API endpoint */
    originalPlanChangeParams: ChoosePlanOptions;
    /**
     * There's possibility to override custom debounce time, though it shouldn't be used on FE.
     * It's primarily done for testing purposes via separate API call.
     */
    customDebounceTime?: number;
    useStoredOffer?: boolean;
    immediateDowngrade?: boolean;
    billingAddressOverride?: BillingAddress;
};

const checkRecentIdenticalPlanAudit = async (
    teamId: TeamId,
    desiredPlanId: PlanIds,
    customDebounceTime = 75
): Promise<boolean> => {
    const debounceTime = new Date().getTime() - 1000 * customDebounceTime;

    const { rows: teamPlanHistoryRows = [] } = await readQueryAsync(
        `SELECT 1 FROM task_mgmt.team_plan_history 
        WHERE team_id = $1 
        AND change = 'plan_changed' 
        AND date > $2 
        AND plan_id = $3`,
        [teamId, debounceTime, desiredPlanId]
    );

    return teamPlanHistoryRows.length > 0;
};

export const changePlanUnified = tracer.wrap(
    'billing.changePlan',
    {},
    async (userId: UserId, teamId: TeamId, planChangeParams: PlanChangeParams): Promise<PlanChangeResponse> => {
        logger.addField('userid', userId);
        logger.addField('team_id', teamId);
        logger.addField('process', 'changePlan');

        let result;
        try {
            result = await _changePlan(userId, teamId, planChangeParams);
        } catch (err: unknown) {
            logger.error({
                msg: 'Failed to perform unified plan change',
                err,
            });
            throw err;
        } finally {
            logger.delField('userid');
            logger.delField('team_id');
            logger.delField('process');
        }

        return result;
    }
);

const _changePlan = async (
    userId: UserId,
    teamId: TeamId,
    planChangeParams: PlanChangeParams
): Promise<PlanChangeResponse> => {
    // TODO: Think about extracting this to unified computations (should addon have same checks?)
    await Promise.all([
        validateIfSalesAssisted(teamId),
        promiseAccessTeam(userId, teamId, {
            permissions: [config.get<string>('permission_constants.billing')],
        }),
    ]);

    const recentPlanChange = await checkRecentIdenticalPlanAudit(
        +teamId,
        planChangeParams.planId,
        planChangeParams.customDebounceTime
    );

    if (recentPlanChange) {
        throw new PlanChangeError(
            'Please retry transaction after 60 seconds',
            'PLAN_018',
            StatusErrorCodes.TooManyRequests
        );
    }

    /**
     * Below function throws specific plan change error if it fails to fetch workspace
     * billing state. Workspace billing state should be fetched after initial charge for additional members,
     * as it can impact the workspace state (which in turn can change billed users)
     */
    const wsBillingState = await getWorkspaceBillingState(userId, teamId, !!planChangeParams.useStoredOffer);

    if (!planChangeParams.planId) {
        throw new PlanChangeError('Invalid plan', 'PLAN_015', StatusErrorCodes.BadRequest);
    }

    const isWorkspaceEligibleForSchedulingDowngrade =
        !isTeamInRenewalGracePeriod({
            grace_period_end: +wsBillingState.renewalGracePeriodEnd,
        }) && wsBillingState.serviceStatus !== WorkspaceServiceStatus.Suspended;

    if (
        shouldEnableSchedulingDowngradeRequests(teamId) &&
        isPlanTierDowngrade(wsBillingState.billedPlan, planChangeParams.planId) &&
        isWorkspaceEligibleForSchedulingDowngrade &&
        !planChangeParams.immediateDowngrade
    ) {
        /**
         * We should perform exactly same validation checks as during unified computations, just postponing the true downgrade.
         * getUnifiedPlanInfo performs such checks underneath.
         */
        await getUnifiedPlanInfo(teamId, planChangeParams.planId, wsBillingState, {
            shouldSkipPlanAvailabilityCheck: false,
        });

        const hasWorkspaceAutoUpgradableTrial = !!wsBillingState.autoUpgradableTrial?.plan;

        if (hasWorkspaceAutoUpgradableTrial) {
            throw new PlanChangeError(
                'Cannot schedule downgrade request while workspace has an auto-upgradable trial',
                'PLAN_019',
                StatusErrorCodes.BadRequest
            );
        }

        await scheduleOrUpdateDowngradeRequest(userId, teamId, {
            type: PlanTransactionLineItemType.Plan,
            source: wsBillingState.billedPlan,
            target: planChangeParams.planId,
        });

        return {
            price_difference: 0,
            bill_amount: 0,
            chat_enabled: false,
            executionDate: wsBillingState.nextBillDate,
            skuInfo: {
                sourceSku: PlanIdToTier[wsBillingState.billedPlan],
                targetSku: PlanIdToTier[planChangeParams.planId],
            },
        };
    }

    const newPromoCodes = planChangeParams.promoCode
        ? {
              [PlanPromoProductType]: {
                  promo_code: planChangeParams.promoCode,
                  cycles_applied: 0,
                  product_type: PlanPromoProductType,
              },
              ...planChangeParams.addons
                  .map(addon => SpecificAddonMap.get(addon.id))
                  .reduce(
                      (acc, productType) => ({
                          ...acc,
                          [productType]: {
                              promo_code: planChangeParams.promoCode,
                              cycles_applied: 0,
                              product_type: productType,
                          },
                      }),
                      {}
                  ),
          }
        : {};

    const {
        oldState,
        newState,
        changes,
        transaction,
        lineItems: allLineItems,
        billingAddress,
        transactionHistoryLineItems: allTransactionHistoryLineItems,
        promoCodeError,
    } = await computeUnifiedPricing(
        teamId,
        userId,
        {
            newPlan: planChangeParams.planId,
            newCycles: planChangeParams.cycles,
            newAddons: planChangeParams.addons,
            newPromoCodes,
            removePromoCode: planChangeParams.removePromoCode,
            payloadProducts: [
                PlanPromoProductType,
                ...(planChangeParams.addons?.map(addon => SpecificAddonMap.get(addon.id)) ?? []),
            ],
            newBillingAddress: planChangeParams.billingAddressOverride,
        },
        { addonsValidation: { shouldSkipAddonEligibityChecks: false } },
        wsBillingState
    );

    /**
     * We should consider rollup line items, if and only if there's any user which has been
     * indeed rolled up during the plan change.
     *
     * NOTE: We cannot do this in the unified computations, since some flows (i.e CRM's rollup) considers
     * such cases as a seperate processing strategy.
     */
    const lineItems = allLineItems.filter(
        lineItem =>
            (lineItem.qty > 0 && lineItem.billingEvent === BillingEventType.SeatsChange) ||
            lineItem.billingEvent === BillingEventType.PlanChange
    );

    const transactionHistoryLineItems = allTransactionHistoryLineItems.filter(
        transactionHistoryLineItem =>
            (transactionHistoryLineItem.qty > 0 &&
                transactionHistoryLineItem.billingEvent === BillingEventType.SeatsChange) ||
            transactionHistoryLineItem.billingEvent === BillingEventType.PlanChange
    );

    if (promoCodeError?.length) {
        logger.error({ msg: 'Provided promo code is not valid', promoCodeError });
        const error = Array.isArray(promoCodeError) ? promoCodeError?.[0] : promoCodeError;
        throw new PlanChangeError(error.err, error.ECODE, error.status);
    }

    try {
        if (+transaction.amount < 0) {
            throw new Error(`Negative BLU plan change transaction amount ${transaction.amount} for team ${teamId}`);
        }

        if (transaction.amount !== '0.00') {
            if (!wsBillingState.paymentMethodToken) {
                throw new PlanChangeError(
                    'Missing Payment Method. Please add a credit card.',
                    'PLAN_017',
                    StatusErrorCodes.PermissionDenied // 403
                );
            }

            const transactionData = computeLegacyPlanChangeTransactionData(
                lineItems,
                oldState,
                newState,
                transaction,
                billingAddress
            );

            const planChangeResponse = await processPlanChangeTransaction(
                userId,
                teamId,
                planChangeParams,
                oldState,
                changes,
                newState,
                lineItems,
                transaction,
                transactionData,
                transactionHistoryLineItems,
                billingAddress
            );

            if (wsBillingState.activeOffer?.tier_name === planIdToTier(planChangeParams.planId)) {
                const workspaceState: OfferWorkspaceState = {
                    totalBillAmount: planChangeResponse.bill_amount,
                    billedUsersThisCycle: newState.billedUsersThisCycle,
                    nextBillingDate: newState.nextBillDate,
                    transactionId: planChangeResponse.transaction_id,
                    newTotalMemberCount: newState.seatCounts.memberCount,
                    lineItems: lineItems.map(item => ({
                        sku: item.sku,
                        amount: item.total,
                        ppu: item.listPricePerUnit,
                        effectivePpu: item.effectivePricePerUnit,
                        qty: item.qty,
                    })),
                    userId,
                    prorate: Number(transactionData.prorate) < 1,
                    creditUsed: transactionData.credit,
                    discountFromLastTransaction: Number(transactionData.discount),
                };

                await cuOfferService.applyActiveOffer(teamId, userId, workspaceState);
            }

            return planChangeResponse;
        }

        const isCreditTransaction = lineItems.reduce((acc, currentItem) => acc + +currentItem.creditUsed, 0) > 0;
        const freeTransactionReason = isCreditTransaction
            ? 'Plan change has been paid with credit'
            : 'Plan change price was zero';

        logger.info({
            msg: `Performing BLU plan change for free`,
            freeTransactionReason,
            changes,
        });

        /**
         * Audit logs are added in the post-processing phase down below. It's added always, even if there's no braintree
         * transaction made.
         */
        const { taxInfo } = transaction;

        const isChatEnabled = await processSuccessfulTransaction(
            userId,
            teamId,
            planChangeParams,
            oldState,
            changes,
            newState,
            lineItems,
            /**
             * There's no transaction id, if it was paid fully with credit or price was zero.
             */
            null,
            transactionHistoryLineItems,
            billingAddress,
            taxInfo
        );

        return {
            price_difference: 0,
            bill_amount: 0,
            chat_enabled: isChatEnabled,
            skuInfo: {
                sourceSku: PlanIdToTier[oldState.billedPlan],
                targetSku: PlanIdToTier[newState.billedPlan],
            },
        };
    } catch (err: unknown) {
        logger.error({ msg: 'Failed to change plan in BLU logic', err, team_id: teamId, planChangeParams });

        throw err;
    }
};

const computeLegacyPlanChangeTransactionData = (
    lineItems: BillingLineItem[],
    oldState: WorkspaceBillingState,
    newState: WorkspaceBillingState,
    transactionInfo: RequestedTransactionInfo,
    billingAddress: BillingAddress
) => {
    const planLineItem = lineItems.find(
        lineItem => lineItem.sku.type === 'plan' && lineItem.billingEvent === BillingEventType.PlanChange
    );
    const subtotal = lineItems.reduce((acc, lineItem) => +(acc + +lineItem.subtotal).toFixed(2), 0);
    const taxRate = +transactionInfo.taxAmount / subtotal;

    // We're applying discount only if it's from promo code
    const totalDiscount = lineItems
        .reduce((total, item) => total + (item.promo ? +item.totalDiscount : 0), 0)
        .toFixed(2);

    return {
        lineItems,
        reason: UnifiedTransactionReason.PlanChange,
        memberCount: planLineItem.qty,
        free_seats: newState.seatConfiguration.free,
        minimum_seats: newState.seatConfiguration.minimum,
        price: transactionInfo.amount,
        credit: oldState.availableCreditAmount,
        creditAfter: newState.availableCreditAmount,
        basePrice: planLineItem.listPriceLumpSum,
        ppu: planLineItem.promo ? planLineItem.listPricePerUnit : planLineItem.effectivePricePerUnit,
        plan_id: newState.billedPlan,
        previous_plan_id: String(oldState.billedPlan),
        cycles: newState.frequency,
        // It's always 0 in legacy, as well, TODO: It should be probably removed
        price_discount: 0,
        prorate: planLineItem.prorate,
        end_of_cycle: String(+newState.nextBillDate),
        promo: planLineItem.promo,
        discount: totalDiscount,
        subtotal,
        taxAmount: +transactionInfo.taxAmount,
        taxRate,
        taxInfo: transactionInfo.taxInfo,
        billingZipCode: billingAddress.postalCode,
        billingRegion: billingAddress.region,
        billingCountryCodeAlpha2: billingAddress.countryCodeAlpha2,
        addons_data: lineItems.reduce(
            (acc, lineItem) =>
                lineItem.sku.type !== 'plan' && lineItem.billingEvent === BillingEventType.PlanChange
                    ? [...acc, { ...lineItem.addonData, id: lineItem.sku.id }]
                    : acc,
            []
        ),
    };
};

const processPlanChangeTransaction = async (
    userId: TeamId,
    teamId: TeamId,
    planChangeParams: PlanChangeParams,
    oldState: WorkspaceBillingState,
    changes: Partial<WorkspaceBillingState>,
    newState: WorkspaceBillingState,
    lineItems: BillingLineItem[],
    transactionInfo: RequestedTransactionInfo,
    braintreeTransactionData: BraintreeSaleData,
    transactionHistoryLineItems: TransactionHistoryLineItemData[],
    billingAddress: BillingAddress
): Promise<PlanChangeResponse> => {
    try {
        const braintreeTrxResult = await asyncCreateTransaction(
            newState.owner,
            teamId,
            +transactionInfo.amount,
            newState.paymentMethodToken,
            braintreeTransactionData,
            {}
        )
            .then(result => result)
            .catch(async err => {
                /**
                 * Insert failed billing audit message, if and only if it's indeed failed billing.
                 * NOTE: It differs from the legacy code, as legacy puts this audit message in couple of cases
                 * (even if the billing succeeded)
                 */
                await CuTeamAuditService.insertAudit(userId, teamId, new Date().getTime(), {
                    action: FailedPlanChangeBillingAction,
                    data: {
                        price: transactionInfo.amount,
                        newPlan: {
                            planId: newState.billedPlan,
                            nextBillDate: newState.nextBillDate,
                            cycles: newState.frequency,
                            billedUsersThisCycle: newState.billedUsersThisCycle,
                        },
                        ...(err && {
                            braintreeResponse: {
                                err: err.err,
                                transaction_id: err.extra?.transaction_id,
                                braintreeProcessorResponseCode: err.extra?.braintreeProcessorResponseCode,
                                braintreeProcessorResponseText: err.extra?.braintreeProcessorResponseText,
                            },
                        }),
                    },
                });

                throw err;
            });

        const { taxInfo } = transactionInfo;

        const isChatEnabled = await processSuccessfulTransaction(
            userId,
            teamId,
            planChangeParams,
            oldState,
            changes,
            newState,
            lineItems,
            braintreeTrxResult.id,
            transactionHistoryLineItems,
            billingAddress,
            taxInfo
        );

        const billAmount = +transactionInfo.amount;

        return {
            price_difference: billAmount,
            bill_amount: billAmount,
            chat_enabled: isChatEnabled,
            transaction_id: braintreeTrxResult.id,
            skuInfo: {
                sourceSku: PlanIdToTier[oldState.billedPlan],
                targetSku: PlanIdToTier[newState.billedPlan],
            },
        };
    } catch (err: unknown) {
        logger.warn({
            msg: 'Failed BLU plan change transaction',
            err,
            teamId,
            braintree: true,
            amount: transactionInfo.amount,
            braintreeTransactionData,
        });

        throw err;
    }
};

const processSuccessfulTransaction = async (
    userId: UserId,
    teamId: TeamId,
    planChangeParams: PlanChangeParams,
    oldState: WorkspaceBillingState,
    changes: Partial<WorkspaceBillingState>,
    newState: WorkspaceBillingState,
    lineItems: BillingLineItem[],
    braintreeTransactionId: string,
    transactionHistoryLineItems: TransactionHistoryLineItemData[],
    billingAddress: BillingAddress,
    taxInfo: TaxInfo
): Promise<boolean> => {
    if (braintreeTransactionId) {
        logger.info({
            msg: `Successful unified plan change payment`,
            team_id: teamId,
            transaction_id: braintreeTransactionId,
            braintree: true,
        });
    }

    const shouldCheckChatEnablement =
        planChangeParams.enableChat &&
        isPlanTierUpgrade(oldState.billedPlan, newState.billedPlan) &&
        shouldEnableChat(+teamId) &&
        isChatV3Qualified(
            +teamId,
            String(+oldState.createdAt),
            PlanIdToTier[newState.billedPlan],
            newState.seatCounts.memberCount
        );

    const chatAlreadyEnabled =
        shouldCheckChatEnablement &&
        (await ClickappsService.isClickappEnabled(String(teamId), ClickAppName.ChatEnablementWorkspace));

    const shouldEnableChatForWorkspace = shouldCheckChatEnablement && !chatAlreadyEnabled;
    let isChatEnabled = false;

    const transactionTotal = +lineItems.reduce((acc, lineItem) => +(acc + +lineItem.total).toFixed(2), 0);
    const creditUsed = lineItems.reduce((acc, lineItem) => +(acc + +lineItem.creditUsed).toFixed(2), 0);

    /** It contains add-on ids which were requested during the plan change request. */
    const requestedAddonIds = (planChangeParams.addons || []).map(({ id }) => id);
    const oldAddonIds = oldState.addons.map(({ addon_id: addonId }: Addon) => addonId);
    const newAddonIds = newState.addons.map(({ addon_id: addonId }: Addon) => addonId);
    const removedAddons = oldState.addons.filter(({ addon_id: addonId }: Addon) => !newAddonIds.includes(addonId));
    const addedAddons = newState.addons.filter(({ addon_id: addonId }: Addon) => !oldAddonIds.includes(addonId));

    const addonTypesDowngradeRequestsToCancel = Array.from(
        new Set([...addedAddons, ...removedAddons].map(({ addon_id: addonId }: Addon) => SpecificAddonMap.get(addonId)))
    );

    const rollUpPlanLineItem = lineItems.find(
        lineItem => lineItem.sku.type === 'plan' && lineItem.billingEvent === BillingEventType.SeatsChange
    );

    const rollUpAddonLineItem = lineItems.find(
        lineItem => lineItem.sku.type !== 'plan' && lineItem.billingEvent === BillingEventType.SeatsChange
    );

    /**
     * We're preparing the transactional queries and ovm updates which are related to plan change flow.
     * OVM version updates are commited if and only if the below transaction will be successful
     */

    const changePlanEntitlements: EntitlementName[] = [
        EntitlementName.Siml,
        EntitlementName.AiEnabled,
        EntitlementName.SessionSettings,
        EntitlementName.RequiredCf,
        EntitlementName.CustomRoles,
    ];

    const entitlementsForNewPlan = entitlementService.getPlanEntitlements(
        newState.entitlementPlan,
        changePlanEntitlements
    );
    const entitlementsForOldPlan = entitlementService.getPlanEntitlements(
        oldState.entitlementPlan,
        changePlanEntitlements
    );

    const {
        planChangeQueries,
        versionUpdates: planChangeOvmVersionUpdates,
        globalPlanChangeQueries,
    } = getPlanChangeQueries(userId, teamId, oldState, newState, changes, {
        entitlementsForNewPlan,
        entitlementsForOldPlan,
    });

    /** Chat enablement query is prepared, and executed within the transaction scope */
    const { query: chatEnablementQuery, params: chatEnablementParams } =
        ChatEnablementWorkspaceClickapp.getReturningUpsertQuery(String(teamId), { chat_enabled: true }, +teamId);

    await writeAsyncFunction(
        async transactionClient => {
            try {
                for (const planChangeQuery of planChangeQueries) {
                    try {
                        await transactionClient.queryAsync(planChangeQuery.query, planChangeQuery.params);
                    } catch (err: unknown) {
                        const extendedError = <Error & QueryObject>{
                            ...(<Error>err),
                            query: planChangeQuery.query,
                            params: planChangeQuery.params,
                        };
                        throw extendedError;
                    }
                }

                for (const newAddon of newState.addons) {
                    await CuAddonsService.insertAddon(teamId, newAddon, transactionClient);
                }

                for (const removedAddon of removedAddons) {
                    await CuAddonsService.removeAddon(teamId, removedAddon, transactionClient);
                }

                /** Enable chat for workspace if and only if it's eligible for enablement */
                if (shouldEnableChatForWorkspace) {
                    ({ rows: [{ chat_enabled: isChatEnabled }] = [{ chat_enabled: false }] } = <
                        DbQueryResult<{ chat_enabled: boolean }>
                    >await transactionClient.queryAsync(chatEnablementQuery, chatEnablementParams));
                }

                /**
                 * We're recording audit log of rollup happening during plan change, if it indeed
                 * happended.
                 */
                if (rollUpPlanLineItem) {
                    const rollupDuringPlanChangeAuditData: RollupDuringPlanChangeAuditData = {
                        action: RollupDuringPlanChangeAction,
                        data: {
                            rolledUpPlan: {
                                planName: rollUpPlanLineItem.sku.id,
                                cycles: rollUpPlanLineItem.sku.frequency,
                                nextBillDate: rollUpPlanLineItem.endDate,
                            },
                            ...(rollUpAddonLineItem && {
                                rolledUpAddon: {
                                    addonId: <SpecificAddonType>rollUpAddonLineItem.sku.id,
                                },
                            }),
                            qty: rollUpPlanLineItem.qty,
                        },
                    };

                    await CuTeamAuditService.insertAudit(
                        userId,
                        teamId,
                        new Date().getTime(),
                        rollupDuringPlanChangeAuditData,
                        transactionClient
                    );
                }

                const teamAuditData: PlanChangeAuditData = {
                    action: PlanChangeAuditAction,
                    data: {
                        price: String(transactionTotal),
                        newPlan: {
                            planId: newState.billedPlan,
                            planName: PlanProductCodes[newState.billedPlan],
                            cycles: newState.frequency,
                            nextBillDate: newState.nextBillDate,
                            billedUsersThisCycle: newState.billedUsersThisCycle,
                        },
                        previousPlan: {
                            planId: oldState.billedPlan,
                            planName: PlanProductCodes[oldState.billedPlan],
                            cycles: oldState.frequency,
                            nextBillDate: oldState.nextBillDate,
                            billedUsersThisCycle: oldState.billedUsersThisCycle,
                        },
                        ...((planChangeParams.addons || []).length && {
                            requestedAddons: planChangeParams.addons.map(addon => ({
                                addonId: addon.id,
                                total: addon.amount,
                            })),
                        }),
                        ...(removedAddons.length && {
                            removedAddons: removedAddons.map(addon => ({
                                addonId: addon.addon_id,
                            })),
                        }),
                    },
                };

                await CuTeamAuditService.insertAudit(
                    userId,
                    teamId,
                    new Date().getTime(),
                    teamAuditData,
                    transactionClient
                );

                const teamsUpdateData: WorkspaceUpdateData = {
                    credit: newState.availableCreditAmount,
                    ...(isPlanTierFreeForever(newState.entitlementPlan) && {
                        sales_type: WorkspaceSalesType.SelfServe,
                    }),
                };

                await CuWorkspaceService.updateWorkspace(teamId, teamsUpdateData, transactionClient);
                await CuTeamPromoCodeService.updateTeamPromos(teamId, oldState, newState, transactionClient);

                await CuPromoCodeHistoryService.insertNewPromoCodeHistoryUsages(
                    teamId,
                    newState,
                    lineItems,
                    transactionClient
                );

                await Promise.all([
                    cancelDowngradeRequest(userId, teamId, PlanTransactionLineItemType.Plan, transactionClient),
                    ...addonTypesDowngradeRequestsToCancel.map(addonType =>
                        cancelDowngradeRequest(userId, teamId, addonType, transactionClient)
                    ),
                ]);

                if (creditUsed) {
                    await CuCreditUsedService.storeCreditUsed(
                        teamId,
                        {
                            creditBefore: oldState.availableCreditAmount,
                            newCredit: newState.availableCreditAmount,
                            creditUsed,
                            transactionSource: 'plan_upgrade',
                        },
                        transactionClient
                    );
                }

                if (oldState.storedPromoCode) {
                    const removeStoredPromoCodeResult = await transactionClient.queryAsync<{
                        removed_stored_promo_code: string;
                    }>(
                        `WITH update_result AS (
                        UPDATE task_mgmt.teams t
                        SET stored_promo_code = NULL 
                        FROM task_mgmt.teams old_team_state
                        WHERE t.id = $1 AND t.id = old_team_state.id AND t.stored_promo_code IS NOT NULL
                        RETURNING t.id, old_team_state.stored_promo_code
                    )
                    INSERT INTO task_mgmt.team_audit (team_id, userid, action, data, date)
                    SELECT
                        $1,
                        $2,
                        'stored_promo_code_removed',
                        json_build_object('removed_stored_promo_code', update_result.stored_promo_code),
                        $3
                    FROM update_result`,
                        [teamId, userId, Date.now()]
                    );

                    if (removeStoredPromoCodeResult.rowCount) {
                        logger.info({
                            msg: 'Stored promo code removed',
                            removed_stored_promo_code: oldState.storedPromoCode,
                            team_id: teamId,
                        });
                    }
                }

                /**
                 * TODO: Move it to separate service.
                 * Should we have one generic error or each query should result in rollback, but with different
                 * error?
                 */
                await transactionClient.queryAsync(
                    'DELETE FROM task_mgmt.failed_transactions_to_submit WHERE team_id = $1',
                    [teamId]
                );

                /**
                 * Below error is thrown only in functional tests for specific workspace,
                 * to assert the rollback logic
                 */
                if (isTestingEnv && oldState.name.includes('test_failUpgrade')) {
                    throw new PlanChangeError(
                        'test error',
                        `trx_id ${braintreeTransactionId}`,
                        StatusErrorCodes.BadRequest
                    );
                }
            } catch (err: unknown) {
                logger.error({
                    msg: 'Failed to update workspace state after plan change',
                    err,
                    team_id: teamId,
                });

                /**
                 * If there was a braintree transaction, we should void it, as the whole post-processing
                 * logic resulted in failure.
                 */

                if (braintreeTransactionId) {
                    await voidBraintreeTransactionInPlanChange(teamId, braintreeTransactionId);
                }

                throw new PlanChangeError(
                    'Internal error while processing plan change',
                    'PLAN_011',
                    StatusErrorCodes.InternalServer
                );
            }
        },
        /** Handling OVM updates */
        { versionUpdates: planChangeOvmVersionUpdates }
    );

    for (const { queryObj, batchId } of globalPlanChangeQueries) {
        try {
            await writeAsync(queryObj.query, queryObj.params, null, null, {
                poolType: DbPool.GlobalWrite,
                globalTableMigrationBatchId: batchId,
            });
        } catch (err) {
            logger.error({
                msg: 'Failed to update global data after workspace plan change',
                err,
                team_id: teamId,
                ECODE: 'PLAN_014',
            });
        }
    }

    // TODO: Should it be in the transaction?
    // TODO: convert to event/signal
    await entitlementService.setEntitlements(newState.entitlementPlan, teamId, [
        EntitlementName.AutomationActions,
        EntitlementName.CustomFieldManualAiUsages,
        EntitlementName.CustomFieldAutomatedAiUsages,
        EntitlementName.PaidAggregatedAutoAi,
        EntitlementName.FreeAggregatedAutoAi,
        EntitlementName.AggregatedAutoAi,
        EntitlementName.TaskManualAiUsages,
        EntitlementName.TaskAutomatedAiUsages,
        EntitlementName.CardsAutomatedAiUsages,
    ]);

    try {
        // Notifies SD Platform that workspace was changed.
        sdTouchWorkspace(teamId).catch(() => {
            /* noop */
        });
    } catch (e) {
        // noop
    }

    const subtotal = +lineItems.reduce((acc, lineItem) => +(acc + +lineItem.subtotal), 0);
    const taxAmount = +lineItems.reduce((acc, lineItem) => +(acc + +(lineItem.tax?.amount ?? 0)), 0);

    metricsClient.increment(MetricNames.BILLING_TAXABLE_REVENUE, +subtotal, {
        txn_type: BillingEventType.PlanChange,
        plan_id: String(newState.billedPlan),
        workspace_no_tax: String(!newState.taxEnabledSetting),
        tax_exempt: String(newState.taxExempt),
        payment_token_exists: String(!!newState.paymentMethodToken),
        country: billingAddress.countryCodeAlpha2,
        has_zip_code: String(!!billingAddress.postalCode),
        billing_region: billingAddress.region,
        taxed: String(taxAmount > 0),
    });

    if (shouldStoreTransactionInTaxSystem({ taxInfo, taxAmount, teamId })) {
        const { taxTransactionId } = await CuMultilineTaxService().fetchMultilineTaxPriceFromPaymentMethod({
            taxLineItems: lineItems.map(lineItem => ({
                lineNumber: lineItem.lineNumber,
                qty: lineItem.qty,
                name: lineItem.sku.id,
                subTotal: +lineItem.subtotal,
                taxCode: lineItem.tax?.code ?? '',
                description: lineItem.description,
            })),
            addTax: newState.isTaxable,
            subTotal: subtotal,
            token: newState.paymentMethodToken,
            context: {
                errorMsg: 'Failed to create tax invoice in plan upgrade flow',
                process: 'plan upgrade',
            },
            workspaceId: String(teamId),
            btTransactionid: braintreeTransactionId,
            storeTransactionInTaxSystem: true,
        });

        await CuTaxService.updateCrmTransactionWithTaxTransactionId(
            braintreeTransactionId,
            taxTransactionId,
            taxInfo.taxSystem
        );
    }

    const currentDate = new Date();
    const areCyclesChanged = !!changes.frequency;

    try {
        await Promise.all([
            ...(transactionHistoryLineItems || [])
                .filter(
                    transactionHistoryLineItem =>
                        transactionHistoryLineItem.billingEvent === BillingEventType.SeatsChange
                )
                .map(async transactionHistoryLineItem => {
                    const transactionHistoryRecord = TransactionHistoryRecord.fromTransactionData({
                        teamId,
                        qty: transactionHistoryLineItem.qty,
                        ppu: transactionHistoryLineItem.ppu,
                        discountedPpu: transactionHistoryLineItem.discountedPpu,
                        totalPaid: transactionHistoryLineItem.totalPaid,
                        taxAmount: transactionHistoryLineItem.taxAmount,
                        subtotal: transactionHistoryLineItem.subtotal,
                        transactionDetails: transactionHistoryLineItem.transactionDetails,
                        eventType: TransactionEventType.PlanSeatsAdded,
                        transactionId: braintreeTransactionId,
                        startAt: currentDate,
                        originalEndAt: oldState.nextBillDate,
                        creationDate: currentDate,
                        transactionTriggeredBy: userId,
                        lineItemDetails: transactionHistoryLineItem.lineItemDetails,
                    });

                    await CuTransactionHistoryService.saveTransactionHistoryRecord(transactionHistoryRecord);
                }),
        ]);
    } catch (err: unknown) {
        // safety check, we should never reach this flow
        logger.error({
            msg: `Unexpected behaviour when generating seats change
            transaction history records in plan change flow - please investigate`,
            err,
            teamId,
        });
    }

    await CuTransactionHistoryService.updateTransactionHistoryPlanEndAt(teamId, currentDate);

    /** If cycles have been change during plan change, we should amend previous add-on records */
    if (areCyclesChanged) {
        await Promise.all(
            Object.values(AddonType).map(async (addonType: AddonType) =>
                CuTransactionHistoryService.updateTransactionHistoryAddOnEndAt(teamId, currentDate, addonType)
            )
        );
    }

    try {
        await Promise.all([
            ...(transactionHistoryLineItems || [])
                .filter(
                    transactionHistoryLineItem =>
                        transactionHistoryLineItem.billingEvent === BillingEventType.PlanChange
                )
                .map(async transactionHistoryLineItem => {
                    const isAddonLineItem = transactionHistoryLineItem.lineItemDetails.type !== 'plan';

                    const isRequestedAddonPurchase =
                        isAddonLineItem &&
                        requestedAddonIds.includes(<SpecificAddonType>transactionHistoryLineItem.lineItemDetails.item);

                    /**
                     * Billing computation algorithm calculates the price for all of the line items, so there's
                     * possibility of any of them being charged for 0 (no charge at all), such line items are critical
                     * from transaction point of view (as they ensure the same transaction data schema), however they are not
                     * vital from revenue recognition point of view. We're filtering some of them out.
                     */

                    if (
                        !changes.nextBillDate &&
                        !changes.frequency &&
                        !changes.serviceStatus &&
                        isAddonLineItem &&
                        !isRequestedAddonPurchase
                    ) {
                        /**
                         * Any of NBD, workspace's frequency, workspace's service status or promo code needs to be changed
                         * to store respective previous add-on transaction history record
                         */
                        return;
                    }
                    /**
                     * TODO: We shouldn't store transaction history line items which are full of 0s. Legacy stores them
                     * if and only if there's status / NBD / frequency change. Unified should do the same but consider
                     * also promo code change
                     */

                    const transactionHistoryRecord = TransactionHistoryRecord.fromTransactionData({
                        teamId,
                        qty: transactionHistoryLineItem.qty,
                        ppu: transactionHistoryLineItem.ppu,
                        discountedPpu: transactionHistoryLineItem.discountedPpu,
                        totalPaid: transactionHistoryLineItem.totalPaid,
                        taxAmount: transactionHistoryLineItem.taxAmount,
                        subtotal: transactionHistoryLineItem.subtotal,
                        transactionDetails: transactionHistoryLineItem.transactionDetails,
                        /**
                         * NOTE: Plan change flow does not allow for add-on upgrades or downgrades, so if there's addon
                         * requested, it's always add-on purchase.
                         * TODO: Maybe it should be generic enough, to support that as well?
                         */
                        eventType: isRequestedAddonPurchase
                            ? TransactionEventType.AddonPurchased
                            : TransactionEventType.PlanChange,
                        transactionId: braintreeTransactionId,
                        startAt: currentDate,
                        originalEndAt: newState.nextBillDate,
                        creationDate: currentDate,
                        transactionTriggeredBy: userId,
                        lineItemDetails: transactionHistoryLineItem.lineItemDetails,
                    });

                    await CuTransactionHistoryService.saveTransactionHistoryRecord(transactionHistoryRecord);
                }),
        ]);
    } catch (err: unknown) {
        // safety check, we should never reach this flow
        logger.error({
            msg: `Unexpected behaviour when generating plan change
            transaction history records in plan change flow - please investigate`,
            err,
            teamId,
        });
    }

    await Promise.all(
        removedAddons.map(async ({ id: removedAddonId }) => {
            const addonType = SpecificAddonMap.get(<SpecificAddonType>removedAddonId);
            await CuTransactionHistoryService.updateTransactionHistoryAddOnEndAt(teamId, new Date(), addonType).catch(
                (err: unknown) => {
                    logger.error({
                        msg: 'Failed to update transaction history add-on end at during plan cancellation',
                        team_id: teamId,
                        err,
                    });
                }
            );
        })
    );
    /**
     * This code is asynchronous, but should not be awaited. It's expected to let this run in the background
     * whereas the customer receives the response already. It's exactly the same logic as in legacy.
     * TODO: We should probably move this to proper background job.
     */

    handlePostRequestActions(teamId, userId, planChangeParams, oldState, newState, lineItems, isChatEnabled).catch(
        err => {
            logger.error({
                msg: 'Failure when handling post request actions in unified plan change with purchase',
                err,
            });
            // ignore error
        }
    );

    return isChatEnabled || chatAlreadyEnabled;

    /**
     * Handle event tracking, email sending, metrics etc.
     */
};

const getWorkspaceBillingState = async (userId: UserId, teamId: TeamId, useStoredOffer: boolean) => {
    try {
        return await cuWorkspaceBillingStateCollector.getWorkspaceBillingState({
            teamId,
            userId,
            seatsGetOptions: { skip_cache: true, useStoredOffer },
        });
    } catch (err: unknown) {
        logger.error({ msg: 'Failure while fetching workspace billing state', err, team_id: teamId, userId });

        throw new PlanChangeError('Failure while processing plan change', 'PLAN_099', StatusErrorCodes.InternalServer);
    }
};

const handlePostRequestActions = async (
    teamId: TeamId,
    userId: UserId,
    planChangeParams: PlanChangeParams,
    oldState: WorkspaceBillingState,
    newState: WorkspaceBillingState,
    lineItems: BillingLineItem[],
    isChatEnabled: boolean
) => {
    const planComparison = comparePlans(oldState.billedPlan, newState.billedPlan);

    if (planComparison !== PlanComparison.NoChange) {
        await addPlanHistory(userId, teamId, newState.billedPlan, oldState.billedPlan, oldState.frequency);
    }

    /**
     * We're computing the transaction details, only for plan change related records (not rolled up ones), as
     * these ones used to be put into analytics etc.
     */

    const transactionTotal = +lineItems
        .filter(lineItem => lineItem.billingEvent === BillingEventType.PlanChange)
        .reduce((acc, lineItem) => +(acc + +lineItem.total).toFixed(2), 0);

    const planLineItem = lineItems.find(
        lineItem => lineItem.sku.type === 'plan' && lineItem.billingEvent === BillingEventType.PlanChange
    );

    const subtotal = +lineItems
        .filter(lineItem => lineItem.billingEvent === BillingEventType.PlanChange)
        .reduce((acc, lineItem) => +(acc + +lineItem.subtotal).toFixed(2), 0);

    const addonLineItems = lineItems.filter(
        lineItem => lineItem.sku.type !== 'plan' && lineItem.billingEvent === BillingEventType.PlanChange
    );

    const [
        { rows: [{ email: userEmail, username: userName }] = [{ email: undefined, username: undefined }] },
        { rows: [{ role: userRoleId }] = [{ role: undefined }] },
    ] = await Promise.all([
        readAsync<{ email: string; username: string }>('SELECT email, username FROM task_mgmt.users WHERE id = $1', [
            userId,
        ]),
        readAsync<{ role: number }>('SELECT role FROM task_mgmt.team_members WHERE team_id = $1 AND userid = $2', [
            teamId,
            userId,
        ]),
    ]);

    const userRoleName = config.get<string>(`roles.${userRoleId}.name`);
    const userDomain = userEmail.split('@')[1];

    const planName = PlanNames[newState.billedPlan];
    const oldPlanName = PlanNames[oldState.billedPlan];

    if (planComparison === PlanComparison.Upgrade) {
        /**
         * TODO: this case is also in legacy, but we should discuss whether it should happen here also,
         * if user has no email associated with its account, we shouldn't try to send an e-mail, but probably
         * add-on updates and other external API calls should occur
         */
        if (!userEmail) {
            return;
        }

        if (isPlanTierUnlimited(newState.entitlementPlan)) {
            sendPlanUpgradeToProEmail(userId, userEmail, teamId, userName);
        } else if (isPlanTierBusiness(newState.entitlementPlan)) {
            sendPlanUpgradeToBusinessEmail(userId, userEmail, teamId, userName);
        } else if (isPlanTierBusinessPlus(newState.entitlementPlan)) {
            sendPlanUpgradeToBusinessPlusEmail(userId, userEmail, teamId, userName);
        }

        sendMultipleAddonsMailing(userId, newState.owner, { id: teamId, name: newState.name }, planChangeParams.addons);
        reportAddonPurchase(teamId, planChangeParams.addons, AddonType.Productivity, CuAddonsReportingService);

        const addonIds = (addonLineItems.map(addon => addon.sku.id) || []).join(',');
        const eventName = isPlanTierFreeForever(oldState.billedPlan)
            ? SegmentEvent.ConvertPaidPlan
            : SegmentEvent.UpgradeWorkspace;

        trackTypedEvent(eventName, +userId, {
            username: userName,
            userRole: userRoleName,
            userRoleId,
            addOnType: addonIds,
            planCode: String(newState.billedPlan),
            planName,
            oldPlanCode: String(oldState.billedPlan),
            oldPlanName,
            /**
             * TODO: User domain is sent as workspace domain in legacy as well,
             * should it be the case though?
             */
            workspaceDomain: userDomain,
            workspaceID: String(teamId),
            workspaceName: newState.name,
            'new plan': String(newState.billedPlan),
            'previous plan': oldPlanName,
            /** This value in legacy seems to be always false */
            promo: false,
            'promo code': planChangeParams.promoCode,
            price: transactionTotal,
            priceWithoutTax: subtotal,
            priceDifference: transactionTotal,
            'price per seat': planLineItem.effectivePricePerUnit,
            'billing cycle': newState.frequency,
        });

        gAnalyticsTransaction({
            user_id: userId,
            // TODO: Legacy sends team id in this place, should it be the case here also?
            transaction_id: teamId,
            total: transactionTotal,
            unit_price: planLineItem.effectivePricePerUnit,
            seats: planLineItem.qty,
            name: `Upgrade: ${planName} - ${newState.frequency}`,
        });
    } else if (planComparison === PlanComparison.NoChange && isPaidPlan(newState.entitlementPlan)) {
        // TODO: Add metrics to check if this flow is anyhow triggered
    }

    /**
     * TODO: Below code should probably be in the above else if statement. Discuss whether it's the case
     * in new unified logic.
     */
    if (isPlanTierFreeForever(newState.entitlementPlan)) {
        trackTypedEvent(SegmentEvent.CancelPlan, +userId, {
            username: userEmail,
            userRole: userRoleName,
            userRoleId,
            planCode: String(newState.billedPlan),
            planName,
            workspaceDomain: userDomain,
            workspaceID: String(teamId),
            workspaceName: newState.name,
            'previous plan': oldPlanName,
        });

        if (planLineItem.residualInfo?.oldSubtotal > 0) {
            const transactionResult = await readAsync<never>(
                `SELECT 1 FROM task_mgmt.transactions WHERE team_id = $1`,
                [teamId]
            ).catch(() => ({ rows: [] } as { rows: unknown[] }));

            const { originalPlanChangeParams } = planChangeParams;

            if (transactionResult && transactionResult.rows.length > 0 && originalPlanChangeParams?.reason) {
                await globalWriteQueryAsync(
                    'INSERT INTO task_mgmt.churn_reason(userid, team_id, date, reason) VALUES ($1, $2, $3, $4)',
                    [userId, teamId, new Date().getTime(), originalPlanChangeParams],
                    { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_2 }
                ).catch((churnInsertErr: unknown) => {
                    logger.error({
                        msg: 'Failed to insert churn reason',
                        err: churnInsertErr,
                    });
                });
            }
        }
    } else {
        /**
         * TODO: These events occur always if the desired plan is not from FF tier. That said,
         * amplitude events think that with every plan change there's a plan cancellation.
         * It should probably be fixed
         */

        trackTypedEvent(SegmentEvent.CancelPlan, +userId, {
            username: userEmail,
            userRole: userRoleName,
            userRoleId,
            planCode: String(newState.billedPlan),
            planName,
            workspaceDomain: userDomain,
            workspaceID: String(teamId),
            workspaceName: newState.name,
            'previous plan': oldPlanName,
        });
    }

    /** TODO: Should it be moved to the database's transaction scope? */
    deleteNylasAccountsIfNecessary(teamId);

    const aiAddon = (planChangeParams.addons || []).find(addon => isAddonIdOfType(addon.id, AddonType.Ai));
    if (aiAddon) {
        trackTypedEvent(SegmentEvent.ConvertPaidAI, +userId, {
            workspaceID: String(teamId),
            addonId: aiAddon.id,
        });
    }

    trackProductivityAddonChange(
        userId,
        { workspaceID: teamId, planID: newState.billedPlan },
        { addonAction: AddonPurchaseOperation.Purchase, addonVariant: SpecificAddonType.ProductivityPowerPack },
        planChangeParams.addons
    );

    if (isChatEnabled) {
        trackTypedEvent(SegmentEvent.EnableChat, +userId, {
            userRole: TeamRoleLabels[<TeamRoles>userRoleId],
            // TODO: Should it be entitlement plan? It's compatible with legacy for now
            planName: PlanNames[newState.entitlementPlan],
            // TODO: Should it be entitlement plan? It's compatible with legacy for now
            planCode: newState.entitlementPlan,
            via: EnableChatVia.PlanUpgrade,
        });
    }
};

const voidBraintreeTransactionInPlanChange = async (teamId: TeamId, braintreeTransactionId: string) => {
    try {
        const successfullyVoided = await voidTransaction(braintreeTransactionId);

        if (!successfullyVoided) {
            logger.error({
                msg: 'Voiding braintree transaction in plan change flow failed',
                teamId,
            });
            planChangeMetrics.increment(MetricNames.FAILURE_TRANSACION_VOIDED_IN_PLAN_CHANGE);
        } else {
            /** Below metric is incremented to match legacy behaviour */
            metricsClient.increment(MetricNames.BRAINTREE_SUCCESSFUL_TSX_VOID_EXCEPT_1_DOLLAR_AUTH, 1, { BLU: '1' });
            planChangeMetrics.increment(MetricNames.SUCCESSFUL_TRANSACION_VOIDED_IN_PLAN_CHANGE);
            logger.info({ msg: 'Voiding braintree transaction in plan change flow succedeed' });
        }
    } catch (err: unknown) {
        logger.error({
            msg: 'Unexpected failure while voiding braintree transaction in plan change  flow',
            err,
            teamId,
        });
        planChangeMetrics.increment(MetricNames.FAILURE_TRANSACION_VOIDED_IN_PLAN_CHANGE);
    }

    planChangeMetrics.increment(MetricNames.TOTAL_TRANSACION_VOIDED_IN_PLAN_CHANGE);
};

export const changePlanRequest = async (
    userId: UserId,
    teamId: TeamId,
    planChangeParams: ChoosePlanOptions
): Promise<PlanChangeResponse> => {
    planChangeMetrics.increment(MetricNames.ATTEMPT_PLAN_CHANGE_TOTAL, 1);

    try {
        planChangeMetrics.increment(MetricNames.ATTEMPT_UNIFIED_ONLY_PLAN_CHANGE, 1);

        const unifiedPlanChangeParams: PlanChangeParams = {
            inviter: planChangeParams.inviter,
            planId: planChangeParams.plan_id,
            cycles: planChangeParams.cycles,
            promoCode: planChangeParams.promo_code,
            removePromoCode: planChangeParams.remove_promo,
            addons: planChangeParams.addons || [],
            enableChat: planChangeParams.enable_chat,
            originalPlanChangeParams: { ...planChangeParams, addons: planChangeParams.addons || [] },
            customDebounceTime: planChangeParams.custom_debounce_time,
            useStoredOffer: planChangeParams.use_stored_offer,
            immediateDowngrade: planChangeParams.immediate_downgrade,
            ...(planChangeParams.taxInfo?.workspace_tax_info && {
                billingAddressOverride: parseWorkspaceTaxInfoToBillingAddress(planChangeParams.taxInfo),
            }),
        };

        const { price_difference, bill_amount, chat_enabled, executionDate, skuInfo } = await changePlanUnified(
            userId,
            teamId,
            unifiedPlanChangeParams
        );

        planChangeMetrics.increment(MetricNames.PROCESSED_SUCCESSFUL_UNIFIED_ONLY_PLAN_CHANGE, 1);
        planChangeMetrics.increment(MetricNames.PROCESSED_PLAN_CHANGE_TOTAL, 1);

        return {
            price_difference,
            bill_amount,
            chat_enabled,
            executionDate,
            skuInfo,
        };
    } catch (err: unknown) {
        logger.warn({ msg: 'Failure while running unified plan change', err, team_id: teamId });

        planChangeMetrics.increment(MetricNames.PROCESSED_FAILURE_UNIFIED_ONLY_PLAN_CHANGE, 1);
        planChangeMetrics.increment(MetricNames.PROCESSED_PLAN_CHANGE_TOTAL, 1);

        throw err;
    }
};

export async function addPlanHistory(
    userId: UserId,
    teamId: TeamId,
    planId: PlanIds,
    oldPlanId: PlanIds,
    oldCycles: WorkspacePaymentsCycle
) {
    try {
        await writeQueryAsync(
            `INSERT INTO task_mgmt.team_plan_history(userid, team_id, date, plan_id, docs_addon_id, change,
            old_plan_id, old_cycle) VALUES($1, $2, $3, $4, $5, $6, $7, $8)`,
            [userId, teamId, new Date().getTime(), planId, null, 'plan_changed', oldPlanId, oldCycles],
            {}
        );
    } catch (err: unknown) {
        logger.error({
            msg: 'Failed to add plan history',
            err,
            userId,
            teamId,
            planId,
            oldPlanId,
        });
    }
}

export async function executeChoosePlanCRM(workspaceId: TeamId, newPlanId: PlanIds, oldPlanId: PlanIds | undefined) {
    const changePlanEntitlements: EntitlementName[] = [
        EntitlementName.Siml,
        EntitlementName.AiEnabled,
        EntitlementName.SessionSettings,
        EntitlementName.RequiredCf,
        EntitlementName.CustomRoles,
    ];

    if (oldPlanId) {
        try {
            const entitlementsForNewPlan = entitlementService.getPlanEntitlements(newPlanId, changePlanEntitlements);
            const entitlementsForOldPlan = entitlementService.getPlanEntitlements(oldPlanId, changePlanEntitlements);

            const { entitlementsChangeQueries, globalEntitlementsChangeQueries } = getEntitlementsChangeQueries(
                workspaceId,
                oldPlanId,
                newPlanId,
                {
                    entitlementsForNewPlan,
                    entitlementsForOldPlan,
                }
            );

            await writeAsyncFunction(async (transactionClient: SimpleClient) => {
                for (const entitlementsChangeQuery of entitlementsChangeQueries) {
                    try {
                        await transactionClient.queryAsync(
                            entitlementsChangeQuery.query,
                            entitlementsChangeQuery.params
                        );
                    } catch (err: unknown) {
                        const extendedError = <Error & QueryObject>{
                            ...(<Error>err),
                            query: entitlementsChangeQuery.query,
                            params: entitlementsChangeQuery.params,
                        };
                        throw extendedError;
                    }
                }
            });

            for (const { queryObj, batchId } of globalEntitlementsChangeQueries) {
                try {
                    await writeAsync(queryObj.query, queryObj.params, null, null, {
                        poolType: DbPool.GlobalWrite,
                        globalTableMigrationBatchId: batchId,
                    });
                } catch (err: unknown) {
                    logger.error({
                        msg: 'Failed to update global data due to entitlements change',
                        err,
                        team_id: workspaceId,
                    });
                }
            }
        } catch (err: unknown) {
            logger.error({ msg: 'Failed to execute entitlements change queries', err, team_id: workspaceId });
        }
    }

    // TODO: convert to event/signal
    const result = await entitlementService.setEntitlements(newPlanId, workspaceId, [
        EntitlementName.AutomationActions,
        EntitlementName.CustomFieldManualAiUsages,
        EntitlementName.CustomFieldAutomatedAiUsages,
        EntitlementName.PaidAggregatedAutoAi,
        EntitlementName.FreeAggregatedAutoAi,
        EntitlementName.AggregatedAutoAi,
        EntitlementName.TaskManualAiUsages,
        EntitlementName.TaskAutomatedAiUsages,
        EntitlementName.CardsAutomatedAiUsages,
    ]);

    try {
        // Notifies SD Platform that workspace was changed.
        sdTouchWorkspace(workspaceId).catch(() => {
            /* noop */
        });
    } catch (e) {
        // noop
    }

    return result;
}
