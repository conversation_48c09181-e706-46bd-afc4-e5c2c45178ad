import aws from 'aws-sdk';
import config from 'config';
import fs from 'fs';
import moment from 'moment-timezone';
import randomstring from 'randomstring';
import uuid from 'node-uuid';
import _ from 'lodash';
import { ObjectType, OperationType } from '@time-loop/ovm-object-version';
import { AdminV3Setting, AdminV2Setting, LayoutSetting } from '@clickup-legacy/libs/settings/WorkspaceSettings';

import { createNewUserId } from '@clickup/data-platform/sequences';
import { getEgressAccessInfoService } from '@clickup-legacy/utils/access/services/authorization/instances';
import { logSkipSsoForSelectedUserConfig } from '@clickup-legacy/utils/audit-log';
import { shouldExcludeWorkspaceData, workspaceDeletionConfig } from '@clickup/data-platform/split-treatments';
import * as webhook from '@clickup-legacy/utils/webhook';
import clickappsService from '@clickup-legacy/models/clickapps/clickapps.service';
import { ParentType } from '@clickup-legacy/models/clickapps/types/parent-type.type';
import { PlanNames } from '@clickup/billing/types';
import { getLogger } from '@clickup/shared/utils-logging';
import { getObjectVersionManager } from '@clickup/object-version-manager-singleton';
import { TransactionClientImpl } from '@clickup/object-version-transaction-client';
import { createWorkspaceIdService } from '@clickup/data-platform/sharding/workspace-assignment';
import { bulkInviteEmailLeadSeeding } from '../user/services/emailLeads';
import * as helpers from '../helpers';
import * as async from '../../utils/asyncHelper';
import * as access from '../../utils/access2';
import * as cf_sign from '../../utils/cf_sign';
import * as color_helper from '../../utils/colorUtils';
import * as db from '../../utils/db';
import * as db2 from '../../utils/db2';
import * as email_helper from '../email/email';
import * as input from '../../utils/input_validation';
import * as notifSettingsMod from '../notifications/notificationSettings';
import * as project from '../project/CRUD';
import * as promoCodeMod from '../billing/promoCode';
import { SegmentEvent } from '../integrations/segment/enums/eventEnums';
import * as statusTemplateMod from '../templates/statusTemplate';
import * as teamMemberMod from './teamMember';
import { _getItems } from '../task/CRUD/getTask/get-items';
import * as viewsCRUD from '../views/views';
import { formatUser } from '../user/userProvider';
import * as thumb from '../../utils/thumbnail';
import * as sqs from '../../utils/v1-ws-message-sending';
import * as blockedDomains from '../blockedDomains';
import * as teamHelper from './helpers';
import * as awsUtil from '../../utils/awsUtils/awsUtil';
import * as pointsMod from '../pointsEstimate';
import { ClickUpError } from '../../utils/errors';
import { TeamErrors } from '../../utils/errors/constants';
import * as deleteViewService from '../views/services/deleteViewService';
import * as deleteSsoDatastore from '../sso/datastores/crud/deleteSso';
import * as deleteUserGroupMemberDatastore from '../teamMember/datastores/deleteTeamMemberDatastore';
import * as environment from '../../utils/environment';
import { closeWorkspaceUserAccessRequests } from './accessRequests';
import { trackEvent } from '../integrations/segment/service';
import { hierarchyObjectVersionUpdateRequests } from '../hierarchy/factories/ovmObjectVersionFactory';
import * as getTeamMod from './getTeam';
import { entitlementService, EntitlementName } from '../entitlements/entitlementService';
import { isEmailDomainBlockedOrOwned } from '../../utils/auth/services/emailOwnershipService';
import { getPlanChangeInfo } from './helpers/planChanges';
import { shouldEnableChat } from '../integrations/split/squadTreatments/chatTreatments';

import {
    shouldRestrictWorkspaceCreationForCorporateUsers,
    shouldActivateAuthenticatedAttachments,
    shouldDefaultNewWorkspacesToAuthAttachmentOn,
    shouldCheckWorkspaceCreationCap,
} from '../integrations/split/squadTreatments/userPlatformTreatments';

import { ssoWorkspaceScopingConfig } from '../integrations/split/squadTreatments/accessManagementTreatments';
import { extendRefreshTokenWithWsCreatedScope } from './services/teamScopeExtensionService';
import { getUpsertInviteMemberOnForMemberRoleQuery } from './factories/workspaceRolePermissionsFactory';
import { GlobalTableMigrationBatchId } from '../../utils/pg/interfaces/GlobalDatabaseByBatchConfig';
import { retry } from '../../libs/common/utils/retry';
import { calculateNextBillDate } from '../../libs/billing/team/utils';
import { buildUpsertTeamBillingInfoQuery } from '../../libs/workspace/datastores/sharedQueries';
import { planService } from '../billing/plan/planService';
import { setWorkspaceSettings } from './services/settingsService';
import { updateRequestForWorkspaceMembers, updateRequestForUser } from '../../libs/user/factories/ovm-update-requests';
import { generateEmailCode } from '../user/helpers/generateEmailCode';
import { FreeForeverDefault } from '../../libs/billing/plan/config/config';
import { notifySettingsChanges } from './helpers/notificationHelpers';
import { QueryableFormulaOperator } from '../field/formulas/entities/formulaOperator';
import { FormulaProvider } from '../field/formulas/services/formulaProvider';
import { FormulaCalculationService } from '../field/formulas/services/formulaCalculationService';
import { mapAdvancedSettingsToAuditLog } from './helpers/teamAuditHelpers';
import {
    formulaFieldBackfillingConfig,
    CalculationTrigger,
} from '../integrations/split/squadTreatments/fieldTreatments';
import { getTeamApiIncludeFields, TeamApiKind } from './services/teamApiIncludeFields';
import { getCurrentWorkspaceMembershipCount } from './getCurrentWorkspaceMembershipCount';
import { TimeTrackingDefaultToBillable } from '../clickapps/types/time-tracking.type';
import { TimeTrackingDefaultToBillableWorkspaceClickapp } from '../clickapps/definitions/time-tracking-default-to-billable-workspace.clickapp';
import { TaskDurationWorkspaceClickapp } from '../clickapps/definitions/task-duration-workspace.clickapp';
import { ChatEnablementWorkspaceClickapp } from '../clickapps/definitions/chat-enablement-workspace.clickapp';
import { NotetakerEnablementWorkspaceClickapp } from '../clickapps/definitions/notetaker-enablement-workspace.clickapp';
import { logLeaveWorkspace } from '../../utils/audit-log/log-user-events';
import { log2faPolicyChange, logAdvancedSettingsUpdated } from '../../utils/audit-log/log-workspace-events';
import { DbPool } from '../../utils/interfaces/TransactionClient';
import { ProjectType } from '../project/interfaces/ProjectType';
import { get2faTeamStatus } from '../../utils/2faTeamSettings';
import { skuTrialService } from '../entitlements/skuTrial/skuTrialService';
import { startTrialV2 } from '../billing/trial/trials';

import {
    upsertAuthenticatedAttachmentsSetting,
    removeAuthenticatedAttachmentsSetting,
} from './datastores/settingsDynamoDatastore';

import { isStagingWorkspaceCreationAllowedForEmail } from '../security/whitelistedStagingDomains';
import { deleteNylasAccountsIfNecessary } from '../../../libs/customer-email/src/common/management';
import { ClickUpTracer } from '../../utils/tracer';
import { PlanPromoProductType } from '../../libs/billing/promoCode/teamPromoCode/types';
import { TeamPromoCodeService } from '../../libs/billing/promoCode/teamPromoCode/teamPromoCodeService';
import { PromoCodeHistoryRepository } from '../../libs/billing/promoCode/promoCodeHistoryRepository';
import { shouldUseSameClientForProjectCreation } from '../integrations/split/squadTreatments/hierarchyTreatments';
import { getPlanPricing } from '../billing/pricing/pricing';
import { deleteWorkspace, attemptHardDeleteWorkspaceMembers } from './delete';
import sdTouchWorkspace from '../sd/api/sdTouchWorkspace';

const logger = getLogger('team');

const tracer = new ClickUpTracer();

const FORM_TYPE = config.get('views.view_types.form');

const profile_creds = awsUtil.awsProfile();
if (profile_creds) {
    aws.config.credentials = profile_creds;
}

aws.config.update({
    region: config.aws.region,
});
const s3 = new aws.S3();

const TeamError = ClickUpError.makeNamedError('team');

const formulaProvider = new FormulaProvider();
const formulaCalculationService = new FormulaCalculationService();

const MAX_WORKSPACES_COUNT = 500;

async function disablePubTemplates(team_id) {
    const params = [team_id];
    const queries = [
        {
            query: `
                UPDATE task_mgmt.projects
                SET public_sharing = FALSE
                WHERE projects.id = ANY
                (
                    SELECT
                        projects.id
                    FROM
                        task_mgmt.teams
                        JOIN task_mgmt.projects ON projects.team = teams.id
                    WHERE
                        teams.id = $1 AND
                        projects.template = TRUE AND
                        projects.public_sharing = TRUE AND
                        projects.deleted IS FALSE
                )`,
            params,
        },
        {
            query: `
                UPDATE task_mgmt.categories
                SET public_sharing = FALSE
                WHERE categories.id = ANY
                (
                    SELECT
                        categories.id
                    FROM
                        task_mgmt.teams
                        JOIN task_mgmt.categories ON categories.team_id = teams.id
                    WHERE
                        teams.id = $1 AND
                        categories.template = TRUE AND
                        categories.public_sharing = TRUE AND
                        categories.deleted IS FALSE
                )
            `,
            params,
        },
        {
            query: `
                UPDATE task_mgmt.subcategories
                SET public_sharing = FALSE
                WHERE subcategories.id = ANY
                (
                    SELECT
                        subcategories.id
                    FROM
                        task_mgmt.teams
                        JOIN task_mgmt.subcategories ON subcategories.team_id = teams.id
                    WHERE
                        teams.id = $1 AND
                        subcategories.template = TRUE AND
                        subcategories.public_sharing = TRUE AND
                        subcategories.deleted IS FALSE
                )
            `,
            params,
        },
        {
            query: `
                UPDATE task_mgmt.items
                SET public_sharing = FALSE
                WHERE items.id = ANY
                (
                    SELECT
                    items.id
                    FROM
                        task_mgmt.teams
                        JOIN task_mgmt.items ON items.team_id = teams.id
                    WHERE
                        teams.id = $1 AND
                        items.template = TRUE AND
                        items.public_sharing = TRUE AND
                        items.deleted IS FALSE
                )
            `,
            params,
        },
    ];

    try {
        await db.promiseBatchQueries(queries);
    } catch (e) {
        throw new TeamError(e);
    }
}

function checkSaastrTrial(req, resp, next) {
    const userid = req.decoded_token.user;

    if (!req.body) {
        req.body = {};
    }

    db.globalReadQuery(
        'SELECT source, saastr_trial FROM task_mgmt.users WHERE id = $1',
        [userid],
        { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_11 },
        (err, result) => {
            if (err) {
                resp.status(500).send({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'TEAM_',
                });
                logger.error({
                    msg: 'Failed to create team checking saastr properties',
                    status: 500,
                    ECODE: '',
                    err,
                });
            } else if (result.rows.length > 0) {
                const user = result.rows[0];
                if ((user.source === 'saastr' || user.source === 'GDC') && user.saastr_trial === true) {
                    req.body.plan = 2;
                    req.body.add_six_months = true;
                } else if (user.source === 'saastr_2020' && user.saastr_trial === true) {
                    req.body.saastr_2020_credit = true;
                } else {
                    req.body.add_six_months = false;
                }
                next();
            } else {
                next();
            }
        }
    );
}

export { checkSaastrTrial };

function checkIfShouldSendWelcomeEmail(userid) {
    db.globalReadQuery(
        'SELECT email, sent_welcome_email FROM task_mgmt.users WHERE id = $1',
        [userid],
        { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_11 },
        (err, result) => {
            if (err) {
                logger.error({
                    msg: 'Failed to select user to check sent_welcome_email',
                    err,
                    userid,
                });
            } else if (
                result.rows.length > 0 &&
                result.rows[0].sent_welcome_email != null &&
                result.rows[0].sent_welcome_email === false
            ) {
                email_helper.sendNewOwnerEmail(userid, result.rows[0].email);
            }
        }
    );
}

const _createTeam = tracer.wrap('team._createTeam', {}, __createTeam);
function __createTeam(userid, options, cb) {
    let avatar_key = null;

    const {
        name,
        usersToAdd = [],
        avatar,
        plan_id,
        default_payment_token,
        promo_code,
        color,
        slack_channel,
        signed_attachments,
        using_github,
        using_gitlab,
        cycles,
        color_theme,
        enable_codox,
        dashboards_enabled,
        add_six_months,
        projects = [],
        onboarding_v2,
        personal_team,
        should_encrypt,
        stored_promo_code,
        address,
        number_of_team_users,
        can_add_guests,
        can_remove_guests,
        time_tracking_rollup,
        time_estimate_rollup,
        saastr_2020_credit,
        onetool,
        reseller_id,
        lineup,
        threaded_comments,
        platform,
        verbose_response,
        is_optimized,
        ga4_attributes,
        build_version,
        segmentTrackingAttributes,
        time_tracking_default_to_billable,
        task_duration,
        chat_enabled,
        chat_context,
    } = options;
    let { time_tracking_display_hours, time_estimate_display_hours } = options;

    if (time_tracking_display_hours == null) {
        time_tracking_display_hours = true;
    }
    if (time_estimate_display_hours == null) {
        time_estimate_display_hours = true;
    }

    const v2_beta_start = new Date().getTime();

    if (!input.validateTeamName(name)) {
        cb({ err: 'Team name invalid', status: 400, ECODE: 'TEAM_063' });
        return;
    }

    if (avatar) {
        let extension = avatar.originalname.split('.');
        extension = extension[extension.length - 1].toLowerCase();
        avatar_key = `team_avatars/${uuid.v4()}.${extension}`;
    }

    const emails = [];
    const userids = [];
    const emailLeadMap = {};

    let throw_cant_add_owner_error = false;
    for (let i = usersToAdd.length - 1; i >= 0; i -= 1) {
        const userToAdd = usersToAdd[i];
        if (!userToAdd.email) {
            usersToAdd.splice(i, 1);
            continue;
        }
        userToAdd.role = parseInt(userToAdd.role, 10);
        if (userToAdd.role < 1 || userToAdd.role > 3) {
            userToAdd.role = 3;
        }

        if (userToAdd.userid) {
            userToAdd.id = userToAdd.userid;
        }
        if (userToAdd.role === config.team_roles.owner && (userToAdd.id !== userid || userToAdd.userid !== userid)) {
            throw_cant_add_owner_error = true;
        }
        if (userToAdd.id) {
            userids.push(userToAdd.id);
        }
        if (userToAdd.email) {
            userToAdd.email = userToAdd.email.toLowerCase();
        }
        emails.push(userToAdd.email);
    }

    userids.push(userid);

    logger.info({
        msg: 'Starting create team',
        plan_id,
        userid,
    });

    if (throw_cant_add_owner_error) {
        cb({
            err: "Can't add another user as the owner of this team",
            status: 400,
            ECODE: 'TEAM_051',
        });
        return;
    }

    async.parallel(
        {
            async getPlan(para_cb) {
                try {
                    const freeForeverPlan = await planService.getDefaultFreeForeverPlanId();
                    const queryPlanId = plan_id || freeForeverPlan;
                    const result = await db.readQueryAsync(`SELECT * FROM task_mgmt.task_plans WHERE id = $1`, [
                        queryPlanId,
                    ]);

                    if (!result?.rows?.length) {
                        logger.error({
                            msg: 'Plan doesnt exist',
                            status: 404,
                            query_plan_id: queryPlanId,
                            plan_id,
                            ECODE: 'TEAM_031',
                        });
                        para_cb({
                            err: 'Plan doesnt exist',
                            status: 404,
                            ECODE: 'TEAM_031',
                        });
                        return;
                    }

                    para_cb(null, {
                        newPlan: result.rows[0],
                        freeForeverPlan,
                    });
                } catch (err) {
                    para_cb(err);
                }
            },
            checkPromoCodeValid(para_cb) {
                if (!promo_code) {
                    para_cb();
                    return;
                }
                // None product type passed intentionally as team is not created yet
                promoCodeMod._checkPromoCodeValid(userid, null, promo_code, undefined, {}, para_cb);
            },
            getEmailToUser(para_cb) {
                userids.push(userid); // add the creating user so we can get their email address

                const query =
                    'SELECT id, LOWER(email) as email FROM task_mgmt.users WHERE deleted = false AND (LOWER(email) = ANY($1) OR id = ANY($2))';
                const params = [emails, userids];

                db.globalReadQuery(query, params, para_cb);
            },
            createdTeamBefore(para_cb) {
                db.globalReadQuery(
                    'SELECT email, set_previous_tools FROM task_mgmt.users WHERE id = $1',
                    [userid],
                    para_cb
                );
            },
            getWorkspacesOwnedByTheUser(para_cb) {
                db.globalReadQuery(
                    `SELECT id FROM task_mgmt.teams_global teams WHERE owner = $1 and deleted is not TRUE`,
                    [userid],
                    para_cb
                );
            },
            async checkCreatorWorkspaceLimit(para_cb) {
                if (!shouldCheckWorkspaceCreationCap(userid)) {
                    para_cb();
                    return;
                }

                try {
                    const wsResult = await getCurrentWorkspaceMembershipCount(userid);

                    if (wsResult >= MAX_WORKSPACES_COUNT) {
                        para_cb(
                            new TeamError('You have reached the maximum number of workspaces.', 'TEAM_231', 400, {
                                userid,
                            })
                        );
                        return;
                    }
                    para_cb();
                } catch (err) {
                    para_cb(err);
                }
            },
        },
        async (err, para_result) => {
            if (err) {
                if (err.err) {
                    cb(err);
                    return;
                }
                logger.error({
                    msg: 'Failed to get plan',
                    err,
                    status: 500,
                    ECODE: 'TEAM_030',
                });
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'TEAM_030',
                });
                return;
            }

            let next_bill_date;
            const { newPlan, freeForeverPlan } = para_result.getPlan;
            const promo_code_row = para_result.checkPromoCodeValid;
            const service_status =
                options.service_status == null ? config.service_status.active : options.service_status;
            let set_previous_tools = false;
            let ownerEmail = '';
            const now = new Date().getTime();
            const reset_date = `${moment().utc().year()}_${moment().utc().month()}`;
            if (add_six_months) {
                newPlan.trial = 30 * 6;
            }

            if (newPlan.trial > 0) {
                next_bill_date = new Date().setDate(new Date().getDate() + newPlan.trial);
            } else {
                next_bill_date = calculateNextBillDate(cycles);
            }

            const createdTeamBeforeRow = para_result.createdTeamBefore.rows[0];

            if (createdTeamBeforeRow) {
                set_previous_tools = createdTeamBeforeRow.set_previous_tools;
                ownerEmail = createdTeamBeforeRow.email;
            }

            // Restrict workspace-creation if owner belongs to the "corporate-owned-email-domain",
            // TODO: Make it driven by "permission"!
            if (ownerEmail) {
                const email_domain_ownership = await isEmailDomainBlockedOrOwned({ email: ownerEmail });

                if (email_domain_ownership?.is_domain_owned) {
                    // (Note!) when ownership is paused, the ws_id would not be in the list domain_owner_ids
                    // and user might be blocked from creation of new workspaces!

                    // when email-domain is owned by multiple workspaces,
                    // we check if user is an owner of any of them
                    const is_user_owns_email_domain_owning_workspace = email_domain_ownership?.domain_owner_ids?.some(
                        owsid => {
                            if (
                                shouldRestrictWorkspaceCreationForCorporateUsers(owsid) &&
                                para_result.getWorkspacesOwnedByTheUser.rows?.some(r => Number(r.id) === owsid)
                            ) {
                                return true;
                            }
                            return false;
                        }
                    );

                    // we should allow new workspace-creation to the owner of corporate workspace
                    // (that workspace, which owns email-domain)
                    // and should prohibit workspace creation to other corporate users
                    if (!is_user_owns_email_domain_owning_workspace) {
                        cb(
                            new TeamError('Workspace creation restricted by organization policy.', 'TEAM_207', 403, {
                                ownerEmail,
                                userid,
                                log_tags: ['security', 'owned-domain'],
                                email_domain_ownership,
                            })
                        );
                        return;
                    }
                }
            }

            // Restrict staging workspace-creation if owner email is not whitelisted
            if (ownerEmail && !personal_team) {
                const emailAllowed = await isStagingWorkspaceCreationAllowedForEmail(ownerEmail);
                if (!emailAllowed) {
                    cb(
                        new TeamError('Workspace creation is not allowed.', 'TEAM_230', 403, {
                            ownerEmail,
                            userid,
                        })
                    );
                    return;
                }
            }

            const emailToUserid = {};
            const useridToEmail = {};
            let ownerInUsersToAdd = false;
            para_result.getEmailToUser.rows.forEach(row => {
                emailToUserid[row.email] = row.id;
                useridToEmail[row.id] = row.email;
            });

            usersToAdd.forEach(userToAdd => {
                if (userToAdd.userid) {
                    userToAdd.id = userToAdd.userid;
                }
                if (!userToAdd.email) {
                    userToAdd.email = useridToEmail[userToAdd.id];
                }
                if (userToAdd.id === userid) {
                    ownerInUsersToAdd = true;
                }
                if (!userToAdd.id) {
                    userToAdd.id = emailToUserid[userToAdd.email];
                }
            });

            let billed_users_this_cycle = usersToAdd.length;
            if (!ownerInUsersToAdd) {
                billed_users_this_cycle += 1;
            }

            let credit = 0;
            if (saastr_2020_credit) {
                credit = 100;
            }

            let query = `
                    UPDATE task_mgmt.teams
                    SET name                         = $1,
                        owner                        = $2,
                        date_created                 = $3,
                        avatar_key                   = $4,
                        plan_id                      = $5,
                        plan_updated                 = $6,
                        default_payment_token        = $7,
                        next_bill_date               = $8,
                        service_status               = $9,
                        billed_users_this_cycle      = $10,
                        color                        = $11,
                        slack_channel                = $12,
                        signed_attachments           = $13,
                        using_github                 = $14,
                        using_gitlab                 = $15,
                        setup_step                   = $16,
                        cycles                       = $17,
                        color_theme                  = $18,
                        personal_team                = $19,
                        should_encrypt               = $20,
                        stored_promo_code            = $21,
                        address                      = $22,
                        number_of_team_users         = $23,
                        view_limit_override          = false,
                        v2_beta                      = $24,
                        v2_beta_start                = $25,
                        time_tracking_display_hours  = $26,
                        time_estimate_display_hours  = $27,
                        enable_codox                 = $28,
                        dashboards_enabled           = $29,
                        can_add_guests               = $30,
                        can_remove_guests            = $31,
                        credit                       = $32,
                        onetool                      = $33,
                        time_tracking_rollup         = $34,
                        time_estimate_rollup         = $35,
                        reseller                     = $36,
                        lineup                       = $37,
                        threaded_comments = $38,
                        automation_count_reset_on = $39,
                        owner_control_private_spaces = false,
                        quick_create_statuses        = true,
                        orderindexes_fixed           = true,
                        sales_type                   = 'self serve',
                        microsoft_365_preview        = true,
                        charge_for_internal_guests   = $40,
                        ai_enabled                   = $41
                    WHERE id = $42
                    RETURNING id`;
            let params = [
                name,
                userid,
                now,
                avatar_key,
                freeForeverPlan,
                now,
                default_payment_token,
                next_bill_date,
                service_status,
                billed_users_this_cycle,
                color,
                slack_channel,
                signed_attachments,
                using_github,
                using_gitlab,
                'project',
                cycles,
                color_theme,
                personal_team,
                should_encrypt,
                stored_promo_code,
                address,
                number_of_team_users,
                true,
                v2_beta_start,
                time_tracking_display_hours,
                time_estimate_display_hours,
                enable_codox == null ? true : enable_codox,
                dashboards_enabled == null ? true : dashboards_enabled,
                can_add_guests,
                can_remove_guests,
                credit,
                onetool,
                time_tracking_rollup,
                time_estimate_rollup,
                reseller_id,
                lineup,
                threaded_comments,
                reset_date,
                true,
                true,
            ];

            // This call sets BDR group and microshard affinity for subsequent DB calls, so we want to make sure it
            // happens before we start the transaction which writes to `teams`
            createWorkspaceIdService
                .create({ userId: userid })
                .then(async ({ workspaceId: new_id }) => {
                    logger.info({
                        msg: 'Begin create team transaction',
                        new_id,
                        userid,
                    });

                    params.push(new_id);

                    db.getCopyConn(cb, { label: '_createTeam' }, (connErr, client, done) => {
                        if (!client.versionUpdates) {
                            client.versionUpdates = [];
                        }

                        client.query('BEGIN', beginErr => {
                            if (beginErr) {
                                cb({
                                    err: 'Internal server error',
                                    status: 500,
                                    ECODE: 'TEAM_001',
                                });
                                logger.error({
                                    msg: 'Failed begin',
                                    err: beginErr,
                                    status: 500,
                                    ECODE: 'TEAM_001',
                                });
                                db.rollback(client, done);
                                return;
                            }

                            client.query(query, params, async (createErr, result) => {
                                if (createErr) {
                                    db.rollback(client, done);
                                    logger.error({
                                        msg: 'Failed to insert into teams',
                                        err: createErr,
                                        status: 500,
                                        ECODE: 'TEAM_002',
                                    });
                                    cb({
                                        err: 'Internal server error',
                                        status: 500,
                                        ECODE: 'TEAM_002',
                                    });
                                } else if (!result.rows.length) {
                                    db.rollback(client, done);
                                    logger.error({
                                        msg: 'Failed to insert into teams, no row returned from UPDATE statement',
                                        new_id,
                                        has_promo_code: !!promo_code, // so we know what UPDATE query was used
                                        status: 500,
                                        ECODE: 'TEAM_206',
                                    });
                                    cb({
                                        err: 'Internal server error',
                                        status: 500,
                                        ECODE: 'TEAM_206',
                                    });
                                } else {
                                    const emailAndInvite = [];
                                    const team_id = result.rows[0].id;
                                    client.versionUpdates.push(
                                        hierarchyObjectVersionUpdateRequests({
                                            objectId: team_id,
                                            objectType: ObjectType.WORKSPACE,
                                            workspaceId: team_id,
                                            operation: OperationType.CREATE,
                                            fanout: false,
                                        })
                                    );
                                    let invite_sent = false;
                                    const userAcl = {};

                                    if (onboarding_v2) {
                                        invite_sent = true;
                                    }

                                    try {
                                        await db2.writeAsync(
                                            'UPDATE task_mgmt.users SET saastr_trial = false WHERE id = $1',
                                            [userid],
                                            undefined,
                                            undefined,
                                            {
                                                poolType: DbPool.GlobalWrite,
                                            }
                                        );
                                    } catch (e) {
                                        logger.error({
                                            msg: 'Failed to update user',
                                            err: e,
                                            status: 500,
                                            ECODE: 'TEAM_059',
                                        });
                                        db.rollback(client, done);
                                        cb({
                                            err: 'Internal server error',
                                            status: 500,
                                            ECODE: 'TEAM_059',
                                        });
                                        return;
                                    }

                                    /**
                                     * @type {Array<{query: string, params: Array<any>}>}
                                     */
                                    const queries = [];

                                    const workspaceSettingsParams = {
                                        adminV3: AdminV3Setting.Enabled,
                                        adminV2: AdminV2Setting.Disabled,
                                    };

                                    if (shouldActivateAuthenticatedAttachments(team_id)) {
                                        workspaceSettingsParams.authenticatedAttachments =
                                            !!shouldDefaultNewWorkspacesToAuthAttachmentOn(team_id);

                                        // IF we are defaulting auth attachments on, write to dynamo.  Otherwise bypass this step.
                                        if (workspaceSettingsParams.authenticatedAttachments) {
                                            await upsertAuthenticatedAttachmentsSetting(team_id);
                                        }
                                    }

                                    queries.push({
                                        query: 'INSERT INTO task_mgmt.workspace_settings (workspace_id, data) VALUES ($1, $2)',
                                        params: [team_id, workspaceSettingsParams],
                                    });

                                    const tbiQuery = buildUpsertTeamBillingInfoQuery(team_id, {
                                        billed_plan_id: freeForeverPlan,
                                        next_bill_date,
                                        billed_users_this_cycle,
                                    });
                                    queries.push(tbiQuery);

                                    if (
                                        time_tracking_default_to_billable != null &&
                                        [
                                            TimeTrackingDefaultToBillable.True,
                                            TimeTrackingDefaultToBillable.False,
                                        ].includes(time_tracking_default_to_billable)
                                    ) {
                                        queries.push(
                                            TimeTrackingDefaultToBillableWorkspaceClickapp.getUpsertQuery(
                                                team_id,
                                                { time_tracking_default_to_billable },
                                                team_id
                                            )
                                        );
                                    }

                                    if (task_duration != null) {
                                        queries.push(
                                            TaskDurationWorkspaceClickapp.getUpsertQuery(
                                                team_id,
                                                { task_duration },
                                                team_id
                                            )
                                        );
                                    }

                                    if (chat_enabled != null && shouldEnableChat(team_id)) {
                                        const appPayload = {
                                            chat_enabled: !!(
                                                (typeof chat_enabled === 'string' &&
                                                    chat_enabled.toLowerCase() === 'true') ||
                                                chat_enabled === true
                                            ),
                                        };
                                        if (chat_context?.source) {
                                            appPayload.chat_context = { source: chat_context.source };
                                        }

                                        queries.push(
                                            ChatEnablementWorkspaceClickapp.getUpsertQuery(team_id, appPayload, team_id)
                                        );
                                    }

                                    // Can remove this once we remove the can_add_team_members = true override for members
                                    // in the workspace-role-permission-overrides split flag
                                    queries.push(getUpsertInviteMemberOnForMemberRoleQuery(team_id));

                                    if (promo_code) {
                                        queries.push(
                                            TeamPromoCodeService.getUpsertPromoCodeQuery(
                                                team_id,
                                                promo_code,
                                                1,
                                                PlanPromoProductType
                                            )
                                        );
                                    }

                                    async.waterfall(
                                        [
                                            wf_cb => {
                                                const planName = PlanNames[newPlan.id];
                                                const workspaceDomain = ownerEmail.split('@')[1];

                                                // workspace owner tracking for Segment
                                                trackEvent(SegmentEvent.CreateUser, userid, {
                                                    username: ownerEmail,
                                                    userRole: 'owner',
                                                    userRoleId: 1,
                                                    planCode: newPlan.id,
                                                    planName,
                                                    workspaceDomain,
                                                    workspaceID: team_id,
                                                    workspaceName: name,
                                                    newUserProps: {
                                                        userId: userid,
                                                        email: ownerEmail,
                                                        via: 'workspace creation',
                                                    },
                                                });

                                                query =
                                                    'INSERT INTO task_mgmt.team_members(team_id, userid, invite, role, date_invited, date_joined, invite_code, invited_by, invite_sent, unseen_notifs) VALUES ';
                                                params = [];
                                                async.each(
                                                    usersToAdd,
                                                    (userToAdd, each_cb) => {
                                                        // todo: check based on user attribute rather than userid negative
                                                        if (userToAdd.id === userid || userToAdd.id < 0) {
                                                            // If userid matches, this is owner, continuing
                                                            // If userid is negative, this is a bot user, skipping
                                                            each_cb();
                                                            return;
                                                        }

                                                        const invite_code = generateEmailCode(userToAdd.email);
                                                        if (userToAdd.id) {
                                                            const changes = {
                                                                invite: true,
                                                                role: userToAdd.role || config.team_roles.user,
                                                                date_invited: now,
                                                                date_joined: null,
                                                                invite_code,
                                                                invited_by: userid,
                                                                invite_sent,
                                                            };

                                                            if (params.length !== 0) {
                                                                query += ', ';
                                                            }
                                                            query += `($${params.length + 1}, $${params.length + 2}, $${
                                                                params.length + 3
                                                            }, $${params.length + 4}, $${params.length + 5}, $${
                                                                params.length + 6
                                                            }, $${params.length + 7}, $${params.length + 8}, $${
                                                                params.length + 9
                                                            }, 0)`;
                                                            params.push(
                                                                team_id,
                                                                emailToUserid[userToAdd.email],
                                                                changes.invite,
                                                                changes.role,
                                                                changes.date_invited,
                                                                changes.date_joined,
                                                                changes.invite_code,
                                                                changes.invited_by,
                                                                changes.invite_sent
                                                            );

                                                            emailAndInvite.push({
                                                                email: userToAdd.email,
                                                                invite_code,
                                                                userid: userToAdd.id,
                                                                planid: newPlan.id,
                                                                via: 'workspace creation',
                                                            });

                                                            updateRequestForWorkspaceMembers({
                                                                workspaceId: Number(team_id),
                                                                users: new Map([[userToAdd.id, { changes }]]),
                                                                operationType: OperationType.CREATE,
                                                                addWorkspaceRecord: true,
                                                                updateGlobalUser: true,
                                                                results: client.versionUpdates,
                                                            });
                                                            userAcl[userToAdd.id] = changes.role;
                                                            emailLeadMap[userToAdd.email] = Number(userToAdd.id);
                                                            each_cb();
                                                        } else {
                                                            // user doesnt exist, creating them and inviting them
                                                            const domain_status = blockedDomains.checkSignupDomain(
                                                                userToAdd.email
                                                            );
                                                            if (
                                                                domain_status !==
                                                                blockedDomains.DomainStatus.VALID_DOMAIN
                                                            ) {
                                                                blockedDomains.signupDomainErrorCallback(
                                                                    domain_status,
                                                                    each_cb
                                                                );
                                                                return;
                                                            }

                                                            createNewUserId(
                                                                userToAdd.email,
                                                                ['team.js:_createTeam:adding-members'],
                                                                { global: true },
                                                                async (sequenceErr, new_userid) => {
                                                                    if (sequenceErr) {
                                                                        each_cb(sequenceErr);
                                                                        return;
                                                                    }

                                                                    try {
                                                                        await db2.writeAsync(
                                                                            `UPDATE task_mgmt.users
                                                                        SET joined = false,
                                                                            email = $2,
                                                                            deleted = false
                                                                        WHERE id = $1`,
                                                                            [new_userid, userToAdd.email],
                                                                            updateRequestForUser({
                                                                                user: new_userid,
                                                                                operationType: OperationType.CREATE,
                                                                            }),
                                                                            undefined,
                                                                            {
                                                                                poolType: DbPool.GlobalWrite,
                                                                            }
                                                                        );
                                                                    } catch (userUpdateErr) {
                                                                        each_cb(userUpdateErr);
                                                                        return;
                                                                    }
                                                                    const userRole =
                                                                        userToAdd.role || config.team_roles.user;
                                                                    queries.push({
                                                                        query: 'INSERT INTO task_mgmt.team_members(team_id, userid, invite, role, date_invited, invite_code, invited_by, invite_sent, unseen_notifs, receive_mobile_notifs_when_active) VALUES ($1, $6, $2, $3, $4, $5, $7, $8, 0, $9)',
                                                                        params: [
                                                                            team_id,
                                                                            true,
                                                                            userRole,
                                                                            now,
                                                                            invite_code,
                                                                            new_userid,
                                                                            userid,
                                                                            invite_sent,
                                                                            false,
                                                                        ],
                                                                    });

                                                                    emailAndInvite.push({
                                                                        email: userToAdd.email,
                                                                        invite_code,
                                                                        userid: userToAdd.id,
                                                                        planid: newPlan.id,
                                                                        via: 'workspace creation',
                                                                    });
                                                                    userAcl[new_userid] = userRole;
                                                                    emailLeadMap[userToAdd.email] = Number(new_userid);
                                                                    each_cb();
                                                                }
                                                            );
                                                        }
                                                    },
                                                    eachErr => {
                                                        // add owner
                                                        if (query[query.length - 1] === ')') {
                                                            query += ', ';
                                                        }
                                                        query += `($${params.length + 1}, $${params.length + 2}, $${
                                                            params.length + 3
                                                        }, $${params.length + 4}, $${params.length + 5}, $${
                                                            params.length + 6
                                                        }, $${params.length + 7}, $${params.length + 8}, true, 0)`;
                                                        params.push(
                                                            team_id,
                                                            userid,
                                                            false,
                                                            config.team_roles.owner,
                                                            now,
                                                            now,
                                                            null,
                                                            null
                                                        );
                                                        queries.push({
                                                            query,
                                                            params,
                                                        });

                                                        userAcl[userid] = config.team_roles.owner;

                                                        wf_cb(eachErr);
                                                    }
                                                );
                                            },
                                            wf_cb => {
                                                async.each(
                                                    queries,
                                                    (eachQuery, each_cb) => {
                                                        client.query(eachQuery.query, eachQuery.params, each_cb);
                                                    },
                                                    eachErr => {
                                                        if (eachErr) {
                                                            wf_cb(eachErr);
                                                        } else {
                                                            wf_cb();
                                                        }
                                                    }
                                                );
                                            },

                                            wf_cb => {
                                                if (!avatar) {
                                                    wf_cb();
                                                    return;
                                                }
                                                thumb.createProfilePicture(avatar.path, thumbErr => {
                                                    if (thumbErr) {
                                                        wf_cb(thumbErr);
                                                        return;
                                                    }

                                                    const s3params = {
                                                        Bucket: config.aws.bucket,
                                                        Key: avatar_key,
                                                        Body: fs.createReadStream(avatar.path),
                                                    };

                                                    s3.upload(s3params, s3Err => {
                                                        fs.unlink(avatar.path, () => {});
                                                        if (s3Err) {
                                                            logger.error({
                                                                msg: 'Failed to upload avatar picture',
                                                                err: s3Err,
                                                                status: 500,
                                                                ECODE: 'TEAM_028',
                                                            });
                                                            wf_cb({
                                                                err: 'Internal server error',
                                                                status: 500,
                                                                ECODE: 'TEAM_028',
                                                            });
                                                        } else {
                                                            wf_cb();
                                                        }
                                                    });
                                                });
                                            },
                                            wf_cb => {
                                                async.eachSeries(
                                                    [...projects],
                                                    (_project, each_cb) => {
                                                        _project.skip_access = true;
                                                        _project.ignore_paywall = true;
                                                        _project.create_team = true;
                                                        _project.private = false;
                                                        _project.team = team_id;
                                                        _project.should_encrypt = should_encrypt;
                                                        if (shouldUseSameClientForProjectCreation(team_id)) {
                                                            _project.client = client;
                                                        }

                                                        if (onboarding_v2) {
                                                            if (!_project.statuses) {
                                                                _project.statuses = config.default_statuses;
                                                            }
                                                            _project.categories = [];

                                                            if (!_project.features) {
                                                                _project.features = {};
                                                            }

                                                            if (
                                                                _project.features &&
                                                                _project.features.time &&
                                                                _project.features.time.enabled
                                                            ) {
                                                                _project.features.time_tracking = {
                                                                    enabled: true,
                                                                };
                                                            }

                                                            _project.features.check_unresolved = {
                                                                enabled: true,
                                                                subtasks: true,
                                                            };

                                                            const features_tasks = [];
                                                            if (
                                                                _project.features &&
                                                                _project.features.priorities &&
                                                                _project.features.priorities.enabled
                                                            ) {
                                                                features_tasks.push('prioritiesTask');
                                                            }
                                                            if (
                                                                _project.features &&
                                                                _project.features.time_tracking &&
                                                                _project.features.time_tracking.enabled
                                                            ) {
                                                                features_tasks.push('timeTrackingTasks');
                                                            }
                                                            if (
                                                                _project.features &&
                                                                _project.features.due_dates &&
                                                                _project.features.due_dates.enabled
                                                            ) {
                                                                features_tasks.push('dueDateTasks');
                                                            }
                                                            if (
                                                                _project.features &&
                                                                _project.features.tags &&
                                                                _project.features.tags.enabled
                                                            ) {
                                                                features_tasks.push('tagsTasks');
                                                            }

                                                            features_tasks.push('standardTasks');
                                                        }
                                                        project._createProject(userid, _project, each_cb);
                                                    },
                                                    projectErr => {
                                                        wf_cb(projectErr);
                                                    }
                                                );
                                            },
                                            wf_cb => {
                                                if (!promo_code) {
                                                    wf_cb(null, {});
                                                    return;
                                                }

                                                const promoCodeHistoryQuery =
                                                    PromoCodeHistoryRepository.getInsertPromoCodeHistoryQuery(
                                                        promo_code,
                                                        team_id,
                                                        now,
                                                        cycles,
                                                        0,
                                                        null,
                                                        PlanPromoProductType
                                                    );
                                                client.query(
                                                    promoCodeHistoryQuery.query,
                                                    promoCodeHistoryQuery.params,
                                                    wf_cb
                                                );
                                            },
                                            (result1, wf_cb) => {
                                                if (!saastr_2020_credit) {
                                                    wf_cb(null, {});
                                                    return;
                                                }

                                                client.query(
                                                    `INSERT INTO task_mgmt.team_edit_audit (userid, team_id, body, date, id) VALUES ($1, $2, $3, $4, $5)`,
                                                    [
                                                        null,
                                                        team_id,
                                                        {
                                                            bonus_type: 'credit',
                                                            source: 'Saastr 2020',
                                                            credit: 100,
                                                        },
                                                        new Date().getTime(),
                                                        uuid.v4(),
                                                    ],
                                                    wf_cb
                                                );
                                            },
                                            (result1, wf_cb) => {
                                                async.eachSeries(
                                                    config.preset_status_list,
                                                    (statusTemplate, each_cb) => {
                                                        statusTemplate.client = client;
                                                        statusTemplate.team_id = team_id;
                                                        statusTemplateMod._createTemplate(
                                                            userid,
                                                            statusTemplate,
                                                            each_cb
                                                        );
                                                    },
                                                    presetStatusTemplateErr => {
                                                        wf_cb(presetStatusTemplateErr);
                                                    }
                                                );
                                            },
                                            function addObjectAclEntry(wf_cb) {
                                                const egressService = getEgressAccessInfoService();
                                                const egressQuery = egressService.getUpsertObjectAclsQuery([
                                                    {
                                                        object_id: team_id,
                                                        object_type: ObjectType.WORKSPACE,
                                                        workspace_id: team_id,
                                                        object_id_int: Number(team_id),
                                                        user_acl: userAcl,
                                                        group_acl: {},
                                                        private: false,
                                                        archived: false,
                                                        deleted: false,
                                                        hierarchy_scopes: [
                                                            {
                                                                object_id: team_id,
                                                                object_type: ObjectType.WORKSPACE,
                                                            },
                                                        ],
                                                        version_vector: {},
                                                    },
                                                ]);
                                                client.query(egressQuery.query, egressQuery.params, wf_cb);
                                            },
                                        ],
                                        async wf_err => {
                                            if (wf_err) {
                                                db.rollback(client, done);
                                                if (!wf_err.ECODE) {
                                                    logger.error({
                                                        msg: 'Failed to insert users into teams',
                                                        err: wf_err,
                                                        status: 500,
                                                        ECODE: 'TEAM_003',
                                                    });
                                                    cb({
                                                        err: 'Failed to insert users into team',
                                                        status: 500,
                                                        ECODE: 'TEAM_003',
                                                    });
                                                } else {
                                                    logger.error(wf_err);
                                                    cb(wf_err);
                                                }
                                            } else {
                                                let events;
                                                const ovm = getObjectVersionManager();
                                                updateRequestForWorkspaceMembers({
                                                    workspaceId: Number(team_id),
                                                    users: [userid],
                                                    addWorkspaceRecord: true,
                                                    operationType: OperationType.CREATE,
                                                    results: client.versionUpdates,
                                                });

                                                try {
                                                    const txClient = new TransactionClientImpl(client, ovm);
                                                    events = await ovm.updateVersions(txClient, client.versionUpdates);
                                                } catch (ovmErr) {
                                                    logger.error({
                                                        msg: 'Failed to write OVM update',
                                                        OVM_CRITICAL: true,
                                                        ECODE: 'OVM_WS_130',
                                                        err: ovmErr,
                                                    });
                                                    db.rollback(client, done);
                                                    cb(ovmErr);
                                                    return;
                                                }

                                                client.query('COMMIT', async commitErr => {
                                                    if (commitErr) {
                                                        db.rollback(client, done);
                                                        logger.error({
                                                            msg: 'Failed to commit',
                                                            err: commitErr,
                                                            status: 500,
                                                            ECODE: 'TEAM_004',
                                                        });
                                                        cb({
                                                            err: 'Failed to create team',
                                                            status: 500,
                                                            ECODE: 'TEAM_004',
                                                        });
                                                    } else {
                                                        done();

                                                        await ovm.notifyChanges(events).catch(() => {});

                                                        const ret_val = {
                                                            id: team_id,
                                                            personal_team,
                                                            users_invited: usersToAdd,
                                                            set_previous_tools: set_previous_tools || false,
                                                        };
                                                        if (avatar_key) {
                                                            ret_val.avatar = cf_sign.getLongCloudfrontSignedURL(
                                                                avatar_key,
                                                                {
                                                                    skip_signed_auth: environment.isProdEU,
                                                                }
                                                            );
                                                        }

                                                        if (onboarding_v2) {
                                                            teamMemberMod.emailInvites(
                                                                userid,
                                                                team_id,
                                                                name,
                                                                useridToEmail[userid], // the workspace creators email address
                                                                ret_val.avatar,
                                                                emailAndInvite
                                                            );
                                                        }
                                                        checkIfShouldSendWelcomeEmail(userid);

                                                        notifSettingsMod._setTeamNotificationSettings(userid, {
                                                            team: team_id,
                                                            settings: [
                                                                {
                                                                    field: 'due_date_summary',
                                                                    time_of_day_hour: 7,
                                                                    time_of_day_minute: 0,
                                                                    email_enabled: 1,
                                                                    email_my_tasks: 1,
                                                                },
                                                                {
                                                                    field: 'description_tag',
                                                                    email_enabled: 1,
                                                                    browser_enabled: 1,
                                                                },
                                                            ],
                                                        });

                                                        if (newPlan.trial > 0) {
                                                            const new_trial_options = {
                                                                newPlanId: newPlan.id,
                                                                days: newPlan.trial,
                                                            };

                                                            try {
                                                                await startTrialV2(userid, team_id, new_trial_options);
                                                                logger.info({
                                                                    msg: 'Successfully started trial for created team',
                                                                    userid,
                                                                    team_id,
                                                                });
                                                            } catch (trial_err) {
                                                                logger.error({
                                                                    msg: 'Failed to start trial for newly created team',
                                                                    userid,
                                                                    team_id,
                                                                    err: trial_err,
                                                                });
                                                            }
                                                        }

                                                        const segmentRequesterQuery =
                                                            'WITH user_workspace_count as (select owner, count(*) as workspace_count from task_mgmt.teams where owner = $1 group by owner) SELECT team_members.role, users.email, workspace_count from task_mgmt.team_members LEFT JOIN task_mgmt.users ON users.id = team_members.userid LEFT JOIN user_workspace_count ON user_workspace_count.owner = users.id WHERE id = $1';

                                                        db.readQuery(
                                                            segmentRequesterQuery,
                                                            [userid],
                                                            (segmentQueryError, data) => {
                                                                if (segmentQueryError) {
                                                                    logger.error(segmentQueryError);
                                                                    return;
                                                                }
                                                                if (!data?.rows?.[0]) {
                                                                    logger.error({
                                                                        msg: 'Segment Eventing skipped due to race condition found in _createTeam, inspect users flow to debug',
                                                                        userid,
                                                                        team_id,
                                                                        personal_team,
                                                                    });
                                                                    return;
                                                                }

                                                                const requester = data.rows[0];
                                                                if (!requester.role) {
                                                                    logger.error({
                                                                        msg: 'Segment Eventing flow skipped due to missing role in check query, inspect users flow to debug',
                                                                        userid,
                                                                        team_id,
                                                                        requester,
                                                                        personal_team,
                                                                    });
                                                                    return;
                                                                }
                                                                const userRole = config.get(
                                                                    `roles.${requester.role}.name`
                                                                );
                                                                const planName = PlanNames[newPlan.id];
                                                                const workspaceDomain = requester.email.split('@')[1];

                                                                trackEvent(SegmentEvent.CreateWorkspace, userid, {
                                                                    username: requester.email,
                                                                    userRole,
                                                                    userRoleId: requester.role,
                                                                    planCode: newPlan.id,
                                                                    planName,
                                                                    workspaceDomain,
                                                                    workspaceID: team_id,
                                                                    workspaceName: name,
                                                                });

                                                                // adding some level of safety with the ternary
                                                                // in case we are missing a case where an insert fails
                                                                // and this goes negative
                                                                const totalWorkspaceOwnershipCount =
                                                                    requester?.workspace_count || 0;
                                                                const currentWorkspaceOwnershipCount =
                                                                    totalWorkspaceOwnershipCount - 1 <= 0
                                                                        ? 0
                                                                        : totalWorkspaceOwnershipCount - 1;
                                                                const currentWorkspaceMembershipCount =
                                                                    data?.rows?.length || 0;

                                                                let tempGA4Attributes = ga4_attributes ?? {};
                                                                if (typeof tempGA4Attributes === 'string') {
                                                                    try {
                                                                        tempGA4Attributes =
                                                                            JSON.parse(tempGA4Attributes);
                                                                    } catch (e) {
                                                                        tempGA4Attributes = {
                                                                            sessionId: 'json-parse-error',
                                                                        };
                                                                    }
                                                                }

                                                                let tempSegmentTrackingAttributes =
                                                                    segmentTrackingAttributes ?? {};
                                                                if (typeof tempSegmentTrackingAttributes === 'string') {
                                                                    try {
                                                                        tempSegmentTrackingAttributes =
                                                                            JSON.parse(tempSegmentTrackingAttributes);
                                                                    } catch (e) {
                                                                        tempSegmentTrackingAttributes = {
                                                                            segmentAnonymousId: 'json-parse-error',
                                                                        };
                                                                    }
                                                                }

                                                                logger.info({
                                                                    msg: '[Onboarding] Tracking complete new workspace setup, these are temp and actual',
                                                                    tempGA4Attributes,
                                                                    ga4_attributes,
                                                                    tempSegmentTrackingAttributes,
                                                                    segmentTrackingAttributes,
                                                                });

                                                                if (!is_optimized) {
                                                                    trackEvent(
                                                                        SegmentEvent.CompletedWorkspaceSetup,
                                                                        userid,
                                                                        {
                                                                            currentWorkspaceOwnershipCount,
                                                                            platform,
                                                                            username: requester.email,
                                                                            userRole,
                                                                            userRoleId: requester.role,
                                                                            planCode: newPlan.id,
                                                                            planName,
                                                                            workspaceDomain,
                                                                            workspaceID: team_id,
                                                                            workspaceName: name,
                                                                            build_version,
                                                                            currentWorkspaceMembershipCount,
                                                                            ..._.pick(tempGA4Attributes, [
                                                                                'sessionId',
                                                                                'clientId',
                                                                                'sessionNumber',
                                                                            ]),
                                                                            ..._.pick(tempSegmentTrackingAttributes, [
                                                                                'segmentAnonymousId',
                                                                            ]),
                                                                        }
                                                                    );
                                                                }
                                                            }
                                                        );

                                                        try {
                                                            // TODO: convert to event/signal
                                                            await entitlementService.setEntitlements(plan_id, team_id, [
                                                                EntitlementName.AutomationActions,
                                                                EntitlementName.CustomFieldManualAiUsages,
                                                                EntitlementName.CustomFieldAutomatedAiUsages,
                                                                EntitlementName.PaidAggregatedAutoAi,
                                                                EntitlementName.FreeAggregatedAutoAi,
                                                                EntitlementName.AggregatedAutoAi,
                                                                EntitlementName.TaskManualAiUsages,
                                                                EntitlementName.TaskAutomatedAiUsages,
                                                                EntitlementName.CardsAutomatedAiUsages,
                                                            ]);
                                                        } catch (automationPaywallErr) {
                                                            logger.error({
                                                                msg: 'Failed to set entitlements during team creation',
                                                                team_id,
                                                                err: automationPaywallErr,
                                                            });
                                                        }

                                                        try {
                                                            // Notifies SD Platform that workspace was changed.
                                                            sdTouchWorkspace(team_id).catch(() => {
                                                                /* noop */
                                                            });
                                                        } catch (e) {
                                                            // noop
                                                        }

                                                        if (verbose_response) {
                                                            // In case of a verbose response, we want to return everything
                                                            // _createTeam would return anyway, as well as whatever _getTeam
                                                            //  returns. _getTeam already has everything _createTeam returns,
                                                            // except the `personal_team`, `users_invited`, and `set_previous_tools` attrs.
                                                            if (options.make_post_creation_updates) {
                                                                try {
                                                                    await editTeamAsync(userid, {
                                                                        team_id,
                                                                        extra_comment_reactions: 1,
                                                                        task_relationships: true,
                                                                        tasks_in_multiple_lists: true,
                                                                        threaded_comments: true,
                                                                        use_master: true,
                                                                    });
                                                                } catch (editTeamErr) {
                                                                    cb(editTeamErr);
                                                                }
                                                            }
                                                            try {
                                                                const team = await getTeamByIdAsync(userid, team_id, {
                                                                    personal_team: ret_val.personal_team,
                                                                    users_invited: ret_val.users_invited,
                                                                    set_previous_tools: ret_val.set_previous_tools,
                                                                    include_projects: options.include_projects,
                                                                    include_plan_info: false,
                                                                });
                                                                cb(null, team);
                                                            } catch (getTeamErr) {
                                                                cb(getTeamErr);
                                                            }
                                                        } else {
                                                            cb(null, ret_val);
                                                        }

                                                        // kick off async after the team is created
                                                        bulkInviteEmailLeadSeeding(emailLeadMap);
                                                    }
                                                });
                                            }
                                        }
                                    );
                                }
                            });
                        });
                    });
                })
                .catch(seqErr => cb(seqErr));
        }
    );
}

export { MAX_WORKSPACES_COUNT };

async function createTeamAsync(userid, options) {
    return new Promise((resolve, reject) => {
        _createTeam(userid, options, (err, result) => {
            if (err) {
                reject(err);
            } else {
                resolve(result);
            }
        });
    });
}

export { createTeamAsync };

export function createTeam(req, resp) {
    const userid = req.decoded_token.user;
    const {
        name,
        default_payment_token,
        stored_promo_code,
        promo_code,
        slack_channel,
        color_theme,
        onboarding_v2,
        should_encrypt,
        address,
        number_of_team_users,
        time_tracking_default_to_billable,
        time_tracking_display_hours,
        time_estimate_display_hours,
        enable_codox,
        dashboards_enabled,
        can_add_guests,
        can_remove_guests,
        saastr_2020_credit,
        time_estimate_rollup,
        time_tracking_rollup,
        lineup,
        threaded_comments,
        ga4_attributes,
        segmentTrackingAttributes,
        chat_enabled,
        chat_context,
    } = req.body;
    let usersToAdd = req.body.add || [];
    const plan_id = req.body.plan;
    const addons = req.body.addons || [];
    const color = req.body.color || color_helper.getRandomColor();
    const signed_attachments = req.body.signed_attachments || false;
    const using_github = req.body.using_github || false;
    const using_gitlab = req.body.using_gitlab || false;
    let { cycles } = req.body;
    let projects = req.body.spaces;
    let avatar = null;

    const build_version = req?.get?.('build-version') ?? '';

    let include_projects = req.query.include_projects || false;

    if (include_projects === 'false') {
        include_projects = false;
    }

    let make_post_creation_updates = req.query.include_projects || false;

    let is_optimized = req.query.is_optimized || false;

    is_optimized = helpers.toBoolean(is_optimized);

    if (make_post_creation_updates === 'false') {
        make_post_creation_updates = false;
    }

    if (name && name.length > 50) {
        resp.status(400).send({
            err: 'Team name too long',
            ECODE: 'TEAM_100',
        });
        return;
    }

    if (req.files && req.files.avatar) {
        [avatar] = req.files.avatar;
    }

    if (cycles !== 'monthly' && cycles !== 'yearly') {
        cycles = 'yearly';
    }

    if (Object.prototype.toString.call(usersToAdd) === '[object String]') {
        try {
            usersToAdd = JSON.parse(usersToAdd);
        } catch (e) {
            resp.status(400).send({
                err: 'Added users not formatted properly',
                ECODE: 'TEAM_040',
            });
            return;
        }
    }

    if (usersToAdd.length > 25) {
        resp.status(400).send({
            err: 'Over limit of users to invite, please create your tma first',
            ECODE: 'TEAM_040',
        });
        return;
    }

    if (Object.prototype.toString.call(projects) === '[object String]') {
        try {
            projects = JSON.parse(projects);
        } catch (e) {
            resp.status(400).send({
                err: 'Spaces not formatted properly',
                ECODE: 'TEAM_040',
            });
            return;
        }
    }

    if (!input.validateColor(color)) {
        logger.error({
            msg: 'Failed to validate color',
            status: 400,
            ECODE: 'TEAM_055',
        });
        resp.status(400).send({
            err: 'Color did not validate',
            ECODE: 'TEAM_055',
        });
        return;
    }

    const platform = helpers.getPlatform(req.headers['user-agent'].toLowerCase());

    _createTeam(
        userid,
        {
            name,
            usersToAdd,
            avatar,
            plan_id,
            addons,
            default_payment_token,
            promo_code,
            color,
            slack_channel,
            signed_attachments,
            using_github,
            using_gitlab,
            cycles,
            color_theme,
            enable_codox,
            dashboards_enabled,
            add_six_months: req.body.add_six_months,
            projects,
            onboarding_v2,
            personal_team: false,
            should_encrypt,
            stored_promo_code,
            address,
            number_of_team_users,
            time_tracking_display_hours,
            time_estimate_display_hours,
            can_add_guests,
            can_remove_guests,
            time_tracking_rollup,
            time_estimate_rollup,
            saastr_2020_credit,
            lineup,
            threaded_comments,
            platform,
            verbose_response: req.query.verbose_response,
            include_projects,
            make_post_creation_updates,
            is_optimized,
            ga4_attributes,
            build_version,
            segmentTrackingAttributes,
            time_tracking_default_to_billable,
            chat_enabled,
            chat_context,
        },
        async (err, result) => {
            if (err) {
                resp.status(err.status).send({
                    err: err.err,
                    ECODE: err.ECODE,
                });
            } else {
                const teamId = result.id;

                try {
                    await skuTrialService.handleGracePeriodTrial(teamId, userid);
                } catch (error) {
                    logger.error({
                        err: error,
                        msg: 'Failed to start grace period trial',
                        teamId,
                    });
                }

                if (ssoWorkspaceScopingConfig(userid)?.shouldExtendRefreshTokenWithWorkspaceCreatedScope) {
                    try {
                        const newRefreshToken = await extendRefreshTokenWithWsCreatedScope({
                            req,
                            resp,
                            responseBody: result,
                            workspaceId: teamId,
                        });
                    } catch (newRefreshTokenErr) {
                        logger.error({
                            msg: 'Failed to extend refresh token with workspace created scope',
                            err: newRefreshTokenErr,
                        });
                    }
                }

                resp.status(200).send(result);
            }
        }
    );
}

function _createPersonalTeam(
    userid,
    plan_id,
    add_six_months,
    time_tracking,
    stored_promo_code,
    number_of_team_users,
    options,
    platform,
    verbose_response,
    build_version,
    cb
) {
    async.parallel(
        {
            username(para_cb) {
                db.readQuery('SELECT username FROM task_mgmt.users WHERE id = $1', [userid], para_cb);
            },
            teamNames(para_cb) {
                db.globalReadQuery(`SELECT name FROM task_mgmt.teams_global teams WHERE owner = $1`, [userid], para_cb);
            },
        },
        (err, result) => {
            if (err) {
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: '',
                });
                return;
            }
            if (result.username.rows.length === 0) {
                cb({ err: 'User not found', status: 404, ECODE: '' });
                return;
            }
            if (options.name && options.name.length > 50) {
                cb({
                    err: 'Team name too long',
                    ECODE: 'TEAM_107',
                    status: 400,
                });
                return;
            }

            const team_names = result.teamNames.rows.map(row => row.name);
            const username = result.username.rows[0].username || 'User';
            let number_to_add = 0;
            let first_name = username;
            if (username && username.includes(' ')) {
                [first_name] = username.split(' ');
            }
            let team_name = `${first_name}'s Workspace`;

            while (team_names.indexOf(team_name) >= 0) {
                number_to_add += 1;

                team_name = `${first_name}'s Workspace (${number_to_add})`;
            }

            team_name = options.name || team_name;

            let projects;
            if (Object.prototype.toString.call(options.spaces) === '[object String]') {
                try {
                    projects = JSON.parse(options.spaces);
                } catch (e) {
                    cb({
                        err: 'Spaces not formatted properly',
                        ECODE: 'TEAM_041',
                        status: 400,
                    });
                    return;
                }
            } else {
                projects = [
                    {
                        name: `Space`,
                        statuses: [
                            {
                                status: 'Open',
                                color: config.default_open_status.color,
                            },
                            {
                                status: 'Closed',
                                color: config.default_closed_status.color,
                            },
                        ],
                        features: {
                            dates: {
                                enabled: false,
                            },
                            priorities: {
                                enabled: false,
                            },
                            time_tracking: {
                                enabled: time_tracking || false,
                            },
                        },
                    },
                ];

                if (options.v2) {
                    projects = [
                        {
                            name: `Space`,
                            statuses: [
                                {
                                    status: 'to do',
                                    color: config.default_open_status.color,
                                    type: 'open',
                                },
                                {
                                    status: 'complete',
                                    color: config.default_closed_status.color,
                                    type: 'closed',
                                },
                            ],
                            features: {
                                dates: {
                                    enabled: false,
                                },
                                priorities: {
                                    enabled: false,
                                },
                                time_tracking: {
                                    enabled: time_tracking || false,
                                },
                            },
                        },
                    ];
                }
            }

            _createTeam(
                userid,
                {
                    name: team_name,
                    usersToAdd: [userid],
                    avatar: options.avatar,
                    plan_id,
                    addons: [],
                    default_payment_token: null,
                    promo_code: null,
                    color: options.color || color_helper.getRandomColor(),
                    slack_channel: null,
                    signed_attachments: false,
                    using_github: false,
                    cycles: 'yearly',
                    color_theme: null,
                    add_six_months,
                    projects,
                    onboarding_v2: true,
                    personal_team: true,
                    should_encrypt: false,
                    stored_promo_code,
                    number_of_team_users,
                    lineup: options.lineup,
                    threaded_comments: options.threaded_comments,
                    platform,
                    verbose_response,
                    include_projects: options.include_projects,
                    make_post_creation_updates: options.make_post_creation_updates,
                    ga4_attributes: options.ga4_attributes,
                    build_version,
                    segmentTrackingAttributes: options.segmentTrackingAttributes,
                    project_type: ProjectType.personalList,
                },
                cb
            );
        }
    );
}

export async function createPersonalTeam(req, resp) {
    let defaultPlan;
    try {
        defaultPlan = await retry(
            async () => planService.getDefaultFreeForeverPlanId(),
            'Fetch default FF plan id',
            logger
        );
    } catch (err) {
        defaultPlan = FreeForeverDefault;
    }

    const userid = req.decoded_token.user;
    const plan_id = req.body.plan || defaultPlan;
    const { time_tracking, stored_promo_code, number_of_team_users } = req.body;
    const options = req.body;

    const build_version = req?.get?.('build-version') ?? '';

    if (req.files && req.files.avatar) {
        const [avatar] = req.files.avatar;
        options.avatar = avatar;
    }

    const platform = helpers.getPlatform(req.headers['user-agent'].toLowerCase());

    let include_projects = req.query.include_projects || false;

    if (include_projects === 'false') {
        include_projects = false;
    }

    options.include_projects = include_projects;

    let make_post_creation_updates = req.query.include_projects || false;

    if (make_post_creation_updates === 'false') {
        make_post_creation_updates = false;
    }

    options.make_post_creation_updates = make_post_creation_updates;

    _createPersonalTeam(
        userid,
        plan_id,
        req.body.add_six_months,
        time_tracking,
        stored_promo_code,
        number_of_team_users,
        options,
        platform,
        req.query.verbose_response,
        build_version,
        async (err, result) => {
            if (err) {
                resp.status(err.status).send({
                    err: err.err,
                    ECODE: err.ECODE,
                });
            } else {
                const teamId = result.id;

                try {
                    await skuTrialService.handleGracePeriodTrial(teamId, userid);
                } catch (error) {
                    logger.error({
                        err: error,
                        msg: 'Failed to start grace period trial',
                        teamId,
                    });
                }

                if (ssoWorkspaceScopingConfig(userid)?.shouldExtendRefreshTokenWithWorkspaceCreatedScope) {
                    try {
                        const newRefreshToken = await extendRefreshTokenWithWsCreatedScope({
                            req,
                            resp,
                            responseBody: result,
                            workspaceId: teamId,
                        });
                    } catch (newRefreshTokenErr) {
                        logger.error({
                            msg: 'Failed to extend refresh token with workspace created scope',
                            err: newRefreshTokenErr,
                        });
                    }
                }

                resp.status(200).send(result);
            }
        }
    );
}

function _deleteTeamProjects(userid, team_id, immediately) {
    const query = 'SELECT id FROM task_mgmt.projects WHERE team = $1';
    db.readQuery(query, [team_id], (err, result) => {
        if (err) {
            logger.error({
                msg: 'Error getting projects to delete under team',
                err,
            });
        } else {
            result.rows.forEach(row => {
                project._deleteProject(row.id, immediately, {}, () => {
                    // let it run
                });
            });
        }
    });
}

function sendRemovedFromTeam(team_id, rem, removedBy) {
    if (!rem) {
        return;
    }
    rem.forEach((userid, idx) => {
        if (!userid) {
            return;
        }

        teamMemberMod._saveUserRemoved(removedBy, team_id, userid, {}, idx);
        sqs.sendWSMessage('sendRemovedFromTeam', [userid, team_id]);
    });
}

export { sendRemovedFromTeam };

/**
 * Tracks the deletion of a team.
 *
 * @param {number} userid - The ID of the user performing the deletion.
 * @param {number} teamid - The ID of the team being deleted.
 * @param {boolean} immediately - Indicates whether the team is being deleted immediately or with a delay.
 */
function trackTeamDeletion(userid, teamid, immediately) {
    const dateDeleted = Date.now();
    db.writeQuery(
        'INSERT INTO task_mgmt.team_audit(userid, team_id, action, date, data) VALUES ($1, $2, $3, $4, $5)',
        [userid, teamid, 'team_deleted', dateDeleted, { immediately }],
        err => {
            if (err) {
                logger.error({
                    msg: 'Failed to track deletion',
                    err,
                    status: 500,
                    ECODE: 'TEAM_204',
                });
            }
        }
    );
    db.globalWriteQuery(
        `INSERT INTO task_mgmt.deleted_teams (team_id, deleted_by, date_deleted, deletion_strategy)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (team_id) DO NOTHING`,
        [teamid, userid, dateDeleted, immediately ? 'IMMEDIATE' : 'DELAYED'],
        { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_2 },
        err => {
            if (err) {
                logger.warn({
                    msg: 'Failed to register team deletion in global table',
                    err,
                    status: 500,
                    ECODE: 'TEAM_204',
                });
            }
        }
    );
}

function deactivatePublicViews(team_id) {
    async.parallel({
        updatePublicViews(para_cb) {
            db.writeQuery(
                'UPDATE task_mgmt.views SET public = FALSE, form_active = FALSE WHERE team_id = $1 AND (public = TRUE OR form_active = TRUE)',
                [team_id],
                err => {
                    if (err) {
                        logger.error({
                            msg: 'Failed to deactivate public views',
                            team_id,
                            err,
                        });
                    }
                    para_cb();
                }
            );
        },
        updatePublicPageViews(para_cb) {
            db.writeQuery(
                `UPDATE task_mgmt.view_docs SET public = FALSE FROM task_mgmt.views WHERE view_docs.view_id = views.view_id AND views.team_id = $1`,
                [team_id],
                err => {
                    if (err) {
                        logger.error({
                            msg: 'Failed to deactivate public doc views',
                            team_id,
                            err,
                        });
                    }
                    para_cb();
                }
            );
        },
    });
}

function _deleteTeamFixed(client, done, userid, team_id, immediately, options, cb) {
    // OVM version updates are NOT committed in this function, caller is expected to handle it
    if (!client.versionUpdates) {
        client.versionUpdates = [];
    }

    client.versionUpdates.push(
        hierarchyObjectVersionUpdateRequests({
            objectId: team_id,
            objectType: ObjectType.WORKSPACE,
            workspaceId: team_id,
            operation: OperationType.DELETE,
            fanout: true,
            fanoutUsersAndGroups: true,
        })
    );

    client.query(
        'UPDATE task_mgmt.teams SET deleted = true WHERE id = $1 AND owner = $2 RETURNING avatar_key, name',
        [team_id, userid],
        (err, result) => {
            if (err) {
                db.rollback(client, done);
                logger.error({
                    msg: 'Failed to delete team',
                    err,
                    status: 500,
                    ECODE: 'TEAM_007',
                });
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'TEAM_007',
                });
            } else if (result.rowCount === 0) {
                db.rollback(client, done);
                cb({
                    err: 'You are not the owner of this team or the team does not exist',
                    ECODE: 'TEAM_008',
                    status: 401,
                });
            } else {
                if (result.rows[0].avatar_key) {
                    s3.deleteObjects(
                        {
                            Bucket: config.aws.bucket,
                            Delete: {
                                Objects: [{ Key: result.rows[0].avatar_key }],
                            },
                        },
                        s3Err => {
                            if (err) {
                                logger.error({
                                    msg: 'Failed to delete old team avatar',
                                    err: s3Err,
                                });
                            }
                        }
                    );
                }
                client.query(
                    'UPDATE task_mgmt.team_members SET deleted = true WHERE team_id = $1 RETURNING userid',
                    [team_id],
                    /**
                     * @param {any} updateErr
                     * @param {{rows: {userid: number}[]}} updateResult
                     */
                    async (updateErr, updateResult) => {
                        if (updateErr) {
                            db.rollback(client, done);
                            logger.error({
                                msg: 'Failed to delete team members',
                                err: updateErr,
                                status: 500,
                                ECODE: 'TEAM_009',
                            });
                            cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'TEAM_009',
                            });
                        } else {
                            if (!options.skipDone) {
                                done();
                            }
                            cb(null, {});

                            const teamResult = options.team_obj || null;

                            trackTeamDeletion(userid, team_id, immediately);
                            deactivatePublicViews(team_id);
                            _deleteTeamProjects(userid, team_id, immediately);
                            deleteViewService.softDeleteAllDocs(userid, team_id, {});

                            if (teamResult) {
                                let value = teamResult?.upgradeInfo?.currentPrice?.total ?? 0;
                                if (
                                    Number(value) !== 0 &&
                                    parseInt(teamResult.service_status, 10) !==
                                        parseInt(config.service_status.trial, 10)
                                ) {
                                    value =
                                        teamResult.plan_info.cycles === 'monthly' ? Number(value) : Number(value) / 12;
                                    if (options.churnPayload) {
                                        const { churnPayload } = options;
                                        db.globalWriteQuery(
                                            // THIS LOGIC DOESNT WORK - known issue
                                            'INSERT INTO task_mgmt.churn_reason(userid, team_id, date, reason) VALUES ($1, $2, $3, $4)',
                                            [userid, team_id, new Date().getTime(), churnPayload],
                                            { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_2 },
                                            churnInsertErr => {
                                                if (churnInsertErr) {
                                                    logger.error({
                                                        msg: 'Failed to insert churn reason',
                                                        churnInsertErr,
                                                    });
                                                }
                                            }
                                        );
                                    }
                                }
                            }
                            sendRemovedFromTeam(
                                team_id,
                                updateResult.rows.map(row => row.userid),
                                userid
                            );
                        }
                    }
                );
            }
        }
    );
}

function _deleteTeamInTransaction(client, done, userid, team_id, immediately, team_obj, cb) {
    client.query('BEGIN', err => {
        if (err) {
            db.rollback(client, done);
            logger.error({
                msg: 'Failed to begin',
                err,
                status: 500,
                ECODE: 'TEAM_006',
            });
            cb({
                err: 'Internal server error',
                status: 500,
                ECODE: 'TEAM_006',
            });
        }

        _deleteTeamFixed(client, done, userid, team_id, immediately, { team_obj, skipDone: true }, async deleteErr => {
            if (deleteErr) {
                cb(deleteErr);
                return;
            }

            let events;
            const ovm = getObjectVersionManager();
            try {
                const txClient = new TransactionClientImpl(client, ovm);
                events = await ovm.updateVersions(txClient, client.versionUpdates);
            } catch (ovmErr) {
                logger.error({
                    msg: 'Failed to write OVM update',
                    OVM_CRITICAL: true,
                    ECODE: 'OVM_WS_131',
                    err: ovmErr,
                });
                db.rollback(client, done);
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'TEAM_010',
                });
                return;
            }

            client.query('COMMIT', async commitErr => {
                if (commitErr) {
                    db.rollback(client, done);
                    logger.error({
                        msg: 'Failed to commit',
                        err: commitErr,
                        status: 500,
                        ECODE: 'TEAM_010',
                    });
                    cb({
                        err: 'Internal server error',
                        status: 500,
                        ECODE: 'TEAM_010',
                    });
                } else {
                    done();
                    await ovm.notifyChanges(events).catch(() => {});
                    webhook.sendWebhookMessage('teamDeleted', { team_id });
                    cb();
                }
            });
        });
    });
}

export { _deleteTeamInTransaction };

const updateHierarchyOwners = (userid, team_id, hierarchy_table, members_table, hierarchy_column, cb) => {
    db.replicaQuery(`SELECT id FROM ${hierarchy_table} WHERE owner = $1`, [userid], {}, (err, result) => {
        if (err) {
            cb(err);
            return;
        }

        const hierarchy_ids = result.rows.map(row => row.id);
        async.eachSeries(
            hierarchy_ids,
            (hierarchy_id, each_cb) => {
                updateHierarchyOwner(
                    userid,
                    team_id,
                    hierarchy_id,
                    hierarchy_table,
                    members_table,
                    hierarchy_column,
                    each_cb
                );
            },
            cb
        );
    });
};

const updateHierarchyOwner = (userid, team_id, hierarchy_id, hierarchy_table, members_table, hierarchy_column, cb) => {
    async.waterfall(
        [
            function findNewOwner(wf_cb) {
                const query = `
                SELECT userid
                FROM task_mgmt.team_members
                WHERE team_members.team_id = $1
                  AND team_members.userid IN (SELECT userid FROM ${members_table} WHERE ${hierarchy_column} = $2)
                ORDER BY team_members.role
                LIMIT 1
            `;
                const params = [team_id, hierarchy_id];

                db.readQuery(query, params, {}, (err, result) => wf_cb(err, result?.rows?.[0]?.userid));
            },
            function updateOwner(new_owner_id, wf_cb) {
                if (!new_owner_id) {
                    wf_cb(null, null);
                    return;
                }

                const query = `
                UPDATE ${hierarchy_table}
                SET owner = $1
                WHERE id = $2
            `;
                const params = [new_owner_id, hierarchy_id];

                db.writeQuery(query, params, err => wf_cb(err, new_owner_id));
            },
            function updateHierarchyMemberPermissionLevel(new_owner_id, wf_cb) {
                if (!new_owner_id) {
                    wf_cb(null);
                    return;
                }

                const query = `
                UPDATE ${members_table}
                SET permission_level = 5
                WHERE userid = $1 AND ${hierarchy_column} = $2
            `;
                const params = [new_owner_id, hierarchy_id];

                db.writeQuery(query, params, wf_cb);
            },
        ],
        cb
    );
};

function updateCategoryOwners(userid, team_id, cb) {
    if (!cb) {
        cb = () => {};
    }

    updateHierarchyOwners(userid, team_id, 'task_mgmt.categories', 'task_mgmt.category_members', 'category', cb);
}
export { updateCategoryOwners };

function updateSubcategoryOwners(userid, team_id, cb) {
    if (!cb) {
        cb = () => {};
    }

    updateHierarchyOwners(
        userid,
        team_id,
        'task_mgmt.subcategories',
        'task_mgmt.subcategory_members',
        'subcategory',
        cb
    );
}
export { updateSubcategoryOwners };

function _leaveTeam(client, done, userid, team_id, cb) {
    client.query('DELETE FROM task_mgmt.team_members WHERE team_id = $1 AND userid = $2', [team_id, userid], err => {
        if (err) {
            done();
            cb(new TeamError(err, 'TEAM_011'));
        } else {
            async.parallel(
                {
                    // remove members
                    projectMembers(para_cb) {
                        const query = `
                            DELETE FROM task_mgmt.project_members
                            WHERE userid = $1
                            AND project_id IN (SELECT id FROM task_mgmt.projects WHERE team = $2 and owner != $1)`;
                        const params = [userid, team_id];
                        client.query(query, params, para_cb);
                    },

                    categoryMembers(para_cb) {
                        const query = `
                            DELETE FROM task_mgmt.category_members
                            WHERE userid = $1
                            AND category IN (
                                SELECT categories.id
                                FROM task_mgmt.categories, task_mgmt.projects
                                WHERE categories.project_id = projects.id
                                AND projects.team = $2
                            )`;
                        const params = [userid, team_id];
                        client.query(query, params, para_cb);
                    },

                    updateCategoryOwners(para_cb) {
                        updateCategoryOwners(userid, team_id, para_cb);
                    },

                    subcategoryMembers(para_cb) {
                        const query = `
                            DELETE FROM task_mgmt.subcategory_members
                            WHERE userid = $1
                            AND subcategory IN (
                                SELECT subcategories.id
                                FROM
                                    task_mgmt.subcategories,
                                    task_mgmt.categories,
                                    task_mgmt.projects
                                WHERE subcategories.category = categories.id
                                    AND categories.project_id = projects.id
                                    AND projects.team = $2
                            )`;
                        const params = [userid, team_id];
                        client.query(query, params, para_cb);
                    },

                    updateSubcategoryOwners(para_cb) {
                        updateSubcategoryOwners(userid, team_id, para_cb);
                    },

                    taskMembers(para_cb) {
                        const query = `
                            DELETE FROM task_mgmt.task_members
                            WHERE userid = $1
                            AND task_id IN (
                                SELECT items.id
                                FROM
                                    task_mgmt.items,
                                    task_mgmt.subcategories,
                                    task_mgmt.categories,
                                    task_mgmt.projects
                                WHERE items.subcategory = subcategories.id
                                    AND subcategories.category = categories.id
                                    AND categories.project_id = projects.id
                                    AND projects.team = $2
                            )`;
                        const params = [userid, team_id];
                        client.query(query, params, para_cb);
                    },

                    // remove sso
                    google_sso(para_cb) {
                        const query = `
                            DELETE FROM task_mgmt.google_ids
                            WHERE userid = $1
                            AND org_id = (SELECT google_sso FROM task_mgmt.teams_global teams WHERE id = $2)`;
                        const params = [userid, team_id];

                        db.globalWriteQuery(
                            query,
                            params,
                            { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_17 },
                            para_cb
                        );
                    },

                    azure_sso(para_cb) {
                        const query = `
                            DELETE FROM task_mgmt.azure_ids
                            WHERE userid = $1
                            AND org_id = (SELECT azure_sso FROM task_mgmt.teams_global teams WHERE id = $2)`;
                        const params = [userid, team_id];

                        // These queries are not done in a transaction, so we should be able to
                        // go directly to the global connection.
                        db.globalWriteQuery(
                            query,
                            params,
                            { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_17 },
                            para_cb
                        );
                    },
                    okta_sso(para_cb) {
                        db.globalWriteQuery(
                            deleteSsoDatastore.REMOVE_USER_OKTA_SSO_LINK_QUERY,
                            [userid, team_id],
                            { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_17 },
                            para_cb
                        );
                    },

                    saml_sso(para_cb) {
                        db.globalWriteQuery(
                            deleteSsoDatastore.REMOVE_USER_SAML_SSO_LINK_QUERY,
                            [userid, team_id],
                            { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_17 },
                            para_cb
                        );
                    },

                    userGroupMembers(para_cb) {
                        client.query(
                            deleteUserGroupMemberDatastore.REMOVE_USER_GROUP_MEMBER_QUERY,
                            [userid, team_id],
                            para_cb
                        );
                    },

                    team_orderindex(para_cb) {
                        db.globalWriteQuery(
                            'DELETE FROM task_mgmt.team_orderindex WHERE user_id = $1 and workspace_id = $2',
                            [userid, team_id],
                            { globalTableMigrationBatchId: GlobalTableMigrationBatchId.ALWAYS_ENABLED },
                            para_cb
                        );
                    },
                },
                async err1 => {
                    if (err1) {
                        done();
                        cb(new TeamError(err1, 'TEAM_085'));
                    } else {
                        let events;
                        const ovm = getObjectVersionManager();
                        const versionUpdates = updateRequestForWorkspaceMembers({
                            workspaceId: Number(team_id),
                            users: [userid],
                            operationType: OperationType.DELETE,
                            addWorkspaceRecord: true,
                            fanoutToGroups: true,
                        });
                        try {
                            const txClient = new TransactionClientImpl(client, ovm);
                            events = await ovm.updateVersions(txClient, versionUpdates);
                        } catch (ovmErr) {
                            logger.error({
                                msg: 'Failed to write OVM update',
                                OVM_CRITICAL: true,
                                ECODE: 'OVM_WS_135',
                                err: ovmErr,
                            });
                            // rollback has no value as we're not in a transaction, except it also calls done
                            db.rollback(client, done);
                            cb(ovmErr);
                            return;
                        }
                        done();
                        await ovm.notifyChanges(events).catch(() => {});
                        cb(null, {});

                        sendRemovedFromTeam(team_id, [userid], userid);
                        deleteNylasAccountsIfNecessary(team_id, userid, true);
                        closeWorkspaceUserAccessRequests(team_id, userid, 'Leaving workspace');
                    }
                }
            );
        }
    });
}

function _changeOwnerAndLeave(client, done, userid, team_id, new_owner, cb) {
    client.query(
        'UPDATE task_mgmt.teams SET owner = $1 WHERE id = $2 AND EXISTS (SELECT userid FROM task_mgmt.team_members WHERE invite = false and userid = $1)',
        [new_owner, team_id],
        async (err, result) => {
            if (err) {
                done();
                cb({
                    msg: 'Failed to change owner of team',
                    status: 500,
                    ECODE: 'TEAM_045',
                });
            } else if (result.rowCount === 0) {
                done();
                cb({
                    msg: 'New owner not found in team',
                    status: 404,
                    ECODE: 'TEAM_046',
                });
            } else {
                let events;
                const ovm = getObjectVersionManager();
                const versionUpdates = updateRequestForWorkspaceMembers({
                    workspaceId: Number(team_id),
                    users: [new_owner],
                    operationType: OperationType.UPDATE,
                    addWorkspaceRecord: true,
                });
                try {
                    const txClient = new TransactionClientImpl(client, ovm);
                    events = await ovm.updateVersions(txClient, versionUpdates);
                } catch (ovmErr) {
                    done();
                    logger.error({
                        msg: 'Failed to write OVM update',
                        OVM_CRITICAL: true,
                        ECODE: 'OVM_WS_134',
                        err: ovmErr,
                    });
                    cb(ovmErr);
                    return;
                }

                await ovm.notifyChanges(events).catch(() => {});
                _leaveTeam(client, done, userid, team_id, cb);
            }
        }
    );
}

async function _deleteOrLeaveTeam(userid, team_id, new_owner, immediately, options, cb) {
    let team_obj;
    try {
        team_obj = await getTeamByIdAsync(userid, team_id, {
            include_plan_info: false,
            includeStorageInfoForNonAdmins: true,
            checkJoined: false,
        });

        const { plan_info, upgradeInfo } = await getPlanPricing(userid, team_id, {
            checkJoined: false,
            skipNonAdminAccessCheck: true,
        });

        team_obj = { ...team_obj, plan_info, upgradeInfo };
    } catch (error) {
        cb(error);
        return;
    }

    db.readQuery(
        'SELECT owner, google_sso, azure_sso, okta_sso, saml_sso FROM task_mgmt.teams WHERE id = $1',
        [team_id],
        async (err, result) => {
            if (err) {
                logger.error({
                    msg: 'Failed to delete or leave team',
                    err,
                    status: 500,
                    ECODE: 'TEAM_005',
                });
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'TEAM_005',
                });
                return;
            }
            if (result.rows.length === 0) {
                logger.error({
                    msg: 'Team not found',
                    status: 404,
                    ECODE: 'TEAM_034',
                });
                cb({
                    err: 'Team not found',
                    status: 404,
                    ECODE: 'TEAM_034',
                });
                return;
            }

            const { google_sso, azure_sso, okta_sso, saml_sso } = result.rows[0];
            const isSSOEnabledOnTeam = google_sso || azure_sso || okta_sso || saml_sso;

            if (result.rows[0].owner === userid && isSSOEnabledOnTeam) {
                logger.info({
                    msg: 'Owner attempting to delete Workspace with SSO enabled',
                    status: 403,
                    ECODE: 'TEAM_047',
                    team_id,
                });
                cb({
                    err: 'You must transfer ownership or disable SSO settings to leave or delete this Workspace',
                    status: 403,
                    ECODE: 'TEAM_047',
                });
                return;
            }

            try {
                await logLeaveWorkspace({ workspaceId: team_id, userId: userid });
            } catch (auditLogErr) {
                logger.debug({
                    msg: 'Failed to add audit-log for team member leaving workspace',
                    err: auditLogErr,
                    ECODE: 'TEAM_208',
                });
            }

            const deletionConfig = workspaceDeletionConfig();

            if (deletionConfig.splitTransactions) {
                // First resolve the action
                if (result.rows[0].owner === userid) {
                    if (new_owner) {
                        // change owner on team
                        db.getConn(cb, { label: 'change owner and leave' }, (connErr, client, done) => {
                            if (connErr) {
                                return;
                            }
                            _changeOwnerAndLeave(client, done, userid, team_id, new_owner, cb);
                        });
                    } else if (deletionConfig.useNewDeletionFlow) {
                        // delete the team
                        // This runs the SSO and other checks again. This is until we refactor the operations
                        // to separate API endpoints - leave/leave+reassign/delete
                        await deleteWorkspace(userid, team_id, options.churnPayload);
                        cb();
                    } else {
                        db.getConn(cb, { label: 'delete team' }, (connErr, client, done) => {
                            if (connErr) {
                                return;
                            }
                            _deleteTeamInTransaction(client, done, userid, team_id, immediately, { team_obj }, cb);
                        });
                    }
                } else {
                    // leave team
                    db.getConn(cb, { label: 'leave team' }, (connErr, client, done) => {
                        if (connErr) {
                            return;
                        }
                        _leaveTeam(client, done, userid, team_id, cb);
                    });
                }
            } else {
                db.getConn(cb, { label: 'delete or leave team' }, (connErr, client, done) => {
                    if (connErr) {
                        return;
                    }

                    if (result.rows[0].owner === userid) {
                        if (new_owner) {
                            // change owner on team
                            _changeOwnerAndLeave(client, done, userid, team_id, new_owner, cb);
                        } else {
                            _deleteTeamInTransaction(client, done, userid, team_id, immediately, { team_obj }, cb);
                        }
                    } else {
                        // leave team
                        _leaveTeam(client, done, userid, team_id, cb);
                    }
                });
            }
        }
    );
}

export function deleteOrLeaveTeam(req, resp) {
    const userid = req.decoded_token.user;
    const { team_id } = req.params;
    const { new_owner } = req.query;
    const options = req.body;
    let hard_remove_members = req.query.hard_remove_members || false;
    let immediately = req.query.immediately || false;

    if (immediately === 'false') {
        immediately = false;
    }
    if (hard_remove_members === 'false') {
        hard_remove_members = false;
    }

    logger.info({
        msg: 'Delete or leave team request',
        userid,
        team_id,
        new_owner,
        immediately,
    });

    _deleteOrLeaveTeam(userid, team_id, new_owner, immediately, options, (err, result) => {
        if (err) {
            resp.status(err.status).send({ err: err.err, ECODE: err.ECODE });
        } else {
            resp.status(200).send(result);

            if (hard_remove_members) {
                attemptHardDeleteWorkspaceMembers(userid, team_id);
            }
        }
    });
}

export async function getPlanChange(req, resp) {
    const { team_id } = req.params;

    logger.info({
        msg: 'Get plan change request',
        team_id,
    });

    try {
        const planChangeInfo = await getPlanChangeInfo(team_id);
        resp.status(200).send(planChangeInfo);
    } catch (err) {
        resp.status(err.status).send({ err: err.err, ECODE: err.ECODE });
    }
}

function _getTeam(userid, team_id, options, cb) {
    getTeamMod._getTeam(userid, team_id, options, cb);
}

export { _getTeam };

function getTeamByIdAsync(userid, team_id, options = {}) {
    return new Promise((res, rej) => {
        _getTeam(userid, team_id, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}

export { getTeamByIdAsync };

export function getTeam(req, resp, next) {
    const userid = req.decoded_token.user;
    const { team_id } = req.params;
    const { promo_code } = req.query;
    const exclude_types = [
        config.get('views.view_types.conversation_dm'),
        config.get('views.view_types.conversation_group_dm'),
    ];

    _getTeam(
        userid,
        team_id,
        {
            ...getTeamApiIncludeFields(userid, TeamApiKind.GetTeam, req.query),
            promo_code,
            exclude_types,
        },
        (err, result) => {
            if (err) {
                next(err);
            } else {
                resp.status(200).send(result);
            }
        }
    );
}

function storeShouldEncrypt(team_id, should_encrypt) {
    db.writeQuery('DELETE FROM task_mgmt.team_encrypt_toggle WHERE team = $1', [team_id], () => {
        db.writeQuery(
            'INSERT INTO task_mgmt.team_encrypt_toggle(team, encrypted, date) VALUES ($1, $2, $3)',
            [team_id, should_encrypt, new Date().getTime()],
            () => {}
        );
    });
}

function saveMadeTeam(userid) {
    db2.writeAsync(
        'UPDATE task_mgmt.users SET set_previous_tools = true WHERE id = $1',
        [userid],
        undefined,
        undefined,
        {
            globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_11,
            poolType: DbPool.GlobalWrite,
        }
    ).catch(err => {
        logger.error({
            msg: 'Failed to set that this user has set previous tools',
            status: 500,
            err,
        });
    });
}

function _setDefaultProject(userid, team_id, default_project, cb) {
    const queries = [];
    if (default_project != null) {
        if (default_project === 'none') {
            default_project = null;
        }
        queries.push(
            {
                query: 'INSERT INTO task_mgmt.default_projects(userid, team, default_project) (SELECT $1, $2, $3 WHERE NOT EXISTS (SELECT * FROM task_mgmt.default_projects WHERE userid = $1 AND team = $2))',
                params: [userid, team_id, default_project],
            },
            {
                query: 'UPDATE task_mgmt.default_projects SET default_project = $3 WHERE userid = $1 AND team = $2',
                params: [userid, team_id, default_project],
            }
        );
    }

    db.batchQueries(queries, err => {
        if (err) {
            cb({ err: 'Internal server error', status: 500, ECODE: 'TEAM_083' });
        } else {
            cb(null, {});
        }
    });
}

export { _setDefaultProject };

function editTeamAsync(userid, options = {}) {
    return new Promise((res, rej) => {
        _editTeam(userid, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}

function _editTeam(userid, options, cb) {
    const {
        team_id,
        name,
        avatar,
        color,
        slack_channel,
        unset_avatar,
        unset_milestone_avatar,
        using_github,
        using_gitlab,
        setup_step,
        previous_tools,
        default_payment_token,
        color_theme,
        personal_team,
        should_encrypt,
        hours_per_day,
        stored_promo_code,
        address,
        require_2fa,
        members_can_add_seats,
        number_of_team_users,
        time_tracking_default_to_billable,
        time_tracking_display_hours,
        time_estimate_display_hours,
        time_tracking_rollup,
        time_estimate_rollup,
        admin_global_delete,
        enable_codox,
        dashboards_enabled,
        can_add_guests,
        can_remove_guests,
        hosted_secret,
        enable_recorder,
        docs_home,
        live_view,
        admin_public_share_override,
        automation_enabled,
        lineup,
        threaded_comments,
        milestone_avatar,
        milestone_title,
        user_presence,
        allow_skip_2fa,
        personal_views,
        unstarted_status_group,
        tasks_in_multiple_lists,
        custom_fields_legacy_ordering,
        points_estimate_rollup,
        points_scale,
        owner_control_private_spaces,
        points_remap,
        sprint_settings,
        wip_limit,
        signed_attachments,
        hide_everything_calendar,
        hide_everything_board,
        emails_as_replies,
        nested_subtasks,
        nested_subtasks_level,
        quick_create_statuses,
        universal_search,
        microsoft_365_preview,
        extra_comment_reactions,
        giphy,
        task_relationships,
        custom_sprint_duration,
        workspace_settings,
        task_duration,
        notetaker_enabled,
    } = options;

    logger.info({
        workspace_settings,
    });

    const use_master = options.use_master || false;

    let {
        default_project,
        disable_public_sharing,
        disable_template_pub_sharing,
        disable_never_expire_pub_links,
        pub_links_max_year,
        estimates_per_assignee,
        points_per_assignee,
        time_in_status,
        subtasks_in_multiple_lists,
        ai_enabled,
        chat_enabled,
        chat_context,
    } = options;

    let settings_diff = null;
    const ovmWSSettingsChanges = [];
    if (name) {
        if (!input.validateTeamName(name)) {
            cb({
                err: 'Team name invalid',
                status: 400,
                ECODE: 'TEAM_064',
            });
            return;
        }
    }

    async.parallel(
        {
            access(para_cb) {
                let team_permissions = false;
                if (
                    typeof can_add_guests !== 'undefined' ||
                    typeof disable_public_sharing !== 'undefined' ||
                    typeof disable_template_pub_sharing !== 'undefined' ||
                    typeof admin_public_share_override !== 'undefined' ||
                    typeof require_2fa !== 'undefined'
                ) {
                    team_permissions = true;
                }

                access.checkAccessTeam(
                    userid,
                    team_id,
                    { permissions: ['can_edit_team'], team_permissions, use_master },
                    para_cb
                );
            },
        },
        err => {
            if (err) {
                if (err && !err.ECODE) {
                    cb({
                        err: 'Internal server error',
                        status: 500,
                        ECODE: 'TEAM_071',
                    });
                } else {
                    cb(err);
                }
                return;
            }

            db.getConn(cb, { label: 'edit team' }, (connErr, client, done) => {
                if (connErr) {
                    return;
                }
                let avatar_key = null;
                let milestone_avatar_key = null;
                let shouldDisableSignedAttachments = false;

                if (avatar) {
                    let extension = avatar.originalname.split('.');
                    extension = extension[extension.length - 1].toLowerCase();
                    avatar_key = `team_avatars/${team_id}_${randomstring.generate(3)}.${extension}`;
                }

                if (milestone_avatar) {
                    let extension = milestone_avatar.originalname.split('.');
                    extension = extension[extension.length - 1].toLowerCase();
                    milestone_avatar_key = `team_milestone_avatars/${team_id}_${randomstring.generate(3)}.${extension}`;
                }

                async.parallel(
                    {
                        team_info(para_cb) {
                            db.readQuery(
                                `
                                SELECT
                                    teams.*
                                FROM
                                    task_mgmt.teams
                                WHERE id = $1`,
                                [team_id],
                                { use_master },
                                para_cb
                            );
                        },
                        async entitlements(para_cb) {
                            try {
                                const result = await entitlementService.getEntitlements(
                                    team_id,
                                    [
                                        EntitlementName.PublicLinkRestrictions,
                                        EntitlementName.PointsPerAssignee,
                                        EntitlementName.CustomRoles,
                                        EntitlementName.TimeInStatus,
                                        EntitlementName.TimeEstimatesPerAssignee,
                                    ],
                                    { skipUsage: true }
                                );

                                para_cb(null, result);
                            } catch (innerErr) {
                                para_cb(innerErr);
                            }
                        },
                        milestoneAvatarUpdate(para_cb) {
                            // dont think this is used anymore
                            if (!milestone_avatar) {
                                para_cb();
                                return;
                            }
                            thumb.createProfilePicture(milestone_avatar.path, thumbErr => {
                                if (thumbErr) {
                                    para_cb(thumbErr);
                                    return;
                                }
                                const s3params = {
                                    Bucket: config.aws.bucket,
                                    Key: milestone_avatar_key,
                                    Body: fs.createReadStream(milestone_avatar.path),
                                };

                                s3.upload(s3params, s3Err => {
                                    fs.unlink(milestone_avatar.path, () => {});
                                    if (s3Err) {
                                        logger.error({
                                            msg: 'Failed to upload milestone avatar picture',
                                            err: s3Err,
                                            status: 500,
                                            ECODE: 'TEAM_050',
                                        });
                                        para_cb({
                                            err: 'Internal server error',
                                            status: 500,
                                            ECODE: 'TEAM_051',
                                        });
                                    } else {
                                        para_cb();
                                    }
                                });
                            });
                        },
                        teamAvatarUpdate(para_cb) {
                            if (!avatar) {
                                para_cb();
                                return;
                            }
                            thumb.createProfilePicture(avatar.path, thumbErr => {
                                if (thumbErr) {
                                    para_cb(thumbErr);
                                    return;
                                }
                                const s3params = {
                                    Bucket: config.aws.bucket,
                                    Key: avatar_key,
                                    Body: fs.createReadStream(avatar.path),
                                };

                                s3.upload(s3params, s3Err => {
                                    fs.unlink(avatar.path, () => {});
                                    if (s3Err) {
                                        logger.error({
                                            msg: 'Failed to upload avatar picture',
                                            err: s3Err,
                                            status: 500,
                                            ECODE: 'TEAM_029',
                                        });
                                        para_cb({
                                            err: 'Internal server error',
                                            status: 500,
                                            ECODE: 'TEAM_029',
                                        });
                                    } else {
                                        para_cb();
                                    }
                                });
                            });
                        },
                        async workspaceSettingsUpdate(para_cb) {
                            if (!workspace_settings) {
                                para_cb();
                                return;
                            }
                            logger.info({
                                msg: 'Updating workspace settings',
                                team_id,
                                workspace_settings,
                            });
                            try {
                                settings_diff = await setWorkspaceSettings(team_id, workspace_settings);

                                if (shouldActivateAuthenticatedAttachments(team_id)) {
                                    logger.debug({
                                        msg: 'Activating authenticated attachments for workspace in editTeam flow',
                                        team_id,
                                        workspace_settings,
                                    });
                                    if (workspace_settings?.authenticatedAttachments === true) {
                                        logger.debug({
                                            msg: 'Dynamo Add Authenticated Attachments Setting editTeam',
                                            team_id,
                                            workspace_settings,
                                        });
                                        shouldDisableSignedAttachments = true;
                                        await upsertAuthenticatedAttachmentsSetting(team_id);
                                    } else if (workspace_settings?.authenticatedAttachments === false) {
                                        logger.debug({
                                            msg: 'Dynamo Remove Authenticated Attachments Setting editTeam',
                                            team_id,
                                            workspace_settings,
                                        });
                                        await removeAuthenticatedAttachmentsSetting(team_id);
                                    }
                                }

                                const updates = {};
                                const validWorkspaceSettingsChangeKeys = ['authenticatedAttachments', 'layoutSetting'];

                                Object.keys(settings_diff).forEach(key => {
                                    if (
                                        settings_diff[key]?._old !== null &&
                                        settings_diff[key]?._new !== null &&
                                        settings_diff[key]._old !== settings_diff[key]._new
                                    ) {
                                        updates[key] = settings_diff[key]._new;

                                        if (validWorkspaceSettingsChangeKeys.includes(key)) {
                                            ovmWSSettingsChanges.push({
                                                field: `settings.${key}`,
                                                before: settings_diff[key]._old,
                                                after: settings_diff[key]._new,
                                            });
                                        }

                                        if (
                                            key === 'layoutSetting' &&
                                            settings_diff[key]._new === LayoutSetting.v4_0_0 &&
                                            !settings_diff[key]._old
                                        ) {
                                            chat_enabled = true;
                                            chat_context = { source: 'auto-enablement' };
                                        }

                                        if (
                                            key === 'skipSsoForSelectedUsers' &&
                                            settings_diff[key]._new !== undefined
                                        ) {
                                            logSkipSsoForSelectedUserConfig({
                                                workspaceId: team_id,
                                                enabled: settings_diff[key]._new,
                                            });
                                        }
                                    }
                                });
                                para_cb();
                                logAdvancedSettingsUpdated({ workspaceId: team_id, ...updates });
                            } catch (workspaceSettingsErr) {
                                logger.error({
                                    msg: 'Failed to update workspace settings',
                                    err: workspaceSettingsErr,
                                    status: 500,
                                    ECODE: 'TEAM_048',
                                });
                                para_cb(workspaceSettingsErr);
                            }
                        },
                    },
                    async (paraErr, para_result) => {
                        if (paraErr) {
                            done();
                            if (!paraErr.err) {
                                cb({
                                    err: 'Internal server error',
                                    status: 500,
                                    ECODE: 'TEAM_027',
                                });
                                logger.error({
                                    msg: 'Failed to get info to update team',
                                    err: paraErr,
                                    status: 500,
                                    ECODE: 'TEAM_027',
                                });
                            } else {
                                cb(paraErr);
                            }
                            return;
                        }

                        const currentTeam = para_result.team_info.rows[0];
                        const current2faStatus = currentTeam.require_2fa;
                        const old_points_scale = currentTeam.points_scale;
                        const old_avatar_key = currentTeam.avatar_key;
                        const old_milestone_avatar_key = currentTeam.milestone_avatar_key;

                        if (
                            disable_public_sharing &&
                            !para_result.entitlements[EntitlementName.PublicLinkRestrictions].entitled
                        ) {
                            disable_public_sharing = null;
                        }

                        if (
                            disable_template_pub_sharing &&
                            !para_result.entitlements[EntitlementName.PublicLinkRestrictions].entitled
                        ) {
                            disable_template_pub_sharing = null;
                        }

                        if (
                            disable_never_expire_pub_links &&
                            !para_result.entitlements[EntitlementName.PublicLinkRestrictions].entitled
                        ) {
                            disable_never_expire_pub_links = null;
                        }

                        if (
                            pub_links_max_year &&
                            !para_result.entitlements[EntitlementName.PublicLinkRestrictions].entitled
                        ) {
                            pub_links_max_year = null;
                        }

                        if (
                            estimates_per_assignee &&
                            !para_result.entitlements[EntitlementName.TimeEstimatesPerAssignee].entitled
                        ) {
                            estimates_per_assignee = null;
                        }

                        if (
                            points_per_assignee &&
                            !para_result.entitlements[EntitlementName.PointsPerAssignee].entitled
                        ) {
                            points_per_assignee = null;
                        }

                        // Time in status only available for Biz+
                        if (time_in_status && !para_result.entitlements[EntitlementName.TimeInStatus].entitled) {
                            time_in_status = null;
                        }

                        if (ai_enabled && currentTeam.is_ai_hidden) {
                            ai_enabled = null;
                        }

                        const queries = [];
                        if (
                            name ||
                            avatar_key ||
                            milestone_avatar_key ||
                            color ||
                            slack_channel ||
                            unset_avatar ||
                            unset_milestone_avatar ||
                            using_github != null ||
                            using_gitlab != null ||
                            setup_step ||
                            previous_tools != null ||
                            default_payment_token ||
                            color_theme ||
                            personal_team != null ||
                            should_encrypt != null ||
                            hours_per_day != null ||
                            stored_promo_code != null ||
                            address != null ||
                            require_2fa != null ||
                            members_can_add_seats != null ||
                            number_of_team_users != null ||
                            time_tracking_display_hours != null ||
                            time_estimate_display_hours != null ||
                            admin_global_delete != null ||
                            enable_codox != null ||
                            dashboards_enabled != null ||
                            can_add_guests != null ||
                            can_remove_guests != null ||
                            hosted_secret != null ||
                            disable_public_sharing != null ||
                            disable_template_pub_sharing != null ||
                            enable_recorder != null ||
                            docs_home != null ||
                            live_view != null ||
                            admin_public_share_override != null ||
                            hosted_secret != null ||
                            time_tracking_rollup != null ||
                            time_estimate_rollup != null ||
                            automation_enabled != null ||
                            ai_enabled != null ||
                            disable_never_expire_pub_links != null ||
                            pub_links_max_year != null ||
                            estimates_per_assignee != null ||
                            points_per_assignee != null ||
                            lineup != null ||
                            threaded_comments != null ||
                            milestone_title != null ||
                            user_presence != null ||
                            personal_views != null ||
                            allow_skip_2fa != null ||
                            unstarted_status_group != null ||
                            tasks_in_multiple_lists != null ||
                            subtasks_in_multiple_lists != null ||
                            custom_fields_legacy_ordering != null ||
                            points_estimate_rollup != null ||
                            points_scale != null ||
                            owner_control_private_spaces != null ||
                            wip_limit != null ||
                            signed_attachments != null ||
                            hide_everything_calendar != null ||
                            emails_as_replies != null ||
                            nested_subtasks != null ||
                            nested_subtasks_level != null ||
                            quick_create_statuses != null ||
                            universal_search != null ||
                            time_in_status != null ||
                            microsoft_365_preview != null ||
                            extra_comment_reactions != null ||
                            giphy != null ||
                            task_relationships != null ||
                            hide_everything_board != null ||
                            custom_sprint_duration != null ||
                            workspace_settings?.authenticatedAttachments === true
                        ) {
                            let updateQuery = 'UPDATE task_mgmt.teams SET ';
                            const updateParams = [];

                            if (name) {
                                updateParams.push(name);
                                updateQuery += `name = $${updateParams.length}`;
                            }

                            if (admin_public_share_override != null) {
                                updateParams.push(admin_public_share_override);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `admin_public_share_override = $${updateParams.length}`;
                            }

                            if (task_relationships != null) {
                                updateParams.push(task_relationships);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `task_relationships = $${updateParams.length}`;
                            }

                            if (allow_skip_2fa != null) {
                                updateParams.push(allow_skip_2fa);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `allow_skip_2fa = $${updateParams.length}`;
                            }

                            if (color_theme) {
                                updateParams.push(color_theme);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `color_theme = $${updateParams.length}`;
                            }

                            if (automation_enabled != null) {
                                updateParams.push(automation_enabled);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `automation_enabled = $${updateParams.length}`;
                            }

                            if (ai_enabled != null) {
                                updateParams.push(ai_enabled);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `ai_enabled = $${updateParams.length}`;
                            }

                            if (user_presence != null) {
                                updateParams.push(user_presence);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `user_presence = $${updateParams.length}`;
                            }

                            if (enable_codox != null) {
                                updateParams.push(enable_codox);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `enable_codox = $${updateParams.length}`;
                            }

                            if (milestone_title != null) {
                                updateParams.push(milestone_title);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `milestone_title = $${updateParams.length}`;
                            }

                            if (dashboards_enabled != null) {
                                updateParams.push(dashboards_enabled);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `dashboards_enabled = $${updateParams.length}`;
                            }

                            if (can_add_guests != null) {
                                updateParams.push(can_add_guests);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `can_add_guests = $${updateParams.length}`;
                            }

                            if (can_remove_guests != null) {
                                updateParams.push(can_remove_guests);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `can_remove_guests = $${updateParams.length}`;
                            }

                            if (time_tracking_display_hours != null) {
                                updateParams.push(time_tracking_display_hours);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `time_tracking_display_hours = $${updateParams.length}`;
                            }
                            if (time_estimate_display_hours != null) {
                                updateParams.push(time_estimate_display_hours);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `time_estimate_display_hours = $${updateParams.length}`;
                            }
                            if (admin_global_delete != null) {
                                let admin_global_delete_val = admin_global_delete;

                                if (!para_result.entitlements[EntitlementName.CustomRoles].entitled) {
                                    admin_global_delete_val = false;
                                }
                                updateParams.push(admin_global_delete_val);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `admin_global_delete = $${updateParams.length}`;
                            }
                            if (require_2fa != null) {
                                updateParams.push(require_2fa);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `require_2fa = $${updateParams.length}`;
                            }
                            if (members_can_add_seats) {
                                updateParams.push(members_can_add_seats);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `members_can_add_seats = $${updateParams.length}`;
                            }
                            if (avatar_key) {
                                updateParams.push(avatar_key);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `avatar_key = $${updateParams.length}`;
                            } else if (unset_avatar) {
                                updateParams.push(null);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `avatar_key = $${updateParams.length}`;
                            }
                            if (milestone_avatar_key) {
                                updateParams.push(milestone_avatar_key);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `milestone_avatar_key = $${updateParams.length}`;
                            } else if (unset_milestone_avatar) {
                                updateParams.push(null);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `milestone_avatar_key = $${updateParams.length}`;
                            }
                            if (color) {
                                updateParams.push(color);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `color = $${updateParams.length}`;
                            }

                            if (disable_public_sharing != null) {
                                updateParams.push(disable_public_sharing);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `disable_public_sharing = $${updateParams.length}`;
                            }

                            if (owner_control_private_spaces != null) {
                                updateParams.push(owner_control_private_spaces);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `owner_control_private_spaces = $${updateParams.length}`;
                            }

                            if (signed_attachments != null || shouldDisableSignedAttachments === true) {
                                // if shouldDisableSignedAttachments is true, then we are disabling signed attachments
                                if (shouldDisableSignedAttachments) {
                                    updateParams.push(false);
                                } else {
                                    updateParams.push(signed_attachments);
                                }

                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `signed_attachments = $${updateParams.length}`;
                            }
                            if (disable_template_pub_sharing != null) {
                                updateParams.push(disable_template_pub_sharing);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `disable_template_pub_sharing = $${updateParams.length}`;
                            }

                            if (personal_views != null) {
                                updateParams.push(personal_views);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }

                                updateQuery += `personal_views = $${updateParams.length}`;
                            }

                            if (disable_never_expire_pub_links != null) {
                                updateParams.push(disable_never_expire_pub_links);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `disable_never_expire_pub_links = $${updateParams.length}`;
                            }

                            if (pub_links_max_year != null) {
                                updateParams.push(pub_links_max_year);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `pub_links_max_year = $${updateParams.length}`;
                            }

                            if (enable_recorder != null) {
                                updateParams.push(enable_recorder);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `enable_recorder = $${updateParams.length}`;
                            }

                            if (unstarted_status_group != null) {
                                updateParams.push(unstarted_status_group);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `unstarted_status_group = $${updateParams.length}`;
                            }

                            if (tasks_in_multiple_lists != null) {
                                updateParams.push(tasks_in_multiple_lists);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `tasks_in_multiple_lists = $${updateParams.length}`;

                                if (tasks_in_multiple_lists === false) {
                                    subtasks_in_multiple_lists = false;
                                }
                            }

                            if (subtasks_in_multiple_lists != null) {
                                updateParams.push(subtasks_in_multiple_lists);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `subtasks_in_multiple_lists = $${updateParams.length}`;
                            }

                            if (points_estimate_rollup != null) {
                                updateParams.push(points_estimate_rollup);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `points_estimate_rollup = $${updateParams.length}`;
                            }

                            if (points_scale != null && Array.isArray(points_scale)) {
                                if (points_scale.length === 0) {
                                    updateParams.push(null);
                                } else {
                                    updateParams.push(points_scale);
                                }

                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `points_scale = $${updateParams.length}`;
                            }

                            if (custom_fields_legacy_ordering != null) {
                                updateParams.push(custom_fields_legacy_ordering);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `custom_fields_legacy_ordering = $${updateParams.length}`;
                            }

                            if (docs_home != null) {
                                updateParams.push(docs_home);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `docs_home = $${updateParams.length}`;
                            }

                            if (live_view != null) {
                                updateParams.push(live_view);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `live_view = $${updateParams.length}`;
                            }

                            if (slack_channel) {
                                updateParams.push(slack_channel);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `slack_channel = $${updateParams.length}`;
                            }
                            if (using_github != null) {
                                updateParams.push(using_github);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `using_github = $${updateParams.length}`;
                            }
                            if (using_gitlab != null) {
                                updateParams.push(using_gitlab);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `using_gitlab = $${updateParams.length}`;
                            }
                            if (setup_step != null) {
                                updateParams.push(setup_step);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `setup_step = $${updateParams.length}`;

                                if (setup_step === 'completed') {
                                    updateParams.push(new Date().getTime());
                                    updateQuery += `, onboarding_complete_time = $${updateParams.length}`;
                                }
                            }
                            if (previous_tools != null) {
                                updateParams.push(previous_tools);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `previous_tools = $${updateParams.length}`;
                            }
                            if (default_payment_token != null) {
                                updateParams.push(default_payment_token);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `default_payment_token = $${updateParams.length}`;
                            }
                            if (personal_team != null) {
                                updateParams.push(personal_team);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `personal_team = $${updateParams.length}`;
                            }
                            if (should_encrypt != null) {
                                updateParams.push(should_encrypt);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `should_encrypt = $${updateParams.length}`;
                            }
                            if (number_of_team_users != null) {
                                updateParams.push(number_of_team_users);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `number_of_team_users = $${updateParams.length}`;
                            }
                            if (hours_per_day != null) {
                                updateParams.push(hours_per_day);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `hours_per_day = $${updateParams.length}`;
                            }
                            if (address != null) {
                                updateParams.push(address);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `address = $${updateParams.length}`;
                            }
                            if (stored_promo_code != null) {
                                if (stored_promo_code === 'none') {
                                    updateParams.push(null);
                                } else {
                                    updateParams.push(stored_promo_code);
                                }
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `stored_promo_code = $${updateParams.length}`;
                            }
                            if (hosted_secret != null) {
                                updateParams.push(hosted_secret);
                                if (updateParams.length > 1) {
                                    updateQuery += ', ';
                                }
                                updateQuery += `hosted_secret = $${updateParams.length}`;
                            }

                            if (time_tracking_rollup != null) {
                                updateParams.push(time_tracking_rollup);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `time_tracking_rollup = $${updateParams.length}`;
                            }

                            if (estimates_per_assignee != null) {
                                updateParams.push(estimates_per_assignee);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `estimates_per_assignee = $${updateParams.length}`;
                            }

                            if (points_per_assignee != null) {
                                updateParams.push(points_per_assignee);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `points_per_assignee = $${updateParams.length}`;
                            }

                            if (custom_sprint_duration != null) {
                                updateParams.push(custom_sprint_duration);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `custom_sprint_duration = $${updateParams.length}`;
                            }

                            if (wip_limit != null) {
                                updateParams.push(wip_limit);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `wip_limit = $${updateParams.length}`;
                            }

                            if (hide_everything_calendar != null) {
                                updateParams.push(hide_everything_calendar);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `hide_everything_calendar = $${updateParams.length}`;
                            }

                            if (hide_everything_board != null) {
                                updateParams.push(hide_everything_board);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `hide_everything_board = $${updateParams.length}`;
                            }

                            if (emails_as_replies != null) {
                                updateParams.push(emails_as_replies);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `emails_as_replies = $${updateParams.length}`;
                            }

                            if (time_estimate_rollup != null) {
                                updateParams.push(time_estimate_rollup);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `time_estimate_rollup = $${updateParams.length}`;
                            }

                            if (lineup != null) {
                                updateParams.push(lineup);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `lineup = $${updateParams.length}`;
                            }

                            if (threaded_comments != null) {
                                updateParams.push(threaded_comments);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `threaded_comments = $${updateParams.length}`;
                            }

                            if (nested_subtasks != null) {
                                if (nested_subtasks === false) {
                                    updateParams.push(null);
                                    if (updateParams.length > 1) {
                                        updateQuery += `, `;
                                    }
                                    updateQuery += `nested_subtasks_level = $${updateParams.length}`;
                                }
                                updateParams.push(nested_subtasks);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `nested_subtasks = $${updateParams.length}`;
                            }

                            if (nested_subtasks_level != null && nested_subtasks !== false) {
                                updateParams.push(Math.min(nested_subtasks_level, 7)); // max nested levels is 7
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `nested_subtasks_level = $${updateParams.length}`;
                            }

                            if (quick_create_statuses != null) {
                                updateParams.push(quick_create_statuses);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `quick_create_statuses = $${updateParams.length}`;
                            }

                            if (universal_search != null) {
                                updateParams.push(universal_search);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `universal_search = $${updateParams.length}`;
                            }

                            if (time_in_status != null) {
                                updateParams.push(time_in_status);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `time_in_status = $${updateParams.length}`;
                            }
                            if (microsoft_365_preview != null) {
                                updateParams.push(microsoft_365_preview);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `microsoft_365_preview = $${updateParams.length}`;
                            }

                            if (extra_comment_reactions != null) {
                                updateParams.push(extra_comment_reactions);
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }
                                updateQuery += `extra_comment_reactions = $${updateParams.length}`;
                            }

                            if (giphy != null) {
                                if (updateParams.length > 1) {
                                    updateQuery += `, `;
                                }

                                updateQuery += ` giphy = $${updateParams.push(giphy)}`;
                            }

                            updateParams.push(team_id);
                            updateQuery += ` WHERE id = $${updateParams.length}`;

                            if (updateParams.length > 1) {
                                queries.push({
                                    query: updateQuery,
                                    params: updateParams,
                                });
                            }
                        }

                        if (setup_step && setup_step === 'completed') {
                            saveMadeTeam(userid);
                        }

                        if (
                            time_tracking_default_to_billable != null &&
                            [TimeTrackingDefaultToBillable.True, TimeTrackingDefaultToBillable.False].includes(
                                time_tracking_default_to_billable
                            )
                        ) {
                            queries.push(
                                TimeTrackingDefaultToBillableWorkspaceClickapp.getUpsertQuery(
                                    team_id,
                                    { time_tracking_default_to_billable },
                                    team_id
                                )
                            );
                        }

                        if (task_duration != null) {
                            queries.push(
                                TaskDurationWorkspaceClickapp.getUpsertQuery(team_id, { task_duration }, team_id)
                            );
                        }

                        if (chat_enabled != null && shouldEnableChat(team_id)) {
                            const update_payload = { chat_enabled };

                            // If chat is being disabled, we need to preserve any current chat_context that exists
                            if (chat_enabled === false) {
                                const existingClickapp = await clickappsService.buildResourceClickapps(
                                    team_id,
                                    ParentType.Team
                                );

                                if (existingClickapp?.chat_context) {
                                    update_payload.chat_context = existingClickapp.chat_context;
                                }
                            } else if (chat_context?.source) {
                                update_payload.chat_context = { source: chat_context.source };
                            }
                            queries.push(
                                ChatEnablementWorkspaceClickapp.getUpsertQuery(team_id, update_payload, team_id)
                            );
                        }

                        if (notetaker_enabled != null) {
                            queries.push(
                                NotetakerEnablementWorkspaceClickapp.getUpsertQuery(
                                    team_id,
                                    { notetaker_enabled },
                                    team_id
                                )
                            );
                        }

                        if (default_project != null) {
                            if (default_project === 'none') {
                                default_project = null;
                            }
                            queries.push(
                                {
                                    query: `
                                        INSERT INTO task_mgmt.default_projects(userid, team, default_project) VALUES($1, $2, $3)
                                        ON CONFLICT DO NOTHING
                                    `,
                                    params: [userid, team_id, default_project],
                                },
                                {
                                    query: 'UPDATE task_mgmt.default_projects SET default_project = $3 WHERE userid = $1 AND team = $2',
                                    params: [userid, team_id, default_project],
                                }
                            );
                        }

                        if (sprint_settings) {
                            const sprint_query = teamHelper.generateSprintQuery(team_id, sprint_settings);
                            queries.push(...sprint_query);
                        }

                        if (!client.versionUpdates) {
                            client.versionUpdates = [];
                        }

                        client.query('BEGIN', async beginErr => {
                            if (beginErr) {
                                db.rollback(client, done);
                                logger.error({
                                    msg: 'Failed to begin',
                                    err: beginErr,
                                    status: 500,
                                    ECODE: 'TEAM_015',
                                });
                                cb({
                                    err: 'Internal server error',
                                    status: 500,
                                    ECODE: 'TEAM_015',
                                });
                            } else {
                                client.versionUpdates.push(
                                    hierarchyObjectVersionUpdateRequests({
                                        objectId: team_id,
                                        objectType: ObjectType.WORKSPACE,
                                        workspaceId: team_id,
                                        operation: OperationType.UPDATE,
                                        fanout: false,
                                        changes: ovmWSSettingsChanges?.length ? ovmWSSettingsChanges : undefined,
                                    })
                                );

                                try {
                                    const team = para_result.team_info.rows[0];

                                    client.versionUpdates.push(...(await getFieldUpdateEvents(team_id, options, team)));
                                } catch (fieldEventsError) {
                                    db.rollback(client, done);
                                    logger.error({
                                        msg: 'Failed to get fields to update on team edit',
                                        err: fieldEventsError,
                                        status: 500,
                                        ECODE: TeamErrors.FailedToGetFieldsToUpdate,
                                    });
                                    cb({
                                        err: 'Internal server error',
                                        status: 500,
                                        ECODE: TeamErrors.FailedToGetFieldsToUpdate,
                                    });
                                    return;
                                }

                                async.each(
                                    queries,
                                    (queryObj, each_cb) => {
                                        client.query(queryObj.query, queryObj.params, queryErr => {
                                            if (queryErr) {
                                                each_cb(queryErr);
                                            } else {
                                                each_cb();
                                            }
                                        });
                                    },
                                    eachErr => {
                                        if (eachErr) {
                                            db.rollback(client, done);
                                            if (!eachErr.err) {
                                                logger.error({
                                                    msg: 'Failed update team',
                                                    team_id,
                                                    err: eachErr,
                                                    status: 500,
                                                    ECODE: 'TEAM_016',
                                                });
                                            }
                                            cb({
                                                err: eachErr.err || 'Internal server error',
                                                status: eachErr.status || 500,
                                                ECODE: eachErr.ECODE || 'TEAM_016',
                                            });
                                        } else {
                                            async.series(
                                                [
                                                    series_cb => {
                                                        series_cb();
                                                        if (
                                                            old_avatar_key &&
                                                            ((avatar_key && old_avatar_key !== avatar_key) ||
                                                                unset_avatar)
                                                        ) {
                                                            s3.deleteObjects(
                                                                {
                                                                    Bucket: config.aws.bucket,
                                                                    Delete: {
                                                                        Objects: [
                                                                            {
                                                                                Key: old_avatar_key,
                                                                            },
                                                                        ],
                                                                    },
                                                                },
                                                                s3err => {
                                                                    if (s3err) {
                                                                        logger.error({
                                                                            msg: 'Failed to delete old team avatar',
                                                                            err: s3err,
                                                                        });
                                                                    }
                                                                }
                                                            );
                                                        }
                                                    },
                                                    series_cb => {
                                                        series_cb();
                                                        if (
                                                            old_milestone_avatar_key &&
                                                            ((milestone_avatar_key &&
                                                                old_milestone_avatar_key !== milestone_avatar_key) ||
                                                                unset_milestone_avatar)
                                                        ) {
                                                            s3.deleteObjects(
                                                                {
                                                                    Bucket: config.aws.bucket,
                                                                    Delete: {
                                                                        Objects: [
                                                                            {
                                                                                Key: old_milestone_avatar_key,
                                                                            },
                                                                        ],
                                                                    },
                                                                },
                                                                s3err => {
                                                                    if (s3err) {
                                                                        logger.error({
                                                                            msg: 'Failed to delete old milestone avatar',
                                                                            err: s3err,
                                                                        });
                                                                    }
                                                                }
                                                            );
                                                        }
                                                    },
                                                ],
                                                async seriesErr => {
                                                    if (seriesErr) {
                                                        db.rollback(client, done);
                                                        cb(seriesErr);
                                                        return;
                                                    }
                                                    let events;
                                                    const ovm = getObjectVersionManager();
                                                    try {
                                                        const txClient = new TransactionClientImpl(client, ovm);
                                                        events = await ovm.updateVersions(
                                                            txClient,
                                                            client.versionUpdates
                                                        );
                                                    } catch (ovmErr) {
                                                        logger.error({
                                                            msg: 'Failed to write OVM update',
                                                            OVM_CRITICAL: false,
                                                            ECODE: 'OVM_WS_137',
                                                            err: ovmErr,
                                                        });
                                                        db.rollback(client, done);
                                                        cb(ovmErr);
                                                        return;
                                                    }

                                                    client.query('COMMIT', async commitErr => {
                                                        if (commitErr) {
                                                            db.rollback(client, done);
                                                            logger.error({
                                                                msg: 'Failed to commit',
                                                                err: commitErr,
                                                                status: 500,
                                                                ECODE: 'TEAM_017',
                                                            });
                                                            cb({
                                                                err: 'Internal server error',
                                                                status: 500,
                                                                ECODE: 'TEAM_017',
                                                            });
                                                        } else {
                                                            done();

                                                            await ovm.notifyChanges(events).catch(() => {});
                                                            notifySettingsChanges(team_id, userid, settings_diff).catch(
                                                                () => {}
                                                            );

                                                            _getTeam(
                                                                userid,
                                                                team_id,
                                                                {
                                                                    include_removed: false,
                                                                    include_plan_info: false,
                                                                },
                                                                cb
                                                            );

                                                            if (require_2fa != null) {
                                                                const twofaTeamValue = get2faTeamStatus(
                                                                    require_2fa,
                                                                    allow_skip_2fa
                                                                );
                                                                log2faPolicyChange({
                                                                    workspaceId: team_id,
                                                                    userid,
                                                                    twofaStatus: twofaTeamValue,
                                                                    previousTwofaStatus: current2faStatus,
                                                                });
                                                            }

                                                            if (should_encrypt != null) {
                                                                storeShouldEncrypt(team_id, should_encrypt);
                                                            }

                                                            if (unstarted_status_group === false) {
                                                                teamHelper.convertUnstartedToCustom(team_id);
                                                            }

                                                            if (tasks_in_multiple_lists === false) {
                                                                teamHelper.deleteMultiSubcatTasks(team_id);
                                                            }

                                                            if (subtasks_in_multiple_lists === false) {
                                                                teamHelper.deleteMultiSubcatSubtasks(team_id);
                                                            }

                                                            if (automation_enabled === false) {
                                                                teamHelper.turnOffAllAutomations(team_id);
                                                            }

                                                            if (old_points_scale && points_scale && team_id) {
                                                                pointsMod.changePoints(
                                                                    team_id,
                                                                    old_points_scale,
                                                                    points_scale,
                                                                    points_remap,
                                                                    points_estimate_rollup
                                                                );
                                                            }

                                                            if (disable_template_pub_sharing && team_id) {
                                                                disablePubTemplates(team_id);
                                                            }

                                                            const validSettingsToLog = {
                                                                admin_public_share_override,
                                                                disable_template_pub_sharing,
                                                                disable_never_expire_pub_links,
                                                                disable_public_sharing,
                                                                owner_control_private_spaces,
                                                                pub_links_max_year,
                                                            };

                                                            mapAdvancedSettingsToAuditLog(
                                                                validSettingsToLog,
                                                                Number(team_id)
                                                            );

                                                            try {
                                                                // Notifies SD Platform that workspace was changed.
                                                                sdTouchWorkspace(team_id).catch(() => {
                                                                    /* noop */
                                                                });
                                                            } catch (e) {
                                                                // noop
                                                            }
                                                        }
                                                    });
                                                }
                                            );
                                        }
                                    }
                                );
                            }
                        });
                    }
                );
            });
        }
    );
}

export { _editTeam };

export function editTeam(req, resp) {
    const userid = req.decoded_token.user;
    const { team_id } = req.params;
    const {
        owner,
        name,
        slack_channel,
        default_project,
        using_github,
        using_gitlab,
        setup_step,
        previous_tools,
        default_payment_token,
        color_theme,
        personal_team,
        should_encrypt,
        hours_per_day,
        stored_promo_code,
        address,
        require_2fa,
        members_can_add_seats,
        number_of_team_users,
        time_tracking_default_to_billable,
        time_tracking_display_hours,
        time_estimate_display_hours,
        enable_codox,
        dashboards_enabled,
        can_add_guests,
        can_remove_guests,
        hosted_secret,
        disable_public_sharing,
        disable_template_pub_sharing,
        enable_recorder,
        docs_home,
        live_view,
        admin_public_share_override,
        automation_enabled,
        ai_enabled,
        disable_never_expire_pub_links,
        pub_links_max_year,
        estimates_per_assignee,
        lineup,
        threaded_comments,
        milestone_title,
        user_presence,
        allow_skip_2fa,
        personal_views,
        unstarted_status_group,
        tasks_in_multiple_lists,
        subtasks_in_multiple_lists,
        custom_fields_legacy_ordering,
        points_estimate_rollup,
        points_scale,
        points_per_assignee,
        wip_limit,
        hide_everything_calendar,
        emails_as_replies,
        nested_subtasks,
        nested_subtasks_level,
        quick_create_statuses,
        universal_search,
        time_in_status,
        microsoft_365_preview,
        extra_comment_reactions,
        task_relationships,
        hide_everything_board,
        custom_sprint_duration,
    } = req.body;
    let { unset_avatar, color } = req.body;
    let avatar = null;
    let milestone_avatar = null;

    if (req.files && req.files.avatar) {
        [avatar] = req.files.avatar;
    }

    if (req.files && req.files.milestone_avatar) {
        [milestone_avatar] = req.files.milestone_avatar;
    }

    if (name && name.length > 50) {
        resp.status(400).send({
            err: 'Team name too long',
            ECODE: 'TEAM_100',
        });
        return;
    }

    if (unset_avatar === 'false') {
        unset_avatar = false;
    }

    if (color && !input.validateColor(color)) {
        color = null;
    }

    logger.info({
        msg: 'Edit team request',
        userid,
        team_id,
        name,
        color,
        slack_channel,
        unset_avatar,
        personal_team,
        require_2fa,
    });

    _editTeam(
        userid,
        {
            team_id,
            name,
            owner,
            avatar,
            color,
            slack_channel,
            unset_avatar,
            default_project,
            using_github,
            using_gitlab,
            setup_step,
            previous_tools,
            default_payment_token,
            color_theme,
            personal_team,
            should_encrypt,
            hours_per_day,
            stored_promo_code,
            address,
            checkAdd: true,
            require_2fa,
            members_can_add_seats,
            number_of_team_users,
            time_tracking_default_to_billable,
            time_tracking_display_hours,
            time_estimate_display_hours,
            enable_codox,
            dashboards_enabled,
            can_add_guests,
            can_remove_guests,
            hosted_secret,
            disable_public_sharing,
            disable_template_pub_sharing,
            enable_recorder,
            docs_home,
            live_view,
            admin_public_share_override,
            automation_enabled,
            ai_enabled,
            disable_never_expire_pub_links,
            pub_links_max_year,
            estimates_per_assignee,
            lineup,
            threaded_comments,
            milestone_avatar,
            milestone_title,
            user_presence,
            allow_skip_2fa,
            personal_views,
            unstarted_status_group,
            tasks_in_multiple_lists,
            subtasks_in_multiple_lists,
            custom_fields_legacy_ordering,
            points_estimate_rollup,
            points_scale,
            points_per_assignee,
            wip_limit,
            hide_everything_calendar,
            emails_as_replies,
            nested_subtasks,
            nested_subtasks_level,
            quick_create_statuses,
            universal_search,
            time_in_status,
            microsoft_365_preview,
            extra_comment_reactions,
            task_relationships,
            hide_everything_board,
            custom_sprint_duration,
        },
        (err, result) => {
            if (err) {
                resp.status(err.status).send({
                    err: err.err,
                    ECODE: err.ECODE,
                    bt_err: err.bt_err,
                });
            } else {
                resp.status(200).send(result);
            }
        }
    );
}

function _getTeams(userid, invite, include_removed, options, cb) {
    getTeamMod._getTeams(userid, invite, include_removed, options, cb);
}

export { _getTeams };

/**
 * @param userid
 * @param invite
 * @param include_removed
 * @param options
 * @returns {Promise<any[]>}
 */
export const getTeamAsync = (userid, invite, include_removed, options) =>
    new Promise((resolve, reject) => {
        _getTeams(userid, invite, include_removed, options, (err, result) => {
            if (err) {
                reject(err);
            } else {
                resolve(result);
            }
        });
    });

function _getTeamsWithProjects(userid, invite, include_removed, options, cb) {
    const {
        from_create_user,
        exclude_team_members,
        collect_workspace_settings,
        collect_sharded_workspace_settings,
        shard_id,
        auth_token,
    } = options;

    const opts = {
        ...getTeamApiIncludeFields(userid, TeamApiKind.GetTeams, options),
        from_create_user,
        exclude_team_members,
        collect_workspace_settings,
        collect_sharded_workspace_settings,
        shard_id,
        auth_token,
    };

    async.parallel(
        {
            teams(para_cb) {
                _getTeams(userid, invite, include_removed, opts, (teamsErr, teams) => {
                    if (teamsErr) {
                        para_cb(teamsErr);
                        return;
                    }
                    para_cb(null, teams);
                });
            },

            projects(para_cb) {
                if (shouldExcludeWorkspaceData()) {
                    para_cb(null, []);
                    return;
                }

                project._getProjects(
                    userid,
                    null,
                    { include_removed, skip_generating_statuses: true, ...options },
                    (projectErr, projects) => {
                        if (projectErr) {
                            para_cb(projectErr);
                            return;
                        }
                        para_cb(null, projects);
                    }
                );
            },
        },
        (err, result) => {
            if (err) {
                cb(err);
                return;
            }

            const team_projects = {};
            result.projects.forEach(row => {
                if (!team_projects[row.team.id]) {
                    team_projects[row.team.id] = [];
                }
                team_projects[row.team.id].push(row);
            });

            result.teams.forEach(team => {
                team.projects = team_projects[team.id] || [];
                team.projects.sort((a, b) => a.project_orderindex - b.project_orderindex);
            });

            cb(null, result.teams);
        }
    );
}

export { _getTeamsWithProjects as _getTeamswithProjects };

export function getTeams(req, resp) {
    const userid = req.decoded_token.user;
    let { invite } = req.query;
    const includeFields = getTeamApiIncludeFields(userid, TeamApiKind.GetTeams, req.query);
    const { include_projects, include_removed } = includeFields;

    // when collecting settings from remote shards, we use this endpoint (GET /team/v1/team)
    // with this parameter and take only workspace_settings, but skip other information
    const only_settings = req.query.only_settings || false;

    // we might collect "workspace_settings" from specific shard.
    // that is not a real need, but a utility/testing option
    const { shard_id } = req.query;

    // by request, we have to collect workspace_settings for all workspaces, located on other shards
    const collect_workspace_settings = req.query.collect_workspace_settings ?? false;
    const collect_sharded_workspace_settings = req.query.collect_sharded_workspace_settings ?? false;

    if (invite == null) {
        invite = 0;
    } else if (invite === 'false' || invite === false) {
        invite = -1;
    } else {
        invite = 1;
    }

    logger.info({
        msg: 'Get teams request',
        userid,
        invite,
        ...includeFields,
    });

    // TODO: we need to pass some authentication params down, to be able to collect settings from remote
    //       (note!) This would be sensitive to "incremental-authentication"
    //       and might (should) fail if not authenticated for remote workspace
    //       !! collecting cross-workspace-data is a potential security risk!

    if (include_projects) {
        // `mobile` is passed down but is never used!
        const opts = {
            ...includeFields,
            collect_workspace_settings,
            collect_sharded_workspace_settings,
            shard_id,
            auth_token: req.headers.authorization,
            allowArchivedWorkspaces: true,
            include_inaccessible_workspaces: true,
        };
        _getTeamsWithProjects(userid, invite, include_removed, opts, async (err, result) => {
            if (err) {
                resp.status(err.status).send({
                    err: err.err,
                    ECODE: err.ECODE,
                });
            } else {
                resp.status(200).send(result);
            }
        });
    } else {
        // when collecting workspace_settings from remote shard, we do not need to collect
        // all workspace data, but minimal subset!

        // actually, that might be beneficial to collect all team info from remote shard.
        // but that would require to collect teamId -> shard_info -> teamInfo
        // which might require more code changes.
        const opts = {
            ...includeFields,
            collect_workspace_settings,
            collect_sharded_workspace_settings,
            only_settings,
            shard_id,
            auth_token: req.headers.authorization,
            include_inaccessible_workspaces: true,
        };
        // XXX should we pass mobile here? it looks different with the code above...

        // (Note!) when collecting workspace_settings from remote shard,
        //         on every shard we would get entire list of team_ids and would get shard info for each team
        //         and then we would filter only teams for that shard
        //         but! we would send requests only to that shards, where user has some teams.
        //         that would be mostly small list of shards.

        _getTeams(userid, invite, include_removed, opts, async (err, result) => {
            if (err) {
                resp.status(err.status).send({
                    err: err.err,
                    ECODE: err.ECODE,
                });
            } else {
                resp.status(200).send(result);
            }
        });
    }
}

function _incrementBillingPopupShown(userid, team_id, cb) {
    access.checkAccessTeam(userid, team_id, {}, accessErr => {
        if (accessErr) {
            cb(accessErr);
            return;
        }

        const query =
            'UPDATE task_mgmt.teams SET billingExceptionPopupDismissed = billingExceptionPopupDismissed + 1 WHERE id = $1';
        const params = [team_id];
        db.writeQuery(query, params, err => {
            if (err) {
                logger.error({
                    msg: 'Failed to update billing exception popup dismissed',
                    err,
                    status: 500,
                    ECODE: 'TEAM_035',
                });
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'TEAM_035',
                });
            } else {
                cb();
            }
        });
    });
}

export function incrementBillingPopupShown(req, resp) {
    const { userid } = req.decoded_token;
    const { team_id } = req.params;

    _incrementBillingPopupShown(userid, team_id, (err, result) => {
        if (err) {
            resp.status(err.status).send({ err: err.err, ECODE: err.ECODE });
        } else {
            resp.status(200).send(result);
        }
    });
}

function _convertTeamToPersonal(userid, team_id, options, cb) {
    access.checkAccessTeam(userid, team_id, { permissions: [] }, accessErr => {
        if (accessErr) {
            cb(accessErr);
            return;
        }

        db.writeQuery(
            'UPDATE task_mgmt.teams SET personal_team = $1 WHERE id = $2 AND owner = $3 AND NOT EXISTS (SELECT userid FROM task_mgmt.team_members WHERE team_id = $2 AND deleted = false AND userid != $3)',
            [options.personal, team_id, userid],
            err => {
                if (err) {
                    logger.error({ msg: 'Failed to set team to personal', status: 500, ECODE: 'TEAM_075', err });
                    cb({ err: 'Internal server error', status: 500, ECODE: 'TEAM_075' });
                } else {
                    cb(null, {});
                }
            }
        );
    });
}

export function convertTeamToPersonal(req, resp) {
    const userid = req.decoded_token.user;
    const { team_id } = req.params;
    const { personal } = req.body;

    _convertTeamToPersonal(userid, team_id, { personal }, (err, result) => {
        if (err) {
            resp.status(err.status).send({ err: err.err, ECODE: err.ECODE });
        } else {
            resp.status(200).send(result);
        }
    });
}

function getPublicTasks(userid, team_id, options, cb) {
    const params = [team_id];

    let paging_query = ``;
    if (options.task_start_public_time && options.task_start_id) {
        params.push(options.task_start_public_time);
        params.push(options.task_start_id);
        paging_query += `
            (
                items.made_public_time < $${params.length - 1}
                OR
                (
                    items.made_public_time = $${params.length - 1}
                    AND
                    items.id < $${params.length}
                )
                OR items.made_public_time IS NULL
            ) AND
        `;
    } else {
        params.push(new Date().getTime());
        paging_query += ` (items.made_public_time <= $${params.length} OR items.made_public_time IS NULL) AND `;
    }

    let search_query = ``;
    if (options.search) {
        params.push(`%${options.search.toLowerCase()}%`);
        search_query = ` LOWER(items.name) LIKE $${params.length} AND`;
    }

    const pubTaskquery = `
        SELECT
            items.id,
            items.made_public_time,
            users.id as userid,
            users.email,
            users.username,
            users.color,
            users.profile_picture_key
        FROM
            task_mgmt.items
            JOIN task_mgmt.subcategories ON subcategories.id = items.subcategory AND subcategories.deleted = FALSE
            JOIN task_mgmt.categories ON subcategories.category = categories.id AND categories.deleted = FALSE
            JOIN task_mgmt.projects ON categories.project_id = projects.id AND projects.deleted = FALSE
            JOIN task_mgmt.teams ON projects.team = teams.id
            LEFT OUTER JOIN task_mgmt.users ON items.made_public_by = users.id
        WHERE
            teams.id = $1 AND
            items.deleted = FALSE AND
            items.template = FALSE AND
            items.archived = FALSE AND
            ${paging_query}
            ${search_query}
            items.public = TRUE
        ORDER BY items.made_public_time DESC, items.made_public_time NULLS LAST, items.id DESC
        LIMIT $${params.length + 1}
    `;

    params.push(config.page_length.public_items + 1);

    db.replicaQuery(pubTaskquery, params, (taskErr, taskResult) => {
        if (taskErr) {
            cb(taskErr);
            return;
        }

        const { rows: publicTasks = [] } = taskResult;

        if (publicTasks.length === 0) {
            cb(null, publicTasks);
            return;
        }

        _getItems(
            publicTasks.map(task => task.id),
            { userid, replica: true },
            (getErr, getResult) => {
                if (getErr) {
                    cb(getErr);
                    return;
                }

                const tasks = publicTasks.map(task => {
                    const found_task = getResult.find(tsk => tsk.id === task.id);

                    let new_task;

                    if (found_task) {
                        new_task = {
                            id: found_task.id,
                            made_public_time: found_task.made_public_time,
                            name: found_task.name,
                            made_public_by: task.userid ? formatUser({ ...task, id: task.userid }) : null,
                            type: 'task',
                            task: found_task,
                        };
                    } else {
                        new_task = {
                            id: task.id,
                            made_public_time: task.made_public_time,
                            name: null,
                            made_public_by: task.userid ? formatUser({ ...task, id: task.userid }) : null,
                            type: 'task',
                            task: null,
                        };
                    }

                    return new_task;
                });

                cb(null, tasks);
            }
        );
    });
}

function getPublicViews(userid, team_id, options, cb) {
    const params = [team_id, config.page_length.public_items + 1];

    let paging_query = ``;
    if (options.view_start_id && options.view_start_public_time) {
        params.push(options.view_start_public_time);
        params.push(options.view_start_id);
        paging_query += `
            (
                views.made_public_time < $${params.length - 1}
                OR
                (
                    views.made_public_time = $${params.length - 1}
                    AND
                    views.view_id < $${params.length}
                )
                OR views.made_public_time IS NULL
            ) AND
        `;
    } else {
        params.push(new Date().getTime());
        paging_query += `(views.made_public_time <= $${params.length} OR views.made_public_time IS NULL) AND `;
    }

    let search_query = ``;
    if (options.search) {
        params.push(`%${options.search.toLowerCase()}%`);
        search_query = ` LOWER(views.name) LIKE $${params.length} AND `;
    }

    const pubViewsQuery = `
        SELECT
            views.view_id,
            views.made_public_time,
            users.id as userid,
            users.email,
            users.username,
            users.color,
            users.profile_picture_key
        FROM task_mgmt.views
            LEFT OUTER JOIN task_mgmt.users ON views.made_public_by = users.id
        WHERE
            team_id = $1 AND
            views.deleted = FALSE AND
            ${paging_query}
            ${search_query}
            views.public = TRUE
        ORDER BY views.made_public_time DESC, views.made_public_time NULLS LAST, views.view_id DESC
        LIMIT $2
    `;

    db.replicaQuery(pubViewsQuery, params, async (viewErr, viewResult) => {
        if (viewErr) {
            cb(viewErr);
            return;
        }

        let { rows: publicViews = [] } = viewResult;

        if (publicViews.length === 0) {
            cb(null, publicViews);
            return;
        }

        try {
            const pubLinksMaxYearEnabled = await db.replicaQueryAsync(
                `SELECT id FROM task_mgmt.teams WHERE id = $1 AND pub_links_max_year IS TRUE`,
                [team_id]
            );
            if (pubLinksMaxYearEnabled.rows.length) {
                const nextYear = new Date(new Date().setFullYear(new Date().getFullYear() + 1)).getTime();
                publicViews = publicViews.filter(
                    publicView => moment(nextYear).diff(moment(+publicView.made_public_time), 'years') <= 1
                );
            }
        } catch (err) {
            cb(err);
            return;
        }

        viewsCRUD._getViews(
            userid,
            {
                in_view_ids: publicViews.map(view => view.view_id),
                parent_id: team_id,
                skip_standard: true,
                parent_type: config.views.parent_types.team,
            },
            (getErr, getResult) => {
                if (getErr) {
                    cb(getErr);
                    return;
                }

                const views = publicViews.map(view => {
                    const found_view = getResult.views.find(vw => vw.id === view.view_id);

                    let new_view;
                    if (found_view) {
                        new_view = {
                            id: found_view.id,
                            made_public_time: found_view.made_public_time,
                            name: found_view.name,
                            made_public_by: view.userid ? formatUser({ ...view, id: view.userid }) : null,
                            type: 'view',
                            view: found_view,
                        };
                    } else {
                        new_view = {
                            id: view.view_id,
                            made_public_time: view.made_public_time,
                            name: null,
                            made_public_by: view.userid ? formatUser({ ...view, id: view.userid }) : null,
                            type: 'view',
                            view: null,
                        };
                    }

                    return new_view;
                });

                cb(null, views);
            }
        );
    });
}

function getTeamPublicItems(userid, team_id, options, cb) {
    if (!team_id) {
        cb({
            err: 'Team id needed',
            status: 500,
            ECODE: 'TEAM_200',
        });
        return;
    }

    access.checkAccessTeam(userid, team_id, {}, async (err, result) => {
        if (err) {
            cb(err);
            return;
        }

        const teamError = {
            err: 'Must be on enterprise plan',
            status: 401,
            ECODE: 'TEAM_201',
        };

        try {
            const isEntitled = await entitlementService.checkEntitlement(team_id, EntitlementName.TeamPublicItems);

            if (!isEntitled) {
                cb(teamError);
                return;
            }
        } catch (entitlementErr) {
            cb(entitlementErr);
            return;
        }

        async.parallel(
            {
                tasks(para_cb) {
                    getPublicTasks(userid, team_id, options, para_cb);
                },
                views(para_cb) {
                    getPublicViews(userid, team_id, options, para_cb);
                },
            },
            (paraErr, paraResult) => {
                if (paraErr) {
                    cb(paraErr);
                    return;
                }

                let has_more = false;
                const sort_value = config.public_sort_value;
                const page = paraResult.tasks.concat(paraResult.views).sort((a, b) => {
                    if (a.made_public_time !== b.made_public_time) {
                        return b.made_public_time - a.made_public_time;
                    }

                    if (a.made_public_time === null || b.made_public_time === null) {
                        return -1;
                    }

                    if (a.type === b.type) {
                        return b.id > a.id ? -1 : 1;
                    }

                    return sort_value[a.type] - sort_value[b.type];
                });

                if (page.length > config.page_length.public_items) {
                    has_more = true;
                }

                let paging = {};
                if (page.length > 0) {
                    paging = {
                        tasks: null,
                        views: null,
                    };

                    for (let i = page.length - 1; i >= 0; i -= 1) {
                        if (paging.task && paging.views) {
                            return;
                        }

                        if (!paging.tasks && page[i].type === 'task' && page[i].made_public_time) {
                            paging.tasks = {
                                start_id: page[i].id,
                                start_public_time: page[i].made_public_time,
                            };
                        }

                        if (!paging.views && page[i].type === 'view' && page[i].made_public_time) {
                            paging.views = {
                                start_id: page[i].id,
                                start_public_time: page[i].made_public_time,
                            };
                        }
                    }
                }

                if (!paging.tasks && options.task_start_id) {
                    paging.tasks = {
                        start_id: options.task_start_id,
                        start_public_time: options.task_start_public_time,
                    };
                }

                if (!paging.views && options.view_start_id) {
                    paging.views = {
                        start_id: options.view_start_id,
                        start_public_time: options.view_start_public_time,
                    };
                }

                cb(null, { has_more, data: page, paging });
            }
        );
    });
}

export function getTeamPublicItemsRoute(req, resp, next) {
    const userid = req.decoded_token.user;
    const { team_id } = req.params;
    const { query } = req;

    getTeamPublicItems(userid, team_id, query, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    });
}

function disablePublicLinks(userid, team_id, options, cb) {
    if (!team_id) {
        cb({
            err: 'Team id needed',
            status: 500,
            ECODE: 'TEAM_202',
        });
        return;
    }

    access.checkAccessTeam(userid, team_id, {}, async (err, result) => {
        if (err) {
            cb(err);
            return;
        }

        let public_link_restrictions_available;

        try {
            public_link_restrictions_available = await entitlementService.checkEntitlement(
                team_id,
                EntitlementName.PublicLinkRestrictions
            );
        } catch (entitlementErr) {
            cb(entitlementErr);
            return;
        }

        if (!public_link_restrictions_available) {
            cb({
                err: 'Must be on enterprise plan',
                status: 401,
                ECODE: 'TEAM_203',
            });
            return;
        }

        const { role } = result;

        const { task_ids, view_ids, all } = options;

        const { can_read } = config.permission_constants;

        if (!task_ids && !view_ids && !all) {
            cb();
            return;
        }

        async.parallel(
            {
                updateTasks(para_cb) {
                    if (!task_ids && !all) {
                        para_cb();
                        return;
                    }

                    access.checkAccessTasks(userid, task_ids, { permissions: [can_read] }, accErr => {
                        if (accErr && role !== 1 && role !== 2) {
                            para_cb(accErr);
                            return;
                        }

                        const params = [];
                        let query = `
                            UPDATE task_mgmt.items
                            SET
                                public = false,
                                made_public_by = null,
                                made_public_time = null
                            WHERE
                                id = ANY($1)
                        `;

                        if (all) {
                            query = `
                            UPDATE task_mgmt.items
                            SET
                                public = FALSE,
                                made_public_by = null,
                                made_public_time = null
                            WHERE
                                id IN
                                (
                                    SELECT items.id
                                    FROM
                                        task_mgmt.items
                                        JOIN task_mgmt.subcategories ON subcategories.id = items.subcategory AND subcategories.deleted = FALSE
                                        JOIN task_mgmt.categories ON subcategories.category = categories.id AND categories.deleted = FALSE
                                        JOIN task_mgmt.projects ON categories.project_id = projects.id AND projects.deleted = FALSE
                                        JOIN task_mgmt.teams ON projects.team = teams.id
                                    WHERE
                                        teams.id = $1 AND
                                        items.deleted = FALSE AND
                                        items.template = FALSE AND
                                        items.archived = FALSE AND
                                        items.public = TRUE
                                )
                            `;
                            params.push(team_id);
                        } else {
                            params.push(task_ids);
                        }

                        db.writeQuery(query, params, para_cb);
                    });
                },
                updateViews(para_cb) {
                    if (!view_ids && !all) {
                        para_cb();
                        return;
                    }

                    access.checkAccessViews(userid, view_ids, { permissions: [] }, accErr => {
                        if (accErr && role !== 1 && role !== 2) {
                            para_cb(accErr);
                            return;
                        }

                        const params = [];
                        let where_query;

                        if (all) {
                            where_query = `
                                team_id = $1 AND
                                deleted = FALSE AND
                                public = TRUE
                            `;
                            params.push(team_id);
                        } else {
                            where_query = `view_id = ANY($1)`;
                            params.push(view_ids);
                        }

                        const query = `
                            UPDATE task_mgmt.views
                            SET
                                public = FALSE,
                                made_public_by = null,
                                made_public_time = null
                            WHERE
                                ${where_query}
                        `;

                        const queries = [
                            {
                                query,
                                params,
                            },
                            // diable public forms
                            // uses different column to be public
                            // form_active
                            {
                                query: `
                                    UPDATE task_mgmt.views
                                    SET
                                        form_active = false
                                    WHERE
                                        ${all ? 'team_id = $1' : 'view_id = ANY($1)'} AND
                                        form_active = true AND
                                        type = $2
                                `,
                                params: [...params, FORM_TYPE],
                            },
                        ];

                        db.batchQueries(queries, para_cb);
                    });
                },
            },
            paraErr => {
                if (paraErr) {
                    cb(paraErr);
                    return;
                }

                cb();
            }
        );
    });
}

export function disablePublicLinksRoute(req, resp, next) {
    const userid = req.decoded_token.user;
    const { team_id } = req.params;

    const { task_ids, view_ids, all } = req.body;

    disablePublicLinks(userid, team_id, { task_ids, view_ids, all }, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    });
}

function disablePubLinksFromSetting(userid, team_id, options, cb) {
    if (!team_id) {
        cb({
            err: 'Internal server error',
            status: 500,
            ECODE: 'TEAM_065',
        });
        return;
    }

    const queries = [];
    let where_query = ``;
    const params = [team_id];

    let set = `
        public = FALSE,
        made_public_by = NULL,
        made_public_time = NULL
    `;

    if (options.pub_links_max_year) {
        params.push(new Date(new Date().setFullYear(new Date().getFullYear() + 1)).getTime());
        where_query = `public_share_expires_on > $${params.length}`;
    } else if (options.disable_never_expire_pub_links) {
        const now = new Date();
        params.push(Number(now.setDate(now.getDate() + 90)));
        set = ` public_share_expires_on = $${params.length}`;
        where_query = `public_share_expires_on IS NULL`;
    }

    if (!where_query) {
        cb();
        return;
    }

    queries.push(
        {
            query: `
                UPDATE task_mgmt.items
                SET
                    ${set}
                WHERE id = ANY(
                    SELECT
                        items.id
                    FROM
                        task_mgmt.teams
                        JOIN task_mgmt.projects ON projects.team = $1
                        JOIN task_mgmt.categories ON categories.project_id = projects.id
                        JOIN task_mgmt.subcategories ON subcategories.category = categories.id
                        JOIN task_mgmt.items ON items.subcategory = subcategories.id
                    WHERE
                        teams.id = $1 AND
                        items.public = TRUE AND
                        items.${where_query} AND
                        items.made_public_by IS NOT NULL AND
                        items.made_public_time IS NOT NULL
                )
            `,
            params,
        },
        {
            query: `
                UPDATE
                    task_mgmt.views
                SET
                    ${set}
                WHERE
                    team_id = $1 AND
                    public = TRUE AND
                    ${where_query} AND
                    made_public_by IS NOT NULL AND
                    made_public_time IS NOT NULL
            `,
            params,
        }
    );

    db.batchQueries(queries, cb);
}

export { disablePubLinksFromSetting };

function estimatesToAssignees(user, team_id, options, cb) {
    if (options.estimates_per_assignee === null) {
        cb();
        return;
    }

    const query = `
        UPDATE task_mgmt.assignees
        SET time_estimate = q1.time_split
        FROM (
            SELECT
                items.id AS task_id,
                items.time_estimate,
                ARRAY_AGG(assignees.userid) FILTER (WHERE assignees.userid IS NOT null) AS task_assignees,
                FLOOR(items.time_estimate / array_length(ARRAY_AGG(assignees.userid), 1)) AS time_split
            FROM
                task_mgmt.teams
                JOIN task_mgmt.projects ON projects.team = teams.id
                JOIN task_mgmt.categories ON categories.project_id = projects.id
                JOIN task_mgmt.subcategories ON subcategories.category = categories.id
                JOIN task_mgmt.items ON items.subcategory = subcategories.id
                LEFT OUTER JOIN task_mgmt.assignees ON assignees.task_id = items.id
            WHERE
                items.deleted = FALSE AND
                items.template = FALSE AND
                items.importing IS NOT TRUE AND
                items.archived = FALSE AND
                subcategories.deleted = FALSE AND
                subcategories.archived = FALSE AND
                subcategories.importing IS NOT TRUE AND
                subcategories.template = FALSE AND
                categories.deleted = FALSE AND
                categories.archived = FALSE AND
                categories.importing IS NOT TRUE AND
                categories.template = FALSE AND
                projects.deleted = FALSE AND
                projects.archived = FALSE AND
                projects.importing IS NOT TRUE AND
                projects.template = FALSE AND
                teams.deleted = FALSE AND
                items.time_estimate IS NOT NULL AND
                assignees.userid IS NOT NULL AND
                teams.id = $1
            GROUP BY items.id, items.time_estimate
        ) AS q1
        WHERE assignees.task_id = q1.task_id AND assignees.userid = ANY(q1.task_assignees)
    `;

    db.writeQuery(query, [team_id], err => {
        if (err) {
            cb(err);
            return;
        }

        cb();
    });
}

export { estimatesToAssignees };

const optionToFormulaOperator = new Map([
    ['time_estimate_rollup', QueryableFormulaOperator.TimeEstimated],
    ['time_tracking_rollup', QueryableFormulaOperator.TimeTracked],
    ['points_estimate_rollup', QueryableFormulaOperator.SprintPoints],
]);

async function getFieldUpdateEvents(teamId, newOptions, existingOptions) {
    const formulaBackfillingConfig = formulaFieldBackfillingConfig(teamId);
    if (formulaBackfillingConfig.triggers?.teamEdit?.disable) {
        return [];
    }

    const promises = [];

    for (const [option, operator] of optionToFormulaOperator.entries()) {
        if (hasChangeInOption(option)) {
            const getFormulaIdsTask = formulaProvider.getCalculatedFormulasWithOperator(teamId, operator);
            promises.push(getFormulaIdsTask);
        }
    }

    const fieldIds = (await Promise.all(promises)).flatMap(x => x);

    const distinctFieldIds = [...new Set(fieldIds)];

    return formulaCalculationService.toVersionUpdates(distinctFieldIds, teamId, CalculationTrigger.TeamEdit);

    function hasChangeInOption(option) {
        return newOptions[option] != null && newOptions[option] !== existingOptions[option];
    }
}
