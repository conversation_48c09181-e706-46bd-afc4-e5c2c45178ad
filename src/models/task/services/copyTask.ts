import config from 'config';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import { parseQuillContent } from '@time-loop/common-utils';
import type { QueryObject, DbQueryResult } from '@clickup/utils/db-types';
import { NestMonolithInstance } from '@clickup/nest/monolith-app-context';
import { FeatureFlagService } from '@clickup/utils/feature-flag';
import { ConfigService } from '@clickup/utils/config';
import { isBoolean, isEmpty } from 'lodash';

import { addPressureToFunctionParams, OperationPrefix, withSemaphoreLimit } from '@clickup-legacy/utils/semaphoreUtils';
import {
    ObjectRelationshipType,
    ObjectType,
    ObjectVersionUpdateRequest,
    OperationType,
} from '@time-loop/ovm-object-version';
import { CopyableObjectKey, CopyOperation, ObjectType as CopyableObjectType } from '@clickup-legacy/utils/copy';
import { CustomType } from '@clickup/task/extensions';
import { generateNewLineOp } from '@clickup/quill';
import { getLogger } from '@clickup/shared/utils-logging';
import { createNewTaskId } from '@clickup/data-platform/sequences';
import { checkListTaskLimit, subtaskLimitChecker, SubtaskLimitError } from '@clickup-legacy/models/task/utils/limits';
import { determineAsyncFunction } from '@clickup-legacy/utils/copy/copy-operation-dynamic-limit';
import { DateInterval, dateInterval } from '@clickup/scheduler/common';
import { checkEntitlements as checkTemplateEntitlements } from '@clickup-legacy/models/templates/templateEntitlements';
import { TEMPLATES_PROCESS_SHUTDOWN_TIMEOUT_MS } from '@clickup-legacy/models/templates/templateIdHelpers';
import { processShutdownController } from '@clickup-legacy/utils/shutdownController';
import {
    AccessValidator,
    CopyTaskOperations,
    CopyTaskTrafficControl,
    CopyTaskEndpoints,
    DataLoader,
    DataWriter,
    ConstraintsChecker,
    isTemplateMembersTemplate,
} from '@clickup/templates/copy-task';
import { templatesCopyTaskNewIdWorkflow, ActivitiesService } from '@clickup/temporal/workflows/copytask';
import {
    getAccessInfoCache,
    getAuthorizationService,
} from '@clickup-legacy/utils/access/services/authorization/instances';
import { getWorkspaceId } from '@clickup-legacy/utils/access/services/authorization/common';
import { createLogger, format, transports } from 'winston';
import { CuPublicTemplatesRepository } from '@clickup-legacy/models/templates/publicTemplates/publicTemplateRepository';
import {
    getTaskServiceLogConfig,
    ShadowTrafficQuery,
    shouldPreventCopyingTaskWithDeletedParent,
    shouldPreventCopyTaskBySubtaskLimit,
} from '@clickup-legacy/models/integrations/split/squadTreatments/taskTreatments';
import {
    getSemaphoreCopyOptions,
    isUnifiedFieldRemappingEnabled,
    shouldRunPreCopyEntitlementChecks,
    useFanoutOnUndeleteUpdate,
    useGracefulShutdown,
} from '@clickup-legacy/models/integrations/split/squadTreatments/templatesTreatments';
import {
    improvedSubtasksOrderFetchWhenCopyingTask,
    shouldTemplateCopyInheritedStatuses,
} from '@clickup-legacy/models/integrations/split/squadTreatments/fieldTreatments';
import {
    shouldBlockCopyTaskForWorkspace,
    shouldPreventCopyTaskAcrossWorkspaces,
} from '@clickup-legacy/models/integrations/split/squadTreatments/unclaimedTreatments';
import { FeatureFlag } from '@clickup-legacy/models/integrations/split/feature-flag.enum';
import { HttpException } from '@nestjs/common/exceptions/http.exception';
import { ItemQueryKey, QueryFamily, type QueryProxyClient } from '@clickup/query-proxy';
import { GenericError } from '@clickup-legacy/utils/errors/interfaces';
import { sendWebhookInBatchOnTaskCreation } from '@clickup-legacy/models/task/helpers/webhookHelper';
import { metricsClient } from '@clickup-legacy/metrics/metricsClient';
import { endTimer, startTimer } from '@clickup-legacy/metrics/timing';
import { batchQueriesAsync, readAsync, writeAsync } from '@clickup-legacy/utils/db2';
import { localBdrDBClient } from '@clickup-legacy/utils/simpleClient/bdrDbClientHelpers';
import { queryProxyFactory } from '@clickup-legacy/utils/queryProxy/queryProxyFactory';
import { bindAttachmentsToParentAsync } from '@clickup-legacy/models/attachment/services/updateAttachmentParent';
import { EntitlementName, entitlementService } from '@clickup-legacy/models/entitlements/entitlementService';
import { DurationMetricObject, DURATION_METRIC_KEY } from '@clickup-legacy/models/templates/templateMetricsCommons';
import { TemplateTracer, TEMPLATE_TRACER_KEY } from '@clickup-legacy/models/templates/templateTracer';
import { batchQueriesSeriesAsyncThenUpdateTasks } from '@clickup-legacy/models/task/helpers/versionUpdateHelper';
import { TaskCreatedVia } from '@clickup-legacy/models/task/interfaces/Task';

import * as async from '@clickup-legacy/utils/asyncHelper';
import { bucketize, timedSteps } from '@clickup-legacy/utils/metrics';

import * as bugsnagAutomation from '@clickup-legacy/automation/resources/bugsnag';
import * as githubAutomation from '@clickup-legacy/automation/resources/github';
import {
    createScheduledDueDateTriggers,
    createScheduledStartDateTriggers,
} from '@clickup-legacy/automation/services/automationScheduledTriggersService';
import * as access from '@clickup-legacy/utils/access2';
import * as base32 from '@clickup-legacy/utils/base32';
import * as db from '@clickup-legacy/utils/db';
import * as encrypt from '@clickup-legacy/utils/encrypt';
import { getTeamId } from '@clickup-legacy/utils/entities/services/entitiesService';
import { ClickUpError } from '@clickup-legacy/utils/errors';
import { TaskErrorCodes } from '@clickup-legacy/utils/errors/constants';
import { promiseMapLimit } from '@clickup-legacy/utils/promises';
import * as sqsWs from '@clickup-legacy/utils/v1-ws-message-sending'; // eslint-disable-line import/no-cycle
import * as sqsNotif from '@clickup-legacy/utils/sqs-notif';
import { ClickUpTracer } from '@clickup-legacy/utils/tracer';
import * as assigneeMod from '@clickup-legacy/models/assignee';
import { copyAttachmentDocs, copyAttachments } from '@clickup-legacy/models/attachment/attachmentCopy';
import * as checklistMod from '@clickup-legacy/models/checklist';
import * as copyComments from '@clickup-legacy/models/comment/services/copyComments';
import { saveTaskTemplateCustomTaskType } from '@clickup-legacy/models/custom_items/templateHelpers';
import * as copyDependencies from '@clickup-legacy/models/dependencies/copyDependencies.service';
import {
    reportTaskChanged,
    reportAttachmentChanged,
} from '@clickup-legacy/models/elastic/services/change-detector/hooks';
import * as fieldValue from '@clickup-legacy/models/field/fieldValue';
import * as fieldHelper from '@clickup-legacy/models/field/helpers/helpers';
import { copyFieldValues, getListRelationshipValuesAsync } from '@clickup-legacy/models/field/helpers/fieldOperations';
import * as groupSharingPaywall from '@clickup-legacy/models/group_sharing/paywall';
import * as groupMod from '@clickup-legacy/models/groups';
import { cUParseInt } from '@clickup-legacy/models/helpers_v2';
import * as googleCal from '@clickup-legacy/models/integrations/googleCalendar/taskNotifications';
import { postTaskCreatedNotifications } from '@clickup-legacy/models/integrations/notifications/notifications';
import { copyExternalLinks, copyLinks } from '@clickup-legacy/models/link/services/copyLinks';
import * as orderindexMod from '@clickup-legacy/models/ordering/task/helpers';
import * as taskOrderindexCopy from '@clickup-legacy/models/ordering/task/services/copyService';
import * as recurrence2 from '@clickup-legacy/models/recurrence_2';
import * as subcatHelper from '@clickup-legacy/models/subcategory/helpers';
import { copyTags } from '@clickup-legacy/models/tags/copyTags';
import * as templateHelpers from '@clickup-legacy/models/templates/templateHelpers';
import { extendDataBasedOnFeatureFlag } from '@clickup-legacy/models/templates/templateHelpers';
import * as createTask from '@clickup-legacy/models/task/CRUD/createTask'; // eslint-disable-line import/no-cycle
import * as getTask from '@clickup-legacy/models/task/CRUD/getTask'; // eslint-disable-line import/no-cycle
import {
    getInsertTaskGroupMembersFromCopiedTask,
    getInsertTaskMembersFromCopiedTask,
    getSubtaskCountForTaskAsync,
} from '@clickup-legacy/models/task/datastores/copyTaskDatastore';
import { remapDateOfTaskV2 } from '@clickup-legacy/models/task/dateHelpers';
import { getSyncBlocksByParentIds } from '@clickup-legacy/models/sync_blocks/syncBlockService';
import { SyncBlock } from '@clickup-legacy/models/sync_blocks/interfaces';
import { replaceAttachmentsInContent, replaceSyncBlocksInContent } from '@clickup-legacy/utils/quill';
import { prepareSyncBlockQueries } from '@clickup-legacy/models/sync_blocks/syncBlockQueryFactory';
import { getDebugLogger } from '@clickup-legacy/utils/debugLog';
import { buildQueryComparatorFromFlag } from '@clickup-legacy/utils/access/factories/egress-query-comparators';
import { getValidFollowersQueryFactory } from './queryFactories/validFollowersQueryFactory';
import { AssigneeValidationTypes, getValidAssigneesQueryFactory } from './queryFactories/validAssigneesQueryFactory';
import { getAssigneeHistoryInsertQuery } from './assigneeHistoryQuery';

const { task: TASK_TYPE } = config.comments.types;

const CopyError = ClickUpError.makeNamedError('item');
const logTaskError = ClickUpError.getIsolatedErrorHandler('item');
const logger = getLogger('item');

const tracer = new ClickUpTracer();
const TaskError = ClickUpError.makeNamedError('task');

const METRIC_NAME = 'copy_task_service.copy_task_step';
function logDurationMetrics(timerStart: [number, number], subtaskCount: number) {
    const duration = endTimer(timerStart);
    metricsClient.timing('copy_task_service.copy_task', duration, { subtask_count: bucketize(subtaskCount) });
}

interface CopyTaskInputParams {
    attachments?: string[];
    assignees?: string[];
    group_assignees?: string[];
    name?: string;
    content?: {
        ops: any[];
    };
    due_date?: number;
    due_date_time?: boolean;
    start_date?: number;
    start_date_time?: boolean;
    priority?: any;
    status?: any;
    time_estimate?: any;
    time_estimate_string?: string;
    tags?: string[];
    form_id?: string;
    via?: string;
    customFields?: any;
    gh_add_link?: any;
    dup_trigger?: number;
}

export interface CopyTaskOptions {
    task_id?: string;
    workspace_id?: string;
    team_id?: string;
    new_task_name?: string;
    new_due_date?: string;
    new_due_date_time?: string;
    new_start_date?: string;
    new_start_date_time?: string;
    new_assignee?: string;
    new_description?: string;
    new_priority?: string;
    new_tags?: string[];
    new_custom_fields?: any;
    new_subtasks?: any[];
    new_checklists?: any[];
    new_attachments?: any[];
    new_followers?: any[];
    new_parent_task_id?: string;
    new_parent_task_workspace_id?: string;
    new_parent_task_team_id?: string;
    new_parent_task_name?: string;
    new_parent_task_due_date?: string;
    new_parent_task_due_date_time?: string;
    new_parent_task_start_date?: string;
    new_parent_task_start_date_time?: string;
    new_parent_task_assignee?: string;
    new_parent_task_description?: string;
    new_parent_task_priority?: string;
    new_parent_task_tags?: string[];
    new_parent_task_custom_fields?: any;
    new_parent_task_subtasks?: any[];
    new_parent_task_checklists?: any[];
    new_parent_task_attachments?: any[];
    new_parent_task_followers?: any[];
    dup_trigger?: number;
    dd_option_map?: any;
    field_map?: any;
    form_id_map?: any;
    ll_option_map?: any;
    in_params?: CopyTaskInputParams;
    subtasks_name_map?: any;
    proxyClient?: QueryProxyClient;
    orderindexStats?: any;
    set_deleted?: boolean;
    set_undeleted?: boolean;
    assigned_comment?: boolean;
    attachment_ids?: string[];
    template?: boolean;
    template_v2?: boolean;
    parent_is_template?: boolean;
    old_assignees?: boolean;
    assignees?: any[];
    group_assignees?: any[];
    skip_access?: boolean;
    template_visibility?: number;
    template_members?: string[];
    template_v2_child?: boolean;
    subcategory_id?: string;
    create_notifications?: boolean;
    public_template_child_copy?: boolean;
    exclude_deleted?: boolean;
    parent?: string | number; // -1 used for root task/not a subtask - real parent task id otherwise
    top_level_parent?: string;
    name?: string;
    content?: boolean;
    from_form?: boolean;
    due_date?: number;
    due_date_time?: boolean;
    start_date?: number;
    start_date_time?: boolean;
    remap_start_date?: boolean;
    old_start_date?: boolean;
    old_due_date?: boolean;
    old_duration?: boolean;
    priority?: any;
    old_status?: any;
    custom_type?: any;
    default_template?: boolean;
    public_template?: boolean;
    followers?: any[];
    old_followers?: boolean;
    group_followers?: any[];
    from_template?: boolean;
    points_per_assignee?: any;
    estimates_per_assignee?: any;
    nested_subtasks?: any;
    subtask_parent?: string | number;
    from_dupe_task?: boolean;
    apply_gantt_dates?: boolean;
    gantt_dates?: any; // think its a set
    copy_subtask?: boolean;
    from_recur?: boolean;
    subtask_date?: boolean;
    subtask_start_date_shift?: number;
    subtask_due_date_shift?: number;
    task_start_date_shift?: number;
    task_due_date_shift?: number;
    old_tags?: boolean;
    tags?: string[];
    old_checklists?: boolean; // if null gets defaulted to true
    old_checklist_item_state?: boolean; // to keep backwards compatible should default to false
    new_content?: any;
    status?: any;
    recur_new_status?: any;
    quick_start_template?: boolean;
    user_tz?: string;
    default_skip_access?: boolean;
    from_parent_template?: boolean;
    from_v2_template_child?: boolean;
    from_task_template?: boolean;
    top?: boolean;
    old_orderindex?: boolean;
    subtask_orderindex?: string;
    orderindex_offset?: number;
    from_parent_task?: boolean;
    usecase?: string;
    recur_settings?: any;
    recurring?: boolean;
    time_estimate?: any;
    old_permanent_template_id?: string;
    parent_is_task_template?: boolean;
    archived?: number;
    template_name?: string;
    recur_next?: any;
    recur_rule?: any;
    skip_privacy?: boolean;
    default_template_id?: string;
    via?: string;
    public_sharing?: boolean;
    [DURATION_METRIC_KEY]?: DurationMetricObject;
    [TEMPLATE_TRACER_KEY]?: TemplateTracer;
    template_group_members?: any[];
    force_copy_external_dependencies?: boolean;
    sync_blocks_old_to_new?: Record<string, string>;
    subtask_count_validation_performed?: boolean;
    callOrigin?: string;
    duration?: DateInterval;
    duration_is_elapsed?: boolean;
    attachments?: boolean;
    auto_id?: string;
    batched_copy_limit?: number;
    bugsnag_link?: unknown;
    bugsnag_link_url?: unknown;
    category_id?: number;
    comment?: boolean;
    comment_attachments?: boolean;
    create_template?: boolean;
    custom_fields?: boolean;
    description?: unknown;
    earliest_due_date?: number;
    earliest_start_date?: number;
    entryEndpoint?: CopyTaskEndpoints;
    external_dependencies?: boolean;
    internal_dependencies?: boolean;
    graceful_shutdown?: boolean;
    latest_due_date?: number;
    list_relationships?: boolean;
    middleware_queries?: QueryObject[];
    notify_all?: boolean;
    old_statuses?: unknown;
    old_subtask_assignees?: boolean;
    origin_task_for_recur?: string;
    permanent_template_id?: string;
    pressure?: {
        [OperationPrefix.COPY_TASK]?: number;
    };
    relationships?: boolean;
    ret_from_private?: boolean;
    return_immediately?: boolean;
    send_notifs_to_user?: boolean;
    set_deleted_for_outside_transaction?: boolean;
    set_recur_copy_self?: boolean;
    skip_date_remapping?: boolean;
    skip_delayed_triggers?: boolean;
    skip_from_copy_hist?: boolean;
    skip_task_created_trigger?: boolean;
    skip_weekends?: boolean;
    subcat_ids?: string[];
    subtask_assignees?: any[];
    subtask_status?: unknown;
    subtasks?: unknown;
    task_activity?: boolean;
    task_relationships?: boolean;
    task_that_recurred?: string;
    template_id?: unknown;
    trigger_id?: string;
    unresolve_comments?: boolean;
    webhook_payload?: unknown;
    ws_key?: any;
    is_list_copy?: boolean;
    old_date_done?: boolean;
    old_date_closed?: boolean;
}

export interface CopyTaskResult {
    id: string;
    list_rel_values?: any[];
    old_to_new?: any;
    old_dependencies?: any;
    old_links?: any;
    from_private?: boolean;
    notif_function?: any;
    permanent_template_id?: string;
    task_start_date_shift?: number;
    task_due_date_shift?: number;
    subtask_start_date_shift?: number;
    subtask_due_date_shift?: number;
    workspace_id?: number;
    subtask_ids?: any[];
}

function getFeatureFlagService(): FeatureFlagService {
    return NestMonolithInstance.get(FeatureFlagService);
}

function getConfigService(): ConfigService {
    return NestMonolithInstance.get(ConfigService);
}

const accessValidatorFactory: (proxyClient: QueryProxyClient) => AccessValidator = (() => {
    let instance: AccessValidator | null = null;
    return (proxyClient: QueryProxyClient) => {
        if (!instance) {
            logger.debug('Starting up copyTask access validator');
            instance = new AccessValidator(
                proxyClient,
                getWorkspaceId,
                getAuthorizationService(),
                getConfigService(),
                CuPublicTemplatesRepository
            );
        }
        return instance;
    };
})();

const copyTaskTrafficControlFactory: () => () => Promise<CopyTaskTrafficControl> = (() => {
    let instance: CopyTaskTrafficControl | null = null;
    return () => {
        if (!instance) {
            logger.debug('Starting up copyTask traffic control');
            instance = new CopyTaskTrafficControl(
                FeatureFlag.TemplatesCopyTaskOptions,
                getFeatureFlagService(),
                getConfigService()
            );
        }

        return async () => {
            if (!instance.isInitialized) {
                await instance.init();
            }

            return instance;
        };
    };
})();

const dataLoaderFactory: (proxyClient: QueryProxyClient) => DataLoader = (() => {
    let instance: DataLoader | null = null;
    return (proxyClient: QueryProxyClient) => {
        if (!instance) {
            logger.debug('Starting up copyTask data loader');
            instance = new DataLoader(
                proxyClient,
                getFeatureFlagService(),
                entitlementService,
                localBdrDBClient,
                getAccessInfoCache()
            );
        }
        return instance;
    };
})();

const dataWriterFactory: (proxyClient: QueryProxyClient) => DataWriter = (() => {
    let instance: DataWriter | null = null;
    const copyIndexModule = { ...taskOrderindexCopy, ...orderindexMod };
    return (proxyClient: QueryProxyClient) => {
        if (!instance) {
            logger.debug('Starting up copyTask data writer');
            instance = new DataWriter(base32.encode, copyIndexModule, proxyClient, localBdrDBClient);
        }
        return instance;
    };
})();

const constraintsCheckerFactory: () => ConstraintsChecker = (() => {
    let instance: ConstraintsChecker | null = null;
    return () => {
        if (!instance) {
            logger.debug('Starting up copyTask constraints checker');
            instance = new ConstraintsChecker(getFeatureFlagService());
        }
        return instance;
    };
})();

const workflowActivitiesServiceFactory: (
    accessValidator: AccessValidator,
    dataLoader: DataLoader
) => ActivitiesService = (() => {
    let instance: ActivitiesService | null = null;
    return (accessValidator: AccessValidator, dataLoader: DataLoader) => {
        if (!instance) {
            logger.debug('Starting up copyTask workflow activities service');
            instance = new ActivitiesService(accessValidator, dataLoader, getConfigService());
        }
        return instance;
    };
})();

/**
 * @param {number} userid
 * @param {string} task_id
 * @param {Object} options
 * @param {number} [options.archived] 0 - skip, null - unarchive, 1 - keep archived
 * @param {boolean} [options.assigned_comment] - Copy only Assigned Comments, skip Comments without Assignees
 * @param {boolean} [options.attachments] - Copy attachments
 * @param {boolean} [options.comment] - Copy Comments
 * @param {boolean} [options.custom_fields] - Copy Custom Fields not including the one with own options (list_relationship, task_relationship)
 * @param {boolean} [options.from_parent_template] - Applying Project, Cat, or SubCat Template
 * @param {boolean} [options.from_template] - Applying template
 * @param {boolean} [options.from_v2_template_child] - Applying parent Template
 * @param {boolean} [options.from_dupe_task] - Duplicating Task
 * @param {boolean} [options.list_relationships] - Copy List Relationship
 * @param {string} [options.name] - New Task name
 * @param {boolean} [options.old_assignees] - Copy old Assignees and Group Assignees
 * @param {boolean} [options.old_due_date] - Copy old Due date
 * @param {string} [options.old_permanent_template_id] - Provided when updating Template
 * @param {boolean} [options.old_start_date] - Copy old Start date
 * @param {boolean} [options.old_subtask_assignees] - Copy old subtasks Assignees and Group Assignees,
 * @param {boolean} [options.old_followers] - Copy old Followers and Group Followers
 * @param {boolean} [options.old_duration] - Copy old Duration
 * @param {boolean} [options.parent_is_task_template] - Child of Task Template, might be nested
 * @param {boolean} [options.parent_is_template] - Saving parent Template, unknown level :(
 * @param {boolean} [options.public_template] - Creating a Task from Public Template
 * @param {boolean} [options.public_template_child_copy] - Copying a subtask which is part of a public template
 * @param {boolean} [options.ret_from_private] - Return original task privacy
 * @param {boolean} [options.old_tags] - Copy old tags
 * @param {boolean} [options.task_relationships] - Copy Task Relationships
 * @param {boolean} [options.task_activity] - To include Task activity
 * @param {boolean} [options.template] - Saving Template v1
 * @param {boolean} [options.template_v2] - Saving Template v2
 * @param {boolean} [options.template_v2_child] - Parent is v2 Template, unknown level :(
 * @param {boolean} [options.top] - Saving Task template
 * @param {boolean} [options.skip_privacy] - If TRUE make Task public
 * @param {boolean} [options.skip_delayed_triggers] - Preserve dates if remapping on scheduling server
 * @param {boolean} [options.subtask_date] - From Recurring task to copy subtask date
 * @param {Map} [options.subtasks_name_map] - A Map of Subtasks names
 * @param {number} [options.subcategory_id] - Subcategory to copy to.
 * @param {number[]} [options.subcat_ids] - Subcategories in copy scope.
 * @param {boolean} [options.apply_gantt_dates] - Apply gantt_dates to Task and Subtasks
 * @param {Map} [options.gantt_dates] - Dates to apply
 * @param {TaskCreatedVia} [options.via] - Source of the task creation
 * @param {QueryProxyClient} [options.proxyClient] - proxy client
 * @param {string} [options.usecase] - Optional. Used to target a split flag, set to 'onboarding' for onboarding templates.
 * @param {boolean} [options.relationships] - If TRUE -> copy all relationships.
 * If task_relationships & list_relationships were null-ish, relationship=TRUE will overwrite
 * task & list relationships to TRUE
 * @param {boolean} [options.return_immediately] - If TRUE -> return new project id as soon as it is available
 * @param {boolean} [options.graceful_shutdown] - If TRUE -> use graceful shutdown. Note: if not explicitly specified,
 * value of options.return_immediately will be used. By default, we want to use graceful shutdown if we return immediately.
 * Overwrite graceful_shutdown if you want to change that behavior.
 * @param {function} cb - The callback to call when the task is copied.  The behavior of when this callback fires
 * depends on whether options.return_immediately is true or false.  If true, the callback will fire as soon as the
 * new task is written to the db, but before subsequent steps (like subtask creation) have executed.  If false, the
 * callback will fire after the entire copyTask operation is complete.
 * @param {function} cb2 - Optional. If provided, this callback will fire after the entire copyTask operation is complete.
 */

function _copyTaskWithoutSemaphoreLimit(
    userid: string,
    task_id: string,
    options: CopyTaskOptions,
    cb: any,
    cb2?: any
): Promise<CopyTaskResult> {
    /**
     * The async semantics of this function are confusing, because we need to maintain this odd behavior where if
     * options.return_immediately is true, the cb2() will fire early as soon as the new task ID is available.
     * However, we also need to track the actual end of the function for tracing purposes and potentially releasing the
     * semaphore, so the whole function is wrapped in a promise that resolves when the function is done.
     */
    return new Promise((resolve, reject) => {
        const copyTaskSpan = tracer.startChildSpan('copyTaskService.copyTask', {
            service: process.env.DD_SERVICE ?? 'copyTask',
            tags: { userId: userid, taskId: task_id },
        });
        const copyTaskTimer = startTimer();
        let subtask_count: number;
        let subtaskCountArchived: number;
        let subtaskCountNotArchived: number;
        const originalCb = cb;
        if (!originalCb) {
            logger.warn({
                msg: 'No callback provided to copyTask',
                stack: new Error().stack,
            });
        }
        cb = (err: any, result: any) => {
            if (originalCb && !originalCb.wasCalled) {
                originalCb(err, result);
                originalCb.wasCalled = true;
            }
        };
        let task_to_copy: any;
        let resolveTaskPromise: () => void;
        const taskToCopyPromise = new Promise(resolveTask => {
            resolveTaskPromise = () => resolveTask(true);
        });
        let resolveSubcategoryTeamPromise: () => void;
        const subcategoryTeamPromise = new Promise(resolveSubcategoryTeam => {
            resolveSubcategoryTeamPromise = () => resolveSubcategoryTeam(true);
        });
        let original_team: number;
        const copyContextExtraData: any = { options };

        const finish = async (err: any, result?: any) => {
            if (err) {
                reject(err);
            } else {
                const { workspace_id, id } = result;
                try {
                    await CopyOperation.finish(
                        CopyableObjectKey.from(ObjectType.TASK, original_team, task_id),
                        CopyableObjectKey.from(ObjectType.TASK, workspace_id, id),
                        {
                            shouldRemapRelationships: options.task_relationships,
                            data: extendDataBasedOnFeatureFlag(workspace_id, {}, copyContextExtraData),
                        }
                    );
                    resolve(result);
                } catch (copyFinishErr) {
                    logger.error({ msg: 'Error finishing copy operation', err: copyFinishErr });
                    result = null;
                    err = copyFinishErr;
                    reject(err);
                }
            }
            copyTaskSpan?.finish();
            logDurationMetrics(copyTaskTimer, subtask_count);
            cb(err, result);
            if (cb2) {
                cb2(err, result);
            }
        };

        const {
            dd_option_map = {},
            field_map = {},
            form_id_map = {},
            ll_option_map = {},
            in_params = {},
            subtasks_name_map,
            sync_blocks_old_to_new = {},
            proxyClient = queryProxyFactory(),
        } = options;

        const { orderindexStats } = options;

        if (options.set_deleted && !options.set_undeleted && getTaskServiceLogConfig().copy_task_set_undeleted) {
            const obj = new Error();
            Error.captureStackTrace(obj);
            logger.info({
                msg: 'copyTaskService.copyTask: set_deleted is true but set_undeleted is false',
                stack: obj.stack,
            });
        }

        options.task_relationships ??= options.relationships;
        options.list_relationships ??= options.relationships;

        if (options.force_copy_external_dependencies === undefined) {
            options.force_copy_external_dependencies = false;
        }

        const list_rel_values: { field_id: string; task_id: string; link_id: string }[] = [];
        const subtask_ids: string[] = []; // The ids of all created descendents of this task
        const subtask_notif_functions: any[] = [];
        const all_attachment_ids = [...(in_params.attachments || []), ...(options.attachment_ids || [])];
        const per_assignee_map: Record<
            string,
            {
                points?: any;
                estimates?: any;
                time_estimate?: any;
            }
        > = {};

        // Using _copyTask() to save or copy a Template (Project, Category, Subcategory, Task)
        const saving_template = options.template || options.template_v2 || options.parent_is_template; // saving a Template
        const check_template_entitlements =
            !saving_template &&
            options.from_template &&
            !options.from_parent_template &&
            shouldRunPreCopyEntitlementChecks();
        // in_params comes from form submissions, and contains values that should overwrite existing template values
        if (in_params.assignees && in_params.assignees.length) {
            options.old_assignees = false;
            options.assignees = in_params.assignees;
        }

        if (in_params.group_assignees && in_params.group_assignees.length) {
            options.old_assignees = false;
            options.group_assignees = in_params.group_assignees;
        }

        // If we're not retaining old status, make sure we're clearing out date done & closed.
        if (options.old_status !== true) {
            options.old_date_done = false;
            options.old_date_closed = false;
        }

        let new_inbox_users = options.assignees || [];
        let old_dependencies: Record<string, string[]> = {};
        let old_links: Record<string, string[]> = {};
        let valid_assignees: string[] = [];
        let valid_group_assignees: string[] = [];
        let valid_followers: string[] = [];
        let valid_group_followers: string[] = [];
        let old_to_new: Record<string, string> = {};
        let default_open_status: any;
        let attachment_ids: string[] = [];
        let only_fields: any;
        let from_v2_template = false;
        let points_per_assignee: any;
        let estimates_per_assignee: any;
        let nested_subtasks: any;
        let has_nested = false;
        let project_id: string;
        let team_id: string;
        let new_workspace_id: number;
        let subcategory_team: string;
        let new_start_date_shift = 0;
        let new_due_date_shift = 0;
        let content: any;
        let customType: any;
        const syncBlocks: SyncBlock[] = [];
        const denyExtendedCustomTaskTypeUsage = !(options.from_recur || options.callOrigin === 'automations');
        const accessValidator = accessValidatorFactory(proxyClient);
        const dataLoader = dataLoaderFactory(proxyClient);
        const dataWriter = dataWriterFactory(proxyClient);
        const constraintsChecker = constraintsCheckerFactory();
        const activitiesService = workflowActivitiesServiceFactory(accessValidator, dataLoader);
        const getCopyTaskTrafficControl = copyTaskTrafficControlFactory();
        const use_graceful_shutdown =
            useGracefulShutdown(userid) && isBoolean(options.graceful_shutdown)
                ? options.graceful_shutdown
                : options.return_immediately;
        let graceful_shutdown_process_id: string;

        const shouldUseWorkflows = async () =>
            (await getCopyTaskTrafficControl()).shouldUseWorkflows(userid, options.entryEndpoint);

        const shouldUseNewLogic = async (operation: CopyTaskOperations) =>
            (await getCopyTaskTrafficControl()).shouldUseNewLogic(operation, userid, options.entryEndpoint);

        async.parallel(
            timedSteps(METRIC_NAME, {
                async runNewWorkflow(para_cb) {
                    if (!(await shouldUseWorkflows())) {
                        para_cb();
                        return;
                    }

                    try {
                        ({
                            isFromV2Template: from_v2_template,
                            workspaceId: team_id,
                            spaceId: project_id,
                            subcategoryWorkspaceId: subcategory_team,
                        } = await templatesCopyTaskNewIdWorkflow(activitiesService, {
                            ...options,
                            userId: userid,
                            taskId: task_id,
                            workspaceId: options.team_id,
                            shouldSkipAccess: options.skip_access,
                            shouldSkipAccessByDefault: options.default_skip_access,
                            subcategoryId: options.subcategory_id,
                            attachmentIds: all_attachment_ids,
                        }));
                        resolveSubcategoryTeamPromise();
                        para_cb();
                    } catch (workflowError: any) {
                        para_cb(workflowError);
                    }
                },
                async accessTeam(para_cb) {
                    if (await shouldUseWorkflows()) {
                        para_cb();
                        return;
                    }
                    if (await shouldUseNewLogic(CopyTaskOperations.validateTeamAccess)) {
                        try {
                            const result = await accessValidator.validateWorkspaceAccess(
                                userid,
                                options.skip_access,
                                options.team_id
                            );
                            para_cb(null, result);
                        } catch (accessTeamError: any) {
                            para_cb(accessTeamError, null);
                        }
                        return;
                    }
                    if (!options.team_id || options.skip_access) {
                        para_cb();
                        return;
                    }

                    access.checkAccessTeam(userid, options.team_id, { permissions: [] }, para_cb);
                },

                async access(para_cb) {
                    if (await shouldUseWorkflows()) {
                        para_cb();
                        return;
                    }
                    if (await shouldUseNewLogic(CopyTaskOperations.validateTaskAccess)) {
                        try {
                            const result = await accessValidator.validateTaskAccess(
                                userid,
                                task_id,
                                options.skip_access || options.default_skip_access
                            );
                            from_v2_template = result.from_v2_template || from_v2_template;
                            para_cb(null, result);
                        } catch (accessTaskError: any) {
                            para_cb(accessTaskError, null);
                        }
                        return;
                    }
                    proxyClient.replicaQuery(
                        {
                            queryFamily: QueryFamily.Item,
                            queryKey: ItemQueryKey.Access,
                            queryParams: [task_id],
                        },
                        async (err: Error | null, result: any) => {
                            if (err) {
                                para_cb(new CopyError(err, 'ITEM_154'));
                                return;
                            }
                            if (result.rows.length) {
                                from_v2_template = true;
                            }
                            if (!userid || options.skip_access || options.default_skip_access) {
                                para_cb();
                                return;
                            }
                            if (!result.rows.length) {
                                access.checkAccessTask(
                                    userid,
                                    task_id,
                                    { permissions: [config.permission_constants.duplicate] },
                                    para_cb
                                );
                                return;
                            }
                            const task_to_check =
                                !result.rows[0].team_id && result.rows[0].parent_team_id
                                    ? result.rows[0].parent
                                    : task_id;
                            try {
                                await access.promiseAccessTaskTemplate(userid, task_to_check, { proxyClient });
                                para_cb();
                            } catch (e) {
                                para_cb(e as Error);
                            }
                        }
                    );
                },

                async accessAttachments(para_cb) {
                    if (await shouldUseWorkflows()) {
                        para_cb();
                        return;
                    }
                    if (await shouldUseNewLogic(CopyTaskOperations.validateAttachmentAccess)) {
                        try {
                            await accessValidator.validateAttachmentAccess(
                                userid,
                                all_attachment_ids,
                                options.skip_access
                            );
                            para_cb();
                        } catch (accessAttachmentError: any) {
                            para_cb(accessAttachmentError, null);
                        }
                        return;
                    }

                    if (
                        !all_attachment_ids ||
                        all_attachment_ids.length === 0 ||
                        userid === config.get<string>('clickbot_assignee') ||
                        options.skip_access
                    ) {
                        para_cb();
                        return;
                    }

                    access.checkAccessAttachments(userid, all_attachment_ids, {}, para_cb);
                },

                async validateTemplateMembers(para_cb) {
                    if (await shouldUseWorkflows()) {
                        para_cb();
                        return;
                    }
                    if (await shouldUseNewLogic(CopyTaskOperations.validateTemplateMembers)) {
                        try {
                            await accessValidator.validateTemplateMembers(task_id, options);
                            if (
                                isTemplateMembersTemplate(
                                    config.get<number>('template_permissions.members'),
                                    options.template_visibility,
                                    options.template_members
                                )
                            ) {
                                const result = await dataLoader.loadWorkspaceAndSpaceForTask(task_id);
                                await subcategoryTeamPromise;
                                team_id = result.workspaceId;
                                project_id ??= result.spaceId;
                            }
                            para_cb();
                        } catch (templateMemebersError: any) {
                            para_cb(templateMemebersError, null);
                        }
                        return;
                    }
                    if (
                        !options.template_v2 ||
                        options.template_visibility !== config.get<number>('template_permissions.members')
                    ) {
                        para_cb();
                        return;
                    }
                    if (!options.template_members || !options.template_members.length) {
                        para_cb(new CopyError('template members cannot be empty', 'ITEM_152', 400));
                        return;
                    }
                    let response;
                    try {
                        response = await db.promiseReplicaQuery(
                            `
                                SELECT projects.team, projects.id
                                FROM task_mgmt.items
                                JOIN task_mgmt.subcategories
                                    ON items.subcategory = subcategories.id
                                JOIN task_mgmt.categories
                                    ON categories.id = subcategories.category
                                JOIN task_mgmt.projects
                                    ON projects.id = categories.project_id
                                WHERE
                                    items.id = $1
                        `,
                            [task_id]
                        );
                    } catch (err) {
                        para_cb(new CopyError(err, 'ITEM_150', 500));
                        return;
                    }
                    if (!response.rows || !response.rows.length) {
                        para_cb(new CopyError('Could not find task', 'ITEM_151', 404));
                        return;
                    }

                    await subcategoryTeamPromise;
                    team_id ??= response.rows[0].team;
                    project_id ??= response.rows[0].id;

                    try {
                        await Promise.all(
                            options.template_members.map(member =>
                                access.promiseAccessTeam(member, team_id, { checkJoined: false })
                            )
                        );
                    } catch (err) {
                        para_cb(err as Error);
                        return;
                    }
                    para_cb();
                },

                async section(para_cb) {
                    if (await shouldUseWorkflows()) {
                        para_cb();
                        return;
                    }
                    if (await shouldUseNewLogic(CopyTaskOperations.validateSubcategoryAccess)) {
                        try {
                            await accessValidator.validateSubcategoryAccess(userid, options.subcategory_id, options);
                            para_cb();
                        } catch (subcatAccessError: any) {
                            para_cb(subcatAccessError, null);
                        }
                        return;
                    }
                    if (
                        !options.template_v2 &&
                        !options.template_v2_child &&
                        options.subcategory_id &&
                        options.create_notifications &&
                        !options.skip_access
                    ) {
                        access.checkAccessSubcategory(
                            userid,
                            options.subcategory_id,
                            { permissions: [config.permission_constants.can_create_tasks] },
                            para_cb
                        );
                    } else {
                        para_cb();
                    }
                },

                async defaultStatus(para_cb) {
                    if (
                        (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                            CopyTaskOperations.loadDefaultOpenStatus,
                            userid,
                            options.entryEndpoint
                        )
                    ) {
                        try {
                            default_open_status = await dataLoader.loadDefaultOpenStatus(
                                task_id,
                                options.subcategory_id,
                                options
                            );
                            para_cb();
                        } catch (defaultStatusError: any) {
                            para_cb(defaultStatusError, null);
                        }
                        return;
                    }
                    if (options.template_v2) {
                        para_cb();
                        return;
                    }
                    let query;
                    let params;

                    if (options.subcategory_id) {
                        query = `
                            SELECT statuses.status, statuses.orderindex, statuses.color, statuses.type
                            FROM task_mgmt.statuses,
                                 task_mgmt.subcategories
                            WHERE subcategories.id = $1
                              AND subcategories.status_group = statuses.status_group
                              AND statuses.type = 'open'`;
                        params = [options.subcategory_id];
                    } else {
                        query = `
                            SELECT statuses.status, statuses.orderindex, statuses.color, statuses.type
                                FROM task_mgmt.statuses, task_mgmt.subcategories, task_mgmt.items
                                WHERE subcategories.id = items.subcategory
                                AND items.id = $1
                                AND subcategories.status_group = statuses.status_group
                                AND statuses.type = 'open'`;
                        params = [task_id];
                    }
                    const debugLogger = getDebugLogger(logger, shouldTemplateCopyInheritedStatuses(team_id));
                    // [CLK-66151] Moved to readQuery due to read after write bugs applying templates
                    db.readQuery(query, params, (err: Error | null, result: any) => {
                        if (err) {
                            para_cb(err);
                        } else if (
                            result.rows.length === 0 &&
                            !options.template_v2_child &&
                            !options.public_template_child_copy
                        ) {
                            debugLogger.debug({
                                message: '[status copy debug] open status not found error',
                                params,
                            });
                            para_cb({
                                err: 'Open status not found',
                                status: 500,
                                ECODE: 'TSKCPY_001',
                            } as unknown as Error);
                        } else if (result.rows.length === 0) {
                            para_cb();
                        } else {
                            default_open_status = result.rows[0].status;
                            para_cb();
                        }
                    });
                },

                async task(para_cb) {
                    if (
                        (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                            CopyTaskOperations.loadTaskToCopy,
                            userid,
                            options.entryEndpoint
                        )
                    ) {
                        try {
                            await subcategoryTeamPromise;
                            ({
                                task: task_to_copy,
                                options,
                                customType,
                                hasNested: has_nested,
                                originalTeam: original_team,
                            } = await dataLoader.loadTaskToCopyAndCopyOptions(
                                task_id,
                                { ...options, subcategory_team, saving_template },
                                {
                                    ...in_params,
                                    workspace_and_space: { workspaceId: team_id, spaceId: project_id },
                                }
                            ));
                            resolveTaskPromise();
                            para_cb();
                        } catch (defaultStatusError: any) {
                            para_cb(defaultStatusError, null);
                        }
                        return;
                    }

                    proxyClient.replicaQuery(
                        {
                            queryFamily: QueryFamily.Item,
                            queryKey: options.exclude_deleted
                                ? ItemQueryKey.GetTaskExcludingDeleted
                                : ItemQueryKey.GetTask,
                            queryParams: [task_id],
                        },
                        async (err: Error | null, result: any) => {
                            if (err) {
                                para_cb({
                                    err: 'Internal server error',
                                    status: 500,
                                    ECODE: 'ITEM_072',
                                } as unknown as Error);
                                return;
                            }

                            if (result.rows.length === 0) {
                                para_cb({
                                    err: 'Task not found',
                                    status: 404,
                                    ECODE: 'ITEM_073',
                                } as unknown as Error);
                                return;
                            }

                            await subcategoryTeamPromise;
                            [task_to_copy] = result.rows;
                            original_team = task_to_copy.workspace_id || task_to_copy.team_id || team_id;
                            task_to_copy.workspace_id =
                                subcategory_team || task_to_copy.workspace_id || task_to_copy.team_id || team_id;

                            const parent = options.parent || task_to_copy.parent;

                            const limitConfig = shouldPreventCopyTaskBySubtaskLimit(1);
                            if (!options.subtask_count_validation_performed) {
                                // parent should be null for non subtasks
                                try {
                                    const isSubtask = !!parent;
                                    ({ subtaskCount: subtaskCountNotArchived, subtaskCountArchived } =
                                        await getSubtaskCountForTaskAsync(task_to_copy.id, {
                                            proxyClient,
                                            isSubtask,
                                        }));
                                    subtask_count = subtaskCountNotArchived + subtaskCountArchived;

                                    if (limitConfig?.limit != null && subtask_count > limitConfig.limit) {
                                        logger.error({
                                            msg: `Task has too many nested subtasks, aborting copy`,
                                            subtask_count,
                                            ECODE: 'ITEM_076',
                                            task_id,
                                            parent,
                                        });
                                        para_cb({
                                            err: 'Too many subtasks',
                                            status: 400,
                                            ECODE: 'ITEM_076',
                                        } as unknown as Error);
                                        return;
                                    }

                                    // if we are copying a subtask to the same list then we need to check and see if the copied
                                    // task (with subtasks) will violate the subtask limit.
                                    // If the options subcategory does not match the subcategory of the parent task then we remove
                                    // the parent for the new task in a process slightly further down.

                                    if (isSubtask && task_to_copy.subcategory === options.subcategory_id) {
                                        await subtaskLimitChecker.checkSubtaskLimitForRootTask(
                                            task_to_copy.workspace_id,
                                            parent,
                                            {
                                                newSubtasks: subtaskCountNotArchived,
                                                newArchivedSubtasks: subtaskCountArchived,
                                            }
                                        );
                                    } else {
                                        // otherwise consider the new (copied) task the root of its own tree
                                        await subtaskLimitChecker.checkSubtaskLimitForRootTask(
                                            task_to_copy.workspace_id,
                                            task_to_copy.id,
                                            { newSubtasks: 0, newArchivedSubtasks: 0 },
                                            {
                                                currentSubtaskCount: subtaskCountNotArchived,
                                                currentArchivedSubtaskCount: subtaskCountArchived,
                                            }
                                        );
                                    }
                                } catch (subtaskErr) {
                                    if (subtaskErr instanceof SubtaskLimitError) {
                                        para_cb(subtaskErr);
                                        return;
                                    }
                                    para_cb(new TaskError(subtaskErr, 'Unable to retrieve subtask count'));
                                    return;
                                }
                            }

                            if (!task_to_copy.workspace_id && task_to_copy.parent) {
                                try {
                                    const parentResult = await proxyClient.replicaQueryAsync({
                                        queryFamily: QueryFamily.Item,
                                        queryKey: options.exclude_deleted
                                            ? ItemQueryKey.GetTaskExcludingDeleted
                                            : ItemQueryKey.GetTask,
                                        queryParams: [task_to_copy.parent],
                                    });

                                    if (parentResult.rows.length === 0) {
                                        logger.error({
                                            msg: 'Task to copy parent not found',
                                            task_id,
                                            parent: task_to_copy.parent,
                                        });
                                    } else {
                                        task_to_copy.workspace_id = parentResult.rows[0].workspace_id;
                                    }
                                } catch (e) {
                                    logger.error({
                                        msg: 'Failed to query task to copy parent',
                                        task_id,
                                        parent: task_to_copy.parent,
                                        err: e,
                                    });
                                }
                            }

                            task_to_copy.original_parent = parent;
                            const task_to_copy_parent = task_to_copy.parent;

                            if (!options.top_level_parent) {
                                options.top_level_parent = task_to_copy.parent;
                            }

                            if (in_params.name) {
                                options.name = in_params.name;
                            }

                            if (in_params.content) {
                                options.content = true;
                                if (options.from_form && in_params.content.ops) {
                                    let { ops } = in_params.content;
                                    const convertOpsToText = (op: any) =>
                                        op.reduce((acc: string, o: any) => {
                                            if (o.insert) {
                                                return `${acc}\n${o.insert}`;
                                            }
                                            return acc;
                                        }, '');
                                    let text_content = convertOpsToText(ops);
                                    let parsed_content: { ops?: any[] } = {};
                                    try {
                                        parsed_content = JSON.parse(task_to_copy.content);
                                    } catch (e) {
                                        // do nothing
                                    }
                                    if (parsed_content && parsed_content.ops) {
                                        ops = [...ops, ...parsed_content.ops];
                                        text_content += `\n${convertOpsToText(parsed_content.ops)}`;
                                    }
                                    task_to_copy.content = JSON.stringify({ ops });
                                    task_to_copy.text_content = text_content;
                                } else {
                                    task_to_copy.content = JSON.stringify(in_params.content);
                                    task_to_copy.text_content = in_params.content;
                                }
                            }

                            if (in_params.due_date) {
                                options.old_due_date = true;
                                task_to_copy.due_date = parseInt(in_params.due_date.toString(), 10);
                                task_to_copy.due_date_time = !!in_params.due_date_time;
                            }

                            if (in_params.start_date) {
                                options.remap_start_date = false;
                                options.old_start_date = true;
                                task_to_copy.start_date = parseInt(in_params.start_date.toString(), 10);
                                task_to_copy.start_date_time = !!in_params.start_date_time;
                            }

                            if (in_params.priority) {
                                options.priority = true;
                                task_to_copy.priority = in_params.priority;
                            }

                            if (in_params.status) {
                                options.old_status = true;
                                task_to_copy.status = in_params.status;
                            }

                            if (in_params.time_estimate) {
                                task_to_copy.time_estimate = in_params.time_estimate;
                            }

                            if (in_params.time_estimate_string) {
                                task_to_copy.time_estimate_string = in_params.time_estimate_string;
                            }

                            if (options.custom_type) {
                                customType = new CustomType(task_to_copy.custom_type);
                                if (
                                    customType.isUsableCustomType() &&
                                    !(options.template || options.template_v2 || options.template_v2_child) &&
                                    !(check_template_entitlements && denyExtendedCustomTaskTypeUsage)
                                ) {
                                    try {
                                        const targetWorkspaceId = Number(task_to_copy.workspace_id);

                                        // For recurring or automation tasks, if the workspace reached the limit for custom task types
                                        // instead of throwing an error default to `null` and for all other cases throw
                                        // an exception.
                                        const allowCustomType = await entitlementService.checkEntitlement(
                                            targetWorkspaceId,
                                            EntitlementName.CustomItems,
                                            {
                                                throwOnDeny: denyExtendedCustomTaskTypeUsage,
                                            }
                                        );
                                        if (!allowCustomType) {
                                            customType = null;
                                        }
                                    } catch (e) {
                                        para_cb({
                                            err: 'Extended custom task type usage limit',
                                            status: 400,
                                            ECODE: 'ITEM_180',
                                        } as unknown as Error);
                                        return;
                                    }
                                }
                            }

                            try {
                                if (options.subcategory_id) {
                                    await checkListTaskLimit(task_to_copy.workspace_id, {
                                        [options.subcategory_id]: 1,
                                    });
                                }
                            } catch (e) {
                                para_cb(e as Error);
                                return;
                            }

                            resolveTaskPromise();

                            if (!task_to_copy_parent) {
                                para_cb();
                                return;
                            }
                            proxyClient.replicaQuery(
                                {
                                    queryFamily: QueryFamily.Item,
                                    queryKey: ItemQueryKey.CheckSubtaskExistence,
                                    queryParams: [task_to_copy_parent, task_id],
                                },
                                (nestedErr, results) => {
                                    if (nestedErr) {
                                        para_cb({
                                            err: 'Internal server error',
                                            status: 500,
                                            ECODE: 'ITEM_091',
                                        } as unknown as Error);
                                        return;
                                    }
                                    if (results.rows.length) {
                                        has_nested = true;
                                    }

                                    if (options.template_v2 || !parent || !options.subcategory_id) {
                                        para_cb();
                                        return;
                                    }
                                    const quitIfParentIsDeleted = shouldPreventCopyingTaskWithDeletedParent();
                                    const queryFunc = quitIfParentIsDeleted ? db.replicaQuery : db.readQuery;
                                    queryFunc(
                                        'SELECT subcategory, deleted FROM task_mgmt.items WHERE id = $1',
                                        [parent],
                                        { useMaster: quitIfParentIsDeleted },
                                        (parentErr: any, parentResult: any) => {
                                            if (parentErr) {
                                                para_cb({
                                                    err: 'Internal server error',
                                                    status: 500,
                                                    ECODE: 'ITEM_084',
                                                } as unknown as Error);
                                                return;
                                            }

                                            // Stop copy operation IF:
                                            // Parent not found matching provided ID (hard-deleted possibly)
                                            // OR
                                            // Parent is found but is soft-deleted AND this is not a template or recursive subtask call
                                            if (
                                                quitIfParentIsDeleted &&
                                                (parentResult.rows.length === 0 ||
                                                    (!options.from_template &&
                                                        !options.from_parent_task &&
                                                        parentResult.rows[0].deleted))
                                            ) {
                                                para_cb(
                                                    new CopyError(
                                                        `Parent task with id ${parent} not found`,
                                                        'ITEM_252',
                                                        400
                                                    )
                                                );
                                                return;
                                            }

                                            if (
                                                parentResult.rows[0].subcategory &&
                                                options.subcategory_id !== parentResult.rows[0].subcategory
                                            ) {
                                                task_to_copy.parent = null;
                                                options.subtask_parent = -1;
                                                options.parent = null;
                                            }

                                            para_cb();
                                        }
                                    );
                                }
                            );
                        }
                    );
                },

                async old_dependencies(para_cb) {
                    if (
                        (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                            CopyTaskOperations.loadDependencies,
                            userid,
                            options.entryEndpoint
                        )
                    ) {
                        try {
                            const dependencies = await dataLoader.loadDependencies(task_id);
                            old_dependencies = { ...old_dependencies, ...dependencies };
                            para_cb();
                        } catch (oldDependenciesError: any) {
                            para_cb(oldDependenciesError, null);
                        }
                        return;
                    }
                    proxyClient.replicaQuery(
                        {
                            queryFamily: QueryFamily.Item,
                            queryKey: ItemQueryKey.GetTaskDependencies,
                            queryParams: [task_id],
                        },
                        (err: Error | null, result: any) => {
                            if (err) {
                                para_cb({ err: 'Internal server error', status: 500, ECODE: '' } as unknown as Error);
                            } else {
                                result.rows.forEach((row: any) => {
                                    if (!old_dependencies[row.task_id]) {
                                        old_dependencies[row.task_id] = [];
                                    }
                                    if (old_dependencies[row.task_id].indexOf(row.depends_on) < 0) {
                                        old_dependencies[row.task_id].push(row.depends_on);
                                    }
                                });
                                para_cb();
                            }
                        }
                    );
                },

                async old_links(para_cb) {
                    if (
                        (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                            CopyTaskOperations.loadLinks,
                            userid,
                            options.entryEndpoint
                        )
                    ) {
                        try {
                            const links = await dataLoader.loadLinks(task_id, options.is_list_copy || false);
                            old_links = { ...old_links, ...links };
                            para_cb();
                        } catch (oldLinksError: any) {
                            para_cb(oldLinksError, null);
                        }
                        return;
                    }
                    proxyClient.replicaQuery(
                        {
                            queryFamily: QueryFamily.Item,
                            queryKey: ItemQueryKey.GetTaskLinks,
                            queryParams: [task_id],
                            options: { ignoreTemplateRelations: options.is_list_copy || false },
                        },
                        (err: Error | null, result: any) => {
                            if (err) {
                                para_cb({ err: 'Internal server error', status: 500, ECODE: '' } as unknown as Error);
                            } else {
                                result.rows.forEach((row: any) => {
                                    if (!old_links[row.task_id]) {
                                        old_links[row.task_id] = [];
                                    }
                                    if (old_links[row.task_id].indexOf(row.link_id) < 0) {
                                        old_links[row.task_id].push(row.link_id);
                                    }
                                });
                                para_cb();
                            }
                        }
                    );
                },

                async validAssignees(para_cb) {
                    if (
                        (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                            CopyTaskOperations.loadValidAssignees,
                            userid,
                            options.entryEndpoint
                        )
                    ) {
                        try {
                            let workspaceId = options.team_id ?? options.workspace_id;
                            if (!workspaceId) {
                                await taskToCopyPromise;
                                workspaceId = task_to_copy?.workspace_id;
                            }
                            valid_assignees = await dataLoader.loadValidAssignees(task_id, workspaceId, {
                                ...options,
                                saving_template,
                            });
                            para_cb();
                        } catch (validAssigneesError: any) {
                            para_cb(validAssigneesError, null);
                        }
                        return;
                    }

                    let assignees: string[] = options.assignees ?? [];

                    if (options.default_template) {
                        valid_assignees = assignees;
                        para_cb();
                        return;
                    }
                    if (!(assignees.length || options.old_assignees)) {
                        para_cb();
                        return;
                    }

                    if (options.public_template && !assignees.length && options.old_assignees) {
                        logger.info({
                            msg: 'Loading old assignees for public template',
                        });

                        try {
                            const { rows: assigneesResult } = await proxyClient.replicaQueryAsync({
                                queryFamily: QueryFamily.Item,
                                queryKey: ItemQueryKey.GetTaskAssignees,
                                queryParams: [task_id],
                            });

                            assignees = assigneesResult.map((assignee: { userid: number }) => String(assignee.userid));
                        } catch (e) {
                            para_cb(e as Error);
                        }
                    }

                    try {
                        let workspaceId = options.team_id ?? options.workspace_id;
                        if (!workspaceId) {
                            await taskToCopyPromise;
                            workspaceId = task_to_copy?.workspace_id;
                        }
                        const query_input = {
                            type: AssigneeValidationTypes.copyTask,
                            saving_template,
                            subcategory_id: options.subcategory_id,
                            task_id,
                            assignees,
                            old_assignees: options.old_assignees,
                            userId: Number(userid),
                            workspaceId: Number(workspaceId),
                            queryFunc: db.promiseReplicaQuery,
                        };

                        const query_factory = getValidAssigneesQueryFactory();

                        const query_result = await query_factory.buildExecuteQuery(
                            query_input,
                            async (q, p): Promise<DbQueryResult<{ userid: string }>> => {
                                try {
                                    return await db.replicaQueryAsync(q, p);
                                } catch (err) {
                                    logger.error({
                                        msg: 'Failed to look up team members',
                                        status: 500,
                                        ECODE: 'ITEM_086',
                                        err,
                                    });
                                    throw new CopyError('Internal server error', 'ITEM_086', 500);
                                }
                            },
                            buildQueryComparatorFromFlag(ShadowTrafficQuery.validAssigneesQuery)
                        );

                        valid_assignees = query_result.rows.map(row => row.userid);
                        para_cb();
                    } catch (e) {
                        para_cb(e as Error);
                    }
                },

                async validGroupAssignees(para_cb) {
                    if (
                        (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                            CopyTaskOperations.loadGroupAssignees,
                            userid,
                            options.entryEndpoint
                        )
                    ) {
                        try {
                            valid_group_assignees = await dataLoader.loadGroupAssignees(task_id, {
                                ...options,
                                saving_template,
                            });
                            para_cb();
                        } catch (groupAssigneesError: any) {
                            para_cb(groupAssigneesError, null);
                        }
                        return;
                    }
                    if (
                        options.public_template ||
                        !(
                            (options.group_assignees && options.group_assignees.length) ||
                            options.old_assignees ||
                            saving_template
                        )
                    ) {
                        para_cb();
                        return;
                    }
                    try {
                        valid_group_assignees = await assigneeMod._validGroupAssignees(
                            options.group_assignees,
                            options.template_v2 || options.template_v2_child || options.old_assignees,
                            +options.subcategory_id,
                            task_id,
                            saving_template
                        );
                    } catch (err) {
                        para_cb(err as Error);
                        return;
                    }
                    para_cb();
                },

                async validFollowers(para_cb) {
                    if (
                        (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                            CopyTaskOperations.loadValidFollowers,
                            userid,
                            options.entryEndpoint
                        )
                    ) {
                        try {
                            let workspaceId = options.team_id ?? options.workspace_id;
                            if (!workspaceId) {
                                await taskToCopyPromise;
                                workspaceId = task_to_copy?.workspace_id;
                            }
                            valid_followers = await dataLoader.loadValidFollowers(task_id, workspaceId, {
                                ...options,
                                saving_template,
                            });
                            para_cb();
                        } catch (validFollowersError: any) {
                            para_cb(validFollowersError, null);
                        }
                        return;
                    }

                    if (
                        options.public_template ||
                        !((options.followers && options.followers.length) || options.old_followers || saving_template)
                    ) {
                        para_cb();
                        return;
                    }
                    try {
                        let workspaceId = options.team_id ?? options.workspace_id;
                        if (!workspaceId) {
                            await taskToCopyPromise;
                            workspaceId = task_to_copy?.workspace_id;
                        }

                        const query_input = {
                            userId: Number(userid),
                            workspaceId: Number(workspaceId),
                            task_id,
                            subcategory_id: options.subcategory_id,
                            template_v2: options.template_v2,
                            template_v2_child: options.template_v2_child,
                            saving_template,
                            followers: options.followers,
                            old_followers: options.old_followers,
                            queryFunc: db.promiseReplicaQuery,
                        };

                        const query_factory = getValidFollowersQueryFactory();

                        const query_result = await query_factory.buildExecuteQuery(
                            query_input,

                            async (q, p): Promise<DbQueryResult<{ userid: string }>> => {
                                try {
                                    return await db.replicaQueryAsync(q, p);
                                } catch (err) {
                                    logger.error({
                                        msg: 'Failed to look up team members',
                                        status: 500,
                                        ECODE: 'ITEM_085',
                                        err,
                                    });
                                    throw new CopyError('Internal server error', 'ITEM_085', 500);
                                }
                            },
                            buildQueryComparatorFromFlag(ShadowTrafficQuery.validFollowersQuery)
                        );

                        valid_followers = [...new Set(query_result.rows.map(row => row.userid))];
                        para_cb();
                    } catch (err) {
                        para_cb(err as Error);
                    }
                },

                async validGroupFollowers(para_cb) {
                    if (
                        (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                            CopyTaskOperations.loadGroupFollowers,
                            userid,
                            options.entryEndpoint
                        )
                    ) {
                        try {
                            valid_group_followers = await dataLoader.loadGroupFollowers(task_id, {
                                ...options,
                                saving_template,
                            });
                            para_cb();
                        } catch (groupFollowersError: any) {
                            para_cb(groupFollowersError, null);
                        }
                        return;
                    }
                    if (
                        options.public_template ||
                        !(
                            (options.group_followers && options.group_followers.length) ||
                            options.old_followers ||
                            saving_template
                        )
                    ) {
                        para_cb();
                        return;
                    }
                    try {
                        valid_group_followers = await assigneeMod._validGroupFollowers(
                            task_id,
                            +options.subcategory_id,
                            options.group_followers,
                            saving_template
                        );
                        para_cb();
                    } catch (err) {
                        para_cb(err as Error);
                    }
                },

                async checkClickApps(para_cb) {
                    if (
                        (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                            CopyTaskOperations.loadClickAppsEnablement,
                            userid,
                            options.entryEndpoint
                        )
                    ) {
                        try {
                            ({
                                pointsPerAssignee: points_per_assignee,
                                estimatesPerAssignee: estimates_per_assignee,
                                nestedSubtasks: nested_subtasks,
                            } = await dataLoader.loadClickAppsEnablement(task_id, options));
                            para_cb();
                        } catch (clickAppsError: any) {
                            para_cb(clickAppsError, null);
                        }
                        return;
                    }
                    if (options.from_template) {
                        points_per_assignee = options.points_per_assignee;
                        estimates_per_assignee = options.estimates_per_assignee;
                        if (options.nested_subtasks != null) {
                            nested_subtasks = options.nested_subtasks;
                            para_cb();
                            return;
                        }
                    }

                    let results;
                    try {
                        results = await proxyClient.replicaQueryAsync({
                            queryFamily: QueryFamily.Item,
                            queryKey: ItemQueryKey.GetClickApps,
                            queryParams: [task_id],
                        });

                        [{ points_per_assignee = false, estimates_per_assignee = false, nested_subtasks = false }] =
                            results && results.rows && results.rows.length ? results.rows : [{}];

                        team_id = team_id || results.rows[0]?.team_id;

                        if (options.from_template) {
                            points_per_assignee = options.points_per_assignee;
                            estimates_per_assignee = options.estimates_per_assignee;
                            if (options.nested_subtasks != null) {
                                nested_subtasks = options.nested_subtasks;
                            }
                        }
                        para_cb();
                    } catch (e) {
                        para_cb(e as Error);
                    }
                },

                async inbox_orderindexes(para_cb) {
                    if (
                        (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                            CopyTaskOperations.loadInboxOrderIndexes,
                            userid,
                            options.entryEndpoint
                        )
                    ) {
                        try {
                            new_inbox_users = await dataLoader.loadInboxOrderIndexes(
                                task_id,
                                options.public_template,
                                options.assignees
                            );
                            para_cb();
                        } catch (orderindexesError: any) {
                            para_cb(orderindexesError, null);
                        }
                        return;
                    }
                    if (options.public_template) {
                        para_cb();
                        return;
                    }

                    const query = `
                        SELECT group_members.userid
                        FROM task_mgmt.group_assignees
                        INNER JOIN task_mgmt.group_members
                            ON group_members.group_id = group_assignees.group_id
                        WHERE group_assignees.task_id = $1

                        UNION

                        SELECT assignees.userid
                        FROM task_mgmt.assignees
                        WHERE task_id = $1`;
                    const params = [task_id];

                    db.replicaQuery(query, params, (err: Error | null, result: any) => {
                        if (err) {
                            para_cb(new CopyError(err, ''));
                            return;
                        }

                        result.rows.forEach((row: any) => {
                            if (new_inbox_users.find(uid => Number(uid) === Number(row.userid))) {
                                return;
                            }

                            new_inbox_users.push(row.userid);
                        });

                        para_cb();
                    });
                },

                async subcategoryTeam(para_cb) {
                    if (await shouldUseWorkflows()) {
                        para_cb();
                        return;
                    }
                    if (await shouldUseNewLogic(CopyTaskOperations.loadSubcategoryTeamAndProject)) {
                        try {
                            const subcategoryWorkspaceAndSpace = await dataLoader.loadSubcategoryWorkspaceAndSpace(
                                options.subcategory_id
                            );
                            if (subcategoryWorkspaceAndSpace) {
                                subcategory_team = subcategoryWorkspaceAndSpace.workspaceId;
                                project_id = subcategoryWorkspaceAndSpace.spaceId;
                            }
                            resolveSubcategoryTeamPromise();
                            para_cb();
                        } catch (subcategoryTeamError: any) {
                            para_cb(subcategoryTeamError, null);
                        }
                        return;
                    }
                    if (!options.subcategory_id) {
                        resolveSubcategoryTeamPromise();
                        para_cb();
                        return;
                    }

                    const query = `
                        SELECT coalesce(subcategories.team_id, categories.team_id, projects.team) AS team_id, projects.id AS project_id
                        FROM task_mgmt.subcategories
                        LEFT JOIN task_mgmt.categories
                            ON categories.id = subcategories.category
                        LEFT JOIN task_mgmt.projects
                            ON projects.id = categories.project_id
                        WHERE subcategories.id = $1`;
                    const params = [options.subcategory_id];

                    // We cannot use replicaQuery here because when creating from a template, the subcategory
                    // might not yet exist on the replica, which would result in the wrong workspace_id being set
                    // on the task. This was observed and fixed in https://github.com/time-loop/clickup/pull/16303/files
                    db.readQuery(query, params, (err1: Error | null, result1: any) => {
                        if (err1) {
                            para_cb(new CopyError(err1, 'ITEM_171'));
                            return;
                        }

                        if (result1.rows.length) {
                            subcategory_team = result1.rows[0].team_id;
                            project_id = result1.rows[0].project_id;
                        }

                        resolveSubcategoryTeamPromise();
                        para_cb();
                    });
                },

                async syncBlocks(para_cb) {
                    if (
                        (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                            CopyTaskOperations.syncBlocks,
                            userid,
                            options.entryEndpoint
                        )
                    ) {
                        try {
                            const { syncBlocks: loadedSyncBlocks, syncBlocksIdMapping } =
                                await dataLoader.loadSyncBlocks(
                                    task_id,
                                    saving_template,
                                    Boolean(options.from_template)
                                );
                            syncBlocks.push(...loadedSyncBlocks);
                            Object.entries(syncBlocksIdMapping).forEach(([key, value]) => {
                                sync_blocks_old_to_new[key] = value;
                            });
                            para_cb();
                        } catch (syncBlocksError: any) {
                            para_cb(syncBlocksError, null);
                        }
                        return;
                    }
                    if (!(saving_template || options.from_template)) {
                        para_cb();
                        return;
                    }

                    try {
                        syncBlocks.push(...(await getSyncBlocksByParentIds([task_id], ObjectType.TASK, proxyClient)));
                        syncBlocks.forEach(syncBlock => {
                            sync_blocks_old_to_new[syncBlock.id] = uuidv4();
                        });
                        para_cb();
                    } catch (syncBlockErr) {
                        para_cb(syncBlockErr as Error);
                    }
                },
            }),
            async (err: Error | null) => {
                if (err) {
                    logger.error({
                        msg: 'Checks before copyTask failed',
                        taskId: task_id,
                        error: err,
                    });
                    finish(err);
                    return;
                }

                const now = new Date().getTime();
                let new_id: string; // task_id from bigint id (encoded)
                let int_id: number; // bigint

                // Check template entitlements
                if (check_template_entitlements) {
                    try {
                        await checkTemplateEntitlements({
                            team_id: task_to_copy.workspace_id,
                            template_id: task_id,
                            template_type: 'task',
                            proxyClient,
                            ...(options.custom_type && !denyExtendedCustomTaskTypeUsage
                                ? { entitlements_to_skip: new Set([EntitlementName.CustomItems]) }
                                : {}),
                        });
                    } catch (entitlement_err: unknown) {
                        logger.error({
                            msg: 'Template entitlement check failed in copyTask',
                            taskId: task_id,
                            error: entitlement_err,
                        });
                        finish(entitlement_err);
                        return;
                    }
                }

                logger.info({
                    msg: '_copyTask for workspace',
                    workspaceId: task_to_copy.workspace_id,
                    originalWorkspaceId: original_team,
                });

                if (shouldBlockCopyTaskForWorkspace(task_to_copy.workspace_id)) {
                    finish({
                        err: 'Copy task blocked for workspace',
                        status: 500,
                        ECODE: 'ITEM_307',
                        workspace_id: task_to_copy.workspace_id,
                    });
                    return;
                }

                task_to_copy.start_date = cUParseInt(task_to_copy.start_date);
                task_to_copy.due_date = cUParseInt(task_to_copy.due_date);

                let new_start_date: number = null;
                let new_start_date_time = task_to_copy.start_date_time ?? false; // We don't want nulls in the db.
                let new_due_date: number = null;
                let new_due_date_time = task_to_copy.due_date_time ?? false;
                let new_duration: DateInterval = task_to_copy.duration ?? null;
                let new_duration_is_elapsed = options.skip_weekends
                    ? !options.skip_weekends
                    : task_to_copy.duration_is_elapsed ?? false;
                let isUsingRemapDateOfTaskV2 = false;

                if (saving_template || options.from_dupe_task || options.skip_date_remapping) {
                    // preserve dates regardless of options.old_start_date and options.old_start_date
                    if (options.old_due_date) {
                        new_due_date = task_to_copy.due_date;
                    }
                    if (options.old_start_date) {
                        new_start_date = task_to_copy.start_date;
                    }
                    if (options.old_duration) {
                        new_duration = task_to_copy.duration;
                        new_duration_is_elapsed = task_to_copy.duration_is_elapsed;
                    }
                } else if (options.apply_gantt_dates) {
                    // gantt rescheduling
                    if (options.gantt_dates.has(task_id)) {
                        const { start_date, start_date_time, due_date, due_date_time, duration, duration_is_elapsed } =
                            options.gantt_dates.get(task_id);

                        new_start_date = options.old_start_date ? start_date : null;
                        new_start_date_time = options.old_start_date ? start_date_time : false;
                        new_due_date = options.old_due_date ? due_date : null;
                        new_due_date_time = options.old_due_date ? due_date_time : false;
                        new_duration = options.old_duration ? duration : null;
                        new_duration_is_elapsed = options.old_duration ? duration_is_elapsed : false;
                    }
                } else if (options.copy_subtask && options.from_recur && !options.subtask_date) {
                    // Task Recurring: Check if user want to remap subtask date,
                    new_start_date = null;
                    new_start_date_time = false;
                    new_due_date = null;
                    new_due_date_time = false;
                    new_duration = null;
                    new_duration_is_elapsed = false;
                } else {
                    isUsingRemapDateOfTaskV2 = true;

                    // Do this ONLY for task templates when task REMAP is being used.
                    // Remapping logic is different for task templates, then from other templates
                    const remapOptions = { ...options };
                    if (options.from_task_template && options.due_date) {
                        remapOptions.latest_due_date = task_to_copy.due_date;
                        remapOptions.remap_start_date = Boolean(options.subtasks);
                    }
                    // legacy rescheduling
                    const original_old_start_date_option = options.old_start_date;
                    ({
                        new_start_date,
                        new_due_date,
                        new_start_date_shift,
                        new_due_date_shift,
                        new_duration,
                        new_duration_is_elapsed,
                    } = remapDateOfTaskV2(task_to_copy, remapOptions, userid));

                    // band-aid: remapDateOfTaskV2() mutates options.old_start_date :(
                    // which causes that start_dates get copied even if options.old_start_date === false
                    options.old_start_date = original_old_start_date_option;
                    new_start_date = options.old_due_date || options.due_date ? new_start_date : null;
                    new_due_date = options.old_due_date || options.due_date ? new_due_date : null;

                    if (options.copy_subtask) {
                        options.subtask_start_date_shift += new_start_date_shift;
                        options.subtask_due_date_shift += new_due_date_shift;
                    } else {
                        options.task_start_date_shift += new_start_date_shift;
                        options.task_due_date_shift += new_due_date_shift;
                    }
                }

                // We don't want date to be 0. We want to treat 0 as null(no date), not as JAN 01 1970.
                if (Number.isNaN(Number(new_start_date)) || Number(new_start_date) === 0) {
                    new_start_date = null;
                }

                if (Number.isNaN(Number(new_due_date)) || Number(new_due_date) === 0) {
                    new_due_date = null;
                }

                let sent_due_date_notif = false;
                const twenty_hours = 20 * 60 * 60 * 1000;

                try {
                    if (
                        options.due_date &&
                        parseInt(options.due_date.toString(), 10) < new Date().getTime() &&
                        options.due_date_time
                    ) {
                        sent_due_date_notif = true;
                    } else if (
                        options.due_date &&
                        parseInt(options.due_date.toString(), 10) + twenty_hours < new Date().getTime() &&
                        !options.due_date_time
                    ) {
                        sent_due_date_notif = true;
                    }
                } catch (e) {
                    // caught
                }

                if (options.content == null) {
                    options.content = true;
                }

                if (in_params.tags && in_params.tags.length) {
                    options.old_tags = false;
                    options.tags = in_params.tags;
                }

                if (options.old_tags == null) {
                    options.old_tags = true;
                }

                if (options.old_checklists == null) {
                    options.old_checklists = true;
                }

                // Checklist item state used to not carry over, to keep the current behavior we won't set the default
                // to true.
                if (options.old_checklist_item_state == null) {
                    options.old_checklist_item_state = false;
                }

                let text_content: string;
                let html_content: string;

                if (!task_to_copy.content) {
                    task_to_copy.content = generateNewLineOp();
                }
                if (options.content === true) {
                    if (!isEmpty(sync_blocks_old_to_new) && (saving_template || options.from_template)) {
                        const quillContent = parseQuillContent(task_to_copy.content);
                        task_to_copy.content = JSON.stringify(
                            replaceSyncBlocksInContent(quillContent, sync_blocks_old_to_new)
                        );
                    }
                    content = task_to_copy.content;
                    text_content = task_to_copy.text_content;
                    html_content = task_to_copy.html_content;
                } else if (options.new_content) {
                    content = options.new_content;
                }

                let status = default_open_status;
                if (options.old_status || saving_template) {
                    status = task_to_copy.status;
                } else if (options.status) {
                    status = options.status;
                } else if (options.recur_new_status) {
                    status = options.recur_new_status;
                }

                if (status && status !== 'Closed' && status !== 'Open' && !status.type) {
                    status = status.toLowerCase();

                    if (status === 'open') {
                        status = 'Open';
                    } else if (status === 'closed') {
                        status = 'Closed';
                    }
                }

                // for quick start template tasks on prod
                if (options.quick_start_template) {
                    task_to_copy.name = task_to_copy.name.replace(/[\r\n]+/gm, '');

                    if (task_to_copy.id === config.get<string>('quick_start_template.assignee_task')) {
                        valid_assignees.push(userid);
                    }

                    if (task_to_copy.id === config.get<string>('quick_start_template.due_date_task')) {
                        const tomorrow = moment()
                            .tz(options.user_tz || 'UTC')
                            .add(1, 'days')
                            .valueOf();

                        new_due_date = tomorrow;
                        new_due_date_time = false;
                    }
                }

                let assignee_hist_items: any[] = [];
                let createHistId: any;
                let order_indexes: any = null;
                let recur_settings_id: string = null;
                let time_estimate_string: string = null;
                let time_estimate: any = null;
                let permanent_template_id: string;
                let destination_subcategory_id: string; // subcategory where copied task will live
                let returnImmediatelyEarlier = false;

                let attachmentMap = {};
                const docAttachmentMap = { view: {}, page: {} };

                async.series(
                    timedSteps(METRIC_NAME, [
                        {
                            returnImmediatelyEarlier: async series_cb => {
                                returnImmediatelyEarlier = (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                                    CopyTaskOperations.returnImmediatelyEarlier,
                                    userid,
                                    options.entryEndpoint
                                );
                                series_cb();
                            },
                        },
                        {
                            checkWorkspace: async series_cb => {
                                if (
                                    (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                                        CopyTaskOperations.preventCrossWorkspaceDuplicateTask,
                                        userid,
                                        options.entryEndpoint
                                    )
                                ) {
                                    try {
                                        await constraintsChecker.assertIsNotCrossWorkspaceDuplicateTask(
                                            subcategory_team,
                                            original_team,
                                            options
                                        );
                                        series_cb();
                                    } catch (notCrossWorkspaceDuplicateTaskError: any) {
                                        series_cb(notCrossWorkspaceDuplicateTaskError, null);
                                    }
                                    return;
                                }
                                if (
                                    from_v2_template ||
                                    options.public_template ||
                                    !options.subcategory_id ||
                                    options.public_template_child_copy ||
                                    options.from_template ||
                                    options.from_parent_template ||
                                    options.template_v2
                                ) {
                                    series_cb();
                                    return;
                                }
                                // if we're not saving a template, the subcategory team should always match the team of
                                // the task being created
                                if (subcategory_team !== String(original_team)) {
                                    const msg = 'List and task must be in the same workspace';
                                    const ECODE = TaskErrorCodes.CopyTaskWorkspaceMismatchError;

                                    logger.error({
                                        task_id,
                                        task_to_copy_workspace_id: task_to_copy.workspace_id,
                                        task_to_copy_team_id: task_to_copy.team_id,
                                        userid,
                                        msg,
                                        ECODE,
                                        subcategory_team,
                                        original_team,
                                        copy_options: JSON.stringify(options),
                                    });

                                    if (shouldPreventCopyTaskAcrossWorkspaces()) {
                                        series_cb({
                                            msg,
                                            err: msg,
                                            status: 400,
                                            ECODE,
                                        } as unknown as Error);
                                        return;
                                    }
                                }
                                series_cb();
                            },
                        },
                        // /.5 validate status
                        // OVM: None
                        {
                            validateStatus: async series_cb => {
                                if (
                                    (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                                        CopyTaskOperations.loadStatus,
                                        userid,
                                        options.entryEndpoint
                                    )
                                ) {
                                    try {
                                        status = await dataLoader.loadStatus(default_open_status, {
                                            ...options,
                                            original_subcategory: task_to_copy.subcategory,
                                            original_status: task_to_copy.status,
                                            saving_template,
                                        });
                                        series_cb();
                                    } catch (validateStatusError: any) {
                                        series_cb(validateStatusError, null);
                                    }
                                    return;
                                }
                                if (options.parent_is_template || options.template) {
                                    series_cb();
                                    return;
                                }
                                let subcategory = options.subcategory_id || task_to_copy.subcategory;
                                if (options.template_v2_child) {
                                    subcategory = task_to_copy.subcategory;
                                }
                                if (options.from_v2_template_child) {
                                    subcategory = options.subcategory_id;
                                }

                                db.readQuery(
                                    'SELECT statuses.status FROM task_mgmt.statuses, task_mgmt.subcategories, task_mgmt.categories WHERE statuses.status = $1 ANd subcategories.id = $2 AND subcategories.category = categories.id AND statuses.status_group = subcategories.status_group',
                                    [status, subcategory],
                                    (statusErr: Error | null, result: any) => {
                                        if (statusErr) {
                                            series_cb({
                                                err: 'Internal server error',
                                                status: 500,
                                                ECODE: 'ITEM_',
                                            } as unknown as Error);
                                        } else if (result.rows.length === 0) {
                                            logger.info(
                                                `[copy task] failed to validate status ${status} for subcategory ${subcategory}. Status set to ${default_open_status}`
                                            );
                                            status = default_open_status;
                                            series_cb();
                                        } else {
                                            series_cb();
                                        }
                                    }
                                );
                            },
                        },

                        // OVM: None - The task isn't created until we set the `id` from `int_id` so no UPDATE needed here
                        {
                            createNewTaskId: async series_cb => {
                                if (
                                    (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                                        CopyTaskOperations.generateTaskId,
                                        userid,
                                        options.entryEndpoint
                                    )
                                ) {
                                    try {
                                        ({ numericId: int_id, stringId: new_id } = await dataWriter.generateTaskId(
                                            task_to_copy.workspace_id
                                        ));
                                        series_cb();
                                    } catch (createNewTaskIdError: any) {
                                        series_cb(createNewTaskIdError);
                                    }
                                    return;
                                }
                                createNewTaskId({ team_id: task_to_copy.workspace_id }, (seq_err, _int_id) => {
                                    if (seq_err) {
                                        logger.error({
                                            msg: 'Failed to get Task Id',
                                            err: seq_err,
                                            workspace_id: task_to_copy.workspace_id,
                                        });
                                        series_cb(seq_err);
                                    } else {
                                        int_id = _int_id;
                                        new_id = base32.encode(int_id);
                                        series_cb();
                                    }
                                });
                            },
                        },

                        {
                            insertTaskIntoItems: async series_cb => {
                                if (
                                    (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                                        CopyTaskOperations.setItemId,
                                        userid,
                                        options.entryEndpoint
                                    )
                                ) {
                                    try {
                                        await dataWriter.setItemId(int_id, new_id, {
                                            ...options,
                                            project_id,
                                            source_workspace_id: task_to_copy.workspace_id,
                                            source_subcategory_id: task_to_copy.subcategory,
                                            source_task_parent: task_to_copy.parent,
                                        });
                                        series_cb();
                                    } catch (setItemIdError: any) {
                                        series_cb(setItemIdError);
                                    }
                                    return;
                                }
                                const taskVersionUpdates = [];
                                if (!task_to_copy.workspace_id) {
                                    logger.error({
                                        msg: 'No workspace ID available',
                                        ECODE: 'OVM_CT_900',
                                        OVM_CRITICAL: true,
                                    });
                                } else {
                                    // collect information about the task potentially being parented by a task or task template
                                    let taskParent = task_to_copy.parent;
                                    if (options.parent != null || options.top) {
                                        taskParent = options.parent === -1 || options.top ? null : options.parent;
                                    }
                                    const relationships = [];
                                    if (taskParent || options.subcategory_id || task_to_copy.subcategory) {
                                        relationships.push({
                                            type: ObjectRelationshipType.TASK_PARENT,
                                            object_type: taskParent ? ObjectType.TASK : ObjectType.LIST,
                                            object_id: taskParent || options.subcategory_id || task_to_copy.subcategory,
                                            workspace_id: task_to_copy.workspace_id,
                                        });
                                    }

                                    if (project_id) {
                                        relationships.push({
                                            type: ObjectRelationshipType.TASK_IN_SPACE,
                                            object_type: ObjectType.SPACE,
                                            object_id: project_id,
                                            workspace_id: task_to_copy.workspace_id,
                                        });
                                    }

                                    taskVersionUpdates.push({
                                        object_type: ObjectType.TASK,
                                        object_id: new_id,
                                        workspace_id: task_to_copy.workspace_id,
                                        operation: OperationType.CREATE,
                                        data: {
                                            relationships,
                                            context: { ws_key: options.ws_key },
                                        },
                                    });
                                    taskVersionUpdates.push({
                                        object_type: ObjectType.TASK_ACCESS,
                                        object_id: new_id,
                                        workspace_id: task_to_copy.workspace_id,
                                        operation: OperationType.CREATE,
                                    });
                                }

                                try {
                                    await writeAsync(
                                        'UPDATE task_mgmt.items SET id = $1 WHERE int_id = $2',
                                        [new_id, int_id],
                                        taskVersionUpdates
                                    );
                                } catch (insertErr) {
                                    series_cb(insertErr as Error);
                                    return;
                                }
                                series_cb(null);
                            },
                        },
                        {
                            setPermanentTemplateId: series_cb => {
                                permanent_template_id = saving_template
                                    ? options.old_permanent_template_id || `t-${new_id}`
                                    : null;
                                series_cb();
                            },
                        },

                        // orderindexes
                        // OVM: None - Order indexes are not part of the task models, so we don't need to worry it here
                        {
                            copyOrderIndexes: async series_cb => {
                                if (
                                    (await getCopyTaskTrafficControl()).shouldUseNewLogic(
                                        CopyTaskOperations.copyOrderIndexes,
                                        userid,
                                        options.entryEndpoint
                                    )
                                ) {
                                    try {
                                        order_indexes = await dataWriter.copyOrderIndexes(
                                            task_id,
                                            new_id,
                                            new_inbox_users,
                                            {
                                                ...options,
                                                orderindexStats,
                                                source_workspace_id: String(task_to_copy.workspace_id),
                                                source_subcategory_id: task_to_copy.subcategory,
                                                userid: +userid,
                                            }
                                        );
                                        if (options.return_immediately && returnImmediatelyEarlier) {
                                            cb(null, { id: new_id, permanent_template_id });
                                        }
                                        series_cb();
                                    } catch (copyOrderIndexesError: any) {
                                        series_cb(copyOrderIndexesError);
                                    }
                                    return;
                                }
                                try {
                                    logger.info({
                                        msg: 'Attempting to copy order indexes',
                                        taskId: task_id,
                                        newId: new_id,
                                        oldOrderIndex: options.old_orderindex || false,
                                        workspaceId: task_to_copy.workspace_id,
                                        subcategoryId: options.subcategory_id || task_to_copy.subcategory,
                                    });
                                    order_indexes = await taskOrderindexCopy.copyTaskOrderindexes(
                                        task_id,
                                        new_id,
                                        options.old_orderindex,
                                        orderindexStats,
                                        String(task_to_copy.workspace_id),
                                        {
                                            subcategory_id: options.subcategory_id || task_to_copy.subcategory,
                                            subtask_orderindex: options.subtask_orderindex,
                                            orderindex_offset: options.orderindex_offset || 0,
                                            from_parent_task: options.from_parent_task,
                                            proxyClient,
                                            userid: +userid,
                                            usecase: options.usecase,
                                        }
                                    );

                                    await promiseMapLimit(new_inbox_users || [], 5, async (assignee: string) => {
                                        const type = `inbox:${assignee}`;
                                        await orderindexMod.setTaskAtMaxIndex(new_id, type, {
                                            team_id: task_to_copy.workspace_id,
                                            extra: {
                                                from: 'copy_new_index_users',
                                                new_id,
                                                workspace_id: task_to_copy.workspace_id,
                                                template_v2: options.template_v2,
                                                subcategory_id: options.subcategory_id,
                                                copying: task_id,
                                                type,
                                            },
                                        });
                                    });

                                    if (options.return_immediately && returnImmediatelyEarlier) {
                                        cb(null, { id: new_id, permanent_template_id });
                                    }

                                    series_cb();
                                } catch (e) {
                                    series_cb(e as Error);
                                }
                            },
                        },

                        // 1 create item
                        // OVM: UPDATE - Task
                        {
                            updateNewTaskValues: async series_cb => {
                                if (task_to_copy.encrypted) {
                                    options.name = encrypt.encrypt(options.name);
                                }

                                if (options.recur_settings) {
                                    options.recurring = task_to_copy.recurring;
                                    recur_settings_id = uuidv4();
                                }

                                let { parent, subtask_parent } = task_to_copy;

                                // remove parent, when creating Template
                                if (options.parent === -1 || options.top) {
                                    parent = null;
                                } else if (options.parent) {
                                    parent = options.parent;
                                }

                                if (options.subtask_parent === -1) {
                                    subtask_parent = null;
                                } else if (options.subtask_parent) {
                                    subtask_parent = options.subtask_parent;
                                }

                                let archived = false;
                                if (options.archived === 1 || saving_template) {
                                    archived = task_to_copy.archived || false;
                                }

                                if (options.time_estimate !== false) {
                                    time_estimate_string = task_to_copy.time_estimate_string;
                                    time_estimate = task_to_copy.time_estimate;
                                }

                                destination_subcategory_id =
                                    options.template_v2 || options.parent_is_task_template
                                        ? null
                                        : options.subcategory_id || task_to_copy.subcategory;

                                const params = [
                                    options.name || task_to_copy.name, // $1
                                    content,
                                    text_content,
                                    now,
                                    now,
                                    'task',
                                    userid,
                                    status,
                                    null,
                                    destination_subcategory_id, // $10
                                    options.priority === true || options.priority == null
                                        ? task_to_copy.priority
                                        : null,
                                    parent,
                                    0,
                                    null,
                                    null,
                                    null,
                                    false,
                                    new_due_date,
                                    new_due_date_time ?? task_to_copy.due_date_time ?? false,
                                    sent_due_date_notif, // $20
                                    task_to_copy.points,
                                    time_estimate,
                                    options.template_v2 || options.template || false,
                                    new_start_date,
                                    new_start_date_time ?? task_to_copy.start_date_time ?? false,
                                    options.template_name,
                                    html_content,
                                    options.recurring || false,
                                    task_to_copy.recur_type,
                                    options.recur_next || task_to_copy.recur_next, // $30
                                    options.recur_new_status || task_to_copy.recur_new_status,
                                    task_to_copy.recur_due_date,
                                    task_to_copy.recur_data,
                                    options.recur_rule || task_to_copy.recur_rule,
                                    options.from_recur ? task_to_copy.recur_task : new_id,
                                    task_to_copy.recur_time,
                                    task_to_copy.recur_on,
                                    task_to_copy.recur_on_status,
                                    task_to_copy.recur_skip_missed,
                                    task_to_copy.recur_copy_original, // $40
                                    task_to_copy.recur_immediately,
                                    task_to_copy.encrypted,
                                    time_estimate_string,
                                    task_to_copy.recur_dst,
                                    task_to_copy.recur_tz,
                                    task_to_copy.recur_tz_offset,
                                    task_to_copy.recur_daily,
                                    options.skip_privacy ? false : task_to_copy.private || false,
                                    options.set_deleted ? now : null,
                                    options.set_deleted || false, // $50
                                    archived,
                                    options.default_template_id || null,
                                    int_id,
                                    recur_settings_id,
                                    in_params.form_id || form_id_map[task_to_copy.form_id],
                                    permanent_template_id,
                                    options.via || TaskCreatedVia.Template,
                                    nested_subtasks ? subtask_parent : null,
                                    dateInterval(new_duration).asIntervalString, // duration
                                    new_duration_is_elapsed, // duration_is_elapsed
                                ];

                                let team_id_query = '';
                                if (options.template_v2) {
                                    if (options.public_template) {
                                        if (options.team_id) {
                                            team_id_query = `
                                        team_id = $${params.push(options.team_id)},
                                        workspace_id = $${params.push(options.team_id)},
                                        `;
                                        } else {
                                            team_id_query = `
                                        team_id = (
                                            SELECT projects.team
                                            FROM task_mgmt.subcategories
                                            JOIN task_mgmt.categories
                                                ON categories.id = subcategories.category
                                            JOIN task_mgmt.projects
                                                ON projects.id = categories.project_id
                                            WHERE
                                                subcategories.id = $${params.push(options.subcategory_id)}
                                        ),
                                        workspace_id = (
                                            SELECT projects.team
                                            FROM task_mgmt.subcategories
                                            JOIN task_mgmt.categories
                                                ON categories.id = subcategories.category
                                            JOIN task_mgmt.projects
                                                ON projects.id = categories.project_id
                                            WHERE
                                                subcategories.id = $${params.push(options.subcategory_id)}
                                        ),
                                        `;
                                        }
                                    } else {
                                        team_id_query = `
                                    team_id = (
                                            SELECT projects.team
                                            FROM task_mgmt.items
                                            JOIN task_mgmt.subcategories
                                                ON items.subcategory = subcategories.id
                                            JOIN task_mgmt.categories
                                                ON categories.id = subcategories.category
                                            JOIN task_mgmt.projects
                                                ON projects.id = categories.project_id
                                            WHERE
                                                items.id = $${params.push(task_id)}
                                        ),
                                        workspace_id = (
                                            SELECT projects.team
                                            FROM task_mgmt.items
                                            JOIN task_mgmt.subcategories
                                                ON items.subcategory = subcategories.id
                                            JOIN task_mgmt.categories
                                                ON categories.id = subcategories.category
                                            JOIN task_mgmt.projects
                                                ON projects.id = categories.project_id
                                            WHERE
                                                items.id = $${params.push(task_id)}
                                        ),
                                    `;
                                        if (
                                            options.template_visibility != null &&
                                            Object.values(
                                                config.get<Record<string, number>>('template_permissions')
                                            ).includes(options.template_visibility)
                                        ) {
                                            team_id_query += `
                                    permissions = $${params.push(options.template_visibility)},
                                        `;
                                        }
                                        if (options.public_sharing !== undefined) {
                                            team_id_query += `
                                        public_sharing = $${params.push(options.public_sharing)},
                                        `;
                                        }
                                    }
                                } else if (subcategory_team) {
                                    team_id_query = `
                                    workspace_id = $${params.push(subcategory_team)}, `;
                                } else {
                                    team_id_query = `
                                    workspace_id = (
                                        SELECT coalesce(items.team_id, subcategories.team_id, categories.team_id, projects.team) AS team_id
                                        FROM task_mgmt.items
                                        LEFT JOIN task_mgmt.subcategories
                                            ON items.subcategory = subcategories.id
                                        LEFT JOIN task_mgmt.categories
                                            ON categories.id = subcategories.category
                                        LEFT JOIN task_mgmt.projects
                                            ON projects.id = categories.project_id
                                        WHERE
                                            items.id = $${params.push(task_id)}
                                        LIMIT 1
                                    ),
                                `;
                                }

                                const status_id_query = `
                                SELECT statuses.id
                                FROM task_mgmt.subcategories, task_mgmt.statuses
                                WHERE subcategories.id = $${params.push(
                                    options.subcategory_id || task_to_copy.subcategory
                                )}
                                AND    subcategories.status_group = statuses.status_group
                                AND    statuses.status = $8::text
                                LIMIT 1
                            `;

                                const query = `
                                UPDATE task_mgmt.tasks SET
                                    NAME = $1,
                                    content = $2,
                                    text_content = $3,
                                    date_created = $4,
                                    date_updated = $5,
                                    type = $6,
                                    creator = $7,
                                    status = $8::text,
                                    time_spent = $9,
                                    subcategory = $10,
                                    orderindex = (${order_indexes.orderindex}),
                                    priority = $11,
                                    parent = $12,
                                    boardindex = $13,
                                    coverimage = $14,
                                    deleted = $50,
                                    import_id = $15,
                                    import_uuid = $16,
                                    importing = $17,
                                    due_date = $18,
                                    due_date_time = $19,
                                    sent_due_date_notif = $20,
                                    points = $21,
                                    time_estimate = $22,
                                    template = $23,
                                    start_date = $24,
                                    start_date_time = $25,
                                    template_name = $26,
                                    html_content = $27,
                                    recurring = $28,
                                    recur_type = $29,
                                    recur_next = $30,
                                    recur_new_status = $31,
                                    recur_due_date = $32,
                                    recur_data = $33,
                                    recur_rule = $34,
                                    recur_task = $35,
                                    recur_time = $36,
                                    recur_on = $37,
                                    recur_on_status = $38,
                                    recur_skip_missed = $39,
                                    recur_copy_original = $40,
                                    recur_immediately = $41,
                                    encrypted = $42,
                                    time_estimate_string = $43,
                                    recur_dst = $44,
                                    recur_tz = $45,
                                    recur_tz_offset = $46,
                                    recur_daily = $47,
                                    private = $48,
                                    owner = $7,
                                    assignee_orderindex = (${order_indexes.assignee_orderindex}),
                                    tag_orderindex = (${order_indexes.tag_orderindex}),
                                    priority_orderindex = (${order_indexes.priority_orderindex}),
                                    due_date_orderindex = (${order_indexes.due_date_orderindex}),
                                    subtask_orderindex = (${order_indexes.subtask_orderindex}),
                                    none_orderindex = (${order_indexes.none_orderindex}),
                                    status_id = (${status_id_query}),
                                    field_orderindex = (${order_indexes.field_orderindex}),
                                    date_deleted = $49,
                                    archived = $51,
                                    default_category_id = $52,
                                    recur_settings = $54,
                                    ${
                                        options.custom_type && customType
                                            ? `custom_type = $${params.push(customType.value)},`
                                            : ''
                                    }
                                    ${
                                        options.old_date_closed !== false
                                            ? `date_closed = $${params.push(task_to_copy.date_closed)},`
                                            : ''
                                    }
                                    ${
                                        options.old_date_done !== false
                                            ? `date_done = $${params.push(task_to_copy.date_done)},`
                                            : ''
                                    }              
                                    ${team_id_query}
                                    form_id = $55,
                                    permanent_template_id = $56,
                                    via = $57,
                                    subtask_parent = $58,
                                    duration = $59,
                                    duration_is_elapsed = $60,
                                    original_subcat = $${params.push(task_to_copy.subcategory)}
                                WHERE int_id = $53
                                RETURNING workspace_id`;

                                const taskVersionUpdates = [];

                                if (options.set_deleted) {
                                    const relationships = [];
                                    if (
                                        subtask_parent ||
                                        parent ||
                                        options.subcategory_id ||
                                        task_to_copy.subcategory
                                    ) {
                                        relationships.push({
                                            type: ObjectRelationshipType.TASK_PARENT,
                                            object_type: parent ? ObjectType.TASK : ObjectType.LIST,
                                            object_id:
                                                subtask_parent ||
                                                parent ||
                                                options.subcategory_id ||
                                                task_to_copy.subcategory,
                                            workspace_id: task_to_copy.workspace_id,
                                        });
                                    }

                                    taskVersionUpdates.push(
                                        {
                                            object_type: ObjectType.TASK,
                                            object_id: new_id,
                                            workspace_id: task_to_copy.workspace_id,
                                            operation: OperationType.DELETE,
                                            data: {
                                                relationships,
                                                context: { ws_key: options.ws_key },
                                            },
                                        },
                                        {
                                            object_type: ObjectType.TASK_ACCESS,
                                            object_id: new_id,
                                            workspace_id: task_to_copy.workspace_id,
                                            operation: OperationType.DELETE,
                                        }
                                    );
                                }

                                copyContextExtraData.taskToCopy = { ...task_to_copy };
                                copyContextExtraData.updateTasksTableParams = [...params];
                                copyContextExtraData.updateTasksTableStatusIdSubquery = status_id_query;
                                let queryResult;
                                try {
                                    queryResult = await writeAsync(query, params, taskVersionUpdates);
                                } catch (insertErr) {
                                    series_cb(insertErr as Error);
                                    return;
                                }

                                new_workspace_id = (queryResult.rows[0] as any)?.workspace_id;
                                CopyOperation.reportObjectCreated(
                                    CopyableObjectKey.from(ObjectType.TASK, original_team, task_id),
                                    CopyableObjectKey.from(ObjectType.TASK, new_workspace_id, new_id)
                                );
                                options[TEMPLATE_TRACER_KEY]?.processSourceAndTargetVerifiedData({
                                    sourceId: task_to_copy.id,
                                    savedId: new_id,
                                    workspaceId: new_workspace_id,
                                });
                                old_to_new[task_id] = new_id;

                                if (use_graceful_shutdown) {
                                    graceful_shutdown_process_id = `${new_id}-copyTask`;
                                    logger.debug(
                                        `processShutdownController job started for job id=${graceful_shutdown_process_id}`
                                    );
                                    processShutdownController.jobStarted(graceful_shutdown_process_id, {
                                        jobTimeoutMs: TEMPLATES_PROCESS_SHUTDOWN_TIMEOUT_MS,
                                        jobTimedOutCb: () => {
                                            logger.warn(
                                                `processShutdownController timed out for process: ${graceful_shutdown_process_id}`
                                            );
                                        },
                                    });
                                }

                                if (options.return_immediately && !returnImmediatelyEarlier) {
                                    cb(null, { id: new_id, permanent_template_id });
                                }
                                series_cb();
                            },
                        },

                        {
                            getSubtaskCountCreateHistoryItem: async series_cb => {
                                if (saving_template) {
                                    series_cb();
                                    return;
                                }

                                // This logic is necessary to support a isolate use case of the automation
                                // infinite loop check. This is planned to be short lived and the task to
                                // fully update the loop check is https://staging.clickup.com/t/negx3y32
                                if (options.callOrigin === 'automations' || options.from_template) {
                                    if (task_to_copy.parent) {
                                        try {
                                            ({ subtaskCount: subtaskCountNotArchived, subtaskCountArchived } =
                                                await getSubtaskCountForTaskAsync(task_to_copy.parent, {
                                                    proxyClient,
                                                    isSubtask: false,
                                                }));
                                            subtask_count = subtaskCountNotArchived + subtaskCountArchived;
                                        } catch (subtaskErr) {
                                            series_cb(
                                                new TaskError(
                                                    err,
                                                    'Unable to retrieve subtask count for Automation loop'
                                                )
                                            );
                                            return;
                                        }
                                    }
                                }

                                const hist_data: {
                                    from_recur?: boolean;
                                    subtaskCount?: number;
                                    task_copied?: string;
                                    from_template?: boolean;
                                    task_that_recurred?: string;
                                    due_date?: {
                                        due_date: number;
                                        due_date_time: boolean;
                                    };
                                    trace_id?: string;
                                    subcategory_id?: string;
                                    trigger_id?: string;
                                    origin_task_for_recur?: string;
                                } = {
                                    from_recur: options.from_recur || false,
                                    subtaskCount: subtask_count,
                                };

                                hist_data.trigger_id = options.trigger_id ?? undefined;

                                if (!options.skip_from_copy_hist) {
                                    hist_data.task_copied = task_id;
                                    hist_data.from_template = options.from_template;
                                }

                                let recur_status = 'Open';
                                if (options.from_recur) {
                                    hist_data.task_that_recurred = options.task_that_recurred;
                                    // Temporary set the recur task status to default_open_status
                                    // Will update this after task "Recur Task Improvement"
                                    recur_status = status;
                                    hist_data.origin_task_for_recur = options.origin_task_for_recur ?? undefined;
                                }

                                // Set new task status based on Duplicate Task
                                if (options.from_dupe_task) {
                                    recur_status = status;
                                }

                                if (options.from_recur && new_due_date) {
                                    hist_data.due_date = {
                                        due_date: new_due_date,
                                        due_date_time: task_to_copy.due_date_time || false,
                                    };
                                }

                                const trace_id = tracer.getTraceID();
                                if (trace_id) {
                                    hist_data.trace_id = trace_id;
                                }

                                if (destination_subcategory_id) {
                                    hist_data.subcategory_id = destination_subcategory_id;
                                }

                                const query = `
                                INSERT INTO task_mgmt.task_history(task_id, field, date, userid, after, data)
                                VALUES($1, $2, $3, $4, null, $6), ($1, 'status', $3, $4, $5, null)
                                RETURNING id`;
                                const params = [
                                    new_id,
                                    'task_creation',
                                    new Date().getTime(),
                                    userid,
                                    recur_status,
                                    hist_data,
                                ];

                                db.writeQuery(query, params, (histErr: Error | null, result: any) => {
                                    if (histErr) {
                                        logger.error({
                                            msg: 'Failed to create task history',
                                            status: 500,
                                            err: histErr,
                                            ECODE: 'ITEM_040',
                                        });
                                        series_cb({
                                            err: 'Internal server error',
                                            status: 500,
                                            ECODE: 'ITEM_040',
                                        } as unknown as Error);
                                    } else {
                                        createHistId = result.rows[0].id;
                                        series_cb();
                                    }
                                });
                            },
                        },

                        {
                            createStartDateHistoryItem: series_cb => {
                                if (!in_params.start_date || in_params.via !== 'form') {
                                    series_cb();
                                    return;
                                }

                                const hist_data = {
                                    via: in_params.via,
                                    start_date_time: in_params.start_date_time || false,
                                };

                                const query = `
                                INSERT INTO task_mgmt.task_history(task_id, field, date, userid, after, data)
                                VALUES($1, $2, $3, $4, $5, $6)`;
                                const params = [
                                    new_id,
                                    'start_date',
                                    new Date().getTime(),
                                    userid,
                                    in_params.start_date,
                                    hist_data,
                                ];

                                db.writeQuery(query, params, series_cb);
                            },
                        },

                        {
                            createDueDateHistoryItem: series_cb => {
                                if (!in_params.due_date || in_params.via !== 'form') {
                                    series_cb();
                                    return;
                                }

                                const hist_data = {
                                    via: in_params.via,
                                    due_date_time: in_params.due_date_time || false,
                                };

                                const query = `
                                INSERT INTO task_mgmt.task_history(task_id, field, date, userid, after, data)
                                VALUES($1, $2, $3, $4, $5, $6)`;
                                const params = [
                                    new_id,
                                    'due_date',
                                    new Date().getTime(),
                                    userid,
                                    in_params.due_date,
                                    hist_data,
                                ];

                                db.writeQuery(query, params, series_cb);
                            },
                        },

                        {
                            createBugSnagLink: series_cb => {
                                if (!options.bugsnag_link) {
                                    series_cb();
                                    return;
                                }

                                bugsnagAutomation.createBugsnagCULink(
                                    options.bugsnag_link,
                                    options.bugsnag_link_url,
                                    new_id,
                                    task_to_copy.workspace_id,
                                    {},
                                    series_cb
                                );
                            },
                        },

                        {
                            updateTeamMaxIndexes: series_cb => {
                                db.writeQuery(
                                    `
                                UPDATE tasK_mgmt.team_max_indexes
                                SET
                                    orderindex = GREATEST(orderindex, $1),
                                    assignee_orderindex = GREATEST(assignee_orderindex, $2),
                                    due_date_orderindex = GREATEST(due_date_orderindex, $3),
                                    priority_orderindex = GREATEST(priority_orderindex, $4),
                                    tag_orderindex = GREATEST(tag_orderindex, $5),
                                    none_orderindex = GREATEST(none_orderindex, $7),
                                    field_orderindex = GREATEST(field_orderindex, $8)
                                WHERE team_id = (
                                    SELECT projects.team
                                    FROM task_mgmt.projects, task_mgmt.categories, task_mgmt.subcategories
                                    WHERE subcategories.id = $6
                                        AND subcategories.category = categories.id
                                        AND categories.project_id = projects.id
                                )`,
                                    [
                                        order_indexes.orderindex,
                                        order_indexes.assignee_orderindex,
                                        order_indexes.due_date_orderindex,
                                        order_indexes.priority_orderindex,
                                        order_indexes.tag_orderindex,
                                        options.subcategory_id || task_to_copy.subcategory,
                                        order_indexes.none_orderindex,
                                        order_indexes.field_orderindex,
                                    ],
                                    (max_index_err: Error) => {
                                        if (max_index_err) {
                                            logger.error({ msg: 'Failed to set orderindex maxes', max_index_err });
                                        }
                                        series_cb();
                                    }
                                );
                            },
                        },

                        {
                            copyTemplateFields: series_cb => {
                                if (!task_to_copy.team_id || !options.subcategory_id) {
                                    series_cb();
                                    return;
                                }

                                const _options = {
                                    custom_fields: options.custom_fields,
                                    task_relationships: options.task_relationships,
                                    proxyClient,
                                };

                                fieldHelper.copyTaskTemplateFields(
                                    userid,
                                    task_id,
                                    options.subcategory_id,
                                    _options,
                                    (err1: Error | null, result: any) => {
                                        if (err1) {
                                            series_cb(err1);
                                            return;
                                        }

                                        if (!result) {
                                            series_cb();
                                            return;
                                        }

                                        only_fields = result.field_ids;

                                        Object.assign(field_map, result.field_map || {});
                                        Object.assign(dd_option_map, result.dd_option_map || {});
                                        Object.assign(ll_option_map, result.ll_option_map || {});

                                        series_cb();
                                    }
                                );
                            },
                        },

                        // OVM: None
                        {
                            copyPerAssigneePoints: series_cb => {
                                if (
                                    (!points_per_assignee && !estimates_per_assignee) ||
                                    !valid_assignees ||
                                    !valid_assignees.length
                                ) {
                                    series_cb();
                                    return;
                                }

                                const query = `
                                SELECT
                                    userid,
                                    points_float,
                                    time_estimate
                                FROM
                                    task_mgmt.assignees
                                WHERE
                                    task_id = $1 AND userid = ANY($2)
                            `;

                                db.replicaQuery(
                                    query,
                                    [task_id, valid_assignees],
                                    (err_: Error | null, result: any) => {
                                        if (err_) {
                                            series_cb(err_);
                                            return;
                                        }

                                        if (!result || !result.rows || !result.rows.length) {
                                            series_cb();
                                            return;
                                        }

                                        result.rows.forEach((row: any) => {
                                            per_assignee_map[row.userid] = {
                                                points: row.points_float || null,
                                                time_estimate: row.time_estimate || null,
                                            };
                                        });

                                        series_cb();
                                    }
                                );
                            },
                        },

                        // 4 set up queries
                        {
                            parallelSteps: series_cb => {
                                async.parallel(
                                    timedSteps(METRIC_NAME, {
                                        async middleware(para_cb) {
                                            if (!options.middleware_queries) {
                                                para_cb();
                                                return;
                                            }

                                            const mqVersionUpdates = options.middleware_queries.reduce(
                                                (acc: any[], mq: any) =>
                                                    mq.versionUpdates ? acc.concat(mq.versionUpdates) : acc,
                                                []
                                            );

                                            try {
                                                await batchQueriesSeriesAsyncThenUpdateTasks(
                                                    options.middleware_queries,
                                                    task_to_copy.workspace_id,
                                                    false,
                                                    mqVersionUpdates,
                                                    { context: { ws_key: options.ws_key } }
                                                );
                                                para_cb();
                                            } catch (mqErr) {
                                                para_cb(mqErr as Error);
                                            }
                                        },

                                        // OVM: UPDATE - Task
                                        fixRecurCopyTask(para_cb) {
                                            if (!options.set_recur_copy_self) {
                                                para_cb();
                                                return;
                                            }

                                            db.writeQuery(
                                                'UPDATE task_mgmt.items SET recur_task = $1 WHERE int_id = $2',
                                                [new_id, int_id],
                                                para_cb
                                            );
                                        },

                                        // OVM: None
                                        templateMembers(para_cb) {
                                            if (
                                                !options.template_v2 ||
                                                options.template_visibility !==
                                                    config.get<number>('template_permissions.members') ||
                                                !options.template_members ||
                                                !options.template_members.length
                                            ) {
                                                para_cb();
                                                return;
                                            }
                                            const params: any[] = [];
                                            let query = `
                                            INSERT INTO task_mgmt.task_template_members
                                                (task_id, userid, workspace_id)
                                            VALUES
                                        `;
                                            options.template_members.forEach(member => {
                                                query += `(
                                                    $${params.push(new_id)},
                                                    $${params.push(member)},
                                                    $${params.push(task_to_copy.workspace_id)}
                                            ), `;
                                            });
                                            query = query.slice(0, -2);
                                            db.writeQuery(query, params, para_cb);
                                        },

                                        // OVM: None
                                        async templateGroupMembers(para_cb) {
                                            if (
                                                !options.template_v2 ||
                                                options.template_visibility !==
                                                    config.get<number>('template_permissions.members') ||
                                                !options.template_group_members ||
                                                !options.template_group_members.length
                                            ) {
                                                para_cb();
                                                return;
                                            }
                                            try {
                                                await Promise.all([
                                                    groupMod.checkGroupsHasAccessToTeam(
                                                        options.template_group_members,
                                                        'task',
                                                        task_id,
                                                        {}
                                                    ),
                                                    groupSharingPaywall.checkGroupSharingPaywall(task_id, 'task'),
                                                ]);

                                                const params = [new_id, task_to_copy.workspace_id];
                                                const values: any[] = [];
                                                let query = `
                                                INSERT INTO task_mgmt.task_template_group_members (task_id, group_id, workspace_id)
                                                VALUES
                                            `;

                                                options.template_group_members.forEach(member => {
                                                    params.push(member);
                                                    values.push(`($1, $${params.length}, $2)`);
                                                });
                                                query += `${values.join(',')}`;

                                                await db.promiseWriteQuery(query, params);
                                                para_cb();
                                            } catch (e) {
                                                para_cb(e as Error);
                                            }
                                        },

                                        assignees(para_cb) {
                                            if (valid_assignees && valid_assignees.length > 0) {
                                                let time_estimate_field = ``;
                                                let points_estimate_field = ``;
                                                if (points_per_assignee) {
                                                    points_estimate_field = ', points_float';
                                                }

                                                if (estimates_per_assignee) {
                                                    time_estimate_field = ', time_estimate';
                                                }

                                                let query = `
                                                INSERT INTO task_mgmt.assignees(
                                                    task_id, workspace_id, userid, date_assigned ${points_estimate_field}
                                                    ${time_estimate_field}
                                                ) VALUES `;
                                                const params = [new_id, task_to_copy.workspace_id];

                                                valid_assignees.forEach((assignee, idx) => {
                                                    if (idx > 0) {
                                                        query += ', ';
                                                    }

                                                    params.push(assignee);
                                                    query += `($1, $2, $${params.length}, $${params.length + 1}`;

                                                    if (points_per_assignee) {
                                                        const points =
                                                            per_assignee_map[assignee] &&
                                                            per_assignee_map[assignee].points
                                                                ? per_assignee_map[assignee].points
                                                                : null;
                                                        query += `, ${points}`;
                                                    }

                                                    if (estimates_per_assignee) {
                                                        const time_estimate_ =
                                                            per_assignee_map[assignee] &&
                                                            per_assignee_map[assignee].time_estimate
                                                                ? per_assignee_map[assignee].time_estimate
                                                                : null;
                                                        query += `, ${time_estimate_}`;
                                                    }

                                                    query += `)`;

                                                    params.push(new Date().getTime());
                                                });

                                                db.writeQuery(query, params, para_cb);
                                            } else {
                                                para_cb();
                                            }
                                        },

                                        // OVM: None
                                        async group_assignee(para_cb) {
                                            if (!(valid_group_assignees && valid_group_assignees.length)) {
                                                para_cb();
                                                return;
                                            }
                                            try {
                                                await assigneeMod._saveGroupAssignees(
                                                    new_id,
                                                    valid_group_assignees,
                                                    task_to_copy.workspace_id
                                                );
                                                para_cb();
                                            } catch (err1) {
                                                para_cb(err1 as Error);
                                            }
                                        },

                                        // OVM: None
                                        async assignee_history(para_cb) {
                                            if (
                                                !(
                                                    (options.task_activity && options.from_dupe_task) ||
                                                    options.old_assignees ||
                                                    (options.assignees && options.assignees.length) ||
                                                    (options.group_assignees && options.group_assignees.length)
                                                ) ||
                                                saving_template
                                            ) {
                                                para_cb();
                                                return;
                                            }

                                            const has_valid_assignees = valid_assignees && valid_assignees.length;
                                            const has_valid_group_assignees =
                                                valid_group_assignees && valid_group_assignees.length;
                                            if (
                                                options.template_v2 ||
                                                options.template_v2_child ||
                                                !(has_valid_assignees || has_valid_group_assignees)
                                            ) {
                                                para_cb();
                                                return;
                                            }

                                            try {
                                                const { query, params } = await getAssigneeHistoryInsertQuery(
                                                    userid,
                                                    task_to_copy,
                                                    new_id,
                                                    valid_assignees,
                                                    valid_group_assignees,
                                                    subtask_count,
                                                    now,
                                                    options
                                                );
                                                if (!query) {
                                                    para_cb();
                                                    return;
                                                }

                                                db.writeQuery(
                                                    query,
                                                    params,
                                                    (assigneeErr: Error | null, result2: any) => {
                                                        if (assigneeErr) {
                                                            para_cb(
                                                                new CopyError(assigneeErr, 'ITEM_110', 500, {
                                                                    msg: 'Failed to create assignee history items',
                                                                })
                                                            );
                                                        } else {
                                                            if (result2.rows) {
                                                                assignee_hist_items = result2.rows;
                                                            }
                                                            para_cb();
                                                        }
                                                    }
                                                );
                                            } catch (assigneeHistoryErr: any) {
                                                logger.error({
                                                    err: assigneeHistoryErr,
                                                    msg: 'Failed to get assignee history',
                                                    ECODE: 'ITEM_109',
                                                });
                                                para_cb();
                                            }
                                        },

                                        // OVM: Update
                                        async group_followers(para_cb) {
                                            if (!(valid_group_followers && valid_group_followers.length)) {
                                                para_cb();
                                                return;
                                            }
                                            try {
                                                await assigneeMod._saveGroupFollowers(
                                                    new_id,
                                                    valid_group_followers,
                                                    task_to_copy.workspace_id
                                                );
                                                para_cb();
                                            } catch (err1) {
                                                para_cb(err1 as Error);
                                            }
                                        },

                                        // OVM: None
                                        time_estimate_history(para_cb) {
                                            if (saving_template || !time_estimate) {
                                                para_cb();
                                                return;
                                            }

                                            if (!options.task_activity && options.from_dupe_task) {
                                                para_cb();
                                                return;
                                            }

                                            let date = now;
                                            proxyClient.replicaQuery(
                                                {
                                                    queryFamily: QueryFamily.Item,
                                                    queryKey: ItemQueryKey.GetFieldFromHistory,
                                                    queryParams: ['time_estimate', task_to_copy.id, time_estimate],
                                                },
                                                (_err, result) => {
                                                    let actedUserId = userid;
                                                    if (result && result.rows && result.rows.length) {
                                                        [{ userid: actedUserId, date }] = result.rows;
                                                    }
                                                    const query = `INSERT INTO task_mgmt.task_history(task_id, field, date, userid, data, after) VALUES ($1, $2, $3, $4, $5, $6)`;
                                                    const params = [
                                                        new_id,
                                                        'time_estimate',
                                                        date,
                                                        actedUserId,
                                                        { time_estimate_string, from_recur: options.from_recur },
                                                        time_estimate,
                                                    ];

                                                    db.writeQuery(query, params, para_cb);
                                                }
                                            );
                                        },

                                        // OVM: None
                                        priority_history(para_cb) {
                                            if (options.create_template || saving_template) {
                                                para_cb();
                                                return;
                                            }

                                            if (!options.task_activity && options.from_dupe_task) {
                                                para_cb();
                                                return;
                                            }

                                            const priority =
                                                options.priority === true || options.priority == null
                                                    ? task_to_copy.priority
                                                    : null;

                                            if (!priority) {
                                                para_cb();
                                                return;
                                            }

                                            let date = now;
                                            proxyClient.replicaQuery(
                                                {
                                                    queryFamily: QueryFamily.Item,
                                                    queryKey: ItemQueryKey.GetFieldFromHistory,
                                                    queryParams: ['priority', task_to_copy.id, priority],
                                                },
                                                (_err, result) => {
                                                    let actedUserId = userid;
                                                    if (result && result.rows && result.rows.length) {
                                                        [{ userid: actedUserId, date }] = result.rows;
                                                    }
                                                    const query = `INSERT INTO task_mgmt.task_history(task_id, field, date, userid, data, after) VALUES ($1, $2, $3, $4, $5, $6)`;
                                                    const params = [
                                                        new_id,
                                                        'priority',
                                                        date,
                                                        actedUserId,
                                                        { from_recur: options.from_recur },
                                                        priority,
                                                    ];
                                                    db.writeQuery(query, params, para_cb);
                                                }
                                            );
                                        },

                                        // OVM: None
                                        followers(para_cb) {
                                            if (
                                                !(valid_followers && valid_followers.length) &&
                                                in_params.via !== 'form'
                                            ) {
                                                para_cb();
                                                return;
                                            }

                                            if (in_params.via === 'form') {
                                                if (!valid_followers.includes(userid)) {
                                                    valid_followers.push(userid);
                                                }

                                                if (valid_assignees) {
                                                    valid_assignees.forEach(assignee => {
                                                        if (!valid_followers.includes(assignee)) {
                                                            valid_followers.push(assignee);
                                                        }
                                                    });
                                                }
                                            }

                                            let query =
                                                'INSERT INTO task_mgmt.followers(task_id, userid, workspace_id) VALUES ';
                                            const params = [new_id, task_to_copy.workspace_id];

                                            query += valid_followers
                                                .map(follower => `($1, $${params.push(follower)}, $2)`)
                                                .join(', ');

                                            query += ` ON CONFLICT (task_id, userid) DO NOTHING`;

                                            db.writeQuery(query, params, para_cb);
                                        },

                                        async recurring(para_cb) {
                                            if (!options.recur_settings) {
                                                para_cb();
                                                return;
                                            }

                                            try {
                                                await recurrence2.copyRecurSettings(userid, task_id, new_id);
                                                para_cb();
                                            } catch (e) {
                                                para_cb(e as Error);
                                            }
                                        },

                                        // OVM: None
                                        task_activity(para_cb) {
                                            if (
                                                (!options.task_activity && options.from_dupe_task) ||
                                                options.from_template ||
                                                saving_template ||
                                                (options.from_recur && !options.task_activity)
                                            ) {
                                                para_cb();
                                                return;
                                            }

                                            // these history fields are already copied over with previous implementation
                                            // Don't copy status history. It need to be created new.
                                            const not_these_fields = [
                                                'assignee',
                                                'group_assignee',
                                                'priority',
                                                'comment',
                                                'time_estimate',
                                                'status',
                                                'task_creation',
                                                'content',
                                                'tag',
                                                'chat_thread_link', // Should never be copied
                                            ];

                                            if (!options.attachments) {
                                                not_these_fields.push('attachments');
                                            }

                                            if (!options.custom_type) {
                                                // Do not record a history item if the custom_type isn't being copied over.
                                                not_these_fields.push('custom_type');
                                            }

                                            proxyClient.replicaQuery(
                                                {
                                                    queryFamily: QueryFamily.Item,
                                                    queryKey: ItemQueryKey.GetTaskHistory,
                                                    queryParams: [task_id, not_these_fields],
                                                },
                                                (proxyErr, result) => {
                                                    if (proxyErr) {
                                                        para_cb(proxyErr);
                                                        return;
                                                    }

                                                    async.each(
                                                        result.rows,
                                                        (row: any, each_cb) => {
                                                            db.writeQuery(
                                                                `INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, deleted, data, via) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
                                                                [
                                                                    new_id,
                                                                    row.field,
                                                                    row.date,
                                                                    row.before,
                                                                    row.after,
                                                                    row.userid,
                                                                    row.deleted,
                                                                    {
                                                                        ...row.data,
                                                                        trigger_id: options.trigger_id ?? undefined,
                                                                    },
                                                                    row.via,
                                                                ],
                                                                each_cb
                                                            );
                                                        },
                                                        para_cb
                                                    );
                                                }
                                            );
                                        },

                                        // OVM: None
                                        async add_task_status_history(para_cb) {
                                            // Insert new Status history for new task when using Template.
                                            // Exclude Recur Task.
                                            if (!options.from_v2_template_child || options.from_recur) {
                                                para_cb();
                                                return;
                                            }
                                            const query = `
                                        INSERT INTO task_mgmt.task_history(task_id, field, date, userid, after, via, data)
                                        VALUES($1, 'status', $2, $3, $4, $5, $6)
                                        RETURNING id`;

                                            const status_hist_data = { status_id: task_to_copy.status_id };

                                            let statusActor = userid;

                                            if (options.old_status && options.task_activity && options.from_dupe_task) {
                                                try {
                                                    const {
                                                        rows: [row],
                                                    } = await readAsync<{ userid: string }>(
                                                        `SELECT * FROM task_mgmt.task_history WHERE task_id = $1 AND field = $2 ORDER BY task_history.date DESC, task_history.id DESC LIMIT 1`,
                                                        [task_id, 'status']
                                                    );
                                                    statusActor = row.userid;
                                                } catch (historyRetrievalErr: any) {
                                                    // swallow error, we will fallback to the actor anyway
                                                    logger.warn({
                                                        msg: 'Failed to get status history user',
                                                        err: historyRetrievalErr,
                                                        task_id,
                                                        ECODE: 'ITEM_329',
                                                    });
                                                }
                                            }

                                            const params = [
                                                new_id,
                                                Date.now(),
                                                statusActor,
                                                task_to_copy.status,
                                                task_to_copy.via,
                                                status_hist_data,
                                            ];

                                            db.writeQuery(query, params, para_cb);
                                        },

                                        // OVM: Task - contains a recursive call to _copyTask function
                                        subtasks: {
                                            tags: { subtask_count: bucketize(subtask_count) },
                                            fn: async para_cb => {
                                                if (!options.subtasks) {
                                                    para_cb();
                                                    return;
                                                }

                                                let params = [task_id];

                                                // main parent task will nullify parent (when subcates dont match)
                                                // but it will be saved on original parent
                                                const task_to_copy_parent =
                                                    task_to_copy.parent || task_to_copy.original_parent;

                                                if (
                                                    task_to_copy_parent &&
                                                    options.top_level_parent !== task_to_copy_parent
                                                ) {
                                                    // the subtask has a different parent than its parent, which shouldnt be possible?
                                                    logger.error({
                                                        msg: 'Found subtask with different parent than its parent',
                                                        task_id,
                                                        task_to_copy_parent: task_to_copy.parent,
                                                        task_to_copy_original_parent: task_to_copy.original_parent,
                                                        top_level_parent: options.top_level_parent,
                                                    });
                                                    para_cb();
                                                    return;
                                                }

                                                if (task_to_copy_parent && has_nested) {
                                                    params = [task_to_copy_parent, task_id];
                                                }

                                                let subTasks: any[];
                                                try {
                                                    ({ rows: subTasks } = await proxyClient.replicaQueryAsync({
                                                        queryFamily: QueryFamily.Item,
                                                        queryKey: ItemQueryKey.GetSubtask,
                                                        queryParams: params,
                                                        options: {
                                                            task_to_copy_parent,
                                                            has_nested,
                                                            archived: options.archived,
                                                            saving_template,
                                                        },
                                                    }));
                                                } catch (getErr) {
                                                    para_cb(getErr as Error);
                                                    return;
                                                }

                                                // Use orderindex stats passed from above (they include subtasks)
                                                // or, if it's a top level task, get them just for its subtasks (all levels)
                                                let orderindexStatsForSubtasks = orderindexStats;
                                                if (
                                                    !orderindexStatsForSubtasks &&
                                                    (!task_to_copy_parent ||
                                                        improvedSubtasksOrderFetchWhenCopyingTask())
                                                ) {
                                                    logger.info({
                                                        msg: 'Fetching order index stats for subtasks',
                                                        task_to_copy_parent,
                                                        task_to_copy: task_id,
                                                    });
                                                    const rootTaskId = improvedSubtasksOrderFetchWhenCopyingTask()
                                                        ? task_to_copy_parent || task_id
                                                        : task_id;

                                                    try {
                                                        orderindexStatsForSubtasks =
                                                            await taskOrderindexCopy.getOrderindexStats(
                                                                rootTaskId,
                                                                config.get('views.parent_types.task'),
                                                                options.from_template || options.from_parent_template,
                                                                {
                                                                    proxyClient,
                                                                    userid: +userid,
                                                                    usecase: '', // Not used ??
                                                                    fetchSubtasksForTaskType: true,
                                                                }
                                                            );
                                                    } catch (orderindexStatsErr) {
                                                        para_cb(orderindexStatsErr as Error);
                                                        return;
                                                    }
                                                }

                                                let inner_start_diff = 0;
                                                let inner_due_diff = 0;

                                                const asyncFunc = determineAsyncFunction(
                                                    Number(userid),
                                                    'subtaskCopyLimit',
                                                    options.pressure?.[OperationPrefix.COPY_TASK]
                                                );

                                                asyncFunc(
                                                    subTasks,
                                                    (row: any, each_cb: any) => {
                                                        let parent;
                                                        if (
                                                            row.subtask_parent &&
                                                            destination_subcategory_id === task_to_copy.subcategory
                                                        ) {
                                                            parent = options.parent || row.parent;
                                                        } else {
                                                            parent = options.parent || new_id; // when copying to same subcat, copy within hierarchy
                                                        }

                                                        let subtask_parent;
                                                        if (
                                                            nested_subtasks &&
                                                            (options.parent ||
                                                                (options.from_template && row.subtask_parent) ||
                                                                (destination_subcategory_id ===
                                                                    task_to_copy.subcategory &&
                                                                    row.subtask_parent))
                                                        ) {
                                                            subtask_parent = new_id;
                                                        } else {
                                                            subtask_parent = -1;
                                                        }

                                                        const _options = prepareOptionsForSubTask(
                                                            options,
                                                            row,
                                                            task_to_copy,
                                                            parent,
                                                            subtask_parent,
                                                            dd_option_map,
                                                            ll_option_map,
                                                            field_map,
                                                            nested_subtasks,
                                                            orderindexStatsForSubtasks,
                                                            saving_template,
                                                            subtasks_name_map,
                                                            sync_blocks_old_to_new,
                                                            inner_due_diff,
                                                            inner_start_diff,
                                                            proxyClient,
                                                            isUsingRemapDateOfTaskV2
                                                        );

                                                        _copyTask(
                                                            userid,
                                                            row.id,
                                                            _options,
                                                            (copyErr: Error | null, copyResult: any) => {
                                                                if (copyErr) {
                                                                    each_cb(copyErr);
                                                                } else {
                                                                    old_to_new = {
                                                                        ...old_to_new,
                                                                        ...copyResult.old_to_new,
                                                                    };
                                                                    Object.keys(copyResult.old_dependencies).forEach(
                                                                        key => {
                                                                            if (!old_dependencies[key]) {
                                                                                old_dependencies[key] =
                                                                                    copyResult.old_dependencies[key];
                                                                            } else {
                                                                                old_dependencies[key] =
                                                                                    copyResult.old_dependencies[
                                                                                        key
                                                                                    ].concat(old_dependencies[key]);
                                                                            }
                                                                        }
                                                                    );
                                                                    Object.keys(copyResult.old_links).forEach(key => {
                                                                        old_links[key] = [
                                                                            ...(old_links[key] || []),
                                                                            ...copyResult.old_links[key],
                                                                        ];
                                                                    });
                                                                    inner_start_diff =
                                                                        copyResult.subtask_start_date_shift || 0;
                                                                    inner_due_diff =
                                                                        copyResult.subtask_due_date_shift || 0;
                                                                    subtask_notif_functions.push(
                                                                        copyResult.notif_function
                                                                    );
                                                                    subtask_ids.push(
                                                                        copyResult.id,
                                                                        ...copyResult.subtask_ids
                                                                    );

                                                                    list_rel_values.push(
                                                                        ...(copyResult.list_rel_values || [])
                                                                    );
                                                                    each_cb();
                                                                }
                                                            }
                                                        );
                                                    },
                                                    (eachErr: Error) => {
                                                        para_cb(eachErr);
                                                    }
                                                );
                                            },
                                        },

                                        // OVM: copyAttachments handles the OVM updates within a separate transaction
                                        attachments(para_cb) {
                                            if (
                                                !options.attachments &&
                                                !options.comment_attachments &&
                                                !options.attachment_ids
                                            ) {
                                                para_cb();
                                                return;
                                            }

                                            const _options = {
                                                ...options,
                                                ...(saving_template
                                                    ? { attachments: true, comment_attachments: true }
                                                    : {}),
                                                coverimage: task_to_copy.coverimage,
                                                team_id: task_to_copy.workspace_id,
                                                proxyClient,
                                                copy_attachments_from_s3: true,
                                            };

                                            copyAttachments(
                                                task_id,
                                                new_id,
                                                null,
                                                _options,
                                                (attach_err: Error | null, attach_result: any) => {
                                                    if (attach_result && attach_result.ids) {
                                                        attachment_ids = attach_result.ids;
                                                    }

                                                    if (attach_result?.attachmentMap) {
                                                        attachmentMap = attach_result.attachmentMap;
                                                    }

                                                    let contentChanged = false;
                                                    if (!isEmpty(attachmentMap)) {
                                                        const quillContent = parseQuillContent(task_to_copy.content);
                                                        const updatedContent = JSON.stringify(
                                                            replaceAttachmentsInContent(
                                                                quillContent,
                                                                attachmentMap,
                                                                task_to_copy.workspace_id
                                                            )
                                                        );
                                                        if (updatedContent !== task_to_copy.content) {
                                                            task_to_copy.content = updatedContent;
                                                            content = task_to_copy.content;
                                                            contentChanged = true;
                                                        }
                                                    }

                                                    if (attach_result?.coverimage || contentChanged) {
                                                        const updateFields = [];
                                                        const updateValues = [new_id];
                                                        let queryIndex = 2;

                                                        if (attach_result?.coverimage) {
                                                            updateFields.push(`coverimage = $${queryIndex}`);
                                                            updateValues.push(attach_result.coverimage);
                                                            queryIndex++;
                                                        }

                                                        if (contentChanged) {
                                                            updateFields.push(`content = $${queryIndex}`);
                                                            updateValues.push(content);
                                                        }

                                                        db.writeQuery(
                                                            `UPDATE task_mgmt.items SET ${updateFields.join(
                                                                ', '
                                                            )} WHERE id = $1`,
                                                            updateValues,
                                                            para_cb
                                                        );
                                                    } else {
                                                        para_cb(attach_err, attach_result);
                                                    }
                                                }
                                            );
                                        },

                                        // OVM: None
                                        attachmentDocs(para_cb) {
                                            if (!options.attachments) {
                                                para_cb();
                                                return;
                                            }

                                            options.description = content;

                                            copyAttachmentDocs(
                                                task_id,
                                                new_id,
                                                null,
                                                {
                                                    ...options,
                                                    team_id: task_to_copy.workspace_id,
                                                    saving_template,
                                                },
                                                (attach_err: Error | null, attach_result: any) => {
                                                    // attachment_ids.push(...attach_result.ids);

                                                    if (attach_result?.viewMap && attach_result?.pageMap) {
                                                        docAttachmentMap.view = attach_result.viewMap;
                                                        docAttachmentMap.page = attach_result.pageMap;
                                                    }

                                                    para_cb(attach_err, attach_result);
                                                }
                                            );
                                        },

                                        async formAttachments(para_cb) {
                                            if (!in_params.attachments) {
                                                para_cb();
                                                return;
                                            }

                                            try {
                                                await bindAttachmentsToParentAsync(
                                                    in_params.attachments,
                                                    new_id,
                                                    config.attachments.types.task,
                                                    {
                                                        workspaceId: task_to_copy.workspace_id, // BUG - was originally workspace_id - would always be undefined inside
                                                    }
                                                );
                                                para_cb();
                                            } catch (err1) {
                                                para_cb(new CopyError(err1, 'ITEM_138'));
                                            }
                                        },

                                        tags(para_cb) {
                                            if (!options.old_tags && !options.tags) {
                                                para_cb();
                                                return;
                                            }

                                            const _options = {
                                                ...options,
                                                userid,
                                                from_v2_template,
                                                ...(saving_template ? { old_tags: true } : {}),
                                                workspace_id: task_to_copy.workspace_id,
                                                shouldRetainOriginalHistoryUser:
                                                    options.task_activity && options.from_dupe_task,
                                            };

                                            copyTags(task_id, new_id, null, _options, para_cb);
                                        },

                                        // OVM: None
                                        checklists(para_cb) {
                                            if (!options.old_checklists) {
                                                para_cb();
                                                return;
                                            }

                                            const copyOption = {
                                                ...options,
                                                carry_item_state: options.old_checklist_item_state,
                                            };
                                            checklistMod._copyChecklists(task_id, new_id, null, copyOption, para_cb);
                                        },

                                        async checkEntitlementAndSaveCustomTaskType(para_cb) {
                                            if (!options.custom_type) {
                                                para_cb();
                                                return;
                                            }

                                            if (!customType?.isCustomType()) {
                                                para_cb();
                                                return;
                                            }

                                            try {
                                                const targetWorkspaceId = Number(task_to_copy.workspace_id);

                                                if (customType?.isPredefinedCustomTypeRange()) {
                                                    para_cb();
                                                    return;
                                                }

                                                await saveTaskTemplateCustomTaskType(+userid, task_id, new_id, {
                                                    targetWorkspaceId,
                                                    proxyClient,
                                                });
                                                para_cb();
                                            } catch (e) {
                                                para_cb(e as Error);
                                            }
                                        },

                                        // OVM: UPDATE - Task (task template updates)
                                        custom_fields(para_cb) {
                                            if (!(options.custom_fields || options.task_relationships)) {
                                                para_cb();
                                                return;
                                            }

                                            async.series(
                                                [
                                                    function saveTemplateFields(_series_cb) {
                                                        if (!options.template_v2) {
                                                            _series_cb();
                                                            return;
                                                        }

                                                        fieldHelper.saveTaskTemplateFields(
                                                            userid,
                                                            new_id,
                                                            task_id,
                                                            {
                                                                workspace_id: task_to_copy.workspace_id,
                                                                proxyClient,
                                                            },
                                                            _series_cb
                                                        );
                                                    },

                                                    function copyValues(_series_cb) {
                                                        const task_activity = saving_template
                                                            ? false
                                                            : options.task_activity;

                                                        const field_opts = {
                                                            field_map,
                                                            dd_option_map,
                                                            ll_option_map,
                                                            userid,
                                                            skip_fields: in_params.customFields,
                                                            only_fields,
                                                            team_id: task_to_copy.workspace_id,
                                                            task_activity,
                                                            task_relationships: options.task_relationships,
                                                            custom_fields: options.custom_fields,
                                                            proxyClient,
                                                        };

                                                        copyFieldValues(new_id, task_id, field_opts, _series_cb);
                                                    },
                                                ],
                                                para_cb
                                            );
                                        },

                                        // OVM: Task and Attachment
                                        overwrite_custom_fields(para_cb) {
                                            // for forms only
                                            if (!in_params.customFields) {
                                                para_cb();
                                                return;
                                            }

                                            async.each(
                                                in_params.customFields,
                                                (field: any, each_cb) => {
                                                    const { value_options } = field;
                                                    const field_options =
                                                        in_params.via === 'form' && value_options
                                                            ? {
                                                                  createTask: true,
                                                                  skipAccess: true,
                                                                  value_options,
                                                                  team_id: task_to_copy.workspace_id,
                                                              }
                                                            : {
                                                                  createTask: true,
                                                                  skipAccess: true,
                                                                  team_id: task_to_copy.workspace_id,
                                                              };
                                                    fieldValue._addFieldValue(
                                                        +userid,
                                                        new_id,
                                                        field.id,
                                                        field.value,
                                                        field_options,
                                                        each_cb
                                                    );
                                                },
                                                para_cb
                                            );
                                        },

                                        // OVM: None
                                        async copySyncBlocks(para_cb) {
                                            if (
                                                syncBlocks.length === 0 ||
                                                !(saving_template || options.from_template)
                                            ) {
                                                para_cb();
                                                return;
                                            }

                                            try {
                                                syncBlocks.forEach(syncBlock => {
                                                    syncBlock.parent_id = new_id;
                                                    syncBlock.id = sync_blocks_old_to_new[syncBlock.id];
                                                });
                                                const queries = prepareSyncBlockQueries(
                                                    Number(userid),
                                                    task_to_copy.workspace_id,
                                                    syncBlocks
                                                );
                                                await batchQueriesAsync(queries);
                                                CopyOperation.reportObjectsCopied(
                                                    ...Object.entries(sync_blocks_old_to_new).map(
                                                        ([originalId, newId]) =>
                                                            [
                                                                CopyableObjectKey.from(
                                                                    CopyableObjectType.SyncBlock,
                                                                    original_team,
                                                                    originalId
                                                                ),
                                                                CopyableObjectKey.from(
                                                                    CopyableObjectType.SyncBlock,
                                                                    new_workspace_id,
                                                                    newId
                                                                ),
                                                            ] as [CopyableObjectKey, CopyableObjectKey]
                                                    )
                                                );
                                                para_cb();
                                            } catch (syncBlockErr) {
                                                para_cb(syncBlockErr as Error);
                                            }
                                        },

                                        // OVM: None
                                        async listRelationship(para_cb) {
                                            if (
                                                isUnifiedFieldRemappingEnabled(userid) ||
                                                !(
                                                    options.list_relationships &&
                                                    (options.parent_is_template || options.from_parent_template)
                                                )
                                            ) {
                                                para_cb();
                                                return;
                                            }

                                            const _options = { field_map, subcat_ids: options.subcat_ids };

                                            try {
                                                const task_links_to_map = (await getListRelationshipValuesAsync(
                                                    new_id,
                                                    task_id,
                                                    _options
                                                )) as any[];

                                                list_rel_values.push(...(task_links_to_map || []));

                                                para_cb();
                                            } catch (e) {
                                                para_cb(new CopyError(e, 'ITEM_211'));
                                            }
                                        },
                                    }),
                                    series_cb
                                );
                            },
                        },
                        // OVM: None
                        {
                            copyAssignedComments: function assigned_comments(series_cb) {
                                if (!options.comment) {
                                    series_cb();
                                    return;
                                }

                                // if options.assigned_comment, then _options.comment should be FALSE
                                const comment = options.comment && !options.assigned_comment;

                                const _options = {
                                    comment,
                                    type: TASK_TYPE,
                                    unresolve_comments: options.unresolve_comments,
                                    exclude_attachment: !options.attachments,
                                    proxyClient,
                                    workspace_id: task_to_copy.workspace_id,
                                    attachmentMap,
                                    docAttachmentMap,
                                };

                                copyComments.copyComments(task_id, new_id, null, _options, series_cb);
                            },
                        },
                        // 3.5
                        {
                            copyTaskMembers: async series_cb => {
                                if (task_to_copy.parent && task_to_copy.parent !== -1) {
                                    series_cb();
                                    return;
                                }

                                if (options.skip_privacy) {
                                    series_cb();
                                    return;
                                }

                                const now_date = new Date().getTime();
                                const task_to_copy_id = task_to_copy.original_parent || task_to_copy.id;

                                const get_team_id_props: {
                                    team_ids?: number[];
                                    subcategory_ids?: number[];
                                    project_ids?: number[];
                                } = {};
                                const { public_template, subcategory_id, category_id, parent_is_template } = options;

                                if (public_template && parent_is_template) {
                                    get_team_id_props.team_ids = task_to_copy.workspace_id;
                                } else if (!public_template && task_to_copy.workspace_id) {
                                    get_team_id_props.team_ids = [task_to_copy.workspace_id];
                                } else if (public_template && subcategory_id) {
                                    get_team_id_props.subcategory_ids = [+subcategory_id];
                                } else if (public_template && category_id) {
                                    get_team_id_props.subcategory_ids = [category_id];
                                } else if (public_template && project_id) {
                                    get_team_id_props.project_ids = [Number(project_id)];
                                }

                                let workspace_id;
                                try {
                                    workspace_id = await getTeamId(get_team_id_props, {});
                                } catch (error) {
                                    series_cb(error as Error);
                                    return;
                                }

                                if (!workspace_id) {
                                    series_cb(new CopyError('Cant find team', 'ITEM_411'));
                                    return;
                                }

                                const versionUpdates = [];

                                // copy over task members and group members queries
                                const queries = [
                                    getInsertTaskMembersFromCopiedTask(new_id, task_to_copy_id, now_date, workspace_id),
                                    getInsertTaskGroupMembersFromCopiedTask(
                                        new_id,
                                        task_to_copy_id,
                                        now_date,
                                        workspace_id
                                    ),
                                ];

                                if (task_to_copy.original_parent) {
                                    // OVM: This UPDATE is part of the larger transaction so no OVM record is needed here
                                    try {
                                        const result = await proxyClient.replicaQueryAsync({
                                            queryFamily: QueryFamily.Item,
                                            queryKey: ItemQueryKey.GetParentPrivate,
                                            queryParams: [task_to_copy.original_parent],
                                        });
                                        let privateParent = false;
                                        if (result.rows.length) {
                                            [{ private: privateParent }] = result.rows;
                                        }
                                        const relationships = [
                                            {
                                                type: ObjectRelationshipType.TASK_PARENT,
                                                object_type: ObjectType.TASK,
                                                object_id: task_to_copy.original_parent,
                                                workspace_id,
                                            },
                                        ];
                                        if (project_id) {
                                            relationships.push({
                                                type: ObjectRelationshipType.TASK_IN_SPACE,
                                                object_type: ObjectType.SPACE,
                                                object_id: project_id,
                                                workspace_id,
                                            });
                                        }
                                        versionUpdates.push({
                                            object_type: ObjectType.TASK,
                                            object_id: new_id,
                                            workspace_id: task_to_copy.workspace_id,
                                            operation: OperationType.CREATE,
                                            data: {
                                                relationships,
                                                context: { ws_key: options.ws_key },
                                            },
                                        });
                                        versionUpdates.push({
                                            object_type: ObjectType.TASK_ACCESS,
                                            object_id: new_id,
                                            workspace_id: task_to_copy.workspace_id,
                                            operation: OperationType.CREATE,
                                        });
                                        queries.push({
                                            query: `
                                                UPDATE task_mgmt.items
                                                SET private = $1
                                                WHERE id = $2`,
                                            params: [privateParent, new_id],
                                        });
                                    } catch (parentErr) {
                                        series_cb(parentErr as Error);
                                        return;
                                    }
                                }

                                /**
                                 * When the User apply Template
                                 * Then give him a full access to the Task
                                 */
                                if (options.from_parent_template) {
                                    queries.push({
                                        query: `
                                        INSERT INTO task_mgmt.task_members
                                            ( task_id, userid, date_added, permission_level, workspace_id)
                                        VALUES
                                            ( $1, $2, $3, $4, $5 )
                                        ON CONFLICT
                                            ( task_id, userid )
                                        DO UPDATE SET
                                            permission_level = $4`,
                                        params: [new_id, userid, new Date().getTime(), 5, workspace_id],
                                    });
                                }

                                try {
                                    await batchQueriesAsync(queries, null, versionUpdates);
                                } catch (batchErr) {
                                    series_cb(new CopyError(batchErr, 'ITEM_210', 500));
                                    return;
                                }
                                series_cb(null);
                            },
                        },

                        // OVM: None
                        {
                            copyTaskDependencies: series_cb => {
                                if (
                                    (!options.top &&
                                        (!options.set_deleted || options.set_deleted_for_outside_transaction)) ||
                                    !options.internal_dependencies
                                ) {
                                    series_cb();
                                } else {
                                    copyDependencies.copyDependencies(
                                        {
                                            old_dependencies,
                                            old_to_new,
                                            userid,
                                        },
                                        series_cb
                                    );
                                }
                            },
                        },

                        // OVM: None
                        {
                            copyExternalDependencies: series_cb => {
                                if (
                                    (!options.force_copy_external_dependencies &&
                                        !options.top &&
                                        (!options.set_deleted || options.set_deleted_for_outside_transaction)) ||
                                    !options.external_dependencies
                                ) {
                                    series_cb();
                                } else {
                                    copyDependencies.copyExternalDependencies(
                                        {
                                            old_dependencies,
                                            old_to_new,
                                            userid,
                                            internal_dependencies: options.internal_dependencies,
                                            workspace_id: task_to_copy.workspace_id,
                                        },
                                        series_cb
                                    );
                                }
                            },
                        },

                        // OVM: None
                        {
                            copyLinks: series_cb => {
                                if (
                                    (!options.top &&
                                        (!options.set_deleted || options.set_deleted_for_outside_transaction)) ||
                                    !options.task_relationships
                                ) {
                                    series_cb();
                                } else {
                                    copyLinks({ old_links, old_to_new, userid }, series_cb);
                                }
                            },
                        },

                        // OVM: None
                        {
                            copyExternalLinks: series_cb => {
                                if (
                                    (!options.top &&
                                        (!options.set_deleted || options.set_deleted_for_outside_transaction)) ||
                                    !options.task_relationships
                                ) {
                                    series_cb();
                                } else {
                                    copyExternalLinks(
                                        {
                                            old_links,
                                            old_to_new,
                                            userid,
                                            internal_dependencies: options.internal_dependencies,
                                        },
                                        series_cb
                                    );
                                }
                            },
                        },

                        // 6 set item to not deleted if starting point
                        // OVM: UPDATE - Task
                        {
                            undeleteTask: async series_cb => {
                                logger.info({
                                    msg: 'undelete task',
                                    set_delete: options.set_deleted,
                                    set_undelete: options.set_undeleted,
                                    task_to_copy: task_id,
                                    new_task_id: new_id,
                                    subtask_ids,
                                });
                                if (!options.set_undeleted) {
                                    series_cb();
                                    return;
                                }
                                const parent = task_to_copy.subtask_parent ?? task_to_copy.parent;

                                const relationships = [];
                                if (parent || task_to_copy.subcategory) {
                                    relationships.push({
                                        type: ObjectRelationshipType.TASK_PARENT,
                                        object_type: parent ? ObjectType.TASK : ObjectType.LIST,
                                        object_id: parent || task_to_copy.subcategory,
                                        workspace_id: task_to_copy.workspace_id,
                                    });
                                }
                                if (project_id) {
                                    relationships.push({
                                        type: ObjectRelationshipType.TASK_IN_SPACE,
                                        object_type: ObjectType.SPACE,
                                        object_id: project_id,
                                        workspace_id: task_to_copy.workspace_id,
                                    });
                                }

                                const taskVersionUpdates: ObjectVersionUpdateRequest[] = [
                                    {
                                        object_type: ObjectType.TASK,
                                        object_id: new_id,
                                        workspace_id: task_to_copy.workspace_id,
                                        operation: OperationType.CREATE,
                                        data: {
                                            relationships,
                                            context: { ws_key: options.ws_key },
                                        },
                                    },
                                    {
                                        object_type: ObjectType.TASK_ACCESS,
                                        object_id: new_id,
                                        workspace_id: task_to_copy.workspace_id,
                                        operation: OperationType.CREATE,
                                    },
                                ];

                                if (parent) {
                                    taskVersionUpdates.push({
                                        object_type: ObjectType.TASK,
                                        object_id: parent,
                                        workspace_id: task_to_copy.workspace_id,
                                        operation: OperationType.UPDATE,
                                        data: {
                                            context: { ws_key: options.ws_key },
                                        },
                                    });
                                }
                                if (subtask_ids.length > 0 && useFanoutOnUndeleteUpdate(userid).enabled) {
                                    taskVersionUpdates.push(
                                        ...subtask_ids.map(subtask_id => ({
                                            object_type: ObjectType.TASK,
                                            object_id: subtask_id,
                                            operation: OperationType.UPDATE,
                                            workspace_id: task_to_copy.workspace_id,
                                        }))
                                    );
                                    taskVersionUpdates.push(
                                        ...subtask_ids.map(subtask_id => ({
                                            object_type: ObjectType.TASK_ACCESS,
                                            object_id: subtask_id,
                                            operation: OperationType.UPDATE,
                                            workspace_id: task_to_copy.workspace_id,
                                        }))
                                    );
                                }
                                try {
                                    await writeAsync(
                                        'UPDATE task_mgmt.items SET deleted = false, date_deleted = null WHERE id = $1',
                                        [new_id],
                                        taskVersionUpdates
                                    );
                                    await sendWebhookInBatchOnTaskCreation(
                                        [new_id, ...subtask_ids],
                                        task_to_copy.workspace_id,
                                        {
                                            dup_trigger: in_params.dup_trigger,
                                            skip_task_created_trigger: options.skip_task_created_trigger,
                                            webhook_payload: options.webhook_payload,
                                            auto_id: options.auto_id,
                                            trigger_id: options.trigger_id,
                                        }
                                    );
                                    // Send Notifications for task and subtasks when it was undeleted. Otherwise, it will
                                    // fails as privacy checks fail for deleted objects.
                                    postTaskCreatedNotifications(userid, [new_id, ...subtask_ids]);
                                } catch (updateErr) {
                                    series_cb(updateErr as Error);
                                    return;
                                }
                                series_cb(null);
                            },
                        },

                        // OVM: None
                        {
                            linkBugSnagIssue: series_cb => {
                                if (!options.bugsnag_link) {
                                    series_cb();
                                    return;
                                }

                                bugsnagAutomation.linkBugsnagIssue(
                                    options.bugsnag_link,
                                    options.trigger_id,
                                    new_id,
                                    team_id,
                                    options,
                                    () => {
                                        series_cb();
                                    }
                                );
                            },
                        },

                        // OVM: None
                        {
                            linkGitHubResource: series_cb => {
                                if (!in_params.gh_add_link) {
                                    series_cb();
                                    return;
                                }

                                githubAutomation.linkGHResourceToTask(new_id, in_params.gh_add_link, (linkErr: any) => {
                                    if (linkErr) {
                                        logTaskError(linkErr, 'ITEM_204');
                                    }
                                });
                            },
                        },
                    ]),
                    async seriesErr => {
                        const finishPromises = [];
                        if (seriesErr) {
                            logger.error({
                                msg: 'Failed to copy task',
                                err: seriesErr,
                                ECODE: 'ITEM_074',
                                new_task_id: new_id,
                            });

                            if (getTaskServiceLogConfig().orphaned_tasks) {
                                const obj = new Error();
                                Error.captureStackTrace(obj);
                                logger.info({
                                    msg: 'copyTaskService.copyTask: info logs when failed',
                                    set_delete: options.set_deleted,
                                    set_undelete: options.set_undeleted,
                                    stack: obj.stack,
                                    task_to_copy: task_id,
                                    new_task_id: new_id,
                                });
                            }

                            const returnedError =
                                (seriesErr as ClickUpError).ECODE || seriesErr instanceof HttpException
                                    ? seriesErr
                                    : ({
                                          err: 'Internal server error',
                                          status: 500,
                                          ECODE: 'ITEM_074',
                                      } as GenericError);

                            if (options.return_immediately) {
                                finishPromises.push(options[TEMPLATE_TRACER_KEY]?.finishTracing(returnedError));
                            }
                            finishPromises.push(finish(returnedError));
                        } else {
                            const finalizeStepTimer = startTimer();
                            if (!saving_template && customType?.isUsableCustomType()) {
                                try {
                                    await entitlementService.incrementEntitlement(
                                        task_to_copy.workspace_id,
                                        EntitlementName.CustomItems
                                    );
                                } catch (e) {
                                    logger.error({
                                        msg: 'Failed to increment entitlement',
                                        err: e,
                                        ECODE: 'ITEM_320',
                                        new_task_id: new_id,
                                        task_to_copy: task_id,
                                    });
                                }
                            }

                            const notif_function = () => {
                                if (options.send_notifs_to_user) {
                                    sqsNotif.sendNotifMessage('createTaskNotifications', [
                                        [],
                                        new_id,
                                        createHistId,
                                        {},
                                    ]);
                                } else {
                                    sqsNotif.sendNotifMessage('createTaskNotifications', [
                                        userid,
                                        new_id,
                                        createHistId,
                                        {},
                                    ]);
                                }

                                assignee_hist_items.forEach((row: any) => {
                                    if (
                                        options.notify_all ||
                                        (row.field === 'assignee' && parseInt(row.after, 10) !== parseInt(userid, 10))
                                    ) {
                                        sqsNotif.sendNotifMessage('createTaskNotification', [
                                            row.after,
                                            new_id,
                                            row.id,
                                            { created_by: userid },
                                        ]);
                                    } else if (row.field === 'group_assignee') {
                                        sqsNotif.sendNotifMessage('createTaskNotifications', [
                                            userid,
                                            new_id,
                                            row.id,
                                            { field: row.field },
                                        ]);
                                    }
                                });
                            };

                            const ret_val: {
                                id: string;
                                list_rel_values?: any[];
                                old_to_new?: any;
                                old_dependencies?: any;
                                old_links?: any;
                                from_private?: boolean;
                                notif_function?: any;
                                permanent_template_id?: string;
                                task_start_date_shift?: number;
                                task_due_date_shift?: number;
                                subtask_start_date_shift?: number;
                                subtask_due_date_shift?: number;
                                workspace_id?: number;
                                subtask_ids?: string[];
                            } = {
                                id: new_id,
                                list_rel_values,
                                old_to_new,
                                old_dependencies,
                                old_links,
                                notif_function,
                                permanent_template_id,
                                task_start_date_shift: options.task_start_date_shift,
                                task_due_date_shift: options.task_due_date_shift,
                                subtask_start_date_shift: options.subtask_start_date_shift,
                                subtask_due_date_shift: options.subtask_due_date_shift,
                                workspace_id: new_workspace_id,
                                subtask_ids,
                            };

                            if (options.ret_from_private) {
                                ret_val.from_private = task_to_copy.private;
                            }

                            finishPromises.push(finish(null, ret_val));

                            const cacheInvalidationAndWebhookTimer = startTimer();
                            const getItemPromise = new Promise((resolveGetItem, rejectGetItem) => {
                                getTask._getItem(
                                    +userid,
                                    new_id,
                                    { include_deleted: true },
                                    async (getErr: Error | null, taskResult: any) => {
                                        if (getErr) {
                                            metricsClient.timing(
                                                METRIC_NAME,
                                                endTimer(cacheInvalidationAndWebhookTimer),
                                                {
                                                    step: 'cache_invalidation_and_webhook',
                                                    success: 'false',
                                                }
                                            );
                                            rejectGetItem();
                                            return;
                                        }

                                        reportTaskChanged(userid, new_id);
                                        for (const id of attachment_ids) {
                                            reportAttachmentChanged(userid, id);
                                        }

                                        subcatHelper.invalidateCachedSubcatTimeEstimate([taskResult.subcategory.id]);
                                        subcatHelper.invalidateCachedSubcatTaskCount([taskResult.subcategory.id]);
                                        subcatHelper.invalidateCachedSubcatTimeSpent([taskResult.subcategory.id]);
                                        googleCal.newCUTaskCreated(taskResult);

                                        subtask_ids.forEach(subtask_id => {
                                            googleCal.newCUTaskIDCreated(subtask_id);
                                        });

                                        if (options.set_deleted || options.from_recur) {
                                            sqsWs.sendWSMessage('sendTaskCreated', [taskResult.id, options.ws_key]);
                                        }

                                        if (userid) {
                                            if (!options.send_notifs_to_user) {
                                                sqsWs.sendWSMessage('sendTaskCopyComplete', [
                                                    userid,
                                                    taskResult.id,
                                                    options.ws_key,
                                                    options.template_id != null,
                                                    options[TEMPLATE_TRACER_KEY]?.getHistoryTracingId(),
                                                ]);
                                            }
                                        }

                                        if (new_due_date && !options.skip_delayed_triggers) {
                                            try {
                                                await createScheduledDueDateTriggers(
                                                    [
                                                        {
                                                            task_id: new_id,
                                                            due_date: new_due_date,
                                                            due_date_time: new_due_date_time,
                                                            workspace_id: task_to_copy.workspace_id,
                                                        },
                                                    ],
                                                    {
                                                        trigger_id: options.trigger_id,
                                                        auto_id: options.auto_id,
                                                        scheduled_from: 'copy_task',
                                                    }
                                                );
                                            } catch (err1) {
                                                logTaskError(err1, 'ITEM_213');
                                            }
                                        }

                                        if (new_start_date && !options.skip_delayed_triggers) {
                                            try {
                                                await createScheduledStartDateTriggers(
                                                    [
                                                        {
                                                            task_id: new_id,
                                                            start_date: new_start_date,
                                                            start_date_time: new_start_date_time,
                                                            workspace_id: task_to_copy.workspace_id,
                                                        },
                                                    ],
                                                    {
                                                        trigger_id: options.trigger_id,
                                                        auto_id: options.auto_id,
                                                        scheduled_from: 'copy_task',
                                                    }
                                                );
                                            } catch (err1) {
                                                logTaskError(err1, 'ITEM_214');
                                            }
                                        }
                                        metricsClient.timing(METRIC_NAME, endTimer(cacheInvalidationAndWebhookTimer), {
                                            step: 'cache_invalidation_and_webhook',
                                            success: 'true',
                                        });
                                        resolveGetItem(null);
                                    }
                                );
                            });

                            finishPromises.push(getItemPromise);
                            if (task_to_copy.team_id) {
                                createTask.updateRecentlyCreated(userid, task_to_copy.team_id, new_id);
                            }

                            if (options.create_notifications) {
                                notif_function();
                                subtask_notif_functions.forEach(fn => {
                                    fn();
                                });
                            }

                            if (
                                options.from_template &&
                                !(options.from_parent_template || options.from_v2_template_child)
                            ) {
                                finishPromises.push(
                                    templateHelpers.storeTemplateUsed(
                                        +userid,
                                        options.permanent_template_id,
                                        'task',
                                        null,
                                        task_to_copy.workspace_id
                                    )
                                );
                            }

                            if (saving_template) {
                                finishPromises.push(
                                    templateHelpers.updateTaskHistory(userid, tracer.getTraceID(), new_id, task_id)
                                );
                            }

                            if (options[DURATION_METRIC_KEY]) {
                                DurationMetricObject.emit(options[DURATION_METRIC_KEY]);
                            }

                            options[TEMPLATE_TRACER_KEY]?.addTags({
                                copiedTasksAndSubtasksCount: Object.keys(old_to_new).length,
                            });
                            finishPromises.push(options[TEMPLATE_TRACER_KEY]?.finishTracing());
                            metricsClient.timing(METRIC_NAME, endTimer(finalizeStepTimer), {
                                step: 'finalize',
                                success: 'true',
                            });
                        }
                        if (use_graceful_shutdown) {
                            await Promise.allSettled(finishPromises);
                            logger.debug(
                                `processShutdownController job done for job id=${graceful_shutdown_process_id}`
                            );
                            processShutdownController.jobDone(graceful_shutdown_process_id);
                        }
                    }
                );
            }
        );
    });
}

export const _copyTask: typeof _copyTaskWithoutSemaphoreLimit = withSemaphoreLimit(
    OperationPrefix.COPY_TASK,
    userid => {
        const splitConfig = getSemaphoreCopyOptions(userid);
        return {
            ...splitConfig.taskCopySemaphoreOptions,
            perTraceIdConcurrencyEnabled: splitConfig.perTraceIdConcurrencyEnabled,
            perTraceIdConcurrencyLimit: splitConfig.perTraceIdConcurrencyLimit,
        };
    },
    CopyOperation.wrap(ObjectType.TASK, _copyTaskWithoutSemaphoreLimit),
    addPressureToFunctionParams([OperationPrefix.COPY_TASK])
);

function prepareOptionsForSubTask(
    options: CopyTaskOptions,
    row: any,
    task_to_copy: any,
    parent: any,
    subtask_parent: string | number,
    dd_option_map: any,
    ll_option_map: any,
    field_map: any,
    nested_subtasks: any,
    orderindexStatsForSubtasks: any,
    saving_template: boolean,
    subtasks_name_map: any,
    sync_blocks_old_to_new: Record<string, string>,
    inner_due_diff: number,
    inner_start_diff: number,
    proxyClient: QueryProxyClient,
    isUsingRemapDateOfTaskV2: boolean
): CopyTaskOptions {
    const due_date_diff =
        task_to_copy.due_date && options.due_date
            ? parseInt(options.due_date.toString(), 10) - parseInt(task_to_copy.due_date, 10)
            : 0;

    const due_date = options.latest_due_date || options.earliest_due_date ? options.due_date : null;

    const from_parent_template = options.from_template || options.from_parent_template;

    const _options: Partial<CopyTaskOptions> = {
        archived: options.archived,
        assigned_comment: options.assigned_comment,
        assignees: options.subtask_assignees,
        attachments: options.attachments,
        auto_id: options.auto_id,
        content: options.content,
        comment: options.comment,
        task_activity: options.task_activity,
        comment_attachments: options.comment_attachments,
        create_notifications: false,
        create_template: options.template_v2,
        custom_fields: options.custom_fields,
        custom_type: options.custom_type,
        dd_option_map,
        default_skip_access: options.default_skip_access,
        due_date,
        due_date_time: row.due_date_time,
        earliest_due_date: options.earliest_due_date,
        earliest_start_date: options.earliest_start_date,
        external_dependencies: options.external_dependencies,
        field_map,
        followers: options.followers,
        from_dupe_task: options.from_dupe_task,
        from_parent_task: true,
        from_parent_template,
        from_task_template: options.from_task_template,
        from_recur: options.from_recur,
        from_template: options.from_template,
        from_v2_template_child: options.from_v2_template_child,
        apply_gantt_dates: options.apply_gantt_dates,
        gantt_dates: options.gantt_dates,
        internal_dependencies: options.internal_dependencies,
        latest_due_date: options.latest_due_date,
        list_relationships: options.list_relationships,
        ll_option_map,
        nested_subtasks,
        old_assignees: options.old_subtask_assignees,
        old_checklists: options.old_checklists,
        old_due_date: options.old_due_date,
        old_followers: options.old_followers,
        old_orderindex: !!orderindexStatsForSubtasks,
        old_start_date: options.old_start_date,
        old_status: options.old_status,
        old_statuses: options.old_statuses,
        old_subtask_assignees: options.old_subtask_assignees,
        old_tags: options.old_tags,
        old_duration: options.old_duration,
        parent,
        parent_is_task_template: options.parent_is_task_template || options.top,
        parent_is_template: saving_template,
        public_template_child_copy: options.public_template || options.public_template_child_copy,
        recur_settings: options.recur_settings,
        remap_start_date: options.remap_start_date,
        set_deleted: false,
        skip_access: options.skip_access,
        skip_weekends: options.skip_weekends,
        skip_delayed_triggers: options.skip_delayed_triggers,
        start_date: options.earliest_start_date ? options.start_date : null,
        start_date_time: row.start_date_time,
        subcategory_id: options.subcategory_id,
        subtask_assignees: options.subtask_assignees,
        subtask_parent,
        status: options.subtask_status || options.status,
        subtasks: nested_subtasks ? options.subtasks : false,
        subtask_status: options.subtask_status,
        tags: options.tags,
        task_relationships: options.task_relationships,
        time_estimate: options.time_estimate,
        template_v2_child: options.template_v2_child,
        user_tz: options.user_tz,
        copy_subtask: true,
        subtask_start_date_shift: inner_start_diff,
        subtask_due_date_shift: inner_due_diff,
        subtask_date: options.subtask_date,
        skip_task_created_trigger: options.skip_task_created_trigger,
        estimates_per_assignee: options.estimates_per_assignee,
        proxyClient,
        batched_copy_limit: 1, // for nested subtasks, go to default of doing in series
        via: options.via,
        webhook_payload: options.webhook_payload,
        orderindexStats: orderindexStatsForSubtasks,
        sync_blocks_old_to_new,
        subtask_count_validation_performed: true,
        callOrigin: options.callOrigin,
        skip_date_remapping: options.skip_date_remapping,
    };

    if (subtasks_name_map && subtasks_name_map.has(row.id)) {
        _options.name = subtasks_name_map.get(row.id);
    }

    if (
        (!isUsingRemapDateOfTaskV2 || options.from_task_template) &&
        task_to_copy.due_date &&
        options.due_date &&
        row.due_date
    ) {
        _options.due_date = parseInt(row.due_date, 10) + due_date_diff;
        _options.due_date_time = row.due_date_time;
    }

    if (options.due_date && row.start_date && due_date_diff && (options.remap_start_date || options.from_recur)) {
        _options.start_date = parseInt(row.start_date, 10) + due_date_diff;
        _options.start_date_time = row.start_date_time;
    }

    if (!options.subtask_date && options.from_recur) {
        _options.due_date = null;
        _options.due_date_time = null;
        _options.start_date = null;
        _options.start_date_time = null;
    }

    return _options;
}
