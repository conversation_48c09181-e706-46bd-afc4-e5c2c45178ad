import { Request, Response } from 'express';
import { copyChecklistItemState } from '../copyChecklistItemState';
import { access } from '../../access';
import { config } from '../../../config';

interface CopyChecklistItemStateRequest extends Request {
    body: {
        source_checklist_item_id: string;
        target_checklist_item_id: string;
    };
}

/**
 * Controller to handle copying checklist item state
 */
export async function copyChecklistItemStateController(req: CopyChecklistItemStateRequest, res: Response): Promise<void> {
    const { source_checklist_item_id, target_checklist_item_id } = req.body;
    const userid = req.userid;

    try {
        // Check access permissions
        await new Promise((resolve, reject) => {
            access.checkAccessChecklistItem(
                userid,
                source_checklist_item_id,
                { permissions: [config.permission_constants.edit_checklists] },
                (err) => {
                    if (err) reject(err);
                    else resolve(null);
                }
            );
        });

        await new Promise((resolve, reject) => {
            access.checkAccessChecklistItem(
                userid,
                target_checklist_item_id,
                { permissions: [config.permission_constants.edit_checklists] },
                (err) => {
                    if (err) reject(err);
                    else resolve(null);
                }
            );
        });

        // Copy the state
        await copyChecklistItemState(userid, {
            sourceItemId: source_checklist_item_id,
            targetItemId: target_checklist_item_id,
            ws_key: req.body.ws_key
        });

        res.json({ ok: true });
    } catch (err) {
        res.status(err.status || 500).json({
            err: err.err || 'Internal server error',
            ECODE: err.ECODE || 'CHECK_999'
        });
    }
} 