import { db } from '../../utils/db';
import { getObjectVersionManager } from '../../utils/ovm';
import { ObjectType, OperationType } from '../../utils/ovm/types';
import { entitiesService } from '../../utils/entities';
import { sqsWs } from '../../utils/sqs';

interface CopyChecklistItemStateOptions {
    sourceItemId: string;
    targetItemId: string;
    ws_key?: string;
}

/**
 * Copy the state (resolved status) from one checklist item to another
 * @param userid - The ID of the user performing the action
 * @param options - Options containing source and target checklist item IDs
 * @returns Promise that resolves when the state is copied
 */
export async function copyChecklistItemState(userid: number, options: CopyChecklistItemStateOptions): Promise<void> {
    const { sourceItemId, targetItemId } = options;

    // Get source checklist item state
    const sourceResult = await db.replicaQueryAsync(
        `SELECT 
            checklist_items.resolved,
            checklists.task_id,
            checklist_items.workspace_id
        FROM 
            task_mgmt.checklist_items
            JOIN task_mgmt.checklists ON checklist_items.checklist_id = checklists.id
        WHERE 
            checklist_items.id = $1`,
        [sourceItemId]
    );

    if (!sourceResult.rows.length) {
        throw {
            err: 'Source checklist item not found',
            status: 400,
            ECODE: 'CHECK_048'
        };
    }

    const { resolved, task_id, workspace_id } = sourceResult.rows[0];

    // Update target checklist item state
    const now = new Date().getTime();
    const result = await db.queryAsync(
        `UPDATE task_mgmt.checklist_items 
        SET resolved = $1 
        WHERE id = $2 
        RETURNING *`,
        [resolved, targetItemId]
    );

    if (!result.rows.length) {
        throw {
            err: 'Target checklist item not found',
            status: 400,
            ECODE: 'CHECK_048'
        };
    }

    // Add history record
    await db.queryAsync(
        `INSERT INTO task_mgmt.task_history(
            task_id, 
            field, 
            date, 
            before, 
            after, 
            userid, 
            data
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
            task_id,
            'checklist_item_resolved',
            now,
            !resolved,
            resolved,
            userid,
            { checklist_item_id: targetItemId }
        ]
    );

    // Update version
    const ovm = getObjectVersionManager();
    await ovm.updateVersion({
        object_id: task_id,
        object_type: ObjectType.TASK,
        operation: OperationType.UPDATE,
        workspace_id,
        data: {
            context: {
                ws_key: options.ws_key
            }
        }
    });

    // Send WebSocket notification
    sqsWs.sendWSMessage('sendChecklistEdited', [userid, result.rows[0].checklist_id, { ws_key: options.ws_key }]);
} 