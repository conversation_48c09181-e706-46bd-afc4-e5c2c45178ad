import config from 'config';

import type { OcmListCacheFactoryConfig } from '@clickup/hierarchy/list';
import { FeatureFlag } from '../feature-flag.enum';
import type { TraceSamplingFeatureFlagConfig } from '../interfaces/TraceSamplingFeatureFlag';
import { createJsonSplitTreatment, createSplitTreatment } from '../splitTreatment';

const REGION_ATTRIBUTES = {
    region: config.get<string>('region'),
};

export const useOvmFanoutForHierarchy = createSplitTreatment('hierarchy-use-ovm-fanout', REGION_ATTRIBUTES);

export const shouldLimitSubcatMembers = createSplitTreatment('hierarchy-should-limit-subcat-members');

export const shouldCacheSimpleHierarchy = createSplitTreatment('hierarchy-should-cache-simple-hierarchy');
export const shouldCacheSimpleHierarchyForWS = createSplitTreatment('hierarchy-should-cache-simple-hierarchy-ws');

export const shouldUseReplicaForStatusMiddleware = createSplitTreatment(
    'hierarchy-should-use-replica-for-status-middleware'
);

export const useAsyncIteratorHierarchy = createSplitTreatment('hierarchy-use-async-iterator');

export const shouldUpdateChangeDetectorQueueAfterEditCustomPrefix = createSplitTreatment(
    'should-update-change-detector-queue-after-edit-custom-prefix'
);

export const shouldUpdateTaskVersionsOnCustomPrefixChange = createSplitTreatment(
    'should-update-task-versions-on-custom-prefix-change'
);

export const getHierarchyTraceSamplingStrategyConfig = createJsonSplitTreatment<TraceSamplingFeatureFlagConfig>({
    flag: FeatureFlag.HierarchyTraceRetentionStrategy,
    defaultTreatment: {
        samplingRate: 0,
        features: {},
        routes: {},
        users: {},
        workspaces: {},
    },
});

export const shouldUseOptimizedMemberHierarchyProjectQuery = createSplitTreatment(
    'should-use-optimized-member-hierarchy-project-query'
);

export const shouldUseOptimizedMemberHierarchySubCategoryQuery = createSplitTreatment(
    'should-use-optimized-member-hierarchy-subcategory-query'
);

export const shouldUseGetMembersCTE = createSplitTreatment(FeatureFlag.ShouldUseGetMembersCTE);

export const shouldUseGetSubcategoryTimeSpentCTE = createSplitTreatment(
    FeatureFlag.ShouldUseGetSubcategoryTimeSpentCTE
);

export const shouldUseHierarchyV3CoreClient = createSplitTreatment('should-use-hierarchy-v3-core-client');

interface StrictFollowerPolicyConfig {
    strictViewsPolicy: boolean;
    strictSubcategoriesPolicy: boolean;
    strictCategoriesPolicy: boolean;
    strictProjectsPolicy: boolean;
}

/**
 * @description
 * A temporary flag used to fix a PII Leakage reported by the client.
 * https://staging.clickup.com/t/8x8uvdgk2
 */
export const strictFollowerPolicyConfig: (workspaceId: number | string) => StrictFollowerPolicyConfig =
    createJsonSplitTreatment<StrictFollowerPolicyConfig>({
        flag: FeatureFlag.StrictFollowerPolicy,
        defaultTreatment: {
            strictViewsPolicy: false,
            strictSubcategoriesPolicy: false,
            strictCategoriesPolicy: false,
            strictProjectsPolicy: false,
        },
    });

export const hierarchySubFoldersEnabledWorkspace: (workspaceId: number) => boolean = createSplitTreatment(
    FeatureFlag.HierarchySubFoldersEnabledWorkspace
);

export const hierarchyAllowDuplicateFolderNames = createSplitTreatment(FeatureFlag.HierarchyAllowDuplicateFolderNames);

export const hierarchyShouldReturnTemplateName = createSplitTreatment(FeatureFlag.HierarchyShouldReturnTemplateName);

export const hierarchySubfolderLimit = (workspaceId: number) =>
    createJsonSplitTreatment<{ limit: number }>({
        flag: FeatureFlag.HierarchySubfolderNestingLimit,
        defaultTreatment: {
            limit: 1,
        },
    })(workspaceId);

/**
 * Configuration for Egress authz query refactors.
 * We should follow a structure of a key representing a file with
 * boolean sub-keys representing individual queries:
 */
export interface RefactorHierarchyEgressQueriesConfig {
    debug?: boolean;
    // src/models/project/factories/memberHierarchy/getTaskMembersQuery.ts
    project_member_hierarchy?: {
        task_members?: boolean;
    };
    categories?: {
        get_subcategories_by_category_proxy?: boolean;
        accessible_project_categories?: boolean;
        get_categories?: boolean;
        get_order_index?: boolean;
    };
    subcategories?: {
        get_subcategories?: boolean;
        deleted_subcategories?: boolean;
        search_subcategories?: boolean;
        get_recent_subcategories?: boolean;
        get_order_index?: boolean;
    };
    members?: {
        task_members?: boolean;
        group_members?: boolean;
    };
    public_controller?: {
        folders?: boolean; // src/models/public_api/controllers/folder.js
        list?: boolean; // src/models/public_api/controllers/list.js
        shared?: boolean; // src/models/public_api/controllers/team.js (shared hierarchy)
    };
    statuses?: boolean;
    projects?: {
        get_batched_grouped_project_statuses?: boolean;
        get_grouped_project_statuses?: boolean;
        get_accessible_category_subcategories?: boolean;
        get_features?: boolean;
    };
    notifications?: {
        get_task_ids?: boolean;
        get_parent_followers_for_subcategory?: boolean;
    };
}

/**
 * Configuration flag to enable individual query refactors
 * https://staging.clickup.com/t/333/CLK-445702
 */
export const refactorHierarchyEgressQueries = createJsonSplitTreatment<RefactorHierarchyEgressQueriesConfig>({
    flag: FeatureFlag.RefactorHierarchyEgressQueries,
    attributes: REGION_ATTRIBUTES,
    defaultTreatment: {},
});

export const updateSubcategoryPositionsOnCopy: (workspaceId: number | string) => boolean = createSplitTreatment(
    FeatureFlag.UpdateSubcategoryPositionsOnCopy
);

export const creatorAccessOnEntityMove = (workspaceId: number | string) =>
    createJsonSplitTreatment<{
        category: boolean;
        subcategory: boolean;
        task: boolean;
    }>({
        flag: FeatureFlag.HierarchyCreatorAccessOnEntityMove,
        defaultTreatment: {
            category: false,
            subcategory: false,
            task: false,
        },
    })(workspaceId);

export const shouldUseSameClientForProjectCreation: (workspaceId: number | string) => boolean = createSplitTreatment(
    FeatureFlag.HierarchyShouldUseSameClientForProjectCreation
);

export const useListOcmFactory: (workspaceId: number | string) => OcmListCacheFactoryConfig =
    createJsonSplitTreatment<OcmListCacheFactoryConfig>({
        flag: FeatureFlag.HierarchyUseOcmListCacheFactory,
        defaultTreatment: {
            get_task_count_for_lists: false,
            get_updated_task_count_for_lists: false,
            get_lists_by_ids: false,
            get_points_count_for_lists: false,
            get_time_estimates_for_lists: false,
        },
    });

export const shouldUseUpdatedArchiveSprintQuery = createSplitTreatment(
    FeatureFlag.HierarchyShouldUseUpdatedArchiveSprintQuery
);

export const listLimitPerSpaceExemptions: (workspaceId: number | string) => { [key: number]: number } =
    createJsonSplitTreatment({
        flag: FeatureFlag.HierarchyListLimitPerSpaceExemptions,
        defaultTreatment: {},
    });

export const hierarchyShouldSkipMembersData: (workspaceId: number) => boolean = createSplitTreatment(
    FeatureFlag.HierarchyShouldSkipMembersData
);

export const hierarchyShouldSkipGroupMembersData: (workspaceId: number) => boolean = createSplitTreatment(
    FeatureFlag.HierarchyShouldSkipGroupMembersData
);

type SprintJobBatchSizeConfig = {
    mark_sprints_done: {
        read: number;
        write: number;
    };
    lock_sprints_forecast: {
        read: number;
        write: number;
    };
    lock_sprints_forecast_past_sprints: {
        read: number;
        write: number;
    };
};

export const hierarchySprintJobBatchSizes = createJsonSplitTreatment<SprintJobBatchSizeConfig>({
    flag: FeatureFlag.HierarchySprintJobBatchSizes,
    defaultTreatment: {
        mark_sprints_done: {
            read: 1000,
            write: 1000,
        },
        lock_sprints_forecast: {
            read: 1000,
            write: 1000,
        },
        lock_sprints_forecast_past_sprints: {
            read: 1000,
            write: 1000,
        },
    },
});

export const shouldCollectPayloadMetrics = createSplitTreatment(FeatureFlag.ShouldCollectPayloadMetrics);

export const useNewCategoryPosition: (workspaceId: number | string) => boolean = createSplitTreatment(
    FeatureFlag.HierarchyUseNewCategoryPosition
);

export const shouldNotUseReplicaForCategoryStatusRead: () => boolean = createSplitTreatment(
    FeatureFlag.ShouldNotUseReplicaForCategoryStatusRead
);

export const shouldUseDuplicationPaywallChecks = createSplitTreatment('hierarchy-use-duplication-paywall-checks');
