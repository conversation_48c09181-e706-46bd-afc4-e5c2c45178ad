import { ObjectType, OperationType } from '@time-loop/ovm-object-version';

import { PersonalListError } from '@clickup-legacy/models/personalList/constants';
import { getPersonalListHierarchyIds, localizePersonalListNames } from '@clickup-legacy/models/personalList/repository';
import { getEgressAccessInfoService } from '@clickup-legacy/utils/access/services/authorization/instances';
import * as cacheInvalidation from '@clickup-legacy/libs/cache/invalidateIdsAndCounts';

import { HierarchyPermissionLevel } from '@clickup/utils/authorization/models';
import { CustomType } from '@clickup/task/extensions';
import { generateNewLineOp } from '@clickup/quill';
import { TransactionClientImpl } from '@clickup/object-version-transaction-client';
import { queryAsyncWithPGClient } from '@clickup/utils/db-lite';
import { updateCanonicalChatViews } from '@clickup-legacy/models/views/services/updateViewService';
import { taskPropertiesEnabled } from '@clickup-legacy/models/integrations/split/squadTreatments/fieldTreatments';
import { ParentType } from '@clickup/field/domain-model';
import { getTaskPropertyApiClientInstance } from '@clickup-legacy/clients/TaskPropertyClient';
import { getUserFromDecodedToken } from '@clickup-legacy/utils/auth/services/userIdProvider';
import { getWorkspaceIdFromContext } from '@clickup/data-platform/context';
import config from 'config';
import moment from 'moment-timezone';
import uuid from 'node-uuid';
import _ from 'lodash';
import { createNewSubcategoryId } from '@clickup/data-platform/sequences';
import { getLogger } from '@clickup/shared/utils-logging';
import { formatPersonalListName, formatPersonalListSpaceName } from '../personalList/utils';
import * as automationService from '../../automation/services/automationService';
import { coeditorClientInstance } from '../../clients/CoeditorClient';
import { hierarchyCoreClientInstance } from '../../clients/HierarchyCoreClient';
import { prepareUpdateYdocQueries } from '../../utils/content/contentQueryFactory';
import { ContentEntityType } from '../../utils/content/interfaces';
import { writeAsync } from '../../utils/db2';
import isNumeric from '../../utils/isNumeric';
import { ClickUpTracer } from '../../utils/tracer';
import { getObjectVersionManager } from '../../utils/version/ObjectVersionManagerFactory';
import { EntityType } from '../coeditor/constants';
import {
    reportAssetViewed,
    reportSubcatChanged,
    reportTasksInSubcategoryChanged,
    shouldReportAssetViewed,
} from '../elastic/services/change-detector/hooks';
import { EntitlementName, entitlementService } from '../entitlements/entitlementService';
import * as helpersV2 from '../helpers_v2';
import {
    hierarchyObjectVersionUpdateRequests,
    hierarchyTreeVersionUpdateRequests,
    userHierarchyVersionUpdateRequests,
} from '../hierarchy/factories/ovmObjectVersionFactory';
import { statusLengthLimit } from '../integrations/split/squadTreatments/fieldTreatments';
import {
    sprintCustomDuration,
    shouldUseReplicaForSubcategoryRead,
    shouldUse100PercentReplicaForSubcategoryRead,
} from '../integrations/split/squadTreatments/unclaimedTreatments';
import {
    refactorHierarchyEgressQueries,
    shouldUseHierarchyV3CoreClient,
    shouldNotUseReplicaForCategoryStatusRead,
    hierarchySubFoldersEnabledWorkspace,
} from '../integrations/split/squadTreatments/hierarchyTreatments';
import { isWriteToRecentTableDisabled } from '../integrations/split/squadTreatments/userPlatformTreatments';
import { isDefaultLocationPermissionsEnabled } from '../integrations/split/squadTreatments/accessManagementTreatments';
import { prepareMarkTaskForDeletionLogQuery } from '../task/factories/taskHistoryQueryFactory';
import { getPinnedTemplatesMappingForSubcategories } from '../templates/pinnedTemplates/subcategories';
import { getPresetTaskTemplate } from '../templates/presetTaskTemplates/repository';
import { unParentChildDocs } from '../views/documents/helpers/documentHelpers';
import { getTemplateNameAndIdByPermanentId } from '../templates/templateIdHelpers';
import { findParentWithProperty } from '../category/services/subfolderService';
import { getUsersInfo } from '../user/datastores/CRUD/getUsers';
import { StatusErrors } from '../../utils/errors/constants';
import { generateStatusId } from '../statuses/services/statusIdProvider';
import { getSubCategoriesQueryObjectFactory } from './datastores/query-objects/get-subcategories-query-object.factory';
import { isLimitedMemberEnabledAsync } from '../team/helpers/roleValidationHelper';
import { buildListChanges } from './services/buildListChanges';
import * as sqsNotif from '../../utils/sqs-notif';
import * as sqsWs from '../../utils/v1-ws-message-sending'; // eslint-disable-line import/no-cycle
import { assureStatusColor } from '../status/status.utils';

import { getGroupsInfo } from '../groups/datastores/groupsDatastore';
import * as async from '../../utils/asyncHelper';
import * as access2 from '../../utils/access2.js';
import * as modelHelpers from '../helpers';
import * as cf_sign from '../../utils/cf_sign';
import * as db from '../../utils/db';
import * as encrypt from '../../utils/encrypt';
import * as input from '../../utils/input_validation.js';
import * as quill from '../../utils/quill';
import * as sprintUtil from '../../utils/sprint';
import * as userInitials from '../../utils/userInitials';
import * as webhook from '../../utils/webhook';
import * as json from '../../utils/json';
import * as taskTemplate from '../templates/taskTemplate';
import * as attachmentsMod from '../attachment/attachment';
import * as deleteViewService from '../views/services/deleteViewService';
import * as categoryMod from '../category/CRUD';
import * as fieldMod from '../field/CRUD/CRUD';
import * as emailHelper from '../email/helpers';
import * as privacy from '../privacy';
import * as projectHelper from '../project/helpers.js';
import * as portfolioPaywall from '../portfolio/paywall';
import * as gantt from '../gantt';
import * as subcatMember from './subcatMember.js';
import * as subcatFollowers from '../follower/services/subcatFollowerService';
import * as subcatStatuses from './subcatStatuses';
import * as statusMod from '../status';
import * as teamMemberMod from '../team/teamMember';
import * as billingNewUsersMod from '../billing/newUsers';
import * as subcatUndoMod from './subcatUndo';
import * as catUndoMod from '../category/catUndo';
import * as viewMod from '../views/views';
import * as statusIDCorrection from '../task/statusIDCorrection';
import * as elasticProducer from '../elastic/producer';
import * as customItemMod from '../customItems.js';
import * as getItemsMod from '../task/CRUD/getTask/get-items';
import * as pointsMod from '../pointsEstimate';
import * as subcatCopy from './subcatCopy';
import * as genericCopy from '../genericCopy';
import * as automationHelpers from '../../automation/helpers';
import * as customItemsMod from '../custom_items/customItems';
import { getTeamId } from '../../utils/entities/services/entitiesService';
import { ClickUpError } from '../../utils/errors';
import { batchQueriesSeriesAsyncThenUpdateTasksWithClient } from '../task/helpers/versionUpdateHelper';
import { checkListLimit } from '../hierarchy/services/hierarchyLimitService';
import { batchQueriesSeriesAsyncThenUpdateHierarchy } from '../hierarchy/helpers/hierarchyUpdateHelper';
import { getPreferredLanguage } from '../../utils/i18n';

import {
    isMemberInsertionSkippedForSubcategoryType,
    isSubcategoryTypePickableOnlyByClickbot,
    SubcategoryType,
} from './interfaces/SubcategoryType';

import { upsertObjectAccessInfoQueryObject } from '../../utils/access/datastores/objectAccessInfoDatastore';
import {
    isValidDefaultPermissionLevel,
    fetchDefaultPermissionLevel,
} from '../../utils/access/services/defaultPermissionLevel';

import {
    uuidValidate,
    getSubcategoryTaskCount,
    getSubcategoryAttachmentCount,
    getSubcategoryTimeSpent,
    getSubcategoryTimeEstimate,
    getAllSubcategoriesForTIML,
    invalidateCachedSubcatTaskCount,
} from './helpers';

import {
    checkGranularDefaultPermissionLevelPaywall,
    verifyListUsage,
} from '../hierarchy/services/hierarchyPaywallService';
import { undoDeleteCategory } from '../category/categoryUndo';

const logger = getLogger('subcategory');

const SubcatError = ClickUpError.makeNamedError('subcategory');

const tracer = new ClickUpTracer();

const {
    can_create_lists,
    can_read,
    archive: can_archive,
    edit_list_details,
    can_edit_privacy,
    remove_status,
    add_status,
    manage_statuses,
    can_convert_item,
    change_title,
    can_set_default_permission_level,
} = config.permission_constants;

const { parent_types } = config.views;

const { subcategory: entityType } = config.parent_types;

async function checkForDuplicateOrderindexes(category_id, subcats) {
    let indexes = subcats.map(row => row.orderindex);
    indexes = indexes.filter((i, idx) => indexes.indexOf(i) === idx);
    if (indexes.length !== subcats.length) {
        const query = `
            UPDATE task_mgmt.subcategories
            SET    orderindex = q2.true_orderindex
            FROM   (SELECT subcategories.id,
                        ( Row_number()
                            OVER (
                                partition BY category
                                ORDER BY subcategories.orderindex) ) - 1 AS true_orderindex
                    FROM
                        task_mgmt.subcategories
                    WHERE  subcategories.category = $1
                        AND subcategories.deleted = false
                        AND subcategories.template = false
                    ORDER  BY subcategories.orderindex) AS q2
            WHERE  subcategories.id = q2.id
            RETURNING subcategories.id as updated_subcategory_id, subcategories.workspace_id
        `;
        const params = [category_id];
        try {
            await batchQueriesSeriesAsyncThenUpdateHierarchy([{ query, params }]);
        } catch (err) {
            logger.error({ msg: 'Failed to update subcategory order indexes', err });
        }
    }
}

function _getSubcategoriesPage(userid, options, cb) {
    const { category_ids, portfolio_id, virtual_portfolio, exclude_archived_fields = false } = options;
    let { subcategory_ids } = options;
    async.series(
        [
            series_cb => {
                if (options.skipAccess || !category_ids) {
                    series_cb();
                    return;
                }

                access2.checkAccessCategories(
                    userid,
                    category_ids,
                    { permissions: [can_read], checkJoined: options.checkJoined },
                    series_cb
                );
            },
        ],
        async err => {
            if (err) {
                cb(new SubcatError(err, 'SUBCAT_064'));
                return;
            }

            if (options.filter_empty === 'false') {
                options.filter_empty = false;
            }
            if (options.comments === 'false') {
                options.comments = false;
            }
            if (options.subtasks === 'false') {
                options.subtasks = false;
            }
            if (options.checklists === 'false') {
                options.checklists = false;
            }

            if (!category_ids && !subcategory_ids && !portfolio_id) {
                cb(new SubcatError('ID Filter required', 'SUBCAT_083'));
                return;
            }

            if (refactorHierarchyEgressQueries().subcategories?.get_subcategories) {
                const clickbotUserId = config.get('clickbot_assignee');
                if (!userid || userid === clickbotUserId) {
                    logger.warn({
                        msg: 'No user id provided or clickbot user id, this uses non egress query',
                        userid,
                    });
                }
            }

            try {
                if (!options.workspace_id) {
                    if (category_ids) {
                        if (category_ids.length > 0) {
                            options.workspace_id = await getTeamId({ category_ids });
                        } else {
                            cb(null, { subcategory_list: [], subcategories: [] });
                            return;
                        }
                    } else if (portfolio_id) {
                        options.workspace_id = await getTeamId({ portfolio_ids: [portfolio_id] });
                    } else if (subcategory_ids) {
                        if (subcategory_ids.length > 0) {
                            options.workspace_id = await getTeamId({ subcategory_ids });
                        } else {
                            cb(null, { subcategory_list: [], subcategories: [] });
                            return;
                        }
                    } else {
                        logger.warn({ msg: 'workspace not found', options });
                    }
                }
            } catch (workspaceErr) {
                logger.error({ msg: 'Failed to get workspace id', workspaceErr });
                cb(new SubcatError(workspaceErr, 'SUBCAT_130'));
                return;
            }

            let isLimitedMemberEnabled = false;
            try {
                isLimitedMemberEnabled = await isLimitedMemberEnabledAsync(options.workspace_id);
            } catch (lmErr) {
                cb(lmErr);
                return;
            }
            let queryObj = {};
            try {
                const getCategoriesQueryFactory = getSubCategoriesQueryObjectFactory();
                queryObj = await getCategoriesQueryFactory.buildQueryObject({
                    ...options,
                    userId: userid,
                    workspaceId: options.workspace_id,
                    isLimitedMemberEnabled,
                });
            } catch (egressErr) {
                logger.error({
                    msg: 'Failed to build query object',
                    error: egressErr,
                    split: refactorHierarchyEgressQueries(),
                    workspaceId: options.workspace_id,
                    category_ids,
                    subcategory_ids,
                    portfolio_id,
                    userid,
                });
                cb(egressErr);
                return;
            }

            const use_replica = shouldUseReplicaForSubcategoryRead() && !options.use_master;
            const replica_percent = shouldUse100PercentReplicaForSubcategoryRead() ? 1 : undefined;
            const { query } = queryObj;
            const { params } = queryObj;
            db.readQuery(query, params, { use_replica, replica_percent }, (queryErr, result) => {
                if (queryErr) {
                    cb(new SubcatError(queryErr, 'SUBCAT_001'));
                    return;
                }

                subcategory_ids = result.rows.map(row => row.id);
                const subcategories = result.rows;
                const time_spent_ids = result.rows.filter(row => row.can_see_time_spent).map(row => row.id);
                const time_estimated_ids = result.rows.filter(row => row.can_see_time_estimated).map(row => row.id);
                const points_estimate_ids = result.rows
                    .filter(row => row.can_see_points_estimated && row.points)
                    .map(row => row.id);
                const found_categories = result.rows.map(row => row.category);
                const found_projects = result.rows.map(row => row.project_id);

                // Not getting the subcat calculations because of outage: https://staging.clickup.com/t/333/CLK-64916
                const skip_subcategory_calculations = ['3357294'].includes(String(subcategories[0]?.team_id));
                const customTaskTypes = {};

                async.parallel(
                    {
                        async automationCount(para_cb) {
                            let triggerCount;
                            try {
                                triggerCount = await automationService.getTriggerCountByParents(
                                    subcategory_ids,
                                    6,
                                    true
                                );
                            } catch (err1) {
                                para_cb(new SubcatError(err1, 'SUBCAT_134'));
                                return;
                            }
                            const count_map = {};
                            triggerCount.forEach((count, parentId) => {
                                count_map[parentId] = count;
                            });

                            para_cb(null, count_map);
                        },

                        taskCount(para_cb) {
                            if (
                                (options.simple && !(options.gantt || options.subcategory_task_count)) ||
                                skip_subcategory_calculations
                            ) {
                                para_cb(null, {});
                                return;
                            }
                            options.all_statuses = true;
                            getSubcategoryTaskCount(userid, options.workspace_id, subcategory_ids, options, para_cb);
                        },

                        hasAttachments(para_cb) {
                            if (options.simple) {
                                para_cb();
                                return;
                            }
                            getSubcategoryAttachmentCount(subcategory_ids, options, para_cb);
                        },

                        timeSpent(para_cb) {
                            if (
                                options.skip_time_spent ||
                                (options.simple && !options.gantt) ||
                                skip_subcategory_calculations
                            ) {
                                para_cb(null, {});
                                return;
                            }
                            getSubcategoryTimeSpent(time_spent_ids, options, para_cb);
                        },

                        async timeEstimate(para_cb) {
                            if (
                                options.skip_time_estimate ||
                                (options.simple && !options.gantt) ||
                                skip_subcategory_calculations
                            ) {
                                para_cb(null, {});
                                return;
                            }
                            await getSubcategoryTimeEstimate(
                                time_estimated_ids,
                                options.workspace_id,
                                options,
                                para_cb
                            );
                        },

                        members(para_cb) {
                            if (options.simple || options.skip_members_data) {
                                para_cb(null, { members: {} });
                                return;
                            }
                            subcatMember._getMembers(subcategory_ids, options, para_cb);
                        },

                        async groupMembers(para_cb) {
                            if (options.simple || options.skip_group_members_data) {
                                para_cb(null, {});
                                return;
                            }

                            try {
                                const grp_result = await subcatMember._getGroupMembers(subcategory_ids);
                                para_cb(null, grp_result);
                            } catch (e) {
                                para_cb(e);
                            }
                        },
                        async assignees(para_cb) {
                            if (!virtual_portfolio && !portfolio_id) {
                                para_cb();
                                return;
                            }
                            try {
                                const resultAssignees = await db.promiseReadQuery(
                                    'SELECT user_id, subcategory_id FROM task_mgmt.subcategory_assignee WHERE subcategory_id = ANY($1)',
                                    [subcategory_ids]
                                );

                                if (resultAssignees.rows.length === 0) {
                                    para_cb();
                                    return;
                                }

                                const user_ids = resultAssignees.rows.map(({ user_id }) => user_id);
                                const users = await getUsersInfo(
                                    user_ids,
                                    ['id', 'email', 'username', 'color', 'profile_picture_key'],
                                    true,
                                    true
                                );
                                const usersObj = _.keyBy(users.rows, 'id');

                                const subcategoryAssignees = {};

                                resultAssignees.rows.forEach(({ user_id, subcategory_id }) => {
                                    if (!subcategoryAssignees[subcategory_id]) {
                                        subcategoryAssignees[subcategory_id] = [];
                                    }
                                    subcategoryAssignees[subcategory_id].push(usersObj[user_id]);
                                });
                                para_cb(null, subcategoryAssignees);
                            } catch (e) {
                                para_cb(e);
                            }
                        },
                        async groupAssignees(para_cb) {
                            if (!virtual_portfolio && !portfolio_id) {
                                para_cb();
                                return;
                            }
                            try {
                                const resultGroupAssignees = await db.promiseReadQuery(
                                    'SELECT group_id, subcategory_id FROM task_mgmt.subcategory_group_assignee WHERE subcategory_id = ANY($1)',
                                    [subcategory_ids]
                                );

                                if (resultGroupAssignees.rows.length === 0) {
                                    para_cb();
                                    return;
                                }

                                const group_ids = resultGroupAssignees.rows.map(({ group_id }) => group_id);
                                const groups = await getGroupsInfo(group_ids, ['id', 'color', 'name']);

                                const groupsObj = _.keyBy(groups.rows, 'id');
                                const subcategoryGroups = {};

                                resultGroupAssignees.rows.forEach(({ group_id, subcategory_id }) => {
                                    if (!subcategoryGroups[subcategory_id]) {
                                        subcategoryGroups[subcategory_id] = [];
                                    }
                                    subcategoryGroups[subcategory_id].push(groupsObj[group_id]);
                                });

                                para_cb(null, subcategoryGroups);
                            } catch (e) {
                                para_cb(e);
                            }
                        },
                        incomingAddresses(para_cb) {
                            emailHelper.getSubcatAddresses(userid, subcategory_ids, {}, para_cb);
                        },

                        permissions(para_cb) {
                            if (options.simple && !options.gantt && !options.include_permissions) {
                                para_cb();
                                return;
                            }
                            access2.checkAccessSubcategories(
                                userid,
                                subcategory_ids,
                                {
                                    permissions: [],
                                    simple_permissions: options.simple_permissions,
                                    dontFail: options.dontFail,
                                    checkJoined: options.checkJoined,
                                },
                                (err2, permissions) => {
                                    if (err2) {
                                        para_cb(err2);
                                    } else {
                                        para_cb(null, permissions);
                                    }
                                }
                            );
                        },

                        features(para_cb) {
                            if (options.simple) {
                                para_cb();
                                return;
                            }
                            projectHelper.getFeaturesBySubcategories(subcategory_ids, para_cb);
                        },

                        statuses(para_cb) {
                            if (!options.include_statuses) {
                                para_cb();
                                return;
                            }

                            statusMod._getStatusesBySubcategories(
                                subcategory_ids,
                                {
                                    replica: options.fresh_statuses != null ? !options.fresh_statuses : true,
                                    exclude_multiple_lists: options.exclude_multiple_lists || false,
                                    exclude_sub_multiple_lists: options.exclude_sub_multiple_lists || false,
                                },
                                para_cb
                            );
                        },

                        async pointsEstimate(para_cb) {
                            const { team_id } = subcategories[0] || {};

                            const shouldCalcSubcatPoints = options.include_points;
                            if (
                                !points_estimate_ids.length ||
                                !subcategories.length ||
                                skip_subcategory_calculations ||
                                !shouldCalcSubcatPoints
                            ) {
                                para_cb(null, {});
                                return;
                            }

                            const parent_type = config.views.parent_types.subcategory;

                            try {
                                const results = await pointsMod.getPointsCount(
                                    userid,
                                    points_estimate_ids,
                                    parent_type,
                                    {
                                        team_id,
                                        req_from: 'get subcat page',
                                    }
                                );

                                para_cb(null, results);
                            } catch (e) {
                                para_cb(e);
                            }
                        },
                        async milestones(para_cb) {
                            if (!portfolio_id && !virtual_portfolio) {
                                para_cb();
                                return;
                            }

                            const team_id = options.workspace_id ?? subcategories[0]?.team_id;

                            const milestone = Object.keys(config.custom_item_types).find(
                                key => config.custom_item_types[key] === 'milestone'
                            );

                            const _options = {
                                custom_type: milestone,
                                team_id,
                            };

                            const parent_type = config.views.parent_types.subcategory;

                            let results;
                            try {
                                results = await customItemMod.getCustomTypesCount(
                                    userid,
                                    subcategory_ids,
                                    parent_type,
                                    _options
                                );
                            } catch (e) {
                                para_cb(e);
                                return;
                            }

                            let all_item_ids = [];
                            Object.keys(results).forEach(key => {
                                all_item_ids = all_item_ids.concat(results[key].item_ids);
                                delete results[key].item_ids;
                            });

                            let items;
                            if (all_item_ids && all_item_ids.length) {
                                try {
                                    items = await getItemsMod._promiseGetItems(all_item_ids, { fields: [], userid });
                                } catch (e) {
                                    para_cb(e);
                                    return;
                                }
                            }

                            if (items && items.length) {
                                Object.keys(results).forEach(key => {
                                    results[key].items = items.filter(
                                        item =>
                                            // main subcategory parent match
                                            Number(item.subcategory.id) === Number(key) ||
                                            // TIML subcategory parent match
                                            item.other_subcategories.find(x => Number(x.id) === Number(key))
                                    );
                                });
                            }

                            para_cb(null, results);
                        },
                        category_permissions(para_cb) {
                            if (!options.include_categories) {
                                para_cb();
                                return;
                            }

                            if (!found_categories.length) {
                                para_cb(null, {});
                                return;
                            }

                            access2.checkAccessCategories(
                                userid,
                                found_categories,
                                { permissions: [], dontFail: true },
                                (err2, permissions) => {
                                    if (err2) {
                                        para_cb(err2);
                                    } else {
                                        para_cb(null, permissions);
                                    }
                                }
                            );
                        },
                        project_permissions(para_cb) {
                            if (!options.include_projects) {
                                para_cb();
                                return;
                            }

                            if (!found_projects.length) {
                                para_cb(null, {});
                                return;
                            }

                            access2.checkAccessProjects(
                                userid,
                                found_projects,
                                { permissions: [], dontFail: true },
                                (err2, permissions) => {
                                    if (err2) {
                                        para_cb(err2);
                                    } else {
                                        para_cb(null, permissions);
                                    }
                                }
                            );
                        },
                        async followers(para_cb) {
                            if (!options.include_followers) {
                                para_cb();
                                return;
                            }
                            try {
                                const results = await subcatFollowers.getFollowersAsync(subcategory_ids);
                                para_cb(null, results);
                            } catch (e) {
                                para_cb(e);
                            }
                        },
                        subcatStatusesByTeam(para_cb) {
                            const status_options = category_ids ? { category_ids } : { subcategory_ids };

                            subcatStatuses.getSubcategoryStatusesByIDs(status_options, (err1, statuses) => {
                                if (err1) {
                                    para_cb(new SubcatError(err1, 'SUBCAT_107'));
                                } else {
                                    para_cb(null, statuses);
                                }
                            });
                        },
                        commentCount(para_cb) {
                            if (!options.comment_count) {
                                para_cb(null);
                                return;
                            }

                            db.readQuery(
                                'SELECT count(*), parent FROM task_mgmt.comments WHERE parent = ANY($1) AND type = $2 AND deleted = false GROUP BY parent',
                                [subcategory_ids, config.comments.types.subcategory],
                                (commentCountErr, countResult) => {
                                    if (commentCountErr) {
                                        para_cb(commentCountErr);
                                    } else {
                                        const count_map = {};
                                        countResult.rows.forEach(row => {
                                            count_map[row.parent] = row.count;
                                        });

                                        subcategories.forEach(subcategory => {
                                            subcategory.comment_count = count_map[subcategory.id] || '0';
                                        });

                                        para_cb();
                                    }
                                }
                            );
                        },
                        task_status_counts(para_cb) {
                            if (!portfolio_id && !virtual_portfolio) {
                                para_cb();
                                return;
                            }

                            let status_count_query = `
                                SELECT
                                    items.subcategory,
                                    array_agg(items.status) AS task_statuses
                                FROM task_mgmt.items
                                WHERE 
                                    items.subcategory = ANY ($1)
                                    ${options.workspace_id ? `AND items.workspace_id = $2` : ''}
                                    AND items.deleted = FALSE
                                    AND items.template = FALSE
                                    AND (items.importing = FALSE OR items.importing IS NULL)
                                    ${options.include_archived ? `` : `AND items.archived = FALSE`}
                                    ${options.include_subtasks ? `` : ` AND items.parent IS NULL`}
                                GROUP BY items.subcategory`;

                            if (!options.exclude_multiple_lists || !options.exclude_sub_multiple_lists) {
                                status_count_query += `
                                    UNION ALL
                                    SELECT
                                        task_subcategories.subcategory,
                                        array_agg(items.status) AS task_statuses
                                    FROM task_mgmt.items
                                        INNER JOIN task_mgmt.task_subcategories
                                            ON task_subcategories.task_id = items.id
                                            AND task_subcategories.subcategory != items.subcategory
                                    WHERE 
                                        task_subcategories.subcategory = ANY ($1)
                                        ${options.workspace_id ? `AND items.workspace_id = $2` : ''}
                                        AND items.deleted = FALSE
                                        AND items.template = FALSE
                                        AND (items.importing = FALSE OR items.importing IS NULL)
                                        ${options.include_archived ? `` : `AND items.archived = FALSE`}
                                        ${
                                            options.include_subtasks
                                                ? ``
                                                : helpersV2.getConditionForMultipleLists(
                                                      !options.exclude_multiple_lists,
                                                      !options.exclude_sub_multiple_lists
                                                  )
                                        }
                                    GROUP BY task_subcategories.subcategory
                                    `;
                            }
                            const status_count_params = [subcategory_ids];
                            if (options.workspace_id) {
                                status_count_params.push(options.workspace_id);
                            }

                            db.readQuery(status_count_query, status_count_params, (err1, result1) => {
                                if (err1) {
                                    para_cb(new SubcatError(err1, 'SUBCAT_108'));
                                    return;
                                }

                                const counts = {};
                                result1.rows.forEach(row => {
                                    if (!counts[row.subcategory]) {
                                        counts[row.subcategory] = {
                                            subcategory: row.subcategory,
                                            counts: {},
                                        };
                                    }
                                    row.task_statuses.forEach(status => {
                                        if (!counts[row.subcategory].counts[status]) {
                                            counts[row.subcategory].counts[status] = 0;
                                        }

                                        counts[row.subcategory].counts[status] += 1;
                                    });
                                });

                                para_cb(null, Object.values(counts));
                            });
                        },
                        team_fields(para_cb) {
                            if (!portfolio_id && !virtual_portfolio) {
                                para_cb();
                                return;
                            }

                            const { team_id } = subcategories[0] || {};

                            if (!team_id) {
                                para_cb();
                                return;
                            }

                            const field_query = `
                                SELECT
                                    subcategories.id,
                                    array_agg(DISTINCT field_parents.field_id) AS field_ids
                                FROM task_mgmt.subcategories
                                INNER JOIN task_mgmt.categories
                                    ON subcategories.category = categories.id
                                INNER JOIN task_mgmt.projects
                                    ON categories.project_id = projects.id
                                INNER JOIN task_mgmt.field_parents
                                    ON (
                                        (field_parents.parent_type = 4 AND field_parents.parent_id = projects.id)
                                        OR (field_parents.parent_type = 5 AND field_parents.parent_id = categories.id)
                                        OR (field_parents.parent_type = 6 AND field_parents.parent_id = subcategories.id)
                                        OR (field_parents.parent_type = 7 AND field_parents.parent_id = projects.team)
                                    )
                                INNER JOIN task_mgmt.fields
                                    ON fields.id = field_parents.field_id
                                WHERE subcategories.id = ANY ($1)
                                    AND fields.deleted = FALSE
                                GROUP BY subcategories.id`;

                            db.replicaQuery(field_query, [subcategory_ids], (err1, result1) => {
                                if (err1) {
                                    para_cb(new SubcatError(err1, 'SUBCAT_118'));
                                    return;
                                }

                                const subcat_field_map = {};
                                const field_ids = [];

                                result1.rows.forEach(row => {
                                    subcat_field_map[row.id] = row.field_ids;
                                    field_ids.push(...row.field_ids);
                                });

                                fieldMod._getFields(
                                    userid,
                                    {
                                        field_ids,
                                        skipAccess: true,
                                        exclude_archived: exclude_archived_fields,
                                    },
                                    (err2, result2) => {
                                        if (err2) {
                                            para_cb(new SubcatError(err2, 'SUBCAT_130'));
                                            return;
                                        }

                                        const field_map = {};

                                        result2.fields.forEach(field => {
                                            field_map[field.id] = field;
                                        });

                                        Object.keys(subcat_field_map).forEach(subcategory_id => {
                                            const fields = subcat_field_map[subcategory_id];

                                            subcat_field_map[subcategory_id] = fields
                                                .map(field_id => field_map[field_id])
                                                .filter(Boolean);
                                        });

                                        para_cb(null, subcat_field_map);
                                    }
                                );
                            });
                        },
                        overdue(para_cb) {
                            if (!portfolio_id && !virtual_portfolio) {
                                para_cb();
                                return;
                            }

                            const start_of_day = moment()
                                .tz(options.timezone || 'UTC')
                                .startOf('day')
                                .valueOf();
                            const now = moment().valueOf();

                            let overdue_query = `
                                SELECT items.subcategory, count(*) AS tasks_overdue
                                FROM task_mgmt.items
                                INNER JOIN task_mgmt.statuses
                                    ON statuses.id = items.status_id
                                LEFT OUTER JOIN task_mgmt.items as parent ON items.parent = parent.id
                                WHERE items.template = FALSE
                                    AND items.deleted = FALSE
                                    AND statuses.type != ALL ($4)
                                    AND items.due_date IS NOT NULL
                                    AND items.subcategory = ANY($1)
                                    AND (items.importing = FALSE OR items.importing IS NULL)
                                    AND (parent.deleted = FALSE OR parent.deleted IS NULL)
									AND (parent.template = FALSE OR parent.template IS NULL)
									AND (parent.importing = FALSE OR parent.importing IS NULL)
                                    AND
                                        CASE
                                            WHEN items.due_date_time THEN items.due_date < $3
                                            ELSE items.due_date < $2
                                        END
                                ${options.include_subtasks ? `` : ` AND items.parent IS NULL`}
                                ${
                                    options.include_archived
                                        ? ``
                                        : ` AND items.archived = FALSE
                                            AND (parent.archived = FALSE OR parent.archived IS NULL)`
                                }
                                GROUP BY items.subcategory`;

                            if (!options.exclude_multiple_lists || !options.exclude_sub_multiple_lists) {
                                overdue_query += `
                                    UNION ALL
                                    SELECT task_subcategories.subcategory, count(*) AS tasks_overdue
                                    FROM task_mgmt.items
                                    INNER JOIN task_mgmt.task_subcategories
                                        ON task_subcategories.task_id = items.id
                                        AND task_subcategories.subcategory != items.subcategory
                                    INNER JOIN task_mgmt.statuses
                                        ON statuses.id = items.status_id
                                    LEFT OUTER JOIN task_mgmt.items as parent ON items.parent = parent.id
                                    WHERE  items.template = FALSE
                                        AND items.deleted = FALSE
                                        AND statuses.type != ALL ($4)
                                        AND items.due_date IS NOT NULL
                                        AND (items.importing = FALSE OR items.importing IS NULL)
                                        AND task_subcategories.subcategory = ANY($1)
                                        AND (parent.deleted = FALSE OR parent.deleted IS NULL)
                                        AND (parent.template = FALSE OR parent.template IS NULL)
                                        AND (parent.importing = FALSE OR parent.importing IS NULL)
                                        AND
                                            CASE
                                                WHEN items.due_date_time THEN items.due_date < $3
                                                ELSE items.due_date < $2
                                            END

                                    ${
                                        options.include_subtasks
                                            ? ``
                                            : helpersV2.getConditionForMultipleLists(
                                                  !options.exclude_multiple_lists,
                                                  !options.exclude_sub_multiple_lists
                                              )
                                    }
                                    ${
                                        options.include_archived
                                            ? ``
                                            : `AND items.archived = FALSE
                                            AND (parent.archived = FALSE OR parent.archived IS NULL)`
                                    }
                                    GROUP BY task_subcategories.subcategory
                                `;
                            }
                            const overdue_params = [subcategory_ids, start_of_day, now, config.done_status_types];

                            db.replicaQuery(overdue_query, overdue_params, (err1, result1) => {
                                if (err1) {
                                    para_cb(new SubcatError(err1, 'SUBCAT_110'));
                                    return;
                                }

                                const total_map = {};
                                result1.rows.forEach(row => {
                                    if (!total_map[row.subcategory]) {
                                        total_map[row.subcategory] = 0;
                                    }

                                    total_map[row.subcategory] += Number(row.tasks_overdue);
                                });

                                para_cb(null, total_map);
                            });
                        },
                        tasks_complete(para_cb) {
                            if (!portfolio_id && !virtual_portfolio) {
                                para_cb();
                                return;
                            }

                            let tasks_complete_query = `
                                SELECT items.subcategory, count(*) AS tasks_complete
                                FROM
                                    task_mgmt.items,
                                    task_mgmt.subcategories,
                                    task_mgmt.statuses
                                WHERE items.template = FALSE
                                    AND items.deleted = FALSE
                                    AND items.status_id = statuses.id
                                    AND items.subcategory = subcategories.id
                                    AND (items.importing = FALSE OR items.importing IS NULL)
                                    AND subcategories.status_group = statuses.status_group
                                    AND items.status = statuses.status
                                    AND statuses.type = ANY($2)
                                    AND items.subcategory = ANY($1)
                                    AND subcategories.id = ANY($1)
                                    ${options.include_subtasks ? `` : ` AND items.parent IS NULL`}
                                    ${
                                        options.include_archived
                                            ? ``
                                            : ` AND items.archived = FALSE
                                                                        AND subcategories.archived = FALSE`
                                    }
                            GROUP BY items.subcategory`;

                            if (!options.exclude_multiple_lists || !options.exclude_sub_multiple_lists) {
                                tasks_complete_query += `
                                    UNION ALL
                                    SELECT task_subcategories.subcategory, count(*) AS tasks_complete
                                    FROM
                                        task_mgmt.items,
                                        task_mgmt.task_subcategories,
                                        task_mgmt.statuses,
                                        task_mgmt.subcategories
                                    WHERE items.template = FALSE
                                        AND items.deleted = FALSE
                                        AND items.id = task_subcategories.task_id
                                        AND task_subcategories.subcategory != items.subcategory
                                        AND subcategories.id = items.subcategory
                                        AND subcategories.status_group = statuses.status_group
                                        AND items.status = statuses.status
                                        AND (items.importing = FALSE OR items.importing IS NULL)
                                        AND statuses.type = ANY($2)
                                        AND task_subcategories.subcategory = ANY($1)
                                        ${
                                            options.include_subtasks
                                                ? ``
                                                : helpersV2.getConditionForMultipleLists(
                                                      !options.exclude_multiple_lists,
                                                      !options.exclude_sub_multiple_lists
                                                  )
                                        }
                                        ${
                                            options.include_archived
                                                ? ``
                                                : ` AND items.archived = FALSE
                                                                            AND subcategories.archived = FALSE`
                                        }
                                GROUP BY task_subcategories.subcategory`;
                            }
                            const tasks_complete_params = [subcategory_ids, config.done_status_types];

                            db.replicaQuery(tasks_complete_query, tasks_complete_params, (err1, result1) => {
                                if (err1) {
                                    para_cb(new SubcatError(err1, 'SUBCAT_111'));
                                    return;
                                }

                                const total_map = {};
                                result1.rows.forEach(row => {
                                    if (!total_map[row.subcategory]) {
                                        total_map[row.subcategory] = 0;
                                    }

                                    total_map[row.subcategory] += Number(row.tasks_complete);
                                });

                                para_cb(null, total_map);
                            });
                        },
                        getGanttInfo(para_cb) {
                            if (!options.gantt) {
                                para_cb();
                                return;
                            }

                            gantt._getGanttPeriods(subcategory_ids, {}, (ganttErr, ganttResult) => {
                                if (ganttErr) {
                                    para_cb(ganttErr);
                                } else {
                                    subcategories.forEach(subcategory => {
                                        subcategory.gantt_start = ganttResult.gantt_start[subcategory.id];
                                        subcategory.gantt_end = ganttResult.gantt_end[subcategory.id];
                                        subcategory.gantt_progress_denominator =
                                            ganttResult.gantt_progress_denominator[subcategory.id] || '0';
                                        subcategory.gantt_progress_numerator =
                                            ganttResult.gantt_progress_numerator[subcategory.id] || '0';
                                    });
                                    para_cb();
                                }
                            });
                        },
                        async pinnedTemplatesMapping(para_cb) {
                            if (!options.include_pinned_templates) {
                                para_cb();
                                return;
                            }

                            try {
                                const mapping = await getPinnedTemplatesMappingForSubcategories({
                                    subcategoryIds: subcategory_ids,
                                });
                                para_cb(null, mapping);
                            } catch (e) {
                                para_cb(e);
                            }
                        },
                        async customTaskTypeDefaults(para_cb) {
                            const defaults = [];
                            const team_id = subcategories[0]?.team_id;
                            for (const subcat of subcategories) {
                                const customType = new CustomType(subcat.custom_items_default);
                                if (customType.isUnset() || customType.isPredefinedCustomTypeRange()) {
                                    continue;
                                }
                                defaults.push(customType.value);
                            }

                            try {
                                const { custom_items } = await customItemsMod.getCustomItemsByIds(
                                    defaults,
                                    team_id,
                                    {}
                                );

                                for (const item of custom_items) {
                                    customTaskTypes[item.id] = item;
                                }
                                para_cb();
                            } catch (e) {
                                para_cb(e);
                            }
                        },
                    },
                    async (paraErr, para_result) => {
                        if (paraErr) {
                            cb(new SubcatError(paraErr, 'SUBCAT_065').logError());
                            return;
                        }

                        const category_to_subcats = {};
                        const subcategory_list = [];
                        const task_counts = para_result.taskCount;
                        const attachment_counts = para_result.hasAttachments;
                        const { members } = para_result.members;
                        const time_spent = para_result.timeSpent;
                        const time_estimate = para_result.timeEstimate;
                        const incoming_addresses = para_result.incomingAddresses;
                        const milestone_counts = para_result.milestones;
                        const points_counts = para_result.pointsEstimate;
                        const group_assignees = para_result.groupAssignees;
                        const { assignees } = para_result;
                        const {
                            permissions,
                            features,
                            category_permissions,
                            project_permissions,
                            subcatStatusesByTeam,
                            task_status_counts,
                            overdue,
                            tasks_complete,
                            team_fields,
                            automationCount,
                            groupMembers,
                            pinnedTemplatesMapping,
                        } = para_result;

                        if (category_ids) {
                            category_ids.forEach(category_id => {
                                category_to_subcats[category_id] = [];
                            });
                        }

                        try {
                            await Promise.all(
                                subcategories.map(async row => {
                                    if (row.assignee) {
                                        row.assignee = {
                                            id: row.assignee_id,
                                            username: row.username,
                                            email: row.email,
                                            color: row.assignee_color,
                                            initials: userInitials.getInitials(row.email, row.username),
                                            profilePicture: cf_sign.getCloudfrontAvatarUrl(row.profile_picture_key),
                                        };
                                    }

                                    row.assignees = [];
                                    if (assignees) {
                                        row.assignees = assignees[row.id] || [];
                                    }

                                    row.group_assignees = [];
                                    if (group_assignees) {
                                        row.group_assignees = group_assignees[row.id] || [];
                                    }

                                    let personalListNames = null;

                                    if (row.personal_list) {
                                        personalListNames = await localizePersonalListNames({
                                            currentUserId: userid,
                                            ownerUserId: row.owner,
                                            language: options.language,
                                        });
                                    }

                                    row.automation_count = automationCount[row.id] || 0;

                                    const { team_id, status } = row;
                                    delete row.team_id;

                                    const { statuses } = _.find(subcatStatusesByTeam, {
                                        team_id,
                                    }) || { statuses: [] };

                                    row.status =
                                        _.find(statuses, {
                                            status,
                                        }) || null;

                                    let total_task_counts = 0;
                                    if (task_status_counts) {
                                        const { counts = {} } =
                                            _.find(task_status_counts, {
                                                subcategory: row.id,
                                            }) || {};
                                        if (para_result.statuses[row.id]) {
                                            row.task_status_counts = para_result.statuses[row.id].map(status_obj => {
                                                status_obj.count = counts[status_obj.status];
                                                return status_obj;
                                            });
                                        }
                                        Object.values(counts).forEach(val => {
                                            total_task_counts += Number(val);
                                        });

                                        row.total_task_counts = total_task_counts;
                                    }

                                    delete row.assignee_id;
                                    delete row.assignee_color;
                                    delete row.email;
                                    delete row.username;
                                    delete row.profile_picture_key;

                                    if (overdue) {
                                        row.overdue_tasks = Number(overdue[row.id] || 0);
                                    }

                                    if (tasks_complete) {
                                        row.tasks_complete = Number(tasks_complete[row.id] || 0);
                                    }

                                    if (milestone_counts) {
                                        if (milestone_counts[row.id]) {
                                            row.milestones = {
                                                all_ids: milestone_counts[row.id].all_ids || [],
                                                items: milestone_counts[row.id].items || [],
                                            };
                                        } else {
                                            row.milestones = {
                                                all_ids: [],
                                                items: [],
                                            };
                                        }
                                    }

                                    if (points_counts) {
                                        if (points_counts[row.id]) {
                                            row.points_total = points_counts[row.id];
                                        } else {
                                            row.points_total = null;
                                        }
                                    }

                                    delete row.points;

                                    if (team_fields) {
                                        row.fields = team_fields[row.id];
                                    }

                                    if (personalListNames) {
                                        row.name = personalListNames.subcategoryName;
                                    }

                                    if (row.sprint) {
                                        row.name = sprintUtil.getSprintNameFromTemplate(
                                            row.name,
                                            row.sprint_date_format,
                                            moment(Number(row.sprint_start_date)).utc(),
                                            moment(Number(row.sprint_end_date)).utc(),
                                            row.sprint_index
                                        );
                                    }

                                    if (options.include_categories) {
                                        const access = Boolean(category_permissions[row.category]);
                                        row.category_details = {
                                            id: row.category,
                                            name: access ? row.category_name : 'Shared with me',
                                            hidden: row.category_hidden,
                                            access,
                                        };
                                    }
                                    delete row.category_name;
                                    delete row.category_hidden;

                                    if (options.include_followers) {
                                        row.followers = para_result.followers.followers[row.id];
                                    }

                                    if (options.include_projects) {
                                        const access = Boolean(project_permissions[row.project_id]);
                                        let project_name = access ? row.project_name : 'Shared with me';

                                        if (personalListNames) {
                                            project_name = personalListNames.projectName;
                                        }

                                        row.project_details = {
                                            id: row.project_id,
                                            name: project_name,
                                            access,
                                        };
                                    }
                                    delete row.project_name;

                                    if (!category_to_subcats[row.category]) {
                                        category_to_subcats[row.category] = [];
                                    }

                                    if (!options.simple) {
                                        row.features = features.subcategory_features[row.id];
                                        row.hasAttachments = parseInt(attachment_counts[row.id] || 0, 10) > 0;
                                        row.members = members[row.id] || [];
                                        row.group_members = groupMembers[row.id] || [];
                                    }

                                    if (options.include_permissions || options.gantt) {
                                        const subcategory_permissions = permissions[row.id];

                                        if (!subcategory_permissions) {
                                            logger.info({
                                                msg: 'No Access To Subcategory',
                                                userid,
                                                subcategory_id: row.id,
                                                category_id: row.category,
                                            });
                                            return;
                                        }

                                        row.permissions = subcategory_permissions.permissions;
                                        row.permission_level = parseInt(permissions[row.id].permission_level, 10);
                                    }

                                    if (!options.simple || options.gantt) {
                                        row.permissions = permissions[row.id].permissions;
                                        row.permission_level = parseInt(permissions[row.id].permission_level, 10);
                                        row.time_spent = parseInt(time_spent[row.id] || 0, 10);
                                        row.time_estimate = parseInt(time_estimate[row.id] || 0, 10);
                                    }

                                    if (!options.simple || options.gantt || options.subcategory_task_count) {
                                        row.taskcount = task_counts[row.id] ? task_counts[row.id].taskcount : '0';
                                        row.unfinishedtaskcount = task_counts[row.id]
                                            ? task_counts[row.id].unfinishedtaskcount
                                            : '0';
                                        row.closedtaskcount = task_counts[row.id]
                                            ? task_counts[row.id].closedtaskcount
                                            : '0';
                                    }

                                    if (options.include_statuses) {
                                        row.statuses = para_result.statuses[row.id] || [];
                                    }

                                    if (row.encrypted) {
                                        row.name = encrypt.decrypt(row.name);
                                        row.content = encrypt.decryptContent(row.content);
                                    }

                                    row.incoming_address = incoming_addresses[row.id];

                                    row.content_size = 'none';
                                    if (row.content) {
                                        let content_json = row.content;
                                        try {
                                            if (typeof content_json === 'string') {
                                                content_json = await json.parse(content_json);
                                            }

                                            let text_content = '';

                                            try {
                                                text_content = await quill.toPlainTextAsync(content_json.ops);
                                            } catch (e) {
                                                //
                                            }

                                            if (options.text_content) {
                                                row.text_content = text_content;
                                            }

                                            if (text_content.length > 500) {
                                                row.content_size = 'long';
                                            } else if (text_content.length > 100) {
                                                row.content_size = 'medium';
                                            } else if (text_content.length > 1) {
                                                row.content_size = 'small';
                                            }
                                        } catch (e) {
                                            // caught
                                        }
                                    }

                                    if (row.priority != null) {
                                        row.priority = config.priorities.map[row.priority];
                                    }

                                    if (pinnedTemplatesMapping) {
                                        row.pinned_templates = pinnedTemplatesMapping[row.id];
                                    }

                                    if (row.avatar) {
                                        row.avatar = {
                                            source: row.avatar.source,
                                            value: row.avatar.value,
                                        };
                                    }

                                    subcategory_list.push(row);

                                    if (customTaskTypes[row.custom_items_default]) {
                                        row.custom_item = customTaskTypes[row.custom_items_default];
                                    }
                                })
                            );
                        } catch (e) {
                            cb(e);
                        }

                        subcategories.forEach(row => {
                            if (!category_to_subcats[row.category]) {
                                category_to_subcats[row.category] = [];
                            }

                            category_to_subcats[row.category].push(row);
                        });

                        if (category_ids && options.filterEmpty && options.filterEmpty !== 'false') {
                            Object.keys(category_to_subcats).forEach(key => {
                                category_to_subcats[key] = category_to_subcats[key].filter(
                                    subcat => parseInt(subcat.taskcount, 10) > 0
                                );
                            });
                        }

                        if (category_ids) {
                            cb(null, {
                                subcategories: category_to_subcats,
                            });
                            for (const key of Object.keys(category_to_subcats)) {
                                await checkForDuplicateOrderindexes(key, category_to_subcats[key]);
                            }
                        } else {
                            cb(null, { subcategory_list });
                        }
                    }
                );
            });
        }
    );
}

export const _getSubcategories = tracer.wrap('subcategory._getSubcategories', {}, __getSubcategories);
function __getSubcategories(userid, options, cb) {
    const { category_ids, portfolio_id } = options;
    const { subcategory_ids } = options;
    const chunk_size = 500;

    if (
        (category_ids && category_ids.length <= chunk_size) ||
        portfolio_id ||
        (subcategory_ids && subcategory_ids.length <= chunk_size)
    ) {
        _getSubcategoriesPage(userid, options, cb);
        return;
    }

    if (category_ids) {
        const chunks = [];
        let i;
        let j;
        for (i = 0, j = category_ids.length; i < j; i += chunk_size) {
            chunks.push(category_ids.slice(i, i + chunk_size));
        }

        let subcategory_list = {};
        async.eachSeries(
            chunks,
            (chunk, each_cb) => {
                _getSubcategoriesPage(userid, { ...options, category_ids: chunk }, (subcat_err, subcat_result) => {
                    if (subcat_err) {
                        each_cb(subcat_err);
                    } else {
                        subcategory_list = { ...subcategory_list, ...subcat_result.subcategories };
                        each_cb();
                    }
                });
            },
            each_err => {
                if (each_err) {
                    cb(each_err);
                } else {
                    cb(null, { subcategories: subcategory_list });
                }
            }
        );
    } else if (subcategory_ids) {
        const chunks = [];
        let i;
        let j;
        for (i = 0, j = subcategory_ids.length; i < j; i += chunk_size) {
            chunks.push(subcategory_ids.slice(i, i + chunk_size));
        }

        let subcategory_list = [];
        async.eachSeries(
            chunks,
            (chunk, each_cb) => {
                _getSubcategoriesPage(userid, { ...options, subcategory_ids: chunk }, (subcat_err, subcat_result) => {
                    if (subcat_err) {
                        each_cb(subcat_err);
                    } else {
                        subcategory_list = subcategory_list.concat(subcat_result.subcategory_list);
                        each_cb();
                    }
                });
            },
            each_err => {
                if (each_err) {
                    cb(each_err);
                } else {
                    subcategory_list.sort((a, b) => Number(a.orderindex) - Number(b.orderindex));
                    cb(null, { subcategory_list });
                }
            }
        );
    } else {
        _getSubcategoriesPage(userid, options, cb);
    }
}

export function promiseGetSubcategories(userid, options) {
    return new Promise((res, rej) => {
        _getSubcategories(userid, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}

export function getSubcategories(req, resp, next) {
    const userid = req.decoded_token.user;
    const { category_id } = req.params;
    const options = req.query;
    options.workspace_id = req.headers['x-workspace-id'];

    options.skipAccess = false;

    logger.info({
        msg: 'Get subcategories request',
        userid,
        category_id,
        options,
    });

    if (category_id) {
        options.category_ids = [category_id];
    }
    _getSubcategories(userid, options, (err, result) => {
        if (err) {
            next(err);
        } else if (category_id) {
            resp.json({ subcategories: result.subcategories[category_id] || [] });
        } else {
            resp.json({ subcategories: result.subcategory_list });
        }
    });
}

/**
 *
 * @param {integer} userid - user id who requested the list
 * @param {integer} subcategory_id - list id
 * @param {function} cb - function callback
 * @param {object} options - options (the list is not complete)
 * @param {boolean} [options.include_task_properties] - if "true", it will append all Task Properties existing in the location
 * @private
 */
export function _getSubcategory(userid, subcategory_id, options, cb) {
    let permission_level;
    let permissions;
    let team_id;
    let subcategory;

    async.waterfall(
        [
            function checkAccess(series_cb) {
                if (userid === config.clickbot_assignee) {
                    series_cb();
                    return;
                }

                const ops = { permissions: [can_read] };

                access2.checkAccessSubcategory(userid, subcategory_id, ops, (err, result) => {
                    if (err) {
                        series_cb(new SubcatError(err, 'SUBCAT_066'));
                        return;
                    }

                    permission_level = parseInt(result.permission_level, 10);
                    permissions = result.permissions;
                    team_id = result.team_id;
                    series_cb();
                });
            },

            function retrieveSubcat(series_cb) {
                const query = `
                    SELECT
                        subcategories.id,
                        subcategories.NAME,
                        subcategories.personal_list,
                        subcategories.orderindex,
                        subcategories.content,
                        subcategories.color,
                        subcategories.status_name,
                        subcategories.avatar,
                        subcategories.custom_items_default,
                        coalesce(subcategories.due_date, subcategories.sprint_end_date) AS due_date,
                        COALESCE (subcategories.date_updated, 0) AS date_updated,
                        subcategories.due_date_time,
                        coalesce(subcategories.start_date, subcategories.sprint_start_date) AS start_date,
                        subcategories.start_date_time,
                        subcategories.archived,
                        coalesce(subcategories.hide_description, false) AS hide_description,
                        subcategories.deleted,
                        subcategories.category,
                        subcategories.encrypted,
                        subcategories.private,
                        subcategories.owner,
                        subcategories.assignee,
                        subcategories.priority,
                        subcategories.status,
                        subcategories.override_statuses,
                        subcategories.email_token,
                        subcategories.editor_token,
                        subcategories.sprint,
                        subcategories.sprint_index,
                        subcategories.sprint_status,
                        subcategories.sprint_start_date,
                        subcategories.sprint_end_date,
                        subcategories.sprint_date_done,
                        subcategories.sprint_date_progress,
                        subcategories.sprint_dashboard_id,
                        subcategories.subcategory_type,
                        categories.project_id,
                        categories.private as category_private,
                        categories.sprint AS category_sprint,
                        categories.hidden AS category_hidden,
                        categories.name as category_name,
                        categories.id as category_id,
                        projects.name AS project_name,
                        projects.id AS project_id,
                        projects.private as project_private,
                        category_sprint_settings.date_format AS sprint_date_format,
                        COALESCE(category_sprint_settings.estimation, team_sprint_settings.estimation) AS estimation,
                        owner.id AS owner_id,
                        owner.email AS owner_email,
                        owner.color AS owner_color,
                        owner.username AS owner_username,
                        owner.profile_picture_key AS owner_profile_picture_key,
                        assignee.id AS assignee_id,
                        assignee.email AS assignee_email,
                        assignee.color AS assignee_color,
                        assignee.username AS assignee_username,
                        assignee.profile_picture_key AS assignee_profile_picture_key
                    FROM task_mgmt.subcategories
                    INNER JOIN task_mgmt.categories
                        ON categories.id = subcategories.category
                    INNER JOIN task_mgmt.projects
                        ON projects.id = categories.project_id
                    LEFT OUTER JOIN task_mgmt.users AS owner
                        ON subcategories.owner = owner.id
                    LEFT OUTER JOIN task_mgmt.users AS assignee
                        ON subcategories.assignee = assignee.id
                    LEFT OUTER JOIN task_mgmt.category_sprint_settings
                        ON category_sprint_settings.category_id = categories.id
                        AND projects.sprints = TRUE
                        AND subcategories.sprint = TRUE
                    LEFT OUTER JOIN task_mgmt.team_sprint_settings
                        ON team_sprint_settings.team_id = projects.team
                        AND projects.sprints = TRUE
                        AND subcategories.sprint = TRUE
                    WHERE subcategories.id = $1::bigint`;

                db.readQuery(query, [subcategory_id], async (queryErr, result) => {
                    if (queryErr) {
                        series_cb(new SubcatError(queryErr, 'SUBCAT_002'));
                        return;
                    }

                    if (result.rows.length === 0) {
                        series_cb(new SubcatError('List not found', 'SUBCAT_003'));
                        return;
                    }
                    const [row] = result.rows;
                    let subcategory_name = row.name;
                    let project_name;

                    // check project and folder access
                    try {
                        let catAccessResult;
                        if (userid === config.clickbot_assignee) {
                            catAccessResult = true;
                        } else {
                            catAccessResult = await access2.checkAccessCategoryAsync(userid, row.category_id, {
                                permissions: [can_read],
                                dontFail: true,
                            });
                        }

                        row.category_details = {
                            id: row.category_id,
                            name: catAccessResult ? row.category_name : 'Shared with me',
                            hidden: row.category_hidden,
                            access: !!catAccessResult,
                        };
                        row.category_access = !!catAccessResult;

                        let projAccessResult;
                        if (userid === config.clickbot_assignee) {
                            projAccessResult = true;
                        } else {
                            projAccessResult = await access2.checkAccessProjectAsync(userid, row.project_id, {
                                permissions: [can_read],
                                dontFail: true,
                            });
                        }
                        project_name = projAccessResult ? row.project_name : 'Shared with me';
                        row.project_details = {
                            id: row.project_id,
                            name: project_name,
                            access: !!projAccessResult,
                        };
                        row.project_access = !!projAccessResult;

                        if (row.personal_list) {
                            subcategory_name = formatPersonalListName(
                                userid,
                                row.owner_id,
                                row.owner_username,
                                options.language
                            );
                            project_name = formatPersonalListSpaceName(
                                userid,
                                row.owner_id,
                                row.owner_username,
                                options.language
                            );
                        }
                        row.project_name = project_name;
                        row.name = subcategory_name;
                    } catch (accessError) {
                        series_cb(new SubcatError(accessError, 'SUBCAT_073'));
                        return;
                    }

                    if (row.owner) {
                        row.owner = row.owner_id;
                        row.owner_obj = {
                            userid: row.owner_id,
                            color: row.owner_color,
                            profilePicture: cf_sign.getCloudfrontAvatarUrl(row.owner_profile_picture_key),
                            initials: userInitials.getInitials(row.owner_email, row.owner_username),
                            email: row.owner_email,
                            username: row.owner_username,
                        };
                    }

                    if (row.sprint) {
                        row.name = sprintUtil.getSprintNameFromTemplate(
                            row.name,
                            row.sprint_date_format,
                            moment(Number(row.sprint_start_date)).utc(),
                            moment(Number(row.sprint_end_date)).utc(),
                            row.sprint_index
                        );
                    }

                    delete row.category_name;
                    delete row.category_sprint;

                    delete row.owner_id;
                    delete row.owner_color;
                    delete row.owner_profile_picture_key;
                    delete row.owner_email;
                    delete row.owner_username;

                    if (row.assignee) {
                        row.assignee = {
                            id: row.assignee_id,
                            userid: row.assignee_id,
                            color: row.assignee_color,
                            profilePicture: cf_sign.getCloudfrontAvatarUrl(row.assignee_profile_picture_key),
                            initials: userInitials.getInitials(row.assignee_email, row.assignee_username),
                            email: row.assignee_email,
                            username: row.assignee_username,
                        };
                    }

                    delete row.assignee_id;
                    delete row.assignee_color;
                    delete row.assignee_profile_picture_key;
                    delete row.assignee_email;
                    delete row.assignee_username;

                    if (row.encrypted) {
                        row.name = encrypt.decrypt(row.name);
                        row.content = encrypt.decryptContent(row.content);
                    }

                    row.content_size = 'none';
                    if (row.content) {
                        let content_json = row.content;
                        try {
                            if (typeof content_json === 'string') {
                                content_json = JSON.parse(content_json);
                            }

                            let text_content = '';

                            try {
                                text_content = await quill.toPlainTextAsync(content_json.ops);
                            } catch (err) {
                                //
                            }

                            if (options.text_content) {
                                row.text_content = text_content;
                            }

                            if (text_content.length > 500) {
                                row.content_size = 'long';
                            } else if (text_content.length > 100) {
                                row.content_size = 'medium';
                            } else if (text_content.length > 1) {
                                row.content_size = 'small';
                            }
                        } catch (e) {
                            // caught
                        }
                    }

                    if (row.priority != null) {
                        row.priority = config.priorities.map[row.priority];
                    }

                    if (row.avatar) {
                        row.avatar = {
                            source: row.avatar.source,
                            value: row.avatar.value,
                        };
                    }

                    row.permissions = permissions;
                    row.permission_level = parseInt(permission_level, 10);
                    row.team_id = team_id;
                    subcategory = row;

                    series_cb();
                });
            },

            function getData(series_cb) {
                async.parallel(
                    {
                        getMembers(para_cb) {
                            subcatMember._getMembers([subcategory_id], {}, (err, result) => {
                                if (err) {
                                    para_cb(err);
                                    return;
                                }

                                subcategory.members = result.members[subcategory_id] || [];
                                para_cb();
                            });
                        },

                        async getGroupMembers(para_cb) {
                            try {
                                const grp_result = await subcatMember._getGroupMembers([subcategory_id]);
                                subcategory.group_members = grp_result[subcategory_id] || [];
                                para_cb();
                            } catch (e) {
                                para_cb(e);
                            }
                        },

                        getFeatures(para_cb) {
                            projectHelper.getFeaturesBySubcategories([subcategory_id], async (err, result) => {
                                if (err) {
                                    para_cb(err);
                                    return;
                                }

                                subcategory.features = result.subcategory_features[subcategory_id];
                                try {
                                    if (subcategory.features?.sprints?.settings?.subcategory_template_id) {
                                        const { name } = await getTemplateNameAndIdByPermanentId(
                                            subcategory.features.sprints.settings.subcategory_template_id,
                                            config.get('hierarchy_tables.lists')
                                        );
                                        subcategory.features.sprints.settings.subcategory_template_name = name;
                                    }
                                } catch (e) {
                                    logger.warn({
                                        msg: 'Error getting template name',
                                        error: e,
                                        subcategory_id,
                                    });
                                }
                                para_cb();
                            });
                        },

                        taskCount(para_cb) {
                            options.all_statuses = true;
                            getSubcategoryTaskCount(userid, team_id, [subcategory_id], options, (err, result) => {
                                if (err) {
                                    para_cb(err);
                                } else {
                                    subcategory.taskCount = result[subcategory_id]
                                        ? result[subcategory_id].taskcount
                                        : '0';
                                    subcategory.taskcount = subcategory.taskCount;
                                    subcategory.unfinishedtaskcount = result[subcategory_id]
                                        ? result[subcategory_id].unfinishedtaskcount
                                        : '0';
                                    subcategory.closedtaskcount = result[subcategory_id]
                                        ? result[subcategory_id].closedtaskcount
                                        : '0';
                                    para_cb();
                                }
                            });
                        },

                        hasAttachments(para_cb) {
                            getSubcategoryAttachmentCount([subcategory_id], options, (err, result) => {
                                if (err) {
                                    para_cb(err);
                                    return;
                                }

                                subcategory.hasAttachments = parseInt(result[subcategory_id] || 0, 10) > 0;
                                para_cb();
                            });
                        },
                        timeSpent(para_cb) {
                            if (options.skip_time_spent || (permissions && !permissions.can_see_time_spent)) {
                                subcategory.time_spent = 0;
                                para_cb();
                                return;
                            }
                            getSubcategoryTimeSpent([subcategory_id], options, (err, result) => {
                                if (err) {
                                    para_cb(err);
                                } else {
                                    subcategory.time_spent = result[subcategory_id] || '0';
                                    para_cb();
                                }
                            });
                        },
                        async timeEstimate(para_cb) {
                            if (options.skip_time_estimate || (permissions && !permissions.can_see_time_spent)) {
                                subcategory.time_estimate = 0;
                                para_cb();
                                return;
                            }
                            await getSubcategoryTimeEstimate([subcategory_id], team_id, options, (err, result) => {
                                if (err) {
                                    para_cb(err);
                                } else {
                                    subcategory.time_estimate = result[subcategory_id] || '0';
                                    para_cb();
                                }
                            });
                        },
                        async pointsEstimate(para_cb) {
                            const shouldCalcSubcatPoints = options.include_points;
                            if ((permissions && !permissions.can_see_points_estimated) || !shouldCalcSubcatPoints) {
                                subcategory.points = 0;
                                para_cb();
                            } else {
                                try {
                                    let subcat_points = {};
                                    const parent_type = config.views.parent_types.subcategory;
                                    subcat_points = await pointsMod.getPointsCount(
                                        userid,
                                        [subcategory_id],
                                        parent_type,
                                        { team_id, req_from: 'get subcat' }
                                    );

                                    subcategory.points = subcat_points[subcategory_id] || 0;
                                    para_cb();
                                } catch (e) {
                                    para_cb(e);
                                }
                            }
                        },
                        async assignees(para_cb) {
                            try {
                                const result = await db.promiseReadQuery(
                                    'SELECT user_id FROM task_mgmt.subcategory_assignee WHERE subcategory_id = $1',
                                    [subcategory_id]
                                );

                                if (result.rows.length === 0) {
                                    subcategory.assignees = [];
                                    para_cb();
                                    return;
                                }

                                const user_ids = result.rows.map(({ user_id }) => user_id);
                                const users = await getUsersInfo(
                                    user_ids,
                                    ['id', 'email', 'username', 'color', 'profile_picture_key'],
                                    true,
                                    true
                                );

                                subcategory.assignees = users.rows.map(user => ({
                                    id: user.id,
                                    color: user.color,
                                    profilePicture: cf_sign.getCloudfrontAvatarUrl(user.profile_picture_key),
                                    initials: userInitials.getInitials(user.email, user.username),
                                    email: user.email,
                                    username: user.username,
                                }));

                                para_cb();
                            } catch (e) {
                                para_cb(e);
                            }
                        },
                        async group_assignees(para_cb) {
                            try {
                                const result = await db.promiseReadQuery(
                                    'SELECT group_id FROM task_mgmt.subcategory_group_assignee WHERE subcategory_id = $1',
                                    [subcategory_id]
                                );

                                if (result.rows.length === 0) {
                                    subcategory.group_assignees = [];
                                    para_cb();
                                    return;
                                }

                                const group_ids = result.rows.map(({ group_id }) => group_id);
                                const groups = await getGroupsInfo(group_ids, ['id', 'color', 'name']);

                                subcategory.group_assignees = groups.rows.map(group => ({
                                    id: group.id,
                                    color: group.color,
                                    name: group.name,
                                }));

                                para_cb();
                            } catch (e) {
                                para_cb(e);
                            }
                        },
                        automation_count(para_cb) {
                            automationHelpers.getAutomationCount(
                                [subcategory.id],
                                parent_types.subcategory,
                                {},
                                (err, result) => {
                                    if (err) {
                                        para_cb(err);
                                        return;
                                    }

                                    subcategory.automation_count = result.counts[subcategory.id] || 0;

                                    para_cb();
                                }
                            );
                        },

                        getInboundAddress(para_cb) {
                            emailHelper.getSubcatAddresses(userid, [subcategory.id], {}, (err, result) => {
                                if (err) {
                                    para_cb(new SubcatError(err, 'SUBCAT_082'));
                                    return;
                                }

                                subcategory.inbound_address = result[subcategory.id];

                                para_cb();
                            });
                        },

                        getStatuses(para_cb) {
                            const shouldUseReplica = !shouldNotUseReplicaForCategoryStatusRead();
                            subcatStatuses.getSubcategoryStatuses(
                                null,
                                { subcategory_id, use_replica: shouldUseReplica },
                                (err, result) => {
                                    if (err) {
                                        para_cb(new SubcatError(err, 'SUBCAT_102'));
                                        return;
                                    }

                                    subcategory.status = _.find(result, { status: subcategory.status });

                                    para_cb();
                                }
                            );
                        },

                        overridenStatuses(para_cb) {
                            statusMod._getStatusesBySubcategories(
                                [subcategory_id],
                                { replica: true },
                                (err, result) => {
                                    if (err) {
                                        para_cb(err);
                                    } else {
                                        subcategory.statuses = result[subcategory_id];
                                        para_cb();
                                    }
                                }
                            );
                        },

                        categoryAccess(para_cb) {
                            access2.checkAccessCategory(userid, subcategory.category, { permissions: [] }, catErr => {
                                if (catErr) {
                                    subcategory.category_details.name = 'Shared with me';
                                }
                                para_cb();
                            });
                        },

                        getViews(para_cb) {
                            if (!options.include_views) {
                                para_cb();
                                return;
                            }

                            const ops = {
                                include_archived: options.include_archived,
                                skipAccess: true,
                                parent_id: subcategory_id,
                                parent_type: config.views.parent_types.subcategory,
                                parent_permissions: permissions,
                                required_templates: true,
                                group_views: true,
                                skip_hierarchy_members: options.skip_hierarchy_members,
                            };

                            viewMod._getViews(userid, ops, (err, result) => {
                                if (err) {
                                    para_cb(new SubcatError(err, 'SUBCAT_122'));
                                    return;
                                }

                                subcategory.views = result.views;
                                subcategory.standard_views = result.standard_views;
                                subcategory.default_view = result.default_view;
                                subcategory.last_view = result.last_view;
                                subcategory.required_templates = result.required_templates || {};
                                subcategory.view_groups = result.groups;

                                para_cb();
                            });
                        },

                        async view_settings(para_cb) {
                            if (!options.include_views && !options.include_view_settings) {
                                para_cb();
                                return;
                            }

                            try {
                                subcategory.view_settings = await viewMod._getViewParentSettings(
                                    Number(subcategory_id),
                                    config.get('views.parent_types.subcategory')
                                );
                                para_cb();
                            } catch (err) {
                                para_cb(err);
                            }
                        },

                        getAttachments(para_cb) {
                            if (!options.include_all) {
                                para_cb(null, subcategory);
                                return;
                            }

                            const ops = {
                                type: config.attachments.types.subcategory,
                                include_hidden: false,
                            };

                            attachmentsMod._getAllAttachments(userid, subcategory.id, ops, (err, result) => {
                                if (err) {
                                    para_cb(new SubcatError(err, 'SUBCAT_086'));
                                    return;
                                }

                                subcategory.attachments = result.attachments;
                                para_cb(null, subcategory);
                            });
                        },

                        getGanttInfo(para_cb) {
                            if (!options.is_gantt) {
                                para_cb();
                                return;
                            }

                            gantt._getGanttPeriods([subcategory.id], {}, (err, result) => {
                                if (err) {
                                    para_cb();
                                } else {
                                    subcategory.gantt_start = result.gantt_start[subcategory.id];
                                    subcategory.gantt_end = result.gantt_end[subcategory.id];
                                    subcategory.gantt_progress_denominator =
                                        result.gantt_progress_denominator[subcategory.id] || '0';
                                    subcategory.gantt_progress_numerator =
                                        result.gantt_progress_numerator[subcategory.id] || '0';
                                    para_cb();
                                }
                            });
                        },

                        async followers(para_cb) {
                            try {
                                const results = await subcatFollowers.getFollowersAsync([subcategory.id]);
                                subcategory.followers = results.followers[subcategory.id];
                                para_cb();
                            } catch (e) {
                                para_cb(e);
                            }
                        },

                        commentCount(para_cb) {
                            if (!options.comment_count) {
                                para_cb(null);
                                return;
                            }

                            db.readQuery(
                                'SELECT count(*) FROM task_mgmt.comments WHERE parent = $1 AND type = $2 AND deleted = false',
                                [subcategory.id, config.comments.types.subcategory],
                                (err, result) => {
                                    if (err) {
                                        para_cb(err);
                                    } else {
                                        if (result.rows.length > 0) {
                                            subcategory.comment_count = result.rows[0].count;
                                        }

                                        para_cb();
                                    }
                                }
                            );
                        },

                        editorId(para_cb) {
                            if (userid && !subcategory.editor_token) {
                                const editor_token = `subcategory:${subcategory.id}:${uuid.v4()}:${uuid.v4()}`;
                                db.writeQuery(
                                    'UPDATE task_mgmt.subcategories SET editor_token = $1 WHERE id = $2',
                                    [editor_token, subcategory.id],
                                    editor_err => {
                                        if (editor_err) {
                                            para_cb(editor_err);
                                        } else {
                                            subcategory.editor_token = editor_token;
                                            para_cb();
                                        }
                                    }
                                );
                            } else {
                                para_cb();
                            }
                        },

                        sprintEstimation(para_cb) {
                            if (subcategory.estimation && uuidValidate(subcategory.estimation)) {
                                // only accept number dropdown custom fields on sprint estimation
                                const query = `
                                    SELECT
                                        COALESCE(SUM((drop_down_options.value->>'value')::FLOAT), 0) AS sum
                                    FROM
                                        task_mgmt.items
                                    LEFT JOIN task_mgmt.task_subcategories
                                        ON task_subcategories.task_id = items.id
                                    JOIN task_mgmt.subcategories
                                        ON subcategories.id = items.subcategory OR task_subcategories.subcategory = subcategories.id
                                    JOIN task_mgmt.field_values
                                        ON field_values.field_id = $1
                                        AND field_values.item_id = items.id
                                        AND field_values.deleted = FALSE
                                    JOIN task_mgmt.drop_down_options drop_down_options
                                        ON drop_down_options.field_id = $1
                                        AND drop_down_options.id = (field_values.value->>'value')::uuid
                                        AND drop_down_options.type = ${config.field_types.number}
                                    WHERE
                                        subcategories.id = $2 AND
                                        items.archived = FALSE AND
                                        items.deleted = FALSE`;
                                db.readQuery(query, [subcategory.estimation, subcategory.id], (err, result) => {
                                    if (err) {
                                        para_cb(err);
                                    } else {
                                        subcategory.sprint_estimation = Number(result.rows[0].sum);
                                        para_cb();
                                    }
                                });
                            } else {
                                para_cb();
                            }
                        },

                        async customItems(para_cb) {
                            const customType = new CustomType(subcategory.custom_items_default);
                            if (customType.isUnset() || customType.isPredefinedCustomTypeRange()) {
                                para_cb();
                                return;
                            }

                            try {
                                const { custom_items: [custom_item] = {} } = await customItemsMod.getCustomItemsByIds(
                                    [customType.value],
                                    team_id,
                                    {}
                                );

                                subcategory.custom_item = custom_item;
                                para_cb();
                            } catch (e) {
                                para_cb(e);
                            }
                        },

                        async presetTaskTemplate(para_cb) {
                            if (!options.include_preset_task_template) {
                                para_cb();
                                return;
                            }

                            try {
                                const presetTaskTemplate = await getPresetTaskTemplate('subcategory', subcategory.id);
                                if (presetTaskTemplate) {
                                    subcategory.preset_task_template = {
                                        permanent_template_id: presetTaskTemplate.permanentTemplateId,
                                        name: presetTaskTemplate.name,
                                    };
                                }
                                para_cb();
                            } catch (e) {
                                para_cb(e);
                            }
                        },

                        async pinnedTemplates(para_cb) {
                            if (!options.include_pinned_templates) {
                                para_cb();
                                return;
                            }

                            try {
                                const pinnedTemplatesMapping = await getPinnedTemplatesMappingForSubcategories({
                                    subcategoryIds: [subcategory.id],
                                });
                                subcategory.pinned_templates = pinnedTemplatesMapping[subcategory.id];
                                para_cb();
                            } catch (e) {
                                para_cb(e);
                            }
                        },

                        async defaultPermissionLevel(para_cb) {
                            try {
                                subcategory.default_permission_level = await fetchDefaultPermissionLevel({
                                    workspaceId: team_id,
                                    space: { id: subcategory.project_id, private: subcategory.project_private },
                                    folder: { id: subcategory.category_id, private: subcategory.category_private },
                                    list: { id: subcategory_id, private: subcategory.private },
                                    options: { useMaster: options?.replica === false },
                                });

                                // remove fields we dont need to return
                                delete subcategory.category_id;
                                delete subcategory.project_private;
                                delete subcategory.category_private;

                                para_cb();
                            } catch (error) {
                                logger.error({
                                    msg: 'Failed to get the default permission level',
                                    ECODE: 'SUBCAT_553',
                                    err: error,
                                    subcategory_id,
                                });
                                para_cb(error);
                            }
                        },

                        async getTaskProperties(para_cb) {
                            try {
                                if (taskPropertiesEnabled(team_id) && options.include_task_properties) {
                                    const taskPropertyApiClient = getTaskPropertyApiClientInstance();
                                    const result = await taskPropertyApiClient.getTaskProperties({
                                        userId: userid,
                                        parentId: subcategory_id,
                                        parentType: ParentType.Subcategory,
                                        workspaceId: team_id,
                                    });

                                    subcategory.task_properties = result.data.data.task_properties.map(
                                        ({ data }) => data
                                    );
                                }

                                para_cb();
                            } catch (error) {
                                para_cb(error);
                            }
                        },
                    },

                    err => {
                        if (err) {
                            series_cb(new SubcatError(err, 'SUBCAT_137'));
                        } else {
                            series_cb();
                        }
                    }
                );
            },
        ],
        err => {
            if (err) {
                cb(err);
            } else {
                cb(null, subcategory);

                if (isWriteToRecentTableDisabled('recent_subcategories')) {
                    return;
                }

                if (userid) {
                    if (subcategory.sprint) {
                        invalidateCachedSubcatTaskCount([subcategory_id]);
                    }
                    db.writeQuery(
                        `
                            INSERT INTO task_mgmt.recent_subcategories
                            (team_id, userid, subcategory_id, date)
                            VALUES ($1, $2, $3, $4)
                            ON CONFLICT (team_id, userid, subcategory_id)
                            DO UPDATE SET date = $4
                        `,
                        [team_id, userid, subcategory_id, new Date().getTime()],
                        () => {}
                    );
                }
            }
        }
    );
}

export function promiseGetSubcategory(userid, subcategory_id, options) {
    return new Promise((resolve, reject) => {
        _getSubcategory(userid, subcategory_id, options, (err, result) => {
            if (err) {
                reject(err);
            } else {
                resolve(result);
            }
        });
    });
}

export function getSubcategory(req, resp, next) {
    const userid = getUserFromDecodedToken(req, 'getSubcategory');
    const { subcategory_id } = req.params;
    const include_all = req.query.include_all === 'true';
    const include_views = req.query.include_views === 'true';
    const skip_hierarchy_members = req.query.skip_hierarchy_members === 'true';
    const is_gantt = req.query.gantt === 'true';
    const skip_time_spent = req.query.skip_time_spent === 'true';
    const skip_time_estimate = req.query.skip_time_estimate === 'true';
    const include_points = req.query.include_points === 'true';
    const include_preset_task_template = req.query.include_preset_task_template === 'true';
    const include_pinned_templates = req.query.include_pinned_templates === 'true';
    const include_view_settings = modelHelpers.toBoolean(req.query.include_view_settings);
    const include_task_properties = req.query.include_task_properties === 'true';
    const language = getPreferredLanguage(req);

    if (!subcategory_id || !isNumeric(subcategory_id)) {
        resp.status(400).send({
            err: 'Provide a valid subcategoryID',
            ECODE: 'SUBCAT_087',
        });
        return;
    }

    if (shouldReportAssetViewed(req.header('Referer'), req.header('User-Agent'))) {
        reportAssetViewed(userid, subcategory_id, 'subcategory');
    }
    const workspaceId = getWorkspaceIdFromContext() || req.headers['x-workspace-id'];
    const v3DataPromise = callV3ListAPI(req, subcategory_id, workspaceId);
    const getSubcategoryPromise = new Promise((resolve, reject) => {
        _getSubcategory(
            userid,
            subcategory_id,
            {
                include_all,
                skip_time_spent,
                skip_time_estimate,
                include_followers: true,
                include_views,
                is_gantt,
                comment_count: true,
                include_points,
                skip_hierarchy_members,
                include_preset_task_template,
                include_pinned_templates,
                include_view_settings,
                include_task_properties,
                language,
            },
            (err, result) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(result);
                }
            }
        );
    });

    Promise.allSettled([v3DataPromise, getSubcategoryPromise]).then(async ([v3DataResult, getSubcategoryResult]) => {
        if (getSubcategoryResult.status === 'rejected') {
            next(getSubcategoryResult.reason);
            return;
        }

        let result = getSubcategoryResult.value;
        if (v3DataResult.status === 'fulfilled') {
            result = getHierarchyV3Data(req, subcategory_id, result, v3DataResult.value, workspaceId);
        } else {
            logger.error({
                ECODE: 'HIERARCHY_007',
                err: v3DataResult.reason,
                msg: 'Failed to get response from hierarchy core v3 client',
            });
            if (
                v3DataResult.reason?.code?.toUpperCase() === 'ECONNABORTED' ||
                v3DataResult.reason?.code?.toUpperCase() === 'ECONNREFUSED' ||
                v3DataResult.reason?.code?.toUpperCase() === 'EHOSTUNREACH' ||
                v3DataResult.reason?.ECODE === 'AUTHN_003'
            ) {
                resp.status(200).send(result);
            } else if (result.deleted) {
                logger.warn({ msg: 'SubCategory is deleted', subcategory_id });
                resp.status(200).send(result);
            } else if (!result.deleted && v3DataResult.reason?.ECODE === 'ACCESS_999') {
                logger.warn({
                    msg: 'SubCategory is not deleted but not found in v3 client',
                    subcategory_id,
                    err: v3DataResult.reason,
                });
                resp.status(200).send(result);
            } else {
                next(v3DataResult.reason);
            }
            return;
        }

        resp.status(200).send(result);
    });
}

export function getSubcategoryAsync(userid, subcategory_id, options) {
    return new Promise((resolve, reject) => {
        _getSubcategory(userid, subcategory_id, options, (err, result) => {
            if (err) {
                reject(err);
            } else {
                resolve(result);
            }
        });
    });
}

export async function callV3ListAPI(req, subcategory_id, workspaceId) {
    const shouldCallV3 = shouldUseHierarchyV3CoreClient() && workspaceId;
    if (!shouldCallV3) {
        logger.error({ msg: 'Workspace id not found in headers for subcategory', subcategory_id });
        return Promise.resolve(null);
    }
    return hierarchyCoreClientInstance.getList(workspaceId, subcategory_id, req.headers);
}

export function getHierarchyV3Data(req, subcategory_id, monolithData, v3Data, workspaceId) {
    if (!workspaceId) {
        logger.error({ msg: 'Workspace id not found in headers for subcategory', subcategory_id });
        return monolithData;
    }
    const shouldCallV3 = shouldUseHierarchyV3CoreClient() && workspaceId && v3Data;
    if (shouldCallV3) {
        const v3Response = v3Data;

        // skip assignee as its in a different format in v3
        // skip name for personal list because it's dynamically computed depending on access and user
        const fieldsToSkip = v3Response.value.personal_list ? ['assignee', 'owner', 'name'] : ['assignee', 'owner'];
        const simplifiedV3Response = _.omit(v3Response.value, fieldsToSkip);
        if (
            v3Response.value?.assignee &&
            monolithData.assignee &&
            v3Response.value.assignee !== monolithData.assignee.id
        ) {
            // TODO: monitor if there are any cases where this happens(https://staging.clickup.com/t/333/CLK-433831)
            logger.warn('Assignee mismatch between monolith and hierarchy v3 core client');
        }
        const updatedResult = { ...monolithData, ...simplifiedV3Response };

        if (v3Response.version) {
            updatedResult._version_vector = { ...v3Response.version };
        }
        return updatedResult;
    }
    return monolithData;
}

export const _createSubcategory = tracer.wrap('subcategory._createSubcategory', {}, __createSubcategory);

function __createSubcategory(userid, category_id, options, cb) {
    const queryFunc = options.client ? options.client.query.bind(options.client) : db.readQuery;

    if (!input.validateSectionName(options.name)) {
        cb(new SubcatError('List Name Invalid', 'SUBCAT_020', 400));
        return;
    }

    if (!options.members) {
        options.members = [];
    }

    if (options.statuses) {
        if (!checkSubcategoryStatusesAreValid(options, cb)) {
            return;
        }

        const first_done_status_ids = _.findIndex(
            options.statuses,
            status_obj => ['done', 'closed'].includes(status_obj.type) || status_obj.status === 'Closed'
        );

        const done_statuses_not_last = options.statuses
            .slice(first_done_status_ids)
            .some(status_obj => !['done', 'closed'].includes(status_obj.type) && status_obj.status !== 'Closed');

        if (done_statuses_not_last) {
            cb(new SubcatError('Done statuses must come last in statuses array', 'SUBCAT_143', 400));
            return;
        }
    }

    if (
        isSubcategoryTypePickableOnlyByClickbot(options.subcategory_type) &&
        userid !== config.get('clickbot_assignee')
    ) {
        cb(new SubcatError('Subcategory type invalid', 'SUBCAT_215', 400));
        return;
    }

    const customTaskTypeDefault = new CustomType(options.custom_items_default);

    let team_id;
    let client;
    let done;
    let para_result;
    let new_id;
    let project_id = null;
    let list_to_copy_views_from = null;
    let events;
    const ovm = getObjectVersionManager();
    const userAcl = {};
    async.waterfall(
        [
            function createHiddenCategory(water_cb) {
                if (!options.project_id) {
                    water_cb();
                    return;
                }

                const cat_options = {
                    hidden: true,
                    id_only: true,
                    position: options.position,
                    import_id: options.import_id,
                    importing: options.importing,
                    ws_key: options.ws_key,
                };

                if (options.import_id) {
                    cat_options.import_uuid = uuid.v4();
                }

                // will check project access
                categoryMod._createCategory(userid, options.project_id, cat_options, (err, result) => {
                    if (err) {
                        water_cb(new SubcatError(err, 'SUBCAT_121'));
                        return;
                    }

                    category_id = result;

                    water_cb();
                });
            },

            function getInfo(water_cb) {
                async.parallel(
                    {
                        access(para_cb) {
                            if (options.project_id || userid === config.clickbot_assignee) {
                                para_cb();
                                return;
                            }

                            access2.checkAccessCategory(
                                userid,
                                category_id,
                                { permissions: [can_create_lists] },
                                para_cb
                            );
                        },

                        accessAssignee(para_cb) {
                            if (!options.assignee) {
                                para_cb();
                                return;
                            }

                            if (options.skip_assignee_access) {
                                para_cb();
                                return;
                            }

                            access2.checkAccessCategory(
                                options.assignee,
                                category_id,
                                { permissions: [], checkJoined: options.checkJoined },
                                para_cb
                            );
                        },

                        checkHiddenCategory(para_cb) {
                            if (options.project_id) {
                                para_cb();
                                return;
                            }

                            const query = `SELECT hidden FROM task_mgmt.categories WHERE id = $1`;
                            const params = [category_id];

                            db.readQuery(query, params, (err, result) => {
                                if (err) {
                                    para_cb(new SubcatError(err, 'SUBCAT_123'));
                                } else if (!result.rows.length) {
                                    para_cb(new SubcatError('Folder not found', 'SUBCAT_124', 404));
                                } else if (result.rows[0].hidden) {
                                    para_cb(new SubcatError('Cannot add to a hidden folder', 'SUBCAT_125', 400));
                                } else {
                                    para_cb();
                                }
                            });
                        },

                        async maxSubcategories(para_cb) {
                            if (options.ignore_paywall) {
                                para_cb();
                                return;
                            }

                            try {
                                await checkListLimit(category_id, null, null, 1, options.subcategory_type);
                                para_cb();
                            } catch (e) {
                                para_cb(e);
                            }
                        },

                        validateName(para_cb) {
                            if (options.validate_name === false) {
                                para_cb();
                                return;
                            }

                            let query;
                            let params;

                            const name_encrypted = encrypt.encrypt(options.name);

                            if (options.project_id) {
                                query = `
                                    SELECT subcategories.id
                                    FROM task_mgmt.subcategories
                                    INNER JOIN task_mgmt.categories
                                        ON categories.id = subcategories.category
                                    WHERE categories.project_id = $2
                                        AND categories.hidden = TRUE
                                        AND categories.archived = FALSE
                                        AND categories.deleted = FALSE
                                        AND categories.template = FALSE
                                        AND subcategories.archived = FALSE
                                        AND subcategories.template = FALSE
                                        AND subcategories.deleted = FALSE
                                        AND (lower(subcategories.name) = lower($1) OR subcategories.name = $3)`;
                                params = [options.name, options.project_id, name_encrypted];
                            } else {
                                query = `
                                    SELECT id
                                    FROM task_mgmt.subcategories
                                    WHERE (LOWER(name) = LOWER($1) OR name = $3)
                                        AND category = $2
                                        AND archived = false
                                        AND deleted = false
                                        AND template = false`;
                                params = [options.name, category_id, name_encrypted];
                            }

                            queryFunc(query, params, (err, result) => {
                                if (err) {
                                    para_cb(new SubcatError(err, 'SUBCAT_017'));
                                } else if (result.rows.length > 0) {
                                    para_cb(new SubcatError('List name taken', 'SUBCAT_016', 400));
                                } else {
                                    para_cb();
                                }
                            });
                        },

                        orderindex(para_cb) {
                            if (options.position != null) {
                                const query = `
                                    SELECT orderindex
                                    FROM task_mgmt.subcategories
                                    WHERE category = $1
                                        AND deleted = false
                                        AND template = false
                                        AND archived = false
                                    ORDER BY orderindex ASC
                                    LIMIT 1
                                    OFFSET $2`;
                                const params = [category_id, options.position];

                                queryFunc(query, params, (err, result) => {
                                    if (err) {
                                        para_cb(new SubcatError(err, 'SUBCAT_031'));
                                    } else if (result.rows.length > 0) {
                                        para_cb(null, result.rows[0].orderindex);
                                    } else {
                                        const query2 = `
                                            SELECT coalesce(max(orderindex) + 1, 0) AS orderindex
                                            FROM task_mgmt.subcategories
                                            WHERE category = $1`;

                                        queryFunc(query2, [category_id], (maxErr, maxResult) => {
                                            if (maxErr) {
                                                para_cb(new SubcatError(maxErr, 'SUBCAT_004'));
                                            } else {
                                                para_cb(null, maxResult.rows[0].orderindex);
                                            }
                                        });
                                    }
                                });
                            } else {
                                const query = `
                                    SELECT coalesce(max(orderindex) + 1, 0) AS orderindex
                                    FROM task_mgmt.subcategories
                                    WHERE category = $1`;

                                queryFunc(query, [category_id], (err, result) => {
                                    if (err) {
                                        para_cb(new SubcatError(err, 'SUBCAT_063'));
                                    } else {
                                        para_cb(null, result.rows[0].orderindex);
                                    }
                                });
                            }
                        },

                        teamEncrypted(para_cb) {
                            const query = `
                                SELECT teams.should_encrypt, projects.team, categories.override_statuses, projects.id AS project_id
                                FROM task_mgmt.teams, task_mgmt.projects, task_mgmt.categories
                                WHERE categories.id = $1
                                    AND categories.project_id = projects.id
                                    AND projects.team = teams.id`;
                            db.readQuery(query, [category_id], para_cb);
                        },

                        subcatStatuses(para_cb) {
                            if (!options.status) {
                                para_cb();
                                return;
                            }

                            subcatStatuses.getSubcategoryStatuses(null, { category_id }, (err, result) => {
                                if (err) {
                                    para_cb(new SubcatError(err, 'SUBCAT_103'));
                                    return;
                                }

                                const subcat_obj =
                                    _.find(result, { status: options.status }) ??
                                    _.find(
                                        result,
                                        obj =>
                                            obj.status &&
                                            options.status &&
                                            obj.status.toLowerCase() === options.status.toLowerCase()
                                    );

                                if (!subcat_obj) {
                                    para_cb(new SubcatError('Invalid status', 'SUBCAT_104', 400));
                                    return;
                                }

                                options.status = subcat_obj.status;

                                para_cb();
                            });
                        },

                        sprintSettings(para_cb) {
                            if (!options.sprint || !category_id) {
                                para_cb();
                                return;
                            }
                            async.parallel(
                                {
                                    sprintSettings(para_cb2) {
                                        db.readQuery(
                                            `SELECT 1 FROM task_mgmt.category_sprint_settings WHERE category_id = $1 AND category_sprint_settings.copy_views = TRUE`,
                                            [category_id],
                                            para_cb2
                                        );
                                    },
                                    currentSprint(para_cb2) {
                                        db.readQuery(
                                            `SELECT subcategories.id FROM task_mgmt.subcategories WHERE subcategories.category = $1 AND subcategories.sprint_start_date < $2 AND subcategories.sprint_end_date > $2`,
                                            [category_id, Date.now()],
                                            para_cb2
                                        );
                                    },
                                },
                                (err, result) => {
                                    if (err) {
                                        para_cb(new SubcatError(err, 'SUBCAT_105'));
                                    } else {
                                        const settings = result.sprintSettings.rows[0];
                                        const current = result.currentSprint.rows[0];
                                        if (!settings || !current) {
                                            para_cb();
                                        } else {
                                            list_to_copy_views_from = current;
                                            para_cb();
                                        }
                                    }
                                }
                            );
                        },
                        async validateCustomItem(para_cb) {
                            if (!customTaskTypeDefault.isCustomType()) {
                                para_cb();
                                return;
                            }
                            if (customTaskTypeDefault.isPredefinedCustomTypeRange()) {
                                if (!customTaskTypeDefault.isActualPredefinedCustomType()) {
                                    para_cb(
                                        new SubcatError('The custom task type provided is invalid', 'CUSTOM_ITEM_037')
                                    );
                                    return;
                                }
                                para_cb();
                                return;
                            }

                            try {
                                await customItemsMod.verifyHaveAccessToCustomItemViaEntity(
                                    customTaskTypeDefault.value,
                                    undefined,
                                    userid,
                                    { category_id }
                                );

                                para_cb();
                            } catch (e) {
                                para_cb(e);
                            }
                        },
                        async getParentWithOverriddenStatuses(para_cb) {
                            try {
                                const parent = await findParentWithProperty(
                                    category_id,
                                    folder => folder.override_statuses
                                );
                                para_cb(null, parent);
                            } catch (err) {
                                para_cb(err);
                            }
                        },

                        async checkGranularDefaultPermissionLevel(para_cb) {
                            try {
                                await checkGranularDefaultPermissionLevelPaywall(
                                    { category_ids: [category_id] },
                                    options?.default_permission_level,
                                    team_id
                                );
                                para_cb();
                            } catch (error) {
                                para_cb(error);
                            }
                        },
                    },
                    (err, result) => {
                        if (err) {
                            water_cb(err);
                        } else {
                            para_result = result;
                            water_cb();
                        }
                    }
                );
            },

            water_cb => {
                team_id = para_result.teamEncrypted.rows[0].team;
                createNewSubcategoryId({ workspace_id: team_id }, (err, seq_id) => {
                    new_id = seq_id;
                    water_cb(err);
                });
            },
            water_cb => {
                if (options.client) {
                    client = options.client;
                    water_cb();
                } else {
                    db.getConn(cb, { label: 'create subcategory' }, (clientErr, _client, _done) => {
                        if (clientErr) {
                            water_cb(clientErr);
                            return;
                        }
                        client = _client;
                        done = _done;
                        water_cb();
                    });
                }
            },

            water_cb => {
                if (!options.client) {
                    client.query('BEGIN', err => water_cb(err));
                    return;
                }

                water_cb();
            },
            async function insertSubcategory(water_cb) {
                let encrypted = false;

                if (!client.versionUpdates) {
                    client.versionUpdates = [];
                }

                if (
                    config.encryption.subcategories &&
                    para_result.teamEncrypted.rows[0] &&
                    para_result.teamEncrypted.rows[0].should_encrypt
                ) {
                    options.name = encrypt.encrypt(options.name);
                    options.content = encrypt.encryptContent(options.content);
                    encrypted = true;
                }

                let override_statuses = false;
                let status_group = null;
                team_id = para_result.teamEncrypted.rows[0].team;

                if (para_result.teamEncrypted.rows[0]) {
                    [{ override_statuses, project_id }] = para_result.teamEncrypted.rows;
                }

                if (hierarchySubFoldersEnabledWorkspace(team_id)) {
                    if (options.override_statuses) {
                        status_group = `subcat_${new_id}`;
                    } else {
                        const parentWithOverriddenStatuses = para_result.getParentWithOverriddenStatuses;
                        if (parentWithOverriddenStatuses.type === config.parent_types.category) {
                            status_group = `cat_${parentWithOverriddenStatuses.data.id}`;
                        } else {
                            status_group = `proj_${parentWithOverriddenStatuses.data.id}`;
                        }
                    }
                } else if (options.override_statuses) {
                    status_group = `subcat_${new_id}`;
                } else if (override_statuses) {
                    status_group = `cat_${category_id}`;
                } else {
                    status_group = `proj_${project_id}`;
                }

                const queries = [];

                if (options.position != null) {
                    queries.push({
                        query: `
                            UPDATE task_mgmt.subcategories
                            SET orderindex = orderindex + 1
                            WHERE category = $1
                                AND template = false
                                AND deleted = false
                                AND orderindex >= $2`,
                        params: [category_id, para_result.orderindex],
                    });
                }

                queries.push({
                    query: `
                        UPDATE task_mgmt.subcategories SET
                            category = $1,
                            name = $2,
                            orderindex = $3,
                            boardindex = $3,
                            import_id = $4,
                            import_uuid = $5,
                            importing = $6,
                            content = $7,
                            color = $8,
                            due_date = $9,
                            due_date_time = $10,
                            start_date = $11,
                            start_date_time = $12,
                            template = false,
                            encrypted = $13,
                            status_group = $14,
                            private = $15,
                            owner = $16,
                            creator = $16,
                            priority =  $17,
                            status = $18,
                            assignee = $19,
                            override_statuses = $20,
                            sprint = $22,
                            sprint_start_date = $23,
                            sprint_end_date = $24,
                            sprint_index = $25,
                            date_created = $26,
                            date_updated = $26,
                            archived = $27,
                            personal_list = $28,
                            custom_items_default = $29,
                            subcategory_type = $30
                            WHERE id = $21 RETURNING id, name, orderindex, category, encrypted, private, owner, override_statuses`,
                    params: [
                        category_id,
                        options.name,
                        para_result.orderindex,
                        options.import_id,
                        options.import_uuid,
                        options.importing,
                        options.content || generateNewLineOp(),
                        options.color,
                        options.due_date,
                        options.due_date_time || false,
                        options.start_date,
                        options.start_date_time,
                        encrypted,
                        status_group,
                        options.pvt || false,
                        userid,
                        options.priority,
                        options.status,
                        options.assignee,
                        options.override_statuses || false,
                        new_id,
                        options.sprint,
                        options.sprint_start_date,
                        options.sprint_end_date,
                        options.sprint_index,
                        Date.now(),
                        Boolean(options.archived),
                        options.personal_list ?? false,
                        customTaskTypeDefault.value,
                        options.subcategory_type,
                    ],
                });

                const privacySetting = {
                    field: 'privacy',
                    before: null,
                    after: options.pvt,
                };

                const objectChanges = [privacySetting];

                if (options.subcategory_type != null) {
                    objectChanges.push({
                        field: 'subcategory_type',
                        after: options.subcategory_type,
                    });
                }

                client.versionUpdates.push(
                    hierarchyObjectVersionUpdateRequests({
                        objectId: new_id,
                        objectType: ObjectType.LIST,
                        workspaceId: team_id,
                        operation: OperationType.CREATE,
                        fanout: false,
                        parent: category_id,
                        ws_key: options.ws_key,
                        options: {
                            project_id: options.project_id,
                        },
                        changes: objectChanges,
                    })
                );

                client.versionUpdates.push(
                    hierarchyTreeVersionUpdateRequests({
                        objectId: category_id,
                        objectType: ObjectType.FOLDER,
                        workspaceId: team_id,
                        operation: OperationType.CREATE,
                        fanout: false,
                        ws_key: options.ws_key,
                    })
                );

                let rows = [];
                async.each(
                    queries,
                    (query, each_cb) => {
                        client.query(query.query, query.params, (err3, result) => {
                            if (err3) {
                                each_cb(err3);
                            } else {
                                rows = result.rows.concat(rows);
                                each_cb();
                            }
                        });
                    },
                    err2 => {
                        if (err2) {
                            water_cb(new SubcatError(err2, 'SUBCAT_005'));
                        } else {
                            const subcategory = rows[0];
                            if (subcategory.encrypted) {
                                subcategory.name = encrypt.decrypt(subcategory.name);
                                subcategory.content = encrypt.decryptContent(subcategory.content);
                            }

                            subcategory.taskCount = 0;
                            water_cb(null, subcategory);
                        }
                    }
                );
            },

            (subcategory, water_cb) => {
                reportSubcatChanged(userid, subcategory.id, { description_changed: false });
                if (options.override_statuses && options.statuses) {
                    const status_texts = [];
                    const status_types = [];
                    options.statuses = options.statuses.map(_status => {
                        const status = _status;
                        if (status.status.toLowerCase() === 'closed' && !status.type) {
                            status.status = 'Closed';
                            status.type = 'closed';
                        } else if (status.status.toLowerCase() === 'open' && !status.type) {
                            status.status = 'Open';
                            status.type = 'open';
                        } else {
                            status.status = status.status.toLowerCase();
                            status.type = status.type || 'custom';
                        }

                        status.color = assureStatusColor(status);

                        if (status.status.toLowerCase() === 'open') {
                            status.status = 'Open';
                        }
                        if (status.status.toLowerCase() === 'closed') {
                            status.status = 'Closed';
                        }

                        status_texts.push(status.status);

                        status_types.push(status.type);
                        return status;
                    });

                    if (status_types.filter(type => type === 'open').length !== 1) {
                        water_cb(
                            new SubcatError(
                                'Open is a required status type and there can only be one',
                                'STATUS_002',
                                400
                            ).logError()
                        );
                        return;
                    }

                    if (status_types.filter(type => type === 'closed').length !== 1) {
                        water_cb(
                            new SubcatError(
                                'Closed is a required status type and there can only be one',
                                'STATUS_003',
                                400
                            ).logError()
                        );
                        return;
                    }

                    const queries = [];
                    options.statuses.forEach((status, idx) => {
                        const status_id = generateStatusId({
                            subcategory_id: subcategory.id,
                        });

                        queries.push({
                            query: 'INSERT INTO task_mgmt.statuses(id, project_id, status, orderindex, color, type, subcategory_id, status_group, workspace_id) (SELECT $7, $1, $2, $3, $4, $5, $6, $8, $9 WHERE NOT EXISTS (SELECT * FROM task_mgmt.statuses WHERE subcategory_id = $6 AND status = $2))',
                            params: [
                                null,
                                status.status,
                                idx,
                                status.color,
                                status.type,
                                subcategory.id,
                                status_id,
                                `subcat_${subcategory.id}`,
                                team_id,
                            ],
                        });
                    });

                    async.each(
                        queries,
                        (query, each_cb) => {
                            client.query(query.query, query.params, each_cb);
                        },
                        err2 => {
                            if (err2) {
                                water_cb(new SubcatError(err2, 'CAT_071'));
                            } else {
                                water_cb(null, subcategory);
                            }
                        }
                    );
                } else {
                    water_cb(null, subcategory);
                }
            },

            function insertMembers(subcategory, water_cb) {
                if (!options.pvt) {
                    userAcl[userid] = 5;
                    water_cb(null, subcategory);
                    return;
                }

                if (isMemberInsertionSkippedForSubcategoryType(options.subcategory_type)) {
                    // The list holding workspace user profiles is private and does not have any members.
                    // Different permissions will be used to check user access to it.
                    water_cb(null, subcategory);
                    return;
                }

                const { id: subcategory_id } = subcategory;
                let { members } = options;

                members.push({ userid, permission_level: 5 });

                if (options.assignee) {
                    members.push({ userid: options.assignee, permission_level: 5 });
                }

                if (members.some(member => member.permission_level == null)) {
                    water_cb(new SubcatError('Missing permission level on member', 'SUBCAT_201', 400));
                    return;
                }

                members = _.uniqBy(members, member => member.userid);

                let query =
                    'INSERT INTO task_mgmt.subcategory_members(subcategory, userid, permission_level, date_added, workspace_id) VALUES ';
                const params = [subcategory_id, new Date().getTime(), team_id];

                members.forEach(({ userid: member_id, permission_level }, idx, arr) => {
                    const comma = idx === arr.length - 1 ? '' : ',';
                    params.push(member_id, permission_level);
                    query += ` ($1, $${params.length - 1}, $${params.length}, $2, $3)${comma} `;
                });

                members.forEach(member => {
                    userAcl[member.userid] = member.permission_level;
                    privacy.insertSubcategoryPrivacy('member_added', userid, subcategory_id, {
                        member: member.userid,
                        permission_level: member.permission_level,
                    });
                });

                client.query(query, params, err => {
                    if (err) {
                        water_cb(err);
                    } else {
                        water_cb(null, subcategory);
                    }
                });
            },

            async function upsertDefaultLocationPermissions(subcategory, water_cb) {
                if (
                    !isDefaultLocationPermissionsEnabled(team_id) ||
                    !isValidDefaultPermissionLevel(options.default_permission_level)
                ) {
                    water_cb(null, subcategory);
                    return;
                }

                const { id: subcategory_id } = subcategory;
                const queryObject = upsertObjectAccessInfoQueryObject({
                    objectId: subcategory_id,
                    objectType: ObjectType.LIST,
                    workspaceId: team_id,
                    defaultPermissionLevel: options.default_permission_level,
                });

                client.query(queryObject.query, queryObject.params, err => {
                    if (err) {
                        water_cb(err);
                    } else {
                        water_cb(null, subcategory);
                    }
                });
            },

            function addAssignees(subcategory, water_cb) {
                if (!options.assignees && !options.group_assignees) {
                    water_cb(null, subcategory);
                    return;
                }

                const { id: subcategory_id } = subcategory;

                const queries = [];
                const addDate = new Date().getTime();
                const assignees = options.assignees || [];
                const groupAssignees = options.group_assignees || [];
                if (assignees && assignees.length > 0) {
                    let query =
                        'INSERT INTO task_mgmt.subcategory_assignee(subcategory_id, user_id, date_assigned, workspace_id) VALUES ';
                    let params = [];
                    assignees.forEach((user_id, index) => {
                        const counter = 4 * index;
                        query += `($${counter + 1}, $${counter + 2}, $${counter + 3}, $${counter + 4})`;
                        query += index === assignees.length - 1 ? ' ' : ', ';
                        params.push(subcategory_id, user_id, addDate, team_id);
                    });

                    queries.push({
                        query,
                        params,
                    });

                    query =
                        'INSERT INTO task_mgmt.subcategory_members(subcategory, userid, permission_level, date_added, workspace_id) VALUES ';
                    params = [];
                    assignees.forEach((user_id, index) => {
                        const counter = 5 * index;
                        query += `($${counter + 1}, $${counter + 2}, $${counter + 3}, $${counter + 4}, $${
                            counter + 5
                        })`;
                        query += index === assignees.length - 1 ? ' ' : ', ';
                        params.push(
                            subcategory_id,
                            user_id,
                            HierarchyPermissionLevel.CAN_CREATE_AND_EDIT,
                            addDate,
                            team_id
                        );
                    });

                    queries.push({
                        query,
                        params,
                    });
                }

                if (groupAssignees && groupAssignees.length > 0) {
                    let query =
                        'INSERT INTO task_mgmt.subcategory_group_assignee(subcategory_id, group_id, date_assigned, workspace_id) VALUES ';
                    let params = [];
                    groupAssignees.forEach((group_id, index) => {
                        const counter = 4 * index;
                        query += `($${counter + 1}, $${counter + 2}, $${counter + 3}, $${counter + 4})`;
                        query += index === groupAssignees.length - 1 ? ' ' : ', ';
                        params.push(subcategory_id, group_id, addDate, team_id);
                    });

                    queries.push({
                        query,
                        params,
                    });

                    query =
                        'INSERT INTO task_mgmt.subcategory_group_members(subcategory, group_id, permission_level, date_added, workspace_id) VALUES ';
                    params = [];
                    groupAssignees.forEach((group_id, index) => {
                        const counter = 5 * index;
                        query += `($${counter + 1}, $${counter + 2}, $${counter + 3}, $${counter + 4}, $${
                            counter + 5
                        })`;
                        query += index === groupAssignees.length - 1 ? ' ' : ', ';
                        params.push(
                            subcategory_id,
                            group_id,
                            HierarchyPermissionLevel.CAN_CREATE_AND_EDIT,
                            addDate,
                            team_id
                        );
                    });

                    queries.push({
                        query,
                        params,
                    });
                }

                async.each(
                    queries,
                    (query, each_cb) => {
                        client.query(query.query, query.params, each_cb);
                    },
                    err2 => {
                        if (err2) {
                            water_cb(err2);
                        } else {
                            water_cb(null, subcategory);
                        }
                    }
                );
            },

            (subcategory, water_cb) => {
                const followers = options.followers || [];
                if (userid) {
                    followers.push(userid);
                }
                subcatFollowers
                    .addFollowersToSubcategoryAsync(followers, subcategory.id, {})
                    .then(() => water_cb(null, subcategory))
                    .catch(err => water_cb(err));
            },
            async function checkPaidPlan(subcategory, water_cb) {
                try {
                    const isPaidPlan = await entitlementService.checkEntitlement(team_id, EntitlementName.PaidPlan);
                    water_cb(null, [subcategory, isPaidPlan]);
                } catch (err) {
                    water_cb(err);
                }
            },
            ([subcategory, isPaidPlan], water_cb) => {
                if (isPaidPlan && !options.personal_list) {
                    const non_read_members = options.members.filter(
                        member => parseInt(member.permission_level, 10) !== 1
                    );
                    const userIds = non_read_members.map(user => user.userid);
                    billingNewUsersMod._billGuestsIfNew(userid, userIds, team_id, { client }, err => {
                        water_cb(err, subcategory);
                    });
                } else {
                    water_cb(null, subcategory);
                }
            },
            function copyViews(subcategory, water_cb) {
                if (!list_to_copy_views_from) {
                    water_cb(null, subcategory);
                } else {
                    subcatCopy.copySubcategoryViews(
                        -1, // ClickBot
                        list_to_copy_views_from.id,
                        subcategory.id,
                        client,
                        {},
                        err => {
                            water_cb(err, subcategory);
                        }
                    );
                }
            },
            function addObjectAclEntry(subcategory, water_cb) {
                const egressService = getEgressAccessInfoService();
                const hierarchyScopes = [
                    {
                        object_id: subcategory.id,
                        object_type: ObjectType.LIST,
                    },
                    {
                        object_id: category_id,
                        object_type: ObjectType.FOLDER,
                    },
                    {
                        object_id: project_id,
                        object_type: ObjectType.SPACE,
                    },
                ];
                const egressQuery = egressService.getUpsertObjectAclsQuery([
                    {
                        object_id: subcategory.id,
                        object_type: ObjectType.LIST,
                        workspace_id: team_id,
                        object_id_int: Number(subcategory.id),
                        user_acl: userAcl,
                        group_acl: {},
                        private: options.pvt || false,
                        archived: options.archived || false,
                        deleted: false,
                        hierarchy_scopes: hierarchyScopes,
                        version_vector: {},
                    },
                ]);
                client.versionUpdates.push(
                    userHierarchyVersionUpdateRequests({
                        objectType: ObjectType.LIST,
                        objectId: subcategory.id,
                        workspaceId: team_id,
                        operation: OperationType.CREATE,
                    })
                );
                client.query(egressQuery.query, egressQuery.params, err => {
                    if (err) {
                        water_cb(err);
                    } else {
                        water_cb(null, subcategory);
                    }
                });
            },
            async (subcategory, water_cb) => {
                if (options.client) {
                    water_cb(null, subcategory);
                    return;
                }

                try {
                    const txClient = new TransactionClientImpl(client, ovm);
                    events = await ovm.updateVersions(txClient, client.versionUpdates);
                } catch (ovmErr) {
                    logger.error({
                        msg: 'Failed to write OVM update',
                        OVM_CRITICAL: true,
                        ECODE: 'OVM_WS_130',
                        err: ovmErr,
                    });
                    water_cb(ovmErr);
                    return;
                }

                client.query('COMMIT', err => {
                    water_cb(err, subcategory);
                });
            },
            function getInboundAddress(subcategory, water_cb) {
                emailHelper.getSubcatAddresses(userid, [subcategory.id], {}, (err, result) => {
                    if (err) {
                        water_cb(err);
                    } else {
                        subcategory.inbound_address = result[subcategory.id];
                        water_cb(null, subcategory);
                    }
                });
            },
        ],
        async (err, subcategory) => {
            if (err && err.logError) {
                err.logError();
            }

            if (err) {
                if (done) {
                    db.rollback(client, done);
                }
                cb(err);
            } else if (options.client || options.return_id) {
                if (!options.client && done) {
                    done();
                }
                cb(null, { subcat_id: subcategory.id, team_id });
                elasticProducer.addSubcatsToES(userid, [subcategory.id], {
                    description_changed: Object.keys(options).includes('content'),
                });
                webhook.sendWebhookMessage('listCreated', { subcategory_id: subcategory.id, team_id });
            } else {
                done();
                if (events) {
                    await ovm.notifyChanges(events).catch(() => {});
                }
                teamMemberMod.invalidateGuestCount(team_id);
                cacheInvalidation.invalidateCachedSubcategoryIds(team_id);
                cacheInvalidation.invalidateCachedSharedTasksCount(team_id);

                elasticProducer.addSubcatsToES(userid, [subcategory.id], {
                    description_changed: Object.keys(options).includes('content'),
                });

                webhook.sendWebhookMessage('listCreated', { subcategory_id: subcategory.id, team_id });
                _getSubcategory(userid, subcategory.id, { text_content: options.text_content }, cb);
                privacy.insertSubcategoryPrivacy('privacy_changed', userid, subcategory.id, { private: options.pvt });
                sqsWs.sendWSMessage('sendSubcategoryCreated', [
                    { userid, category_id, subcategory, ws_key: options.ws_key },
                ]);

                if (options.override_statuses) {
                    projectHelper.setGroupedProjectsStatuses([project_id], {}, () => {});
                }
            }
        }
    );
}

export const promiseCreateSubcategory = async (userid, category_id, options = {}) =>
    new Promise((res, rej) => {
        _createSubcategory(userid, category_id, options, (err, result) => {
            if (err) rej(err);
            else res(result);
        });
    });

export function createSubcategory(req, resp, next) {
    const userid = getUserFromDecodedToken(req, 'createSubcategory');
    const { category_id, project_id } = req.params;
    const {
        name,
        content,
        position,
        color,
        due_date,
        due_date_time,
        start_date,
        start_date_time,
        private: pvt,
        members = [],
        validate_name,
        priority,
        status,
        assignee,
        assignees,
        group_assignees,
        sprint,
        sprint_start_date,
        sprint_end_date,
        sprint_index,
        override_statuses,
        statuses,
        custom_items_default,
        subcategory_type,
        default_permission_level,
    } = req.body;

    logger.info({
        msg: 'Create subcategory request',
        userid,
        category_id,
        project_id,
        name,
        position,
    });

    const options = {
        name,
        content,
        position,
        color,
        due_date,
        due_date_time,
        start_date,
        start_date_time,
        pvt,
        members,
        validate_name,
        priority,
        status,
        assignee,
        assignees,
        group_assignees,
        project_id,
        sprint,
        sprint_start_date,
        sprint_end_date,
        sprint_index,
        override_statuses,
        statuses,
        custom_items_default,
        subcategory_type,
        default_permission_level,
    };

    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    options.ws_key = ws_key;

    _createSubcategory(userid, category_id, options, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    });
}

export function _createSubcategoriesTransaction(userid, category_id, subcategories, client, ws_key, cb) {
    const subcat_ids = [];
    async.eachSeries(
        subcategories,
        (subcat, each_cb) => {
            const options = {
                name: subcat.name,
                sprint: subcat.sprint,
                sprint_start_date: subcat.sprint_start_date,
                sprint_end_date: subcat.sprint_end_date,
                sprint_index: subcat.sprint_index,
                position: subcat.position,
                client,
                ws_key,
            };

            // accepts client to allow transaction of creating all subcats
            _createSubcategory(userid, category_id, options, (createErr, eachResult) => {
                if (createErr) {
                    each_cb(createErr);
                } else {
                    subcat_ids.push(eachResult.subcat_id);
                    each_cb();
                }
            });
        },
        eachErr => {
            if (eachErr) {
                cb(eachErr);
            } else {
                cb(null, subcat_ids);
            }
        }
    );
}

export function _createSubcategories(userid, category_id, subcategories, ws_key, cb, options) {
    let events;
    const ovm = getObjectVersionManager();
    db.getConn(null, { label: 'bulk create subcats' }, (connErr, client, done) => {
        if (connErr) {
            cb(connErr);
        } else {
            client.query('BEGIN', () => {
                _createSubcategoriesTransaction(
                    userid,
                    category_id,
                    subcategories,
                    client,
                    ws_key,
                    async (err, subcat_ids) => {
                        if (err) {
                            db.rollback(client, done);
                            cb(err);
                        } else {
                            try {
                                const txClient = new TransactionClientImpl(client, ovm);
                                events = await ovm.updateVersions(txClient, client.versionUpdates);
                            } catch (ovmErr) {
                                logger.error({
                                    msg: 'Failed to write OVM update',
                                    OVM_CRITICAL: true,
                                    ECODE: 'OVM_WS_130',
                                    err: ovmErr,
                                });
                                db.rollback(client, done);
                                cb(ovmErr);
                                return;
                            }

                            client.query('COMMIT', async commitErr => {
                                if (commitErr) {
                                    logger.error({
                                        msg: 'Failed to commit bulk create sprints',
                                        err: commitErr,
                                        category_id,
                                    });
                                    db.rollback(client, done);
                                    cb(commitErr);
                                } else {
                                    done();
                                    if (events) {
                                        await ovm.notifyChanges(events).catch(() => {});
                                    }
                                    if (options?.return_ids_only) {
                                        cb(null, { subcategory_ids: subcat_ids });
                                        return;
                                    }
                                    _getSubcategories(
                                        userid,
                                        { subcategory_ids: subcat_ids, use_master: true },
                                        (getErr, result) => {
                                            if (getErr) {
                                                cb(getErr);
                                            } else {
                                                cb(null, result);
                                            }
                                        }
                                    );
                                }
                            });
                        }
                    }
                );
            });
        }
    });
}

export function createSubcategories(req, res, next) {
    const userid = req.decoded_token.user;
    const { category_id } = req.params;
    const { subcategories } = req.body;

    logger.info({
        msg: 'Create bulk subcategories request',
        userid,
        category_id,
    });

    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;

    _createSubcategories(userid, category_id, subcategories, ws_key, (err, result) => {
        if (err) {
            next(err);
        } else {
            res.status(201).send({ subcategories: result.subcategory_list });
        }
    });
}

export function _editSubcategory(userid, subcategory_id, options, cb) {
    const {
        archived,
        content,
        color,
        ws_key,
        priority,
        status,
        assignee,
        assignees,
        group_assignees,
        override_statuses,
        statuses = [],
        status_change_map = {},
        unset_status,
        sprint,
        sprint_index,
        sprint_status,
        sprint_start_date,
        sprint_end_date,
        sprint_date_done,
        sprint_date_progress,
        sprint_dashboard_id,
        hide_description,
        copy_fields = [],
        copy_locations = [],
        keep_creator,
        status_name,
        avatar,
        ydoc,
        custom_items_default,
    } = options;
    let { name, due_date, due_date_time, start_date, start_date_time, subcategory_type } = options;
    let setPrivate = options.private;
    let oldSubcategory = null;
    let encrypted = false;
    let client;
    let txClient;
    let done;
    let category;
    let folderless_list;
    let status_obj;
    let project_id;
    let team_id;
    let creator;
    let old_sprint;
    let is_archived;
    let is_personal_list;

    const customTaskTypeDefault = new CustomType(custom_items_default);

    const hist_ids = [];
    const ovm = getObjectVersionManager();

    if (name && !input.validateSectionName(name)) {
        cb(new SubcatError('List name invalid', 'SUBCAT_021', 400));
        return;
    }
    if (avatar && !input.validateAvatar(avatar)) {
        cb(new SubcatError('Avatar invalid', 'SUBCAT_207', 400));
        return;
    }

    if (subcategory_type) {
        if (!Object.values(SubcategoryType).includes(subcategory_type)) {
            cb(
                new SubcatError(
                    `Subcategory type invalid, supported types are ${Object.values(SubcategoryType)}`,
                    'SUBCAT_208',
                    400
                )
            );
            return;
        }

        if (typeof subcategory_type === 'string') {
            subcategory_type = SubcategoryType[subcategory_type];
        }

        if (isSubcategoryTypePickableOnlyByClickbot(subcategory_type) && userid !== config.get('clickbot_assignee')) {
            cb(new SubcatError('Subcategory type invalid', 'SUBCAT_213', 400));
            return;
        }
    }

    async.series(
        [
            function getEncrypted(series_cb) {
                const query = `
                    SELECT subcategories.*, categories.hidden AS folderless_list, categories.project_id
                    FROM task_mgmt.subcategories
                    INNER JOIN task_mgmt.categories
                        ON categories.id = subcategories.category
                    WHERE subcategories.id = $1`;

                db.readQuery(query, [subcategory_id], (err, result) => {
                    if (err) {
                        series_cb(new SubcatError(err));
                        return;
                    }

                    if (!result.rows.length) {
                        series_cb();
                        return;
                    }

                    [oldSubcategory] = result.rows;

                    // can remove this private change once sharing hits prod
                    if (oldSubcategory === setPrivate) {
                        options.private = null;
                        setPrivate = null;
                    }

                    encrypted = oldSubcategory.encrypted;
                    category = oldSubcategory.category;
                    folderless_list = oldSubcategory.folderless_list;
                    project_id = oldSubcategory.project_id;
                    old_sprint = oldSubcategory.sprint;
                    is_archived = oldSubcategory.archived;
                    is_personal_list = oldSubcategory.personal_list;
                    series_cb();
                });
            },
            function validateRequest(series_cb) {
                if (is_personal_list && name) {
                    cb(new SubcatError(`Personal List name can't be modified`, 'SUBCAT_206'));
                    return;
                }

                if (encrypted && name) {
                    name = encrypt.encrypt(name);
                }

                async.parallel(
                    {
                        access(para_cb) {
                            if (userid === config.get('clickbot_assignee')) {
                                para_cb();
                                return;
                            }

                            const convert_prop_to_permission = (value, property) => {
                                const conversion_map = {
                                    name: edit_list_details,
                                    archived: [can_archive, can_create_lists],
                                    content: edit_list_details,
                                    color: edit_list_details,
                                    status_name: edit_list_details,
                                    avatar: edit_list_details,
                                    status: edit_list_details,
                                    assignee: edit_list_details,
                                    priority: edit_list_details,
                                    due_date: edit_list_details,
                                    due_date_time: edit_list_details,
                                    start_date: edit_list_details,
                                    start_date_time: edit_list_details,
                                    sprint: edit_list_details,
                                    sprint_index: edit_list_details,
                                    sprint_status: edit_list_details,
                                    sprint_start_date: edit_list_details,
                                    sprint_end_date: edit_list_details,
                                    sprint_date_done: edit_list_details,
                                    sprint_date_progress: edit_list_details,
                                    sprint_dashboard_id: edit_list_details,
                                    override_statuses: [remove_status, add_status, manage_statuses],
                                    statuses: value?.length > 0 ? [remove_status, add_status, manage_statuses] : [],
                                    private: can_edit_privacy,
                                    custom_items_default: [edit_list_details, can_convert_item],
                                    subcategory_type: change_title,
                                    default_permission_level: can_set_default_permission_level,
                                };
                                return conversion_map[property];
                            };

                            const permissions = _.chain(options)
                                .map(convert_prop_to_permission)
                                .filter(_.identity)
                                .flatten()
                                .uniq()
                                .value();

                            access2.checkAccessSubcategory(userid, subcategory_id, { permissions }, para_cb);
                        },

                        checkPortfolioPaywall(para_cb) {
                            if (!assignee) {
                                para_cb();
                                return;
                            }

                            portfolioPaywall.validateUsageCb({ subcategory_id }, para_cb);
                        },

                        accessAssignee(para_cb) {
                            if (!assignee || assignee === 'none') {
                                para_cb();
                                return;
                            }

                            access2.checkAccessSubcategory(
                                assignee,
                                subcategory_id,
                                { permissions: [], checkJoined: false },
                                para_cb
                            );
                        },

                        validateName(para_cb) {
                            if (!name || options.validate_name === false) {
                                para_cb();
                                return;
                            }

                            let query;
                            const params = [name, subcategory_id];

                            if (folderless_list) {
                                query = `
                                    SELECT subcategories.id
                                    FROM task_mgmt.subcategories
                                    INNER JOIN task_mgmt.categories
                                        ON subcategories.category = categories.id
                                    WHERE categories.project_id = (
                                        SELECT categories.project_id
                                        FROM task_mgmt.subcategories
                                        INNER JOIN task_mgmt.categories
                                            ON categories.id = subcategories.category
                                        WHERE subcategories.id = $2
                                    )
                                        AND categories.hidden = TRUE
                                        AND categories.archived = FALSE
                                        AND categories.deleted = FALSE
                                        AND categories.template = FALSE
                                        AND subcategories.id != $2
                                        AND subcategories.archived = FALSE
                                        AND subcategories.template = FALSE
                                        AND subcategories.deleted = FALSE
                                        AND lower(subcategories.name) = lower($1)`;
                            } else {
                                query = `
                                    SELECT id
                                    FROM task_mgmt.subcategories
                                    WHERE LOWER(name) = LOWER($1)
                                        AND category = (
                                            SELECT category
                                            FROM task_mgmt.subcategories
                                            WHERE id = $2
                                        )
                                        AND archived = false
                                        AND deleted = false
                                        AND id != $2
                                        AND template = false`;
                            }

                            db.readQuery(query, params, (err, result) => {
                                if (err) {
                                    para_cb(new SubcatError(err, 'SUBCAT_018', 500));
                                } else if (result.rows.length > 0) {
                                    para_cb(new SubcatError('List name taken', 'SUBCAT_019', 400));
                                } else {
                                    para_cb();
                                }
                            });
                        },

                        validateStatuses(para_cb) {
                            if (!status && !color) {
                                para_cb();
                                return;
                            }

                            if (color && ['#fff', '#ffffff'].includes(color.toLowerCase())) {
                                para_cb();
                                return;
                            }

                            subcatStatuses.getSubcategoryStatuses(null, { subcategory_id }, (err, result) => {
                                if (err) {
                                    para_cb(new SubcatError(err, 'SUBCAT_100'));
                                    return;
                                }

                                status_obj = _.find(result, { status });

                                if (!status_obj) {
                                    status_obj = _.find(result, { color: color && color.toLowerCase() });
                                }

                                if (!status_obj) {
                                    para_cb(new SubcatError('Invalid status for team', 'SUBCAT_101'));
                                    return;
                                }

                                para_cb();
                            });
                        },

                        async validateCustomItem(para_cb) {
                            if (!customTaskTypeDefault.isCustomType()) {
                                para_cb();
                                return;
                            }

                            if (customTaskTypeDefault.isPredefinedCustomTypeRange()) {
                                if (!customTaskTypeDefault.isActualPredefinedCustomType()) {
                                    customTaskTypeDefault.value = undefined;
                                }
                                para_cb();
                                return;
                            }

                            try {
                                await customItemsMod.verifyHaveAccessToCustomItemViaEntity(
                                    customTaskTypeDefault.value,
                                    undefined,
                                    userid,
                                    { subcategory_id }
                                );

                                para_cb();
                            } catch (e) {
                                para_cb(e);
                            }
                        },

                        async checkNotEditingSprintDates(para_cb) {
                            try {
                                const changeSprintDates =
                                    (due_date && oldSubcategory.due_date !== due_date) ||
                                    (start_date && oldSubcategory.start_date !== start_date);
                                if (old_sprint && changeSprintDates) {
                                    const customSprintQuery = `SELECT
                                        COALESCE(teams.custom_sprint_duration, false) as custom_sprint_duration
                                        FROM task_mgmt.teams
                                        INNER JOIN task_mgmt.projects ON projects.team = teams.id
                                        INNER JOIN task_mgmt.categories ON categories.project_id = projects.id
                                        INNER JOIN task_mgmt.subcategories ON subcategories.category = categories.id
                                        WHERE subcategories.id = $1`;
                                    const customSprintResult = await db.readQueryAsync(customSprintQuery, [
                                        subcategory_id,
                                    ]);
                                    if (
                                        sprintCustomDuration(userid) ||
                                        (customSprintResult.rows.length > 0 &&
                                            customSprintResult.rows[0].custom_sprint_duration)
                                    ) {
                                        para_cb();
                                    } else {
                                        para_cb(new SubcatError(`Cannot edit sprint dates`, 'SUBCAT_038', 400));
                                    }
                                } else {
                                    para_cb();
                                }
                            } catch (e) {
                                para_cb(e);
                            }
                        },

                        getCreator(para_cb) {
                            if (!keep_creator) {
                                para_cb(null, {});
                                return;
                            }

                            db.replicaQuery(
                                `SELECT creator FROM task_mgmt.subcategories WHERE id =$1`,
                                [subcategory_id],
                                (err, result) => {
                                    if (err) {
                                        para_cb(err);
                                        return;
                                    }

                                    if (result && result.rows && result.rows.length) {
                                        const [{ creator: cat_creator }] = result.rows;
                                        creator = cat_creator;
                                    }

                                    para_cb();
                                }
                            );
                        },

                        async getTeamId(para_cb) {
                            try {
                                team_id = await getTeamId({
                                    subcategory_ids: [subcategory_id],
                                });

                                if (!team_id) {
                                    throw new SubcatError(`Cannot find team`, 'SUBCAT_098', 404);
                                }

                                para_cb();
                            } catch (error) {
                                para_cb(error);
                            }
                        },

                        async checkPlanLimits(para_cb) {
                            try {
                                await verifyListUsage(subcategory_id, { ...options }, team_id);
                                para_cb();
                            } catch (planErr) {
                                para_cb(planErr);
                            }
                        },

                        async maxSubcategories(para_cb) {
                            const unarchiving = is_archived === true && archived === false;
                            if (unarchiving) {
                                try {
                                    await checkListLimit(category, null, team_id, 1);
                                } catch (e) {
                                    para_cb(e);
                                    return;
                                }
                            }
                            para_cb();
                        },
                    },
                    err => series_cb(err)
                );
            },

            function getConn(series_cb) {
                db.getConn(cb, { label: 'edit subcategory' }, (clientErr, _client, _done) => {
                    if (clientErr) {
                        series_cb(clientErr);
                        return;
                    }
                    client = _client;
                    client.versionUpdates = [];
                    done = _done;
                    series_cb();
                });
            },
            async function begin(series_cb) {
                try {
                    txClient = new TransactionClientImpl(client, ovm);
                    await txClient.beginAsync();
                    series_cb();
                } catch (beginErr) {
                    series_cb({
                        err: 'Internal server error',
                        status: 500,
                        ECODE: '',
                    });
                }
            },
            function setStatuses(series_cb) {
                if (override_statuses !== true) {
                    series_cb();
                    return;
                }

                statusMod._editStatuses(
                    userid,
                    { subcategory_id, statusChangeMap: status_change_map, statuses, client, done },
                    series_cb
                );
            },
            function makeEdit(series_cb) {
                const queries = [];

                let query = 'UPDATE task_mgmt.subcategories SET ';
                const params = [];

                if (name) {
                    params.push(name);
                    query += `name = $${params.length}`;
                }

                if (sprint != null) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(sprint);
                    query += `sprint = $${params.length}`;
                }

                if (sprint_index != null) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(sprint_index);
                    query += `sprint_index = $${params.length}`;
                }

                if (sprint_status != null) {
                    const now = moment().tz('America/Los_Angeles').valueOf();
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(sprint_status === 'none' ? null : sprint_status);
                    query += `sprint_status = $${params.length}`;
                    if (sprint_status === 'done') {
                        params.push(now);
                        query += `, sprint_date_done = $${params.length}`;
                    }
                    if (sprint_status === 'in progress') {
                        params.push(now);
                        query += `, sprint_date_progress = $${params.length}`;
                    }
                }

                if (sprint_start_date) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(sprint_start_date);
                    query += `sprint_start_date = $${params.length}`;
                }

                if (sprint_end_date) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(sprint_end_date);
                    query += `sprint_end_date = $${params.length}`;
                }

                if (sprint_date_done != null) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(sprint_date_done === 'none' ? null : moment().tz('America/Los_Angeles').valueOf());
                    query += `sprint_date_done = $${params.length}`;
                }

                if (sprint_date_progress != null) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(sprint_date_progress === 'none' ? null : moment().tz('America/Los_Angeles').valueOf());
                    query += `sprint_date_progress = $${params.length}`;
                }
                if (sprint_dashboard_id != null) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(sprint_dashboard_id);
                    query += `sprint_dashboard_id = $${params.length}`;
                }

                if (content) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(content);
                    query += `content = $${params.length}`;
                }

                if (archived != null) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(archived);
                    query += `archived = $${params.length}`;
                }

                if (hide_description != null) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(hide_description);
                    query += `hide_description = $${params.length}`;
                }

                if (!customTaskTypeDefault.isUnset()) {
                    if (params.length > 0) {
                        query += ', ';
                    }

                    params.push(customTaskTypeDefault.value);
                    query += `custom_items_default = $${params.length}`;
                }

                if (assignee) {
                    if (params.length > 0) {
                        query += ', ';
                    }

                    params.push(assignee === 'none' ? null : assignee);
                    query += `assignee = $${params.length}`;
                }

                if (priority) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(priority === 'none' ? null : priority);
                    query += `priority = $${params.length}`;
                }

                if (color && !status_obj) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(color);
                    query += `color = $${params.length}`;
                }

                if (status_obj) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(status_obj.status, status_obj.color);
                    query += `status = $${params.length - 1}, color = $${params.length}`;
                } else if (unset_status) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(null);
                    query += `status = $${params.length}, color = $${params.length}`;
                }

                if (due_date != null) {
                    if (due_date === 'none') {
                        due_date = null;
                        due_date_time = false;
                    }
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(due_date);
                    query += `due_date = $${params.length}`;
                }

                if (due_date_time != null) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(due_date_time || false);
                    query += `due_date_time = $${params.length}`;
                }

                if (start_date != null) {
                    if (start_date === 'none') {
                        start_date = null;
                        start_date_time = null;
                    }
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(start_date);
                    query += `start_date = $${params.length}`;
                }

                if (start_date_time != null) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(start_date_time);
                    query += `start_date_time = $${params.length}`;
                }

                if (override_statuses === true) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(override_statuses, `subcat_${subcategory_id}`);
                    query += `override_statuses = $${params.length - 1}, status_group = $${params.length}`;
                }
                if (setPrivate != null) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(setPrivate);
                    query += `private = $${params.length}`;
                }

                if (subcategory_type) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(subcategory_type);
                    query += `subcategory_type = $${params.length}`;
                }

                if (setPrivate) {
                    if (params.length > 0) {
                        query += ', ';
                    }
                    params.push(userid);
                    query += `owner = $${params.length}`;

                    queries.push(
                        {
                            query: `
                                INSERT INTO
                                    task_mgmt.subcategory_members(subcategory, userid, date_added, permission_level, workspace_id)
                                VALUES ($1, $2, $3, $4, $5)
                                ON CONFLICT (subcategory, userid) DO UPDATE SET permission_level = $4
                            `,
                            params: [subcategory_id, userid, new Date().getTime(), 5, team_id],
                        },
                        {
                            query: `
                                DELETE FROM task_mgmt.followers
                                USING task_mgmt.items, task_mgmt.subcategories
                                WHERE items.id = followers.task_id
                                    AND subcategories.id = items.subcategory
                                    AND subcategories.id = $1
                                    AND followers.userid NOT IN (
                                        SELECT userid
                                        FROM task_mgmt.subcategory_members
                                        WHERE subcategory_members.subcategory = $1
                                    )
                                    AND followers.userid NOT IN (
                                        SELECT userid
                                        FROM task_mgmt.task_members
                                        WHERE items.id = task_members.task_id
                                    ) RETURNING task_id AS updated_task_id`,
                            params: [subcategory_id],
                        }
                    );

                    if (keep_creator && creator) {
                        queries.push({
                            query: `
                                INSERT INTO task_mgmt.subcategory_members(subcategory, userid, date_added, permission_level, workspace_id)
                                VALUES ($1, $2, $3, $4, $5)
                                ON CONFLICT DO NOTHING
                            `,
                            params: [subcategory_id, creator, new Date().getTime(), 5, team_id],
                        });
                    }
                }

                ({ query } = getUpdateQueryChanged(query, params, status_name, 'status_name'));
                ({ query } = getUpdateQueryChanged(query, params, avatar, 'avatar'));

                if (params.length) {
                    query += `
                    , date_updated = $${params.push(new Date().getTime())}
                    WHERE id = $${params.push(subcategory_id)}`;

                    queries.push({ query, params });
                }

                // Add in the other history items
                const history_fields = ['content', 'start_date', 'due_date', 'name', 'priority', 'status'];
                const hist_params = [];
                let hist_query =
                    'INSERT INTO task_mgmt.subcat_history(id, subcategory, field, date, before, after, userid, via, data, workspace_id) VALUES ';

                Object.keys(options).forEach(key => {
                    if (history_fields.includes(key)) {
                        let after = options[key];

                        const data = {};

                        if (key === 'start_date') {
                            data.old_start_date_time = oldSubcategory.start_date_time;
                            data.start_date_time = options.start_date_time;
                            if (after === 'none') {
                                after = null;
                            }
                        }

                        if (key === 'due_date') {
                            data.old_due_date_time = oldSubcategory.due_date_time;
                            data.due_date_time = options.due_date_time;
                            if (after === 'none') {
                                after = null;
                            }
                        }

                        if (after === oldSubcategory[key]) {
                            return;
                        }

                        if (hist_params.length > 0) {
                            hist_query += ', ';
                        }

                        const hist_id = uuid.v4();
                        hist_ids.push(hist_id);

                        hist_params.push(
                            hist_id,
                            subcategory_id,
                            key,
                            new Date().getTime(),
                            oldSubcategory[key],
                            after,
                            userid,
                            null,
                            data,
                            team_id
                        );
                        hist_query += ` ($${hist_params.length - 9}, $${hist_params.length - 8}, $${
                            hist_params.length - 7
                        }, $${hist_params.length - 6}, $${hist_params.length - 5}, $${hist_params.length - 4}, $${
                            hist_params.length - 3
                        }, $${hist_params.length - 2}, $${hist_params.length - 1}, $${hist_params.length}) `;
                    }
                });
                // TODO: Assigness added as follower and added history
                if (assignee) {
                    const after = options.assignee;
                    const data = {};
                    if (hist_params.length > 0) {
                        hist_query += ', ';
                    }

                    let hist_id = uuid.v4();
                    hist_ids.push(hist_id);

                    if (oldSubcategory.assignee) {
                        hist_params.push(
                            hist_id,
                            subcategory_id,
                            'assignee',
                            new Date().getTime(),
                            oldSubcategory.assignee,
                            null,
                            userid,
                            null,
                            data,
                            team_id
                        );
                        hist_query += ` ($${hist_params.length - 9}, $${hist_params.length - 8}, $${
                            hist_params.length - 7
                        }, $${hist_params.length - 6}, $${hist_params.length - 5}, $${hist_params.length - 4}, $${
                            hist_params.length - 3
                        }, $${hist_params.length - 2}, $${hist_params.length - 1}, $${hist_params.length}) `;

                        hist_query += ', ';
                    }

                    hist_id = uuid.v4();
                    hist_ids.push(hist_id);

                    hist_params.push(
                        hist_id,
                        subcategory_id,
                        'assignee',
                        new Date().getTime(),
                        null,
                        after,
                        userid,
                        null,
                        data,
                        team_id
                    );
                    hist_query += ` ($${hist_params.length - 9}, $${hist_params.length - 8}, $${
                        hist_params.length - 7
                    }, $${hist_params.length - 6}, $${hist_params.length - 5}, $${hist_params.length - 4}, $${
                        hist_params.length - 3
                    }, $${hist_params.length - 2}, $${hist_params.length - 1}, $${hist_params.length}) `;

                    if (assignee && assignee !== 'none') {
                        queries.push({
                            query: 'INSERT INTO task_mgmt.subcat_followers(subcategory, userid, workspace_id) (SELECT $1, $2, $3 WHERE NOT EXISTS (SELECT 1 FROM Task_mgmt.subcat_followers WHERE userid = $2 aND subcategory = $1))',
                            params: [subcategory_id, assignee, team_id],
                        });
                    }
                }

                if (hist_params.length > 0) {
                    queries.push({
                        query: hist_query,
                        params: hist_params,
                    });
                }

                if (override_statuses === false) {
                    queries.push(
                        {
                            query: 'DELETE FROM task_mgmt.statuses WHERE subcategory_id = $1',
                            params: [subcategory_id],
                        },
                        {
                            query: 'UPDATE task_mgmt.subcategories SET override_statuses = false, status_group = $1 WHERE id = $2 AND true = (SELECT override_statuses FROM task_mgmt.categories WHERE categories.id = subcategories.category)',
                            params: [`cat_${category}`, subcategory_id],
                        },
                        {
                            query: "UPDATE task_mgmt.subcategories SET override_statuses = false, status_group = 'proj_' || categories.project_id FROM task_mgmt.categories WHERE subcategories.id = $1 AND categories.id = subcategories.category AND (categories.override_statuses = false OR categories.override_statuses IS NULL)",
                            params: [subcategory_id],
                        }
                    );
                }

                if (content) {
                    queries.push(
                        prepareUpdateYdocQueries(
                            ContentEntityType.LIST,
                            oldSubcategory.id,
                            oldSubcategory.ydoc,
                            content,
                            ydoc
                        )
                    );
                }

                if (options.middleware_queries) {
                    queries.push(...options.middleware_queries);
                    const mqVersionUpdates = options.middleware_queries.reduce(
                        (acc, mq) => (mq.versionUpdates ? acc.concat(mq.versionUpdates) : acc),
                        []
                    );
                    if (mqVersionUpdates.length) {
                        client.versionUpdates.push(mqVersionUpdates);
                    }
                }

                const changes = buildListChanges(options, oldSubcategory);

                const parent = changes.some(change => change.field === 'private') ? category : null;
                client.versionUpdates.push(
                    hierarchyObjectVersionUpdateRequests({
                        objectType: ObjectType.LIST,
                        objectId: String(subcategory_id),
                        workspaceId: team_id,
                        operation: OperationType.UPDATE,
                        ws_key: options.ws_key,
                        userId: userid,
                        changes,
                        parent,
                        originatingService: options.originating_service_override,
                    })
                );

                async.eachSeries(
                    queries,
                    (queryObj, each_cb) => {
                        client.query(queryObj.query, queryObj.params, (eachErr, eachResult) => {
                            if (eachErr) {
                                each_cb(eachErr);
                                return;
                            }

                            if (team_id) {
                                for (const row of eachResult.rows) {
                                    if ('updated_task_id' in row) {
                                        client.versionUpdates.push({
                                            object_id: row.updated_task_id,
                                            object_type: ObjectType.TASK,
                                            workspace_id: team_id,
                                            operation: OperationType.UPDATE,
                                        });
                                    }
                                }
                            }
                            each_cb();
                        });
                    },
                    eachErr => {
                        if (eachErr) {
                            logger.error({
                                msg: 'Failed to update subcats',
                                err: eachErr,
                                status: 500,
                                ECODE: 'SUBCAT_011',
                            });
                            series_cb(new SubcatError(eachErr, 'SUBCAT_011', 500));

                            return;
                        }

                        series_cb();
                    }
                );
            },
            async function updateCanonicalChatViewChildren(series_cb) {
                try {
                    await updateCanonicalChatViews(
                        userid,
                        subcategory_id,
                        config.views.parent_types.subcategory,
                        name ?? oldSubcategory.name,
                        team_id,
                        archived
                    );
                    series_cb();
                } catch (err) {
                    series_cb({
                        err,
                        ECODE: 'SUBCAT_140',
                    });
                }
            },
            function addAssignees(series_cb) {
                if (!assignees && !group_assignees) {
                    series_cb();
                    return;
                }

                const queries = [];
                const addDate = new Date().getTime();
                if (assignees) {
                    if (assignees.add && assignees.add.length > 0) {
                        let query =
                            'INSERT INTO task_mgmt.subcat_followers (subcategory, userid, workspace_id) VALUES ';
                        let params = [];
                        assignees.add.forEach((user_id, index) => {
                            const counter = index * 3;
                            query += `($${counter + 1}, $${counter + 2}, $${counter + 3})`;
                            query += assignees.add.length === index + 1 ? ' ' : ', ';
                            params.push(subcategory_id, user_id, team_id);
                        });
                        query += ' ON CONFLICT (subcategory, userid) DO NOTHING';
                        queries.push({ query, params });

                        query =
                            'INSERT INTO task_mgmt.subcategory_assignee(subcategory_id, user_id, date_assigned, workspace_id) VALUES ';
                        params = [];

                        assignees.add.forEach((user_id, index) => {
                            const counter = index * 4;
                            query += `($${counter + 1}, $${counter + 2}, $${counter + 3}, $${counter + 4})`;
                            query += assignees.add.length === index + 1 ? ' ' : ', ';
                            params.push(subcategory_id, user_id, addDate, team_id);
                        });
                        query += ' ON CONFLICT DO NOTHING';
                        queries.push({ query, params });

                        query =
                            'INSERT INTO task_mgmt.subcategory_members(subcategory, userid, date_added, permission_level, workspace_id) VALUES ';
                        params = [];
                        assignees.add.forEach((user_id, index) => {
                            const counter = index * 5;
                            query += `($${counter + 1}, $${counter + 2}, $${counter + 3}, $${counter + 4}, $${
                                counter + 5
                            })`;
                            query += assignees.add.length === index + 1 ? ' ' : ', ';
                            params.push(
                                subcategory_id,
                                user_id,
                                addDate,
                                HierarchyPermissionLevel.CAN_CREATE_AND_EDIT,
                                team_id
                            );
                        });
                        query += ' ON CONFLICT DO NOTHING';
                        queries.push({ query, params });
                    }

                    if (assignees.rem && assignees.rem.length > 0) {
                        queries.push({
                            query: `DELETE FROM task_mgmt.subcat_followers WHERE subcategory = $1 AND userid = ANY($2)`,
                            params: [subcategory_id, assignees.rem],
                        });

                        queries.push({
                            query: `DELETE FROM task_mgmt.subcategory_assignee WHERE subcategory_id = $1 AND user_id = ANY($2)`,
                            params: [subcategory_id, assignees.rem],
                        });

                        queries.push({
                            query: `DELETE FROM task_mgmt.subcategory_members WHERE subcategory = $1 AND userid = ANY($2)`,
                            params: [subcategory_id, assignees.rem],
                        });
                    }
                }

                if (group_assignees) {
                    if (group_assignees.add && group_assignees.add.length > 0) {
                        let query1 =
                            'INSERT INTO task_mgmt.subcategory_group_assignee(subcategory_id, group_id, date_assigned, workspace_id) VALUES ';
                        let params1 = [];
                        group_assignees.add.forEach((group_id, index) => {
                            const counter = index * 4;
                            query1 += `($${counter + 1}, $${counter + 2}, $${counter + 3}, $${counter + 4})`;
                            query1 += group_assignees.add.length === index + 1 ? ' ' : ', ';
                            params1.push(subcategory_id, group_id, addDate, team_id);
                        });
                        query1 += 'ON CONFLICT (subcategory_id, group_id) DO NOTHING';
                        queries.push({ query: query1, params: params1 });

                        query1 =
                            'INSERT INTO task_mgmt.subcategory_group_members(subcategory, group_id, permission_level, date_added, workspace_id) VALUES ';
                        params1 = [];
                        group_assignees.add.forEach((group_id, index) => {
                            const counter = index * 5;
                            query1 += `($${counter + 1}, $${counter + 2}, $${counter + 3}, $${counter + 4}, $${
                                counter + 5
                            })`;
                            query1 += group_assignees.add.length === index + 1 ? ' ' : ', ';
                            params1.push(
                                subcategory_id,
                                group_id,
                                HierarchyPermissionLevel.CAN_CREATE_AND_EDIT,
                                addDate,
                                team_id
                            );
                        });
                        query1 += 'ON CONFLICT DO NOTHING';
                        queries.push({ query: query1, params: params1 });
                    }

                    if (group_assignees.rem && group_assignees.rem.length > 0) {
                        queries.push({
                            query: `DELETE FROM task_mgmt.subcategory_group_members WHERE subcategory = $1 AND group_id = ANY($2)`,
                            params: [subcategory_id, group_assignees.rem],
                        });

                        queries.push({
                            query: `DELETE FROM task_mgmt.subcategory_group_assignee WHERE subcategory_id = $1 AND group_id = ANY($2)`,
                            params: [subcategory_id, group_assignees.rem],
                        });
                    }
                }

                async.each(
                    queries,
                    (query, each_cb) => {
                        client.query(query.query, query.params, each_cb);
                    },
                    err2 => {
                        if (err2) {
                            series_cb(err2);
                        } else {
                            series_cb();
                        }
                    }
                );
            },
            async series_cb => {
                if (override_statuses == null) {
                    series_cb();
                    return;
                }
                try {
                    const query_obj = statusIDCorrection.updateStatusIdsForSubcategoryRequest(subcategory_id);
                    await batchQueriesSeriesAsyncThenUpdateTasksWithClient(
                        [query_obj],
                        txClient,
                        ovm,
                        team_id,
                        false,
                        client.versionUpdates,
                        { context: { ws_key } }
                    );
                    series_cb();
                } catch (err) {
                    series_cb(err);
                }
            },

            async function upsertDefaultLocationPermissions(series_cb) {
                if (
                    !isDefaultLocationPermissionsEnabled(team_id) ||
                    !isValidDefaultPermissionLevel(options.default_permission_level)
                ) {
                    series_cb();
                    return;
                }

                const queryObject = upsertObjectAccessInfoQueryObject({
                    objectId: subcategory_id,
                    objectType: ObjectType.LIST,
                    workspaceId: team_id,
                    defaultPermissionLevel: options.default_permission_level,
                });

                client.query(queryObject.query, queryObject.params, series_cb);
            },

            async function commit(series_cb) {
                try {
                    if (override_statuses == null && client.versionUpdates.length) {
                        const versionEvents = await ovm.updateVersions(txClient, client.versionUpdates);
                        txClient.recordEvents(versionEvents);
                    }
                    await txClient.commitAsync();
                    done();
                    series_cb();
                } catch (commitErr) {
                    series_cb({
                        err: 'Internal server error',
                        status: 500,
                        ECODE: '',
                    });
                }
            },
            series_cb => {
                if (copy_fields.length && copy_locations.length) {
                    genericCopy.copyObjects(
                        userid,
                        subcategory_id,
                        parent_types.subcategory,
                        copy_fields,
                        copy_locations,
                        {},
                        series_cb
                    );
                } else {
                    series_cb();
                }
            },
        ],
        err => {
            if (err) {
                db.rollback(client, done);
                cb(new SubcatError(err));
                return;
            }

            if (assignee) {
                portfolioPaywall.incrementUsageCountCb({ subcategory_id });
            }

            elasticProducer.addSubcatsToES(userid, [subcategory_id], {
                description_changed: Object.keys(options).includes('content'),
            });

            if (content && !options.skip_content_broadcast) {
                coeditorClientInstance
                    .postContentUpdate(subcategory_id, EntityType.LIST, content, team_id)
                    .catch(() => {});
            }

            _getSubcategory(userid, subcategory_id, { comment_count: true, replica: false }, (get_err, subcategory) => {
                if (!get_err) {
                    sqsWs.sendWSMessage('sendSubcategoryEdited', [{ subcategory, ws_key }]);
                }

                if (!get_err && setPrivate != null) {
                    sqsWs.sendWSMessage('sendSubcategoryPrivacyChange', [subcategory_id, setPrivate, options.ws_key]);

                    privacy.insertSubcategoryPrivacy('privacy_changed', userid, subcategory_id, {
                        private: setPrivate,
                    });
                }

                hist_ids.forEach(hist_id => {
                    sqsNotif.sendNotifMessage('createSubcategoryNotifications', [userid, subcategory_id, hist_id]);
                });

                webhook.sendWebhookMessage('listUpdated', { subcategory_id, hist_ids, team_id });

                if (override_statuses) {
                    projectHelper.setGroupedProjectsStatuses([project_id], {}, () => {});
                }

                cb(get_err, { subcategory });
            });
        }
    );
    if (archived != null) {
        try {
            reportTasksInSubcategoryChanged(userid, subcategory_id);
        } catch (err) {
            logger.error({ msg: 'Failed to report Subcategory Tasks Changed', err });
        }
    }
}

export function editSubcategoryAsync(userid, subcategory_id, options) {
    return new Promise((resolve, reject) => {
        _editSubcategory(userid, subcategory_id, options, (err, result) => {
            if (err) {
                reject(err);
            } else {
                resolve(result);
            }
        });
    });
}

export function editSubcategory(req, resp, next) {
    const userid = getUserFromDecodedToken(req, 'editSubcategory');
    const token_userid = req.decoded_token.user;

    const { subcategory_id } = req.params;
    const options = _.pick(req.body, [
        'name',
        'archived',
        'content',
        'color',
        'due_date',
        'due_date_time',
        'start_date',
        'start_date_time',
        'private',
        'validate_name',
        'status',
        'unset_status',
        'status_name',
        'avatar',
        'assignee',
        'assignees',
        'group_assignees',
        'priority',
        'override_statuses',
        'statuses',
        'status_change_map',
        'sprint',
        'sprint_index',
        'sprint_status',
        'sprint_start_date',
        'sprint_end_date',
        'sprint_date_done',
        'sprint_date_progress',
        'sprint_dashboard_id',
        'hide_description',
        'copy_fields',
        'copy_locations',
        'keep_creator',
        'custom_items_default',
        'subcategory_type',
        'default_permission_level',
    ]);

    options.ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    options.middleware_queries = req.middleware_queries || [];

    const originating_service_override =
        token_userid === config.get('clickbot_assignee') ? req.body.originating_service_override : undefined;
    if (originating_service_override) {
        options.originating_service_override = originating_service_override;
    }

    logger.info({
        msg: 'Edit subcategory request',
        userid,
        subcategory_id,
        options,
    });

    const workspaceId = getWorkspaceIdFromContext() || req.headers['x-workspace-id'];

    _editSubcategory(userid, subcategory_id, options, async (err, result) => {
        if (err) {
            next(err);
            return;
        }

        try {
            const v3Data = await callV3ListAPI(req, subcategory_id, workspaceId);

            const finalResult = getHierarchyV3Data(req, subcategory_id, result, v3Data, workspaceId);
            resp.status(200).send(finalResult);
        } catch (v3Err) {
            logger.error({
                ECODE: 'HIERARCHY_007',
                err: v3Err,
                msg: 'Failed to get response from hierarchy core v3 client',
            });

            if (
                v3Err?.code?.toUpperCase() === 'ECONNABORTED' ||
                v3Err?.code?.toUpperCase() === 'ECONNREFUSED' ||
                v3Err?.code?.toUpperCase() === 'EHOSTUNREACH' ||
                v3Err?.ECODE === 'AUTHN_003'
            ) {
                resp.status(200).send(result);
            } else if (result.deleted) {
                logger.warn({ msg: 'SubCategory is deleted', subcategory_id });
                resp.status(200).send(result);
            } else if (!result.deleted && v3Err?.ECODE === 'ACCESS_999') {
                logger.warn({
                    msg: 'SubCategory is not deleted but not found in v3 client',
                    subcategory_id,
                    err: v3Err,
                });
                resp.status(200).send(result);
            } else {
                next(v3Err);
            }
        }
    });
}

function getUpdateQueryChanged(query, params, propertyValue, propertyName) {
    // According to the current logic, if any value that needs to be set NULL, then it should be passed as "none"
    if (propertyValue != null) {
        if (propertyValue === 'none') {
            propertyValue = null;
        }
        if (params.length > 0) {
            query += ', ';
        }
        params.push(propertyValue);
        query += `${propertyName} = $${params.length}`;
    }
    return { query, params };
}

async function convertTaskTemplatesFromDeletedSubcategory(userid, subcategory_id) {
    let select;
    const params = [];
    const query = `
        SELECT id
        FROM task_mgmt.items
        WHERE
            subcategory = $${params.push(subcategory_id)}
            AND template = TRUE
            AND team_id IS NULL
    `;

    try {
        select = await db.promiseReplicaQuery(query, params);
    } catch (err) {
        logger.error({ msg: 'Failed to look up task templates to convert', err, ECODE: 'SUBCAT_160' });
        return;
    }
    if (!select.rows || !select.rows.length) {
        return;
    }
    const template_ids = select.rows.map(row => row.id);
    try {
        await taskTemplate._convertV1ToV2(userid, template_ids);
    } catch (err) {
        logger.error({ msg: 'Failed to convert task templates to v2', err, ECODE: 'SUBCAT_161' });
    }
}

/**
 * Deletes a given subcategory using a provided client.
 *
 * @param userid - the user ID making the request
 * @param subcategory_id - the ID of the subcategory to delete
 * @param client - the DB Client instance to run queries against
 * @param done
 * @param setDateDeleted
 * @param immediately
 * @param options
 * @param cb
 * @private
 */
export async function _deleteSubcategoryWithClient(
    userid,
    subcategory_id,
    client,
    done,
    setDateDeleted,
    immediately,
    options,
    cb
) {
    // Consider moving the logic to get the workspace_id further up the stack
    const workspace_id = await getTeamId({ subcategory_ids: [subcategory_id] }, {});
    if (!cb) {
        cb = options;
    }

    if (!cb) {
        cb = immediately;
        immediately = false;
    }

    let date_deleted = null;
    let categoryId;

    if (immediately) {
        date_deleted = 0;
    }

    if (setDateDeleted) {
        date_deleted = Date.now();
    }

    async.waterfall(
        [
            function getCategoryHidden(water_cb) {
                if (options.template_v2) {
                    water_cb(null, false);
                    return;
                }
                const query = `
                    SELECT
                        categories.hidden,
                        categories.id AS category_id
                    FROM
                        task_mgmt.categories,
                        task_mgmt.subcategories
                    WHERE subcategories.id = $1
                        AND categories.id = subcategories.category`;
                const params = [subcategory_id];
                client.query(query, params, (err, result) => {
                    if (err) {
                        db.rollback(client, done);
                        water_cb(new SubcatError(err, 'SUBCAT_036').logError());
                    } else if (!result.rows.length) {
                        db.rollback(client, done);
                        water_cb(new SubcatError('Not Found', 'SUBCAT_037', 404).logError());
                    } else {
                        categoryId = result.rows[0].category_id;
                        water_cb(null, result.rows[0].hidden);
                    }
                });
            },
            function setDeleted(hidden, water_cb) {
                let query;
                let params;
                if (hidden && !options.dont_delete_hidden_parent) {
                    query = `
                        UPDATE task_mgmt.subcategories
                        SET deleted = true
                        WHERE id = $1
                        RETURNING orderindex, boardindex, category`;
                    params = [subcategory_id];
                } else {
                    query = `
                        UPDATE task_mgmt.subcategories
                        SET deleted = true, date_deleted = $2, deleted_by = $3
                        WHERE id = $1
                        RETURNING orderindex, boardindex, category`;
                    params = [subcategory_id, date_deleted, userid];
                }

                client.query(query, params, (err, result) => {
                    if (err) {
                        db.rollback(client, done);
                        water_cb(new SubcatError(err, 'SUBCAT_007').logError());
                    } else if (!result.rows.length) {
                        db.rollback(client, done);
                        water_cb(new SubcatError('Not Found', 'SUBCAT_054', 404).logError());
                    } else {
                        client?.versionUpdates?.push(
                            hierarchyTreeVersionUpdateRequests({
                                objectType: ObjectType.FOLDER,
                                objectId: categoryId,
                                workspaceId: workspace_id,
                                operation: OperationType.DELETE,
                                ws_key: options.ws_key,
                            })
                        );
                        water_cb(null, result.rows[0].category);
                    }
                });
            },

            function setHiddenFolderDeleted(category_id, water_cb) {
                if (options.dont_delete_hidden_parent) {
                    water_cb(null, category_id);
                    return;
                }
                const query = `
                    UPDATE task_mgmt.categories
                    SET deleted = TRUE, date_deleted = $2, deleted_by = $3
                    WHERE id = $1
                    AND hidden = TRUE`;
                const params = [category_id, date_deleted, userid];

                client.query(query, params, err => {
                    if (err) {
                        water_cb(new SubcatError(err, 'SUBCAT_095'));
                    } else {
                        water_cb(null, category_id);
                    }
                });
            },

            function updateOrderindex(category_id, water_cb) {
                if (options.delete_all_subcategories) {
                    water_cb();
                    return;
                }
                const query = `
                    UPDATE task_mgmt.subcategories
                    SET orderindex = orderindex - 1
                    WHERE category = $1
                        AND orderindex > (SELECT orderindex FROM task_mgmt.subcategories WHERE id = $2)`;
                const params = [category_id, subcategory_id];

                client.query(query, params, err => {
                    if (err) {
                        db.rollback(client, done);
                        water_cb(new SubcatError(err, 'SUBCAT_008').logError());
                    } else {
                        water_cb();
                    }
                });
            },
            async function updateChildren(water_cb) {
                client?.versionUpdates?.push(
                    hierarchyObjectVersionUpdateRequests({
                        objectType: ObjectType.LIST,
                        objectId: String(subcategory_id),
                        workspaceId: workspace_id,
                        operation: OperationType.DELETE,
                        fanout: true,
                        ws_key: options.ws_key,
                    })
                );

                const taskCountQuery = `SELECT count(id)
                    FROM task_mgmt.tasks
                    WHERE
                        subcategory = $1`;
                const taskCountResult = await db.readQueryAsync(taskCountQuery, [subcategory_id]);

                if (taskCountResult.rows && taskCountResult.rows[0]?.count > 5000) {
                    // Batch delete all the tasks in background
                    convertTaskTemplatesFromDeletedSubcategory(userid, subcategory_id);
                    water_cb(null);
                    logger.debug({ msg: 'Batch deleting tasks for subcat', subcategory_id });
                    await deleteSubCategoryTasks(workspace_id, subcategory_id, immediately, userid);
                    logger.debug({ msg: 'Batch deletion tasks for subcat complete', subcategory_id });
                    webhook.sendWebhookMessage('listDeleted', { team_id: workspace_id, subcategory_id });
                } else {
                    const query = immediately
                        ? `UPDATE task_mgmt.items SET deleted = true, date_deleted = 0 WHERE items.id = ANY(select id FROM task_mgmt.tasks where subcategory = $1 AND (template = false OR template IS NULL)) RETURNING id, parent`
                        : `UPDATE task_mgmt.items SET deleted = true, date_deleted = NULL WHERE items.id = ANY(select id FROM task_mgmt.tasks where subcategory = $1 AND (template = false OR template IS NULL)) RETURNING id, parent`;
                    const params = [subcategory_id];
                    client.query(query, params, async (err, result) => {
                        if (err) {
                            db.rollback(client, done);
                            water_cb(new SubcatError(err, 'SUBCAT_009'));
                        } else {
                            convertTaskTemplatesFromDeletedSubcategory(userid, subcategory_id);
                            webhook.sendWebhookMessage('listDeleted', { team_id: workspace_id, subcategory_id });
                            if (result.rows.length) {
                                const taskIds = result.rows.flatMap(({ id, parent }) => (parent ? [] : id));
                                try {
                                    await logTaskDeletionMark(
                                        workspace_id,
                                        taskIds,
                                        userid,
                                        true,
                                        { list_ids: [subcategory_id] },
                                        client
                                    );
                                    const subcategoryIds = (await getAllSubcategoriesForTIML(taskIds)) || [];
                                    invalidateCachedSubcatTaskCount(subcategoryIds);
                                } catch (e) {
                                    logger.warn({ msg: 'task delete post action failed', subcategory_id, err: e });
                                }
                            }
                            water_cb();
                        }
                    });
                }
            },
            function selectChildViews(water_cb) {
                const { conversation, conversation_dm, conversation_group_dm, form } = config.views.view_types;
                const viewTypesToDelete = [conversation, conversation_dm, conversation_group_dm, form];
                async.parallel(
                    {
                        view_ids(para_cb) {
                            const query = `
                                SELECT view_id
                                FROM task_mgmt.views
                                WHERE parent_id = $1
                                AND deleted IS FALSE
                                AND parent_type = 6
                                AND type = ANY($2)
                            `;

                            const params = [subcategory_id, viewTypesToDelete];

                            db.readQuery(query, params, (err, result) => {
                                if (err) {
                                    water_cb(new SubcatError(err, 'SUBCAT_013').logError());
                                    return;
                                }
                                para_cb(
                                    null,
                                    result.rows.map(row => row.view_id)
                                );
                            });
                        },
                    },
                    (err, result) => {
                        if (err) {
                            db.rollback(client, done);
                            water_cb(new SubcatError(err, 'SUBCAT_014').logError());
                        } else {
                            water_cb(null, result.view_ids);
                        }
                    }
                );
            },

            function deleteChildViews(view_ids, water_cb) {
                async.parallel(
                    {
                        async deleteViews() {
                            await deleteViewService.deleteViewsAsync(userid, view_ids, {});
                        },
                    },
                    (err, result) => {
                        if (err) {
                            db.rollback(client, done);
                            water_cb(new SubcatError(err, 'SUBCAT_015').logError());
                        } else {
                            water_cb(null, result.view_ids);
                        }
                    }
                );
                water_cb();
            },

            async function moveOrphanedDocs(waterCb) {
                try {
                    await unParentChildDocs(workspace_id, userid, subcategory_id, entityType, client);
                } catch (err) {
                    logger.error({
                        msg: 'An error occurred while attempting to move orphaned child docs',
                        ECODE: 'SUBCAT_235',
                        workspace_id,
                        userid,
                        subcategory_id,
                        err,
                    });
                }
                waterCb(null);
            },
        ],
        err => cb(err)
    );
}

async function deleteSubCategoryTasks(workspaceId, subcategory_id, immediately, userId) {
    let breakLoop = false;
    let loopCount = 0;
    const batchSize = 5000;
    try {
        while (!breakLoop) {
            const offset = loopCount * batchSize;

            const query = immediately
                ? `UPDATE task_mgmt.items SET deleted = true, date_deleted = 0 WHERE items.id = ANY(select id FROM task_mgmt.tasks where subcategory = $1 AND (template = false OR template IS NULL) ORDER BY id OFFSET $2 LIMIT $3) RETURNING id, parent`
                : `UPDATE task_mgmt.items SET deleted = true, date_deleted = NULL WHERE items.id = ANY(select id FROM task_mgmt.tasks where subcategory = $1 AND (template = false OR template IS NULL) ORDER BY id OFFSET $2 LIMIT $3) RETURNING id, parent`;
            const params = [subcategory_id, offset, batchSize];
            const result = await writeAsync(query, params);
            loopCount++;

            if (!result.rowCount || result.rowCount === 0 || result.rowCount < batchSize) breakLoop = true;

            if (result.rows.length) {
                const taskIds = result.rows.flatMap(({ id, parent }) => (parent ? [] : id));
                try {
                    await logTaskDeletionMark(workspaceId, taskIds, userId, true, { list_ids: [subcategory_id] });
                    const subcategoryIds = (await getAllSubcategoriesForTIML(taskIds)) || [];
                    invalidateCachedSubcatTaskCount(subcategoryIds);
                } catch (e) {
                    logger.warn({ msg: 'task delete post action failed', subcategory_id, err: e });
                }
            }
        }
    } catch (e) {
        logger.error({ msg: 'SUBCAT_009: failed to batch delete tasks in subcat', subcategory_id });
        const catQuery = `SELECT categories.id, categories.hidden
            FROM task_mgmt.categories, task_mgmt.subcategories
            WHERE subcategories.id = $1 AND categories.id = subcategories.category;`;
        const catResult = await db.readQueryAsync(catQuery, [subcategory_id]);
        if (catResult.rows && catResult.rows[0]?.hidden) {
            await undoDeleteCategory(userId, catResult.rows[0].id);
        } else {
            await subcatUndoMod._undoDelete(userId, subcategory_id);
        }
        logger.warn({ msg: 'List has been reverted', subcategory_id });
    }
}

/**
 * A copy of task/deleteDatastore.ts#logTaskDeletionMark() to be used with lists.
 * @param workspaceId
 * @param taskIds
 * @param actorId
 * @param isMarkedForDeletion
 * @param context
 * @param [client]
 * @return {Promise<unknown>}
 */
async function logTaskDeletionMark(workspaceId, taskIds, actorId, isMarkedForDeletion, context, client) {
    const queryObj = prepareMarkTaskForDeletionLogQuery(workspaceId, taskIds, actorId, isMarkedForDeletion, context);
    if (queryObj === undefined) {
        return;
    }

    const { query, params } = queryObj;
    if (client) {
        await queryAsyncWithPGClient(client, query, params);
        return;
    }
    await writeAsync(query, params);
}

/**
 * @param userid
 * @param subcategory_id
 * @param options
 * @param cb
 * @private
 */
export function _deleteSubcategory(userid, subcategory_id, options, cb) {
    if (!cb) {
        cb = options;
    }

    const versionUpdates = [];
    let events = [];
    async.waterfall(
        [
            async function checkAccess(water_cb) {
                if (options.template_v2) {
                    water_cb();
                    return;
                }
                let team_id;
                try {
                    team_id = await getTeamId({
                        subcategory_ids: [subcategory_id],
                    });
                } catch {
                    water_cb(new SubcatError(`Cannot find team`, 'SUBCAT_153', 404));
                    return;
                }

                let personalListData;
                try {
                    personalListData = await getPersonalListHierarchyIds(userid, team_id);
                } catch (err) {
                    if (err instanceof PersonalListError) {
                        personalListData = null;
                    } else {
                        water_cb(err);
                        return;
                    }
                }
                if (personalListData?.category?.subcategory?.id === subcategory_id) {
                    water_cb(new SubcatError(`You don't have permission to do this action`, 'SUBCAT_154', 401));
                    return;
                }

                access2.checkAccessSubcategory(userid, subcategory_id, { can_delete: true, permissions: [] }, err =>
                    water_cb(err)
                );
            },

            function getClient(water_cb) {
                db.getCopyConn(null, { label: '_deleteSubcategory' }, (err, client, done) => {
                    if (err) {
                        done();
                        water_cb(new SubcatError(err, 'SUBCAT_067').logError());
                    } else {
                        client.versionUpdates = [];
                        water_cb(null, client, done);
                    }
                });
            },

            function beginTransation(client, done, water_cb) {
                client.query('BEGIN', err => {
                    if (err) {
                        db.rollback(client, done);
                        water_cb(new SubcatError(err, 'SUBCAT_006').logError());
                    } else {
                        water_cb(null, client, done);
                    }
                });
            },

            async function deleteSubcat(client, done, water_cb) {
                await _deleteSubcategoryWithClient(userid, subcategory_id, client, done, true, false, options, err => {
                    if (err) {
                        // rollback is handled in parent func
                        water_cb(new SubcatError(err, 'SUBCAT_068').logError());
                    } else {
                        water_cb(null, client, done);
                    }
                });
            },

            async function commitTransaction(client, done, water_cb) {
                const ovm = getObjectVersionManager();
                if (client.versionUpdates?.length) {
                    versionUpdates.push(...client.versionUpdates);
                }

                if (versionUpdates.length > 0) {
                    try {
                        const txClient = new TransactionClientImpl(client, ovm);
                        events = await ovm.updateVersions(txClient, versionUpdates);
                    } catch (err2) {
                        water_cb(new SubcatError(err2, 'SUBCAT_010'));
                        return;
                    }
                }

                client.query('COMMIT', async err => {
                    if (err) {
                        db.rollback(client, done);
                        water_cb(new SubcatError(err, 'SUBCAT_010').logError());
                    } else {
                        // eslint-disable-next-line @typescript-eslint/no-empty-function
                        await ovm.notifyChanges(events).catch(() => {});
                        done();
                        water_cb();
                    }
                });
            },
        ],
        err => {
            if (err) {
                cb(err);
            } else {
                cb(null, {});
                sqsWs.sendWSMessage('sendSubcategoryDeleted', [{ subcategory_id }]);
            }
        }
    );
}

export function promiseDeleteSubcategory(userid, subcategory_id, options = {}) {
    return new Promise((res, rej) => {
        _deleteSubcategory(userid, subcategory_id, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}

export function deleteSubcategory(req, resp, next) {
    const userid = req.decoded_token.user;
    const { subcategory_id } = req.params;

    if (req.headers['user-agent'] && req.headers['user-agent'].includes('Alamofire') && config.env !== 'dev') {
        // block 2.13 and down if not staging
        try {
            const user_agent = req.headers['user-agent'];
            const version = Number(user_agent.split(' ')[0].split('/')[1]);
            if (version < 2.14) {
                next(new SubcatError('iOS cannot delete lists', 'SUBCAT_047', 400, { userid, subcategory_id }));
                return;
            }
        } catch (e) {
            next(new SubcatError('iOS cannot delete lists', 'SUBCAT_047', 400, { userid, subcategory_id }));
            return;
        }
    }

    logger.info({
        msg: 'Delete subcategory request',
        userid,
        subcategory_id,
    });

    _deleteSubcategory(userid, subcategory_id, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(202).send(result);
        }
    });
}

export function getMaxSubcategoriesAllowedInCategory() {
    return config.max.subcategories;
}

function checkSubcategoryStatusesAreValid(subcategory, cb) {
    if (!statusLengthLimit().validate?.subcategory) {
        return true;
    }

    if (subcategory.override_statuses) {
        const invalidStatus = subcategory.statuses.find(({ status }) => !input.validateStatus(status));

        if (invalidStatus) {
            cb(new SubcatError('Status is invalid', StatusErrors.StatusIsInvalid, 400, { invalidStatus }).logError());
            return false;
        }
    }

    return true;
}
