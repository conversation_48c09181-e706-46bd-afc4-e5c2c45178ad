import config from 'config';
import uuid from 'node-uuid';
import _ from 'lodash';
import { ObjectType, OperationType } from '@time-loop/ovm-object-version';
import { shouldLimitChecklistItems } from '@clickup-legacy/models/integrations/split/squadTreatments/taskTreatments';
import { getLogger } from '@clickup/shared/utils-logging';
import { ItemQueryKey, QueryFamily } from '@clickup/query-proxy';
import { getObjectVersionManager } from '@clickup/object-version-manager-singleton';
import { ClickUpError } from '../utils/errors';
import * as access from '../utils/access2';
import * as automation from '../automation/queue/send';
import * as cf_sign from '../utils/cf_sign';
import * as checklistTemplate from './templates/checklistTemplate';
import * as db from '../utils/db';
import * as encrypt from '../utils/encrypt';
import * as sqsNotif from '../utils/sqs-notif';
import * as sqsWs from '../utils/v1-ws-message-sending';
import * as subcatHelper from './subcategory/helpers';
import * as templateHelpers from './templates/templateHelpers';
import * as userInitials from '../utils/userInitials';
import * as groupMod from './groups';
import * as promises from '../utils/promises';

import {
    batchQueriesAsync,
    batchQueriesSeriesAsync,
    writeAsync,
    batchQueriesWithClientAsync,
    getTransactionClientAsync,
} from '../utils/db2';

import { TransactionClientImpl } from '../utils/transaction/TransactionClientImpl';
import * as entitiesService from '../utils/entities/services/entitiesService';
import * as async from '../utils/asyncHelper';
import { queryProxyFactory } from '../utils/queryProxy/queryProxyFactory';
import {
    shouldUseRefactoredMoveChecklistItem,
    shouldLimitTaskChecklists,
} from './integrations/split/squadTreatments/taskTreatments';
import { doesUserWantToFollowTaskEdits } from './task/helpers/userWatcherPreferences';
import { DurationMetricObject } from './templates/templateMetricsCommons';
import { TemplateTracer } from './templates/templateTracer';
import { reportTaskChanged } from './elastic/services/change-detector/hooks';
import { CURRENT_SHARD_ID } from '../config/domains/shardingConfig';
import { getVersionUpdateForTask } from './task/helpers/versionUpdateHelper';
import { trashRetentionPolicyHelper } from '../utils/trash/trashRetentionPolicy';
import { metricsClient } from '../metrics/metricsClient';
import { EntitlementName, entitlementService } from './entitlements/entitlementService';
import * as webhook from '../utils/webhook';

const logger = getLogger('checklist');

const CheckError = ClickUpError.makeNamedError('checklist');
const logCheckError = ClickUpError.getIsolatedErrorHandler('checklist');

const { edit_checklists, delete: _delete } = config.permission_constants;

async function allChecklistsResolved(task_id) {
    const query = `
        SELECT 1
        FROM task_mgmt.checklists
        INNER JOIN task_mgmt.checklist_items
            ON checklist_items.checklist_id = checklists.id
        WHERE checklists.task_id = $1
            AND checklists.deleted IS NOT TRUE
            AND checklist_items.deleted IS NOT TRUE
            AND checklist_items.resolved IS NOT TRUE`;
    const params = [task_id];

    try {
        // use master
        const { rows } = await db.promiseReadQuery(query, params, { use_master: true });
        return !rows.length;
    } catch (err) {
        throw new CheckError(err, 'CHECK_045');
    }
}

async function triggerAutomationIfNeeded(task_ids, team_id, options) {
    await promises.promiseMap(task_ids, async task_id => {
        const taskChecklistsResolved = await allChecklistsResolved(task_id);

        if (!taskChecklistsResolved) {
            return;
        }

        automation.triggerEvent(
            'task',
            { task_id, team_id, trigger: 'checklists_resolved', __skip_perform: true },
            { auto_id: options.auto_id }
        );
    });
}

async function checkTaskChecklistLimit(task_ids, options) {
    if (!shouldLimitTaskChecklists()) {
        return;
    }
    const team_id = options?.team_id ?? (await entitiesService.getTeamId({ task_ids }));
    const additionalChecklists = options?.additionalChecklists ?? 0;

    const query = options?.client?.query.bind(options?.client) ?? db.promiseReplicaQuery;
    const { rows } = await query(
        'SELECT COUNT(*) FROM task_mgmt.checklists WHERE task_id = ANY($1) AND deleted IS NOT TRUE',
        [task_ids]
    );
    await entitlementService.checkEntitlementLimit(
        team_id,
        EntitlementName.TaskChecklistLimit,
        Number(rows[0].count) + additionalChecklists,
        { throwOnDeny: true }
    );
}
export { checkTaskChecklistLimit };

function _createChecklist(userid, task_id, _options, cb) {
    const options = _options;
    let team_id;

    async.parallel(
        {
            access(para_cb) {
                if (options.skip_access) {
                    para_cb();
                    return;
                }
                access.checkAccessTask(
                    userid,
                    task_id,
                    { permissions: [config.permission_constants.add_checklists] },
                    para_cb
                );
            },
            async limits(para_cb) {
                try {
                    team_id = await entitiesService.getTeamId({
                        task_ids: [task_id],
                    });
                    await checkTaskChecklistLimit([task_id], { ...options, additionalChecklists: 1, team_id });
                    para_cb();
                } catch (err) {
                    para_cb(err);
                }
            },
            orderindex(para_cb) {
                if (options.orderindex != null) {
                    para_cb();
                    return;
                }

                db.replicaQuery(
                    'SELECT coalesce(MAX(orderindex), 0) as orderindex FROM task_mgmt.checklists WHERE task_id = $1',
                    [task_id],
                    (err, result) => {
                        if (err) {
                            para_cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'CHECK_001',
                            });
                        } else {
                            options.orderindex = Number(result.rows[0].orderindex) + 1;
                            para_cb();
                        }
                    }
                );
            },
            teamEncrypted(para_cb) {
                db.replicaQuery(
                    'SELECT teams.should_encrypt FROM task_mgmt.teams, task_mgmt.projects, task_mgmt.categories, task_mgmt.subcategories, task_mgmt.items WHERE items.id = $1 AND items.subcategory = subcategories.id AND subcategories.category = categories.id AND categories.project_id = projects.id AND projects.team = teams.id',
                    [task_id],
                    para_cb
                );
            },
        },
        (err, result) => {
            if (err) {
                cb(err);
                return;
            }

            let encrypted = false;

            if (
                config.encryption.checklists &&
                result.teamEncrypted.rows[0] &&
                result.teamEncrypted.rows[0].should_encrypt
            ) {
                options.name = encrypt.encrypt(options.name);
                encrypted = true;
            }

            const checklist_id = uuid.v4();

            const query =
                'INSERT INTO task_mgmt.checklists(id, task_id, name, creator, date_created, orderindex, show_completed, template, deleted, encrypted, workspace_id) VALUES ($1, $2, $3, $4, $5, $6, $7, false, false, $8, (SELECT workspace_id FROM task_mgmt.tasks where id = $2)) RETURNING *';
            const params = [
                checklist_id,
                task_id,
                options.name,
                userid,
                new Date().getTime(),
                options.orderindex,
                options.show_completed,
                encrypted,
            ];

            async.waterfall(
                [
                    async wf_cb => {
                        const versionRequests = getTaskVersionUpdates(
                            task_id,
                            team_id,
                            OperationType.UPDATE,
                            options.ws_key,
                            'CHECK_60'
                        );

                        if (options.client) {
                            options.client.query(query, params, async (createErr, createResult) => {
                                if (createErr) {
                                    wf_cb(createErr);
                                    return;
                                }

                                let events;
                                const ovm = getObjectVersionManager();
                                try {
                                    const txClient = new TransactionClientImpl(options.client, ovm);
                                    events = await ovm.updateVersions(txClient, versionRequests);
                                } catch (versionerr) {
                                    wf_cb(versionerr);
                                    return;
                                }

                                // eslint-disable-next-line @typescript-eslint/no-empty-function
                                await ovm.notifyChanges(events).catch(() => { });
                                wf_cb(null, createResult);
                            });
                        } else {
                            let createResult;

                            try {
                                createResult = await writeAsync(query, params, versionRequests);
                            } catch (error) {
                                wf_cb(error);
                                return;
                            }

                            wf_cb(null, createResult);
                        }
                    },
                ],
                (err2, result2) => {
                    if (err2) {
                        return cb({
                            err: 'Internal server error',
                            status: 500,
                            ECODE: 'CHECK_002',
                        });
                    }
                    const row = result2.rows[0];
                    const taskVersionUpdated = getVersionUpdateForTask(
                        task_id,
                        team_id,
                        result2.__version_change_events
                    );

                    if (encrypted) {
                        row.name = encrypt.decrypt(row.name);
                    }

                    const checklist = {
                        id: row.id,
                        task_id: row.task_id,
                        name: row.name,
                        orderindex: row.orderindex,
                        show_completed: row.show_completed,
                        resolved: 0,
                        unresolved: 0,
                        items: [],
                    };

                    if (!options.skip_notify) {
                        sqsWs.sendWSMessage('sendChecklistCreated', [
                            userid,
                            task_id,
                            checklist,
                            {
                                ws_key: options.ws_key,
                            },
                        ]);
                    }

                    return cb(null, {
                        checklist,
                        version: taskVersionUpdated,
                    });
                }
            );
        }
    );
}
export { _createChecklist };

export function createChecklist(req, resp) {
    const userid = req.decoded_token.user;
    const { task_id } = req.params;
    const { name, orderindex, show_completed } = req.body;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;

    _createChecklist(
        userid,
        task_id,
        {
            name,
            orderindex,
            show_completed,
            ws_key,
        },
        (err, result) => {
            if (err) {
                resp.status(err.status).send({
                    err: err.err,
                    ECODE: err.ECODE,
                });
            } else {
                resp.status(200).send(result);
            }
        }
    );
}

function _deleteChecklist(userid, checklist_id, options, cb) {
    let task_id;

    async.parallel(
        {
            getTaskId(para_cb) {
                db.replicaQuery(
                    'SELECT checklists.task_id FROM task_mgmt.checklists WHERE checklists.id = $1',
                    [checklist_id],
                    (err, result) => {
                        if (err) {
                            para_cb(err);
                            return;
                        }

                        // CLK-76439 checklist is possible to not associated with a task
                        if (result.rows.length) {
                            [{ task_id }] = result.rows;
                        }
                        para_cb();
                    }
                );
            },
            async access(para_cb) {
                if (options.skip_access) {
                    para_cb(null, {});
                    return;
                }
                let isV1;
                try {
                    isV1 = await checklistTemplate._checkTemplateV1(userid, checklist_id);
                } catch (err) {
                    // dont error
                }
                if (!isV1) {
                    try {
                        await access.promiseAccessChecklistTemplate(userid, checklist_id);
                        para_cb();
                        return;
                    } catch (err) {
                        // fall through
                    }
                }
                access.checkAccessChecklist(
                    userid,
                    checklist_id,
                    { permissions: [edit_checklists, _delete], include_subcat_map: true, is_delete: true },
                    (err, permissions, permissionResult) => {
                        if (err) para_cb(err);
                        else {
                            para_cb(null, { permissions, permissionResult });
                        }
                    }
                );
            },
        },
        async (err, results) => {
            if (err) {
                cb(err);
                return;
            }

            const queries = [
                {
                    query: `
                        UPDATE task_mgmt.checklists
                        SET deleted = true, date_deleted = $2
                        WHERE id = $1
                        RETURNING task_id`,
                    params: [checklist_id, new Date().getTime()],
                },
                {
                    query: 'UPDATE task_mgmt.checklist_items SET deleted = true WHERE checklist_id = $1',
                    params: [checklist_id],
                },
            ];

            let versionRequests = [];
            if (task_id) {
                const team_id = await entitiesService.getTeamId({
                    task_ids: [task_id],
                });
                versionRequests = getTaskVersionUpdates(
                    task_id,
                    team_id,
                    OperationType.UPDATE,
                    options.ws_key,
                    'CHECK_61'
                );
            }

            try {
                const { __version_change_events: events } = await batchQueriesAsync(
                    queries,
                    undefined,
                    versionRequests
                );
                const taskVersionUpdated = events?.find(
                    event =>
                        event.object_id === task_id &&
                        event.operation === OperationType.UPDATE &&
                        event.object_type === ObjectType.TASK
                );
                cb(null, {
                    version: taskVersionUpdated,
                });

                if (task_id) {
                    sqsWs.sendWSMessage('sendChecklistDeleted', [
                        userid,
                        task_id,
                        checklist_id,
                        {
                            ws_key: options.ws_key,
                        },
                    ]);
                }

                if (results.access) {
                    const { permissionResult } = results.access;
                    if (permissionResult && permissionResult.subcategory_map) {
                        subcatHelper.invalidateCachedSubcatTaskCount(Object.values(permissionResult.subcategory_map));
                    }
                }
            } catch (error) {
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'CHECK_003',
                });
            }
        }
    );
}
export { _deleteChecklist };

export function promiseDeleteChecklist(userid, checklist_id, options = {}) {
    return new Promise((res, rej) => {
        _deleteChecklist(userid, checklist_id, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}

export function deleteChecklist(req, resp) {
    const userid = req.decoded_token.user;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    const { checklist_id } = req.params;

    _deleteChecklist(userid, checklist_id, { ws_key }, (err, result) => {
        if (err) {
            resp.status(err.status).send({ err: err.err, ECODE: err.ECODE });
        } else {
            resp.status(200).send(result);
        }
    });
}

function treeify(_list) {
    const list = _list;
    const treeList = [];
    const lookup = {};
    for (let i = 0; i < list.length; i += 1) {
        lookup[list[i].id] = list[i];
        list[i].children = [];
    }
    list.forEach(obj => {
        if (obj.parent != null && lookup[obj.parent]) {
            lookup[obj.parent].children.push(obj);
            lookup[obj.parent].children.sort((a, b) => a.orderindex - b.orderindex);
        } else {
            treeList.push(obj);
        }
    });
    treeList.sort((a, b) => a.orderindex - b.orderindex);
    return treeList;
}
export { treeify };

function setChecklistItemsOrderindexes(index_map) {
    if (!index_map || Object.keys(index_map).length === 0) {
        return;
    }

    const queries = [];
    Object.keys(index_map).forEach(id => {
        queries.push({
            query: 'UPDATe task_mgmt.checklist_items SET orderindex = $1 WHERE id = $2',
            params: [index_map[id], id],
        });
    });

    async.eachSeries(
        queries,
        (query, each_cb) => {
            db.writeQuery(query.query, query.params, each_cb);
        },
        () => { }
    );
}

function _getChecklists(userid, task_id, options, cb) {
    let returnItem;
    const orderindexes_to_set = {};

    const { proxyClient = queryProxyFactory() } = options;

    if (!task_id && !options.checklist_id) {
        cb({ err: 'A task id or checklist id must be given', status: 400, ECODE: 'CHECK_033' });
        return;
    }

    async.parallel(
        {
            access(para_cb) {
                if (options.skip_access || !task_id) {
                    para_cb();
                    return;
                }
                access.checkAccessTask(userid, task_id, { permissions: [] }, para_cb);
            },
            async accessChecklist(para_cb) {
                if (options.skip_access || !options.checklist_id) {
                    para_cb();
                    return;
                }
                if (options.templates && options.isV1 === false) {
                    try {
                        await access.promiseAccessChecklistTemplate(userid, options.checklist_id);
                    } catch (err) {
                        para_cb(err);
                        return;
                    }
                    para_cb();
                    return;
                }
                access.checkAccessChecklist(userid, options.checklist_id, { permissions: [] }, para_cb);
            },
            checklists(para_cb) {
                const params = [];
                if (task_id) {
                    params.push(task_id);
                }
                if (options.checklist_id) {
                    params.push(options.checklist_id);
                }

                const queryFunc = options.replica
                    ? proxyClient.replicaQuery.bind(proxyClient)
                    : proxyClient.readQuery.bind(proxyClient);

                queryFunc(
                    {
                        queryFamily: QueryFamily.Item,
                        queryKey: ItemQueryKey.GetChecklists,
                        queryParams: params,
                        options: {
                            task_id,
                            checklist_id: options.checklist_id,
                            templates: options.templates,
                        },
                    },
                    async (err, result) => {
                        if (err) {
                            logger.error({
                                msg: 'Failed to get checklists',
                                status: 500,
                                ECODE: 'CHECK_004',
                                err,
                                userid,
                                task_id,
                            });
                            para_cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'CHECK_004',
                            });
                            return;
                        }
                        let checklists = [];
                        const checklist_to_idx = {};
                        const maxes = {};

                        const template_user_id = config.get('template_user');

                        const groups_to_get = [...new Set(result.rows.map(row => row.group_assignee))].filter(
                            grp => grp != null
                        );

                        let group_map = {};

                        try {
                            const { groups } = await groupMod.promiseGetUserGroups(null, null, {
                                group_ids: groups_to_get,
                                skipAccess: true,
                            });
                            group_map = _.keyBy(groups, 'id');
                        } catch (error) {
                            para_cb(error);
                            return;
                        }

                        result.rows.forEach((row, idx) => {
                            if (checklist_to_idx[row.check_id] == null) {
                                checklist_to_idx[row.check_id] = checklists.length;

                                let { checklist_name } = row;
                                if (row.checklist_encrypted) {
                                    checklist_name = encrypt.decrypt(row.checklist_name);
                                }

                                checklists.push({
                                    id: row.check_id,
                                    task_id: row.task_id,
                                    name: checklist_name,
                                    encrypted: row.checklist_encrypted,
                                    date_created: row.checklist_date_created,
                                    orderindex: row.checklist_orderindex,
                                    show_completed: row.show_completed,
                                    creator: row.creator,
                                    resolved: 0,
                                    unresolved: 0,
                                    items: [],
                                });
                            }

                            if (row.id) {
                                let checklist_item_name = row.name;
                                if (row.encrypted) {
                                    checklist_item_name = encrypt.decrypt(row.name);
                                }

                                if (row.orderindex == null) {
                                    row.orderindex = maxes[row.check_id] + idx;
                                    orderindexes_to_set[row.id] = row.orderindex;
                                }

                                if (parseInt(row.orderindex, 10) > maxes[row.check_id] || !maxes[row.check_id]) {
                                    maxes[row.check_id] = parseInt(row.orderindex, 10);
                                }

                                const checklist_item = {
                                    id: row.id,
                                    name: checklist_item_name,
                                    orderindex: row.orderindex,
                                    assignee: null,
                                    group_assignee: null,
                                    resolved: row.resolved,
                                    encrypted: row.encrypted,
                                    parent: row.parent,
                                    date_created: row.date_created,
                                    start_date: row.start_date,
                                    start_date_time: row.start_date_time,
                                    due_date: row.due_date,
                                    due_date_time: row.due_date_time,
                                    sent_due_date_notif: row.sent_due_date_notif,
                                    children: [],
                                };
                                if (row.resolved) {
                                    checklists[checklist_to_idx[row.check_id]].resolved += 1;
                                } else {
                                    checklists[checklist_to_idx[row.check_id]].unresolved += 1;
                                }

                                if (row.assignee_id) {
                                    checklist_item.assignee = {
                                        id: row.assignee_id,
                                        username: row.username,
                                        email: row.email,
                                        color: row.color,
                                        initials: userInitials.getInitials(row.email, row.username),
                                        profilePicture: cf_sign.getCloudfrontAvatarUrl(row.profile_picture_key),
                                    };
                                } else if (Number(row.assignee) === template_user_id) {
                                    checklist_item.assignee = {
                                        id: template_user_id,
                                        username: 'Example User',
                                        color: '#a1a1a1',
                                        initials: 'EU',
                                        profilePicture: null,
                                    };
                                }

                                if (row.group_assignee_id) {
                                    if (!group_map[row.group_assignee_id]) {
                                        logger.warn({
                                            msg: 'Failed to find group assignee',
                                            status: 404,
                                            ECODE: 'CHECK_653',
                                            group_assignee_id: row.group_assignee_id,
                                            checklist_id: row.check_id,
                                        });
                                    }
                                    checklist_item.group_assignee = group_map[row.group_assignee_id] || null;
                                }

                                if (options.include_creator) {
                                    checklist_item.creator = row.creator;
                                }

                                if (options.returnItem && options.returnItem === row.id) {
                                    returnItem = checklist_item;
                                }

                                checklists[checklist_to_idx[row.check_id]].items.push(checklist_item);
                            }

                            if (options.include_creator) {
                                checklists[checklist_to_idx[row.check_id]].creator = row.checklist_creator;
                            }
                        });

                        if (!options.skip_treeify) {
                            checklists = checklists.map(checklist => {
                                const _checklist = checklist;
                                _checklist.items = treeify(_checklist.items);
                                return _checklist;
                            });
                        }
                        checklists.sort((a, b) => a.orderindex - b.orderindex);
                        para_cb(null, checklists);
                    }
                );
            },
        },
        async (err, result) => {
            if (err) {
                return cb(err);
            }

            const team_id =
                result.accessChecklist?.team_id ?? (await entitiesService.getTeamId({ task_ids: [task_id] }, {}));

            const ret_val = { checklists: result.checklists, team_id };
            if (options.returnItem && returnItem) {
                ret_val.returnItem = returnItem;
            }

            setChecklistItemsOrderindexes(orderindexes_to_set);

            return cb(null, ret_val);
        }
    );
}
export { _getChecklists };

export async function getChecklists(req, resp, next) {
    const userid = req.decoded_token.user;
    const { task_id } = req.params;
    let { checklist_id } = req.params;
    let { templates } = req.query;
    if (templates === 'false') {
        templates = false;
    }
    try {
        let isV1;
        if (templates) {
            checklist_id = await templateHelpers._idByPermanentId(
                checklist_id,
                config.get('hierarchy_tables.checklists')
            );
            isV1 = await checklistTemplate._checkTemplateV1(userid, checklist_id);
        }
        _getChecklists(userid, task_id, { checklist_id, templates, isV1, replica: true }, (err, result) => {
            if (err) {
                next(err);
            } else {
                resp.status(200).send(result);
            }
        });
    } catch (err) {
        next(err);
    }
}

/**
 *
 * @param userid
 * @param checklist_id
 * @param {Object} options
 * @param {boolean} [options.replica] - if true, use replica for querying check list
 * @param cb
 * @private
 */
function _editChecklist(userid, checklist_id, options, cb) {
    let encrypted = false;
    let task_id;

    async.parallel(
        {
            access(para_cb) {
                const permissions = [];
                if (options.name || options.orderindex) {
                    permissions.push(config.permission_constants.edit_checklists);
                }
                access.checkAccessChecklist(userid, checklist_id, { permissions }, para_cb);
            },
            encrypted(para_cb) {
                const queryFunc = options.replica ? db.replicaQuery : db.readQuery;
                queryFunc(
                    'SELECT task_id, encrypted FROM task_mgmt.checklists WHERE id = $1',
                    [checklist_id],
                    (err, result) => {
                        if (err) {
                            para_cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'CHECK_00',
                            });
                        } else if (result.rows.length === 0) {
                            para_cb({
                                err: 'Checklist not found',
                                status: 404,
                                ECODE: 'CHECK_044',
                            });
                        } else {
                            [{ task_id, encrypted }] = result.rows;
                            para_cb();
                        }
                    }
                );
            },
        },
        async err => {
            if (err) {
                cb(err);
                return;
            }

            const { query, params } = _getEditChecklistQuery(checklist_id, options, encrypted);

            if (query.length === 0) {
                cb({
                    err: 'No changes made to checklist',
                    status: 400,
                    ECODE: 'CHECK_005',
                });
                return;
            }

            const team_id = await entitiesService.getTeamId({
                task_ids: [task_id],
            });
            let events;
            try {
                ({ __version_change_events: events } = await writeAsync(query, params, [
                    {
                        object_type: ObjectType.TASK,
                        object_id: task_id,
                        workspace_id: team_id,
                        operation: OperationType.UPDATE,
                        data: {
                            context: {
                                ws_key: options.ws_key,
                            },
                        },
                    },
                ]));
            } catch (error) {
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'CHECK_006',
                });
                return;
            }

            _getChecklists(userid, null, { checklist_id, skip_access: true }, (err3, result) => {
                if (err3) {
                    cb(err3);
                } else {
                    const taskVersionUpdated = getVersionUpdateForTask(task_id, team_id, events);
                    cb(null, {
                        checklist: result.checklists[0],
                        version: taskVersionUpdated,
                    });
                    if (result.checklists[0]) {
                        sqsWs.sendWSMessage('sendChecklistEdited', [
                            userid,
                            checklist_id,
                            {
                                ws_key: options.ws_key,
                            },
                        ]);
                    }
                }
            });
        }
    );
}
export { _editChecklist };

function _getEditChecklistQuery(checklist_id, options, encrypted) {
    let query = `UPDATE task_mgmt.checklists SET `;
    const params = [];

    if (options.name) {
        if (encrypted) {
            options.name = encrypt.encrypt(options.name);
        }
        params.push(options.name);
        query += ` name = $${params.length}`;
    }

    if (options.orderindex != null) {
        if (params.length > 0) {
            query += ', ';
        }
        params.push(options.orderindex);
        query += ` orderindex = $${params.length}`;
    }

    if (options.show_completed != null) {
        if (params.length > 0) {
            query += ', ';
        }
        params.push(options.show_completed);
        query += ` show_completed = $${params.length}`;
    }

    if (options.start_date) {
        if (params.length > 0) {
            query += ', ';
        }
        params.push(options.start_date);
        params.push(options.start_date_time ?? false);
        query += ` start_date = $${params.length - 1}, start_date_time = $${params.length}`;
    }

    if (options.due_date) {
        if (params.length > 0) {
            query += ', ';
        }
        params.push(options.due_date);
        params.push(options.due_date_time ?? false);
        query += ` due_date = $${params.length - 1}, due_date_time = $${params.length}`;
    }

    // Return empty query if no changes were made
    if (params.length === 0) {
        return {
            query: '',
            params: [],
        };
    }

    params.push(checklist_id);
    query += `WHERE id = $${params.length}`;

    return { query, params };
}

export function editChecklist(req, resp) {
    const userid = req.decoded_token.user;
    const { checklist_id } = req.params;
    const { name, show_completed, orderindex } = req.body;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;

    _editChecklist(
        userid,
        checklist_id,
        {
            name,
            orderindex,
            show_completed,
            ws_key,
        },
        (err, result) => {
            if (err) {
                resp.status(err.status).send({
                    err: err.err,
                    ECODE: err.ECODE,
                });
            } else {
                resp.status(200).send(result);
            }
        }
    );
}

function flattenChecklistItems(_items, parent) {
    if (!_items) {
        return [];
    }

    let items = _items;

    items.forEach(_item => {
        const item = _item;
        item.id = uuid.v4();

        if (parent) {
            item.parent = parent;
        }

        if (item.children) {
            items = items.concat(flattenChecklistItems(item.children, item.id));
        }

        delete item.children;
    });

    return items;
}

/**
 * Check if the checklist item limit is reached and throw an error if it is.
 * @param checklist_id
 * @param options
 * @returns {Promise<void>}
 */
async function checkChecklistItemLimit(checklist_id, options) {
    if (!shouldLimitChecklistItems()?.countLimit) {
        return;
    }

    const team_id = options?.team_id ?? (await entitiesService.getTeamId({ checklist_ids: [checklist_id] }));
    if (!team_id) {
        // lack of team_id will cause an error below
        logger.warn({ msg: `No team_id found for checkChecklistItemLimit`, checklist_id });
        return;
    }
    const additionalChecklistItemCount = options?.additionalChecklistItems ?? 0;

    const query = options?.client?.query.bind(options?.client) ?? db.promiseReplicaQuery;
    const { rows } = await query(
        'SELECT COUNT(*) FROM task_mgmt.checklist_items WHERE checklist_id = $1 AND deleted IS NOT TRUE',
        [checklist_id]
    );
    await entitlementService.checkEntitlementLimit(
        team_id,
        EntitlementName.ChecklistItemLimit,
        Number(rows[0].count) + additionalChecklistItemCount,
        { throwOnDeny: true }
    );
}

/**
 * Check if the checklist item name length limit is reached and throw an error if it is.
 * @param checklist_id
 * @param options
 * @returns {Promise<void>}
 */
async function checkChecklistItemLengthLimit(checklist_id, items, options) {
    if (!shouldLimitChecklistItems()?.lengthLimit) {
        return;
    }
    const team_id = options?.team_id ?? (await entitiesService.getTeamId({ checklist_ids: [checklist_id] }));
    if (!team_id) {
        // lack of team_id will cause an error below
        logger.warn({ msg: `No team_id found for checkChecklistItemLengthLimit`, checklist_id });
        return;
    }
    const maxLength = Math.max(...items.map(item => item.name.length ?? 0));
    await entitlementService.checkEntitlementLimit(team_id, EntitlementName.ChecklistItemLengthLimit, maxLength, {
        throwOnDeny: true,
    });
}

/**
 *
 * @param userid
 * @param checklist_id
 * @param _options
 * @param {Object[]} _options.items checklist items to create
 * @param {boolean} [_options.skip_access] ignores access check
 * @param {boolean} [_options.skip_notify] ignores notification
 * @param {boolean} [_options.skip_version] skips OVM update
 * @param {boolean} [_options.skip_replica] don't use replica but use BDR
 * @param {boolean} [_options.team_id] Id of the team the checklist belongs to
 * @param {boolean} [_options.check_assignee_team_access] Ensure assignees have access to the team
 * @param cb
 * @private
 */
function _createChecklistItems(userid, checklist_id, _options, cb) {
    const options = _options;
    if (!options.items || !Array.isArray(options.items)) {
        cb({
            err: 'Missing checklist items array',
            status: 400,
            ECODE: 'CHECK_020',
        });
        return;
    }
    options.items = flattenChecklistItems(options.items);

    let task_id;
    let team_id;

    async.parallel(
        {
            access(para_cb) {
                if (options.skip_access) {
                    para_cb();
                    return;
                }
                access.checkAccessChecklist(
                    userid,
                    checklist_id,
                    { permissions: [config.permission_constants.edit_checklists] },
                    para_cb
                );
            },
            checklist(para_cb) {
                if (options.task_id) {
                    ({ task_id } = options);
                    para_cb();
                    return;
                }

                const readQuery = options.skip_replica ? db.readQuery : db.replicaQuery;

                readQuery('SELECT * FROM task_mgmt.checklists WHERE id = $1', [checklist_id], (err, result) => {
                    if (err) {
                        para_cb({
                            err: 'Internal server error',
                            status: 500,
                            ECODE: 'CHECK_007',
                        });
                    } else if (result.rows.length) {
                        [{ task_id }] = result.rows;
                        para_cb(null, result);
                    } else {
                        para_cb({
                            err: 'Checklist not found',
                            status: 400,
                            ECODE: 'CHECK_043',
                        });
                    }
                });
            },
            validateAssignees(para_cb) {
                if (options.skip_access && !(options.team_id && options.check_assignee_team_access)) {
                    para_cb();
                    return;
                }

                const assignees = [];
                options.items.forEach(item => {
                    if (item.assignee) {
                        assignees.push(item.assignee);
                    }
                });

                async.each(
                    assignees,
                    (assignee, each_cb) => {
                        if (!options.skip_access) {
                            access.checkAccessChecklist(
                                assignee,
                                checklist_id,
                                { permissions: [], checkJoined: false },
                                each_cb
                            );
                        } else if (options.team_id && options.check_assignee_team_access) {
                            access.checkAccessTeam(assignee, options.team_id, { permissions: [] }, each_cb);
                        }
                    },
                    err => {
                        if (err) {
                            para_cb({
                                err: 'Assignees must have access to the checklist',
                                status: 400,
                                ECODE: 'CHECK_026',
                            });
                        } else {
                            para_cb();
                        }
                    }
                );
            },
            async validateGroupAssignee(para_cb) {
                if (options.skip_access && !(options.team_id && options.check_assignee_team_access)) {
                    para_cb();
                    return;
                }

                const group_assignees = [...new Set(options.items.map(item => item.group_assignee))].filter(
                    group => group != null
                );

                if (!group_assignees?.length) {
                    para_cb();
                    return;
                }

                try {
                    if (!options.skip_access) {
                        await groupMod.checkGroupsHasAccessToTeam(group_assignees, 'checklist', checklist_id, {});
                    } else if (options.team_id && options.check_assignee_team_access) {
                        await groupMod.checkGroupsHasAccessToTeam(group_assignees, 'team', options.team_id, {});
                    }
                    para_cb();
                } catch (error) {
                    para_cb(error);
                }
            },
            teamEncrypted(para_cb) {
                db.replicaQuery(
                    'SELECT teams.should_encrypt, items.subcategory, teams.id AS team_id FROM task_mgmt.checklists, task_mgmt.teams, task_mgmt.projects, task_mgmt.categories, task_mgmt.subcategories, task_mgmt.items WHERE checklists.id = $1 AND checklists.task_id = items.id AND items.subcategory = subcategories.id AND subcategories.category = categories.id AND categories.project_id = projects.id AND projects.team = teams.id',
                    [checklist_id],
                    para_cb
                );
            },

            async limit(para_cb) {
                try {
                    if (task_id) {
                        team_id = await entitiesService.getTeamId({
                            task_ids: [task_id],
                        });
                    } else {
                        team_id = await entitiesService.getTeamId({
                            checklist_ids: [checklist_id],
                        });
                    }
                    await Promise.all([
                        checkChecklistItemLimit(checklist_id, {
                            ...options,
                            additionalChecklistItems: options.items.length,
                            team_id,
                        }),
                        checkChecklistItemLengthLimit(checklist_id, options.items, { ...options, team_id }),
                    ]);
                    para_cb();
                } catch (err) {
                    para_cb(err);
                }
            },
        },
        async (err, result) => {
            if (err) {
                cb(err);
                return;
            }

            const queries = [];

            let encrypted = false;

            if (
                config.encryption.checklists &&
                result.teamEncrypted.rows[0] &&
                result.teamEncrypted.rows[0].should_encrypt
            ) {
                encrypted = true;
            }

            const user_wants_to_follow_task_edits = await doesUserWantToFollowTaskEdits(userid, team_id);
            const checklist_item_ids = options.items.map(item => item.id);
            const now = Date.now();

            for (let i = 0; i < options.items.length; i += 1) {
                const checklist_item = options.items[i];

                if (encrypted) {
                    checklist_item.name = encrypt.encrypt(checklist_item.name);
                }

                queries.push(
                    {
                        query: `
                            INSERT INTO task_mgmt.checklist_items(
                                checklist_id, id, parent, name, orderindex, date_created, 
                                creator, assignee, resolved, deleted, encrypted, group_assignee, workspace_id,
                                start_date, start_date_time, due_date, due_date_time) 
                            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $10, false, $9, $11, 
                                (SELECT workspace_id FROM task_mgmt.checklists where id = $1),
                                $12, $13, $14, $15)`,
                        params: [
                            checklist_id,
                            checklist_item.id,
                            checklist_item.parent,
                            checklist_item.name,
                            checklist_item.orderindex,
                            now,
                            userid,
                            checklist_item.assignee,
                            encrypted,
                            checklist_item.resolved || false,
                            checklist_item.group_assignee,
                            checklist_item.start_date,
                            checklist_item.start_date_time ?? false,
                            checklist_item.due_date,
                            checklist_item.due_date_time ?? false,
                        ],
                    },
                    {
                        query: "INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) (SELECT $1, $2, $3, $4, $5, $6, $7 WHERE NOT EXISTS (SELECT * FROM task_mgmt.task_history WHERE task_id = $1 AND field = $2 AND userid = $6 AND date > $8  AND data->>'checklist_id' = $9 AND hidden IS NOT TRUE)) RETURNING id",
                        params: [
                            task_id,
                            'checklist_items_added',
                            now,
                            null,
                            checklist_item.id,
                            userid,
                            { checklist_id },
                            now - 60000 * 5,
                            checklist_id,
                        ],
                    },
                    {
                        query: "UPDATE task_mgmt.task_history SET after = after || '|' || $4::text, date = $3 WHERE id IN (SELECT id FROM task_mgmt.task_history WHERE task_id = $1 AND field = $2 AND userid = $5 AND date >= $6 AND after != $4 AND data->>'checklist_id' = $7 AND hidden IS NOT TRUE) RETURNING id",
                        params: [
                            task_id,
                            'checklist_items_added',
                            now,
                            checklist_item.id,
                            userid,
                            now - 60000 * 5,
                            checklist_id,
                        ],
                    }
                );

                if (checklist_item.assignee) {
                    if (checklist_item.assignee !== userid) {
                        queries.push({
                            query: 'INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) VALUES ((SELECT task_id FROM task_mgmt.checklists WHERE id = $1), $2, $3, $4, $5, $6, $7) RETURNING id, field, after',
                            params: [
                                checklist_id,
                                'checklist_item_assignee',
                                new Date().getTime(),
                                null,
                                checklist_item.assignee,
                                userid,
                                { checklist_item_id: checklist_item.id },
                            ],
                        });
                    }
                    if (
                        options.assignee !== userid ||
                        (options.assignee === userid && user_wants_to_follow_task_edits)
                    ) {
                        queries.push({
                            query: 'INSERT INTO task_mgmt.followers(task_id, userid, workspace_id) SELECT $1, $2, $3 WHERE NOT EXISTS (SELECT * FROM task_mgmt.followers WHERE task_id = $1 and userid = $2)',
                            params: [task_id, checklist_item.assignee, team_id],
                        });
                    }
                }

                if (checklist_item.group_assignee) {
                    queries.push(
                        {
                            query: `INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id, field, after`,
                            params: [
                                task_id,
                                'checklist_item_assignee',
                                new Date().getTime(),
                                null,
                                checklist_item.group_assignee,
                                userid,
                                { checklist_item_id: checklist_item.id, group_assignee: true },
                            ],
                        },
                        {
                            query: `
                                INSERT INTO task_mgmt.group_followers(task_id, group_id, date_added, workspace_id) 
                                SELECT $1, $2, $3, $4 WHERE NOT EXISTS (SELECT * FROM task_mgmt.group_followers WHERE task_id = $1 and group_id = $2)
                            `,
                            params: [task_id, checklist_item.group_assignee, new Date().getTime(), team_id],
                        }
                    );
                }
            }
            if (userid && task_id) {
                reportTaskChanged(userid, task_id);
            }

            const versionUpdates = [];
            if (team_id) {
                versionUpdates.push({
                    object_id: task_id,
                    object_type: ObjectType.TASK,
                    workspace_id: team_id,
                    operation: OperationType.UPDATE,
                    data: {
                        context: {
                            ws_key: options.ws_key,
                        },
                    },
                });
            } else if (!options.skip_version) {
                logger.error({
                    msg: 'Failed to write OVM update, Missing workspace ID',
                    ECODE: 'OVM_WS_923',
                    OVM_CRITICAL: true,
                });
            }
            if (options.client) {
                if (!options.skip_version && Array.isArray(options?.client?.versionUpdates)) {
                    options.client.versionUpdates.push(...versionUpdates);
                }
                async.eachSeries(
                    queries,
                    (query, each_cb) => {
                        options.client.query(query.query, query.params, each_cb);
                    },
                    err2 => {
                        if (err2) {
                            logger.error({
                                msg: 'Failed to create checklist items',
                                status: 500,
                                ECODE: 'CHECK_032',
                                err: err2,
                                userid,
                                checklist_id,
                            });
                            cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'CHECK_032',
                            });
                            return;
                        }
                        cb(err);
                    }
                );
            } else {
                let rows;
                try {
                    rows = await batchQueriesSeriesAsync(queries, versionUpdates);
                } catch (err3) {
                    logger.error({
                        msg: 'Failed to create checklist items',
                        status: 500,
                        ECODE: 'CHECK_008',
                        err: err3,
                        userid,
                        checklist_id,
                    });
                    cb({
                        err: 'Internal server error',
                        status: 500,
                        ECODE: 'CHECK_008',
                    });
                    return;
                }
                _getChecklists(
                    userid,
                    null,
                    { checklist_id, skip_access: true, skip_treeify: options.return_updated },
                    (err4, result2) => {
                        if (err4) {
                            cb(err4);
                            return;
                        }

                        const taskVersionUpdated = getVersionUpdateForTask(
                            task_id,
                            team_id,
                            rows.__version_change_events
                        );
                        const ret_val = {
                            version: taskVersionUpdated,
                        };

                        if (result2.checklists[0] && options.return_updated) {
                            ret_val.items = result2.checklists[0].items.filter(item =>
                                checklist_item_ids.includes(item.id)
                            );
                        } else {
                            [ret_val.checklist] = result2.checklists;
                        }

                        cb(null, ret_val);

                        if (result2.checklists[0] && !options.skip_notify) {
                            sqsWs.sendWSMessage('sendChecklistEdited', [
                                userid,
                                checklist_id,
                                { ws_key: options.ws_key },
                            ]);
                            sqsWs.sendWSMessage('sendTaskUpdated', [
                                task_id,
                                {
                                    userid,
                                    ws_key: options.ws_key,
                                    hist_ids: rows?.map(row => row.id),
                                    date_updated: new Date().getTime(),
                                },
                            ]);
                        }
                    }
                );
                if (!options.skip_notify) {
                    rows.forEach(row => {
                        sqsNotif.sendNotifMessage('createTaskNotifications', [userid, task_id, row.id, {}]);
                        webhook.sendWebhookMessage('taskUpdated', {
                            team_id,
                            hist_id: row.id,
                            task_id,
                            userid,
                        });
                    });
                }
                if (result.teamEncrypted.rows[0]) {
                    subcatHelper.invalidateCachedSubcatTaskCount([result.teamEncrypted.rows[0].subcategory]);
                }
            }
        }
    );
}
export { _createChecklistItems };

export function createChecklistItems(req, resp) {
    const userid = req.decoded_token.user;
    const { checklist_id } = req.params;
    const { items } = req.body;

    const options = {
        items,
        ws_key: req.headers.sessionid || req.decoded_token.ws_key,
        return_updated: req.query.return_updated === 'true',
        skip_replica: true,
    };
    _createChecklistItems(userid, checklist_id, options, (err, result) => {
        if (err) {
            resp.status(err.status).send({
                err: err.err,
                ECODE: err.ECODE,
            });
        } else {
            resp.status(200).send(result);
        }
    });
}

function _deleteChecklistItem(userid, checklist_item_id, options, cb) {
    access.checkAccessChecklistItem(
        userid,
        checklist_item_id,
        {
            permissions: [
                config.permission_constants.edit_checklists,
                config.permission_constants.can_delete_checklist_item,
            ],
            is_delete: true,
        },
        (err, result) => {
            if (err) {
                cb(err);
                return;
            }

            const { task_id, subcategory_map, team_id } = result;

            db.getCopyConn(cb, { label: '_deleteChecklistItem' }, (connErr, client, done) => {
                let checklist_item_ids = [checklist_item_id];
                let all_checklist_item_ids = [checklist_item_id];
                const date_deleted = new Date().getTime();
                let checklist_id = null;
                const ovm = getObjectVersionManager();
                let events;

                async.series(
                    [
                        series_cb => {
                            client.query('BEGIN', series_cb);
                        },
                        series_cb => {
                            client.query(
                                'UPDATE task_mgmt.checklist_items SET deleted = true, date_deleted = $1 WHERE id = $2 returning checklist_id',
                                [date_deleted, checklist_item_id],
                                (updateErr, result1) => {
                                    if (updateErr) {
                                        series_cb(updateErr);
                                    } else {
                                        if (result1.rows.length > 0) {
                                            [{ checklist_id }] = result1.rows;
                                        }
                                        series_cb();
                                    }
                                }
                            );
                        },
                        series_cb => {
                            async.whilst(
                                () => checklist_item_ids.length > 0,
                                whilst_cb => {
                                    client.query(
                                        'UPDATE task_mgmt.checklist_items SET deleted = true WHERE parent = ANY($1) AND deleted = false AND date_deleted IS NULL AND checklist_id = $2 RETURNING id',
                                        [checklist_item_ids, checklist_id],
                                        (updateErr, result1) => {
                                            if (err) {
                                                whilst_cb(updateErr);
                                            } else {
                                                checklist_item_ids = result1.rows
                                                    .map(row => row.id)
                                                    .filter(item_id => !all_checklist_item_ids.includes(item_id));
                                                all_checklist_item_ids =
                                                    all_checklist_item_ids.concat(checklist_item_ids);
                                                whilst_cb();
                                            }
                                        }
                                    );
                                },
                                whilstErr => {
                                    series_cb(whilstErr);
                                }
                            );
                        },
                        series_cb => {
                            client.query(
                                "UPDATE task_mgmt.task_history SET deleted = true WHERE ((field = 'checklist_item_added' AND after = ANY($1)) OR (field = 'checklist_item_resolved' AND data->>'checklist_item_id' = ANY($1)) OR (field = 'checklist_item_assignee' AND data->>'checklist_item_id' = ANY($1))) AND task_id = (SELECT task_id FROM task_mgmt.checklists WHERE checklists.id = $2)",
                                [all_checklist_item_ids, checklist_id],
                                updateErr => {
                                    series_cb(updateErr);
                                }
                            );
                        },
                        async series_cb => {
                            const txClient = new TransactionClientImpl(client, ovm);
                            const versionRequests = [];
                            if (team_id) {
                                versionRequests.push({
                                    object_type: ObjectType.TASK,
                                    object_id: task_id,
                                    operation: OperationType.UPDATE,
                                    workspace_id: team_id,
                                    data: {
                                        context: {
                                            ws_key: options.ws_key,
                                        },
                                    },
                                });
                            } else {
                                logger.error({
                                    msg: 'Missing workspace ID for OVM update',
                                    ECODE: 'OVM_WS_931',
                                    OVM_CRITICAL: true,
                                });
                            }
                            try {
                                events = await ovm.updateVersions(txClient, versionRequests);
                            } catch (ovmErr) {
                                series_cb(ovmErr);
                                return;
                            }
                            series_cb(null);
                        },
                        series_cb => {
                            client.query('COMMIT', series_cb);
                        },
                    ],
                    async seriesErr => {
                        if (seriesErr) {
                            db.rollback(client, done);
                            logger.error({
                                msg: 'Failed to delete checklist item',
                                err: seriesErr,
                            });
                            cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'CHECK_009',
                            });
                        } else {
                            done();
                            // eslint-disable-next-line @typescript-eslint/no-empty-function
                            await ovm.notifyChanges(events).catch(() => { });
                            if (userid && task_id) {
                                reportTaskChanged(userid, task_id);
                            }
                            const taskVersionUpdated = getVersionUpdateForTask(task_id, team_id, events);
                            cb(null, {
                                version: taskVersionUpdated,
                            });
                            sqsWs
                                .sendWSMessage('sendChecklistItemDeleted', [
                                    userid,
                                    task_id,
                                    checklist_id,
                                    checklist_item_id,
                                    {
                                        ws_key: options.ws_key,
                                    },
                                ])
                                .catch(() => { });
                            subcatHelper.invalidateCachedSubcatTaskCount([subcategory_map[task_id]]);
                        }
                    }
                );
            });
        }
    );
}
export { _deleteChecklistItem };

export function deleteChecklistItem(req, resp) {
    const userid = req.decoded_token.user;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    const { checklist_item_id } = req.params;

    _deleteChecklistItem(userid, checklist_item_id, { ws_key }, (err, result) => {
        if (err) {
            resp.status(err.status).send({
                err: err.err,
                ECODE: err.ECODE,
            });
        } else {
            resp.status(200).send(result);
        }
    });
}

function getNestedChildrenIds(checklistItems, parent) {
    // Just run bfs
    const parentMap = new Map();
    for (let i = 0; i < checklistItems.length; i += 1) {
        const item = checklistItems[i];
        if (!item.parent) {
            continue;
        }
        let children = [];
        if (parentMap.has(item.parent)) {
            children = parentMap.get(item.parent);
        } else {
            parentMap.set(item.parent, children);
        }
        children.push(item.id);
    }
    const nestedChildren = new Set();
    nestedChildren.add(parent);
    const childrenToExplore = [parent];
    while (childrenToExplore.length > 0) {
        const node = childrenToExplore.pop();
        if (parentMap.has(node)) {
            for (const child of parentMap.get(node)) {
                if (!nestedChildren.has(child)) {
                    nestedChildren.add(child);
                    childrenToExplore.push(child);
                }
            }
        }
    }
    return [...nestedChildren];
}

const GET_CHECKLIST_ITEMS_QUERY = `
    SELECT 
        checklist_items.*, 
        checklists.task_id 
    FROM 
        task_mgmt.checklists, 
        task_mgmt.checklist_items 
    WHERE 
        checklist_items.id = $1 
        AND checklists.id = checklist_items.checklist_id
    `;

function getChecklistUpdateQuery(options, checklist_item_id) {
    let query = 'UPDATE task_mgmt.checklist_items SET checklist_id = $1';
    const params = [options.checklist_id, checklist_item_id];

    if (options.orderindex != null) {
        params.push(options.orderindex);
        query += `, orderindex = $${params.length}`;
    }
    if (options.parent) {
        params.push(options.parent);
        query += `, parent = $${params.length}`;
    } else {
        query += `, parent = null`;
    }

    query += ' WHERE id = $2';
    return { query, params };
}

function moveChecklistPostProcessing(options, userid, old_checklist, checklist_item_id) {
    if (old_checklist.encrypted) {
        old_checklist.name = encrypt.decrypt(old_checklist.name);
    }

    const checklist_item = {
        id: checklist_item_id,
        name: old_checklist.name,
        checklist_id: options.checklist_id || old_checklist.checklist_id,
        orderindex: options.orderindex || old_checklist.orderindex,
        parent: options.parent || old_checklist.parent,
        resolved: old_checklist.resolved,
        assignee: old_checklist.assignee,
    };

    sqsWs.sendWSMessage('sendChecklistItemMoved', [
        userid,
        old_checklist.task_id,
        old_checklist.checklist_id,
        old_checklist.parent,
        checklist_item,
        { ws_key: options.ws_key },
    ]);
}

async function _moveChecklistItemRefactored(userid, checklist_item_id, options) {
    if (!options.checklist_id) {
        throw new CheckError('New checklist id required', 'CHECK_023', 400);
    }

    const res = await Promise.all([
        access.promiseAccessChecklist(userid, options.checklist_id, {
            permissions: [config.permission_constants.edit_checklists],
        }),
        access.promiseCheckAccessChecklistItem(userid, checklist_item_id, {
            permissions: [config.permission_constants.edit_checklists],
        }),
        db.promiseReplicaQuery(GET_CHECKLIST_ITEMS_QUERY, [checklist_item_id]),
    ]);

    const { team_id } = res[1];
    const { task_id } = res[1];
    const old_checklist = res[2].rows[0];
    if (!old_checklist) {
        throw new CheckError('Checklist item not found', 'CHECK_048', 400);
    }

    const allChecklistItemsResult = await db.promiseReplicaQuery(
        `
        SELECT checklist_items.id, checklist_items.parent
        FROM tasK_mgmt.checklist_items
        WHERE checklist_id = $1
    `,
        [old_checklist.checklist_id]
    );
    const allChecklistItems = allChecklistItemsResult.rows;
    const childChecklistItems = getNestedChildrenIds(allChecklistItems, checklist_item_id);

    const ovm = getObjectVersionManager();
    const client = await getTransactionClientAsync(ovm, {}, { label: '_moveChecklistItemRefactored' });
    let events;

    try {
        await client.beginAsync();

        const { query, params } = getChecklistUpdateQuery(options, checklist_item_id);
        await client.queryAsync(query, params);

        const childrenQuery =
            'UPDATE task_mgmt.checklist_items SET checklist_id = $2 WHERE id = ANY($1) aND checklist_id = $3';
        const childrenParams = [childChecklistItems, options.checklist_id, old_checklist.checklist_id];
        await client.queryAsync(childrenQuery, childrenParams);

        const versionRequests = getTaskVersionUpdates(
            task_id,
            team_id,
            OperationType.UPDATE,
            options.ws_key,
            'OVM_WS_932'
        );
        events = await ovm.updateVersions(client, versionRequests);

        await client.commitAsync();
    } catch (err) {
        await client.rollbackAsync();
        throw new CheckError(err, 'CHECK_050');
    }

    await ovm.notifyChanges(events).catch(() => { });
    moveChecklistPostProcessing(options, userid, old_checklist, checklist_item_id);

    const taskVersionUpdated = getVersionUpdateForTask(task_id, team_id, events);
    return {
        version: taskVersionUpdated,
    };
}

function _moveChecklistItem(userid, checklist_item_id, options, cb) {
    if (!options.checklist_id) {
        cb({
            err: 'New checklist id required',
            status: 400,
            ECODE: 'CHECK_023',
        });
        return;
    }

    let team_id;
    let task_id;
    async.parallel(
        {
            accessChecklist(para_cb) {
                access.checkAccessChecklist(
                    userid,
                    options.checklist_id,
                    { permissions: [config.permission_constants.edit_checklists] },
                    para_cb
                );
            },
            accessChecklistItem(para_cb) {
                access.checkAccessChecklistItem(
                    userid,
                    checklist_item_id,
                    { permissions: [config.permission_constants.edit_checklists] },
                    (err, res) => {
                        if (err) {
                            para_cb(err);
                            return;
                        }
                        team_id = res.team_id;
                        task_id = res.task_id;

                        para_cb();
                    }
                );
            },
            checklistItem(para_cb) {
                db.replicaQuery(GET_CHECKLIST_ITEMS_QUERY, [checklist_item_id], (err, result) => {
                    if (err) {
                        logger.error({
                            msg: 'Failed to get checklist item for edit',
                            err,
                        });
                        para_cb({
                            err: 'Internal server error',
                            status: 500,
                            ECODE: 'CHECK_025',
                        });
                    } else if (!result.rows.length) {
                        para_cb({ err: 'Checklist item not found', status: 400, ECODE: 'CHECK_048' });
                    } else {
                        para_cb(null, result);
                    }
                });
            },
        },
        (err, result) => {
            if (err) {
                cb(err);
                return;
            }
            const ovm = getObjectVersionManager();
            let events;

            db.getCopyConn(cb, { label: '_moveChecklistItem' }, (connErr, client, done) => {
                let checklist_item_ids = [checklist_item_id];
                let all_checklist_item_ids = [checklist_item_id];
                const old_checklist = result.checklistItem.rows[0];

                async.series(
                    [
                        series_cb => {
                            client.query('BEGIN', series_cb);
                        },
                        series_cb => {
                            const { query, params } = getChecklistUpdateQuery(options, checklist_item_id);
                            client.query(query, params, series_cb);
                        },
                        series_cb => {
                            async.whilst(
                                () => checklist_item_ids.length > 0,
                                whilst_cb => {
                                    client.query(
                                        'UPDATE task_mgmt.checklist_items SET checklist_id = $2 WHERE parent = ANY($1) aND Checklist_id = $3 RETURNING id',
                                        [checklist_item_ids, options.checklist_id, old_checklist.checklist_id],
                                        (updateErr, updateResult) => {
                                            if (updateErr) {
                                                whilst_cb(updateErr);
                                            } else {
                                                checklist_item_ids = updateResult.rows
                                                    .map(row => row.id)
                                                    .filter(item_id => !all_checklist_item_ids.includes(item_id));
                                                all_checklist_item_ids =
                                                    all_checklist_item_ids.concat(checklist_item_ids);
                                                whilst_cb();
                                            }
                                        }
                                    );
                                },
                                whilstErr => {
                                    series_cb(whilstErr);
                                }
                            );
                        },
                        async series_cb => {
                            const txClient = new TransactionClientImpl(client, ovm);
                            const versionRequests = getTaskVersionUpdates(
                                task_id,
                                team_id,
                                OperationType.UPDATE,
                                options.ws_key,
                                'OVM_WS_932'
                            );
                            try {
                                events = await ovm.updateVersions(txClient, versionRequests);
                            } catch (ovmErr) {
                                series_cb(ovmErr);
                                return;
                            }
                            series_cb(null);
                        },
                        series_cb => {
                            client.query('COMMIT', series_cb);
                        },
                    ],
                    async seriesErr => {
                        if (seriesErr) {
                            db.rollback(client, done);
                            logger.error({
                                msg: 'Failed to move checklist item',
                                err: seriesErr,
                            });
                            cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'CHECK_024',
                            });
                        } else {
                            done();
                            // eslint-disable-next-line @typescript-eslint/no-empty-function
                            await ovm.notifyChanges(events).catch(() => { });
                            const taskVersionUpdated = getVersionUpdateForTask(task_id, team_id, events);

                            cb(null, { version: taskVersionUpdated });

                            moveChecklistPostProcessing(options, userid, old_checklist, checklist_item_id);
                        }
                    }
                );
            });
        }
    );
}

export async function moveChecklistItem(req, resp) {
    const userid = req.decoded_token.user;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    const { checklist_item_id } = req.params;
    const options = req.body;

    options.ws_key = ws_key;

    if (shouldUseRefactoredMoveChecklistItem()) {
        try {
            const result = await _moveChecklistItemRefactored(userid, checklist_item_id, options);
            resp.status(200).send(result);
        } catch (e) {
            resp.status(e.status || 500).send(e);
        }
    } else {
        _moveChecklistItem(userid, checklist_item_id, options, (err, result) => {
            if (err) {
                resp.status(err.status).send({ err: err.err, ECODE: err.ECODE });
            } else {
                resp.status(200).send(result);
            }
        });
    }
}

/**
 *
 * @param userid
 * @param checklist_item_id
 * @param {Object} _options
 * @param {bool} [_options.replica] - if true, use replica db for querying check list item
 * @param cb
 * @private
 */
function _editChecklistItem(userid, checklist_item_id, _options, cb) {
    if (!checklist_item_id) {
        cb(null, {});
        return;
    }

    const options = _options;
    async.parallel(
        {
            access(para_cb) {
                access.checkAccessChecklistItem(
                    userid,
                    checklist_item_id,
                    { permissions: [config.permission_constants.edit_checklists] },
                    para_cb
                );
            },
            accessParent(para_cb) {
                if (options.parent) {
                    access.checkAccessChecklistItem(
                        userid,
                        options.parent,
                        { permissions: [config.permission_constants.edit_checklists] },
                        para_cb
                    );
                } else {
                    para_cb();
                }
            },
            checklistItem(para_cb) {
                const queryFunc = options.replica ? db.replicaQuery : db.readQuery;
                queryFunc(
                    `
                        SELECT 
                            checklist_items.*, 
                            checklists.task_id 
                        FROM 
                            task_mgmt.checklists
                                JOIN task_mgmt.checklist_items ON checklists.id = checklist_items.checklist_id
                        WHERE 
                            checklist_items.id = $1 
                    `,
                    [checklist_item_id],
                    (err, result) => {
                        if (err) {
                            logger.error({
                                msg: 'Failed to get checklist item for edit',
                                err,
                            });
                            para_cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'CHECK_010',
                            });
                        } else {
                            para_cb(null, result);
                        }
                    }
                );
            },
            validateAssignee(para_cb) {
                if (!options.assignee) {
                    para_cb();
                    return;
                }

                access.checkAccessChecklistItem(
                    options.assignee,
                    checklist_item_id,
                    { permissions: [], checkJoined: false },
                    err => {
                        if (err) {
                            para_cb({
                                err: 'Assignee must have access to checklist item',
                                status: 400,
                                ECODE: 'CHECK_028',
                            });
                        } else {
                            para_cb();
                        }
                    }
                );
            },
            async validateGroupAssignee(para_cb) {
                if (!options.group_assignee) {
                    para_cb();
                    return;
                }

                try {
                    await groupMod.checkGroupsHasAccessToTeam(
                        [options.group_assignee],
                        'checklist_item',
                        checklist_item_id,
                        {}
                    );
                    para_cb();
                } catch (error) {
                    para_cb(error);
                }
            },
        },
        async (err, result) => {
            if (err) {
                cb(err);
                return;
            }

            const wasResolved = result.checklistItem.rows[0].resolved;
            const { assignee, task_id, checklist_id, encrypted, group_assignee } = result.checklistItem.rows[0];
            const old_checklist_parent = result.checklistItem.rows[0].parent;
            const subcategory_id = result.access.subcategory_map[task_id];
            const { team_id } = result.access;
            const user_wants_to_follow_task_edits = await doesUserWantToFollowTaskEdits(userid, team_id);

            if (options.name) {
                try {
                    await checkChecklistItemLengthLimit(checklist_id, [{ name: options.name }], {
                        ...options,
                        team_id,
                    });
                } catch (error) {
                    cb(error);
                    return;
                }
            }

            if (options.name && encrypted) {
                options.name_decrypted = options.name;
                options.name = encrypt.encrypt(options.name);
            }

            const queries = [];

            if (options.group_assignee && assignee) {
                options.assignee = null;
            } else if (options.assignee && group_assignee) {
                options.group_assignee = null;
            }

            let query = `UPDATE task_mgmt.checklist_items SET `;
            const params = [];

            Object.keys(options).forEach(key => {
                if (
                    [
                        'name',
                        'resolved',
                        'parent',
                        'orderindex',
                        'assignee',
                        'group_assignee',
                        'start_date',
                        'start_date_time',
                        'due_date',
                        'due_date_time',
                    ].includes(key)
                ) {
                    if (params.length > 0) {
                        query += `, `;
                    }
                    params.push(options[key]);
                    query += ` ${key} = $${params.length}`;
                }
            });

            if (params.length === 0) {
                cb({
                    err: 'No changes made to checklist item',
                    status: 400,
                    ECODE: 'CHECK_011',
                });
                return;
            }

            params.push(checklist_item_id);
            query += ` WHERE id = $${params.length}`;

            if (options?.assignee && options.assignee !== assignee) {
                queries.push({
                    query: 'INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) VALUES ((SELECT task_id FROM task_mgmt.checklists WHERE id = $1), $2, $3, $4, $5, $6, $7) RETURNING id, field, after',
                    params: [
                        checklist_id,
                        'checklist_item_assignee',
                        new Date().getTime(),
                        assignee,
                        options.assignee,
                        userid,
                        { checklist_item_id },
                    ],
                });
                if (options.assignee !== userid || (options.assignee === userid && user_wants_to_follow_task_edits)) {
                    queries.push({
                        query: 'INSERT INTO task_mgmt.followers(task_id, userid, workspace_id) SELECT $1, $2, workspace_id FROM task_mgmt.items WHERE id = $1 AND NOT EXISTS (SELECT * FROM task_mgmt.followers WHERE task_id = $1 and userid = $2)',
                        params: [task_id, options.assignee],
                    });
                }
            }

            if (options?.group_assignee && group_assignee !== options.group_assignee) {
                queries.push(
                    {
                        query: `INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) 
                            VALUES ((SELECT task_id FROM task_mgmt.checklists WHERE id = $1), $2, $3, $4, $5, $6, $7) RETURNING id, field, after`,
                        params: [
                            checklist_id,
                            'checklist_item_assignee',
                            new Date().getTime(),
                            group_assignee,
                            options.group_assignee,
                            userid,
                            { checklist_item_id, group_assignee: true },
                        ],
                    },
                    {
                        query: `
                            INSERT INTO task_mgmt.group_followers(task_id, group_id, date_added, workspace_id) 
                            SELECT $1, $2, $3, (SELECT team_id FROM task_mgmt.groups WHERE id = $2) WHERE NOT EXISTS (SELECT * FROM task_mgmt.group_followers WHERE task_id = $1 and group_id = $2)
                        `,
                        params: [task_id, options.group_assignee, new Date().getTime()],
                    }
                );
            }

            if (options.resolved != null && wasResolved !== options.resolved) {
                queries.push({
                    query: 'INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) VALUES ((SELECT task_id FROM task_mgmt.checklists WHERE id = $1), $2, $3, $4, $5, $6, $7) RETURNING id',
                    params: [
                        checklist_id,
                        'checklist_item_resolved',
                        new Date().getTime(),
                        wasResolved,
                        options.resolved,
                        userid,
                        { checklist_item_id },
                    ],
                });
            }

            queries.push({
                query,
                params,
            });

            if (userid && options.task_id) {
                reportTaskChanged(userid, task_id);
            }

            const versionUpdates = [];
            if (team_id) {
                versionUpdates.push({
                    object_id: task_id,
                    object_type: ObjectType.TASK,
                    workspace_id: team_id,
                    operation: OperationType.UPDATE,
                    data: {
                        context: {
                            ws_key: options.ws_key,
                        },
                    },
                });
            } else {
                logger.error({
                    msg: 'Missing workspace ID for OVM update',
                    ECODE: 'OVM_WS_939',
                    OVM_CRITICAL: true,
                });
            }

            try {
                const rows = await batchQueriesAsync(queries, null, versionUpdates);

                const events = rows.__version_change_events;
                const taskVersionUpdated = getVersionUpdateForTask(task_id, team_id, events);

                rows.forEach(row => {
                    if (row.id) {
                        sqsNotif.sendNotifMessage('createTaskNotifications', [userid, task_id, row.id, {}]);
                        webhook.sendWebhookMessage('taskUpdated', {
                            team_id,
                            hist_id: row.id,
                            task_id,
                            userid,
                        });
                    }
                });

                if (options.resolved) {
                    triggerAutomationIfNeeded([task_id], team_id, options).catch(e => logCheckError(e, 'CHECK_047'));
                }

                _getChecklists(
                    userid,
                    null,
                    { checklist_id, skip_access: true, returnItem: checklist_item_id },
                    (checklistErr, checklistResult) => {
                        if (checklistErr) {
                            cb(checklistErr);
                        } else {
                            if (checklistResult.returnItem) {
                                sqsWs.sendWSMessage('sendChecklistItemEdited', [
                                    userid,
                                    task_id,
                                    checklist_id,
                                    old_checklist_parent,
                                    checklistResult.returnItem,
                                    {
                                        ws_key: options.ws_key,
                                    },
                                ]);

                                sqsWs.sendWSMessage('sendTaskUpdated', [
                                    task_id,
                                    {
                                        userid,
                                        ws_key: options.ws_key,
                                        hist_ids: rows?.map(row => row.id),
                                        date_updated: new Date().getTime(),
                                    },
                                ]);
                            }

                            cb(null, {
                                checklist: checklistResult.checklists[0],
                                version: taskVersionUpdated,
                            });
                            subcatHelper.invalidateCachedSubcatTaskCount([subcategory_id]);
                        }
                    }
                );
            } catch (batchErr) {
                logger.error({
                    msg: 'Failed to edit checklist item',
                    err: batchErr,
                });
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'CHECK_012',
                });
            }
        }
    );
}
export { _editChecklistItem };

export function editChecklistItem(req, resp) {
    const userid = req.decoded_token.user;
    const { checklist_item_id } = req.params;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;

    const options = req.body;
    options.ws_key = ws_key;

    _editChecklistItem(userid, checklist_item_id, options, (err, result) => {
        if (err) {
            resp.status(err.status).send({ err: err.err, ECODE: err.ECODE });
        } else {
            resp.status(200).send(result);
        }
    });
}

function _editChecklistItems(userid, options, cb) {
    if (options.checklist_item_ids.length === 0) {
        cb(null, {});
        return;
    }

    const permissions = [];

    if (options.resolved != null) {
        permissions.push(config.permission_constants.can_resolve_checklist_item_if_assigned);
    }

    if (options.assignee || options.group_assignee) {
        permissions.push(config.permission_constants.edit_checklists);
    }

    async.parallel(
        {
            accessChecklistItems(para_cb) {
                async.map(
                    options.checklist_item_ids,
                    (checklist_item_id, map_cb) => {
                        access.checkAccessChecklistItem(userid, checklist_item_id, { permissions }, map_cb);
                    },
                    para_cb
                );
            },
            checklistItems(para_cb) {
                db.replicaQuery(
                    'SELECT checklist_items.*, checklists.task_id FROM task_mgmt.checklists, task_mgmt.checklist_items WHERE checklist_items.id = ANY($1) AND checklists.id = checklist_items.checklist_id',
                    [options.checklist_item_ids],
                    (err, result) => {
                        if (err) {
                            logger.error({
                                msg: 'Failed to get checklist items for edit',
                                err,
                            });
                            para_cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'CHECK_021',
                            });
                        } else {
                            para_cb(null, result);
                        }
                    }
                );
            },
            accessParent(para_cb) {
                if (options.parent) {
                    access.checkAccessChecklistItem(
                        userid,
                        options.parent,
                        { permissions: [config.permission_constants.edit_checklists] },
                        para_cb
                    );
                } else {
                    para_cb();
                }
            },
            validateAssignee(para_cb) {
                if (!options.assignee) {
                    para_cb();
                    return;
                }

                async.each(
                    options.checklist_item_ids,
                    (checklist_item_id, each_cb) => {
                        access.checkAccessChecklistItem(
                            options.assignee,
                            checklist_item_id,
                            { permissions: [], checkJoined: false },
                            each_cb
                        );
                    },
                    err => {
                        if (err) {
                            para_cb({
                                err: 'Assignee must have access to checklist items',
                                status: 400,
                                ECODE: 'CHECK_027',
                            });
                        } else {
                            para_cb();
                        }
                    }
                );
            },
            async validateGroupAssignee(para_cb) {
                if (!options.group_assignee) {
                    para_cb();
                    return;
                }

                const checkGroupsHasAccessToTeamPromises = options.checklist_item_ids.map(checklist_item_id =>
                    groupMod.checkGroupsHasAccessToTeam(
                        [options.group_assignee],
                        'checklist_item',
                        checklist_item_id,
                        {}
                    )
                );

                try {
                    await Promise.all(checkGroupsHasAccessToTeamPromises);
                    para_cb();
                } catch (error) {
                    para_cb(error);
                }
            },
        },
        async (err, result) => {
            if (err) {
                cb(err);
                return;
            }

            const { accessChecklistItems } = result;
            const checklistAccessMap = _.mapKeys(accessChecklistItems, ({ checklist_item_id }) => checklist_item_id);
            let subcategory_ids = [];

            const team_id = result.accessChecklistItems?.[0]?.team_id;

            result.checklistItems.rows = result.checklistItems.rows.filter(row => {
                const {
                    role_permissions: { permission_level },
                } = checklistAccessMap[row.id];

                subcategory_ids = subcategory_ids.concat(Object.values(checklistAccessMap[row.id].subcategory_map));

                if (permission_level !== 3) {
                    return true;
                }

                return userid === row.assignee;
            });

            if (!result.checklistItems.rows.length) {
                cb(new CheckError('Must be assigned to some or all checklist items to resolve', 'CHECK_034', 400));
                return;
            }

            options.checklist_item_ids = result.checklistItems.rows.map(row => row.id);

            const queries = [];
            let checklist_id = null;
            let task_id = null;

            result.checklistItems.rows.forEach(row => {
                ({ checklist_id, task_id } = row);
                if (options.assignee && row.assignee !== options.assignee) {
                    queries.push({
                        query: 'INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) VALUES ((SELECT task_id FROM task_mgmt.checklists WHERE id = $1), $2, $3, $4, $5, $6, $7) RETURNING id',
                        params: [
                            checklist_id,
                            'checklist_item_assignee',
                            new Date().getTime(),
                            row.assignee || row.group_assignee,
                            options.assignee,
                            userid,
                            { checklist_item_id: row.id },
                        ],
                    });
                }

                if (options.group_assignee && row.group_assignee !== options.group_assignee) {
                    queries.push({
                        query: 'INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) VALUES ((SELECT task_id FROM task_mgmt.checklists WHERE id = $1), $2, $3, $4, $5, $6, $7) RETURNING id',
                        params: [
                            checklist_id,
                            'checklist_item_assignee',
                            new Date().getTime(),
                            row.group_assignee || row.assignee,
                            options.group_assignee,
                            userid,
                            { checklist_item_id: row.id },
                        ],
                    });
                }

                if (options.resolved != null && row.resolved !== options.resolved) {
                    queries.push({
                        query: 'INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) VALUES ((SELECT task_id FROM task_mgmt.checklists WHERE id = $1), $2, $3, $4, $5, $6, $7) RETURNING id',
                        params: [
                            checklist_id,
                            'checklist_item_resolved',
                            new Date().getTime(),
                            row.resolved,
                            options.resolved,
                            userid,
                            { checklist_item_id: row.id },
                        ],
                    });
                }
            });

            const checklist_id_map = _.keyBy(result.checklistItems.rows, 'id');

            options.checklist_item_ids.forEach(checklist_item_id => {
                const checklist_item = checklist_id_map[checklist_item_id];

                if (options.group_assignee && checklist_item && checklist_item.assignee) {
                    options.assignee = null;
                } else if (options.assignee && checklist_item && checklist_item.group_assignee) {
                    options.group_assignee = null;
                }

                let query = `UPDATE task_mgmt.checklist_items SET `;
                const params = [checklist_item_id];

                Object.keys(options).forEach(key => {
                    if (['resolved', 'assignee', 'parent', 'group_assignee'].includes(key)) {
                        if (params.length > 1) {
                            query += `, `;
                        }
                        params.push(options[key]);
                        query += ` ${key} = $${params.length}`;
                    }
                });

                query += ` WHERE id = $1`;
                queries.push({
                    query,
                    params,
                });
            });

            const versionUpdates = [];
            if (team_id && task_id) {
                versionUpdates.push({
                    object_id: task_id,
                    object_type: ObjectType.TASK,
                    operation: OperationType.UPDATE,
                    workspace_id: team_id,
                    data: {
                        context: {
                            ws_key: options.ws_key,
                        },
                    },
                });
            } else {
                logger.error({
                    msg: 'Missing workspace ID or task id for OVM update',
                    ECODE: 'OVM_WS_931',
                    OVM_CRITICAL: true,
                    team_id,
                    task_id,
                });
            }

            try {
                const rows = await batchQueriesAsync(queries, null, versionUpdates);
                const taskVersionUpdated = getVersionUpdateForTask(task_id, team_id, rows.__version_change_events);
                rows.forEach(row => {
                    if (row.id) {
                        sqsNotif.sendNotifMessage('createTaskNotifications', [userid, task_id, row.id, {}]);
                        webhook.sendWebhookMessage('taskUpdated', {
                            team_id,
                            hist_id: row.id,
                            task_id,
                            userid,
                        });
                    }
                });

                if (options.resolved) {
                    const task_ids = [...new Set(result.checklistItems.rows.map(row => row.task_id))];
                    triggerAutomationIfNeeded(task_ids, team_id, options).catch(e => logCheckError(e, 'CHECK_046'));
                }

                _getChecklists(userid, null, { checklist_id, skip_access: true }, (checklistErr, checklistResult) => {
                    if (checklistErr) {
                        cb(checklistErr);
                    } else {
                        cb(null, {
                            checklist: checklistResult.checklists[0],
                            version: taskVersionUpdated,
                        });
                        if (checklistResult.checklists[0]) {
                            sqsWs.sendWSMessage('sendChecklistEdited', [
                                userid,
                                checklist_id,
                                { ws_key: options.ws_key },
                            ]);
                        }
                    }
                });
            } catch (batchErr) {
                logger.error({
                    msg: 'Failed to edit checklist item',
                    err: batchErr,
                });
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'CHECK_022',
                });
            }
        }
    );
}

export function editChecklistItems(req, resp) {
    const userid = req.decoded_token.user;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;

    const options = req.body;
    options.ws_key = ws_key;

    _editChecklistItems(userid, options, (err, result) => {
        if (err) {
            resp.status(err.status).send({ err: err.err, ECODE: err.ECODE });
        } else {
            resp.status(200).send(result);
        }
    });
}

function _editChecklistItemsIndex(userid, checklist_id, options, cb) {
    const { items = [] } = options;

    if (!Array.isArray(items)) {
        cb({ err: 'Items is not an array', status: 400, ECODE: 'CHECK_036' });
        return;
    }

    async.parallel(
        {
            access(para_cb) {
                access.checkAccessChecklist(
                    userid,
                    checklist_id,
                    { permissions: [config.permission_constants.edit_checklists] },
                    para_cb
                );
            },
        },
        paraErr => {
            if (paraErr) {
                cb(paraErr);
                return;
            }

            const queries = [];

            items.forEach(cl_item => {
                queries.push({
                    query: `UPDATE task_mgmt.checklist_items SET orderindex = $1 WHERE id = $2 AND checklist_id = $3`,
                    params: [cl_item.orderindex, cl_item.id, checklist_id],
                });
            });

            db.batchQueries(queries, err => {
                if (err) {
                    cb({ err: 'Internal server error', status: 500, ECODE: 'CHECK_035' });
                } else {
                    cb(null, {});
                    sqsWs.sendWSMessage('sendChecklistEdited', [userid, checklist_id, { ws_key: options.ws_key }]);
                }
            });
        }
    );
}

export function editChecklistItemsIndex(req, resp) {
    const userid = req.decoded_token.user;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    const { checklist_id } = req.params;

    const options = req.body;
    options.ws_key = ws_key;

    _editChecklistItemsIndex(userid, checklist_id, options, (err, result) => {
        if (err) {
            resp.status(err.status).send({ err: err.err, ECODE: err.ECODE });
        } else {
            resp.status(200).send(result);
        }
    });
}

function _undoDeleteChecklist(userid, checklist_id, options, cb) {
    access.checkAccessChecklist(
        userid,
        checklist_id,
        { permissions: [config.permission_constants.edit_checklists] },
        async (err, accessData) => {
            if (err) {
                cb(err);
                return;
            }

            const { team_id } = accessData;
            db.replicaQuery(
                'SELECT task_id FROM task_mgmt.checklists WHERE id = $1',
                [checklist_id],
                async (readErr, result) => {
                    if (readErr) {
                        cb({
                            err: 'Internal server error',
                            status: 500,
                            ECODE: 'CHECK_049',
                        });
                        return;
                    }

                    let task_id;
                    if (result.rows.length) {
                        [{ task_id }] = result.rows;
                    }

                    try {
                        await checkTaskChecklistLimit([task_id], { additionalChecklists: 1, team_id });
                    } catch (limitErr) {
                        cb(limitErr);
                        return;
                    }

                    const queries = [
                        {
                            query: 'UPDATE task_mgmt.checklists SET deleted = false, date_deleted = NULL WHERE id = $1',
                            params: [checklist_id],
                        },
                        {
                            query: 'UPDATE task_mgmt.checklist_items SET deleted = false WHERE checklist_id = $1 AND date_deleted IS NULL RETURNING id',
                            params: [checklist_id],
                        },
                    ];

                    const versionRequests = getTaskVersionUpdates(
                        task_id,
                        team_id,
                        OperationType.UPDATE,
                        options?.ws_key,
                        'CHECK_62'
                    );

                    let rows;
                    try {
                        rows = await batchQueriesAsync(queries, undefined, versionRequests);
                    } catch (batchErr) {
                        cb({
                            err: 'Internal server error',
                            status: 500,
                            ECODE: 'CHECK_013',
                        });
                        return;
                    }

                    const taskVersionUpdated = getVersionUpdateForTask(task_id, team_id, rows.__version_change_events);

                    cb(null, { version: taskVersionUpdated });
                    const checklist_item_ids = rows.map(row => row.id);

                    // update task history
                    // TODO having update history to be within the same transaction as previous update queries
                    db.writeQuery(
                        "DELETE FROM task_mgmt.task_history WHERE ((field = 'checklist_item_added' AND after = ANY($1)) OR (field = 'checklist_item_resolved' AND data->>'checklist_item_id' = ANY($1)) OR (field = 'checklist_item_assignee' AND data->>'checklist_item_id' = ANY($1))) AND task_id = (SELECT task_id FROM task_mgmt.checklists WHERE checklists.id = $2)",
                        [checklist_item_ids, checklist_id],
                        histErr => {
                            if (histErr) {
                                logger.error({
                                    msg: 'Failed to delete history items for checklist items',
                                    err: histErr,
                                });
                            }
                        }
                    );

                    // send ws message
                    _getChecklists(
                        userid,
                        null,
                        { checklist_id, skip_access: true },
                        (checklistErr, checkListResult) => {
                            if (checklistErr) {
                                // checklistErr
                            } else if (checkListResult.checklists[0]) {
                                sqsWs.sendWSMessage('sendChecklistCreated', [
                                    userid,
                                    checkListResult.checklists[0].task_id,
                                    checkListResult.checklists[0],
                                    {
                                        ws_key: options?.ws_key,
                                    },
                                ]);
                            }
                        }
                    );
                }
            );
        }
    );
}

export function undoDeleteChecklist(req, resp) {
    const userid = req.decoded_token.user;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    const { checklist_id } = req.params;

    _undoDeleteChecklist(userid, checklist_id, { ws_key }, (err, result) => {
        if (err) {
            resp.status(err.status).send({ err: err.err, ECODE: err.ECODE });
        } else {
            resp.status(200).send(result);
        }
    });
}

function _undoDeleteChecklistItem(userid, checklist_item_id, options, cb) {
    access.checkAccessChecklistItem(
        userid,
        checklist_item_id,
        { permissions: [config.permission_constants.edit_checklists] },
        async (err, accessResult) => {
            if (err) {
                cb(err);
                return;
            }

            const { team_id, task_id, checklist_id: parentCheckListId } = accessResult;

            try {
                await checkChecklistItemLimit(parentCheckListId, { ...options, additionalChecklistItems: 1, team_id });
            } catch (limitErr) {
                cb(limitErr);
                return;
            }

            const ovm = getObjectVersionManager();
            let events;
            const updateVersions = [];
            if (task_id && team_id) {
                updateVersions.push({
                    object_id: task_id,
                    object_type: ObjectType.TASK,
                    operation: OperationType.UPDATE,
                    workspace_id: team_id,
                    data: {
                        context: {
                            ws_key: options.ws_key,
                        },
                    },
                });
            } else {
                logger.error({
                    msg: 'Missing workspace ID or task ID for OVM update',
                    ECODE: 'OVM_WS_944',
                    OVM_CRITICAL: true,
                    task_id,
                    team_id,
                });
            }
            db.getCopyConn(cb, { label: '_undoDeleteChecklistItem' }, (connErr, client, done) => {
                let checklist_item_ids = [checklist_item_id];
                let all_checklist_item_ids = [checklist_item_id];
                let checklist_id;
                async.series(
                    [
                        series_cb => {
                            client.query('BEGIN', series_cb);
                        },
                        series_cb => {
                            client.query(
                                'UPDATE task_mgmt.checklist_items SET deleted = false, date_deleted = null WHERE id = $1 returning checklist_id',
                                [checklist_item_id],
                                (updateErr, result) => {
                                    if (updateErr) {
                                        series_cb(updateErr);
                                    } else {
                                        if (result.rows[0]) {
                                            [{ checklist_id }] = result.rows;
                                        }
                                        series_cb();
                                    }
                                }
                            );
                        },
                        series_cb => {
                            async.whilst(
                                () => checklist_item_ids.length > 0,
                                whilst_cb => {
                                    client.query(
                                        'UPDATE task_mgmt.checklist_items SET deleted = false WHERE parent = ANY($1) AND checklist_id = $2 AND deleted = true AND date_deleted IS NULL RETURNING id',
                                        [checklist_item_ids, checklist_id],
                                        (updateErr, result) => {
                                            if (updateErr) {
                                                whilst_cb(updateErr);
                                            } else {
                                                checklist_item_ids = result.rows
                                                    .map(row => row.id)
                                                    .filter(item_id => !all_checklist_item_ids.includes(item_id));
                                                all_checklist_item_ids =
                                                    all_checklist_item_ids.concat(checklist_item_ids);
                                                whilst_cb();
                                            }
                                        }
                                    );
                                },
                                whilstErr => {
                                    series_cb(whilstErr);
                                }
                            );
                        },
                        series_cb => {
                            client.query(
                                `
                                UPDATE task_mgmt.task_history 
                                SET    deleted = false 
                                WHERE  (( field = 'checklist_item_added' 
                                        AND after = ANY ( $1 ) ) 
                                        OR ( field = 'checklist_item_resolved' 
                                            AND data ->> 'checklist_item_id' = ANY ( $1 ) ) 
                                        OR ( field = 'checklist_item_assignee' 
                                            AND data ->> 'checklist_item_id' = ANY ( $1 ) ))
                                        AND task_id = (SELECT task_id 
                                                        FROM   task_mgmt.checklists 
                                                        WHERE  checklists.id = $2) 
                                `,
                                [all_checklist_item_ids, checklist_id],
                                updateErr => {
                                    series_cb(updateErr);
                                }
                            );
                        },
                        async series_cb => {
                            const txClient = new TransactionClientImpl(client, ovm);
                            try {
                                events = await ovm.updateVersions(txClient, updateVersions);
                            } catch (ovmErr) {
                                logger.error({
                                    msg: 'Failed to persist OVM update',
                                    ECODE: 'OVM_WS_945',
                                    OVM_CRITICAL: true,
                                    err: ovmErr,
                                });

                                series_cb(ovmErr);
                                return;
                            }
                            series_cb();
                        },
                        series_cb => {
                            client.query('COMMIT', series_cb);
                        },
                        async series_cb => {
                            // eslint-disable-next-line @typescript-eslint/no-empty-function
                            await ovm.notifyChanges(events).catch(() => { });
                            series_cb();
                        },
                    ],
                    seriesErr => {
                        if (seriesErr) {
                            db.rollback(client, done);
                            logger.error({
                                msg: 'Failed to undo delete checklist item',
                                err: seriesErr,
                            });
                            cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'CHECK_014',
                            });
                        } else {
                            done();
                            const taskVersionUpdated = getVersionUpdateForTask(task_id, team_id, events);
                            cb(null, { version: taskVersionUpdated });
                            if (checklist_id) {
                                sqsWs.sendWSMessage('sendChecklistEdited', [
                                    userid,
                                    checklist_id,
                                    { ws_key: options.ws_key },
                                ]);
                            }
                        }
                    }
                );
            });
        }
    );
}

export function undoDeleteChecklistItem(req, resp) {
    const userid = req.decoded_token.user;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    const { checklist_item_id } = req.params;

    _undoDeleteChecklistItem(userid, checklist_item_id, { ws_key }, (err, result) => {
        if (err) {
            resp.status(err.status).send({
                err: err.err,
                ECODE: err.ECODE,
            });
        } else {
            resp.status(200).send(result);
        }
    });
}

async function getChecklistsForPermanentDeletion(batchSize) {
    const query = `
        SELECT id, task_id
        FROM task_mgmt.checklists
        WHERE deleted = true
            AND date_deleted < $1
            AND NOT EXISTS (
                SELECT FROM task_mgmt.workspace_to_shard AS wts
                WHERE wts.workspace_id = task_mgmt.checklists.workspace_id
                    AND (wts.shard_id <> $2 OR wts.migrating IS TRUE)
            )
        LIMIT $3`;
    const params = [trashRetentionPolicyHelper.getHardDeletionCutoffFromNow(), CURRENT_SHARD_ID, batchSize];
    const result = await db.replicaQueryAsync(query, params);
    return result.rows;
}
export { getChecklistsForPermanentDeletion };

async function _permanentlyDeleteChecklists(candidates) {
    const checklist_ids = candidates.map(c => c.id);
    const task_ids = candidates.map(c => c.task_id);

    // firstly, delete from task history,
    try {
        // get the IDs of the checklist items
        const result = await db.replicaQueryAsync(
            'SELECT id FROM Task_mgmt.checklist_items WHERE checklist_id = ANY($1)',
            [checklist_ids]
        );
        const checklist_item_ids = result.rows.map(row => row.id);
        await db.writeQueryAsync(
            "DELETE FROM task_mgmt.task_history WHERE task_id = ANY($2) AND ((field = 'checklist_item_added' AND after = ANY($1)) OR (field = 'checklist_item_resolved' AND data->>'checklist_item_id' = ANY($1)) OR (field = 'checklist_item_assignee' AND data->>'checklist_item_id' = ANY($1)))",
            [checklist_item_ids, task_ids]
        );
    } catch (err) {
        logger.error({
            msg: 'Failed to delete history items for checklist items',
            err,
        });
        throw err;
    }

    // secondly, delete from checklist_items
    try {
        const items = await db.writeQueryAsync(
            'DELETE FROM Task_mgmt.checklist_items WHERE checklist_id = ANY($1) RETURNING id',
            [checklist_ids]
        );
        metricsClient.increment('checklist.permanentlyDeleteChecklists.items.deleteCount', items.rows.length);
    } catch (err) {
        logger.error({
            msg: 'Failed to permanently delete checklists items',
            err,
            ECODE: 'CHECK_016',
        });
        throw err;
    }

    // delete from the check list at last
    try {
        const ids = await db.writeQueryAsync('DELETE FROM task_mgmt.checklists WHERE id = ANY($1) RETURNING id', [
            checklist_ids,
        ]);
        metricsClient.increment('checklist.permanentlyDeleteChecklists.deleteCount', ids.rows.length);
    } catch (err) {
        logger.error({
            msg: 'Failed to permanently delete checklists',
            err,
            ECODE: 'CHECK_015',
        });
        throw err;
    }
}
export { _permanentlyDeleteChecklists };

async function getChecklistItemsForPermanentDeletion(batchSize) {
    const query = `
        SELECT id, checklist_id
        FROM task_mgmt.checklist_items
        WHERE deleted = true
            AND date_deleted < $1
            AND date_deleted IS NOT NULL
            AND NOT EXISTS (
                SELECT FROM task_mgmt.workspace_to_shard AS wts
                WHERE wts.workspace_id = task_mgmt.checklist_items.workspace_id
                    AND (wts.shard_id <> $2 OR wts.migrating IS TRUE)
            )
        LIMIT $3`;
    const params = [trashRetentionPolicyHelper.getHardDeletionCutoffFromNow(), CURRENT_SHARD_ID, batchSize];
    const result = await db.replicaQueryAsync(query, params);
    return result.rows;
}
export { getChecklistItemsForPermanentDeletion };

async function _permanentlyDeleteChecklistItems(candidates) {
    const checklist_item_ids = candidates.map(c => c.id);
    const checklist_ids = candidates.map(c => c.checklist_id);

    try {
        await db.writeQueryAsync(
            "DELETE FROM task_mgmt.task_history WHERE task_id IN (SELECT task_id FROM task_mgmt.checklists WHERE id = ANY($2)) AND ((field = 'checklist_item_added' AND after = ANY($1)) OR (field = 'checklist_item_resolved' AND data->>'checklist_item_id' = ANY($1)) OR (field = 'checklist_item_assignee' AND data->>'checklist_item_id' = ANY($1)))",
            [checklist_item_ids, checklist_ids]
        );
    } catch (err) {
        logger.error({
            msg: 'Failed to delete history items for checklist items',
            err,
        });
        throw err;
    }

    try {
        const ids = await db.writeQueryAsync(`DELETE FROM task_mgmt.checklist_items WHERE id = ANY($1) RETURNING id`, [
            checklist_item_ids,
        ]);
        metricsClient.increment('checklist.permanentlyDeleteChecklistItems.deleteCount', ids.rows.length);
    } catch (err) {
        logger.error({
            msg: 'Failed to permanently delete checklist items',
            err,
            ECODE: 'CHECK_017',
        });
        throw err;
    }
}
export { _permanentlyDeleteChecklistItems };

function _getCheckListItemsCount(task_ids, cb) {
    db.replicaQuery(
        `SELECT coalesce(q1.task_id, q2.task_id) as task_id, resolved, unresolved
        FROM   (SELECT Count(*) AS resolved, 
                       checklists.task_id 
                FROM   task_mgmt.checklists, 
                       task_mgmt.checklist_items 
                WHERE  checklists.id = checklist_items.checklist_id 
                       AND checklists.deleted = false 
                       AND checklist_items.deleted = false 
                       AND checklist_items.resolved = true
                       AND checklists.task_id = ANY ( $1 ) 
                GROUP  BY task_id) AS q1 
               FULL OUTER JOIN (SELECT Count(*) AS unresolved, 
                            checklists.task_id 
                     FROM   task_mgmt.checklists, 
                            task_mgmt.checklist_items 
                     WHERE  checklists.id = checklist_items.checklist_id 
                            AND checklists.deleted = false 
                            AND checklist_items.deleted = false 
                            AND resolved = false 
                            AND checklists.template = false 
                            AND checklists.deleted = false 
                            AND checklists.task_id = ANY ( $1 ) 
                     GROUP  BY task_id) AS q2 
                 ON q1.task_id = q2.task_id`,
        [task_ids],
        (err, result) => {
            if (err) {
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'CHECK_018',
                });
            } else {
                const checklist_counts = {};
                result.rows.forEach(row => {
                    checklist_counts[row.task_id] = {
                        resolved: Number(row.resolved) || 0,
                        unresolved: Number(row.unresolved) || 0,
                    };
                });
                cb(null, checklist_counts);
            }
        }
    );
}
export { _getCheckListItemsCount };

function _createChecklistHistoryItem(row, _obj_to_push, cb) {
    const obj_to_push = _obj_to_push;
    // we need to get the checklist info w/o the items and the checklist item info
    let checklist_item_ids = null;
    if (row.field === 'checklist_items_added') {
        checklist_item_ids = row.after.split('|');
    } else if (row.field === 'checklist_item_added') {
        checklist_item_ids = [row.after];
    } else {
        checklist_item_ids = [row.data.checklist_item_id];
    }

    async.parallel(
        {
            checklist(para_cb) {
                db.replicaQuery(
                    'SELECT task_id, checklists.name as checklist_name, checklists.show_completed, checklists.orderindex as checklist_orderindex, checklists.id as check_id, checklists.encrypted as checklist_encrypted, checklist_items.*, users.id as assignee_id, users.username as username, users.color as color, users.email as email, users.profile_picture_key  FROM task_mgmt.checklists, (SELECT * FROM task_mgmt.checklist_items WHERE id = ANY($1)) as checklist_items LEFT OUTER JOIN task_mgmt.users ON checklist_items.assignee = users.id WHERE checklists.id = checklist_items.checklist_id ORDER BY checklist_items.date_created ASC',
                    [checklist_item_ids],
                    (err, result) => {
                        if (err) {
                            logger.error({
                                msg: 'Failed to fetch checklist for history',
                                err,
                                ECODE: 'CHECK_019',
                            });
                            para_cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'CHECK_019',
                            });
                        } else if (result.rows.length === 0) {
                            para_cb({
                                err: 'Checklist not found',
                                status: 500,
                                ECODE: 'CHECK_029',
                            });
                        } else {
                            const checklist_row = result.rows[0];

                            if (checklist_row.checklist_encrypted) {
                                checklist_row.checklist_name = encrypt.decrypt(checklist_row.checklist_name);
                            }

                            obj_to_push.checklist = {
                                id: checklist_row.check_id,
                                task_id: checklist_row.task_id,
                                name: checklist_row.checklist_name,
                                orderindex: checklist_row.checklist_orderindex,
                                show_completed: checklist_row.show_completed,
                            };

                            const checklist_items = [];
                            result.rows.forEach(inRow => {
                                const _row = inRow;
                                if (_row.encrypted) {
                                    try {
                                        _row.name = encrypt.decrypt(_row.name);
                                    } catch (e) {
                                        // failed to decrypt
                                    }
                                }

                                const checklist_item = {
                                    id: _row.id,
                                    name: _row.name,
                                    orderindex: _row.orderindex,
                                    assignee: null,
                                    resolved: _row.resolved,
                                    parent: _row.parent,
                                    children: [],
                                };
                                if (_row.assignee_id) {
                                    checklist_item.assignee = {
                                        id: _row.assignee_id,
                                        username: _row.username,
                                        email: _row.email,
                                        color: _row.color,
                                        initials: userInitials.getInitials(_row.email, _row.username),
                                        profilePicture: cf_sign.getCloudfrontAvatarUrl(_row.profile_picture_key),
                                    };
                                }

                                if (row.field !== 'checklist_items_added') {
                                    obj_to_push.checklist_item = checklist_item;
                                }
                                checklist_items.push(checklist_item);
                            });

                            obj_to_push.checklist_items = checklist_items;

                            para_cb();
                        }
                    }
                );
            },
        },
        err => {
            if (err) {
                cb(err);
            } else {
                cb(null, obj_to_push);
            }
        }
    );
}
export { _createChecklistHistoryItem };

/**
 * Creates a history item for a checklist operation
 * @param {Object} row - The row data containing checklist operation details
 * @param {Object} options - Options for creating the history item
 * @param {boolean} options.skip_access - Whether to skip access checks
 * @param {boolean} options.skip_treeify - Whether to skip building the tree structure
 * @param {boolean} options.include_creator - Whether to include creator information
 * @param {boolean} options.carry_item_state - Whether to carry over checklist item resolved state
 * @param {Object} options.proxyClient - Query proxy client for database operations
 * @param {Function} cb - Callback function
 */
function _copyChecklists(task_id, new_id, client, options, cb) {
    const { proxyClient = queryProxyFactory() } = options;

    _getChecklists(
        null,
        task_id,
        { skip_access: true, skip_treeify: true, include_creator: true, proxyClient },
        async (err, result) => {
            if (err) {
                cb(err);
                return;
            }
            const { checklists } = result;
            const queries = [];

            if (checklists.length === 0) {
                cb();
                return;
            }

            try {
                await checkTaskChecklistLimit([new_id], { client, additionalChecklists: checklists.length });
                await Promise.all(
                    checklists.map(async checklist => {
                        const new_checklist_id = uuid.v4();
                        let orderindex_query = `coalesce((SELECT max(orderindex) FROM task_mgmt.checklists WHERE task_id = $2), 0) + 1`;

                        let checklist_show_completed = checklist.show_completed;
                        if (typeof checklist.show_completed !== 'boolean') {
                            checklist_show_completed = true;
                        }

                        let checklist_orderindex = checklist.orderindex;
                        if (typeof checklist.orderindex !== 'number' && typeof checklist.orderindex !== 'string') {
                            checklist_orderindex = 0;
                        }

                        let checklist_name = checklist.name;
                        if (checklist.encrypted) {
                            checklist_name = encrypt.encrypt(checklist.name);
                        }

                        const params = [
                            new_checklist_id,
                            new_id,
                            checklist_name,
                            options.userid || checklist.creator,
                            options.carry_item_state ?? false,
                            checklist.date_created,
                            checklist.encrypted,
                        ];

                        if (!options.from_template) {
                            params.push(checklist_orderindex);
                            orderindex_query = `$${params.length}`;
                        }

                        queries.push({
                            query: `INSERT INTO task_mgmt.checklists(id, task_id, name, creator, date_created, orderindex, show_completed, template, deleted, encrypted, workspace_id) VALUES ($1, $2, $3, $4, $5, ${orderindex_query}, $6, false, false, $7, (SELECT workspace_id FROM task_mgmt.tasks where id = $2))`,
                            params,
                        });

                        const new_checklist_item_ids = {};
                        checklist.items.forEach(checklist_item => {
                            const new_checklist_item_id = uuid.v4();
                            new_checklist_item_ids[checklist_item.id] = new_checklist_item_id;
                        });

                        const template_user_id = config.get('template_user');

                        await checkChecklistItemLimit(new_checklist_id, {
                            client,
                            additionalChecklistItems: checklist.items.length,
                        });

                        checklist.items.forEach(checklist_item => {
                            const new_checklist_item_id = new_checklist_item_ids[checklist_item.id];
                            let assignee = null;
                            if (checklist_item.assignee) {
                                assignee = options.public_template ? template_user_id : checklist_item.assignee.id;
                            }

                            let group_assignee;
                            if (checklist_item.group_assignee && !options.public_template) {
                                group_assignee = checklist_item.group_assignee.id;
                            }

                            let checklist_item_name = checklist_item.name;
                            if (checklist_item.encrypted) {
                                checklist_item_name = encrypt.encrypt(checklist_item.name);
                            }

                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.checklist_items(
                                        checklist_id, id, parent, name, orderindex, date_created, creator, assignee, 
                                        resolved, deleted, encrypted, group_assignee, workspace_id,
                                        start_date, start_date_time, due_date, due_date_time, sent_due_date_notif) 
                                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, false, $10, $11, 
                                        (SELECT workspace_id FROM task_mgmt.checklists where id = $1),
                                        $12, $13, $14, $15, $16)`,
                                params: [
                                    new_checklist_id, // 1
                                    new_checklist_item_id,
                                    new_checklist_item_ids[checklist_item.parent],
                                    checklist_item_name,
                                    checklist_item.orderindex, // 5
                                    checklist_item.date_created,
                                    options.userid || checklist_item.creator,
                                    assignee,
                                    options.carry_item_state ?? false,
                                    checklist_item.encrypted, // 10
                                    group_assignee,
                                    checklist_item.start_date, // 12
                                    checklist_item.start_date_time,
                                    checklist_item.due_date,
                                    checklist_item.due_date_time,
                                    checklist_item.sent_due_date_notif,
                                ],
                            });

                            if (assignee) {
                                queries.push({
                                    query: `INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id, task_id, field, after, userid`,
                                    params: [
                                        new_id, // this is the newly create task.id after the template is copied
                                        'checklist_item_assignee',
                                        new Date().getTime(),
                                        null,
                                        assignee,
                                        options.userid || checklist_item.creator,
                                        { checklist_item_id: new_checklist_item_id },
                                    ],
                                });

                                // Also adds the user as a follower of the task
                                // if not already a follower
                                if (assignee !== options.userid) {
                                    queries.push({
                                        query: 'INSERT INTO task_mgmt.followers(task_id, userid, workspace_id) SELECT $1, $2, $3 WHERE NOT EXISTS (SELECT 1 FROM task_mgmt.followers WHERE task_id = $1 and userid = $2)',
                                        params: [new_id, assignee, options.team_id],
                                    });
                                }
                            }
                        });
                    })
                );
            } catch (e) {
                cb(e);
                return;
            }

            try {
                const destinationWorkspaceId = await entitiesService.getTeamId({
                    task_ids: [new_id],
                });

                const versionUpdates = [];
                if (destinationWorkspaceId) {
                    versionUpdates.push({
                        object_id: new_id,
                        object_type: ObjectType.TASK,
                        workspace_id: destinationWorkspaceId,
                        operation: OperationType.UPDATE,
                        data: {
                            context: {
                                ws_key: options.ws_key,
                            },
                        },
                    });
                } else {
                    logger.error({
                        msg: 'Missing workspace ID for OVM update',
                        ECODE: 'OVM_WS_961',
                        OVM_CRITICAL: true,
                        task_id,
                    });
                }
                let rows = [];
                if (client) {
                    if (Array.isArray(client.versionUpdates)) {
                        client.versionUpdates.push(...versionUpdates);
                    }
                    const ovm = getObjectVersionManager();
                    const txClient = new TransactionClientImpl(client, ovm);

                    rows = await batchQueriesWithClientAsync(queries, txClient, false);
                } else {
                    rows = await batchQueriesAsync(queries, null, versionUpdates);
                }

                // Send notifications for the new checklist items from a template to the assignee.
                rows.forEach(row => {
                    if (row.id && row?.field === 'checklist_item_assignee') {
                        sqsNotif.sendNotifMessage('createTaskNotifications', [row.userid, row.task_id, row.id]);
                    }
                });

                cb();
            } catch (batchErr) {
                cb(batchErr);
            }
        }
    );
}
export { _copyChecklists };

function _copyChecklist(userid, checklist_id, _options, cb) {
    const options = _options;
    const { client } = options;
    let { team_id } = options;
    async.parallel(
        {
            getChecklist(para_cb) {
                _getChecklists(
                    null,
                    null,
                    {
                        checklist_id,
                        skip_access: true,
                        skip_treeify: true,
                        include_creator: true,
                        templates: true,
                    },
                    para_cb
                );
            },
            async validateTemplateMembers(para_cb) {
                if (!options.template_v2 || options.template_visibility !== config.template_permissions.members) {
                    para_cb();
                    return;
                }
                if (!options.template_members || !options.template_members.length) {
                    para_cb(new CheckError('template members cannot be empty', 'CHECK_040', 400));
                    return;
                }
                let response;
                try {
                    response = await db.promiseReplicaQuery(
                        `
                            SELECT projects.team 
                            FROM task_mgmt.items
                            JOIN task_mgmt.subcategories
                                ON items.subcategory = subcategories.id
                            JOIN task_mgmt.categories
                                ON categories.id = subcategories.category 
                            JOIN task_mgmt.projects 
                                ON projects.id = categories.project_id 
                            WHERE 
                                items.id = $1
                    `,
                        [options.template_task_id]
                    );
                } catch (err) {
                    para_cb(new CheckError(err, 'CHECK_041', 500));
                    return;
                }
                if (!response.rows || !response.rows.length) {
                    para_cb(new CheckError('Could not find checklist', 'CHECK_042', 404));
                    return;
                }
                team_id = response.rows[0].team;
                try {
                    await Promise.all(
                        options.template_members.map(member =>
                            access.promiseAccessTeam(member, team_id, { checkJoined: false })
                        )
                    );
                } catch (err) {
                    para_cb(err);
                    return;
                }
                para_cb();
            },
            async checkTaskChecklistLimits(para_cb) {
                if (!options.task_id) {
                    para_cb();
                    return;
                }
                // Ensure the resultant task would not exceed the limit on checklists per task
                try {
                    await checkTaskChecklistLimit([options.task_id], { team_id, client, additionalChecklists: 1 });
                    para_cb();
                } catch (e) {
                    para_cb(e);
                }
            },
        },
        async (err, result) => {
            if (err) {
                cb(err);
                return;
            }
            if (result.getChecklist.checklists.length === 0) {
                cb({
                    err: 'Checklist not found',
                    status: 400,
                    ECODE: 'CHECK_030',
                });
                return;
            }

            const queries = [];
            const checklist = result.getChecklist.checklists[0];
            logger.debug({ msg: 'Copying checklist', checklist });

            const new_checklist_id = uuid.v4();

            if (typeof checklist.show_completed !== 'boolean') {
                checklist.show_completed = true;
            }

            if (typeof checklist.orderindex !== 'number' && typeof checklist.orderindex !== 'string') {
                checklist.orderindex = 0;
            }

            let encrypted = false;

            if (checklist.encrypted) {
                checklist.name = encrypt.encrypt(checklist.name);
                options.name = encrypt.encrypt(options.name);
                encrypted = true;
            }

            let permanent_template_id = null;

            if (options.template || options.template_v2) {
                permanent_template_id = options.permanent_template_id ?? `t-${new_checklist_id}`;
            }

            let project_id_query = `null`;
            let team_id_query = `null`;
            let permission_query = `null`;
            let workspace_id = team_id;
            let public_sharing_query = `null`;
            const params = [
                new_checklist_id,
                options.template_v2 ? null : options.task_id,
                options.name || checklist.name,
                userid,
                checklist.date_created,
                checklist.show_completed,
                options.template_v2 || options.template || false,
                encrypted,
                options.set_deleted || false,
                options.set_deleted ? new Date().getTime() : null,
                permanent_template_id,
            ];

            if (options.template) {
                options.task_id = null;
                params.push(checklist.task_id);
                project_id_query = `(SELECT project_id FROM task_mgmt.categories, task_mgmt.subcategories, task_mgmt.items WHERE items.id = $${params.length} AND subcategories.id = items.subcategory AND subcategories.category = categories.id)`;
                workspace_id =
                    workspace_id ??
                    `(SELECT projects.team FROM task_mgmt.projects, task_mgmt.categories, task_mgmt.subcategories, task_mgmt.items WHERE items.id = $${params.length} AND subcategories.id = items.subcategory AND subcategories.category = categories.id AND categories.project_id = projects.id)`;
            } else if (options.template_v2) {
                options.task_id = null;
                team_id_query = `(
                    SELECT projects.team 
                    FROM task_mgmt.items
                    JOIN task_mgmt.subcategories
                        ON items.subcategory = subcategories.id
                    JOIN task_mgmt.categories
                        ON categories.id = subcategories.category 
                    JOIN task_mgmt.projects 
                        ON projects.id = categories.project_id 
                    WHERE 
                        items.id = $${params.push(options.template_task_id || checklist.task_id)}
                )`;
                workspace_id = workspace_id ?? team_id_query;
                if (
                    options.template_visibility != null &&
                    Object.values(config.template_permissions).includes(options.template_visibility)
                ) {
                    permission_query = `$${params.push(options.template_visibility)}`;
                }
                if (options.public_sharing !== undefined) {
                    public_sharing_query = `$${params.push(options.public_sharing)}`;
                }
            } else if (!options.task_id) {
                options.task_id = checklist.task_id;
            }

            workspace_id = workspace_id ?? `(SELECT workspace_id FROM task_mgmt.tasks WHERE id = $2)`;

            let orderindex_query = `coalesce((SELECT max(orderindex) FROM task_mgmt.checklists WHERE task_id = $2), 0) + 1`;

            if (!options.from_template) {
                params.push(checklist.orderindex);
                orderindex_query = `$${params.length}`;
            }

            queries.push({
                query: `
                    INSERT INTO task_mgmt.checklists(
                        id, 
                        task_id, 
                        name, 
                        creator, 
                        date_created, 
                        orderindex, 
                        show_completed, 
                        template, 
                        project_id, 
                        deleted, 
                        date_deleted,
                        team_id, 
                        permissions,
                        public_sharing,
                        encrypted,
                        permanent_template_id, 
                        workspace_id
                    ) VALUES (
                        $1, 
                        $2, 
                        $3, 
                        $4, 
                        $5, 
                        ${orderindex_query}, 
                        $6, 
                        $7, 
                        ${project_id_query}, 
                        $9, 
                        $10,
                        ${team_id_query},
                        ${permission_query},
                        ${public_sharing_query},
                        $8,
                        $11,
                        ${workspace_id}
                    )`,
                params,
            });
            if (
                options.template_v2 &&
                options.template_visibility === config.template_permissions.members &&
                options.template_members &&
                options.template_members.length
            ) {
                const member_params = [];
                let query = `
                    INSERT INTO task_mgmt.checklist_template_members
                        (checklist_id, userid, workspace_id)
                    VALUES 
                `;
                options.template_members.forEach(member => {
                    query += `(
                            $${member_params.push(new_checklist_id)},
                            $${member_params.push(member)},
                            $${member_params.push(team_id)}
                    ), `;
                });
                query = query.slice(0, -2);
                queries.push({ query, params: member_params });
            }
            const new_checklist_item_ids = {};
            checklist.items.forEach(checklist_item => {
                const new_checklist_item_id = uuid.v4();
                new_checklist_item_ids[checklist_item.id] = new_checklist_item_id;
            });

            const notifs_to_send = [];

            try {
                await checkChecklistItemLimit(new_checklist_id, {
                    client,
                    team_id,
                    additionalChecklistItems: checklist.items.length,
                });
            } catch (e) {
                cb(e);
                return;
            }

            checklist.items.forEach(checklist_item => {
                const new_checklist_item_id = new_checklist_item_ids[checklist_item.id];
                let assignee = null;
                let group_assignee = null;

                if (!options.public_template && checklist_item.assignee) {
                    assignee = checklist_item.assignee.id;
                }

                if (!options.public_template && checklist_item.group_assignee) {
                    group_assignee = checklist_item.group_assignee.id;
                }

                let checklist_item_name = checklist_item.name;
                if (checklist_item.encrypted) {
                    checklist_item_name = encrypt.encrypt(checklist_item.name);
                }

                queries.push({
                    query: `
                        INSERT INTO task_mgmt.checklist_items(
                            checklist_id, id, parent, name, orderindex, date_created, creator, assignee, 
                            group_assignee, resolved, deleted, encrypted, workspace_id,
                            start_date, start_date_time, due_date, due_date_time, sent_due_date_notif) 
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, false, false, $10, 
                            (SELECT workspace_id FROM task_mgmt.checklists where id = $1),
                            $11, $12, $13, $14, $15)`,
                    params: [
                        new_checklist_id,
                        new_checklist_item_id,
                        new_checklist_item_ids[checklist_item.parent],
                        checklist_item_name,
                        checklist_item.orderindex,
                        checklist_item.date_created,
                        userid,
                        assignee,
                        group_assignee,
                        checklist_item.encrypted,
                        checklist_item.start_date,
                        checklist_item.start_date_time,
                        checklist_item.due_date,
                        checklist_item.due_date_time,
                        checklist_item.sent_due_date_notif,
                    ],
                });

                if (!options.template_v2 && options.task_id && assignee && assignee !== userid) {
                    queries.push({
                        query: 'INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id, after',
                        params: [
                            options.task_id,
                            'checklist_item_assignee',
                            new Date().getTime(),
                            null,
                            assignee,
                            userid,
                            { checklist_item_id: new_checklist_item_id },
                        ],
                    });
                }

                if (!options.template_v2 && options.task_id && group_assignee) {
                    queries.push({
                        query: 'INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id, after',
                        params: [
                            options.task_id,
                            'checklist_item_assignee',
                            new Date().getTime(),
                            null,
                            group_assignee,
                            userid,
                            { checklist_item_id: new_checklist_item_id },
                        ],
                    });
                }
            });

            const versionUpdates = [];
            if (team_id) {
                if (options.task_id) {
                    versionUpdates.push({
                        object_id: options.task_id,
                        object_type: ObjectType.TASK,
                        operation: OperationType.UPDATE,
                        workspace_id: team_id,
                        data: {
                            context: {
                                ws_key: options.ws_key,
                            },
                        },
                    });
                } else {
                    // this is not a failure scenario.
                    // That just means the checklist is becoming a template, which isn't part of any entity.
                    // No model update here.
                }
            } else {
                logger.error({
                    msg: 'Missing workspace ID for OVM update',
                    ECODE: 'OVM_WS_947',
                    OVM_CRITICAL: true,
                });
            }
            if (client) {
                if (Array.isArray(client.versionUpdates)) {
                    client.versionUpdates.push(...versionUpdates);
                }
                async.each(
                    queries,
                    (query, each_cb) => {
                        client.query(query.query, query.params, (queryErr, queryResult) => {
                            if (queryErr) {
                                each_cb(queryErr);
                            } else {
                                if (queryResult.rows && queryResult.rows.length > 0) {
                                    notifs_to_send.push({
                                        userid: queryResult.rows[0].after,
                                        hist_id: queryResult.rows[0].id,
                                        task_id: options.task_id,
                                    });
                                }
                                each_cb();
                            }
                        });
                    },
                    eachErr => {
                        if (eachErr) {
                            logger.error({
                                msg: 'Failed to copy checklist',
                                err: eachErr,
                            });
                            cb(eachErr);
                        } else {
                            cb(null, {
                                id: new_checklist_id,
                                notifs_to_send,
                            });
                        }
                    }
                );
            } else {
                try {
                    const rows = await batchQueriesAsync(queries, null, versionUpdates);
                    rows.forEach(row => {
                        notifs_to_send.push({
                            userid: row.after,
                            hist_id: row.id,
                            task_id: options.task_id,
                        });
                    });
                    cb(null, {
                        id: new_checklist_id,
                        notifs_to_send,
                    });
                } catch (batchErr) {
                    cb({
                        err: 'Internal server error',
                        status: 500,
                        ECODE: 'CHECK_031',
                    });
                }
            }

            if (options[DurationMetricObject.KEY]) {
                DurationMetricObject.emit(options[DurationMetricObject.KEY]);
            }

            options[TemplateTracer.KEY]?.processSourceAndTargetVerifiedData({
                sourceId: checklist.id,
                savedId: new_checklist_id,
                workspaceId: team_id || checklist.workspace_id,
            });
            options[TemplateTracer.KEY]?.finishTracing();
        }
    );
}
export { _copyChecklist };

export async function _promiseCopyChecklist(userid, checklist_id, options) {
    return new Promise((res, rej) => {
        _copyChecklist(userid, checklist_id, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}

function getTaskVersionUpdates(task_id, team_id, operation, ws_key, ecode = '') {
    if (!task_id || !team_id) {
        logger.error({
            msg: 'Failed create task version updates',
            ECODE: ecode,
            team_id,
            task_id,
            OVM_CRITICAL: true,
        });

        return [];
    }

    const versionRequest = {
        object_type: ObjectType.TASK,
        object_id: task_id,
        workspace_id: team_id,
        operation,
        data: {
            context: {
                ws_key,
            },
        },
    };

    return [versionRequest];
}
