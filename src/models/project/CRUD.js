import aws from 'aws-sdk';
import config from 'config';
import fs from 'fs';
import randomstring from 'randomstring';
import _ from 'lodash';
import uuid from 'node-uuid';
import { generateNewLineOp } from '@clickup/quill';
import { ObjectType, OperationType, ObjectRelationshipType } from '@time-loop/ovm-object-version';
import { getRoleKey, getLimitedMemberRoleSubtypes } from '@clickup/utils/authorization';
import { HierarchyPermissionLevel, UserRoleSubType } from '@clickup/utils/authorization/models';
import { updateCanonicalChatViews } from '@clickup-legacy/models/views/services/updateViewService';
import { createNewProjectId } from '@clickup/data-platform/sequences';
import {
    isLimitedMemberEnabledAsync,
    isLimitedMemberEnabledCb,
} from '@clickup-legacy/models/team/helpers/roleValidationHelper';
import * as cacheInvalidation from '@clickup-legacy/libs/cache/invalidateIdsAndCounts';
import { updateRequestForWorkspaceMembers } from '@clickup-legacy/libs/user/factories/ovm-update-requests';
import { getEgressAccessInfoService } from '@clickup-legacy/utils/access/services/authorization/instances';
import { getUserFromDecodedToken } from '@clickup-legacy/utils/auth/services/userIdProvider';
import { getLogger } from '@clickup/shared/utils-logging';
import * as async from '../../utils/asyncHelper';
import * as access2 from '../../utils/access2';
import * as sqs from '../../utils/v1-ws-message-sending';

import {
    hierarchyObjectVersionUpdateRequests,
    hierarchyTreeVersionUpdateRequests,
    userHierarchyVersionUpdateRequests,
} from '../hierarchy/factories/ovmObjectVersionFactory';

import * as categoryMod from '../category/CRUD';
import * as catTemplate from '../templates/categoryTemplate';
import * as checklistTemplate from '../templates/checklistTemplate';
import * as cf_sign from '../../utils/cf_sign';
import * as colorUtil from '../../utils/colorUtils';
import * as customTaskIds from '../task/customTaskIds';
import * as db from '../../utils/db';
import * as db2 from '../../utils/db2';
import * as delayed_emails from '../email/delayed_emails';
import * as email_helper from '../email/email';
import * as gantt from '../gantt';
import * as input from '../../utils/input_validation';
import * as listViewSettings from '../listViewSettings';
import * as deleteViewService from '../views/services/deleteViewService';
import * as privacy from '../privacy';
import * as statusIDCorrection from '../task/statusIDCorrection';
import * as thumb from '../../utils/thumbnail';
import * as userInitials from '../../utils/userInitials';
import * as viewMod from '../views/views';
import * as viewSchema from '../views/schema';
import * as webhook from '../../utils/webhook';
import { getUsersAsync, formatUser } from '../user/userProvider';
import * as projectPosition from './projectPosition';
import * as awsUtil from '../../utils/awsUtils/awsUtil';
import * as teamHelper from '../team/helpers';
import * as genericCopy from '../genericCopy';
import * as automationHelpers from '../../automation/helpers';
import * as projectMember from './projectMember';
import * as templateHelpers from '../templates/templateHelpers';
import * as groupMod from '../groups';
import * as orderindexMigration from '../ordering/task/migrate';
import * as groupSharingPaywall from '../group_sharing/paywall';
import * as recent from '../profile/recent';
import { getSubcategoryStatusesAsync } from '../subcategory/subcatStatuses';
import { ClickUpError } from '../../utils/errors';
import { ClickUpTracer } from '../../utils/tracer';
import * as helpers from './helpers';
import * as modelHelpers from '../helpers';
import * as environment from '../../utils/environment';
import { getSharedEntityQueries } from '../shared_entity_util/util/sharedEntityQueryBuilder';
import { EntitlementName, entitlementService } from '../entitlements/entitlementService';
import { getCategoryStatusIdAsync } from '../shared_entity_util/datastores/sharedEntityDatastore';
import { getTeamId } from '../../utils/entities/services/entitiesService';
import {
    reportTasksInProjectChanged,
    reportProjectChanged,
    reportAssetViewed,
    shouldReportAssetViewed,
} from '../elastic/services/change-detector/hooks';
import { queryTeamSpacesCount } from './datastores/spaceDataStore';
import { TransactionClientImpl } from '../../utils/transaction/TransactionClientImpl';
import { getObjectVersionManager } from '../../utils/version/ObjectVersionManagerFactory';
import { batchQueriesSeriesAsyncThenUpdateTasksWithClient } from '../task/helpers/versionUpdateHelper';
import { coeditorClientInstance } from '../../clients/CoeditorClient';
import { EntityType } from '../coeditor/constants';
import { prepareUpdateYdocQueries } from '../../utils/content/contentQueryFactory';
import { ContentEntityType } from '../../utils/content/interfaces';

import {
    shouldUpdateChangeDetectorQueueAfterEditCustomPrefix,
    shouldUpdateTaskVersionsOnCustomPrefixChange,
    shouldUseHierarchyV3CoreClient,
} from '../integrations/split/squadTreatments/hierarchyTreatments';

import { isDefaultLocationPermissionsEnabled } from '../integrations/split/squadTreatments/accessManagementTreatments';
import { checkAccessTeam } from '../../utils/access/teamsAccess';
import { whereNotGuest } from '../../utils/access/factories/where-not-guest.factory';
import * as timeSpentDatastore from '../time/datastores/timeSpentDatastore';
import { getWorkspaceSettings } from '../team/services/settingsService';
import { checkProjectLimit } from '../hierarchy/services/hierarchyLimitService';
import { unParentChildDocs } from '../views/documents/helpers/documentHelpers';
import { verifyProjectUsage } from '../hierarchy/services/hierarchyPaywallService';
import { objectIdsForTasksInSpace } from '../task/factories/ovmFanOutQueryFactory';
import { dashboardsAsAViewInSpaceViewSettings } from '../integrations/split/squadTreatments/dashboardsTreatments';
import { hierarchyCoreClientInstance } from '../../clients/HierarchyCoreClient';
import { getUserRole, getUsersInfo } from '../user/datastores/CRUD/getUsers';
import { TimeTrackingDefaultToBillable } from '../clickapps/types/time-tracking.type';
import { TimeTrackingDefaultToBillableSpaceClickapp } from '../clickapps/definitions/time-tracking-default-to-billable-space.clickapp';
import { SchedulerEnablementSpaceClickapp } from '../clickapps/definitions/scheduler-enablement-space.clickapp';
import clickappsService from '../clickapps/clickapps.service';
import { ParentType } from '../clickapps/types/parent-type.type';

import {
    ProjectType,
    getExcludeHiddenSpacesQueryCondition,
    canSpaceBePrivate,
    isMemberInsertionSkippedForProjectType,
} from './interfaces/ProjectType';

import { getGroupsInfo } from '../groups/datastores/groupsDatastore';
import { StatusErrors } from '../../utils/errors/constants';
import { statusLengthLimit } from '../integrations/split/squadTreatments/fieldTreatments';
import { generateStatusId } from '../statuses/services/statusIdProvider';
import { GlobalTableMigrationBatchId } from '../../utils/pg/interfaces/GlobalDatabaseByBatchConfig';
import { validateAndCoerceProjectType } from './spaceHelpers';
import { assureStatusColor } from '../status/status.utils';
import { upsertObjectAccessInfoQueryObject } from '../../utils/access/datastores/objectAccessInfoDatastore';
import { updateRequestForWorkspace } from '../../libs/workspace/factories/ovm-update-requests';
import {
    isValidDefaultPermissionLevel,
    fetchDefaultPermissionLevel,
} from '../../utils/access/services/defaultPermissionLevel';
import { getPreferredLanguage } from '../../utils/i18n';
import { ParentTypeAbbrev } from '../dashboard/location-overview/helpers';
import { getOrCreateLocationOverviewDashboard } from '../dashboard/location-overview/create';

const logger = getLogger('project');

const { project: entityType } = config.parent_types;

const profile_creds = awsUtil.awsProfile();
if (profile_creds) {
    aws.config.credentials = profile_creds;
}

aws.config.update({
    region: config.aws.region,
});
const s3 = new aws.S3();

const {
    can_read,
    can_create_spaces,
    can_edit_privacy,
    can_create_personal_list,
    can_edit_space_settings,
    archive,
    delete: _delete,
    can_set_default_permission_level,
} = config.permission_constants;
const { parent_types } = config.views;

const ProjectError = ClickUpError.makeNamedError('project');

const tracer = new ClickUpTracer();

function _getTeamMembers(project_ids, _db, cb) {
    const memberQuery = `
    SELECT
        users.id,
        users.profile_picture_key,
        users.username,
        users.email,
        users.color,
        project_members.date_joined,
        project_members.permission_level,
        project_members.project_id,
        project_members.hidden,
        team_members.role,
        team_members.role_subtype
    FROM
        task_mgmt.projects
        JOIN task_mgmt.project_members
            ON projects.id = project_members.project_id
        JOIN task_mgmt.users
            ON users.id = project_members.userid
        JOIN task_mgmt.team_members
            ON projects.team = team_members.team_id
            AND users.id = team_members.userid
        WHERE projects.id = ANY($1);
    `;
    _db.readQuery(memberQuery, [project_ids], cb);
}

async function getOwnerControlPrivateSpacesSetting(team_id) {
    const query = 'SELECT owner_control_private_spaces FROM task_mgmt.teams WHERE id = $1';
    const params = [team_id];
    const result = await db.readQueryAsync(query, params);
    return result?.rows[0]?.owner_control_private_spaces === true;
}

function _membersProject(simple_permissions, members = [], limitedMemberEnabled = false) {
    const members_to_project = {};

    members.forEach(member => {
        if (!member.permission_level || !member.date_joined) {
            return;
        }

        if (!members_to_project[member.project_id]) {
            members_to_project[member.project_id] = [];
        }
        members_to_project[member.project_id].push({
            user: {
                id: member.id,
                email: member.email,
                username: member.username,
                color: member.color,
                initials: userInitials.getInitials(member.email, member.username),
                profilePicture: cf_sign.getCloudfrontAvatarUrl(member.profile_picture_key),
            },
            permissions: simple_permissions ? null : config.permission_levels[member.permission_level],
            permission_level: parseInt(member.permission_level, 10),
            removed: false,
            date_joined: member.date_joined,
            role: member.role,
            role_subtype: limitedMemberEnabled ? member.role_subtype : undefined,
            role_key: (member.role_key = getRoleKey(
                member.role,
                limitedMemberEnabled ? member.role_subtype : undefined
            )),
        });
    });

    return members_to_project;
}

function _projectMemberids(members = []) {
    const project_memberids = {};
    members.forEach(member => {
        if (!member.permission_level || !member.date_joined) {
            return;
        }

        if (!project_memberids[member.project_id]) {
            project_memberids[member.project_id] = [];
        }
        project_memberids[member.project_id].push(member.id);
    });

    return project_memberids;
}

function _getProject(userid, project_id, options, cb) {
    const { include_removed, use_replica } = options;

    access2.checkAccessProject(userid, project_id, { permissions: [can_read] }, (accessErr, permissions) => {
        if (accessErr) {
            if (accessErr.ECODE === `ACCESS_015` && options.is_gantt) {
                cb();
                return;
            }
            cb(new ProjectError(accessErr, 'PROJ_079'));
            return;
        }

        const query = `
            SELECT
                projects.*,
                project_indexes.orderindex as project_orderindex,
                default_categories.default_category,
                 project_preferences.hide_project,
                 users.id as userid, users.email as email,
                 users.username as username, users.profile_picture_key,
                 users.color as user_color,
                 T.using_github,
                 T.using_gitlab,
                 T.using_bitbucket,
                 T.date_created as team_date_created,
                 T.id as team_id, T.name as team_name,
                 coalesce(T.time_estimate_rollup, false) AS time_estimate_rollup,
                 coalesce(T.time_tracking_rollup, false) AS time_tracking_rollup,
                 coalesce(project_members.hidden, false) as hidden,
                 team_members.role,
                 team_members.can_see_time_spent,
                 team_members.can_see_time_estimated,
                 coalesce(team_members.can_see_points_estimated, true) AS can_see_points_estimated
            FROM task_mgmt.projects
            LEFT JOIN task_mgmt.teams T
                ON projects.team = T.id
            LEFT JOIN task_mgmt.team_members
                ON projects.team = team_members.team_id AND team_members.userid = $2
            LEFT OUTER JOIN task_mgmt.project_members
                ON project_members.project_id = projects.id AND project_members.userid = $2
            LEFT OUTER JOIN (
                SELECT *  FROM task_mgmt.project_preferences WHERE userid = $2 AND project_id = $1
            ) as project_preferences
                ON projects.id = project_preferences.project_id
            LEFT OUTER JOIN task_mgmt.default_categories
                ON (default_categories.project_id = $1 AND default_categories.userid = $2)
            LEFT JOIN task_mgmt.project_indexes
                ON projects.id = project_indexes.project_id AND project_indexes.userid = $2,
            task_mgmt.users
            WHERE projects.id = $1
                AND users.id = projects.owner
                AND projects.deleted = false`;
        const params = [project_id, userid];

        const projects_query = use_replica ? db.replicaQuery : db.readQuery;

        projects_query(query, params, (err, result) => {
            if (err) {
                cb(new ProjectError(err, 'PROJ_005'));
                return;
            }

            if (result.rows.length === 0) {
                cb(new ProjectError('Space not found', 'PROJ_006', 404));
                return;
            }

            result.rows[0].team = {
                id: result.rows[0].team_id,
                name: result.rows[0].team_name,
                date_created: result.rows[0].team_date_created,
                using_github: result.rows[0].using_github,
                using_gitlab: result.rows[0].using_gitlab,
                using_bitbucket: result.rows[0].using_bitbucket,
            };

            delete result.rows[0].team_id;
            delete result.rows[0].team_name;
            delete result.rows[0].team_date_created;
            delete result.rows[0].using_github;
            delete result.rows[0].using_gitlab;
            delete result.rows[0].using_bitbucket;
            delete result.rows[0].ydoc;

            result.rows[0] = helpers.formatFeaturesFromRow(result.rows[0]);

            if (result.rows[0].priority != null) {
                result.rows[0].priority = config.priorities.map[result.rows[0].priority];
            }

            result.rows[0].permissions = permissions.permissions;
            result.rows[0].permission_level = parseInt(permissions.permission_level, 10);

            result.rows[0].owner = {
                id: result.rows[0].userid,
                username: result.rows[0].username,
                email: result.rows[0].email,
                color: result.rows[0].user_color,
                initials: userInitials.getInitials(result.rows[0].email, result.rows[0].username),
                profilePicture: cf_sign.getCloudfrontAvatarUrl(result.rows[0].profile_picture_key),
            };

            result.rows[0].avatar = cf_sign.getLongCloudfrontSignedURL(result.rows[0].avatar_key, {
                skip_signed_auth: environment.isProdEU,
            });
            result.rows[0].hide_project = result.rows[0].hide_project || false;
            result.rows[0].orderindex = result.rows[0].project_orderindex;

            delete result.rows[0].userid;
            delete result.rows[0].user_color;
            delete result.rows[0].username;
            delete result.rows[0].email;
            delete result.rows[0].profile_picture_key;
            delete result.rows[0].avatar_key;
            const [project] = result.rows;

            async.parallel(
                {
                    async clickapps(para_cb) {
                        try {
                            const clickapps = await clickappsService.buildResourceClickapps(
                                project_id,
                                ParentType.Project
                            );

                            _.merge(project.features, clickapps);

                            para_cb();
                        } catch (_err) {
                            para_cb(_err);
                        }
                    },

                    statuses(para_cb) {
                        helpers.getStatuses([project_id], { asArray: true }, (statusErr, statusResult) => {
                            if (statusErr) {
                                para_cb(statusErr);
                            } else {
                                para_cb(null, statusResult.statuses[project_id]);
                            }
                        });
                    },

                    members(para_cb) {
                        const memberQuery = `
                            SELECT users.id,
                                users.profile_picture_key,
                                users.username,
                                users.email,
                                users.color,
                                project_members.date_joined,
                                project_members.permission_level,
                                team_members.role,
                                team_members.role_subtype
                            FROM   task_mgmt.project_members,
                                task_mgmt.users,
                                task_mgmt.projects,
                                task_mgmt.team_members
                            WHERE
                                users.id = project_members.userid
                                AND project_members.project_id = $1
                                AND projects.id = $1
                                AND projects.team = team_members.team_id
                                AND team_members.userid = users.id
                                AND (
                                    projects.private IS NOT TRUE OR
                                    project_members.permission_level IS NOT NULL
                                )
                            `;

                        db.readQuery(memberQuery, [project_id], para_cb);
                    },

                    async groupMembers(para_cb) {
                        try {
                            const grp_result = await projectMember._getGroupMembers([project_id]);
                            para_cb(null, grp_result);
                        } catch (e) {
                            para_cb(e);
                        }
                    },

                    assignedMembers(para_cb) {
                        if (!include_removed) {
                            para_cb();
                            return;
                        }

                        const memberQuery = `SELECT DISTINCT users.id, users.email, users.username, users.color, users.profile_picture_key FROM task_mgmt.assignees, task_mgmt.items, task_mgmt.subcategories, task_mgmt.categories, task_mgmt.projects, task_mgmt.users WHERE assignees.task_id = items.id AND items.subcategory = subcategories.id AND subcategories.category = categories.id AND categories.project_id = $1 AND users.id = assignees.userid`;

                        db.readQuery(memberQuery, [project_id], para_cb);
                    },

                    taskCount(para_cb) {
                        if (!options.mobile) {
                            para_cb(null, { rows: [] });
                            return;
                        }
                        db.replicaQuery(
                            'SELECT categories.project_id, count(*) FROM task_mgmt.categories, task_mgmt.subcategories, task_mgmt.items, task_mgmt.statuses WHERE categories.project_id = $1 AND categories.archived = false AND categories.id = subcategories.category AND categories.deleted = false AND categories.template = false AND subcategories.deleted = false AND subcategories.template = false AND subcategories.archived = false AND subcategories.id = items.subcategory AND items.deleted = false AND items.archived = false AND items.template = false AND items.parent IS NULL AND items.status = statuses.status AND statuses.status_group = subcategories.status_group AND statuses.type != ALL($2) GROUP BY categories.project_id',
                            [project_id, config.closed_status_types],
                            para_cb
                        );
                    },

                    async templateInformation(para_cb) {
                        if (!project.template) {
                            para_cb();
                            return;
                        }

                        try {
                            await templateHelpers.getTemplateInformation(
                                [{ id: project.id, permanent_template_id: project.permanent_template_id }],
                                'project'
                            );
                        } catch (error) {
                            // skip
                        }
                    },

                    all_statuses(para_cb) {
                        helpers.getGroupedProjectsStatuses(userid, [project_id], {}, para_cb);
                    },

                    listViewSettings(para_cb) {
                        listViewSettings._getListViewSettings(userid, { project_ids: [project_id] }, para_cb);
                    },

                    views(para_cb) {
                        if (!options.include_views) {
                            para_cb();
                            return;
                        }

                        const view_options = {
                            skipAccess: true,
                            parent_id: project_id,
                            parent_type: config.views.parent_types.project,
                            parent_permissions: permissions.permissions,
                            required_templates: true,
                            group_views: true,
                            include_archived: options.include_archived,
                            skip_hierarchy_members: options.skip_hierarchy_members,
                        };

                        viewMod._getViews(userid, view_options, para_cb);
                    },

                    async view_settings(para_cb) {
                        if (!options.include_views && !options.include_view_settings) {
                            para_cb();
                            return;
                        }

                        try {
                            const settings = await viewMod._getViewParentSettings(
                                Number(project_id),
                                config.get('views.parent_types.project')
                            );
                            para_cb(null, settings);
                        } catch (err2) {
                            para_cb(err2);
                        }
                    },

                    categories(para_cb) {
                        if (!options.is_gantt) {
                            para_cb();
                            return;
                        }

                        categoryMod._getCategories(
                            userid,
                            {
                                project_id: project.id,
                                skip_statuses: !options.include_statuses && !options.is_gantt,
                                simple: true,
                                gantt: options.is_gantt,
                                include_comment_count: options.include_comment_count,
                                use_master: options.use_master,
                                include_points: true,
                                include_pinned_templates: options.include_pinned_templates,
                            },
                            (err2, catResult) => {
                                if (err2) {
                                    para_cb(err2);
                                } else {
                                    project.categories = catResult.categories;
                                    gantt._getGanttPeriodsForProjects([project], options, para_cb);
                                }
                            }
                        );
                    },

                    settings_templates(para_cb) {
                        const template_ids = [];
                        if (project.list_view_template) {
                            template_ids.push(project.list_view_template);
                        }
                        if (project.board_view_template) {
                            template_ids.push(project.board_view_template);
                        }
                        if (project.calendar_view_template) {
                            template_ids.push(project.calendar_view_template);
                        }
                        if (project.gantt_view_template) {
                            template_ids.push(project.gantt_view_template);
                        }
                        if (project.box_view_template) {
                            template_ids.push(project.box_view_template);
                        }

                        if (project.activity_view_template) {
                            template_ids.push(project.activity_view_template);
                        }

                        if (project.mind_map_view_template) {
                            template_ids.push(project.mind_map_view_template);
                        }

                        if (project.timeline_view_template) {
                            template_ids.push(project.timeline_view_template);
                        }

                        if (project.workload_view_template) {
                            template_ids.push(project.workload_view_template);
                        }

                        if (project.table_view_template) {
                            template_ids.push(project.table_view_template);
                        }

                        if (project.map_view_template) {
                            template_ids.push(project.map_view_template);
                        }

                        if (template_ids.length === 0) {
                            para_cb();
                            return;
                        }

                        viewMod._getViews(
                            userid,
                            { skipAccess: true, in_view_ids: template_ids, skip_standard: true },
                            (viewErr, viewResult) => {
                                if (viewErr) {
                                    para_cb(viewErr);
                                } else {
                                    viewResult.views.forEach(view => {
                                        view.standard = false;
                                        if (project.list_view_template === view.id) {
                                            project.list_view_template = view;
                                        } else if (project.board_view_template === view.id) {
                                            project.board_view_template = view;
                                        } else if (project.calendar_view_template === view.id) {
                                            project.calendar_view_template = view;
                                        } else if (project.gantt_view_template === view.id) {
                                            project.gantt_view_template = view;
                                        } else if (project.box_view_template === view.id) {
                                            project.box_view_template = view;
                                        } else if (project.activity_view_template === view.id) {
                                            project.activity_view_template = view;
                                        } else if (project.mind_map_view_template === view.id) {
                                            project.mind_map_view_template = view;
                                        } else if (project.timeline_view_template === view.id) {
                                            project.timeline_view_template = view;
                                        } else if (project.table_view_template === view.id) {
                                            project.table_view_template = view;
                                        } else if (project.workload_view_template === view.id) {
                                            project.workload_view_template = view;
                                        } else if (project.map_view_template === view.id) {
                                            project.map_view_template = view;
                                        }
                                    });

                                    [
                                        'list_view_template',
                                        'board_view_template',
                                        'calendar_view_template',
                                        'gantt_view_template',
                                        'box_view_template',
                                        'activity_view_template',
                                        'mind_map_view_template',
                                        'timeline_view_template',
                                        'table_view_template',
                                        'workload_view_template',
                                        'map_view_template',
                                    ].forEach(template => {
                                        if (typeof project[template] === 'string') {
                                            project[template] = null;
                                        }
                                    });

                                    para_cb();
                                }
                            }
                        );
                    },

                    automationCount(para_cb) {
                        automationHelpers.getAutomationCount([project.id], config.parent_types.project, {}, para_cb);
                    },

                    commentCount(para_cb) {
                        if (!options.include_comment_count) {
                            para_cb(null);
                            return;
                        }

                        db.readQuery(
                            'SELECT count(*), parent FROM task_mgmt.comments WHERE parent = $1 AND type = $2 AND deleted = false GROUP BY parent',
                            [project_id, config.comments.types.project],
                            (commentCountErr, countResult) => {
                                if (commentCountErr) {
                                    para_cb(commentCountErr);
                                } else {
                                    project.comment_count = 0;

                                    if (countResult.rows.length) {
                                        project.comment_count = countResult.rows[0].count;
                                    }

                                    para_cb();
                                }
                            }
                        );
                    },
                    async getAssignee(para_cb) {
                        if (!project.assignee) {
                            para_cb();
                            return;
                        }

                        try {
                            const userMap = await getUsersAsync([project.assignee], project.team.id);
                            project.assignee = userMap[project.assignee];
                            para_cb();
                        } catch (e) {
                            para_cb(e);
                        }
                    },
                    async getStatus(para_cb) {
                        if (!project.status) {
                            para_cb();
                            return;
                        }

                        try {
                            const subcatStatuses = await getSubcategoryStatusesAsync(project.team.id, {});
                            project.status = _.find(subcatStatuses, status => status.status === project.status);
                            para_cb();
                        } catch (e) {
                            para_cb(e);
                        }
                    },
                    async getAssignees(para_cb) {
                        try {
                            const res = await db.promiseReadQuery(
                                'SELECT user_id FROM task_mgmt.project_assignee WHERE project_id = $1',
                                [project_id]
                            );

                            if (res.rows.length === 0) {
                                project.assignees = [];
                                para_cb();
                                return;
                            }

                            const user_ids = res.rows.map(({ user_id }) => user_id);
                            const users = await getUsersInfo(
                                user_ids,
                                ['id', 'email', 'username', 'color', 'profile_picture_key'],
                                true,
                                true
                            );

                            project.assignees = users.rows.map(user => ({
                                id: user.id,
                                color: user.color,
                                profilePicture: cf_sign.getCloudfrontAvatarUrl(user.profile_picture_key),
                                initials: userInitials.getInitials(user.email, user.username),
                                email: user.email,
                                username: user.username,
                            }));

                            para_cb();
                        } catch (e) {
                            para_cb(e);
                        }
                    },
                    async getGroupAssignees(para_cb) {
                        try {
                            const res = await db.promiseReadQuery(
                                'SELECT group_id FROM task_mgmt.project_group_assignee WHERE project_id = $1',
                                [project_id]
                            );

                            if (res.rows.length === 0) {
                                project.group_assignees = [];
                                para_cb();
                                return;
                            }

                            const group_ids = res.rows.map(({ group_id }) => group_id);
                            const groups = await getGroupsInfo(group_ids, ['id', 'color', 'name']);

                            project.group_assignees = groups.rows.map(group => ({
                                id: group.id,
                                color: group.color,
                                name: group.name,
                            }));

                            para_cb();
                        } catch (e) {
                            para_cb(e);
                        }
                    },

                    async defaultPermissionLevel(para_cb) {
                        try {
                            project.default_permission_level = await fetchDefaultPermissionLevel({
                                workspaceId: project?.team?.id,
                                space: { id: project_id, private: project.private },
                            });

                            para_cb();
                        } catch (error) {
                            logger.error({
                                msg: 'Failed to get the default permission level',
                                ECODE: 'PROJ_053',
                                err: error,
                                project_id,
                            });
                            para_cb(error);
                        }
                    },
                },
                (paraErr, result2) => {
                    if (paraErr) {
                        cb(new ProjectError(paraErr, 'PROJ_020'));
                        return;
                    }

                    project.members = [];

                    const userids = [];

                    for (const row of result2?.members?.rows ?? []) {
                        row.user = {
                            id: row.id,
                            username: row.username,
                            email: row.email,
                            color: row.color,
                            initials: userInitials.getInitials(row.email, row.username),
                            profilePicture: cf_sign.getCloudfrontAvatarUrl(row.profile_picture_key),
                        };

                        if (row.permission_level) {
                            row.permissions = config.permission_levels[row.permission_level.toString()];
                        }
                        row.removed = false;

                        const userId = row.id;

                        row.role_subtype = row.role_subtype ?? UserRoleSubType.NONE;

                        delete row.id;
                        delete row.username;
                        delete row.email;
                        delete row.color;
                        delete row.profile_picture_key;

                        project.members.push(row);
                        userids.push(userId);
                    }

                    if (include_removed) {
                        result2?.assignedMembers?.rows.forEach(row => {
                            if (userids.indexOf(row.id) < 0) {
                                result.rows[0].members.push({
                                    user: {
                                        id: row.id,
                                        username: row.username,
                                        email: row.email,
                                        color: row.color,
                                        initials: userInitials.getInitials(row.email, row.username),
                                        profilePicture: cf_sign.getCloudfrontAvatarUrl(row.profile_picture_key),
                                    },
                                    removed: true,
                                });
                            }
                        });
                    }

                    project.group_members = result2?.groupMembers?.[project_id] || [];

                    let taskcount = '0';

                    if (result2.taskCount.rows && result2.taskCount.rows[0]) {
                        taskcount = result2.taskCount.rows[0].count;
                    }

                    project.all_statuses = result2.all_statuses[project_id];
                    project.taskcount = taskcount;
                    project.statuses = result2.statuses;
                    project.listViewSettings = result2.listViewSettings[project_id];
                    project.automation_count = result2.automationCount.counts[project_id] || 0;
                    project.editor_token = `project:${project.id}:${uuid.v4()}:${uuid.v4()}`;

                    if (options.include_views) {
                        project.views = result2.views.views;
                        project.standard_views = result2.views.standard_views;
                        project.default_view = result2.views.default_view;
                        project.last_view = result2.views.last_view;
                        project.required_templates = result2.views.required_templates || {};
                        project.view_groups = result2.views.groups;
                    }

                    if (options.include_views || options.include_view_settings) {
                        project.view_settings = result2.view_settings;
                    }

                    if (
                        !dashboardsAsAViewInSpaceViewSettings(project.team.id) &&
                        project.preset_views?.includes(config.views.view_types.dashboard)
                    ) {
                        project.preset_views = project.preset_views.filter(
                            v => v !== config.views.view_types.dashboard
                        );
                    }

                    cb(null, project);
                }
            );
        });
    });
}
export { _getProject };

export function _promiseGetProject(userid, project_id, options) {
    return new Promise((resolve, reject) => {
        try {
            _getProject(userid, project_id, options, (err, result) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(result);
                }
            });
        } catch (e) {
            reject(e);
        }
    });
}

function isNumeric(number) {
    return !Number.isNaN(parseFloat(number)) && !Number.isNaN(+number);
}

export function getProject(req, resp, next) {
    const userid = req.decoded_token.user;
    const { project_id } = req.params;
    let include_removed = req.query.include_removed || false;
    let include_comment_count = req.query.include_comment_count || false;
    const include_views = req.query.include_views === 'true';
    const skip_hierarchy_members = req.query.skip_hierarchy_members === 'true';
    const is_gantt = req.query.gantt === 'true';
    const include_pinned_templates = req.query.include_pinned_templates === 'true';
    const include_view_settings = modelHelpers.toBoolean(req.query.include_view_settings);
    let mobile = false;

    if (!project_id || !isNumeric(project_id)) {
        resp.status(400).send({
            err: 'Provide a valid projectID',
            ECODE: 'PROJ_099',
        });
        return;
    }
    if (shouldReportAssetViewed(req.header('Referer'), req.header('User-Agent'))) {
        reportAssetViewed(userid, project_id, 'project');
    }

    let user_agent = req.headers['user-agent'];
    if (user_agent) {
        user_agent = user_agent.toLowerCase();
        if (user_agent.indexOf('ios') >= 0 || user_agent.indexOf('android') >= 0) {
            mobile = true;
        }
    }

    if (include_removed === 'false') {
        include_removed = false;
    }

    if (include_comment_count === 'false') {
        include_comment_count = false;
    }

    _getProject(
        userid,
        project_id,
        {
            include_removed,
            include_views,
            skip_hierarchy_members,
            is_gantt,
            mobile,
            include_comment_count,
            include_pinned_templates,
            use_replica: false,
            include_view_settings,
        },
        async (err, result) => {
            if (err) {
                next(err);
            } else {
                const workspaceId = req.headers['x-workspace-id'] || result.team?.id;
                const shouldCallV3 = shouldUseHierarchyV3CoreClient() && workspaceId && !result.deleted;
                if (shouldCallV3) {
                    try {
                        const v3Response = await hierarchyCoreClientInstance.getSpace(
                            workspaceId,
                            project_id,
                            req.headers
                        );

                        mergeHierarchyV3SpaceBulkData(
                            result,
                            v3Response.value,
                            v3Response.version
                                ? {
                                      _version_vector: { ...v3Response.version },
                                  }
                                : null
                        );
                    } catch (err2) {
                        logger.error({
                            ECODE: 'HIERARCHY_004',
                            err: err2,
                            msg: 'Failed to get response from hierarchy core v3 client',
                        });

                        if (
                            err2?.code?.toUpperCase() === 'ECONNABORTED' ||
                            err2?.code?.toUpperCase() === 'ECONNREFUSED' ||
                            err2?.code?.toUpperCase() === 'EHOSTUNREACH'
                        ) {
                            resp.status(200).send(result);
                        } else {
                            next(err2);
                        }

                        return;
                    }
                }

                resp.status(200).send(result);
            }
        }
    );
}

const DEFAULT_PROJECT_FEATURES = {
    due_dates: {
        enabled: false,
        start_date: false,
        remap_due_dates: false,
        remap_closed_due_date: false,
    },

    time_tracking: {
        enabled: false,
        harvest: false,
    },

    custom_items: {
        enabled: false,
    },

    milestones: {
        enabled: false,
    },

    wip_limits: {
        enabled: false,
    },

    tags: {
        enabled: false,
    },

    time_estimates: {
        enabled: false,
    },

    checklists: {
        enabled: true,
    },

    custom_fields: {
        enabled: false,
    },

    remap_dependencies: {
        enabled: true,
    },

    dependency_warning: {
        enabled: true,
    },

    portfolios: {
        enabled: false,
    },

    sprints: {
        enabled: false,
    },

    emails: {
        enabled: true,
    },

    status_pies: {
        enabled: true,
    },
};
export { DEFAULT_PROJECT_FEATURES };

function validateProjectStatuses(statuses) {
    const status_texts = [];
    const status_types = [];

    const validateStatus = statusLengthLimit().validate?.project;

    let invalidStatus;
    statuses.forEach(status => {
        if (validateStatus && !input.validateStatus(status.status)) {
            invalidStatus = status;
        }

        if (status.status === 'Open' && !status.type) {
            status.type = 'open';
        }
        if (status.status === 'Closed' && !status.type) {
            status.type = 'closed';
        }
        status_texts.push(status.status);
        status_types.push(status.type);
    });

    if (invalidStatus) {
        return new ProjectError('Status is invalid', StatusErrors.StatusIsInvalid, 400, { invalidStatus }).logError();
    }
    if (status_types.filter(type => type === 'open').length !== 1) {
        return new ProjectError('Open is a required status type and there can only be one', 'PROJ_009', 400);
    }
    if (status_types.filter(type => type === 'closed').length !== 1) {
        return new ProjectError('Closed is a required status type and there can only be one', 'PROJ_010', 400);
    }
    return null;
}

function _createProject(userid, options, cb) {
    let ownerInAdd = false;
    let created_from_template = false;
    let client;
    let done;
    const useridsToAdd = [];
    const emailsToAdd = [];
    let userRows = [];
    const email_invite_userids = [];
    const email_invites = [];

    const versionUpdates = [];
    const ovm = getObjectVersionManager();
    let events;

    const admin_can_manage = modelHelpers.toBoolean(options.admin_can_manage);
    options.admin_can_manage = admin_can_manage;

    if (!input.validateProjectName(options.name)) {
        cb(new ProjectError('Space name invalid', 'PROJ_060', 400));
        return;
    }

    if (!options.features) {
        options.features = DEFAULT_PROJECT_FEATURES;
    }

    if (!options.features.emails) {
        options.features.emails = {
            enabled: true,
        };
    }

    if (!options.features.portfolios) {
        options.features.portfolios = {
            enabled: false,
        };
    }

    if (!options.features.milestones) {
        options.features.milestones = {
            enabled: false,
        };
    }

    if (!options.features.custom_items) {
        options.features.custom_items = {
            enabled: false,
        };
    }

    if (!options.features.wip_limits) {
        options.features.wip_limits = {
            enabled: false,
        };
    }

    if (!options.features.due_dates) {
        options.features.due_dates = {
            enabled: false,
            start_date: false,
            remap_due_dates: false,
            remap_closed_due_date: false,
        };
    }

    if (!options.features.time_tracking) {
        options.features.time_tracking = {
            enabled: false,
            harvest: false,
        };
    }

    if (!options.features.points) {
        options.features.points = {
            enabled: false,
            scale: [],
        };
    }

    if (!options.features.priorities) {
        options.features.priorities = {
            enabled: false,
        };
    }

    if (!options.features.tags) {
        options.features.tags = {
            enabled: false,
        };
    }

    if (!options.features.time_estimates) {
        options.features.time_estimates = {
            enabled: false,
        };
    }

    if (!options.features.checklists) {
        options.features.checklists = {
            enabled: false,
        };
    }

    if (!options.features.check_unresolved) {
        options.features.check_unresolved = {
            enabled: false,
        };
    }

    if (!options.features.custom_fields) {
        options.features.custom_fields = {
            enabled: false,
        };
    }

    if (!options.features.remap_dependencies) {
        options.features.remap_dependencies = {
            enabled: true,
        };
    }

    if (!options.features.dependency_warning) {
        options.features.dependency_warning = {
            enabled: true,
        };
    }

    if (!options.features.sprints) {
        options.features.sprints = {
            enabled: false,
        };
    }

    if (!options.features.status_pies) {
        options.features.status_pies = {
            enabled: false,
        };
    }

    if (!options.usersToAdd) {
        options.usersToAdd = [];
    }

    if (!options.statuses) {
        options.statuses = [
            { color: config.default_open_status.color, orderindex: 0, type: 'open', status: 'to do' },
            { color: config.default_closed_status.color, orderindex: 1, type: 'closed', status: 'complete' },
        ];
    }

    options.usersToAdd.forEach(userToAdd => {
        userToAdd.role = parseInt(userToAdd.role, 10);

        if (userToAdd.userid) {
            userToAdd.id = userToAdd.userid;
        }
        if (userToAdd.id === userid) {
            ownerInAdd = true;
        }
        if (userToAdd.id) {
            useridsToAdd.push(userToAdd.id);
        }
        if (userToAdd.email) {
            emailsToAdd.push(userToAdd.email);
        }
        if (userToAdd.role < 1 || userToAdd.role > 3) {
            userToAdd.role = 3;
        }
    });

    if (!ownerInAdd) {
        useridsToAdd.push(userid);
    }

    if (options.project_prefix) {
        options.project_prefix = options.project_prefix.toUpperCase();
    }

    if (options.preset_views == null || options.preset_views.length === 0) {
        options.preset_views = [config.views.view_types.list, config.views.view_types.board];
    }

    if (
        !dashboardsAsAViewInSpaceViewSettings(options.team) &&
        options.preset_views?.includes(config.views.view_types.dashboard)
    ) {
        options.preset_views = options.preset_views.filter(v => v !== config.views.view_types.dashboard);
    }

    if (options.project_type) {
        try {
            if (options.personal_list) {
                options.project_type = ProjectType.personalList;
            }
            options.project_type = validateAndCoerceProjectType(options.project_type);
        } catch (err) {
            cb(err);
            return;
        }
    }

    const statusErr = validateProjectStatuses(options.statuses);
    if (statusErr) {
        cb(statusErr);
        return;
    }

    recent.invalidateCachedRecentTasks(userid, options.team, 'task_mgmt.recently_created');
    timeSpentDatastore.invalidateCachedTimeTrackedTasks(options.team);

    const skipAccessOrClickBot = options.skip_access || userid === config.clickbot_assignee;

    async.parallel(
        {
            access(para_cb) {
                if (skipAccessOrClickBot) {
                    para_cb();
                    return;
                }

                const permission = options.personal_list ? can_create_personal_list : can_create_spaces;
                access2.checkAccessTeam(userid, options.team, { permissions: [permission] }, para_cb);
            },

            dupe_name(para_cb) {
                if (options.validate_name === false) {
                    para_cb();
                    return;
                }

                db.readQuery(
                    'SELECT id FROM task_mgmt.projects WHERE name = $1 AND team = $2 AND deleted = false AND archived = false AND template = false',
                    [options.name, options.team],
                    (err, result) => {
                        if (err) {
                            para_cb(new ProjectError(err, 'PROJ_022'));
                        } else if (result.rows.length > 0) {
                            para_cb(new ProjectError('Space with this name already exists', 'PROJECT_023', 400));
                        } else {
                            para_cb();
                        }
                    }
                );
            },

            async checkUsersInTeam(para_cb) {
                if (options.skip_access) {
                    userRows = [{ userid }];
                    para_cb();
                    return;
                }

                if (userid === config.clickbot_assignee) {
                    userRows = [{ userid: options.creator }];
                    para_cb();
                    return;
                }

                try {
                    const limitedMemberEnabled = await isLimitedMemberEnabledAsync(options.team);

                    db.readQuery(
                        `
                            SELECT userid
                            FROM task_mgmt.team_members, task_mgmt.users
                            WHERE
                                (userid = ANY($1) OR users.email = ANY($2))
                                AND team_id = $3
                                AND users.id = team_members.userid
                                AND users.deleted = false
                                AND (
                                    role != $4
                                    ${
                                        limitedMemberEnabled
                                            ? `OR role_subtype IN (${getLimitedMemberRoleSubtypes().join(', ')})`
                                            : ''
                                    }
                                )
                        `,
                        [useridsToAdd, emailsToAdd, options.team, config.team_roles.guest],
                        (err, result) => {
                            if (err) {
                                para_cb(new ProjectError(err, 'PROJ_042'));
                            } else {
                                userRows = result.rows;
                                para_cb();
                            }
                        }
                    );
                } catch (err) {
                    para_cb(new ProjectError(err, 'PROJ_043'));
                }
            },

            invitesNotSent(para_cb) {
                if (skipAccessOrClickBot) {
                    para_cb();
                    return;
                }
                db.readQuery(
                    'SELECT team_members.userid, users.email, team_members.invite_code, team_members.team_id, team_members.date_invited, teams.avatar_key, teams.name as team_name, team_members.invited_by, invited_by.username as invited_by_username, invited_by.email as invited_by_email, invited_by.color, invited_by.profile_picture_key FROM task_mgmt.team_members, task_mgmt.teams, task_mgmt.users, task_mgmt.users as invited_by WHERE team_id = $1 AND invite_sent = false AND teams.id = $1 AND users.id = team_members.userid AND invited_by.id = team_members.invited_by',
                    [options.team],
                    (err, result) => {
                        if (err) {
                            para_cb(new ProjectError(err, 'PROJ_062'));
                        } else {
                            result.rows.forEach(row => {
                                email_invite_userids.push(row.userid);
                                email_invites.push(row);
                            });
                            para_cb();
                        }
                    }
                );
            },
            async checkPlanLimits(para_cb) {
                if (options.ignore_paywall && options.create_team) {
                    para_cb();
                    return;
                }

                try {
                    await verifyProjectUsage(null, { ...options }, options.team);
                    para_cb();
                } catch (planErr) {
                    para_cb(planErr);
                }
            },

            async maxProjects(para_cb) {
                if (options.ignore_paywall) {
                    para_cb();
                    return;
                }

                try {
                    await checkProjectLimit(null, options.team, 1, options.project_type);
                    para_cb();
                } catch (e) {
                    para_cb(e);
                }
            },

            templatesAccess(para_cb) {
                if (skipAccessOrClickBot) {
                    para_cb();
                    return;
                }

                const templates_to_check = [
                    options.list_view_template,
                    options.board_view_template,
                    options.calendar_view_template,
                    options.gantt_view_template,
                    options.box_view_template,
                    options.activity_view_template,
                    options.mind_map_view_template,
                    options.timeline_view_template,
                    options.table_view_template,
                    options.workload_view_template,
                    options.map_view_template,
                ];

                async.each(
                    templates_to_check,
                    (template_id, each_cb) => {
                        if (!template_id) {
                            each_cb();
                            return;
                        }

                        created_from_template = true;
                        access2.checkAccessView(userid, template_id, {}, each_cb);
                    },
                    para_cb
                );
            },

            async checkGroupAccess(para_cb) {
                if (!options.add_groups || !options.add_groups.length) {
                    para_cb();
                    return;
                }

                try {
                    const query = `SELECT * FROM task_mgmt.groups WHERE groups.id = ANY($1) AND groups.team_id = $2`;
                    const grp_result = await db.promiseReplicaQuery(query, [options.add_groups, options.team]);

                    if (
                        !grp_result.rows ||
                        !grp_result.rows.length ||
                        grp_result.rows.length !== options.add_groups.length
                    ) {
                        para_cb(new ProjectError('Group doesnt have access', 'PROJ_595'));
                        return;
                    }

                    para_cb();
                } catch (e) {
                    para_cb(new ProjectError(e, 'PROJ_596'));
                }
            },

            async workspaceSettings(para_cb) {
                try {
                    const { defaultNewSpacesToPrivate } = await getWorkspaceSettings(options.team, {
                        only: { defaultNewSpacesToPrivate: 1 },
                    });
                    options.private =
                        (defaultNewSpacesToPrivate === true || options.private === true) &&
                        canSpaceBePrivate(options.project_type);
                    para_cb();
                } catch (settingsErr) {
                    logger.error({
                        msg: 'Failed to select workspace settings in get team',
                        err: settingsErr,
                        ECODE: 'TEAM_205',
                    });
                    para_cb(settingsErr);
                }
            },
            async checkOwnerControlPrivateSpacesSetting(para_cb) {
                if (options.admin_can_manage) {
                    para_cb();
                    return;
                }
                try {
                    const ownerControlPrivateSpaces = await getOwnerControlPrivateSpacesSetting(options.team);
                    if (ownerControlPrivateSpaces) {
                        options.admin_can_manage = true;
                    }
                    para_cb();
                } catch (settingsErr) {
                    para_cb(settingsErr);
                }
            },
        },
        (err, para_result) => {
            if (err) {
                if (done) {
                    done();
                }

                cb(new ProjectError(err, 'PROJ_080'));
                return;
            }

            if (options.list_view_settings) {
                try {
                    options.list_view_settings.type = config.views.view_types.list_required;
                    viewSchema.validateView(options.list_view_settings);
                } catch (e) {
                    cb(new ProjectError(e, 'PROJ_126'));
                    return;
                }
            }
            if (options.board_view_settings) {
                try {
                    options.board_view_settings.type = config.views.view_types.board_required;
                    viewSchema.validateView(options.board_view_settings);
                } catch (e) {
                    cb(new ProjectError(e, 'PROJ_127'));
                    return;
                }
            }
            if (options.calendar_view_settings) {
                try {
                    options.calendar_view_settings.type = config.views.view_types.calendar_required;
                    viewSchema.validateView(options.calendar_view_settings);
                } catch (e) {
                    cb(new ProjectError(e, 'PROJ_128'));
                    return;
                }
            }
            if (options.gantt_view_settings) {
                try {
                    options.gantt_view_settings.type = config.views.view_types.gantt_required;
                    viewSchema.validateView(options.gantt_view_settings);
                } catch (e) {
                    cb(new ProjectError(e, 'PROJ_129'));
                    return;
                }
            }

            if (options.box_view_settings) {
                try {
                    options.box_view_settings.type = config.views.view_types.box_required;
                    viewSchema.validateView(options.box_view_settings);
                } catch (e) {
                    cb(new ProjectError(e, 'PROJ_134'));
                    return;
                }
            }

            if (options.activity_view_settings) {
                try {
                    options.activity_view_settings.type = config.views.view_types.activity_required;
                    viewSchema.validateView(options.activity_view_settings);
                } catch (e) {
                    cb(new ProjectError(e, 'PROJ_156'));
                    return;
                }
            }

            if (options.mind_map_view_settings) {
                try {
                    options.mind_map_view_settings.type = config.views.view_types.mind_map_required;
                    viewSchema.validateView(options.mind_map_view_settings);
                } catch (e) {
                    cb(new ProjectError(e, 'PROJ_159'));
                    return;
                }
            }

            if (options.timeline_view_settings) {
                try {
                    options.timeline_view_settings.type = config.views.view_types.timeline_required;
                    viewSchema.validateView(options.timeline_view_settings);
                } catch (e) {
                    cb(new ProjectError(e, 'PROJ_201'));
                    return;
                }
            }

            if (options.workload_view_settings) {
                try {
                    options.workload_view_settings.type = config.views.view_types.workload_required;
                    viewSchema.validateView(options.workload_view_settings);
                } catch (e) {
                    cb(new ProjectError(e, 'PROJ_204'));
                    return;
                }
            }

            if (options.table_view_settings) {
                try {
                    options.table_view_settings.type = config.views.view_types.table_required;
                    viewSchema.validateView(options.table_view_settings);
                } catch (e) {
                    cb(new ProjectError(e, 'PROJ_203'));
                    return;
                }
            }

            async.series(
                [
                    series_cb => {
                        if (options.client) {
                            client = options.client;
                            done = options.done;
                            series_cb();
                        } else {
                            db.getConn(series_cb, { label: 'create project' }, (connErr, _client, _done) => {
                                client = _client;
                                done = _done;
                                series_cb();
                            });
                        }
                    },
                    series_cb => {
                        if (options.client) {
                            series_cb();
                            return;
                        }

                        client.query('BEGIN', beginErr => {
                            if (beginErr) {
                                series_cb(new ProjectError(beginErr, 'PROJ_001'));
                                db.rollback(client, done);
                                return;
                            }

                            series_cb();
                        });
                    },
                ],
                seriesErr => {
                    if (seriesErr) {
                        db.rollback(client, done);
                        cb(new ProjectError(seriesErr, 'PROJ_081'));
                        return;
                    }

                    createNewProjectId({ team_id: options.team }, (seqErr, new_id) => {
                        if (seqErr) {
                            db.rollback(client, done);
                            cb(seqErr);
                            return;
                        }

                        const ovmChange = {
                            after: created_from_template,
                            field: 'created_from_template',
                        };

                        const privacySetting = {
                            field: 'privacy',
                            before: null,
                            after: options.private,
                        };

                        const objectChanges = [ovmChange, privacySetting];

                        if (options.project_type != null && options.project_type !== ProjectType.default) {
                            objectChanges.push({
                                field: 'project_type',
                                after: options.project_type,
                            });
                        }

                        versionUpdates.push(
                            hierarchyObjectVersionUpdateRequests({
                                objectType: ObjectType.SPACE,
                                objectId: new_id,
                                workspaceId: options.team,
                                operation: OperationType.CREATE,
                                fanout: false,
                                ws_key: options.ws_key,
                                changes: objectChanges,
                            })
                        );
                        versionUpdates.push(
                            hierarchyTreeVersionUpdateRequests({
                                objectType: ObjectType.WORKSPACE,
                                objectId: options.team,
                                workspaceId: options.team,
                                operation: OperationType.CREATE,
                                ws_key: options.ws_key,
                            })
                        );

                        async.series(
                            [
                                series_cb => {
                                    series_cb();
                                },
                                series_cb => {
                                    series_cb();
                                },
                            ],
                            seriesErrX => {
                                if (seriesErrX) {
                                    db.rollback(client, done);
                                    cb(new ProjectError(seriesErrX, 'PROJ_081'));
                                    return;
                                }

                                let query = `
                                    UPDATE task_mgmt.projects
                                    SET
                                        owner = $1,
                                        creator = $1,
                                        name = $3,
                                        date_created = $4,
                                        private = $5,
                                        multiple_assignees = $6,
                                        slack_channel = $7,
                                        import_id = $8,
                                        import_uuid = $9,
                                        importing = $10,
                                        due_dates = $11,
                                        time_tracking = $12,
                                        points = $13,
                                        points_scale = $14,
                                        orderindex = coalesce((SELECT count(*) FROM task_mgmt.projects WHERE team = $2), 0),
                                        priorities = $15,
                                        due_dates_start_date = $16,
                                        harvest = $17,
                                        tags = $18,
                                        template = false,
                                        time_estimates = $19,
                                        deleted = false,
                                        checklists = $20,
                                        content = $21,
                                        color = $22,
                                        due_date = $23,
                                        due_date_time = $24,
                                        check_unresolved = $25,
                                        check_unresolved_subtasks = $26,
                                        check_unresolved_checklists = $27,
                                        check_unresolved_comments = $28,
                                        remap_due_dates = $29,
                                        custom_fields = $30,
                                        remap_dependencies = $31,
                                        dependency_warning = $32,
                                        storage_used = 0,
                                        portfolios = $33,
                                        default_preset_view = $34,
                                        preset_views = $35,
                                        remap_closed_due_date = $36,
                                        list_view_settings = $37,
                                        board_view_settings = $38,
                                        calendar_view_settings = $39,
                                        gantt_view_settings = $40,
                                        avatar_source = $41,
                                        avatar_value = $42,
                                        list_view_template = $43,
                                        board_view_template = $44,
                                        calendar_view_template = $45,
                                        gantt_view_template = $46,
                                        list_view_update_views = $48,
                                        board_view_update_views = $49,
                                        calendar_view_update_views = $50,
                                        gantt_view_update_views = $51,
                                        box_view_update_views = $52,
                                        box_view_template = $53,
                                        box_view_settings = $54,
                                        time_tracking_rollup = $55,
                                        time_estimate_rollup = $56,
                                        milestones = $57,
                                        activity_view_update_views = $58,
                                        activity_view_template = $59,
                                        activity_view_settings = $60,
                                        mind_map_view_template = $61,
                                        mind_map_view_update_views = $62,
                                        mind_map_view_settings = $63,
                                        timeline_view_settings = $64,
                                        timeline_view_update_views = $65,
                                        timeline_view_template = $66,
                                        table_view_template = $67,
                                        table_view_update_views = $68,
                                        table_view_settings = $69,
                                        workload_view_template = $70,
                                        workload_view_update_views = $71,
                                        workload_view_settings = $72,
                                        sprints = $73,
                                        emails_clickapp = $74,
                                        project_prefix = $75,
                                        custom_task_ids_start_100 = $76,
                                        custom_task_ids_start = $77,
                                        custom_task_ids_display = $78,
                                        wip_limits = $79,
                                        map_view_template = $80,
                                        map_view_update_views = $81,
                                        date_updated = $82,
                                        custom_items = $83,
                                        admin_can_manage = $84,
                                        archived = $85,
                                        priority = $86,
                                        assignee = $87,
                                        start_date = $88,
                                        start_date_time = $89,
                                        personal_list = $90,
                                        status_pies = $91,
                                        project_type = $92
                                    WHERE id = $47
                                    RETURNING id`;
                                const params = [
                                    userid > 0 ? userid : options.creator,
                                    options.team,
                                    options.name,
                                    new Date().getTime(),
                                    options.private || false,
                                    options.multiple_assignees,
                                    options.slack_channel,
                                    options.import_id,
                                    options.import_uuid,
                                    options.importing,
                                    options.features.due_dates.enabled,
                                    options.features.time_tracking.enabled,
                                    options.features.points.enabled,
                                    options.features.points.scale,
                                    options.features.priorities.enabled,
                                    options.features.due_dates.start_date,
                                    options.features.time_tracking.harvest,
                                    options.features.tags.enabled,
                                    options.features.time_estimates.enabled,
                                    options.features.checklists.enabled,
                                    options.content || generateNewLineOp(),
                                    options.color,
                                    options.due_date,
                                    options.due_date_time || false,
                                    options.features.check_unresolved.enabled,
                                    options.features.check_unresolved.subtasks,
                                    options.features.check_unresolved.checklists,
                                    options.features.check_unresolved.comments,
                                    options.features.due_dates.remap_due_dates,
                                    options.features.custom_fields.enabled,
                                    options.features.remap_dependencies.enabled,
                                    options.features.dependency_warning.enabled,
                                    options.features.portfolios.enabled,
                                    options.default_preset_view,
                                    options.preset_views,
                                    options.features.due_dates.remap_closed_due_date,
                                    options.list_view_settings,
                                    options.board_view_settings,
                                    options.calendar_view_settings,
                                    options.gantt_view_settings,
                                    options.avatar_source,
                                    options.avatar_value,
                                    options.list_view_template,
                                    options.board_view_template,
                                    options.calendar_view_template,
                                    options.gantt_view_template,
                                    new_id,
                                    options.list_view_update_views,
                                    options.board_view_update_views,
                                    options.calendar_view_update_views,
                                    options.gantt_view_update_views,
                                    options.box_view_updat_views,
                                    options.box_view_template,
                                    options.box_view_settings,
                                    options.features.time_tracking.rollup,
                                    options.features.time_estimates.rollup,
                                    options.features.milestones.enabled,
                                    options.activity_view_update_views,
                                    options.activity_view_template,
                                    options.activity_view_settings,
                                    options.mind_map_view_template,
                                    options.mind_map_view_update_views,
                                    options.mind_map_view_settings,
                                    options.timeline_view_settings,
                                    options.timeline_view_update_views,
                                    options.timeline_view_template,
                                    options.table_view_template,
                                    options.table_view_update_views,
                                    options.table_view_settings,
                                    options.workload_view_template,
                                    options.workload_view_update_views,
                                    options.workload_view_settings,
                                    options.features.sprints.enabled,
                                    options.features.emails.enabled,
                                    options.project_prefix,
                                    options.custom_task_ids_start_100,
                                    options.custom_task_ids_start,
                                    options.custom_task_ids_display,
                                    options.features.wip_limits.enabled,
                                    options.map_view_template,
                                    options.map_view_update_views,
                                    Date.now(),
                                    options.features.custom_items.enabled,
                                    options.admin_can_manage,
                                    Boolean(options.archived),
                                    options.priority,
                                    options.assignee,
                                    options.start_date,
                                    options.start_date_time || false,
                                    options.personal_list ?? false,
                                    options.features.status_pies.enabled,
                                    options.project_type,
                                ];

                                client.query(query, params, (createErr, createResult) => {
                                    if (createErr) {
                                        cb(new ProjectError(createErr, 'PROJ_002'));
                                        db.rollback(client, done);
                                    } else {
                                        const project_id = createResult.rows[0].id;
                                        let avatar_key;
                                        const userAcl = {};
                                        const groupAcl = {};

                                        if (options.avatar) {
                                            let extension = options.avatar.originalname.split('.');
                                            extension = extension[extension.length - 1].toLowerCase();
                                            avatar_key = `project_avatars/${project_id}.${extension}`;
                                        }

                                        async.parallel(
                                            {
                                                avatarUpdate(para_cb) {
                                                    if (options.avatar && avatar_key) {
                                                        const query2 = `UPDATE task_mgmt.projects SET avatar_key = $1 WHERE id = $2`;

                                                        client.query(query2, [avatar_key, project_id], para_cb);
                                                    } else {
                                                        para_cb();
                                                    }
                                                },
                                                members(para_cb) {
                                                    if (isMemberInsertionSkippedForProjectType(options.project_type)) {
                                                        // Some project types do not have members despite being private.
                                                        // Different permissions will be used to check user access to such projects.
                                                        para_cb();
                                                        return;
                                                    }

                                                    let memberQuery =
                                                        'INSERT INTO task_mgmt.project_members(project_id, userid, date_joined, permission_level, workspace_id) VALUES ';
                                                    const memberParams = [];
                                                    const now = new Date().getTime();
                                                    for (let i = 0; i < userRows.length; i += 1) {
                                                        if (i !== 0) {
                                                            memberQuery += ', ';
                                                        }

                                                        privacy.insertProjectPrivacy(
                                                            'member_added',
                                                            userid,
                                                            project_id,
                                                            {
                                                                member: userRows[i].userid,
                                                                permission_level: 5,
                                                                team: options.team,
                                                            }
                                                        );

                                                        memberQuery += `($${memberParams.length + 1}, $${
                                                            memberParams.length + 2
                                                        }, $${memberParams.length + 3}, $${memberParams.length + 4}, $${
                                                            memberParams.length + 5
                                                        })`;

                                                        memberParams.push(
                                                            project_id,
                                                            userRows[i].userid,
                                                            now,
                                                            5,
                                                            options.team
                                                        );
                                                        userAcl[userRows[i].userid] = 5;
                                                    }
                                                    client.query(memberQuery, memberParams, para_cb);
                                                },
                                                group_members(para_cb) {
                                                    if (!options.add_groups || !options.add_groups.length) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    if (isMemberInsertionSkippedForProjectType(options.project_type)) {
                                                        // Some project types do not have members despite being private.
                                                        // Different permissions will be used to check user access to such projects.
                                                        para_cb();
                                                        return;
                                                    }

                                                    const queries = [];
                                                    options.add_groups.forEach(grp => {
                                                        privacy.insertProjectPrivacy(
                                                            'group_added',
                                                            userid,
                                                            project_id,
                                                            {
                                                                group_id: grp,
                                                                permission_level: 5,
                                                                team: options.team,
                                                            }
                                                        );

                                                        queries.push({
                                                            query: `
                                                                INSERT INTO task_mgmt.project_group_members(project_id, group_id, permission_level, date_added, workspace_id)
                                                                VALUES ($1, $2, $3, $4, $5) ON CONFLICT DO NOTHING`,
                                                            params: [
                                                                project_id,
                                                                grp,
                                                                5,
                                                                new Date().getTime(),
                                                                options.team,
                                                            ],
                                                        });
                                                        groupAcl[grp] = 5;
                                                    });

                                                    async.each(
                                                        queries,
                                                        (queryObj, each_cb) => {
                                                            client.query(queryObj.query, queryObj.params, each_cb);
                                                        },
                                                        eachErr => {
                                                            para_cb(eachErr);
                                                        }
                                                    );
                                                },
                                                statusQueries(para_cb) {
                                                    const queries = [];
                                                    options.statuses.forEach((status, idx) => {
                                                        if (idx !== 0) {
                                                            query += ', ';
                                                        }
                                                        if (status.status.toLowerCase() === 'closed' && !status.type) {
                                                            status.status = 'Closed';
                                                            status.type = 'closed';
                                                        } else if (
                                                            status.status.toLowerCase() === 'open' &&
                                                            !status.type
                                                        ) {
                                                            status.status = 'Open';
                                                            status.type = 'open';
                                                        } else {
                                                            status.type = status.type || 'custom';
                                                            if (
                                                                status.status !== 'Open' &&
                                                                status.status !== 'Closed'
                                                            ) {
                                                                status.status = status.status.toLowerCase();
                                                            }
                                                        }

                                                        if (status.status.toLowerCase() === 'open') {
                                                            status.status = 'Open';
                                                        }
                                                        if (status.status.toLowerCase() === 'closed') {
                                                            status.status = 'Closed';
                                                        }

                                                        status.color = assureStatusColor(status);

                                                        const status_id = generateStatusId({
                                                            project_id,
                                                        });

                                                        queries.push({
                                                            query: 'INSERT INTO task_mgmt.statuses(id, project_id, status, orderindex, color, type, status_group, workspace_id) (SELECT $6, $1, $2, $3, $4, $5, $7, (SELECT team FROM task_mgmt.projects WHERE id = $1) WHERE NOT EXISTS (SELECT * FROM task_mgmt.statuses WHERE project_id = $1 AND status = $2))',
                                                            params: [
                                                                project_id,
                                                                status.status,
                                                                idx,
                                                                status.color,
                                                                status.type,
                                                                status_id,
                                                                `proj_${project_id}`,
                                                            ],
                                                        });
                                                    });

                                                    async.each(
                                                        queries,
                                                        (queryObj, each_cb) => {
                                                            client.query(queryObj.query, queryObj.params, each_cb);
                                                        },
                                                        eachErr => {
                                                            para_cb(eachErr);
                                                        }
                                                    );
                                                },
                                                onboardingComplete(para_cb) {
                                                    if (!options.mark_onboarding_complete) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    client.query(
                                                        "UPDATE task_mgmt.teams SET setup_step = 'completed', onboarding_complete_time = $1 WHERE onboarding_complete_time IS NULL and id = $2",
                                                        [new Date().getTime(), options.team],
                                                        para_cb
                                                    );
                                                },
                                                updateInvites(para_cb) {
                                                    if (email_invite_userids.length === 0) {
                                                        para_cb();
                                                        return;
                                                    }
                                                    versionUpdates.push(
                                                        updateRequestForWorkspaceMembers({
                                                            workspaceId: Number(options.team),
                                                            users: email_invite_userids,
                                                            operationType: OperationType.UPDATE,
                                                        })
                                                    );
                                                    client.query(
                                                        'UPDATE task_mgmt.team_members SET invite_sent = true WHERE team_id = $1 AND userid = ANY($2)',
                                                        [options.team, email_invite_userids],
                                                        para_cb
                                                    );
                                                },
                                                sprintSettings(para_cb) {
                                                    if (
                                                        !options.features.sprints ||
                                                        !options.features.sprints.settings
                                                    ) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    const sprintQuery = teamHelper.generateSprintQuery(
                                                        options.team,
                                                        options.features.sprints.settings
                                                    );
                                                    async.each(
                                                        sprintQuery,
                                                        (queryObj, each_cb) => {
                                                            client.query(queryObj.query, queryObj.params, each_cb);
                                                        },
                                                        eachErr => {
                                                            para_cb(eachErr);
                                                        }
                                                    );
                                                },
                                                async addObjectAclEntry(para_cb) {
                                                    const egressService = getEgressAccessInfoService();
                                                    const egressQuery = egressService.getUpsertObjectAclsQuery([
                                                        {
                                                            object_id: project_id,
                                                            object_type: ObjectType.SPACE,
                                                            workspace_id: options.team,
                                                            user_acl: userAcl,
                                                            group_acl: groupAcl,
                                                            private: options.private || false,
                                                            archived: Boolean(options.archived),
                                                            deleted: false,
                                                            object_id_int: Number(project_id),
                                                            hierarchy_scopes: [
                                                                {
                                                                    object_id: project_id,
                                                                    object_type: ObjectType.SPACE,
                                                                },
                                                            ],
                                                            version_vector: {},
                                                        },
                                                    ]);
                                                    versionUpdates.push(
                                                        userHierarchyVersionUpdateRequests({
                                                            objectType: ObjectType.SPACE,
                                                            objectId: project_id,
                                                            workspaceId: options.team,
                                                            operation: OperationType.CREATE,
                                                        })
                                                    );
                                                    client.query(egressQuery.query, egressQuery.params, para_cb);
                                                },
                                                projectPrefix(para_cb) {
                                                    if (!options.project_prefix) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    client.query(
                                                        `INSERT INTO task_mgmt.used_project_prefixes(team_id, project_id, project_prefix) (SELECT $1, $2, $3 WHERE NOT EXISTS (SELECT 1 FROM tasK_mgmt.used_project_prefixes WHERE team_id = $1 AND project_id = $2 AND project_prefix = $3))`,
                                                        [options.team, project_id, options.project_prefix],
                                                        para_cb
                                                    );
                                                },
                                                timeTrackingDefaultToBillable(para_cb) {
                                                    const { default_to_billable } = options.features.time_tracking;

                                                    if (
                                                        default_to_billable == null ||
                                                        !Object.values(TimeTrackingDefaultToBillable).includes(
                                                            default_to_billable
                                                        )
                                                    ) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    const clickappValue = {
                                                        time_tracking: {
                                                            default_to_billable,
                                                        },
                                                    };

                                                    const queryDetails =
                                                        TimeTrackingDefaultToBillableSpaceClickapp.getUpsertQuery(
                                                            project_id,
                                                            clickappValue,
                                                            options.team
                                                        );

                                                    client.query(queryDetails.query, queryDetails.params, para_cb);
                                                },

                                                async objectAccessInfo(para_cb) {
                                                    if (
                                                        !isValidDefaultPermissionLevel(
                                                            options.default_permission_level
                                                        ) ||
                                                        !isDefaultLocationPermissionsEnabled(options.team)
                                                    ) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    try {
                                                        const queryObj = upsertObjectAccessInfoQueryObject({
                                                            objectId: project_id,
                                                            objectType: ObjectType.SPACE,
                                                            workspaceId: options.team,
                                                            defaultPermissionLevel: options.default_permission_level,
                                                        });
                                                        client.query(queryObj.query, queryObj.params, para_cb);
                                                    } catch (error) {
                                                        para_cb(error);
                                                    }
                                                },

                                                async createLocationOverviewDashboard(para_cb) {
                                                    try {
                                                        await getOrCreateLocationOverviewDashboard({
                                                            userId: userid,
                                                            parentType: ParentTypeAbbrev.space,
                                                            parentId: project_id.toString(),
                                                            preferredLanguage: options.preferredLanguage || 'en-US',
                                                        });

                                                        para_cb();
                                                    } catch (lodaavError) {
                                                        logger.warn({
                                                            msg: 'Failed to create Location Overview Dashboard for new space',
                                                            ECODE: 'PROJ_597',
                                                            project_id,
                                                            userid,
                                                            err: lodaavError,
                                                        });

                                                        // Continue without failing the space creation
                                                        para_cb();
                                                    }
                                                },
                                            },
                                            paraErr => {
                                                if (paraErr) {
                                                    db.rollback(client, done);
                                                    if (paraErr.ECODE) {
                                                        cb(new ProjectError(paraErr, 'PROJ_082'));
                                                    } else {
                                                        cb(new ProjectError(paraErr, 'PROJ_003'));
                                                    }
                                                } else {
                                                    let first_status = null;
                                                    let open_status = null;

                                                    if (
                                                        options.statuses &&
                                                        options.statuses[1] &&
                                                        options.statuses[1].status
                                                    ) {
                                                        first_status = options.statuses[1].status;
                                                    }
                                                    if (options.statuses) {
                                                        open_status = options.statuses.filter(
                                                            status => status.type === 'open'
                                                        )[0].status;
                                                    }
                                                    async.series(
                                                        [
                                                            async series_cb => {
                                                                if (options.client) {
                                                                    if (versionUpdates?.length) {
                                                                        client.versionUpdates ??= [];
                                                                        client.versionUpdates.push(...versionUpdates);
                                                                    }
                                                                    series_cb();
                                                                    return;
                                                                }
                                                                if (versionUpdates.length > 0) {
                                                                    try {
                                                                        const txClient = new TransactionClientImpl(
                                                                            client,
                                                                            ovm
                                                                        );

                                                                        events = await ovm.updateVersions(
                                                                            txClient,
                                                                            versionUpdates
                                                                        );
                                                                    } catch (versionUpdateError) {
                                                                        logger.error({
                                                                            msg: 'Failed to write OVM update',
                                                                            ECODE: 'OVM_WS_511',
                                                                            OVM_CRITICAL: true,
                                                                            err: versionUpdateError,
                                                                        });

                                                                        series_cb(versionUpdateError);
                                                                        return;
                                                                    }
                                                                }

                                                                client.query('COMMIT', async commitErr => {
                                                                    if (commitErr) {
                                                                        series_cb(
                                                                            new ProjectError(commitErr, 'PROJ_083')
                                                                        );
                                                                    } else {
                                                                        done();

                                                                        if (events) {
                                                                            // eslint-disable-next-line @typescript-eslint/no-empty-function
                                                                            await ovm
                                                                                .notifyChanges(events)
                                                                                .catch(() => {});
                                                                        }
                                                                        series_cb();
                                                                    }
                                                                });
                                                            },
                                                            series_cb => {
                                                                if (options.categories) {
                                                                    async.eachSeries(
                                                                        options.categories,
                                                                        (_category, each_cb) => {
                                                                            const categoryOptions = {
                                                                                team_id: options.team,
                                                                                should_encrypt: options.should_encrypt,
                                                                            };

                                                                            if (!client.versionUpdates) {
                                                                                client.versionUpdates = [];
                                                                            }
                                                                            categoryMod._createCategoriesWithClient(
                                                                                userid,
                                                                                project_id,
                                                                                {
                                                                                    ...categoryOptions,
                                                                                    skip_access: true,
                                                                                    client,
                                                                                    done,
                                                                                    first_status,
                                                                                    open_status,
                                                                                    categories: [_category],
                                                                                    ws_key: options.ws_key,
                                                                                },
                                                                                each_cb
                                                                            );
                                                                            versionUpdates.push(client.versionUpdates);
                                                                        },
                                                                        seriesErr2 => {
                                                                            series_cb(seriesErr2);
                                                                        }
                                                                    );
                                                                } else {
                                                                    series_cb();
                                                                }
                                                            },
                                                            async series_cb => {
                                                                if (options.client) {
                                                                    series_cb();
                                                                    return;
                                                                }

                                                                if (versionUpdates.length > 0) {
                                                                    try {
                                                                        const txClient = new TransactionClientImpl(
                                                                            client,
                                                                            ovm
                                                                        );
                                                                        events = await ovm.updateVersions(
                                                                            txClient,
                                                                            versionUpdates
                                                                        );
                                                                    } catch (versionUpdateError) {
                                                                        logger.error({
                                                                            msg: 'Failed to write OVM update',
                                                                            ECODE: 'OVM_WS_511',
                                                                            OVM_CRITICAL: true,
                                                                            err: versionUpdateError,
                                                                        });

                                                                        series_cb(versionUpdateError);
                                                                        return;
                                                                    }
                                                                }

                                                                client.query('COMMIT', async commitErr => {
                                                                    if (commitErr) {
                                                                        series_cb(
                                                                            new ProjectError(commitErr, 'PROJ_083')
                                                                        );
                                                                    } else {
                                                                        done();
                                                                        if (events) {
                                                                            // eslint-disable-next-line @typescript-eslint/no-empty-function
                                                                            await ovm
                                                                                .notifyChanges(events)
                                                                                .catch(() => {});
                                                                        }
                                                                        series_cb();
                                                                    }
                                                                });
                                                            },
                                                            series_cb => {
                                                                helpers.setGroupedProjectsStatuses(
                                                                    [project_id],
                                                                    {},
                                                                    series_cb
                                                                );
                                                            },
                                                            series_cb => {
                                                                if (!options.avatar) {
                                                                    series_cb();
                                                                    return;
                                                                }
                                                                thumb.createProfilePicture(
                                                                    options.avatar.path,
                                                                    thumbErr => {
                                                                        if (thumbErr) {
                                                                            series_cb(thumbErr);
                                                                            return;
                                                                        }

                                                                        const s3params = {
                                                                            Bucket: config.aws.bucket,
                                                                            Key: avatar_key,
                                                                            Body: fs.createReadStream(
                                                                                options.avatar.path
                                                                            ),
                                                                        };

                                                                        s3.upload(s3params, s3err => {
                                                                            fs.unlink(options.avatar.path, () => {});
                                                                            if (s3err) {
                                                                                series_cb(
                                                                                    new ProjectError(s3err, 'PROJ_025')
                                                                                );
                                                                            } else {
                                                                                series_cb();
                                                                            }
                                                                        });
                                                                    }
                                                                );
                                                            },
                                                        ],
                                                        seriesErr2 => {
                                                            if (seriesErr2) {
                                                                db.rollback(client, done);
                                                                cb(new ProjectError(seriesErr2, 'PROJ_004'));
                                                            } else {
                                                                cb(null, {
                                                                    id: project_id,
                                                                    users_invited: userRows,
                                                                });
                                                                email_invites.forEach(row => {
                                                                    const initials = userInitials.getInitials(
                                                                        row.invited_by_email,
                                                                        row.invited_by_username
                                                                    );
                                                                    const profilePicture =
                                                                        cf_sign.getCloudfrontPublicURL(
                                                                            row.profile_picture_key
                                                                        );

                                                                    email_helper.sendTeamInvite(
                                                                        row.userid,
                                                                        row.team_id,
                                                                        row.team_name,
                                                                        row.email,
                                                                        row.invite_code,
                                                                        row.invited_by_username || row.invited_By_email,
                                                                        row.invited_by_email,
                                                                        profilePicture,
                                                                        row.color,
                                                                        initials
                                                                    );
                                                                    delayed_emails.createInviteEmails(
                                                                        row.userid,
                                                                        row.team_id,
                                                                        row.date_invited
                                                                    );

                                                                    let team_avatar = null;
                                                                    if (row.avatar_key) {
                                                                        team_avatar =
                                                                            cf_sign.getLongCloudfrontSignedURL(
                                                                                row.avatar_key,
                                                                                {
                                                                                    skip_signed_auth:
                                                                                        environment.isProdEU,
                                                                                }
                                                                            );
                                                                    }

                                                                    sqs.sendWSMessage('sendTeamInvite', [
                                                                        row.userid,
                                                                        row.team_id,
                                                                        row.team_name,
                                                                        row.email,
                                                                        row.invite_code,
                                                                        row.invited_by_username || row.invited_By_email,
                                                                        profilePicture,
                                                                        row.color,
                                                                        initials,
                                                                        team_avatar,
                                                                    ]);
                                                                });

                                                                if (options.project_prefix) {
                                                                    customTaskIds.addProjectToProcessingSet(project_id);
                                                                }

                                                                _getProject(
                                                                    userid,
                                                                    project_id,
                                                                    {
                                                                        include_removed: false,
                                                                        use_master: true,
                                                                        use_replica: false,
                                                                    },
                                                                    (getErr, project) => {
                                                                        if (!getErr && project) {
                                                                            privacy.insertProjectPrivacy(
                                                                                'privacy_changed',
                                                                                userid,
                                                                                project_id,
                                                                                {
                                                                                    private: project.private,
                                                                                    team: options.team,
                                                                                }
                                                                            );

                                                                            webhook.sendWebhookMessage('spaceCreated', {
                                                                                project_id,
                                                                                team_id: options.team,
                                                                            });

                                                                            sqs.sendWSMessage('sendProjectCreated', [
                                                                                { project, ws_key: options.ws_key },
                                                                            ]);
                                                                        }
                                                                    }
                                                                );
                                                            }
                                                        }
                                                    );
                                                }
                                            }
                                        );
                                    }
                                });
                            }
                        );
                    });
                }
            );
        }
    );
}
export { _createProject };

export function createProject(req, resp, next) {
    const userid = getUserFromDecodedToken(req, 'createProject');

    const jsonFields = [
        'project_id',
        'private',
        'team',
        'name',
        'features',
        'statuses',
        'default_preset_view',
        'preset_views',
        'list_view_settings',
        'board_view_settings',
        'gantt_view_settings',
        'box_view_settings',
        'calendar_view_settings',
        'activity_view_settings',
        'mind_map_view_settings',
        'timeline_view_settings',
        'table_view_settings',
        'workload_view_settings',
        'add',
        'add_groups',
        'rem',
        'rem_groups',
        'edit',
        'priorities',
        'avatar_source',
        'avatar_value',
    ];

    if (req.get('Content-Type').includes('multipart')) {
        let jsonErr = false;

        jsonFields.forEach(field => {
            if (req.body[field] == null) {
                return;
            }

            try {
                req.body[field] = JSON.parse(req.body[field]);
            } catch (e) {
                jsonErr = true;
            }
        });

        if (jsonErr) {
            next(new ProjectError('JSON parse error', 'PROJ_029', 400));
            return;
        }
    }

    const {
        name,
        slack_channel,
        due_date_time,
        content,
        color,
        due_date,
        validate_name,
        preset_views,
        default_preset_view,
        add: usersToAdd = [],
        statuses = [
            { status: 'Open', color: config.default_open_status.color },
            { status: 'Closed', color: config.default_closed_status.color },
        ],
        features,
        team,
        list_view_settings,
        board_view_settings,
        gantt_view_settings,
        calendar_view_settings,
        box_view_settings,
        activity_view_settings,
        mind_map_view_settings,
        timeline_view_settings,
        workload_view_settings,
        table_view_settings,
        list_view_template,
        board_view_template,
        gantt_view_template,
        calendar_view_template,
        box_view_template,
        activity_view_template,
        mind_map_view_template,
        timeline_view_template,
        workload_view_template,
        table_view_template,
        list_view_update_views,
        board_view_update_views,
        gantt_view_update_views,
        calendar_view_update_views,
        box_view_update_views,
        activity_view_update_views,
        mind_map_view_update_views,
        timeline_view_update_views,
        workload_view_update_views,
        table_view_update_views,
        avatar_source,
        avatar_value,
        map_view_template,
        map_view_update_views,
        add_groups,
        admin_can_manage,
        creator,
        default_permission_level,
    } = req.body;

    const _private = req.body.private || false;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;

    let { mark_onboarding_complete, multiple_assignees } = req.body;
    let avatar = null;

    if (req.files && req.files.avatar) {
        [avatar] = req.files.avatar;
    }

    if (statuses) {
        const first_done_status_idx = _.findIndex(
            statuses,
            status_obj => ['done', 'closed'].includes(status_obj.type) || status_obj.status === 'Closed'
        );

        const done_statuses_not_last = statuses
            .slice(first_done_status_idx)
            .some(status_obj => !['done', 'closed'].includes(status_obj.type) && status_obj.status !== 'Closed');

        if (done_statuses_not_last) {
            next(new ProjectError('Done statuses must come last in statuses array', 'PROJ_124', 400));
            return;
        }
    }

    if (multiple_assignees === 'false') {
        multiple_assignees = false;
    } else if (multiple_assignees == null) {
        multiple_assignees = true;
    }

    if (mark_onboarding_complete === 'false') {
        mark_onboarding_complete = false;
    }

    logger.info({
        msg: 'Create project request',
        userid,
        team,
        name,
        private: _private,
        usersToAdd,
        _statuses: statuses,
        multiple_assignees,
        slack_channel,
    });

    if (!team) {
        next(new ProjectError('Team not provided', 'PROJ_021', 400));
        return;
    }

    // project_type can only be set by the clickbot user
    const project_type = userid === config.clickbot_assignee ? req.body.project_type : undefined;

    // Get preferred language from request
    const preferredLanguage = getPreferredLanguage(req) || 'en-US';

    _createProject(
        userid,
        {
            team,
            name,
            usersToAdd,
            private: _private,
            statuses,
            avatar,
            multiple_assignees,
            slack_channel,
            mark_onboarding_complete,
            features,
            content,
            color,
            due_date,
            due_date_time,
            validate_name,
            ws_key,
            preset_views,
            default_preset_view,
            list_view_settings,
            board_view_settings,
            gantt_view_settings,
            calendar_view_settings,
            box_view_settings,
            activity_view_settings,
            mind_map_view_settings,
            timeline_view_settings,
            table_view_settings,
            workload_view_settings,
            list_view_template,
            board_view_template,
            gantt_view_template,
            calendar_view_template,
            box_view_template,
            activity_view_template,
            mind_map_view_template,
            timeline_view_template,
            workload_view_template,
            table_view_template,
            list_view_update_views,
            board_view_update_views,
            gantt_view_update_views,
            calendar_view_update_views,
            box_view_update_views,
            activity_view_update_views,
            mind_map_view_update_views,
            timeline_view_update_views,
            workload_view_update_views,
            table_view_update_views,
            avatar_source,
            avatar_value,
            map_view_template,
            map_view_update_views,
            add_groups,
            admin_can_manage,
            creator,
            project_type,
            default_permission_level,
            preferredLanguage,
        },
        (err, result) => {
            if (err) {
                next(err);
            } else {
                reportProjectChanged(result.id);
                resp.status(200).send(result);
            }
        }
    );
}

export function promiseCreateProject(userid, options = {}) {
    return new Promise((res, rej) => {
        _createProject(userid, options, (err, data) => {
            if (err) rej(err);
            else {
                reportProjectChanged(data.id);
                res(data);
            }
        });
    });
}

function getRepoProjects(projectIDs, cb) {
    if (projectIDs.length === 0) {
        cb(undefined, new Map());
        return;
    }

    const query = `
        SELECT DISTINCT
                id,
                NULLIF(********************.gl_repo_id IS NOT NULL, FALSE) AS has_gitlab,
                NULLIF(project_bitbucket_repos.bb_repo_id IS NOT NULL, FALSE) AS has_bitbucket
        FROM unnest($1::bigint[]) AS id
        LEFT JOIN task_mgmt.********************
            ON ********************.project_id = id
        LEFT JOIN task_mgmt.project_bitbucket_repos
            ON project_bitbucket_repos.project_id = id
    `;
    db.globalReadQuery(
        query,
        [projectIDs],
        {
            globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_34,
        },
        (err, result) => {
            if (err) {
                cb(err);
                return;
            }
            cb(
                undefined,
                new Map(
                    result.rows.map(row => [
                        row.id,
                        {
                            has_gitlab: row.has_gitlab,
                            has_bitbucket: row.has_bitbucket,
                        },
                    ])
                )
            );
        }
    );
}

const _getProjects = tracer.wrap('project._getProject', {}, __getProjects);
function __getProjects(userid, team, options, cb) {
    if (!options) {
        options = {};
    }

    if (team) {
        orderindexMigration.addToQueue(team).catch(() => {});
    }

    const valid_team_roles = [];
    Object.keys(config.roles).forEach(role_key => {
        if (config.roles[role_key].public_spaces_visible) {
            valid_team_roles.push(role_key);
        }
    });

    let query;
    let params;
    const timeInStatusSelect = options.team_time_in_status_feature ? 'teams.time_in_status,' : '';
    isLimitedMemberEnabledCb(team, (lmErr, isLimitedMemberEnabled) => {
        if (lmErr) {
            cb(lmErr);
            return;
        }

        // "team == null" is only used in the Chrome plugin right now and we are looking to deprecate it.
        // For the case of limited member enabled I think its fair to just deal with the behavior of this
        // returning "false" until the Split flag and "charge_for_internal_guests" checks are completely removed.
        // See https://github.com/time-loop/clickup/pull/45907/files#r1813420913
        if (team == null) {
            query = `
                SELECT DISTINCT
                    projects.*,
                    project_indexes.orderindex as project_orderindex,
                    project_preferences.hide_project,
                    default_categories.default_category,
                    users.id as userid,
                    users.email,
                    users.username,
                    users.profile_picture_key,
                    users.color AS user_color,
                    coalesce(teams.time_estimate_rollup, false) AS time_estimate_rollup,
                    coalesce(teams.time_tracking_rollup, false) AS time_tracking_rollup,
                    coalesce(project_members.hidden, false) as hidden,
                    teams.using_gitlab,
                    teams.using_bitbucket,
                    ${timeInStatusSelect}
                    team_members.role,
                    team_members.can_see_time_spent,
                    team_members.can_see_time_estimated,
                    coalesce(team_members.can_see_points_estimated, true) AS can_see_points_estimated
                FROM
                    task_mgmt.projects
                    LEFT JOIN task_mgmt.users ON users.id = projects.owner
                    LEFT JOIN task_mgmt.team_members ON team_members.userid = $1
                    LEFT JOIN task_mgmt.teams ON projects.team = teams.id
                    LEFT JOIN task_mgmt.project_indexes ON project_indexes.userid = $1 AND project_indexes.project_id = projects.id
                    LEFT OUTER JOIN (SELECT * FROM task_mgmt.project_preferences WHERE userid = $1) AS project_preferences
                        ON projects.id = project_preferences.project_id
                    LEFT OUTER JOIN (SELECT * FROM task_mgmt.project_members WHERE userid = $1) AS project_members
                        ON project_members.project_id = projects.id
                    LEFT OUTER JOIN task_mgmt.default_categories
                        ON (default_categories.userid = $1 AND default_categories.project_id = projects.id)
                    LEFT JOIN LATERAL (
                        SELECT
                            project_group_members.project_id,
                            project_group_members.group_id
                        FROM
                            task_mgmt.group_members
                            JOIN task_mgmt.project_group_members ON
                                    project_group_members.group_id = group_members.group_id AND
                                    project_group_members.project_id = projects.id
                        WHERE
                                group_members.userid = $1
                                ${whereNotGuest(isLimitedMemberEnabled)}
                        LIMIT 1
                    ) AS project_group_member ON project_group_member.project_id = projects.id
                WHERE
                    (projects.importing = false OR projects.importing IS NULL)
                    AND ${getExcludeHiddenSpacesQueryCondition()}
                    AND projects.template = false
                    AND team_members.team_id = teams.id
                    AND team_members.date_joined IS NOT NULL
                    AND team_members.deleted IS NOT TRUE
                `;
            params = [userid];

            if (options.archived_projects) {
                query += ` AND projects.archived = true`;
            } else if (!options.include_archived) {
                query += ' AND projects.archived = false';
            }

            if (!options.public) {
                query += `
                    AND (
                        (projects.private = false AND team_members.role = ANY($${params.push(valid_team_roles)}))
                        OR (project_members.permission_level IS NOT NULL OR project_group_member.group_id IS NOT NULL)
                    ) `;
            }
        } else {
            query = `
                SELECT DISTINCT
                    projects.*,
                    project_indexes.orderindex as project_orderindex,
                    project_preferences.hide_project,
                    default_categories.default_category,
                    users.id as userid,
                    users.email,
                    users.username,
                    users.profile_picture_key,
                    users.color AS user_color,
                    coalesce(project_members.hidden, false) as hidden,
                    teams.using_gitlab,
                    teams.using_bitbucket,
                    ${timeInStatusSelect}
                    team_members.role,
                    team_members.can_see_time_spent,
                    team_members.can_see_time_estimated,
                    coalesce(team_members.can_see_points_estimated, true) AS can_see_points_estimated
                FROM
                    task_mgmt.projects
                    LEFT JOIN task_mgmt.users ON users.id = projects.owner
                    LEFT JOIN task_mgmt.team_members ON team_members.userid = $1 AND team_members.team_id = $2
                    LEFT JOIN task_mgmt.teams ON projects.team = teams.id
                    LEFT JOIN task_mgmt.project_indexes ON project_indexes.userid = $1 AND project_indexes.project_id = projects.id
                    LEFT OUTER JOIN (SELECT * FROM task_mgmt.project_preferences WHERE userid = $1) AS project_preferences
                        ON projects.id = project_preferences.project_id
                    LEFT OUTER JOIN (SELECT * FROM task_mgmt.project_members WHERE userid = $1) AS project_members
                        ON project_members.project_id = projects.id
                    LEFT OUTER JOIN task_mgmt.default_categories
                        ON (default_categories.userid = $1 AND default_categories.project_id = projects.id)
                    LEFT JOIN LATERAL (
                        SELECT
                            project_group_members.project_id,
                            project_group_members.group_id
                        FROM
                            task_mgmt.group_members
                            JOIN task_mgmt.project_group_members ON
                                    project_group_members.group_id = group_members.group_id AND
                                    project_group_members.project_id = projects.id
                        WHERE
                                group_members.userid = $1
                                ${whereNotGuest(isLimitedMemberEnabled)}
                        LIMIT 1
                    ) AS project_group_member ON project_group_member.project_id = projects.id
                WHERE
                    teams.id = $2
                    AND team_members.date_joined IS NOT NULL
                    AND team_members.deleted IS NOT TRUE
                    AND (projects.importing = false OR projects.importing IS NULL)
                    AND ${getExcludeHiddenSpacesQueryCondition()}
                    AND projects.template = false`;
            params = [userid, team];

            if (options.archived) {
                query += ` AND projects.archived = true`;
            } else if (!options.include_archived) {
                query += ' AND projects.archived = false';
            }

            if (!options.public) {
                query += `
                    AND (
                        (projects.private = false AND team_members.role = ANY($${params.push(valid_team_roles)}))
                        OR (project_members.permission_level IS NOT NULL OR project_group_member.group_id IS NOT NULL)
                    ) `;
            }
        }

        if (!options.include_deleted) {
            query += ` AND projects.deleted = false`;
        }

        if (options.project_ids) {
            query += ` AND projects.id = ANY($${params.push(options.project_ids)})`;
        }
        const itemsPerPage = options.itemsPerPage ? Number(options.itemsPerPage) : 10;
        if (options.paging) {
            if (options.last_index) {
                query += `
                    AND project_indexes.userid = $1
                    AND project_indexes.orderindex > $${params.push(options.last_index)}
                `;
            }
            query += `
                ORDER BY project_indexes.orderindex ASC
                LIMIT ${itemsPerPage}
            `;
        }

        db.readQuery(query, params, { use_master: options.use_master }, (err, result) => {
            if (err) {
                cb(new ProjectError(err, 'PROJ_007'));
            } else {
                if (result.rows.length === 0) {
                    cb(null, []);
                    return;
                }
                let indexes_need_fixing = false;
                result.rows.forEach(row => {
                    if (row.project_orderindex == null) {
                        indexes_need_fixing = true;
                    }
                });

                const indexes = result.rows.map(row => row.project_orderindex);
                indexes.forEach((i, idx) => {
                    if (indexes.indexOf(i) !== idx && i != null) {
                        indexes_need_fixing = true;
                    }
                });

                if (indexes_need_fixing && !options.project_indexes_fixed) {
                    projectPosition.fix_project_indexes(userid, team, () => {
                        options.project_indexes_fixed = true;
                        _getProjects(userid, team, options, cb);
                    });
                    return;
                }

                const teams = [];
                const project_ids = [];
                const template_ids = [];

                result.rows.forEach(row => {
                    if (row.team) {
                        teams.push(row.team);
                    }
                    row.hide_project = row.hide_project || false;
                    row.avatar = cf_sign.getLongCloudfrontSignedURL(row.avatar_key, {
                        skip_signed_auth: environment.isProdEU,
                    });
                    delete row.avatar_key;

                    row = helpers.formatFeaturesFromRow(row);

                    row.owner = {
                        id: row.userid,
                        username: row.username,
                        email: row.email,
                        color: row.user_color,
                        initials: userInitials.getInitials(row.email, row.username),
                        profilePicture: cf_sign.getCloudfrontAvatarUrl(row.profile_picture_key),
                    };

                    if (row.list_view_template) {
                        template_ids.push(row.list_view_template);
                    }
                    if (row.board_view_template) {
                        template_ids.push(row.board_view_template);
                    }
                    if (row.calendar_view_template) {
                        template_ids.push(row.calendar_view_template);
                    }
                    if (row.gantt_view_template) {
                        template_ids.push(row.gantt_view_template);
                    }
                    if (row.box_view_template) {
                        template_ids.push(row.box_view_template);
                    }
                    if (row.activity_view_template) {
                        template_ids.push(row.activity_view_template);
                    }
                    if (row.mind_map_view_template) {
                        template_ids.push(row.mind_map_view_template);
                    }
                    if (row.timeline_view_template) {
                        template_ids.push(row.timeline_view_template);
                    }
                    if (row.workload_view_template) {
                        template_ids.push(row.workload_view_template);
                    }
                    if (row.table_view_template) {
                        template_ids.push(row.table_view_template);
                    }
                    if (row.map_view_template) {
                        template_ids.push(row.map_view_template);
                    }

                    row.orderindex = row.project_orderindex;

                    if (
                        !dashboardsAsAViewInSpaceViewSettings(row.team.id) &&
                        row.preset_views?.includes(config.views.view_types.dashboard)
                    ) {
                        row.preset_views = row.preset_views.filter(v => v !== config.views.view_types.dashboard);
                    }

                    delete row.userid;
                    delete row.username;
                    delete row.user_color;
                    delete row.email;
                    delete row.profile_picture_key;
                    delete row.ydoc;

                    project_ids.push(row.id);
                });

                const projects = result.rows;

                async.parallel(
                    {
                        teamMembers(para_cb) {
                            if (options.simple) {
                                para_cb(null, { rows: [] });
                                return;
                            }
                            db.readQuery(
                                'SELECT teams.id, teams.owner, teams.using_github, teams.using_gitlab, teams.using_bitbucket, teams.name, teams.date_created, teams.avatar_key, users.id as userid, users.email as email, users.username as username, users.profile_picture_key, users.color FROM task_mgmt.teams, task_mgmt.users WHERE teams.id = ANY($1) AND users.id = teams.owner',
                                [teams],
                                para_cb
                            );
                        },

                        async clickapps(para_cb) {
                            try {
                                const clickappMap = await clickappsService.buildResourcesClickapps(
                                    project_ids,
                                    ParentType.Project
                                );

                                projects.forEach(project => {
                                    const clickapps = clickappMap[project.id] ?? {};
                                    _.merge(project.features, clickapps);
                                });
                                para_cb();
                            } catch (_err) {
                                para_cb(_err);
                            }
                        },

                        taskCount(para_cb) {
                            if (options.simple || !options.mobile) {
                                para_cb(null, { rows: [] });
                                return;
                            }
                            db.replicaQuery(
                                'SELECT categories.project_id, count(*) FROM task_mgmt.categories, task_mgmt.subcategories, task_mgmt.items, task_mgmt.statuses WHERE categories.project_id = ANY($1) AND categories.archived = false AND categories.id = subcategories.category AND subcategories.deleted = false AND subcategories.template = false AND subcategories.archived = false AND subcategories.id = items.subcategory AND items.deleted = false AND items.archived = false AND items.template = false AND items.parent IS NULL AND items.status = statuses.status AND statuses.status_group = subcategories.status_group AND statuses.type != ALL($2) GROUP BY categories.project_id',
                                [project_ids, config.closed_status_types],
                                para_cb
                            );
                        },
                        members(para_cb) {
                            if (options.simple) {
                                para_cb();
                                return;
                            }
                            _getTeamMembers(project_ids, db, para_cb);
                        },
                        async groupMembers(para_cb) {
                            if (options.simple) {
                                para_cb();
                                return;
                            }
                            try {
                                const grp_results = await projectMember._getGroupMembers(project_ids);
                                para_cb(null, grp_results);
                            } catch (e) {
                                para_cb(e);
                            }
                        },
                        async hierarchyMembers(para_cb) {
                            if (!options.include_hierarchy_members) {
                                para_cb();
                                return;
                            }
                            try {
                                const hierarchyResults = {};
                                let position = 0;
                                const batchSize = 10;
                                const privateIds = projects.filter(proj => proj.private).map(proj => proj.id);
                                while (position < privateIds.length) {
                                    const idsForBatch = privateIds.slice(position, position + batchSize);
                                    const batchResult = await Promise.all(
                                        idsForBatch.map(projectId =>
                                            projectMember.promiseHierarchyMembers(userid, projectId, {
                                                include_children: false,
                                                v2: true,
                                            })
                                        )
                                    );
                                    idsForBatch.forEach((projectId, index) => {
                                        hierarchyResults[projectId] = batchResult[index];
                                    });
                                    position += batchSize;
                                }
                                para_cb(null, hierarchyResults);
                            } catch (e) {
                                para_cb(e);
                            }
                        },
                        assignedMembers(para_cb) {
                            if (!options.include_removed) {
                                para_cb();
                                return;
                            }
                            const memberQuery =
                                'SELECT users.id, users.email, users.username, users.color, users.profile_picture_key, projects.id as project_id FROM task_mgmt.assignees, task_mgmt.items, task_mgmt.subcategories, task_mgmt.categories, task_mgmt.projects, task_mgmt.users WHERE assignees.task_id = items.id AND items.subcategory = subcategories.id AND subcategories.category = categories.id AND categories.project_id = ANY($1) AND users.id = assignees.userid';

                            db.readQuery(memberQuery, [project_ids], para_cb);
                        },
                        statuses(para_cb) {
                            if (options.simple && !options.gantt) {
                                para_cb();
                                return;
                            }
                            helpers.getStatuses(project_ids, { asArray: true }, para_cb);
                        },

                        automationCounts(para_cb) {
                            automationHelpers.getAutomationCount(project_ids, config.parent_types.project, {}, para_cb);
                        },

                        all_statuses(para_cb) {
                            if (options.public || options.simple) {
                                para_cb(null, {});
                                return;
                            }

                            helpers.getGroupedProjectsStatuses(
                                userid,
                                project_ids,
                                { skip_generating_statuses: options.skip_generating_statuses },
                                para_cb
                            );
                        },
                        listViewSettings(para_cb) {
                            if (options.simple) {
                                para_cb();
                                return;
                            }
                            listViewSettings._getListViewSettings(userid, { project_ids }, para_cb);
                        },
                        permissions(para_cb) {
                            if (options.simple && !options.gantt && !options.project_permissions) {
                                para_cb();
                                return;
                            }

                            if (options.public) {
                                para_cb();
                                return;
                            }

                            const spaceAccessCheckOptions = {
                                permissions: [],
                                simple_permissions: options.simple_permissions,
                                checkJoined: false,
                            };

                            if (options.allowArchivedWorkspaces === true) {
                                spaceAccessCheckOptions.allowArchivedWorkspaces = true;
                            }

                            access2.checkAccessProjects(userid, project_ids, spaceAccessCheckOptions, para_cb);
                        },
                        view_parent_settings(para_cb) {
                            if (!options.include_view_settings) {
                                para_cb();
                                return;
                            }
                            const viewParentSettingsQuery = `SELECT parent_id, settings from task_mgmt.view_parent_settings WHERE parent_id = ANY($1)`;
                            db.readQuery(viewParentSettingsQuery, [project_ids], para_cb);
                        },
                        settings_templates(para_cb) {
                            if (template_ids.length === 0) {
                                para_cb();
                                return;
                            }

                            viewMod._getViews(
                                userid,
                                { skipAccess: true, in_view_ids: template_ids, skip_standard: true },
                                (viewErr, viewResult) => {
                                    if (viewErr) {
                                        para_cb(viewErr);
                                    } else {
                                        projects.forEach(project => {
                                            viewResult.views.forEach(view => {
                                                view.standard = false;
                                                if (project.list_view_template === view.id) {
                                                    project.list_view_template = view;
                                                } else if (project.board_view_template === view.id) {
                                                    project.board_view_template = view;
                                                } else if (project.calendar_view_template === view.id) {
                                                    project.calendar_view_template = view;
                                                } else if (project.gantt_view_template === view.id) {
                                                    project.gantt_view_template = view;
                                                } else if (project.box_view_template === view.id) {
                                                    project.box_view_template = view;
                                                } else if (project.activity_view_template === view.id) {
                                                    project.activity_view_template = view;
                                                } else if (project.mind_map_view_template === view.id) {
                                                    project.mind_map_view_template = view;
                                                } else if (project.timeline_view_template === view.id) {
                                                    project.timeline_view_template = view;
                                                } else if (project.table_view_template === view.id) {
                                                    project.table_view_template = view;
                                                } else if (project.workload_view_template === view.id) {
                                                    project.workload_view_template = view;
                                                } else if (project.map_view_template === view.id) {
                                                    project.map_view_template = view;
                                                }
                                            });
                                        });
                                        para_cb();
                                    }
                                }
                            );
                        },
                        repo_projects(para_cb) {
                            getRepoProjects(project_ids, para_cb);
                        },
                    },
                    (paraErr, paraResult) => {
                        if (paraErr) {
                            cb(new ProjectError(paraErr, 'PROJ_008'));
                        } else {
                            const id_to_team = {};
                            paraResult.teamMembers.rows.forEach(row => {
                                row.owner = {
                                    id: row.userid,
                                    username: row.username,
                                    email: row.email,
                                    color: row.color,
                                    initials: userInitials.getInitials(row.email, row.username),
                                    profilePicture: cf_sign.getCloudfrontAvatarUrl(row.profile_picture_key),
                                };

                                delete row.userid;
                                delete row.username;
                                delete row.email;
                                delete row.color;
                                delete row.profile_picture_key;

                                id_to_team[row.id] = row;
                            });
                            let members_to_project = {};
                            const members = paraResult.members ? paraResult.members.rows : [];
                            const group_members = paraResult.groupMembers ? paraResult.groupMembers : {};

                            if (!options.simple) {
                                members_to_project = _membersProject(
                                    options.simple_permissions,
                                    members,
                                    isLimitedMemberEnabled
                                );
                            }
                            const project_memberids = _projectMemberids(members);

                            if (options.include_removed) {
                                paraResult.assignedMembers.rows.forEach(row => {
                                    if (!members_to_project[row.project_id]) {
                                        members_to_project[row.project_id] = [];
                                        project_memberids[row.project_id] = [];
                                    }
                                    if (project_memberids[row.project_id].indexOf(row.id) < 0) {
                                        members_to_project[row.project_id].push({
                                            user: {
                                                id: row.id,
                                                email: row.email,
                                                username: row.username,
                                                color: row.color,
                                                initials: userInitials.getInitials(row.email, row.username),
                                                profilePicture: cf_sign.getCloudfrontAvatarUrl(row.profile_picture_key),
                                            },
                                            removed: true,
                                        });
                                    }
                                });
                            }

                            const id_to_task_count = {};
                            paraResult.taskCount.rows.forEach(row => {
                                id_to_task_count[row.project_id] = row.count;
                            });

                            const view_parent_settings = {};
                            if (options.include_view_settings) {
                                paraResult.view_parent_settings.rows.forEach(row => {
                                    view_parent_settings[row.parent_id] = row.settings;
                                });
                            }

                            const ret_val = [];
                            projects.forEach(project => {
                                if (!options.simple || options.gantt) {
                                    project.statuses = paraResult.statuses.statuses[project.id];
                                    project.permissions = paraResult.permissions[project.id].permissions;
                                    project.permission_level = parseInt(
                                        paraResult.permissions[project.id].permission_level,
                                        10
                                    );
                                } else if (options.simple && options.project_permissions) {
                                    project.permissions = paraResult.permissions[project.id].permissions;
                                }

                                project.view_settings = view_parent_settings[project.id];

                                project.automation_count = paraResult.automationCounts.counts[project.id] || 0;

                                if (!options.simple) {
                                    if (project.team && id_to_team[project.team]) {
                                        project.team = id_to_team[project.team];
                                        project.team.avatar = cf_sign.getLongCloudfrontSignedURL(
                                            project.team.avatar_key,
                                            {
                                                skip_signed_auth: environment.isProdEU,
                                            }
                                        );

                                        delete project.team.avatar_key;
                                    }

                                    project.taskcount = id_to_task_count[project.id] || '0';

                                    project.listViewSettings = paraResult.listViewSettings[project.id];
                                }

                                if (project.private && !options.simple) {
                                    project.members = members_to_project[project.id];
                                    project.group_members = group_members[project.id] || [];
                                }

                                if (options.include_hierarchy_members) {
                                    const currentMembers = paraResult.hierarchyMembers[project.id] || {};
                                    const projCount = currentMembers.members?.project?.length;
                                    const teamCount = currentMembers.members?.team?.length;
                                    if (options.limit_hierarchy_members && currentMembers.members) {
                                        currentMembers.members.project = currentMembers.members.project.slice(0, 10);
                                        currentMembers.members.team = currentMembers.members.team.slice(0, 10);
                                        project.hierarchy_members = currentMembers;
                                    } else {
                                        project.hierarchy_members = currentMembers;
                                    }
                                    project.hierarchy_member_count = projCount + teamCount;
                                }

                                if (paraResult.members && paraResult.members.rows) {
                                    const found_member = paraResult.members.rows.find(
                                        member =>
                                            member.id === userid && Number(member.project_id) === Number(project.id)
                                    );

                                    if (found_member) {
                                        project.hidden = found_member.hidden != null ? found_member.hidden : false;
                                    }
                                }
                                project.all_statuses = paraResult.all_statuses[project.id];
                                const hasRepos = paraResult.repo_projects.get(project.id);
                                project.project_using_gitlab = (project.using_gitlab && hasRepos?.has_gitlab) || null;
                                project.project_using_bitbucket =
                                    (project.using_bitbucket && hasRepos?.has_bitbucket) || null;
                                ret_val.push(project);
                            });
                            ret_val.sort((a, b) => a.project_orderindex - b.project_orderindex);
                            cb(null, ret_val);
                        }
                    }
                );
            }
        });
    });
}
export { _getProjects };

function _promiseGetProjects(userid, team, options) {
    return new Promise((resolve, reject) => {
        try {
            _getProjects(userid, team, options, (err, result) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(result);
                }
            });
        } catch (e) {
            reject(e);
        }
    });
}

export { _promiseGetProjects };

export function getProjects(req, resp, next) {
    const userid = req.decoded_token.user;
    const { team } = req.query;
    let include_removed = req.query.include_removed || false;
    let include_archived = req.query.include_archived || false;
    let archived = req.query.archived || false;
    const paging = req.query.paging === 'true';
    const settings = req.query.settings === 'true';
    const use_master = req.query.use_master === 'true';
    const include_view_settings = modelHelpers.toBoolean(req.query.include_view_settings);
    const skip_generating_statuses = true;
    const include_hierarchy_members = req.query.include_hierarchy_members === 'true';
    const limit_hierarchy_members = req.query.limit_hierarchy_members === 'true';

    const { last_index } = req.query;
    const { itemsPerPage } = req.query;
    let mobile = false;

    let user_agent = req.headers['user-agent'];
    if (user_agent) {
        user_agent = user_agent.toLowerCase();
        if (user_agent.indexOf('ios') >= 0 || user_agent.indexOf('android') >= 0) {
            mobile = true;
        }
    }

    if (team && !isNumeric(team)) {
        resp.status(400).send({
            err: 'Provide a valid teamID',
            ECODE: 'PROJ_099',
        });
        return;
    }

    if (include_removed === 'false') {
        include_removed = false;
    }
    if (include_archived === 'false') {
        include_archived = false;
    }
    if (archived === 'false') {
        archived = false;
    }

    _getProjects(
        userid,
        team,
        {
            include_removed,
            include_archived,
            archived,
            mobile,
            paging,
            last_index,
            itemsPerPage,
            settings,
            use_master,
            skip_generating_statuses,
            include_hierarchy_members,
            limit_hierarchy_members,
            include_view_settings,
        },
        async (err, result) => {
            if (err) {
                next(err);
            } else {
                const workspaceId = req.headers['x-workspace-id'] || team;
                const shouldCallV3 = shouldUseHierarchyV3CoreClient() && workspaceId && !result.deleted;
                if (shouldCallV3) {
                    try {
                        const v3Response = await hierarchyCoreClientInstance.getSpaceInBulk(
                            workspaceId,
                            result.map(r => r.id),
                            req.headers
                        );

                        result.forEach(r => {
                            v3Response.spaces.find(
                                v3 => Number(v3.object_id) === Number(r.id) && mergeHierarchyV3SpaceBulkData(r, v3.data)
                            );
                        });
                    } catch (err2) {
                        logger.error({
                            ECODE: 'HIERARCHY_008',
                            err: err2,
                            msg: 'Failed to get response from hierarchy core v3 client',
                        });

                        if (
                            err2?.code?.toUpperCase() === 'ECONNABORTED' ||
                            err2?.code?.toUpperCase() === 'ECONNREFUSED' ||
                            err2?.code?.toUpperCase() === 'EHOSTUNREACH'
                        ) {
                            resp.status(200).send(result);
                        } else {
                            next(err2);
                        }

                        return;
                    }
                }

                resp.status(200).send(result);
            }
        }
    );
}

function mergeHierarchyV3SpaceBulkData(monolithData, v3Data, additionalData) {
    // skip assignee as it is in a different format in v3
    const simplifiedV3Data = _.omit(v3Data, ['assignee', 'owner', 'team', 'orderindex', 'hidden']);
    Object.assign(monolithData, simplifiedV3Data, additionalData || {});
}

export async function getProjectsCount(req, resp, next) {
    const userid = req.decoded_token.user;
    const { teamId } = req.query;
    if (!teamId) {
        next(new ProjectError('Missing required query parameter: teamId', 'PROJ_056'));
    }
    checkAccessTeam(userid, teamId, {}, async err => {
        if (err) {
            next(err);
        }
        try {
            const result = await queryTeamSpacesCount(teamId);
            resp.status(200).send({
                joined: Number(result.rows[0].joined),
                archived: Number(result.rows[0].archived),
            });
        } catch (e) {
            next(e);
        }
    });
}

function getUpdateViewQuery(project_id, view_type, settings) {
    let update_columns = ``;
    const params = [project_id, view_type];

    if (settings.settings && settings.calendar_settings) {
        settings.settings = { extra: settings.calendar_settings };
    }
    if (settings.settings && settings.timeline_settings) {
        settings.settings = { extra: settings.timeline_settings };
    }
    if (settings.settings && settings.workload_settings) {
        settings.settings = { extra: settings.workload_settings };
    }
    if (settings.settings && settings.gantt_settings) {
        settings.settings = { extra: settings.gantt_settings };
    }
    if (settings.box_settings) {
        delete settings.box_settings.collpapsed;
        settings.settings = { extra: settings.box_settings };
    }

    const valid_settings = [
        'show_task_locations',
        'show_subtask_parent_names',
        'collapse_empty_columns',
        'show_assignees',
        'show_images',
        'extra',
    ];

    let add_comma = false;
    Object.keys(settings.settings).forEach(setting => {
        if (valid_settings.includes(setting)) {
            if (add_comma) {
                update_columns += ', ';
            }

            params.push(settings.settings[setting]);
            update_columns += ` ${setting} = $${params.length}`;
            add_comma = true;
        }
    });

    return [
        {
            query: `
            UPDATE task_mgmt.views
            SET
                ${update_columns}
            WHERE
            type = $2
            AND
            (
                (
                    parent_type = ${parent_types.category}
                    AND parent_id IN (
                        SELECT categories.id::text
                        FROM
                            task_mgmt.categories
                        WHERE
                            categories.project_id = $1
                    )
                )
            )
            AND (visibility = 1 OR visibility IS NULL)
            ${settings.update_views === 1 ? ` AND standard = true` : ``}
        `,
            params,
        },
        {
            query: `
            UPDATE task_mgmt.views
            SET
                ${update_columns}
            WHERE
            type = $2
            AND
            (
                (
                    parent_type = ${parent_types.subcategory}
                    AND parent_id IN (
                        SELECT subcategories.id ::text
                        FROM
                            task_mgmt.categories,
                            task_mgmt.subcategories
                        WHERE
                            categories.project_id = $1
                            AND categories.id = subcategories.category
                    )
                )
            )
            AND (visibility = 1 OR visibility IS NULL)
            ${settings.update_views === 1 ? ` AND standard = true` : ``}
        `,
            params,
        },
        {
            query: `
            UPDATE task_mgmt.views
            SET
                ${update_columns}
            WHERE
            type = $2
            AND
            (
                (
                    parent_type = ${parent_types.project}
                    AND
                    parent_id = $1::text
                )
            )
            AND (visibility = 1 OR visibility IS NULL)
            ${settings.update_views === 1 ? ` AND standard = true` : ``}
        `,
            params,
        },
    ];
}

function getViewUpdateQueries(userid, project_id, options, cb) {
    let queries = [];
    const view_query = `
        SELECT view_id
                FROM tasK_mgmt.views
                WHERE
                    type = $2
                    AND

                        (
                            parent_type = $3
                            AND parent_id IN (
                                SELECT categories.id::text
                                FROM
                                    task_mgmt.categories
                                WHERE
                                    categories.project_id = $1
                            )
                        )
                    AND (visibility = 1 OR visibility IS NULL)
                    AND standard = true
            UNION
                SELECT view_id
                FROM tasK_mgmt.views
                WHERE
                    type = $2
                    AND
                    parent_type = $4
                    AND parent_id IN (
                        SELECT subcategories.id::text
                        FROM
                            task_mgmt.categories,
                            task_mgmt.subcategories
                        WHERE
                            categories.project_id = $1
                            AND categories.id = subcategories.category
                    )
                    AND (visibility = 1 OR visibility IS NULL)
                    AND standard = true
            UNION
                SELECT view_id
                FROM tasK_mgmt.views
                WHERE
                    type = $2
                    AND parent_type = $5
                    AND parent_id = $1::text
                    AND (visibility = 1 OR visibility IS NULL)
                    AND standard = true
    `;
    async.parallel(
        {
            list(para_cb) {
                if (!options.list_view_template || options.list_view_update_views !== 1) {
                    para_cb();
                    return;
                }

                const params = [
                    project_id,
                    config.views.view_types.list,
                    parent_types.category,
                    parent_types.subcategory,
                    parent_types.project,
                ];

                db.readQuery(view_query, params, (err, result) => {
                    if (err) {
                        para_cb(err);
                        return;
                    }

                    if (result.rows.length <= 0) {
                        para_cb();
                        return;
                    }

                    viewMod._getViews(
                        userid,
                        { view_id: options.list_view_template, skip_standard: true },
                        (getErr, getResult) => {
                            if (getErr) {
                                para_cb(getErr);
                                return;
                            }

                            if (!getResult.views[0]) {
                                para_cb();
                                return;
                            }

                            const _options = getResult.views[0];
                            delete _options.name;
                            delete _options.template_members;
                            delete _options.template_group_members;
                            delete _options.template_visibility;

                            _options.view_ids = result.rows.map(row => row.view_id);

                            viewMod._getEditQueries(userid, null, _options, (queryErr, queryResult) => {
                                if (queryErr) {
                                    para_cb(queryErr);
                                    return;
                                }

                                queries = queries.concat(queryResult.queries);

                                para_cb();
                            });
                        }
                    );
                });
            },
            board(para_cb) {
                if (!options.board_view_template || options.board_view_update_views !== 1) {
                    para_cb();
                    return;
                }

                const params = [
                    project_id,
                    config.views.view_types.board,
                    parent_types.category,
                    parent_types.subcategory,
                    parent_types.project,
                ];
                db.readQuery(view_query, params, (err, result) => {
                    if (err) {
                        para_cb(err);
                        return;
                    }

                    if (!result.rows.length) {
                        para_cb();
                        return;
                    }

                    viewMod._getViews(
                        userid,
                        { view_id: options.board_view_template, skip_standard: true },
                        (getErr, getResult) => {
                            if (getErr) {
                                para_cb(getErr);
                                return;
                            }

                            if (!getResult.views[0]) {
                                para_cb();
                                return;
                            }

                            const _options = getResult.views[0];
                            delete _options.name;
                            delete _options.template_members;
                            delete _options.template_group_members;
                            delete _options.template_visibility;

                            _options.view_ids = result.rows.map(row => row.view_id);

                            viewMod._getEditQueries(userid, null, _options, (queryErr, queryResult) => {
                                if (queryErr) {
                                    para_cb(queryErr);
                                    return;
                                }

                                queries = queries.concat(queryResult.queries);

                                para_cb();
                            });
                        }
                    );
                });
            },
            calendar(para_cb) {
                if (!options.calendar_view_template || options.calendar_view_update_views !== 1) {
                    para_cb();
                    return;
                }

                const params = [
                    project_id,
                    config.views.view_types.calendar,
                    parent_types.category,
                    parent_types.subcategory,
                    parent_types.project,
                ];
                db.readQuery(view_query, params, (err, result) => {
                    if (err) {
                        para_cb(err);
                        return;
                    }

                    if (!result.rows.length) {
                        para_cb();
                        return;
                    }

                    viewMod._getViews(
                        userid,
                        { view_id: options.calendar_view_template, skip_standard: true },
                        (getErr, getResult) => {
                            if (getErr) {
                                para_cb(getErr);
                                return;
                            }

                            if (!getResult.views[0]) {
                                para_cb();
                                return;
                            }

                            const _options = getResult.views[0];
                            delete _options.name;
                            delete _options.template_members;
                            delete _options.template_group_members;
                            delete _options.template_visibility;

                            _options.view_ids = result.rows.map(row => row.view_id);

                            viewMod._getEditQueries(userid, null, _options, (queryErr, queryResult) => {
                                if (queryErr) {
                                    para_cb(queryErr);
                                    return;
                                }

                                queries = queries.concat(queryResult.queries);

                                para_cb();
                            });
                        }
                    );
                });
            },
            gantt(para_cb) {
                if (!options.gantt_view_template || options.gantt_view_update_views !== 1) {
                    para_cb();
                    return;
                }

                const params = [
                    project_id,
                    config.views.view_types.gantt,
                    parent_types.category,
                    parent_types.subcategory,
                    parent_types.project,
                ];
                db.readQuery(view_query, params, (err, result) => {
                    if (err) {
                        para_cb(err);
                        return;
                    }

                    if (!result.rows.length) {
                        para_cb();
                        return;
                    }

                    viewMod._getViews(
                        userid,
                        { view_id: options.gantt_view_template, skip_standard: true },
                        (getErr, getResult) => {
                            if (getErr) {
                                para_cb(getErr);
                                return;
                            }

                            if (!getResult.views[0]) {
                                para_cb();
                                return;
                            }

                            const _options = getResult.views[0];
                            delete _options.name;
                            delete _options.template_members;
                            delete _options.template_group_members;
                            delete _options.template_visibility;

                            _options.view_ids = result.rows.map(row => row.view_id);

                            viewMod._getEditQueries(userid, null, _options, (queryErr, queryResult) => {
                                if (queryErr) {
                                    para_cb(queryErr);
                                    return;
                                }

                                queries = queries.concat(queryResult.queries);

                                para_cb();
                            });
                        }
                    );
                });
            },
            box(para_cb) {
                if (!options.box_view_template || String(options.box_view_update_views) !== '1') {
                    para_cb();
                    return;
                }

                const params = [
                    project_id,
                    config.views.view_types.box,
                    parent_types.category,
                    parent_types.subcategory,
                    parent_types.project,
                ];

                db.readQuery(view_query, params, (err, result) => {
                    if (err) {
                        para_cb(err);
                        return;
                    }

                    if (!result.rows.length) {
                        para_cb();
                        return;
                    }

                    viewMod._getViews(
                        userid,
                        { view_id: options.box_view_template, skip_standard: true },
                        (getErr, getResult) => {
                            if (getErr) {
                                para_cb(getErr);
                                return;
                            }

                            if (!getResult.views[0]) {
                                para_cb();
                                return;
                            }

                            const _options = getResult.views[0];
                            delete _options.name;
                            delete _options.template_members;
                            delete _options.template_group_members;
                            delete _options.template_visibility;

                            _options.view_ids = result.rows.map(row => row.view_id);

                            viewMod._getEditQueries(userid, null, _options, (queryErr, queryResult) => {
                                if (queryErr) {
                                    para_cb(queryErr);
                                    return;
                                }

                                queries = queries.concat(queryResult.queries);

                                para_cb();
                            });
                        }
                    );
                });
            },
            activity(para_cb) {
                if (!options.activity_view_template || String(options.activity_view_update_views) !== 1) {
                    para_cb();
                    return;
                }

                const params = [
                    project_id,
                    config.views.view_types.activity,
                    parent_types.category,
                    parent_types.subcategory,
                    parent_types.project,
                ];

                db.readQuery(view_query, params, (err, result) => {
                    if (err) {
                        para_cb(err);
                        return;
                    }

                    if (!result.rows.length) {
                        para_cb();
                        return;
                    }

                    viewMod._getViews(
                        userid,
                        { view_id: options.activity_view_template, skip_standard: true },
                        (getErr, getResult) => {
                            if (getErr) {
                                para_cb(getErr);
                                return;
                            }

                            if (!getResult.views[0]) {
                                para_cb();
                                return;
                            }

                            const _options = getResult.views[0];
                            delete _options.name;
                            delete _options.template_members;
                            delete _options.template_group_members;
                            delete _options.template_visibility;

                            _options.view_ids = result.rows.map(row => row.view_id);

                            viewMod._getEditQueries(userid, null, _options, (queryErr, queryResult) => {
                                if (queryErr) {
                                    para_cb(queryErr);
                                    return;
                                }

                                queries = queries.concat(queryResult.queries);

                                para_cb();
                            });
                        }
                    );
                });
            },
            mindMap(para_cb) {
                if (!options.mind_map_view_template || String(options.mind_map_view_update_views) !== 1) {
                    para_cb();
                    return;
                }

                const params = [
                    project_id,
                    config.views.view_types.mind_map,
                    parent_types.category,
                    parent_types.subcategory,
                    parent_types.project,
                ];

                db.readQuery(view_query, params, (err, result) => {
                    if (err) {
                        para_cb(err);
                        return;
                    }

                    if (!result.rows.length) {
                        para_cb();
                        return;
                    }

                    viewMod._getViews(
                        userid,
                        { view_id: options.mind_map_view_template, skip_standard: true },
                        (getErr, getResult) => {
                            if (getErr) {
                                para_cb(getErr);
                                return;
                            }

                            if (!getResult.views[0]) {
                                para_cb();
                                return;
                            }

                            const _options = getResult.views[0];
                            delete _options.name;
                            delete _options.template_members;
                            delete _options.template_group_members;
                            delete _options.template_visibility;

                            _options.view_ids = result.rows.map(row => row.view_id);

                            viewMod._getEditQueries(userid, null, _options, (queryErr, queryResult) => {
                                if (queryErr) {
                                    para_cb(queryErr);
                                    return;
                                }

                                queries = queries.concat(queryResult.queries);

                                para_cb();
                            });
                        }
                    );
                });
            },
            timeline(para_cb) {
                if (!options.timeline_view_template || String(options.timeline_view_update_views) !== 1) {
                    para_cb();
                    return;
                }

                const params = [
                    project_id,
                    config.views.view_types.timeline,
                    parent_types.category,
                    parent_types.subcategory,
                    parent_types.project,
                ];

                db.readQuery(view_query, params, (err, result) => {
                    if (err) {
                        para_cb(err);
                        return;
                    }

                    if (!result.rows.length) {
                        para_cb();
                        return;
                    }

                    viewMod._getViews(
                        userid,
                        { view_id: options.timeline_view_template, skip_standard: true },
                        (getErr, getResult) => {
                            if (getErr) {
                                para_cb(getErr);
                                return;
                            }

                            if (!getResult.views[0]) {
                                para_cb();
                                return;
                            }

                            const _options = getResult.views[0];
                            delete _options.name;
                            delete _options.template_members;
                            delete _options.template_group_members;
                            delete _options.template_visibility;

                            _options.view_ids = result.rows.map(row => row.view_id);

                            viewMod._getEditQueries(userid, null, _options, (queryErr, queryResult) => {
                                if (queryErr) {
                                    para_cb(queryErr);
                                    return;
                                }

                                queries = queries.concat(queryResult.queries);

                                para_cb();
                            });
                        }
                    );
                });
            },
            table(para_cb) {
                if (!options.table_view_template || String(options.table_view_update_views) !== 1) {
                    para_cb();
                    return;
                }

                const params = [
                    project_id,
                    config.views.view_types.table,
                    parent_types.category,
                    parent_types.subcategory,
                    parent_types.project,
                ];

                db.readQuery(view_query, params, (err, result) => {
                    if (err) {
                        para_cb(err);
                        return;
                    }

                    if (!result.rows.length) {
                        para_cb();
                        return;
                    }

                    viewMod._getViews(
                        userid,
                        { view_id: options.table_view_template, skip_standard: true },
                        (getErr, getResult) => {
                            if (getErr) {
                                para_cb(getErr);
                                return;
                            }

                            if (!getResult.views[0]) {
                                para_cb();
                                return;
                            }

                            const _options = getResult.views[0];
                            delete _options.name;
                            delete _options.template_members;
                            delete _options.template_group_members;
                            delete _options.template_visibility;

                            _options.view_ids = result.rows.map(row => row.view_id);

                            viewMod._getEditQueries(userid, null, _options, (queryErr, queryResult) => {
                                if (queryErr) {
                                    para_cb(queryErr);
                                    return;
                                }

                                queries = queries.concat(queryResult.queries);

                                para_cb();
                            });
                        }
                    );
                });
            },
            workload(para_cb) {
                if (!options.workload_view_template || String(options.workload_view_update_views) !== 1) {
                    para_cb();
                    return;
                }

                const params = [
                    project_id,
                    config.views.view_types.workload,
                    parent_types.category,
                    parent_types.subcategory,
                    parent_types.project,
                ];

                db.readQuery(view_query, params, (err, result) => {
                    if (err) {
                        para_cb(err);
                        return;
                    }

                    if (!result.rows.length) {
                        para_cb();
                        return;
                    }

                    viewMod._getViews(
                        userid,
                        { view_id: options.workload_view_template, skip_standard: true },
                        (getErr, getResult) => {
                            if (getErr) {
                                para_cb(getErr);
                                return;
                            }

                            if (!getResult.views[0]) {
                                para_cb();
                                return;
                            }

                            const _options = getResult.views[0];
                            delete _options.name;
                            delete _options.template_members;
                            delete _options.template_group_members;
                            delete _options.template_visibility;

                            _options.view_ids = result.rows.map(row => row.view_id);

                            viewMod._getEditQueries(userid, null, _options, (queryErr, queryResult) => {
                                if (queryErr) {
                                    para_cb(queryErr);
                                    return;
                                }

                                queries = queries.concat(queryResult.queries);

                                para_cb();
                            });
                        }
                    );
                });
            },
            map(para_cb) {
                if (!options.map_view_template || options.map_view_update_views !== 1) {
                    para_cb();
                    return;
                }

                const params = [
                    project_id,
                    config.views.view_types.map,
                    parent_types.category,
                    parent_types.subcategory,
                    parent_types.project,
                ];

                db.readQuery(view_query, params, (err, result) => {
                    if (err) {
                        para_cb(err);
                        return;
                    }

                    if (result.rows.length <= 0) {
                        para_cb();
                        return;
                    }

                    viewMod._getViews(
                        userid,
                        { view_id: options.map_view_template, skip_standard: true },
                        (getErr, getResult) => {
                            if (getErr) {
                                para_cb(getErr);
                                return;
                            }

                            if (!getResult.views[0]) {
                                para_cb();
                                return;
                            }

                            const _options = getResult.views[0];
                            delete _options.name;
                            delete _options.template_members;
                            delete _options.template_group_members;
                            delete _options.template_visibility;

                            _options.view_ids = result.rows.map(row => row.view_id);

                            viewMod._getEditQueries(userid, null, _options, (queryErr, queryResult) => {
                                if (queryErr) {
                                    para_cb(queryErr);
                                    return;
                                }

                                queries = queries.concat(queryResult.queries);

                                para_cb();
                            });
                        }
                    );
                });
            },
        },
        err => {
            cb(err, { queries });
        }
    );
}

function _editProject(userid, project_id, options, cb) {
    let old_avatar_key = null;
    const userRows = [];
    const useridsToAdd = [];
    const emailsToAdd = [];
    let new_owner = null;
    let check_owner = false;
    let was_prefix_edited = false;
    let team_id;
    let throw_multi_owner_error = false;
    let throw_id_error = false;
    const now = new Date().getTime();
    let queries = [];
    let admin_can_manage;
    const changes = [];

    const versionUpdates = [];

    const {
        assignee,
        assignees,
        group_assignees,
        due_date,
        due_date_time,
        start_date,
        start_date_time,
        status_name,
        priority,
        name,
        avatar,
        keep_creator,
        ydoc,
        skip_content_broadcast,
    } = options;
    const propsToCheckIfUpdated = {
        assignee,
        due_date,
        due_date_time,
        start_date,
        start_date_time,
        status_name,
        priority,
        name,
        avatar,
    };

    if (options.admin_can_manage !== undefined) {
        admin_can_manage = modelHelpers.toBoolean(options.admin_can_manage);
        options.admin_can_manage = admin_can_manage;
    }

    if (options.new_owner) {
        new_owner = options.new_owner;
    }

    if (options.project_prefix) {
        options.project_prefix = options.project_prefix.toUpperCase();
    }

    if (options.project_prefix === '') {
        options.project_prefix = null;
    }

    if (options.project_prefix === null) {
        options.custom_task_ids_start = null;
        options.custom_task_ids_display = null;
    }

    if (options.name) {
        if (!input.validateProjectName(options.name)) {
            cb(new ProjectError('Space name invalid', 'PROJ_061', 400));
            return;
        }
        changes.push({
            field: 'name',
            after: options.name,
        });
    }

    if (options.avatar_value) {
        changes.push({
            field: 'avatar_value',
            after: options.avatar_value,
        });
    }

    if (options.content) {
        changes.push({
            field: 'content',
        });
    }

    if (options.archived != null) {
        changes.push({
            field: 'archived',
            after: options.archived,
        });
    }

    if (options.project_type) {
        try {
            options.project_type = validateAndCoerceProjectType(options.project_type);
        } catch (err) {
            cb(err);
            return;
        }
    }

    if (options.statuses) {
        logger.info({
            msg: `Edit ${options.in_client ? 'multiple projects' : 'project'}- statuses passed in request body`,
        }); // TODO remove after monitoring

        if (!checkProjectStatusesAreValid(options, cb)) {
            return;
        }

        const first_done_status_idx = _.findIndex(
            options.statuses,
            status_obj => ['done', 'closed'].includes(status_obj.type) || status_obj.status === 'Closed'
        );

        const done_statuses_not_last = options.statuses
            .slice(first_done_status_idx)
            .some(status_obj => !['done', 'closed'].includes(status_obj.type) && status_obj.status !== 'Closed');

        if (done_statuses_not_last) {
            cb(new ProjectError('Done statuses must come last in statuses array', 'PROJ_125', 400));
            return;
        }
    }

    if (options.add && options.add.length > 0) {
        options.add.forEach(userToAdd => {
            userToAdd.role = parseInt(userToAdd.role, 10);
            if (userToAdd.userid) {
                userToAdd.id = userToAdd.userid;
            }
            if (userToAdd.id) {
                useridsToAdd.push(userToAdd.id);
            }
            if (userToAdd.email) {
                emailsToAdd.push(userToAdd.email);
            }
            if (userToAdd.role < 1 || userToAdd.role > 3) {
                userToAdd.role = 3;
            }

            if (userToAdd.role === config.team_roles.owner) {
                check_owner = true;
                if (new_owner != null) {
                    // throw error because there can only be one owner
                    throw_multi_owner_error = true;
                } else {
                    new_owner = userToAdd.id;
                }
            }
        });
    }

    if (options.edit && options.edit.length > 0) {
        options.edit.forEach(userToEdit => {
            userToEdit.role = parseInt(userToEdit.role, 10);

            if (userToEdit.role === config.team_roles.owner) {
                check_owner = true;
                if (new_owner != null) {
                    // throw error because there can only be one owner
                    throw_multi_owner_error = true;
                } else if (!userToEdit.id && !userToEdit.userid) {
                    throw_id_error = true;
                } else {
                    new_owner = userToEdit.id;
                }
            }

            if (userToEdit.role < 1 || userToEdit.role > 3) {
                userToEdit.role = 3;
            }
        });
    }

    if (throw_multi_owner_error) {
        cb(new ProjectError('You can only have one owner of a space', 'PROJ_044', 400));
        return;
    }

    if (throw_id_error) {
        const err_msg = 'You can only make team members the owner of the space, send their user id';
        cb(new ProjectError(err_msg, 'PROJ_045'));
        return;
    }

    let client;
    let done;
    let txClient;
    let ovm;
    let role;
    let defaultNewSpacesToPrivate;
    let currentProject;

    async.series(
        [
            series_cb => {
                // get view update queries
                getViewUpdateQueries(userid, project_id, options, (err, result) => {
                    if (err) {
                        series_cb(err);
                    } else {
                        queries = queries.concat(result.queries);
                        series_cb();
                    }
                });
            },
            async series_cb => {
                try {
                    const result = await db.readQueryAsync('SELECT * FROM task_mgmt.projects WHERE id = $1', [
                        project_id,
                    ]);
                    [currentProject] = result.rows;
                    series_cb();
                } catch (err) {
                    series_cb(err);
                }
            },
            series_cb => {
                async.parallel(
                    {
                        access(para_cb) {
                            if (options.skipAccess || userid === config.get('clickbot_assignee')) {
                                para_cb();
                                return;
                            }

                            const permissions = [];

                            if (
                                (options.add && options.add.length) ||
                                (options.edit && options.edit.length) ||
                                (options.rem && options.rem.length) ||
                                (options.rem_groups && options.rem_groups.length) ||
                                (options.add_groups && options.add_groups.length)
                            ) {
                                permissions.push(can_edit_privacy);
                            }

                            if (
                                (options.features && Object.keys(options.features).length) ||
                                options.multiple_assignees != null ||
                                options.project_prefix != null ||
                                options.name != null ||
                                options.priorities != null ||
                                checkIfProjectSharedAttributesAreUpdated(propsToCheckIfUpdated)
                            ) {
                                permissions.push(can_edit_space_settings);
                            }

                            if (options.private != null) {
                                permissions.push(can_edit_privacy);
                            }

                            if (options.archived != null) {
                                permissions.push(archive, can_create_spaces);
                            }

                            if (options.default_permission_level !== undefined) {
                                permissions.push(can_set_default_permission_level);
                            }

                            if (permissions.length > 0) {
                                access2.checkAccessProject(userid, project_id, { permissions }, para_cb);
                            } else {
                                para_cb();
                            }
                        },
                        /** For box_view_update_views
                         * 1. on paid plan
                         * For admin_can_manage
                         * 1. owner_control_private_spaces enabled on WS
                         * 2. Current user is the space owner
                         */
                        teamCheck(para_cb) {
                            if (
                                options.box_view_update_views == null &&
                                admin_can_manage == null &&
                                (!options.features ||
                                    !options.features.wip_limits ||
                                    !options.features.wip_limits.enabled)
                            ) {
                                para_cb();
                                return;
                            }

                            const query = `
                                SELECT
                                    COALESCE (teams.owner_control_private_spaces, false) AS owner_control_private_spaces,
                                    projects.owner,
                                    COALESCE (projects.admin_can_manage, true) AS old_admin_can_manage
                                FROM task_mgmt.teams
                                INNER JOIN task_mgmt.projects
                                    ON teams.id = projects.team
                                WHERE projects.id = $1`;
                            const params = [project_id];

                            db.readQuery(query, params, (err, result) => {
                                if (err) {
                                    para_cb(new ProjectError(err, 'PROJ_140'));
                                    return;
                                }
                                if (!result.rows.length) {
                                    para_cb(new ProjectError('Not found', 'PROJ_139', 404));
                                    return;
                                }
                                const [{ owner_control_private_spaces, owner, old_admin_can_manage }] = result.rows;

                                if (admin_can_manage === null) {
                                    para_cb();
                                    return;
                                }

                                if (
                                    !owner_control_private_spaces &&
                                    admin_can_manage != null &&
                                    admin_can_manage !== old_admin_can_manage
                                ) {
                                    para_cb(
                                        new ProjectError(
                                            'To let admins manage this space, this feature must be turned on in the Workspace',
                                            'PROJ_143',
                                            403
                                        )
                                    );
                                    return;
                                }

                                if (owner !== userid && options.new_owner !== userid && !old_admin_can_manage) {
                                    para_cb(
                                        new ProjectError(
                                            'To let admins manage this space, you must be the space owner',
                                            'PROJ_144',
                                            403
                                        )
                                    );
                                    return;
                                }
                                para_cb();
                            });
                        },
                        async checkPlanLimits(para_cb) {
                            try {
                                await verifyProjectUsage(project_id, { ...options });
                                para_cb();
                            } catch (planErr) {
                                para_cb(planErr);
                            }
                        },

                        async checkSpaceSettings(para_cb) {
                            try {
                                team_id = await (project_id && getTeamId({ project_ids: [project_id] }));
                                ({ defaultNewSpacesToPrivate } = await getWorkspaceSettings(team_id, {
                                    only: { defaultNewSpacesToPrivate: 1 },
                                }));
                                role = await getUserRole(team_id, userid);
                                const ownerControlPrivateSpaces = await getOwnerControlPrivateSpacesSetting(team_id);

                                const currentProjectPrivacy = currentProject?.private || false;
                                const changingToPublic = currentProjectPrivacy && options.private === false;

                                if (
                                    ((changingToPublic && defaultNewSpacesToPrivate) ||
                                        (options.admin_can_manage === false && ownerControlPrivateSpaces)) &&
                                    role &&
                                    !(role === config.team_roles.owner || role === config.team_roles.admin)
                                ) {
                                    para_cb(
                                        new ProjectError(
                                            "You don't have permission to make this change",
                                            'PROJ_085',
                                            403
                                        )
                                    );
                                    return;
                                }
                                para_cb();
                            } catch (err) {
                                para_cb(err);
                            }
                        },

                        dupe_name(para_cb) {
                            if (!options.name || options.validate_name === false) {
                                para_cb();
                                return;
                            }
                            db.readQuery(
                                'SELECT id FROM task_mgmt.projects WHERE name = $1 AND team = (SELECT team FROM task_mgmt.projects WHERE id = $2) AND template = false AND archived = false AND deleted = false AND id != $2 AND coalesce(projects.importing, false) = false',
                                [options.name, project_id],
                                (err, result) => {
                                    if (err) {
                                        para_cb(new ProjectError(err, 'PROJ_023'));
                                    } else if (result.rows.length > 0) {
                                        para_cb(new ProjectError('Name already in use', 'PROJ_024', 400));
                                    } else {
                                        para_cb();
                                    }
                                }
                            );
                        },

                        old_avatar_key(para_cb) {
                            if (!options.avatar && !options.unset_avatar) {
                                para_cb();
                                return;
                            }

                            db.readQuery(
                                'SELECT id, avatar_key FROM task_mgmt.projects WHERE id = $1',
                                [project_id],
                                (err, result) => {
                                    if (err) {
                                        para_cb(new ProjectError(err, 'PROJ_027'));
                                    } else if (result.rows.length === 0) {
                                        para_cb(new ProjectError('Space not found', 'PROJ_028', 404));
                                    } else {
                                        old_avatar_key = result.rows[0].avatar_key;
                                        para_cb();
                                    }
                                }
                            );
                        },

                        async checkAddedMembersInTeam(para_cb) {
                            const limitedMemberEnabled = await isLimitedMemberEnabledAsync(options.team);
                            db.readQuery(
                                `
                                select
                                    team_members.userid
                                from
                                    task_mgmt.team_members
                                join task_mgmt.users on
                                    users.id = team_members.userid
                                where
                                    (userid = any($1)
                                        or users.email = any($2))
                                    and users.deleted = false
                                    and team_id = (
                                    select
                                        team
                                    from
                                        task_mgmt.projects
                                    where
                                        id = $3)
                                   ${whereNotGuest(limitedMemberEnabled)}
                                `,
                                [useridsToAdd, emailsToAdd, project_id],
                                (err, result) => {
                                    if (err) {
                                        para_cb(new ProjectError(err, 'PROJ_153'));
                                    } else {
                                        userRows.push(...result.rows);
                                        para_cb();
                                    }
                                }
                            );
                        },

                        checkNewOwnerOnTeam(para_cb) {
                            if (!options.new_owner) {
                                para_cb();
                                return;
                            }
                            db.readQuery(
                                'SELECT * FROM task_mgmt.team_members WHERE userid = $1 AND team_id = (SELECT team FROM task_mgmt.projects WHERE id = $2) AND invite = false',
                                [options.new_owner, project_id],
                                (err, result) => {
                                    if (err) {
                                        para_cb(new ProjectError(err, 'PROJ_047'));
                                    } else if (result.rows.length === 0) {
                                        const err_msg = 'New owner must be on team and accepted invite';
                                        para_cb(new ProjectError(err_msg, 'PROJ_048'));
                                    } else {
                                        para_cb();
                                    }
                                }
                            );
                        },

                        templatesAccess(para_cb) {
                            if (options.skip_access) {
                                para_cb();
                                return;
                            }

                            const templates_to_check = [
                                options.list_view_template,
                                options.board_view_template,
                                options.calendar_view_template,
                                options.gantt_view_template,
                                options.map_view_template,
                            ];

                            async.each(
                                templates_to_check,
                                (template_id, each_cb) => {
                                    if (!template_id) {
                                        each_cb();
                                        return;
                                    }

                                    access2.checkAccessView(userid, template_id, {}, each_cb);
                                },
                                para_cb
                            );
                        },

                        prefix_used(para_cb) {
                            if (!options.project_prefix) {
                                para_cb();
                                return;
                            }
                            const query = `
                                        SELECT 1
                                        FROM   task_mgmt.used_project_prefixes
                                        WHERE  team_id =
                                                (
                                                    SELECT team
                                                    FROM   task_mgmt.projects
                                                    WHERE  id = $1)
                                        AND    project_id != $1
                                        AND    project_prefix = $2
                                    `;
                            const params = [project_id, options.project_prefix];

                            db.replicaQuery(query, params, (err, result) => {
                                if (err) {
                                    para_cb(err);
                                } else if (result.rows.length) {
                                    para_cb({
                                        err: 'Custom id prefix has been used in another space',
                                        status: 400,
                                        ECODE: 'PROJ_164',
                                    });
                                } else {
                                    was_prefix_edited = true;
                                    para_cb();
                                }
                            });
                        },

                        async checkGroupsAccess(para_cb) {
                            if (!options.add_groups || !options.add_groups.length) {
                                para_cb();
                                return;
                            }

                            try {
                                await Promise.all([
                                    groupMod.checkGroupsHasAccessToTeam(options.add_groups, 'project', project_id, {}),
                                    groupSharingPaywall.checkGroupSharingPaywall(project_id, 'project'),
                                ]);

                                para_cb();
                            } catch (e) {
                                para_cb(e);
                            }
                        },

                        async sharedEntityQueries(para_cb) {
                            await getSharedEntityAttributeAndAssigneeQueries(
                                queries,
                                project_id,
                                userid,
                                assignee,
                                options
                            );
                            para_cb();
                        },
                    },
                    async (err, result) => {
                        if (err) {
                            if (err.status) {
                                series_cb(err);
                            } else {
                                series_cb(new ProjectError(err, 'PROJ_054', 500, { project_id, userid }));
                            }
                            return;
                        }

                        team_id = result?.access?.team_id || team_id;

                        const { team } = currentProject;
                        const old_owner = currentProject ? currentProject.owner : null;

                        if (currentProject && currentProject.owner === userid) {
                            role = config.team_roles.owner;
                        }

                        if (currentProject.private !== options.private) {
                            changes.push({
                                field: 'private',
                                before: currentProject.private,
                                after: options.private,
                            });
                        }

                        if (options.color && currentProject.color !== options.color) {
                            changes.push({
                                field: 'color',
                                before: currentProject.color,
                                after: options.color,
                            });
                        }

                        if (check_owner && role !== config.team_roles.owner) {
                            const err_msg = 'Must be the owner of the space to change the owner of the space';
                            series_cb(new ProjectError(err_msg, 'PROJ_046', 400));
                            return;
                        }

                        if (options.list_view_settings) {
                            try {
                                options.list_view_settings.type = config.views.view_types.list_required;
                                viewSchema.validateView(options.list_view_settings);
                                if (options.list_view_settings.update_views) {
                                    queries.push(
                                        ...getUpdateViewQuery(
                                            project_id,
                                            config.views.view_types.list,
                                            options.list_view_settings
                                        )
                                    );
                                }
                            } catch (e) {
                                cb(new ProjectError(e, 'PROJ_130'));
                                return;
                            }
                        }
                        if (options.board_view_settings) {
                            try {
                                options.board_view_settings.type = config.views.view_types.board_required;
                                viewSchema.validateView(options.board_view_settings);
                                if (options.board_view_settings.update_views) {
                                    queries.push(
                                        ...getUpdateViewQuery(
                                            project_id,
                                            config.views.view_types.board,
                                            options.board_view_settings
                                        )
                                    );
                                }
                            } catch (e) {
                                cb(new ProjectError(e, 'PROJ_131'));
                                return;
                            }
                        }
                        if (options.calendar_view_settings) {
                            try {
                                options.calendar_view_settings.type = config.views.view_types.calendar_required;
                                viewSchema.validateView(options.calendar_view_settings);
                                if (options.calendar_view_settings.update_views) {
                                    queries.push(
                                        ...getUpdateViewQuery(
                                            project_id,
                                            config.views.view_types.calendar,
                                            options.calendar_view_settings
                                        )
                                    );
                                }
                            } catch (e) {
                                cb(new ProjectError(e, 'PROJ_132'));
                                return;
                            }
                        }
                        if (options.gantt_view_settings) {
                            try {
                                options.gantt_view_settings.type = config.views.view_types.gantt_required;
                                viewSchema.validateView(options.gantt_view_settings);
                                if (options.gantt_view_settings.update_views) {
                                    queries.push(
                                        ...getUpdateViewQuery(
                                            project_id,
                                            config.views.view_types.gantt,
                                            options.gantt_view_settings
                                        )
                                    );
                                }
                            } catch (e) {
                                cb(new ProjectError(e, 'PROJ_133'));
                                return;
                            }
                        }

                        if (options.box_view_settings) {
                            try {
                                options.box_view_settings.type = config.views.view_types.box_required;
                                viewSchema.validateView(options.box_view_settings);
                                if (options.box_view_settings.update_views) {
                                    queries.push(
                                        ...getUpdateViewQuery(
                                            project_id,
                                            config.views.view_types.box,
                                            options.box_view_settings
                                        )
                                    );
                                }
                            } catch (e) {
                                cb(new ProjectError(e, 'PROJ_141'));
                                return;
                            }
                        }

                        if (options.activity_view_settings) {
                            try {
                                options.activity_view_settings.type = config.views.view_types.activity_required;
                                viewSchema.validateView(options.activity_view_settings);
                                if (options.activity_view_settings.update_views) {
                                    queries.push(
                                        ...getUpdateViewQuery(
                                            project_id,
                                            config.views.view_types.activity,
                                            options.activity_view_settings
                                        )
                                    );
                                }
                            } catch (e) {
                                cb(new ProjectError(e, 'PROJ_157'));
                                return;
                            }
                        }

                        if (options.mind_map_view_settings) {
                            try {
                                options.mind_map_view_settings.type = config.views.view_types.mind_map_required;
                                viewSchema.validateView(options.mind_map_view_settings);
                                if (options.mind_map_view_settings.update_views) {
                                    queries.push(
                                        ...getUpdateViewQuery(
                                            project_id,
                                            config.views.view_types.mind_map,
                                            options.mind_map_view_settings
                                        )
                                    );
                                }
                            } catch (e) {
                                cb(new ProjectError(e, 'PROJ_158'));
                                return;
                            }
                        }

                        if (options.timeline_view_settings) {
                            try {
                                options.timeline_view_settings.type = config.views.view_types.timeline_required;
                                viewSchema.validateView(options.timeline_view_settings);
                                if (options.timeline_view_settings.update_views) {
                                    queries.push(
                                        ...getUpdateViewQuery(
                                            project_id,
                                            config.views.view_types.timeline,
                                            options.timeline_view_settings
                                        )
                                    );
                                }
                            } catch (e) {
                                cb(new ProjectError(e, 'PROJ_202'));
                                return;
                            }
                        }

                        if (options.workload_view_settings) {
                            try {
                                options.workload_view_settings.type = config.views.view_types.workload_required;
                                viewSchema.validateView(options.workload_view_settings);
                                if (options.workload_view_settings.update_views) {
                                    queries.push(
                                        ...getUpdateViewQuery(
                                            project_id,
                                            config.views.view_types.workload,
                                            options.workload_view_settings
                                        )
                                    );
                                }
                            } catch (e) {
                                cb(new ProjectError(e, 'PROJ_210'));
                                return;
                            }
                        }

                        if (options.table_view_settings) {
                            try {
                                options.table_view_settings.type = config.views.view_types.table_required;
                                viewSchema.validateView(options.table_view_settings);
                                if (options.table_view_settings.update_views) {
                                    queries.push(
                                        ...getUpdateViewQuery(
                                            project_id,
                                            config.views.view_types.table,
                                            options.table_view_settings
                                        )
                                    );
                                }
                            } catch (e) {
                                cb(new ProjectError(e, 'PROJ_206'));
                                return;
                            }
                        }

                        if (
                            old_owner === userid ||
                            role === config.team_roles.owner ||
                            role === config.team_roles.admin
                        ) {
                            if (options.avatar) {
                                let extension = options.avatar.originalname.split('.');
                                extension = extension[extension.length - 1].toLowerCase();
                                options.avatar_key = `project_avatars/${project_id}_${randomstring.generate(
                                    3
                                )}.${extension}`;
                            }

                            let query = 'UPDATE task_mgmt.projects SET ';
                            const params = [];

                            if (new_owner) {
                                options.owner = new_owner;
                            }

                            const simple_fields = [
                                'name',
                                'content',
                                'archived',
                                'owner',
                                'avatar_key',
                                'multiple_assignees',
                                'color',
                                'assignee',
                                'priority',
                                'due_date',
                                'due_date_time',
                                'slack_channel',
                                'default_preset_view',
                                'preset_views',
                                'list_view_settings',
                                'board_view_settings',
                                'calendar_view_settings',
                                'gantt_view_settings',
                                'box_view_settings',
                                'activity_view_settings',
                                'mind_map_view_settings',
                                'timeline_view_settings',
                                'table_view_settings',
                                'workload_view_settings',
                                'avatar_source',
                                'avatar_value',
                                'list_view_template',
                                'board_view_template',
                                'calendar_view_template',
                                'gantt_view_template',
                                'box_view_template',
                                'activity_view_template',
                                'mind_map_view_template',
                                'timeline_view_template',
                                'workload_view_template',
                                'table_view_template',
                                'map_view_template',
                                'list_view_update_views',
                                'board_view_update_views',
                                'calendar_view_update_views',
                                'gantt_view_update_views',
                                'box_view_update_views',
                                'map_view_update_views',
                                'activity_view_update_views',
                                'mind_map_view_update_views',
                                'timeline_view_update_views',
                                'workload_view_update_views',
                                'table_view_update_views',
                                'sprints',
                                'admin_can_manage',
                                'project_prefix',
                                'custom_task_ids_start_100',
                                'custom_task_ids_start',
                                'custom_task_ids_display',
                                'start_date',
                                'start_date_time',
                                'status',
                                'project_type',
                            ];

                            if (options.preset_views) {
                                const presetViewsQuery = `SELECT preset_views FROM task_mgmt.projects
                                                WHERE id = $1`;
                                const presetViewsParams = [project_id];
                                db.readQuery(presetViewsQuery, presetViewsParams, (error, res) => {
                                    if (error) {
                                        logger.error({
                                            msg: 'Failed to select standard views',
                                            err: error,
                                        });
                                        return;
                                    }

                                    if (res?.rows.length) {
                                        const deletedViewsToSave =
                                            res.rows[0]?.preset_views?.filter(
                                                view => !options.preset_views.includes(view)
                                            ) || [];

                                        const addedViewsToSave =
                                            options.preset_views.filter(
                                                view => !res.rows[0].preset_views?.includes(view)
                                            ) || [];

                                        if (deletedViewsToSave.length || addedViewsToSave.length) {
                                            db.readQuery(
                                                `SELECT view_id, type, standard, parent_id
                                                            FROM task_mgmt.views
                                                              WHERE deleted = FALSE
                                                              AND type = ANY($1)
                                                              AND ((parent_id IN
                                                                    (SELECT DISTINCT projects.id::text
                                                                     FROM task_mgmt.projects
                                                                     WHERE id = $2)
                                                                AND parent_type = ${config.get(
                                                                    'views.parent_types.project'
                                                                )})
                                                                OR (parent_id IN
                                                                    (SELECT DISTINCT categories.id::text
                                                                     FROM task_mgmt.projects
                                                                              JOIN task_mgmt.categories ON categories.project_id = projects.id
                                                                     WHERE projects.id = $2)
                                                                    AND parent_type = ${config.get(
                                                                        'views.parent_types.category'
                                                                    )})
                                                                OR (parent_id IN
                                                                    (SELECT DISTINCT subcategories.id::text
                                                                     FROM task_mgmt.projects
                                                                              JOIN task_mgmt.categories ON categories.project_id = projects.id
                                                                              JOIN task_mgmt.subcategories ON subcategories.category = categories.id
                                                                     WHERE projects.id = $2)
                                                                    AND parent_type = ${config.get(
                                                                        'views.parent_types.subcategory'
                                                                    )}))`,
                                                [[...deletedViewsToSave, ...addedViewsToSave], project_id],
                                                (viewErr, viewResult) => {
                                                    if (viewErr) {
                                                        logger.error({
                                                            msg: 'Failed to read standard views query',
                                                            err: viewErr,
                                                        });
                                                        return;
                                                    }
                                                    if (viewResult.rows?.length) {
                                                        const uniqueViewsByType = _.uniqWith(
                                                            viewResult.rows.filter(({ type, standard }) => {
                                                                if (deletedViewsToSave.includes(type)) {
                                                                    return standard;
                                                                }
                                                                return true;
                                                            }),
                                                            (prev, next) =>
                                                                prev.parent_id === next.parent_id &&
                                                                prev.type === next.type
                                                        );
                                                        const updateViewsQueries = [];

                                                        for (const row of uniqueViewsByType) {
                                                            updateViewsQueries.push({
                                                                query: `UPDATE task_mgmt.views SET standard = ${addedViewsToSave.includes(
                                                                    row.type
                                                                )} WHERE view_id = $1`,
                                                                params: [row.view_id],
                                                            });
                                                        }
                                                        db.batchQueries(updateViewsQueries, {}, updateViewsError => {
                                                            if (updateViewsError) {
                                                                logger.error({
                                                                    msg: 'Failed to update standard view',
                                                                    updateViewsError,
                                                                    ECODE: 'PROJ_152',
                                                                });
                                                            }
                                                        });
                                                    }
                                                    if (deletedViewsToSave.length) {
                                                        const selectViewsParentsQuery = `
                                                        SELECT id, ${config.get('views.parent_types.project')} AS TYPE
                                                        FROM task_mgmt.projects
                                                        WHERE projects.id = $1
                                                        UNION
                                                        SELECT id, ${config.get('views.parent_types.category')} AS TYPE
                                                        FROM task_mgmt.categories
                                                        WHERE project_id = $1
                                                          AND hidden = FALSE
                                                        UNION
                                                        SELECT id, ${config.get(
                                                            'views.parent_types.subcategory'
                                                        )} AS TYPE
                                                        FROM task_mgmt.subcategories
                                                        WHERE category IN
                                                            (SELECT id
                                                             FROM task_mgmt.categories
                                                             WHERE project_id = $1)
                                                        `;
                                                        db.readQuery(
                                                            selectViewsParentsQuery,
                                                            [project_id],
                                                            (viewParentErr, viewParentsRes) => {
                                                                if (viewParentErr) {
                                                                    logger.error({
                                                                        msg: 'Failed to read views parents query',
                                                                        err: viewParentErr,
                                                                    });
                                                                    return;
                                                                }
                                                                const parentsWithoutView = viewParentsRes?.rows.reduce(
                                                                    (acc, parent) => {
                                                                        deletedViewsToSave.forEach(viewType => {
                                                                            const parentCopy = { ...parent };
                                                                            parentCopy.viewType = viewType;
                                                                            const existingView = viewResult.rows.find(
                                                                                row =>
                                                                                    row.parent_id === parentCopy.id &&
                                                                                    row.type === parentCopy.viewType
                                                                            );
                                                                            if (!existingView) {
                                                                                acc.push(parentCopy);
                                                                            }
                                                                        });
                                                                        return acc;
                                                                    },
                                                                    []
                                                                );
                                                                if (parentsWithoutView?.length) {
                                                                    async.map(
                                                                        parentsWithoutView,
                                                                        ({ viewType, id, type: parentType }) => {
                                                                            const viewName = Object.entries(
                                                                                config.views.view_types
                                                                            ).find(([, type]) => type === viewType);
                                                                            viewMod._createView(
                                                                                userid,
                                                                                {
                                                                                    type: viewType,
                                                                                    name: viewName?.length
                                                                                        ? _.startCase(viewName[0])
                                                                                        : '',
                                                                                    standard_view: false,
                                                                                    locked: false,
                                                                                    pinned: false,
                                                                                    user_filter_settings: true,
                                                                                    skip_access: false,
                                                                                    skipAccess: false,
                                                                                    default_skip_access: false,
                                                                                    dontFail: false,
                                                                                    use_gantt: undefined,
                                                                                    skip_privacy_check: false,
                                                                                    visibility:
                                                                                        config.views.visibility.public,
                                                                                    me_view: false,
                                                                                    parent: {
                                                                                        id,
                                                                                        type: parentType,
                                                                                    },
                                                                                },
                                                                                createViewErr => {
                                                                                    if (createViewErr) {
                                                                                        logger.error({
                                                                                            msg: 'Failed to create a new view from standard view',
                                                                                            err: createViewErr,
                                                                                        });
                                                                                    }
                                                                                }
                                                                            );
                                                                        }
                                                                    );
                                                                }
                                                            }
                                                        );
                                                    }
                                                }
                                            );
                                        }
                                    }
                                });
                            }

                            if (options.unset_avatar) {
                                options.avatar_key = null;
                            }

                            if (options.unset_status && !options.status) {
                                if (params.length > 0) {
                                    query += ', ';
                                }
                                params.push(null);
                                query += `status = $${params.length}`;
                            }

                            Object.keys(options).forEach(field => {
                                if (simple_fields.indexOf(field) >= 0) {
                                    if (options[field] === 'none') {
                                        options[field] = null;
                                        if (field === 'due_date') {
                                            options.due_date_time = null;
                                        }
                                    }

                                    if (params.length > 0) {
                                        query += ', ';
                                    }
                                    params.push(options[field]);
                                    query += `${field} = $${params.length}`;
                                }
                            });

                            if (options.project_prefix) {
                                queries.push({
                                    query: `
                                    INSERT INTO task_mgmt.used_project_prefixes
                                    (
                                                team_id,
                                                project_id,
                                                project_prefix
                                    )
                                    (
                                        SELECT
                                                (
                                                        SELECT team
                                                        FROM   task_mgmt.projects
                                                        WHERE  id = $1),
                                                $1,
                                                $2
                                        WHERE  NOT EXISTS
                                                (
                                                        SELECT 1
                                                        FROM   task_mgmt.used_project_prefixes
                                                        WHERE  team_id =
                                                                (
                                                                    SELECT team
                                                                    FROM   task_mgmt.projects
                                                                    WHERE  id = $1)
                                                        AND    project_id = $1
                                                        AND    project_prefix = $2
                                                )
                                    )
                                    `,
                                    params: [project_id, options.project_prefix],
                                });
                            }

                            if (options.default_preset_view) {
                                queries.push({
                                    query: `
                                    DELETE
                                    FROM
                                        task_mgmt.last_standard_view
                                    WHERE
                                        view_type = ANY($2)
                                        AND
                                        (
                                            (
                                                parent_type = $3
                                                AND parent_id IN (
                                                    SELECT categories.id::bigint
                                                    FROM
                                                        task_mgmt.categories
                                                    WHERE
                                                        categories.project_id = $1
                                                )
                                            )
                                            OR
                                            (
                                                parent_type = $4
                                                AND parent_id IN (
                                                    SELECT subcategories.id::bigint
                                                    FROM
                                                        task_mgmt.categories,
                                                        task_mgmt.subcategories
                                                    WHERE
                                                        categories.project_id = $1
                                                        AND categories.id = subcategories.category
                                                )
                                            )
                                            OR
                                            (
                                                parent_type = $5
                                                AND
                                                parent_id = $1::bigint
                                            )
                                        )
                                        AND userid IN (
                                            SELECT userid
                                            FROM
                                                tasK_mgmt.team_members,
                                                task_mgmt.projects
                                            WHERE
                                                projects.id = $1
                                                AND team_members.team_id = projects.team
                                        )
                                    `,
                                    params: [
                                        project_id,
                                        ['list', 'board', 'gantt', 'calendar'],
                                        parent_types.category,
                                        parent_types.subcategory,
                                        parent_types.project,
                                    ],
                                });
                            }

                            const valid_features = Object.keys(config.features);
                            if (options.features) {
                                const { features } = options;

                                Object.keys(features).forEach(key => {
                                    if (valid_features.indexOf(key) >= 0) {
                                        if (params.length > 0) {
                                            query += ', ';
                                        }
                                        params.push(features[key].enabled);
                                        if (key === 'emails') {
                                            key = 'emails_clickapp';
                                        }
                                        query += `${key} = $${params.length}`;
                                    }
                                });

                                if (features.wip_limits) {
                                    queries.push({
                                        query: 'UPDATE task_mgmt.teams SET wip_limit = (SELECT bool_or(wip_limits) OR $1 FROM task_mgmt.projects WHERE id != $2 AND team = (SELECT team FROM task_mgmt.projects WHERE id = $2)) WHERE id = (SELECT team FROM task_mgmt.projects WHERE id = $2)',
                                        params: [features.wip_limits.enabled, project_id],
                                    });
                                    updateRequestForWorkspace({
                                        workspace: team_id,
                                        operationType: OperationType.UPDATE,
                                        results: versionUpdates,
                                    });
                                }

                                if (features.points && features.points.scale) {
                                    if (params.length > 0) {
                                        query += ', ';
                                    }

                                    if (features.points.scale === null || !features.points.scale.length) {
                                        features.points.scale = [];
                                    }
                                    params.push(features.points.scale);
                                    query += `points_scale = $${params.length}`;
                                }
                                if (features.due_dates && features.due_dates.start_date != null) {
                                    if (params.length > 0) {
                                        query += ', ';
                                    }
                                    params.push(features.due_dates.start_date);
                                    query += `due_dates_start_date = $${params.length}`;
                                }
                                if (features.due_dates && features.due_dates.remap_due_dates != null) {
                                    if (params.length > 0) {
                                        query += ', ';
                                    }
                                    params.push(features.due_dates.remap_due_dates);
                                    query += `remap_due_dates = $${params.length}`;
                                }
                                if (features.due_dates && features.due_dates.remap_closed_due_date != null) {
                                    if (params.length > 0) {
                                        query += ', ';
                                    }
                                    params.push(features.due_dates.remap_closed_due_date);
                                    query += `remap_closed_due_date = $${params.length}`;
                                }
                                if (features.time_tracking && features.time_tracking.harvest != null) {
                                    if (params.length > 0) {
                                        query += ', ';
                                    }
                                    params.push(features.time_tracking.harvest);
                                    query += `harvest = $${params.length}`;
                                }

                                if (features.check_unresolved) {
                                    if (features.check_unresolved.subtasks != null) {
                                        if (params.length > 0) {
                                            query += ', ';
                                        }
                                        params.push(features.check_unresolved.subtasks);
                                        query += `check_unresolved_subtasks = $${params.length}`;
                                    }
                                    if (features.check_unresolved.checklists != null) {
                                        if (params.length > 0) {
                                            query += ', ';
                                        }
                                        params.push(features.check_unresolved.checklists);
                                        query += `check_unresolved_checklists = $${params.length}`;
                                    }
                                    if (features.check_unresolved.comments != null) {
                                        if (params.length > 0) {
                                            query += ', ';
                                        }
                                        params.push(features.check_unresolved.comments);
                                        query += `check_unresolved_comments = $${params.length}`;
                                    }
                                }

                                if (features.time_tracking && features.time_tracking.rollup != null) {
                                    if (params.length > 0) {
                                        query += ', ';
                                    }
                                    params.push(features.time_tracking.rollup);
                                    query += `time_tracking_rollup = $${params.length}`;
                                }

                                if (features.time_estimates && features.time_estimates.rollup != null) {
                                    if (params.length > 0) {
                                        query += ', ';
                                    }
                                    params.push(features.time_estimates.rollup);
                                    query += `time_estimate_rollup = $${params.length}`;
                                }

                                if (
                                    features.time_tracking?.default_to_billable != null &&
                                    Object.values(TimeTrackingDefaultToBillable).includes(
                                        features.time_tracking?.default_to_billable
                                    )
                                ) {
                                    const defaultToBillableValue = {
                                        time_tracking: {
                                            default_to_billable: features.time_tracking?.default_to_billable,
                                        },
                                    };

                                    queries.push(
                                        TimeTrackingDefaultToBillableSpaceClickapp.getUpsertQuery(
                                            project_id,
                                            defaultToBillableValue,
                                            team
                                        )
                                    );
                                }
                            }

                            if (options.private != null) {
                                params.push(options.private);
                                if (params.length > 1) {
                                    query += ', ';
                                }
                                query += `private = $${params.length}`;
                            }

                            if (options.content != null) {
                                params.push(Date.now());
                                query += `, date_updated = $${params.length}`;
                            }

                            if (options.private) {
                                if (keep_creator && currentProject.owner) {
                                    queries.push({
                                        query: `
                                                INSERT INTO task_mgmt.project_members(project_id, userid, date_joined, permission_level, workspace_id)
                                                VALUES($1, $2, $3, $4, $5)
                                                ON CONFLICT DO NOTHING
                                            `,
                                        params: [
                                            project_id,
                                            currentProject.owner,
                                            new Date().getTime(),
                                            config.get('hierarchy_permission_levels.can_create_and_edit'),
                                            team,
                                        ],
                                    });
                                } else if (keep_creator === false) {
                                    // when another user is making project private
                                    // and they select keep creator false then we make them
                                    // the new owner of the project
                                    queries.push(
                                        {
                                            query: 'UPDATE task_mgmt.projects SET owner = $1 WHERE id = $2',
                                            params: [userid, project_id],
                                        },
                                        {
                                            query: `DELETE FROM task_mgmt.project_members WHERE userid = $1 AND project_id = $2`,
                                            params: [currentProject.owner, project_id],
                                        }
                                    );
                                }

                                queries.push(
                                    {
                                        query: `
                                            INSERT INTO task_mgmt.project_members(project_id, userid, date_joined, workspace_id) (
                                                SELECT $1, $2, $3, $4 WHERE NOT EXISTS(
                                                    SELECT *
                                                    FROM task_mgmt.project_members
                                                    WHERE project_id = $1
                                                    AND userid = $2
                                                )
                                            ) ON CONFLICT (project_id, userid) DO NOTHING`,
                                        params: [project_id, userid, new Date().getTime(), team],
                                    },
                                    {
                                        query: `
                                            UPDATE task_mgmt.project_members
                                            SET permission_level = $3
                                            WHERE project_id = $1
                                            AND userid = $2`,
                                        params: [project_id, userid, 5],
                                    }
                                );

                                const improvedFollowersQuery = {
                                    query: `
                                    DELETE FROM task_mgmt.followers
                                    USING
                                        task_mgmt.items,
                                        task_mgmt.subcategories,
                                        task_mgmt.categories,
                                        task_mgmt.projects,
                                    	task_mgmt.project_members ,
                                    	task_mgmt.category_members,
                                    	task_mgmt.subcategory_members,
                                    	task_mgmt.task_members
                                    WHERE items.id = followers.task_id
                                        AND subcategories.id = items.subcategory
                                        AND categories.id = subcategories.category
                                        AND projects.id = categories.project_id
                                    	AND project_members.userid = followers.userid AND project_members.project_id = $1
                                    	AND category_members.userid = followers.userid AND category_members.category = categories.id
                                    	AND subcategory_members.userid = followers.userid AND subcategory_members.subcategory = subcategories.id
                                    	AND task_members.userid = followers.userid AND task_members.task_id = items.id
                                        AND projects.id = $1
                                        AND project_members.userid IS NULL
                                        AND category_members.userid IS NULL
                                        AND subcategory_members.userid IS NULL
                                        AND task_members.userid IS NULL
                                    RETURNING followers.task_id AS updated_task_id;
                                        `,
                                    params: [project_id],
                                };

                                if (!currentProject.private) {
                                    logger.info({
                                        msg: 'privacy changed from public to private, deleting followers',
                                        userid,
                                        project_id,
                                    });
                                    queries.push(improvedFollowersQuery);
                                }
                            }

                            if (params.length > 0) {
                                params.push(project_id);
                                query += ` WHERE id = $${params.length}`;

                                queries.push({
                                    query,
                                    params,
                                });
                            }

                            if (options.content) {
                                queries.push(
                                    prepareUpdateYdocQueries(
                                        ContentEntityType.SPACE,
                                        project_id,
                                        currentProject.ydoc,
                                        options.content,
                                        ydoc
                                    )
                                );
                            }

                            if (options.add_groups && options.add_groups.length) {
                                options.add_groups.forEach(grp => {
                                    queries.push({
                                        query: `
                                            INSERT INTO task_mgmt.project_group_members(project_id, group_id, permission_level, date_added, workspace_id)
                                            VALUES ($1, $2, $3, $4, $5) ON CONFLICT DO NOTHING`,
                                        params: [project_id, grp, 5, new Date().getTime(), team],
                                    });
                                });
                            }

                            if (options.rem_groups && options.rem_groups.length) {
                                options.rem_groups.forEach(grp => {
                                    queries.push({
                                        query: `
                                            DELETE FROM task_mgmt.project_group_members WHERE project_id = $1 AND group_id = $2
                                        `,
                                        params: [project_id, grp],
                                    });
                                });
                            }

                            const addDate = new Date().getTime();
                            if (assignees) {
                                if (assignees.add && assignees.add.length > 0) {
                                    let query0 =
                                        'INSERT INTO task_mgmt.project_assignee(project_id, user_id, date_assigned, workspace_id) VALUES ';
                                    let params0 = [];
                                    assignees.add.forEach((user_id, index) => {
                                        const counter = 4 * index;
                                        query0 += `($${counter + 1}, $${counter + 2}, $${counter + 3}, $${
                                            counter + 4
                                        })`;
                                        query0 += index === assignees.add.length - 1 ? ' ' : ', ';
                                        params0.push(project_id, user_id, addDate, team_id);
                                    });

                                    query0 += 'ON CONFLICT (project_id, user_id) DO NOTHING';
                                    queries.push({
                                        query: query0,
                                        params: params0,
                                    });

                                    query0 =
                                        'INSERT INTO task_mgmt.project_members(project_id, userid, date_joined, permission_level, workspace_id) VALUES ';
                                    params0 = [];
                                    assignees.add.forEach((user_id, index) => {
                                        const counter = 5 * index;
                                        query0 += `($${counter + 1}, $${counter + 2}, $${counter + 3}, $${
                                            counter + 4
                                        }, $${counter + 5})`;
                                        query0 += index === assignees.add.length - 1 ? ' ' : ', ';
                                        params0.push(
                                            project_id,
                                            user_id,
                                            addDate,
                                            HierarchyPermissionLevel.CAN_CREATE_AND_EDIT,
                                            team_id
                                        );
                                    });

                                    query0 += 'ON CONFLICT (project_id, userid) DO NOTHING';
                                    queries.push({
                                        query: query0,
                                        params: params0,
                                    });
                                }

                                if (assignees.rem && assignees.rem.length > 0) {
                                    queries.push({
                                        query: 'DELETE FROM task_mgmt.project_assignee WHERE project_id = $1 AND user_id = ANY($2)',
                                        params: [project_id, assignees.rem],
                                    });

                                    queries.push({
                                        query: 'DELETE FROM task_mgmt.project_members WHERE project_id = $1 AND userid = ANY($2)',
                                        params: [project_id, assignees.rem],
                                    });
                                }
                            }

                            if (group_assignees) {
                                if (group_assignees.add && group_assignees.add.length > 0) {
                                    let query1 =
                                        'INSERT INTO task_mgmt.project_group_assignee(project_id, group_id, date_assigned, workspace_id) VALUES ';
                                    let params1 = [];
                                    group_assignees.add.forEach((group_id, index) => {
                                        const counter = 4 * index;
                                        query1 += `($${counter + 1}, $${counter + 2}, $${counter + 3}, $${
                                            counter + 4
                                        })`;
                                        query1 += index === group_assignees.add.length - 1 ? ' ' : ', ';
                                        params1.push(project_id, group_id, addDate, team_id);
                                    });

                                    query1 += 'ON CONFLICT (project_id, group_id) DO NOTHING';

                                    queries.push({
                                        query: query1,
                                        params: params1,
                                    });

                                    query1 =
                                        'INSERT INTO task_mgmt.project_group_members(project_id, group_id, date_added, permission_level, workspace_id) VALUES ';
                                    params1 = [];
                                    group_assignees.add.forEach((group_id, index) => {
                                        const counter = 5 * index;
                                        query1 += `($${counter + 1}, $${counter + 2}, $${counter + 3}, $${
                                            counter + 4
                                        }, $${counter + 5})`;
                                        query1 += index === group_assignees.add.length - 1 ? ' ' : ', ';
                                        params1.push(
                                            project_id,
                                            group_id,
                                            addDate,
                                            HierarchyPermissionLevel.CAN_CREATE_AND_EDIT,
                                            team_id
                                        );
                                    });

                                    query1 += 'ON CONFLICT (project_id, group_id) DO NOTHING';

                                    queries.push({
                                        query: query1,
                                        params: params1,
                                    });
                                }

                                if (group_assignees.rem && group_assignees.rem.length > 0) {
                                    queries.push({
                                        query: 'DELETE FROM task_mgmt.project_group_assignee WHERE project_id = $1 AND group_id = ANY($2)',
                                        params: [project_id, group_assignees.rem],
                                    });

                                    queries.push({
                                        query: 'DELETE FROM task_mgmt.project_group_members WHERE project_id = $1 AND group_id = ANY($2)',
                                        params: [project_id, group_assignees.rem],
                                    });
                                }
                            }

                            if (userRows.length > 0) {
                                userRows.forEach(userToAdd => {
                                    queries.push({
                                        query: `
                                        INSERT INTO task_mgmt.project_members(project_id, userid, date_joined, permission_level, workspace_id)
                                        VALUES ( $1, $2, $3, 5, $4)
                                        ON CONFLICT (project_id, userid) DO UPDATE SET permission_level = 5, date_joined = $3`,
                                        params: [
                                            project_id,
                                            userToAdd.userid || userToAdd.id,
                                            new Date().getTime(),
                                            team,
                                        ],
                                    });
                                });
                            }
                            let throw_cant_remove_owner_error = false;
                            if (options.rem && options.rem.length > 0) {
                                options.rem.forEach(userToRem => {
                                    if (userToRem.id) {
                                        queries.push({
                                            query: 'DELETE FROM task_mgmt.project_members WHERE project_id = $1 AND userid = $2',
                                            params: [project_id, userToRem.id],
                                        });
                                    } else if (userToRem.email) {
                                        queries.push({
                                            query: 'DELETE FROM task_mgmt.project_members WHERE project_id = $1 AND userid = (SELECT id FROM task_mgmt.users WHERE  deleted = false AND email = $2)',
                                            params: [project_id, userToRem.email],
                                        });
                                    } else if (typeof userToRem === 'string') {
                                        queries.push({
                                            query: 'DELETE FROM task_mgmt.project_members WHERE project_id = $1 AND userid = $2',
                                            params: [project_id, userToRem],
                                        });
                                    }
                                    if (userToRem.id === old_owner && !options.new_owner) {
                                        throw_cant_remove_owner_error = true;
                                    }
                                });
                            }

                            if (options.private === true || options.private === 'true') {
                                queries.push({
                                    query: 'DELETE FROM tasK_mgmt.project_members WHERE project_id = $1 AND permission_level IS NULL',
                                    params: [project_id],
                                });
                            }

                            if (throw_cant_remove_owner_error) {
                                series_cb(new ProjectError('Cant remove owner without a new owner', 'PROJ_211', 400));
                                return;
                            }

                            if (new_owner) {
                                queries.push({
                                    query: 'UPDATE task_mgmt.projects SET owner = $1 WHERE id = $2',
                                    params: [new_owner, project_id],
                                });

                                queries.push({
                                    query: `INSERT INTO task_mgmt.project_members (
                                                project_id,
                                                userid,
                                                workspace_id,
                                                permission_level,
                                                date_joined)
                                            VALUES ($1,$2,$3,$4,$5)
                                            ON CONFLICT (project_id, userid) DO
                                            UPDATE SET permission_level = $4`,
                                    params: [
                                        project_id,
                                        new_owner,
                                        team_id,
                                        HierarchyPermissionLevel.CAN_CREATE_AND_EDIT,
                                        new Date().getTime(),
                                    ],
                                });
                            }

                            if (options.statuses) {
                                const status_texts = [];
                                let open_status = 'Open';
                                options.statuses.forEach(status => {
                                    if (status.status.toLowerCase() === 'closed' && !status.type) {
                                        status.status = 'Closed';
                                        status.type = 'closed';
                                    } else if (status.status.toLowerCase() === 'open' && !status.type) {
                                        status.status = 'Open';
                                        status.type = 'open';
                                    } else {
                                        status.type = status.type || 'custom';
                                        status.status = status.status.toLowerCase();
                                    }

                                    status.color = assureStatusColor(status);

                                    if (status.type === 'open') {
                                        open_status = status.status;
                                    }

                                    if (status.status.toLowerCase() === 'open') {
                                        status.status = 'Open';
                                    }
                                    if (status.status.toLowerCase() === 'closed') {
                                        status.status = 'Closed';
                                    }

                                    status_texts.push(status.status);
                                });
                                queries.push({
                                    query: 'DELETE FROM task_mgmt.statuses WHERE project_id = $1 AND status != ANY ($2)',
                                    params: [project_id, status_texts],
                                });
                                options.statuses.forEach((status, idx) => {
                                    const status_id = generateStatusId({
                                        project_id,
                                    });

                                    queries.push({
                                        query: 'INSERT INTO task_mgmt.statuses(id, project_id, status, orderindex, color, type, status_group, workspace_id) (SELECT $6, $1, $2, $3, $4, $5, $7, $8 WHERE NOT EXISTS (SELECT * FROM task_mgmt.statuses WHERE project_id = $1 AND status = $2))',
                                        params: [
                                            project_id,
                                            status.status,
                                            idx,
                                            status.color,
                                            status.type,
                                            status_id,
                                            `proj_${project_id}`,
                                            team_id,
                                        ],
                                    });
                                });
                                queries.push({
                                    query: "WITH rows AS (UPDATE task_mgmt.items SET status = $1::text, status_id = (SELECT statuses.id from task_mgmt.subcategories, task_mgmt.statuses WHERE items.subcategory = subcategories.id AND subcategories.status_group = statuses.status_group AND statuses.status = $1) WHERE status != ALL($2) and subcategory IN (SELECT subcategories.id FROM task_mgmt.subcategories, task_mgmt.categories WHERE subcategories.category = categories.id AND categories.project_id = $3 AND (categories.override_statuses = false OR categories.override_statuses IS NULL) AND (subcategories.override_statuses = false OR subcategories.override_statuses IS NULL)) returning id, status) INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) SELECT id, 'status', $4, status, $1::text, $5, $6 FROM rows RETURNING task_id AS updated_task_id ",
                                    params: [
                                        open_status,
                                        status_texts,
                                        project_id,
                                        now,
                                        userid,
                                        { status_type: 'open' },
                                    ],
                                });

                                queries.push(statusIDCorrection.updateStatusIdsForProjectRequest(project_id));
                            }
                        }

                        if (options.default_category != null) {
                            queries.push(
                                {
                                    query: 'INSERT INTO task_mgmt.default_categories(userid, project_id, default_category, workspace_id) (SELECT $1, $2, $3, (SELECT team from task_mgmt.projects where id = $2) WHERE NOT EXISTS(SELECT * FROM task_mgmt.default_categories WHERE userid = $1 and project_id = $2))',
                                    params: [userid, project_id, options.default_category],
                                },
                                {
                                    query: 'UPDATE task_mgmt.default_categories SET default_category = $3 WHERE userid = $1 AND project_id = $2',
                                    params: [userid, project_id, options.default_category],
                                }
                            );
                        }

                        if (
                            isValidDefaultPermissionLevel(options.default_permission_level) &&
                            isDefaultLocationPermissionsEnabled(team_id)
                        ) {
                            queries.push(
                                upsertObjectAccessInfoQueryObject({
                                    objectId: project_id,
                                    objectType: ObjectType.SPACE,
                                    workspaceId: team_id,
                                    defaultPermissionLevel: options?.default_permission_level,
                                })
                            );
                        }

                        async.series(
                            [
                                series_cb2 => {
                                    // capture the change of avatar
                                    if (options.avatar_key || old_avatar_key) {
                                        const change = {
                                            field: 'avatar_key',
                                        };
                                        if (old_avatar_key) {
                                            change.before = old_avatar_key;
                                        }
                                        if (options.avatar_key) {
                                            change.after = options.avatar_key;
                                        }
                                        changes.push(change);
                                    }

                                    if (options.avatar_key) {
                                        thumb.createProfilePicture(options.avatar.path, thumbErr => {
                                            if (thumbErr) {
                                                series_cb2(new ProjectError(thumbErr, 'PROJ_084'));
                                                return;
                                            }
                                            const s3params = {
                                                Bucket: config.aws.bucket,
                                                Key: options.avatar_key,
                                                Body: fs.createReadStream(options.avatar.path),
                                            };

                                            s3.upload(s3params, s3err => {
                                                fs.unlink(options.avatar.path, () => {});
                                                if (s3err) {
                                                    series_cb2(new ProjectError(s3err, 'PROJ_026'));
                                                } else {
                                                    series_cb2();
                                                }
                                            });
                                        });
                                    } else {
                                        series_cb2();
                                    }
                                },
                                series_cb2 => {
                                    if (options.in_client) {
                                        client = options.in_client;
                                        series_cb2();
                                        return;
                                    }

                                    db.getConn(cb, { label: 'edit project' }, (client_err, _client, _done) => {
                                        if (client_err) {
                                            series_cb2(client_err);
                                            return;
                                        }

                                        client = _client;
                                        done = _done;

                                        series_cb2();
                                    });
                                },
                                async series_cb2 => {
                                    ovm = getObjectVersionManager();
                                    txClient = new TransactionClientImpl(client, ovm);
                                    if (options.in_client) {
                                        series_cb2();
                                        return;
                                    }
                                    try {
                                        await txClient.beginAsync();
                                        series_cb2();
                                    } catch (beginErr) {
                                        db.rollback(client, done);
                                        series_cb2(new ProjectError(beginErr, 'PROJ_017'));
                                    }
                                },
                                async series_cb2 => {
                                    try {
                                        // TODO: fanout when there are auth-related changes.
                                        //       The PUT /projects/:id API is called by the UI at a heavy rate
                                        //       so we can't fanout indiscriminately without causing excessive OVM load
                                        versionUpdates.push(
                                            hierarchyObjectVersionUpdateRequests({
                                                objectType: ObjectType.SPACE,
                                                objectId: Number(project_id),
                                                workspaceId: Number(team_id),
                                                operation: OperationType.UPDATE,
                                                fanout: true,
                                                editOptions: options,
                                                ws_key: options.ws_key,
                                                changes,
                                            })
                                        );

                                        const context = { ws_key: options.ws_key };

                                        if (was_prefix_edited && shouldUpdateTaskVersionsOnCustomPrefixChange()) {
                                            const versionUpdateForSpace = versionUpdates.find(
                                                req =>
                                                    req.object_type === ObjectType.SPACE && req.object_id === project_id
                                            );
                                            if (versionUpdateForSpace) {
                                                if (!versionUpdateForSpace.data) {
                                                    versionUpdateForSpace.data = { context };
                                                }

                                                if (!versionUpdateForSpace.data.relationships) {
                                                    versionUpdateForSpace.data.relationships = [];
                                                }

                                                versionUpdateForSpace.data.relationships.push({
                                                    type: ObjectRelationshipType.FAN_OUT,
                                                    query: objectIdsForTasksInSpace(project_id),
                                                    object_type: ObjectType.TASK,
                                                    object_id: '*',
                                                    workspace_id: Number(team_id),
                                                    operation: OperationType.UPDATE,
                                                });
                                            } else {
                                                versionUpdates.push({
                                                    object_type: ObjectType.SPACE,
                                                    object_id: Number(project_id),
                                                    workspace_id: Number(team_id),
                                                    operation: OperationType.UPDATE,
                                                    data: {
                                                        relationships: [
                                                            {
                                                                type: ObjectRelationshipType.FAN_OUT,
                                                                query: objectIdsForTasksInSpace(project_id),
                                                                object_type: ObjectType.TASK,
                                                                object_id: '*',
                                                                workspace_id: Number(team_id),
                                                                operation: OperationType.UPDATE,
                                                            },
                                                        ],
                                                        context,
                                                        changes,
                                                    },
                                                });
                                            }
                                        }

                                        await batchQueriesSeriesAsyncThenUpdateTasksWithClient(
                                            queries,
                                            txClient,
                                            ovm,
                                            team_id,
                                            false,
                                            versionUpdates,
                                            { context }
                                        );

                                        series_cb2();
                                    } catch (qErr) {
                                        if (!options.in_client) {
                                            db.rollback(client, done);
                                        }
                                        series_cb2(new ProjectError(qErr, 'PROJ_018'));
                                    }
                                },
                                series_cb2 => {
                                    if (Object.keys(options).indexOf('hidden') === -1) {
                                        series_cb2();
                                        return;
                                    }

                                    helpers.hideProjectForUser(
                                        userid,
                                        project_id,
                                        { client, ...options, team },
                                        series_cb2
                                    );
                                },
                                async series_cb2 => {
                                    if (options.in_client) {
                                        series_cb2();
                                        return;
                                    }
                                    try {
                                        await txClient.commitAsync();
                                        done();

                                        series_cb2();
                                    } catch (commitErr) {
                                        if (!options.in_client) {
                                            db.rollback(client, done);
                                        }
                                        series_cb2(new ProjectError(commitErr, 'PROJ_019'));
                                    }
                                },
                                series_cb2 => {
                                    if (options.statuses) {
                                        helpers.setGroupedProjectsStatuses([project_id], {}, series_cb2);
                                    } else {
                                        series_cb2();
                                    }
                                },
                                series_cb2 => {
                                    if (
                                        options.copy_fields &&
                                        options.copy_fields.length &&
                                        options.copy_locations &&
                                        options.copy_locations.length
                                    ) {
                                        genericCopy.copyObjects(
                                            userid,
                                            project_id,
                                            parent_types.project,
                                            options.copy_fields,
                                            options.copy_locations,
                                            {},
                                            series_cb2
                                        );
                                    } else {
                                        series_cb2();
                                    }
                                },
                                series_cb2 => {
                                    if (!options.content || skip_content_broadcast) {
                                        series_cb2();
                                        return;
                                    }

                                    coeditorClientInstance
                                        .postContentUpdate(project_id, EntityType.SPACE, options.content, team)
                                        .catch(() => {});

                                    series_cb2();
                                },
                                async series_cb2 => {
                                    await updateCanonicalChatViews(
                                        userid,
                                        project_id,
                                        config.views.parent_types.project,
                                        name ?? currentProject.name,
                                        team_id,
                                        options.archived
                                    );

                                    series_cb2();
                                },
                            ],
                            series2Err => {
                                if (series2Err) {
                                    series_cb(new ProjectError(series2Err, 'PROJ_086'));
                                } else {
                                    series_cb(null, {});
                                    if (old_avatar_key && old_avatar_key !== options.avatar_key) {
                                        s3.deleteObjects(
                                            {
                                                Bucket: config.aws.bucket,
                                                Delete: {
                                                    Objects: [{ Key: old_avatar_key }],
                                                },
                                            },
                                            s3err => {
                                                if (s3err) {
                                                    logger.error({
                                                        msg: 'Failed to delete old project avatar',
                                                        err: s3err,
                                                    });
                                                }
                                            }
                                        );
                                    }
                                }
                            }
                        );
                    }
                );
            },
        ],
        err => {
            cb(err, {});

            if (!err && !options.in_client && !options.default_category) {
                sqs.sendWSMessage('sendProjectEdited', [userid, project_id, options.ws_key]);
            }

            if (options.project_prefix) {
                customTaskIds.addProjectToProcessingSet(project_id);
                if (shouldUpdateChangeDetectorQueueAfterEditCustomPrefix()) {
                    try {
                        reportTasksInProjectChanged(userid, project_id);
                    } catch (error) {
                        logger.error({ msg: 'Failed to report Project Tasks Changed (CUSTOM TASK IDS)', error });
                    }
                }
            }

            if (!err) {
                webhook.sendWebhookMessage('spaceUpdated', { project_id, team_id });
            }

            if (!err && options.private != null) {
                sqs.sendWSMessage('sendProjectPrivacyChange', [project_id, options.private, options.ws_key]);
                privacy.insertProjectPrivacy('privacy_changed', userid, project_id, { private: options.private });

                if (team_id) {
                    cacheInvalidation.invalidateCachedSubcategoryIds(team_id);
                    cacheInvalidation.invalidateCachedCategoryIds(team_id);
                    cacheInvalidation.invalidateCachedSharedTasksCount(team_id);
                    recent.invalidateCachedRecentTasks(userid, team_id, 'task_mgmt.recently_created');
                }
            }
            if (team_id) {
                timeSpentDatastore.invalidateCachedTimeTrackedTasks(team_id);
            }
            reportProjectChanged(project_id);
        }
    );
    if (options.archived != null) {
        try {
            reportTasksInProjectChanged(userid, project_id);
        } catch (err) {
            logger.error({ msg: 'Failed to report Project Tasks Changed', err });
        }
    }
}
export { _editProject };

function checkIfProjectSharedAttributesAreUpdated(propsToCheckIfUpdated) {
    for (let i = 0; propsToCheckIfUpdated && i < Object.values(propsToCheckIfUpdated).length; i++) {
        if (Object.values(propsToCheckIfUpdated)[i]) {
            return true;
        }
    }
    return false;
}

async function getSharedEntityAttributeAndAssigneeQueries(queries, projectId, userId, assignee, options) {
    try {
        let statusId = null;
        const teamId = await getTeamId({ project_ids: [projectId] }, {});
        if (options.status_name && options.status_name !== 'none') {
            statusId = await getCategoryStatusIdAsync(
                teamId?.toString(),
                projectId,
                entityType,
                options.status_name,
                options.status_type
            );
        }
        // adding the property to options back to avatar is hacky but
        // it gets cleaner in the utility function
        await getSharedEntityQueries(
            userId,
            teamId?.toString(),
            projectId,
            entityType,
            assignee,
            statusId,
            { ...options, avatar: options.locationOverviewAvatar },
            queries
        );
    } catch (err) {
        logger.error({ msg: 'Failed to get shared entity update queries', err });
    }
}

function _setDefaultCategory(userid, project_id, category, options, cb) {
    const queries = [];

    const team_id = options?.team_id;

    if (category != null) {
        queries.push(
            {
                query: 'INSERT INTO task_mgmt.default_categories(userid, project_id, default_category, workspace_id) (SELECT $1, $2, $3, (SELECT team from task_mgmt.projects where id = $2) WHERE NOT EXISTS(SELECT * FROM task_mgmt.default_categories WHERE userid = $1 and project_id = $2))',
                params: [userid, project_id, category],
            },
            {
                query: 'UPDATE task_mgmt.default_categories SET default_category = $3 WHERE userid = $1 AND project_id = $2',
                params: [userid, project_id, category],
            }
        );
    }

    if (queries.length > 0) {
        db.batchQueries(queries, err => {
            if (err) {
                cb(new ProjectError(err, 'PROJ_030'));
            } else {
                cb(null, {});
            }
        });
    } else {
        cb(null, {});
    }
}

function onlyDefaultCategoryChanged(options) {
    // skip options added by middleware
    const simplifiedOptions = _.omit(options, ['skip_access', 'skipAccess', 'default_skip_access', 'dontFail']);
    return Object.keys(simplifiedOptions).length === 1 && simplifiedOptions.default_category;
}

export { onlyDefaultCategoryChanged };

export function editProject(req, resp, next) {
    const userid = req.decoded_token.user;
    const { project_id } = req.params;
    const options = req.body;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    const onlyDefaultCategoryChange = onlyDefaultCategoryChanged(options);

    options.features = options.features || {};
    options.ws_key = ws_key;

    if (options.unset_avatar === 'false') {
        options.unset_avatar = false;
    }

    if (req.files && req.files.avatar) {
        [options.avatar] = req.files.avatar;
    }

    const jsonFields = [
        'repos',
        'rem',
        'rem_groups',
        'edit',
        'rem_repos',
        'add',
        'add_groups',
        'statuses',
        'edit_repos',
        'features',
        'list_view_settings',
        'board_view_settings',
        'calendar_view_settings',
        'gantt_view_settings',
        'box_view_settings',
        'activity_view_settings',
        'mind_map_view_settings',
        'timeline_view_settings',
        'table_view_settings',
        'workload_view_settings',
        'map_view_settings',
        'preset_views',
        'name',
        'team',
        'avatar_source',
        'avatar_value',
        'list_view_template',
        'board_view_template',
        'calendar_view_template',
        'gantt_view_template',
        'box_view_template',
        'activity_view_template',
        'mind_map_view_template',
        'timeline_view_template',
        'workload_view_template',
        'table_view_template',
        'map_view_template',
        'new_owner',
        'copy_fields',
        'copy_locations',
    ];

    if (req.get('Content-Type').includes('multipart')) {
        let errFound = false;

        jsonFields.forEach(field => {
            if (options[field] && Object.prototype.toString.call(options[field]) === '[object String]') {
                try {
                    options[field] = JSON.parse(options[field]);
                } catch (e) {
                    errFound = true;
                }
            }
        });

        if (errFound) {
            next(new ProjectError('JSON field not formatted properly', 'PROJ_033', 400));
            return;
        }
    }

    if (options.multiple_assignees === 'false') {
        options.multiple_assignees = false;
    }

    if (onlyDefaultCategoryChange) {
        logger.debug({ msg: 'Edit project req, default category' });
        _setDefaultCategory(userid, project_id, options.default_category, options, (err, result) => {
            if (err) {
                next(err);
            } else {
                resp.status(200).send(result);
            }
        });
        return;
    }

    _editProject(userid, project_id, options, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    });
}

export function promiseEditProject(userid, project_id, options = {}) {
    return new Promise((res, rej) => {
        _editProject(userid, project_id, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}

function _editProjects(userid, projects, options, cb) {
    async.each(
        projects,
        (project, each_cb) => {
            _editProject(userid, project.id, project, each_cb);
        },
        eachErr => {
            if (eachErr) {
                cb(new ProjectError(eachErr, 'PROJ_088'));
            } else {
                cb(null, {});
                projects.forEach(project => {
                    sqs.sendWSMessage('sendProjectEdited', [userid, project.id, options.ws_key]);
                });
            }
        }
    );
}

export function editProjects(req, resp, next) {
    const userid = req.decoded_token.user;
    const { projects } = req.body;

    const options = {
        ws_key: req.headers.sessionid || req.decoded_token.ws_key,
    };

    _editProjects(userid, projects, options, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    });
}

function _deleteProject(project_id, immediately, options = {}, cb) {
    if (typeof immediately === 'function') {
        cb = immediately;
        immediately = false;
    }

    let date = new Date().getTime();
    if (immediately) {
        date = 0;
    }

    getTeamId({ project_ids: [project_id] }, {})
        .then(team_id => {
            if (!team_id) {
                cb(new ProjectError('Team not found.', 'PROJ_256', 500));
                return;
            }
            recent.invalidateCachedRecentTasks(options.userid, team_id, 'task_mgmt.recently_created');
            timeSpentDatastore.invalidateCachedTimeTrackedTasks(team_id);

            db.getCopyConn(cb, { label: '_deleteProject' }, (err, client, done) => {
                client.query('BEGIN', beginErr => {
                    if (beginErr) {
                        db.rollback(client, done);
                        cb(new ProjectError(beginErr, 'PROJ_012'));
                        return;
                    }

                    async.parallel(
                        {
                            async delProject(para_cb) {
                                try {
                                    const project_id_number = Number(project_id);

                                    const versionUpdates = [
                                        hierarchyObjectVersionUpdateRequests({
                                            objectType: ObjectType.SPACE,
                                            objectId: project_id_number,
                                            workspaceId: Number(team_id),
                                            operation: OperationType.DELETE,
                                            fanout: true,
                                            ws_key: options.ws_key,
                                        }),
                                    ];
                                    versionUpdates.push(
                                        hierarchyTreeVersionUpdateRequests({
                                            objectType: ObjectType.WORKSPACE,
                                            objectId: team_id,
                                            workspaceId: Number(team_id),
                                            operation: OperationType.DELETE,
                                            ws_key: options.ws_key,
                                        })
                                    );

                                    await db2.writeAsync(
                                        `UPDATE task_mgmt.projects
                                            SET deleted = true, date_deleted = $2, deleted_by = $3
                                            WHERE id = $1
                                            RETURNING team`,
                                        [project_id_number, date, options.userid],
                                        versionUpdates
                                    );

                                    para_cb(null, team_id);
                                } catch (e) {
                                    para_cb(new ProjectError(err, 'PROJ_013'));
                                }
                            }, // delProject
                            async convertTemplates(para_cb) {
                                let select;
                                const params = [];
                                const query = `
                                        SELECT id
                                        FROM task_mgmt.categories
                                        WHERE
                                            project_id = $${params.push(project_id)}
                                            AND template = TRUE
                                            AND team_id IS NULL
                                    `;

                                try {
                                    select = await db.promiseReadQuery(query, params);
                                } catch (convert_err) {
                                    para_cb(new ProjectError(convert_err, 'PROJ_160', 500));
                                    return;
                                }
                                if (select.rows && select.rows.length) {
                                    const template_ids = select.rows.map(row => row.id);
                                    try {
                                        await catTemplate._convertV1ToV2(options.userid, template_ids);
                                    } catch (convert_err) {
                                        para_cb(new ProjectError(convert_err, 'PROJ_161', 500));
                                    }
                                }

                                let checklist_select;
                                const checklist_params = [];
                                const checklist_query = `
                                        SELECT id
                                        FROM task_mgmt.checklists
                                        WHERE
                                            project_id = $${checklist_params.push(project_id)}
                                            AND template = TRUE
                                            AND team_id IS NULL
                                    `;

                                try {
                                    checklist_select = await db.promiseReadQuery(checklist_query, checklist_params);
                                } catch (convert_err) {
                                    para_cb(new ProjectError(convert_err, 'PROJ_162', 500));
                                    return;
                                }
                                if (!checklist_select.rows || !checklist_select.rows.length) {
                                    para_cb();
                                    return;
                                }
                                const checklist_template_ids = checklist_select.rows.map(row => row.id);
                                try {
                                    await checklistTemplate._convertV1ToV2(options.userid, checklist_template_ids);
                                } catch (convert_err) {
                                    para_cb(new ProjectError(convert_err, 'PROJ_163', 500));
                                    return;
                                }
                                para_cb();
                            },
                            async moveOrphanedDocs(paraCb) {
                                try {
                                    await unParentChildDocs(team_id, options.userid, project_id, entityType, client);
                                } catch (moveOrphanedDocsErr) {
                                    logger.error({
                                        msg: 'An error occurred while attempting to move orphaned child docs',
                                        ECODE: 'PROJ_401',
                                        team_id,
                                        userid: options.userid,
                                        project_id,
                                        moveOrphanedDocsErr,
                                    });
                                }
                                paraCb();
                            },
                            async deleteChildChatViews(paraCb) {
                                let select;
                                const params = [project_id];
                                const query = `
                                        SELECT view_id
                                        FROM task_mgmt.views
                                        WHERE parent_id = $1
                                        AND deleted IS FALSE
                                        AND parent_type = 4
                                        AND type IN (8, 30, 31)
                                    `;

                                try {
                                    select = await db.promiseReadQuery(query, params);
                                } catch (convert_err) {
                                    paraCb(new ProjectError(convert_err, 'PROJ_162', 500));
                                    return;
                                }

                                if (select.rows && select.rows.length) {
                                    const view_ids = select.rows.map(row => row.view_id);
                                    try {
                                        await deleteViewService.deleteViewsAsync(options.userid, view_ids, {});
                                    } catch (convert_err) {
                                        paraCb(new ProjectError(convert_err, 'PROJ_163', 500));
                                    }
                                }

                                paraCb();
                            },
                        },
                        (deleteErr, result) => {
                            if (deleteErr) {
                                db.rollback(client, done);
                                cb(new ProjectError(deleteErr, 'PROJ_013'));
                            } else {
                                db.readQuery(
                                    'SELECT id FROM task_mgmt.categories WHERE project_id = $1 AND (template = false OR template IS NULL)',
                                    [project_id],
                                    (categoryErr, categoryResult) => {
                                        if (categoryErr) {
                                            db.rollback(client, done);
                                            cb(new ProjectError(categoryErr, 'PROJ_014'));
                                            return;
                                        }

                                        const categories = [];
                                        categoryResult.rows.forEach(row => {
                                            categories.push(row.id);
                                        });

                                        async.each(
                                            categories,
                                            (category_id, each_cb) => {
                                                categoryMod._deleteCategoryWithClient(
                                                    options.userid,
                                                    category_id,
                                                    client,
                                                    done,
                                                    immediately,
                                                    { set_date_deleted: false, delete_all_categories: true },
                                                    each_cb
                                                );
                                            },
                                            eachErr => {
                                                if (eachErr) {
                                                    db.rollback(client, done);
                                                    cb(new ProjectError(eachErr, 'PROJ_015'));
                                                    return;
                                                }

                                                client.query('COMMIT', commitErr => {
                                                    if (commitErr) {
                                                        db.rollback(client, done);
                                                        cb(new ProjectError(commitErr, 'PROJ_016'));
                                                    } else {
                                                        done();

                                                        cb(null, {});
                                                        webhook.sendWebhookMessage('spaceDeleted', {
                                                            team_id,
                                                            project_id,
                                                        });
                                                        sqs.sendWSMessage('sendProjectDeleted', [
                                                            {
                                                                project_id,
                                                                team: team_id,
                                                                ws_key: options.ws_key,
                                                            },
                                                        ]);
                                                    }
                                                });
                                            }
                                        );
                                    }
                                );
                            }
                        }
                    );
                });
            });
        })
        .catch(err1 => {
            logger.error({
                msg: 'Failed to get workspace id',
                project_id,
                err1,
            });

            cb(err1);
        });
}
export { _deleteProject };

function _deleteProjectAsync(project_id, immediately, options) {
    return new Promise((res, rej) => {
        _deleteProject(project_id, immediately, options, (err, result) => {
            if (err) {
                rej(err);
            } else {
                res(result);
            }
        });
    });
}

export { _deleteProjectAsync };

function _deleteProjectCheckingAccess(userid, project_id, new_owner, deleteProject, options, cb) {
    if (!project_id || !isNumeric(project_id)) {
        cb(new ProjectError('Provide a valid projectID', 'PROJ_099', 400));
        return;
    }

    let team_id;
    async.parallel(
        {
            async projectAccess(para_cb) {
                if (options.isTemplate === true) {
                    try {
                        const result = await access2.promiseAccessProjectTemplate(userid, project_id, {
                            permissions: [_delete],
                        });

                        team_id = result?.team;

                        if (!team_id) {
                            para_cb(new ProjectError('Team not found', 'PROJ_255'));
                            return;
                        }

                        para_cb();
                    } catch (error) {
                        para_cb(error);
                    }
                } else {
                    access2.checkAccessProject(userid, project_id, { permissions: [_delete] }, (err, result) => {
                        if (err) {
                            para_cb(err);
                            return;
                        }

                        team_id = result?.team_id;

                        if (!team_id) {
                            para_cb(new ProjectError('Team not found', 'PROJ_256'));
                            return;
                        }

                        para_cb();
                    });
                }
            },

            owner(para_cb) {
                const query = 'SELECT owner FROM task_mgmt.projects WHERE id = $1';
                const params = [project_id];

                db.readQuery(query, params, (err, result) => {
                    if (err) {
                        para_cb(new ProjectError(err, 'PROJ_114'));
                    } else if (!result.rows.length) {
                        para_cb(new ProjectError('Space not found', 'PROJ_115'));
                    } else {
                        para_cb(null, result.rows[0].owner);
                    }
                });
            },

            item_templates(para_cb) {
                const { subcategory, category } = options;
                let query = `
                    SELECT items.id, items.name
                    FROM
                        task_mgmt.items,
                        task_mgmt.subcategories,
                        task_mgmt.categories,
                        task_mgmt.projects
                    WHERE items.template = TRUE
                        AND items.deleted = FALSE
                        AND items.archived = FALSE
                        AND items.subcategory = subcategories.id
                        AND subcategories.template = FALSE
                        AND subcategories.deleted = FALSE
                        AND subcategories.archived = FALSE
                        AND subcategories.category = categories.id
                        AND categories.template = FALSE
                        AND categories.deleted = FALSE
                        AND categories.archived = FALSE
                        AND categories.project_id = projects.id
                        AND projects.id = $1`;
                const params = [project_id];

                if (subcategory) {
                    params.push(subcategory);
                    query += ` AND subcategories.id = $${params.length}`;
                }

                if (category) {
                    params.push(category);
                    query += ` AND categories.id = $${params.length}`;
                }

                db.replicaQuery(query, params, (err, result) => {
                    if (err) {
                        para_cb(new ProjectError(err, 'PROJ_090'));
                    } else {
                        para_cb(null, result.rows);
                    }
                });
            },

            subcat_templates(para_cb) {
                const { category, subcategory } = options;

                if (subcategory) {
                    para_cb(null, []);
                    return;
                }

                let query = `
                    SELECT subcategories.id, subcategories.name
                    FROM task_mgmt.subcategories, task_mgmt.categories, task_mgmt.projects
                    WHERE subcategories.template = TRUE
                        AND subcategories.archived = FALSE
                        AND subcategories.deleted = FALSE
                        AND subcategories.category = categories.id
                        AND categories.template = FALSE
                        AND categories.archived = FALSE
                        AND categories.deleted = FALSE
                        AND categories.project_id = projects.id
                        AND projects.id = $1`;
                const params = [project_id];

                if (category) {
                    params.push(category);
                    query += ` AND categories.id = $${params.length}`;
                }

                db.readQuery(query, params, (err, result) => {
                    if (err) {
                        para_cb(new ProjectError(err, 'PROJ_091'));
                    } else {
                        para_cb(null, result.rows);
                    }
                });
            },

            cat_templates(para_cb) {
                if (options.subcategory || options.category) {
                    para_cb(null, []);
                    return;
                }

                const query = `
                    SELECT categories.id, categories.name
                    FROM task_mgmt.categories, task_mgmt.projects
                    WHERE categories.template = TRUE
                        AND categories.deleted = FALSE
                        AND categories.archived = FALSE
                        AND categories.project_id = projects.id
                        AND projects.id = $1`;
                const params = [project_id];

                db.readQuery(query, params, (err, result) => {
                    if (err) {
                        para_cb(new ProjectError(err, 'PROJ_092'));
                    } else {
                        para_cb(null, result.rows);
                    }
                });
            },
        },
        (err, result) => {
            if (err) {
                cb(new ProjectError(err, 'PROJ_093'));
            } else if (result.owner !== userid && !deleteProject && !new_owner) {
                // NOTE: v1 API won't hit this as it always sets deleteProject to true
                helpers.leaveProject(userid, project_id, cb);
            } else if (new_owner) {
                helpers.changeOwnerAndLeaveProject(userid, project_id, new_owner, team_id, cb);
            } else {
                options.userid = userid;
                _deleteProject(project_id, false, options, cb);
            }
        }
    );
}
export { _deleteProjectCheckingAccess };

function _deleteProjectCheckingAccessAsync(userid, project_id, new_owner, deleteProject, options) {
    return new Promise((res, rej) => {
        _deleteProjectCheckingAccess(userid, project_id, new_owner, deleteProject, options, (err, result) => {
            if (err) {
                rej(err);
            } else {
                res(result);
            }
        });
    });
}

export { _deleteProjectCheckingAccessAsync };

export function deleteOrLeaveProject(req, resp, next) {
    const userid = req.decoded_token.user;
    const { project_id } = req.params;
    const { new_owner } = req.query;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    let deleteProject = req.query.delete_project || false;

    if (deleteProject === 'false') {
        deleteProject = false;
    }

    logger.info({
        msg: 'Delete or leave project request',
        userid,
        project_id,
        new_owner,
        deleteProject,
    });

    // WE ARE FORCING DELETE PROJECT = true BECAUSE WE REMOVED PROJECT INVITES/LEAVING FOR NOW, USERS CANT LEAVE PROJECT CURRENTLY
    deleteProject = true;

    _deleteProjectCheckingAccess(userid, project_id, new_owner, deleteProject, { ws_key }, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    });
}

function _getInaccessibleProjects(userid, team_id, options, cb) {
    async.waterfall(
        [
            wf_cb => {
                access2.checkAccessTeam(
                    userid,
                    team_id,
                    { permissions: [config.permission_constants.can_list_inaccessible_spaces] },
                    err => {
                        if (err) {
                            wf_cb(err);
                            return;
                        }
                        wf_cb();
                    }
                );
            },
            wf_cb => {
                const query = `
                SELECT
                    projects.id,
                    projects.admin_can_manage,
                    projects.name AS project_name,
                    users.id AS userid,
                    users.email AS email,
                    users.username AS username,
                    users.profile_picture_key,
                    users.color AS user_color,
                    count
                FROM tasK_mgmt.projects
                    INNER JOIN task_mgmt.users ON users.id = projects.owner
                    LEFT OUTER JOIN tasK_mgmt.project_members
                        on projects.id = project_members.project_id
                        AND project_members.userid = $1
                    LEFT JOIN LATERAL (
                        SELECT
                            count(*),
                            project_id
                        FROM
                            task_mgmt.project_members,
                            task_mgmt.team_members
                        WHERE
                            project_members.project_id = projects.id
                            AND team_members.team_id = projects.team
                            AND team_members.userid = project_members.userid
                        GROUP BY project_id
                    ) as member_count ON member_count.project_id = projects.id
                WHERE
                    projects.team = $2
                    AND projects.private = true
                    AND projects.deleted = false
                    AND project_members.userid IS NULL`;
                const params = [userid, team_id];

                db.readQuery(query, params, (err, q_result) => {
                    if (err) {
                        wf_cb(err);
                        return;
                    }

                    const projects = q_result.rows.map(row => ({
                        id: row.id,
                        member_count: Number(row.count),
                        admin_can_manage: row.admin_can_manage,
                        project_name: row.project_name,
                        owner: formatUser({
                            id: row.userid,
                            email: row.email,
                            username: row.username,
                            profile_picture_key: row.profile_picture_key,
                            color: row.user_color,
                        }),
                    }));
                    wf_cb(null, projects);
                });
            },
            (projects, wf_cb) => {
                isLimitedMemberEnabledCb(team_id, (lmErr, limitedMemberEnabled) => {
                    if (lmErr) {
                        wf_cb(lmErr);
                        return;
                    }

                    const project_ids = projects.map(project => project.id);
                    _getTeamMembers(project_ids, db, (err, q_results) => {
                        if (err) {
                            wf_cb(err);
                        } else {
                            const { rows: members = [] } = q_results;
                            const members_to_project = _membersProject(false, members, limitedMemberEnabled);
                            projects.forEach(project => {
                                project.members = members_to_project[project.id];
                            });
                            wf_cb(null, projects);
                        }
                    });
                });
            },
        ],
        (err, projects) => {
            if (err) {
                cb(new ProjectError(err, 'PROJ_096'));
                return;
            }
            cb(null, { projects });
        }
    );
}

export function getInaccessibleProjects(req, resp, next) {
    const userid = req.decoded_token.user;
    const { team_id } = req.params;

    _getInaccessibleProjects(userid, team_id, {}, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    });
}

function _recoverOrphanedSpace(userid, team_id, project_id, options, cb) {
    access2.checkAccessTeam(
        userid,
        team_id,
        { permissions: [config.permission_constants.can_recover_inaccessible_spaces] },
        permission_err => {
            if (permission_err) {
                cb(permission_err);
                return;
            }

            const query = `
                SELECT
                    projects.id,
                    projects.team,
                    projects.admin_can_manage,
                    teams.owner_control_private_spaces,
                    count
                FROM tasK_mgmt.projects
                    INNER JOIN task_mgmt.users ON users.id = projects.owner
                    JOIN task_mgmt.teams ON projects.team = teams.id
                    LEFT OUTER JOIN tasK_mgmt.project_members
                        on projects.id = project_members.project_id
                        AND project_members.userid = $1
                    LEFT JOIN LATERAL (
                        SELECT
                            count(*),
                            project_id
                        FROM
                            task_mgmt.project_members,
                            task_mgmt.team_members
                        WHERE
                            project_members.project_id = projects.id
                            AND team_members.team_id = projects.team
                            AND team_members.userid = project_members.userid
                        GROUP BY project_id
                    ) as member_count ON member_count.project_id = projects.id
                WHERE
                    projects.team = $2
                    AND projects.id = $3
                    AND projects.private = true
                    AND project_members.userid IS NULL`;
            const params = [userid, team_id, project_id];

            db.readQuery(query, params, async (q_err, q_result) => {
                if (q_err) {
                    cb(q_err);
                    return;
                }
                const result = q_result.rows[0];

                if (
                    q_result.rows.length === 0 ||
                    result.team !== team_id ||
                    (result.count > 0 && !(result.admin_can_manage && result.owner_control_private_spaces))
                ) {
                    cb({ err: 'Invalid space', status: 400, ECODE: 'PROJ_146' });
                    return;
                }

                const queries = [
                    {
                        query: 'UPDATE task_mgmt.projects SET owner = $1 WHERE id = $2',
                        params: [userid, project_id],
                    },
                    {
                        query: 'INSERT INTO task_mgmt.project_members(project_id, userid, date_joined, permission_level, workspace_id) VALUES($2, $1, $3, 5, $4)',
                        params: [userid, project_id, new Date().getTime(), team_id],
                    },
                    {
                        query: 'INSERT INTO task_mgmt.category_members(category, userid, date_added, permission_level, workspace_id) SELECT categories.id, $1, $3, 5, $4 FROM task_mgmt.categories WHERE categories.project_id = $2 AND categories.private = TRUE AND categories.deleted = FALSE ON CONFLICT (category, userid) DO NOTHING',
                        params: [userid, project_id, new Date().getTime(), team_id],
                    },
                    {
                        query: 'INSERT INTO task_mgmt.subcategory_members(subcategory, userid, date_added, permission_level, workspace_id) SELECT subcategories.id, $1, $3, 5, $4 FROM task_mgmt.subcategories JOIN task_mgmt.categories ON categories.id = subcategories.category WHERE categories.project_id = $2 AND subcategories.private = TRUE AND categories.deleted = FALSE AND subcategories.deleted = FALSE ON CONFLICT (subcategory, userid) DO NOTHING',
                        params: [userid, project_id, new Date().getTime(), team_id],
                    },
                    {
                        query: 'INSERT INTO task_mgmt.task_members(task_id, userid, date_added, permission_level, workspace_id) SELECT items.id, $1, $3, 5, $4 FROM task_mgmt.items JOIN task_mgmt.subcategories ON subcategories.id = items.subcategory JOIN task_mgmt.categories ON categories.id = subcategories.category WHERE categories.project_id = $2 AND items.private = TRUE AND categories.deleted = FALSE AND subcategories.deleted = FALSE AND items.deleted = FALSE ON CONFLICT (task_id, userid) DO NOTHING',
                        params: [userid, project_id, new Date().getTime(), team_id],
                    },
                ];

                const versionUpdates = [
                    hierarchyObjectVersionUpdateRequests({
                        objectType: ObjectType.SPACE,
                        objectId: Number(project_id),
                        workspaceId: Number(team_id),
                        operation: OperationType.UPDATE,
                        fanout: true,
                    }),
                ];
                try {
                    await db2.batchQueriesSeriesAsync(queries, versionUpdates);
                    cb(null, {});
                } catch (batch_err) {
                    cb(batch_err);
                }
            });
        }
    );
}

export function recoverOrphanedSpace(req, resp, next) {
    const userid = req.decoded_token.user;
    const { team_id, project_id } = req.params;

    _recoverOrphanedSpace(userid, team_id, project_id, {}, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    });
}

function checkProjectStatusesAreValid(project, cb) {
    if (!statusLengthLimit().validate?.project) {
        return true;
    }

    const invalidStatus = project.statuses.find(({ status }) => !input.validateStatus(status));

    if (invalidStatus) {
        cb(new ProjectError('Status is invalid', StatusErrors.StatusIsInvalid, 400, { invalidStatus }).logError());
        return false;
    }

    return true;
}
