/* eslint-disable no-case-declarations */

import cluster from 'cluster';

import config from 'config';
import os from 'os';
import uuid from 'node-uuid';
import semver from 'semver';
import _ from 'lodash';
import axios from 'axios';
import https from 'https';
import { consumeWebsocketMessagesForHost } from '@clickup-legacy/utils/websocketRedisDistribution';

import {
    websocketMessageDistributionConfig,
    shouldUseExtendedWebsocketV2Tracing,
    dedupeWebsocketMessageSubscriptions,
    shouldSkipWebsocketMessagesForFrontendBuildVersion,
    shouldSkipWebsocketV1Message,
    shouldTransformOvmEventToWebsocketV2Message,
    websocketPayloadBatchSizeConfig,
} from '@clickup/websocket/split-treatments';

import { getEnvironmentChecks } from '@clickup-legacy/utils/environmentWrapper';
import { WebsocketDistributorMetricNames } from '@clickup/websocket/message-util';
import { isWorkspaceAssignedToCurrentShard } from '@clickup-legacy/utils/workspaceId/checks';
import { ShardRoutingErrorCodes } from '@clickup-legacy/models/sharding/interfaces/errorCodes.interface';
import { BroadcastStrategy } from '@clickup/websocket/domain';
import { shardBroadcastService as broadcastService } from '@clickup/data-platform/sharding/routing/broadcast';
import { isWorkspaceFrozen } from '@clickup/data-platform/sharding/is-workspace-frozen';

import {
    prepareDeleteWebsocketSubsToProjectQuery,
    prepareDeleteWebsocketSubsToTaskQuery,
    prepareDeleteWebsocketSubsToUserQuery,
} from '@clickup/websocket/subscriptions';

import { TEXT_MAP } from 'dd-trace/ext/formats';
import ddMagicTags from 'dd-trace/ext/tags';
import {
    IncomingMessagesConfig,
    mapWebsocketClientMessageToIncomingMessage,
} from '@clickup/websocket/incoming-message-helpers';
import { WebsocketIncomingMessageProducer } from '@clickup/websocket/incoming-message-producer';
import { KafkaProducerImpl } from '@clickup/shared/utils-kafka/client';
import { initContext, AsyncStorage } from '@clickup/shared/utils-async-storage';
import { getLogger } from '@clickup/shared/utils-logging';
import { setWorkspaceIdForDbAffinity } from '@clickup/data-platform/context-db';
import { _getUsers, _promiseGetUsers } from './user/userProvider';
import { configureHostRegistrationListener } from './websockets/hostRegistrationListener';
import { metricsClient } from '../metrics/metricsClient';
import { MetricNames } from './websockets/metricNames';
import { ClickUpError } from '../utils/errors';
import { ClickUpTracer } from '../utils/tracer';
import * as async from '../utils/asyncHelper';
import * as checklistMod from './checklist'; // eslint-disable-line import/no-cycle
import * as projectMod from './project/CRUD';
import { _getItem } from './task/CRUD/getTask'; // eslint-disable-line import/no-cycle
import { _getItems } from './task/CRUD/getTask/get-items';
import * as categoryMod from './category/CRUD';
import * as subcatMod from './subcategory/CRUD'; // eslint-disable-line import/no-cycle
import { _getHistoryItems } from './history/get-history-item.service';
import * as commentSearch from './comment/commentSearch';
import * as viewMod from './views/views'; // eslint-disable-line import/no-cycle
import * as timeSpentMod from './timeSpent'; // eslint-disable-line import/no-cycle
import * as pageHelpers from './views/pages/services/pageHelpers';
import { activityAuroraWriterParams } from './integrations/split/squadTreatments/workAnalyticsTreatments';
import { BlockedErrorCodes, GoalErrorCodes } from '../utils/errors/constants';
import * as access from '../utils/access2';
import * as cf_sign from '../utils/cf_sign';
import * as db from '../utils/db';
import { GlobalTableMigrationBatchId } from '../utils/pg/interfaces/GlobalDatabaseByBatchConfig';
import { wsJwtCheck } from '../utils/auth/jwt/jwtCheckMiddlewares';
import * as sqs from '../utils/v1-ws-message-sending'; // eslint-disable-line import/no-cycle
import * as userInitials from '../utils/userInitials';
import * as notifMod from './notifications/notification';
import { amplitudeRequest } from './integrations/amplitude';
import { toBoolean } from './helpers';
import { getMessageSubscriptions, MESSAGE_KEYS } from './websockets/services/getSubscriptionsService';
import { getKeyResultTeamId } from './goals/datastore/keyResultsDatastore';
import { insertWSSubscription } from './websockets/services/websocketSubsService';

import {
    handleCategorySubscription,
    handleProjectSubscription,
    handleSubcategorySubscription,
    handleTaskSubscription,
    sendNewBuildV2,
    sendUserOnline as sendUserOnlineWS,
    setCommenting,
    setCommentingPage,
    setCommentingView,
    setViewingPage,
    setViewingView,
    sendTakeoverPageReq,
    sendTakeoverPageAckReq,
    handleViewSubscriptions,
    handleSubscribe,
    unsubscribeViewsWS,
} from './websockets/services/wsMessageActionService';

import * as environment from '../utils/environment';
import { safeGetRequiredConfigString } from '../utils/configUtil';
import { SHARDING_ENABLED } from '../config/domains/shardingConfig';

import {
    logWebsocketMessageDispatchError,
    logWebsocketMessageProcessingError,
    shouldSendMessage,
} from './websockets/utils';

import { shouldEnforceShardRouting } from '../utils/workspaceId/enforceShardRouting';
import { createMessagesAndSend } from './websockets/services/sendMessagesService';
import { promiseWebsocketsReadQuery, promiseWebsocketsWriteQuery } from './websockets/datastores/db/websocketsDbHelper';
import { processShutdownController } from '../utils/shutdownController';
import { MetricNamesV2 } from './websockets/metricNamesV2';
import * as websocketRedis from '../utils/websocketRedisDistribution';
import { closeTaskActivities } from './websockets/datastores/subscriptionMonitorDatastore';
import { closeUserActivitiesIfAny, insertTaskActivity } from './websockets/datastores/taskActivityDatastore';

const debugLogger = getLogger('wssHelper');

const isLocalEnv = getEnvironmentChecks(config.get('env')).isLocal;

// websocket status codes 4000-4999 are reserved for use by the application
// https://datatracker.ietf.org/doc/html/rfc6455#section-7.4.2
const WSS_STATUS_CODE_WORKSPACE_NOT_FOUND = 4000;
const WSS_STATUS_CODE_WORKSPACE_FROZEN = 4001;
const WSS_STATUS_CODE_ALREADY_AUTHENTICATED = 4002;
const WSS_STATUS_CODE_FAILED_AUTH = 4003;

const tracer = new ClickUpTracer();

const _REGION_TO_CONFIG_LB_URL = {
    australia: 0, // dualstack.cu-prod-au-alb-1301700681.ap-southeast-2.elb.amazonaws.com
    north_asia: 1, // dualstack.cu-prod-kr-alb-1277852555.ap-northeast-2.elb.amazonaws.com
    us_west: 2, // dualstack.cu-prod-or-alb-*********.us-west-2.elb.amazonaws.com
    south_asia: 3, // dualstack.cu-prod-in-alb-*********.ap-south-1.elb.amazonaws.com
    us_east: 4, // dualstack.cu-prod-va-alb-1628194480.us-east-1.elb.amazonaws.com
    eu_west: 5, // dualstack.cu-prod-ie-alb-90825617.eu-west-1.elb.amazonaws.com
};

const _DEV_REGION_TO_CONFIG_LB_URL = {
    us_west: 0, // dualstack.clickup-staging-or-alb-*********.us-west-2.elb.amazonaws.com
    eu_central: 2, // dualstack.clickup-staging-eu-alb-1999216910.eu-central-1.elb.amazonaws.com
};

const host = os.hostname() + (environment.isLocal ? ':9004' : ':80');

const {
    subcategory: SUBCATEGORY_TYPE,
    category: CATEGORY_TYPE,
    project: PROJECT_TYPE,
    view: VIEW_TYPE,
    comment: COMMENT_TYPE,
    task: TASK_TYPE,
    doc: DOC_TYPE,
} = config.comments.types;

/** @type {Record<string,Array<import("ws")>>} */
const websockets = {};
const ws_to_update = [];

const WSError = ClickUpError.makeNamedError('wss');
const logWSError = ClickUpError.getIsolatedErrorHandler('wss');
const logger = ClickUpError.getBasicLogger('wss');

const AUTHORIZATION_HEADER = safeGetRequiredConfigString('wss.authorization_header');

function _getTasksProjectIds(task_ids, cb) {
    const query = `
        SELECT categories.project_id, items.id
        FROM task_mgmt.items,
             task_mgmt.subcategories,
             task_mgmt.categories
        WHERE items.subcategory = subcategories.id
          AND subcategories.category = categories.id
          AND items.id = ANY ($1)`;

    db.replicaQuery(query, [task_ids], (err, result) => {
        if (err) {
            cb(logWSError(err, 'WSS_044'));
        } else {
            const project_ids_map = _.chain(result.rows)
                .mapKeys(row => row.id)
                .mapValues(row => row.project_id)
                .value();
            cb(null, project_ids_map);
        }
    });
}

function _getTaskProjectId(task_id, cb) {
    _getTasksProjectIds([task_id], (err, result) => {
        if (err) {
            cb(err);
        } else if (!result[task_id]) {
            logger.error({ msg: 'Unable to resolve project for task', taskId: task_id });
            cb(logWSError('Not Found', 'WSS_045', 404));
        } else {
            cb(null, result[task_id]);
        }
    });
}

function _promiseGetTaskProjectId(task_id) {
    return new Promise((res, rej) => {
        _getTaskProjectId(task_id, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}

async function getTaskParentIdForThreadedComment(parentCommentId) {
    const query = `SELECT parent
                   FROM task_mgmt.comments
                   WHERE id = $1
                     AND type = ${TASK_TYPE}`;
    const params = [parentCommentId];
    const [row] = (await db.promiseReplicaQuery(query, params)).rows;

    return row?.parent;
}

function _getTaskTeamId(task_id, cb) {
    const query = `
        SELECT projects.team
        FROM task_mgmt.items,
             task_mgmt.subcategories,
             task_mgmt.categories,
             task_mgmt.projects
        WHERE items.id = $1
          AND items.subcategory = subcategories.id
          AND subcategories.category = categories.id
          AND categories.project_id = projects.id`;

    db.replicaQuery(query, [task_id], (err, result) => {
        if (err) {
            cb(logWSError(err, 'WSS_075'));
        } else if (!result.rows.length) {
            cb(logWSError('Not Found', 'WSS_076', 404));
        } else {
            cb(null, result.rows[0].team);
        }
    });
}

function storeSession(userid, ws) {
    if (config.env === 'prod') {
        amplitudeRequest(userid, 'open session', ws?.team_id ? { workspaceID: ws.team_id } : {});
    }
}

const android_sessions = [];
const ios_sessions = [];
const web_sessions = [];

async function storeMemorySessions(platform) {
    const page_length = 50;

    let sessions;
    let login_col = 'last_login_web';
    if (platform === 'android') {
        sessions = android_sessions.splice(0, page_length);
        login_col = 'last_login_android';
    } else if (platform === 'ios') {
        sessions = ios_sessions.splice(0, page_length);
        login_col = 'last_login_ios';
    } else {
        sessions = web_sessions.splice(0, page_length);
    }

    if (!sessions.length) {
        setTimeout(() => {
            storeMemorySessions(platform);
        }, 5000);
        return;
    }

    const query = `UPDATE task_mgmt.users
                   SET last_session  = $1,
                       session_count = session_count + 1,
                       ${login_col}  = $2
                   WHERE id = ANY ($3)`;
    const params = [new Date().getTime(), new Date().getTime(), sessions];

    try {
        await db.globalWriteQueryAsync(query, params, {
            globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_11,
        });
    } catch (err) {
        logWSError(err, 'WSS_041');
    } finally {
        setTimeout(
            () => {
                storeMemorySessions(platform);
            },
            sessions.length === page_length ? 100 : 5000
        );
    }
}

export { storeMemorySessions };

function startSession(userid, ws, req) {
    // iOS's WebSocket lib does not pass a user-agent header. Might cause false
    // positives, but most desktop browsers pass the correct UA
    let user_agent = null;

    if (ws && ws.upgradeReq && ws.upgradeReq.headers && ws.upgradeReq.headers['user-agent']) {
        user_agent = ws.upgradeReq.headers['user-agent'];
    }
    if (req && req.headers && req.headers['user-agent']) {
        user_agent = req.headers['user-agent'];
    }

    if (user_agent) {
        user_agent = user_agent.toLowerCase();
    }

    if (user_agent === null) {
        // iOS
        ios_sessions.push(userid);
    } else if (user_agent.indexOf('okhttp') >= 0) {
        // Android
        android_sessions.push(userid);
    } else {
        // Web
        web_sessions.push(userid);
    }
    storeSession(userid, ws);
}

async function closeSession(ws, end, start) {
    try {
        // Update the last_session and session_time
        await db.globalWriteQueryAsync(
            'UPDATE task_mgmt.users SET last_session = $2, session_time = session_time + $3 WHERE id = $1',
            [ws.user, end, end - start],
            { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_11 }
        );
    } catch (err) {
        logWSError(err, 'WSS_042');
    }
}

let wsSendQueueCount = 0;
let wsSendQueueBytes = 0;

function detectPlatform(userAgent) {
    const normalizedUA = userAgent.toLowerCase();

    // Check for mobile (dart) user agent
    const isClickupAgent = normalizedUA.includes('clickup');

    if (normalizedUA.includes('dart') && isClickupAgent) {
        return 'mobile';
    }

    // Check for desktop (electron) user agent
    if (normalizedUA.includes('electron') && isClickupAgent) {
        return 'desktop';
    }

    // Default case for web
    return 'web';
}

/**
 *
 * @param context
 * @param {import("ws")} ws
 * @param message
 * @param {import("@clickup/websocket/message-util").WebsocketDistributorContext} distributor_context
 * @param cb
 */
function _instrumentedWsSend(context, ws, message, distributor_context, cb) {
    const startWsSendEpochMs = Date.now();
    // if distributor_context is not provided, swap with cb
    if (typeof distributor_context === 'function') {
        cb = distributor_context;
        distributor_context = null;
    }
    const { method } = message;

    let tags;

    const isV2WSMessage = isWebsocketV2Message(message);

    if (context?.parentSpanContext) {
        tracer.getActiveSpan()?.addLink(context.parentSpanContext);
    }

    if (isV2WSMessage) {
        tags = {
            message_name: message.event.name,
            client_platform: ws?.userAgent ? detectPlatform(ws.userAgent) : 'unknown',
        };
    } else {
        tags = { method, func: context?.func ?? 'N/A' };
    }

    const ddTags = {
        userAgent: ws.userAgent,
        team: ws.team_id,
        userid: ws.user,
        ...tags,
    };
    tracer.addTagsToActiveScope({ ws_context: ddTags });

    // check to make sure that the ws is still open before continuing.
    if (ws.readyState !== ws.OPEN) {
        if (isV2WSMessage) {
            metricsClient.increment(MetricNamesV2.WEBSOCKET_V2_MESSAGE_SENT, 1);
        } else {
            metricsClient.increment(MetricNames.WEBSOCKET_MESSAGE_SENT, 1);
        }
        metricsClient.distribution(MetricNames.WEBSOCKET_MESSAGE_SENT_SKIPPED_DISTRIBUTION, 1, {
            reason: 'not_open',
            ...tags,
            isAlive: ws.isAlive?.toString() ?? 'unknown',
        });
        cb();
        return;
    }

    if (ws.messageSendingBlocked) {
        if (isV2WSMessage) {
            metricsClient.increment(MetricNamesV2.WEBSOCKET_V2_MESSAGE_SENT, 1, tags);
        } else {
            metricsClient.increment(MetricNames.WEBSOCKET_MESSAGE_SENT, 1, tags);
        }
        metricsClient.distribution(MetricNames.WEBSOCKET_MESSAGE_SENT_SKIPPED_DISTRIBUTION, 1, {
            reason: 'blocked_client_version',
            client_version: ws.buildVersion ?? 'unknown',
            ...tags,
        });
        cb();
        return;
    }

    wsSendQueueCount++;
    metricsClient.gauge(MetricNames.WEBSOCKET_MESSAGE_SEND_QUEUE, wsSendQueueCount);
    const messageStr = JSON.stringify(message);
    const stringifiedEpochMs = Date.now();

    const messageBytes = Buffer.byteLength(messageStr);
    wsSendQueueBytes += messageBytes;

    metricsClient.gauge(MetricNames.WEBSOCKET_MESSAGE_SEND_QUEUE_BYTES, wsSendQueueBytes);

    ws.send(messageStr, err => {
        wsSendQueueCount--;
        wsSendQueueBytes -= messageBytes;
        metricsClient.gauge(MetricNames.WEBSOCKET_MESSAGE_SEND_QUEUE, wsSendQueueCount);
        metricsClient.gauge(MetricNames.WEBSOCKET_MESSAGE_SEND_QUEUE_BYTES, wsSendQueueBytes);
        const wsSendTimeEpochMs = Date.now();
        if (isV2WSMessage) {
            metricsClient.increment(MetricNamesV2.WEBSOCKET_V2_MESSAGE_SENT, 1, tags);

            if (err) {
                logger.error({
                    msg: 'Failed to send websocket message to client',
                    ws_context: {
                        message_version: 'v2',
                        uuid: ws?.uuid,
                        workspace_id: ws?.team_id,
                        user_id: ws?.user,
                        ...tags,
                    },
                    err,
                });
                metricsClient.increment(MetricNamesV2.WEBSOCKET_V2_MESSAGE_SENT_ERROR, 1, tags);
                cb(err);
                return;
            }

            if (context?.websocket_ovm_consumer_context?.ovm_event_publish_time_ms) {
                metricsClient.distribution(
                    MetricNamesV2.WEBSOCKET_V2_MESSAGE_SENT_TIME,
                    wsSendTimeEpochMs - context.websocket_ovm_consumer_context.ovm_event_publish_time_ms,
                    {
                        ...tags,
                        start_position: 'change_notification',
                    }
                );
            } else {
                metricsClient.distribution(
                    MetricNamesV2.WEBSOCKET_V2_MESSAGE_SENT_TIME,
                    wsSendTimeEpochMs - message.event.date_created,
                    {
                        ...tags,
                        start_position: 'db_transaction',
                    }
                );
            }

            if (context?.websocket_ovm_consumer_context?.ovm_consumer_received_epoch_ms) {
                metricsClient.distribution(
                    MetricNamesV2.WEBSOCKET_V2_MESSAGE_E2E_PIPELINE_TIME,
                    wsSendTimeEpochMs - context.websocket_ovm_consumer_context.ovm_consumer_received_epoch_ms,
                    tags
                );
            }

            metricsClient.distribution(MetricNamesV2.WEBSOCKET_V2_MESSAGE_SENT_SIZE, messageBytes, tags);
        } else {
            metricsClient.increment(MetricNames.WEBSOCKET_MESSAGE_SENT, 1, tags);

            if (err) {
                logger.error({
                    msg: 'Failed to send websocket message to client',
                    ws_context: {
                        message_version: 'v1',
                        uuid: ws?.uuid,
                        workspace_id: ws?.team_id,
                        user_id: ws?.user,
                    },
                    err,
                });
                metricsClient.increment(MetricNames.WEBSOCKET_MESSAGE_SENT_ERROR, 1, tags);
                cb(err);
                return;
            }

            if (context?.date_created) {
                metricsClient.distribution(
                    MetricNames.WEBSOCKET_MESSAGE_SENT_TIME,
                    wsSendTimeEpochMs - context.date_created,
                    tags
                );
            }

            metricsClient.distribution(MetricNames.WEBSOCKET_MESSAGE_SENT_SIZE, messageBytes, tags);
        }

        if (distributor_context && !err) {
            const message_version = isV2WSMessage ? 'v2' : 'v1';
            const startToStoredTime = Math.max(
                0,
                distributor_context.stored_in_queue_epoch_ms - distributor_context.send_start_epoch_ms
            );
            const storedToConsumedTime =
                distributor_context.consumed_epoch_ms - distributor_context.stored_in_queue_epoch_ms;
            const startToSentTime = Math.max(wsSendTimeEpochMs - distributor_context.send_start_epoch_ms, 0);
            const consumedToMessageStartSendTime = startWsSendEpochMs - distributor_context.consumed_epoch_ms;

            if (startToStoredTime > 0) {
                metricsClient.distribution(
                    WebsocketDistributorMetricNames.OUTGOING_MESSAGE_PROCESSING_TIME,
                    startToStoredTime,
                    { ...tags, message_version, step: 'start-to-stored' }
                );
            }

            if (storedToConsumedTime > 0) {
                metricsClient.distribution(
                    WebsocketDistributorMetricNames.OUTGOING_MESSAGE_PROCESSING_TIME,
                    storedToConsumedTime,
                    { ...tags, message_version, step: 'stored-to-consumed' }
                );
            }

            if (consumedToMessageStartSendTime > 0) {
                metricsClient.distribution(
                    WebsocketDistributorMetricNames.OUTGOING_MESSAGE_PROCESSING_TIME,
                    consumedToMessageStartSendTime,
                    {
                        ...tags,
                        message_version,
                        step: 'consumed-to-message-start-send',
                    }
                );
            }

            if (startToSentTime > 0) {
                metricsClient.distribution(
                    WebsocketDistributorMetricNames.OUTGOING_MESSAGE_PROCESSING_TIME,
                    startToSentTime,
                    { ...tags, message_version, step: 'start-to-sent' }
                );
            }
        }

        cb(err);
    });
}

const instrumentedWsSend = tracer.wrap('wssHelper.instrumentedWsSend', {}, _instrumentedWsSend);

async function _setTaskActivity(ws_uuid, userid, task_id, viewing, workspaceId) {
    if (activityAuroraWriterParams().stop_task_writes) {
        return;
    }
    if (!userid || !task_id) {
        return;
    }

    let response;
    try {
        response = await promiseWebsocketsReadQuery(
            `
                SELECT ws_uuid
                FROM task_mgmt.ws_subscriptions
                WHERE userid = $1
                  AND task_id = $2
                  AND viewing = true
                  AND ws_uuid != $3
            `,
            [userid, task_id, ws_uuid]
        );
    } catch (err) {
        logWSError(err, 'WSS_095');
        return;
    }
    if (response.rows && response.rows.length) {
        return;
    }

    try {
        if (viewing) {
            // clean up any transient activity states
            if (!activityAuroraWriterParams().stop_closing_multiple_active_tasks) {
                await closeUserActivitiesIfAny(userid);
            }
            await insertTaskActivity({ userid, task_id, workspace_id: workspaceId });
        } else {
            await closeTaskActivities([{ userid, task_id }]);
        }
    } catch (err) {
        logWSError(err, 'WSS_096');
    }
}

async function _setViewing(ws, userid, ws_uuid, ws_key, task_id, viewing, initial) {
    const sendingViewingSet = shouldSendMessage('viewingSet', undefined, 'sendViewingSetOpts');
    const sendingViewingUnset = shouldSendMessage('viewingUnset', undefined, 'sendViewingUnsetOpts');
    const sendingCommentingSet = shouldSendMessage('commentingSet', undefined, 'sendCommentingSet');

    try {
        const result = await access.checkAccessTaskAsync(userid, task_id, { permissions: [] });
        if (!ws.team_id && result.permissions[task_id]) {
            ws.team_id = result.permissions[task_id].team_id;
        }
    } catch (err) {
        return;
    }

    if (ws.task_id && ws.task_id !== task_id) {
        try {
            await _setTaskActivity(ws_uuid, userid, ws.task_id, false, ws.team_id);
        } catch (err) {
            logWSError(err, 'WSS_097');
        }
    }
    try {
        await _setTaskActivity(ws_uuid, userid, task_id, viewing, ws.team_id);
    } catch (err) {
        logWSError(err, 'WSS_098');
    }

    // if none of the 3 messages are sent to the clients in the end, we can stop
    // here
    if (!sendingViewingSet && !sendingViewingUnset && !sendingCommentingSet) {
        return;
    }

    try {
        if (sendingViewingSet || sendingViewingUnset) {
            await promiseWebsocketsWriteQuery(
                'UPDATE task_mgmt.ws_subscriptions SET viewing = $4, task_id = $1 WHERE userid = $2 AND ws_uuid = $3',
                [task_id, userid, ws_uuid, viewing]
            );
        }
    } catch (err) {
        logWSError(err, 'WSS_047', 500, { task_id, userid, ws_uuid });
        return;
    }

    if (ws.task_id && ws.task_id !== task_id) {
        if (sendingViewingUnset) {
            sqs.sendWSMessage('sendViewingUnsetOpts', [
                { ws_uuid, ws_key, userid, task_id: ws.task_id, team_id: ws.team_id },
            ]);
        }
        if (sendingCommentingSet) {
            sqs.sendWSMessage('sendCommentingSet', [ws_uuid, ws_key, userid, ws.task_id, false]);
        }
    }

    // send the set viewing to all relevant hosts
    if (viewing && sendingViewingSet) {
        sqs.sendWSMessage('sendViewingSetOpts', [{ ws_uuid, ws_key, userid, task_id, team_id: ws.team_id }]);
        if (initial) {
            sendExistingTaskViewers(ws, userid, task_id);
        }
    } else if (!viewing && sendingViewingUnset) {
        sqs.sendWSMessage('sendViewingUnsetOpts', [{ ws_uuid, ws_key, userid, task_id, team_id: ws.team_id }]);
    }
}

async function sendExistingTaskViewers(ws, target_userid, task_id) {
    try {
        const { rows } = await promiseWebsocketsReadQuery(
            `SELECT DISTINCT userid::int, ws_uuid, ws_key
             FROM task_mgmt.ws_subscriptions
             WHERE userid != $1
               AND task_id = $2
               AND viewing = true`,
            [target_userid, task_id]
        );
        rows.forEach(({ userid: row_userid, ws_uuid, ws_key }) => {
            sqs.sendWSMessage('sendViewingSetOpts', [
                { ws_uuid, ws_key, userid: row_userid, task_id, team_id: ws.team_id },
            ]);
        });
    } catch (err) {
        logWSError(err, 'WSS_048', 500, { target_userid, task_id });
    }
}

async function insertWSConnection(ws, msg) {
    let version_two = false;

    if (msg.buildVersion) {
        version_two = semver.gte(semver.coerce(msg.buildVersion).version, '2.64.0');
    }

    const query = `
        INSERT INTO task_mgmt.ws_subscriptions(userid, ws_uuid, ws_key, host, ip, team, date_updated, version_two)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (userid, ws_uuid)
            DO UPDATE SET host         = $4,
                          ip           = $5,
                          ws_key       = $3,
                          team         = $6,
                          date_updated = $7,
                          version_two  = $8`;
    const params = [
        ws.user,
        ws.uuid,
        ws.ws_key,
        config.lb_url,
        host,
        ws.team_id || msg.teamId,
        new Date().getTime(),
        version_two,
    ];

    try {
        await promiseWebsocketsWriteQuery(query, params);
    } catch (err) {
        logWSError(err, 'WSS_049', 400);
    }

    if (msg.taskId && msg.viewing === true) {
        _setViewing(ws, ws.user, ws.uuid, ws.ws_key, msg.taskId, msg.viewing);
    }
}

function notifAvailable(ws) {
    if (!ws.user) {
        return;
    }

    ws_to_update.push(ws);
}

async function _sendUserOnline(ws_uuid, ws_key, userid, team_id, user_agent, online) {
    // regardless send the user online message to our systems before we check for activity writes
    sendUserOnlineWS(ws_uuid, ws_key, userid, team_id, user_agent, online, host);

    if (activityAuroraWriterParams().stop_team_writes) {
        return;
    }
    if (!userid || !team_id) {
        return;
    }
    let response;
    try {
        response = await promiseWebsocketsReadQuery(
            `
                SELECT ws_uuid
                FROM task_mgmt.ws_subscriptions
                WHERE userid = $1
                  AND team = $2
                  AND ws_uuid != $3
            `,
            [userid, team_id, ws_uuid]
        );
    } catch (err) {
        logWSError(err, 'WSS_090');
        return;
    }
    if (response.rows?.length === 0) {
        return;
    }
    try {
        await access.promiseAccessTeam(userid, team_id);
    } catch (err) {
        logWSError(err, 'WSS_091');
        return;
    }

    const queries = [];

    if (online) {
        // clean up any transient activity states
        const delete_params = [];
        const delete_query = `
            UPDATE task_mgmt.team_activity
            SET end_time = $${delete_params.push(Date.now())}
            WHERE
                userid = $${delete_params.push(userid)}
                AND team_id = $${delete_params.push(team_id)}
                AND end_time IS NULL
        `;
        queries.push({ query: delete_query, params: delete_params });
        const insert_params = [];
        const insert_query = `
            INSERT INTO task_mgmt.team_activity
                (userid, team_id, start_time)
            VALUES ($${insert_params.push(userid)},
                    $${insert_params.push(team_id)},
                    $${insert_params.push(Date.now())}
                )
            ON CONFLICT(userid, team_id, start_time)
                DO NOTHING
        `;
        queries.push({ query: insert_query, params: insert_params });
    } else {
        const update_params = [];
        const update_query = `
            UPDATE task_mgmt.team_activity
            SET end_time = $${update_params.push(Date.now())}
            WHERE
                userid = $${update_params.push(userid)}
                AND team_id = $${update_params.push(team_id)}
                AND end_time IS NULL
        `;
        queries.push({ query: update_query, params: update_params });
    }
    try {
        try {
            await db.batchAuroraActivityQueriesAsync(queries);
        } catch (err) {
            // double try for specific error on team activity fail here
            logger.error({
                process: 'aurora-activity-migration',
                msg: 'Batch write query to activity aurora db',
                success: false,
                err,
            });
            throw err;
        }
    } catch (err) {
        logWSError(err, 'WSS_092');
    }
}

function onAuth(ws) {
    _sendUserOnline(ws.uuid, ws.ws_key, ws.user, ws.team_id, ws.userAgent, true);
}

async function isForCurrentShard(message) {
    if (!SHARDING_ENABLED) {
        return true;
    }

    if (!message.teamId || !shouldEnforceShardRouting(config.get('sharding'))) {
        return true;
    }

    try {
        return await isWorkspaceAssignedToCurrentShard(message.teamId);
    } catch (err) {
        if (err instanceof ClickUpError && err.ECODE === ShardRoutingErrorCodes.RequestAtWrongShard) {
            return false;
        }
        throw err;
    }
}

async function authUser(message, ws, req) {
    const { token } = message;

    if (ws.authenticated && ws.team_id !== message.teamId) {
        ws.close(WSS_STATUS_CODE_ALREADY_AUTHENTICATED, 'Cannot re-authenticate without reconnecting');
        return;
    }

    wsJwtCheck(token, async (err, decoded_token) => {
        if (err) {
            logWSError(err, 'WSS_007');
            ws.close(WSS_STATUS_CODE_FAILED_AUTH, 'WSS_134: Authentication failed');
            metricsClient.increment(MetricNames.WEBSOCKET_USER_AUTH_ERROR, 1);
            return;
        }
        try {
            if (!(await isForCurrentShard(message))) {
                logWSError(
                    `received connection for wrong shard for workspace=${message.teamId} userAgent=${req?.headers['user-agent']}`,
                    'WSS_113',
                    400
                );
                ws.close(WSS_STATUS_CODE_WORKSPACE_NOT_FOUND, 'WSS_133: Workspace not found');
                return;
            }

            if (await isWorkspaceFrozen(message.teamId)) {
                ws.close(
                    WSS_STATUS_CODE_WORKSPACE_FROZEN,
                    `${BlockedErrorCodes.WorkspaceBlocked}: Workspace undergoing scheduled maintenance, we will be back online shortly`
                );
                return;
            }

            if (websockets[ws.user] && websockets[ws.user].includes(ws)) {
                websockets[ws.user].splice(websockets[ws.user].indexOf(ws), 1);
            }

            ws.authenticated = true;
            ws.user = decoded_token.user;

            if (message.sessionId) {
                ws.ws_key = message.sessionId;
            } else {
                ws.ws_key = decoded_token.ws_key;
            }

            if (message.teamId) {
                if (ws.team_id && ws.team_id !== message.teamId) {
                    _sendUserOnline(ws.uuid, ws.ws_key, ws.user, ws.team_id, ws.userAgent, false);
                }
                ws.team_id = message.teamId;
            }

            if (message.notifsForAllTeams) {
                ws.notifsForAllTeams = true;
            }

            if (message.projectId) {
                if (message.projectId >= 0) {
                    access.checkAccessProject(ws.user, message.projectId, { permissions: [] }, err1 => {
                        if (err1) {
                            delete message.projectId;
                        } else {
                            ws.project_id = message.projectId;
                        }

                        insertWSSubscription(ws, message);
                    });
                } else {
                    ws.project_id = message.projectId;
                    insertWSSubscription(ws, message);
                }
            }

            insertWSConnection(ws, message);

            if (!websockets[decoded_token.user]) {
                websockets[decoded_token.user] = [];
            }

            if (message.clearPing) {
                clearInterval(ws.pingInterval);
            }

            websockets[decoded_token.user].push(ws);

            ws.send(
                JSON.stringify({
                    msg: 'AuthReceived',
                    user: decoded_token.user,
                    ws_key: ws.ws_key,
                }),
                () => {}
            );

            notifAvailable(ws);

            if (!ws.session_started) {
                startSession(ws.user, ws, req);
                ws.session_started = true;
            }

            onAuth(ws);
        } catch (authCheckErr) {
            logWSError(authCheckErr, 'WSS_117', 400);
            ws.close(WSS_STATUS_CODE_WORKSPACE_NOT_FOUND, 'WSS_117: Workspace auth check error');
        }
    });
}

function oauthUser(message, ws, req) {
    const { token } = message;

    db.globalReplicaQuery(
        'SELECT * FROM task_mgmt.oauth_access_tokens WHERE token = $1',
        [token],
        { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_18 },
        (err, result) => {
            if (err) {
                ws.send(JSON.stringify(err), () => {});
                return;
            }

            if (result.rows.length === 0) {
                ws.send(
                    JSON.stringify({
                        err: 'Oauth token not found',
                        status: 401,
                        ECODE: 'OAUTH_019',
                    }),
                    () => {}
                );
                return;
            }

            req.decoded_token = { user: result.rows[0].userid };

            if (websockets[ws.user] && websockets[ws.user].indexOf(ws) >= 0) {
                websockets[ws.user].splice(websockets[ws.user].indexOf(ws), 1);
            }

            ws.authenticated = true;
            ws.user = req.decoded_token.user;
            if (message.sessionId) {
                ws.ws_key = message.sessionId;
            } else {
                ws.ws_key = req.decoded_token.ws_key;
            }

            insertWSConnection(ws, message);

            if (!websockets[req.decoded_token.user]) {
                websockets[req.decoded_token.user] = [];
            }

            if (message.clearPing) {
                clearInterval(ws.pingInterval);
            }

            websockets[req.decoded_token.user].push(ws);

            ws.send(
                JSON.stringify({
                    msg: 'AuthReceived',
                    user: req.decoded_token.user,
                    ws_key: ws.ws_key,
                }),
                () => {}
            );

            notifAvailable(ws);

            if (!ws.session_started) {
                startSession(ws.user, ws, req);
                ws.session_started = true;
            }
            onAuth(ws);
        }
    );
}

async function unsubscribeTaskWS(ws, userid, ws_uuid, task_id) {
    const { query, params } = prepareDeleteWebsocketSubsToTaskQuery(userid, ws_uuid, task_id);
    try {
        await promiseWebsocketsWriteQuery(query, params);
    } catch (err) {
        logWSError(err, 'WSS_080', 400);
    }
}

async function unsubscribeWS(ws, userid, ws_uuid, task_id, project_id, view_id) {
    const { query, params } = prepareDeleteWebsocketSubsToProjectQuery(userid, ws_uuid, task_id, project_id, view_id);

    try {
        await promiseWebsocketsWriteQuery(query, params);
    } catch (err) {
        logWSError(err, 'WSS_080', 400);
    }
}

async function unsubscribeTasksWS(ws, userid, ws_uuid) {
    const { query, params } = prepareDeleteWebsocketSubsToUserQuery(userid, ws_uuid);
    try {
        await promiseWebsocketsWriteQuery(query, params);
    } catch (err) {
        logWSError(err, 'WSS_080', 400);
    }
}

async function _deleteWSSubscription(ws_uuid) {
    try {
        await promiseWebsocketsWriteQuery('DELETE FROM task_mgmt.ws_subscriptions WHERE ws_uuid = $1', [ws_uuid]);
    } catch (err) {
        logWSError(err, 'WSS_052');
    }

    try {
        await promiseWebsocketsWriteQuery('DELETE FROM tasK_mgmt.ws_subs WHERE ws_uuid = $1', [ws_uuid]);
    } catch (err) {
        logWSError(err, 'WSS_081');
    }
}

let consumerStarted = false;

function startRedisConsumer() {
    if (consumerStarted) {
        return;
    }
    consumerStarted = true;
    consumeWebsocketMessagesForHost(host, message => {
        handleClusterMessage(message);
    });
}

let incomingMessageKafkaProducer;

async function getIncomingMessageKafkaProducer() {
    if (!incomingMessageKafkaProducer) {
        const producer = await KafkaProducerImpl.getKafkaProducer({
            clientId: 'be-websocket-gateway-service',
        });
        incomingMessageKafkaProducer = new WebsocketIncomingMessageProducer(producer);
    }
    return incomingMessageKafkaProducer;
}

export async function clickupWS(ws, req) {
    startRedisConsumer();
    ws.authenticated = false;
    ws.user = null;
    ws.team_id = null;
    ws.uuid = uuid.v4();
    ws.start = new Date().getTime();
    ws.multi_sub = false;
    ws.isAlive = true;
    ws.messageSendingBlocked = false;
    ws.buildVersion = null;
    ws.userAgent = req?.headers['user-agent'] || 'unknown';

    websocketRedis.websocketClientActivityRepository().addWebsocketConnectionActivity(host, ws.uuid);

    // example taken from: https://github.com/websockets/ws/tree/d8dd4852b81982fc0a6d633673968dff90985000#how-to-detect-and-close-broken-connections
    ws.pingInterval = setInterval(() => {
        if (ws.isAlive === false) {
            logger.warn({
                msg: `Websocket connection closed due to client pong not received`,
                ws_context: {
                    uuid: ws?.uuid,
                    user_id: ws?.user,
                    workspace_id: ws?.team_id,
                    user_agent: ws?.userAgent,
                },
            });
            ws.terminate();
            return;
        }
        ws.isAlive = false;
        ws.ping();
        if (ws.buildVersion) {
            ws.messageSendingBlocked = shouldSkipWebsocketMessagesForFrontendBuildVersion(ws.buildVersion);
        }
    }, 20000 + Math.floor(Math.random() * 100));

    ws.on('pong', () => {
        ws.isAlive = true;
        const { userPresenceConfig } = websocketMessageDistributionConfig();
        websocketRedis.websocketClientActivityRepository().addWebsocketConnectionActivity(host, ws.uuid);
        if (!ws.authenticated || !ws.user || !ws.team_id || !ws.uuid) {
            return;
        }
        if (!userPresenceConfig?.userOnlineCompatMode) {
            websocketRedis.websocketClientActivityRepository().updateTeamUserActivity({
                teamId: ws.team_id,
                wsUserId: ws.user,
                wsUuid: ws.uuid,
                userAgent: ws.userAgent,
                isOnline: true,
                websocketLoadBalancerHost: config.lb_url,
                websocketServerIp: host,
            });
        } else {
            websocketRedis.websocketUserPresenceService().updateWorkspaceUserPresence({
                workspaceId: ws.team_id,
                wsUserId: ws.user,
                wsUuid: ws.uuid,
                userAgent: ws.userAgent,
                isOnline: true,
                websocketLoadBalancerHost: config.lb_url,
                websocketServerIp: host,
            });
        }
    });

    ws.on('message', message => {
        initContext(AsyncStorage.getInstance(), async () => {
            try {
                message = JSON.parse(message);
            } catch (err) {
                logWSError(err, 'WSS_001');
                return;
            }

            let metricMethod;
            let incomingMessageConfig;

            try {
                metricMethod = message.method;

                incomingMessageConfig = new IncomingMessagesConfig();

                setWorkspaceIdForDbAffinity(ws.team_id || message.teamId);

                if (incomingMessageConfig.shouldSendToKafka(message.method)) {
                    const incomingMessage = mapWebsocketClientMessageToIncomingMessage(message, ws);
                    if (incomingMessage) {
                        // ship to kafka
                        const kafkaProducer = await getIncomingMessageKafkaProducer();
                        await kafkaProducer.send({ value: JSON.stringify(incomingMessage) });

                        metricsClient.distribution(MetricNames.WEBSOCKET_INCOMING_MESSAGE_ROUTING, 1, {
                            method: message.method.toLowerCase(),
                            routing: 'kafka',
                        });

                        return;
                    }
                }
                metricsClient.distribution(MetricNames.WEBSOCKET_INCOMING_MESSAGE_ROUTING, 1, {
                    method: message.method.toLowerCase(),
                    routing: 'local',
                });

                switch (message.method) {
                    case 'auth':
                        if (message.multi_sub) {
                            ws.multi_sub = true;
                        }
                        ws.buildVersion = message.buildVersion;
                        ws.messageSendingBlocked = !!shouldSkipWebsocketMessagesForFrontendBuildVersion(
                            ws.buildVersion
                        );
                        await authUser(message, ws, req);
                        break;
                    case 'multiSub':
                        ws.multi_sub = message.multiSub;
                        break;
                    case 'oauth':
                        // we want to remove this case - log to check if it's being used
                        logger.info({
                            msg: 'received oauth websocket message',
                            userAgent: req?.headers['user-agent'],
                        });
                        oauthUser(message, ws, req);
                        break;
                    case 'teamId':
                        ws.team_id = message.teamId;
                        notifAvailable(ws);
                        break;
                    case 'category':
                        await handleCategorySubscription(ws, message);
                        break;
                    case 'subcategory':
                        await handleSubcategorySubscription(ws, message);
                        break;
                    case 'projectId': // equivalent to handleProjectSubscription in
                        // wsMessageActionService.ts
                        await handleProjectSubscription(ws, message);
                        break;
                    case 'taskId':
                        await handleTaskSubscription(ws, message);
                        break;
                    case 'multiSubTaskId':
                        await handleTaskSubscription({ ...ws, multi_sub: true }, message);
                        break;
                    case 'unsubTaskId':
                        await unsubscribeTaskWS(ws, ws.user, ws.uuid, message.taskId);
                        break;
                    case 'multiSubViewIds':
                        // the existing view subscription through the projectId message remains unchanged
                        await handleViewSubscriptions({ ...ws, multi_sub: true }, message);
                        break;
                    case 'unsubViewIds':
                        await unsubscribeViewsWS(ws, message);
                        break;
                    case 'unsub':
                        await unsubscribeWS(
                            ws,
                            ws.user,
                            ws.uuid,
                            message.taskId,
                            message.subcategory,
                            message.category,
                            message.projectId,
                            message.view_id
                        );
                        break;
                    case 'unsubTasks':
                        await unsubscribeTasksWS(ws, ws.user, ws.uuid);
                        break;
                    case 'viewing':
                        await _setViewing(
                            ws,
                            ws.user,
                            ws.uuid,
                            ws.ws_key,
                            message.taskId,
                            message.viewing,
                            message.initial
                        );
                        break;
                    case 'commenting':
                        await setCommenting(ws, message);
                        break;
                    case 'viewingView':
                        await setViewingView(ws, message);
                        break;
                    case 'viewingPage':
                        await setViewingPage(ws, message);
                        break;
                    case 'commentingView':
                        await setCommentingView(ws, message);
                        break;
                    case 'commentingPage':
                        await setCommentingPage(ws, message);
                        break;
                    case 'takeoverPage':
                        await sendTakeoverPageReq(ws, message);
                        break;
                    case 'takeoverPageAck':
                        await sendTakeoverPageAckReq(ws, message);
                        break;
                    case 'subscribe':
                        await handleSubscribe(ws, message);
                        break;
                    default:
                        logger.debug({ msg: 'Unknown ws message', message });
                        metricMethod = 'unknown';
                        break;
                }
            } catch (err) {
                logWSError(err, 'WSS_012');
                return;
            }

            metricsClient.increment(MetricNames.WEBSOCKET_MESSAGE_RECEIVED, 1, { method: metricMethod });
        });
    });

    ws.on('error', err => {
        metricsClient.increment(MetricNames.WEBSOCKET_ERROR, 1, { code: err?.code });
    });

    ws.on('close', () => {
        initContext(AsyncStorage.getInstance(), () => {
            const now = Date.now();
            metricsClient.increment(MetricNames.WEBSOCKET_CLOSE);
            metricsClient.distribution(MetricNames.WEBSOCKET_CONNECTION_DURATION, now - ws.start, {
                buildVersion: ws.buildVersion,
            });

            websocketRedis.websocketClientActivityRepository().removeUserTeamActivity(ws.uuid, ws.team_id, ws.user);
            websocketRedis.websocketClientActivityRepository().removeWebsocketConnection(host, ws.uuid);

            clearInterval(ws.pingInterval);

            setWorkspaceIdForDbAffinity(ws.team_id);

            if (websockets[ws.user] && websockets[ws.user].indexOf(ws) >= 0) {
                websockets[ws.user].splice(websockets[ws.user].indexOf(ws), 1);
            }

            // TODO to be further improved later RDMP-7348
            // We recently added graceful shutdown support to the websocket server.
            // these connections never made it through previously on shutdown and now
            // they are creating a lot of resource contentions during shut down.
            // to help performance, we will NOT process any of these things on
            // graceful shutdown.

            if (ws.user && !processShutdownController.isShutdownRequested()) {
                closeSession(ws, now, ws.start);
                _sendUserOnline(ws.uuid, ws.ws_key, ws.user, ws.team_id, ws.userAgent, false);
            }

            if (ws.task_id && !processShutdownController.isShutdownRequested()) {
                if (shouldSendMessage('viewingUnset', undefined, 'sendViewingUnsetOpts')) {
                    sqs.sendWSMessage('sendViewingUnsetOpts', [
                        {
                            ws_uuid: ws.uuid,
                            ws_key: ws.ws_key,
                            userid: ws.user,
                            task_id: ws.task_id,
                            team_id: ws.team_id,
                        },
                    ]);
                }

                if (!activityAuroraWriterParams().stop_close_records_on_disconnect) {
                    closeTaskActivities([{ userid: ws.user, task_id: ws.task_id }]).catch(err => {
                        debugLogger.error({ msg: 'Close task activities failed', ECODE: 'WSS_027', error: err });
                    });
                }

                if (shouldSendMessage('commentingSet', undefined, 'sendCommentingSet')) {
                    sqs.sendWSMessage('sendCommentingSet', [ws.uuid, ws.ws_key, ws.user, ws.task_id, false]);
                }

                ws.task_id = null;
            }

            // We do not need to delete here because we have a cascade delete on the
            // ws_subscriptions table
            // @see: configureHostRegistrationListener
            if (!processShutdownController.isShutdownRequested()) {
                _deleteWSSubscription(ws.uuid);
            }
        });
    });
}

export async function newBuildPosted(req, resp, next) {
    if (req.headers.authorization !== AUTHORIZATION_HEADER) {
        next(new WSError('Unauthorized', 'WSS_004', 401));
        debugLogger.error({ msg: 'New Build rejected, incorrect headers', ECODE: 'WSS04' });
    } else {
        try {
            debugLogger.info({ msg: 'New Build!' });
            // Broadcast this to the current shard (g001)
            if (shouldTransformOvmEventToWebsocketV2Message('newBuild')) {
                await sendNewBuildV2();
            } else {
                await sqs.sendWSMessage('sendNewBuild');
            }
            // Now broadcast this to all other shards
            // Otherwise only workspaces that haven't been migrated to a shard will get the new build toast
            await broadcastService.broadcastRequest(req);
            resp.send({});
        } catch (error) {
            debugLogger.error({ msg: 'New Build failed during WS message send', ECODE: 'WSS_010', error });
            next(new WSError('New Build Broadcast failed', 'WSS_010', 500));
        }
    }
}

export function newStatusPosted(req, resp, next) {
    const { message, type, regions } = req.body;
    if (req.headers.authorization !== AUTHORIZATION_HEADER) {
        next(new WSError('Unauthorized', 'WSS_004', 401));
        debugLogger.error({ msg: 'New status rejected, incorrect headers', ECODE: 'WSS04' });
    } else {
        debugLogger.info({ msg: 'New Status!', message, type });
        sqs.sendWSMessage('sendNewStatus', [message || '', type || 'message', regions || []]);
        resp.send({});
    }
}

export async function sendNewBuild(cb) {
    const query = `SELECT lb_url, array_agg(host) AS hosts
                   FROM task_mgmt.hosts
                   GROUP BY lb_url`;
    const httpsAgent = new https.Agent({ timeout: 10000 });

    try {
        const result = await promiseWebsocketsReadQuery(query, []);
        for (const { lb_url, hosts } of result.rows) {
            const reqOptions = {
                method: 'POST',
                url: `${config.env === 'local' ? 'http' : 'https'}://${lb_url}/v1/lbWsNewBuild`,
                headers: {
                    authorization: config.ws.pw,
                },
                data: { hosts },
                httpsAgent,
            };

            try {
                await axios.request(reqOptions);
            } catch (reqErr) {
                logWebsocketMessageProcessingError(reqErr, 'WSS_903', 'Failed to send request to /v1/lbWsNewBuild', {
                    func: 'sendNewBuild',
                });
            }
        }
        cb();
    } catch (err) {
        logWSError(err, 'WSS_003');
    }
}

export async function sendNewStatus(message, type, regions, cb) {
    const query = `SELECT lb_url, array_agg(host) AS hosts
                   FROM task_mgmt.hosts
                   GROUP BY lb_url`;

    try {
        const result = await promiseWebsocketsReadQuery(query, []);

        let lb_host_group = [];
        let regionMap = _REGION_TO_CONFIG_LB_URL;
        const { env } = config;

        if (environment.isAnyStaging) {
            regionMap = _DEV_REGION_TO_CONFIG_LB_URL;
        }

        debugLogger.info({
            message: 'Building region map',
            exact_env: env,
        });

        if (regions && regions.length) {
            const lb_regions = [];
            regions.forEach(region => {
                if (region && (regionMap[region] || regionMap[region] === 0)) {
                    lb_regions.push(config.lb_urls[regionMap[region]]);
                }
            });
            lb_host_group = result.rows.filter(lb_host => lb_regions.includes(lb_host.lb_url));

            debugLogger.info({
                message: 'Using regions',
                regions,
                lb_host_count: lb_host_group.length,
            });
        } else {
            lb_host_group = result.rows;

            debugLogger.info({
                message: 'Skipping regions',
                lb_host_count: lb_host_group.length,
            });
        }

        if (!lb_host_group.length) {
            logger.debug({ msg: 'New status submitted with incorrect regions', message, type });
            cb();
        }

        const httpsAgent = new https.Agent({ timeout: 10000 });

        for (const { lb_url, hosts } of lb_host_group) {
            const reqOptions = {
                method: 'POST',
                url: `${env === 'local' ? 'http' : 'https'}://${lb_url}/v1/lbWsNewStatus`,
                headers: {
                    authorization: config.ws.pw,
                },
                data: { hosts, message, type },
                httpsAgent,
            };

            try {
                await axios.request(reqOptions);
            } catch (reqErr) {
                logWebsocketMessageProcessingError(reqErr, 'WSS_924', 'Failed to send request to /v1/lbWsNewStatus', {
                    func: 'sendNewStatus',
                });
            }
        }
        cb();
    } catch (err) {
        logWSError(err, 'WSS_024');
    }
}

export async function receiveNewStatusFromOtherApps(req, resp, next) {
    if (!config.ws.pw || req.headers.authorization !== config.ws.pw) {
        next(new WSError('Unauthorized', 'WS_025', 401));
        return;
    }

    const method = 'newStatus';
    const { hosts = [], message, type } = req.body;

    debugLogger.info({
        message: 'Receiving new status from other apps',
        lb_url: config.lb_url,
        exact_env: config.env,
        host_count: hosts.length,
    });

    for (const ip of hosts) {
        if (ip === host && !isLocalEnv) {
            handleClusterMessage({ msg: { method, message, type } });
        } else {
            const req_opts = {
                method: 'POST',
                url: `http://${ip}/v1/wsNewStatus`,
                headers: {
                    authorization: config.ws.pw,
                },
                data: { message, type },
            };

            try {
                await axios.request(req_opts);
            } catch (reqErr) {
                logWebsocketMessageDispatchError(reqErr, 'WSS_925', 'Failed to send request to /v1/wsNewStatus', {
                    method,
                });
            }
        }
    }

    resp.send({});
}

export function receiveNewStatusFromOtherHost(req, resp, next) {
    if (!config.ws.pw || req.headers.authorization !== config.ws.pw) {
        next(new WSError('Unauthorized', 'WSS_026', 401));
        return;
    }

    const { message, type } = req.body;

    debugLogger.info({
        message: 'Receiving new status from other hosts',
        exact_env: config.env,
        type,
    });

    handleClusterMessage({ msg: { method: 'newStatus', message, type } });

    resp.send({});
}

export async function receiveNewBuildFromOtherApps(req, resp, next) {
    if (!config.ws.pw || req.headers.authorization !== config.ws.pw) {
        next(new WSError('Unauthorized', 'WS_005', 401));
        return;
    }

    const method = 'newBuild2';
    const { hosts = [] } = req.body;

    for (const ip of hosts) {
        if (ip === host && !isLocalEnv) {
            handleClusterMessage({ msg: { method } });
        } else {
            const reqOptions = {
                method: 'POST',
                url: `http://${ip}/v1/wsNewBuild`,
                headers: {
                    authorization: config.ws.pw,
                },
            };

            try {
                await axios.request(reqOptions);
            } catch (reqErr) {
                logWebsocketMessageDispatchError(reqErr, 'WSS_905', 'Failed to send request to /v1/wsNewBuild', {
                    method,
                });
            }
        }
    }

    resp.send({});
}

export function receiveNewBuildFromOtherHost(req, resp, next) {
    if (!config.ws.pw || req.headers.authorization !== config.ws.pw) {
        next(new WSError('Unauthorized', 'WSS_006', 401));
        return;
    }

    debugLogger.info({
        message: 'Receiving new build from other hosts',
        exact_env: config.env,
    });

    handleClusterMessage({ msg: { method: 'newBuild2' } });

    resp.send({});
}

function notifAvailableInterval() {
    if (ws_to_update.length === 0) {
        setTimeout(notifAvailableInterval, 5000);
        return;
    }

    const interval_ws = ws_to_update.splice(0);
    const team_ids = [];
    const team_user_ids = [];
    const userids = [];

    interval_ws.forEach(ws => {
        if (ws.team_id && !ws.notifsForAllTeams) {
            team_ids.push(ws.team_id);
            team_user_ids.push(ws.user);
        } else if (ws.notifsForAllTeams) {
            userids.push(ws.user);
        }
    });

    async.parallel(
        {
            allTeams(para_cb) {
                if (!userids.length) {
                    para_cb();
                    return;
                }

                notifMod._usersNotificationsAvailableAnyTeam(userids, (err, result) => {
                    if (err) {
                        logWSError(err, 'WSS_008');
                    } else {
                        interval_ws.forEach(ws => {
                            if (ws.notifsForAllTeams && result[ws.user]) {
                                const message = {};
                                message.notifs = [];

                                Object.keys(result[ws.user]).forEach(key => {
                                    message.notifs.push(result[ws.user][key]);
                                });

                                message.method = 'anyTeamNotificationsAvailable';
                                instrumentedWsSend(null, ws, message, () => {});
                            }
                        });
                    }

                    para_cb();
                });
            },

            singleTeam(para_cb) {
                if (team_user_ids.length === 0) {
                    para_cb();
                    return;
                }

                notifMod._usersNotificationsAvailable(team_user_ids, team_ids, (err, result) => {
                    if (err) {
                        logWSError(err, 'WSS_009');
                    } else {
                        interval_ws.forEach(ws => {
                            if (ws.team_id && !ws.notifsForAllTeams && result[ws.user] && result[ws.user][ws.team_id]) {
                                const message = result[ws.user][ws.team_id];
                                message.method = 'notificationsAvailable';
                                instrumentedWsSend(null, ws, message, () => {});
                            }
                        });
                    }
                    para_cb();
                });
            },
        },
        () => setTimeout(notifAvailableInterval, 5000)
    );
}

if (config.api_server) {
    setTimeout(notifAvailableInterval, 5000);
}

function isWebsocketV2Message(message) {
    return ['websocket_message', 'websocket_ovm_message'].includes(message?.schema);
}

const handleClusterMessage = tracer.wrap('handleClusterMessage', {}, _handleClusterMessage);

function _handleClusterMessage(body) {
    const { context, msg, userid, ws_uuid, distributor_context } = body;

    if (shouldUseExtendedWebsocketV2Tracing()) {
        tracer.addTagsToRootSpan({ [ddMagicTags.MANUAL_KEEP]: 1 });
    }

    const isV2WSMessage = isWebsocketV2Message(msg);

    const metricTags = {};

    if (isV2WSMessage) {
        Object.assign(metricTags, {
            message_name: msg.event.name,
            message_version: 'v2',
        });
    } else {
        Object.assign(metricTags, {
            message_version: 'v1',
            method: msg?.method ?? 'unknown',
            func: context?.func ?? 'N/A',
        });
    }
    const tracerTagContext = {};

    if (typeof msg.trace_context === 'object') {
        try {
            const spanContext = tracer.tracer?.extract(TEXT_MAP, msg.trace_context);
            if (spanContext) {
                const activeSpan = tracer.getActiveSpan();
                activeSpan?.addLink(spanContext);
                Object.assign(context, { parentSpanContext: spanContext });
            }
            Object.assign(tracerTagContext, {
                ws_context: {
                    ...metricTags,
                    ws_uuid,
                    userid,
                },
            });
            if (!shouldUseExtendedWebsocketV2Tracing()) {
                delete msg.trace_context;
            }
        } catch (err) {
            // noop
        }
    }

    let foundWebsocket = false;

    if (['newBuild2', 'newStatus'].includes(msg.method) || msg.broadcast_strategy === BroadcastStrategy.ALL) {
        async.eachSeries(
            websockets,
            (ws_subscriptions, outer_each_cb) => {
                async.eachSeries(
                    ws_subscriptions,
                    (ws, each_cb) => {
                        instrumentedWsSend(context, ws, msg, distributor_context, () => {
                            setTimeout(() => each_cb(), 3);
                        });
                    },
                    () => setTimeout(() => outer_each_cb(), 3)
                );
            },
            () => {}
        );
    } else if (websockets[userid]) {
        for (const ws of websockets[userid]) {
            if (msg.msg === 'newNotif') {
                notifAvailable(ws);
                foundWebsocket = true;
            } else if (ws.uuid === ws_uuid) {
                foundWebsocket = true;
                instrumentedWsSend(context, ws, msg, distributor_context, () => {});
                break;
            }
        }
    } else {
        Object.assign(tracerTagContext, {
            handleMessageType: 'userNotFoundLocally',
        });
        metricsClient.distribution(MetricNames.WEBSOCKET_MESSAGE_SENT_SKIPPED_DISTRIBUTION, 1, {
            ...metricTags,
            reason: 'not_connected',
        });
    }

    if (foundWebsocket) {
        Object.assign(tracerTagContext, {
            handleMessageType: 'uuidFoundLocally',
        });
    } else {
        Object.assign(tracerTagContext, {
            handleMessageType: 'uuidNotFoundLocally',
        });
        metricsClient.distribution(MetricNames.WEBSOCKET_MESSAGE_SENT_SKIPPED_DISTRIBUTION, 1, {
            ...metricTags,
            reason: 'not_connected',
        });
    }

    tracer.addTagsToActiveScope(tracerTagContext);
}

const handleClusterMessageDeprecated = tracer.wrap('handleClusterMessageDeprecated', {}, handleClusterMessage);

if (cluster && cluster.worker) {
    cluster.worker.on('message', (worker, body, handle) => {
        if (!handle) {
            body = worker;
        }

        handleClusterMessageDeprecated(body);
    });
}

let sendMsgToOtherLocalHostsViaHttpCount = 0;

async function _sendMsgToOtherLocalHostsViaHttp(body, isV2Endpoint) {
    sendMsgToOtherLocalHostsViaHttpCount++;
    metricsClient.gauge(MetricNames.WEBSOCKET_SEND_MESSAGE_TO_OTHER_HOSTS_QUEUE, sendMsgToOtherLocalHostsViaHttpCount);

    // have tags defined outside of try catch block to be accessible in catch
    // block
    const tags = {
        transport_type: 'http',
    };

    const endpoint = isV2Endpoint ? '/v2/wsMsg' : '/v1/wsMsg';

    try {
        const { shouldUseRegexHostToIpUnfurl, timeout } = websocketPayloadBatchSizeConfig();

        const { context, msg } = body;
        let { ip } = body;

        const tracerTags = { isV2Endpoint, message: tags, originalIp: ip };

        if (shouldUseRegexHostToIpUnfurl) {
            const [hostname, port] = ip.split(':');

            const parsedIp = hostname
                .match(/ip-(([0-9]{1,3})-([0-9]{1,3})-([0-9]{1,3})-([0-9]{1,3}))/)?.[1]
                ?.replace(/-/g, '.');

            if (parsedIp) {
                ip = `${parsedIp}:${port}`;
            }

            tracerTags.unfurl_ip = {
                successfullyUnfurled: !!parsedIp,
                result: { hostname, port, ip },
            };
        }

        tags.method = msg?.method ?? 'unknown';
        tags.func = context?.func ?? 'unknown';

        const req_opts = {
            method: 'POST',
            url: `http://${ip}${endpoint}`,
            headers: {
                authorization: config.ws.pw,
            },
            data: body,
            timeout,
        };
        tracer.addTagsToActiveScope(tracerTags);

        await axios.request(req_opts);
    } catch (reqErr) {
        logWebsocketMessageDispatchError(reqErr, 'WSS_900', `Failed to send request to ${endpoint}`, tags);
    } finally {
        sendMsgToOtherLocalHostsViaHttpCount--;
        metricsClient.gauge(
            MetricNames.WEBSOCKET_SEND_MESSAGE_TO_OTHER_HOSTS_QUEUE,
            sendMsgToOtherLocalHostsViaHttpCount
        );
    }
}

const sendMsgToOtherLocalHostsViaHttp = tracer.wrap(
    'wssHelper.sendMsgToOtherLocalHostsViaHttp',
    {},
    _sendMsgToOtherLocalHostsViaHttp
);

let sendMsgToOtherLocalHostsViaRedisCount = 0;

/**
 *
 * @param {import("@clickup/websocket/message-util").MulticastMessageBody[]} messages
 * @returns {Promise<void>}
 * @private
 */
async function _sendMessagesToOtherLocalHostsViaRedis(messages) {
    sendMsgToOtherLocalHostsViaRedisCount += messages.length;
    const tags = {
        transport_type: 'redis',
    };
    try {
        const result = await websocketRedis.websocketMessageProducerService().sendMessagesToHosts(messages);
        for (const [msgErr, , { context, msg }] of result) {
            tags.method = msg?.method ?? 'unknown';
            tags.func = context?.func ?? 'unknown';
            if (msgErr) {
                logWebsocketMessageProcessingError(
                    msgErr,
                    'WSS_900',
                    `Failed to send request to redis for distribution`,
                    tags
                );
            }
        }
    } catch (err) {
        logger.error({
            msg: 'Failed to distribute messages to hosts.',
            err,
        });
        metricsClient.increment(MetricNames.WEBSOCKET_MESSAGE_PROCESSING_ERROR, messages.length, {
            ...tags,
            code: 'WSS_900',
        });
    } finally {
        sendMsgToOtherLocalHostsViaRedisCount -= messages.length;
        metricsClient.gauge(
            MetricNames.WEBSOCKET_SEND_MESSAGE_TO_REDIS_DISTRIBUTOR_QUEUE,
            sendMsgToOtherLocalHostsViaRedisCount
        );
    }
}

const sendMessagesToOtherLocalHostsViaRedis = tracer.wrap(
    'wssHelper.sendMessagesToOtherLocalHostsViaRedis',
    {},
    _sendMessagesToOtherLocalHostsViaRedis
);

function _sendMsgToOtherHosts(context, msg, cb = () => {}) {
    if (!MESSAGE_KEYS[msg.msg]) {
        cb();
        return;
    }

    const messageValid = shouldSendMessage(msg?.msg, msg?.schema, context?.func);

    if (!messageValid) {
        cb();
        return;
    }

    if (context?.start_func_time) {
        context.func_processing_time_ms = Date.now() - context.start_func_time;
    }

    const hist_only_subscriptions = [];
    const subscriptions = [];

    async.series(
        [
            async function getWSSubscription(series_cb) {
                try {
                    const result = await getMessageSubscriptions(msg);
                    result.forEach(wss => {
                        if (config.ws.hist_only.includes(msg.msg) && wss.version_two) {
                            hist_only_subscriptions.push(wss);
                        } else {
                            subscriptions.push(wss);
                        }
                    });

                    series_cb();
                } catch (e) {
                    series_cb(e);
                }
            },

            async function sendHistMessages(series_cb) {
                // taskMoved is deprecated in version two
                if (!hist_only_subscriptions.length || msg.msg === 'taskMoved') {
                    series_cb();
                    return;
                }
                try {
                    await createMessagesAndSend(context, msg, hist_only_subscriptions, { hist_only: true });
                } catch (err) {
                    // logWebsocketMessageProcessingError is already called in
                    // createMessagesAndSend
                    logWSError(err, 'WSS_104');
                }
                series_cb();
            },

            async function sendRest(series_cb) {
                if (!subscriptions.length) {
                    series_cb();
                    return;
                }

                try {
                    await createMessagesAndSend(context, msg, subscriptions, {});
                } catch (err) {
                    // logWebsocketMessageProcessingError is already called in
                    // createMessagesAndSend
                    logWSError(err, 'WSS_105');
                }
                series_cb();
            },
        ],
        err => {
            const tags = {
                // All msgs have a "msg" field, but some (like
                // "inboxMessageToUser") also have a "method" field
                method: msg.method || msg.msg,
            };

            if (err) {
                logWebsocketMessageDispatchError(err, 'WSS_020', 'Unable to get websocket message subscriptions', tags);
            }

            try {
                cb();
            } catch (e) {
                logWebsocketMessageDispatchError(err, 'WSS_108', 'Unknown error when executing callback', tags);
            }
        }
    );
}

const sendMsgToOtherHosts = tracer.wrap('sendMsgToOtherHosts', {}, _sendMsgToOtherHosts);
export { sendMsgToOtherHosts };

/**
 *
 * @param {import("@clickup/websocket/message-util").MulticastMessageBody[]} messages
 * @returns {Promise<void>}
 */
async function _batchDistributeWebsocketMessages(messages) {
    // start job on shutdown controller to make sure we send all messages before shutting down.
    const jobId = `batchDistributeWebsocketMessages_${Date.now()}_${uuid.v4()}`;
    const jobTimeoutMs = 15000;
    processShutdownController.jobStarted(jobId, {
        jobTimeoutMs,
        jobTimedOutCb: () => {
            logger.error({
                msg: 'Failed to send messages to another host in time.',
                jobTimeoutMs,
            });
        },
    });

    /** @type {import("@clickup/websocket/message-util").MulticastMessageBody[]} */
    const messagesToSend = [];

    for (const message of messages) {
        // TODO: remove userid & ws_uuid from message once we are transitioned to multi-cast
        const { context, ip, msg, userid, ws_uuid, subscriptions } = message;

        const messageValid = shouldSendMessage(msg?.msg, msg?.schema, context?.func);

        if (!messageValid) {
            continue;
        }

        if (ip === host && !isLocalEnv) {
            // TODO: only use subscriptions once we are transitioned to multi-cast
            const subIdentities = subscriptions ?? [{ userid, ws_uuid }];
            for (const subIdentity of subIdentities) {
                const messageToHandle = {
                    context,
                    msg,
                    userid: subIdentity.userid,
                    ws_uuid: subIdentity.ws_uuid,
                };
                handleClusterMessage(messageToHandle);
                if (cluster.worker) {
                    message.workerId = cluster.worker.id;
                    cluster.worker.send(messageToHandle);
                }
            }
        } else {
            messagesToSend.push(message);
        }
    }

    await sendMessagesToOtherLocalHostsViaRedis(messagesToSend).catch(err => {
        logger.error({ msg: 'Failed to batch send messages to other local hosts', err });
    });
    processShutdownController.jobDone(jobId);
}

const batchDistributeWebsocketMessages = tracer.wrap(
    'wssHelper.batchDistributeWebsocketMessages',
    {},
    _batchDistributeWebsocketMessages
);

export function receiveMsgFromOtherApps(req, resp, next) {
    if (!config.ws.pw || req.headers.authorization !== config.ws.pw) {
        next(new WSError('Unauthorized', 'WSS_021', 401));
        return;
    }

    const isV2Endpoint = req.path.startsWith('/v2/');

    let { messages } = req.body;

    if (!messages) {
        messages = [req.body];
    }

    const { useRedisDistributionForV1 } = websocketMessageDistributionConfig();

    if (useRedisDistributionForV1 && config.get('websocket_distributor.enabled')) {
        batchDistributeWebsocketMessages(messages).catch(err => {
            logger.error({
                msg: 'Failed to batch distribute messages',
                err,
            });
        });
    } else {
        // TODO: start job process for sending to other hosts. If we are in the middle of sending and SIGTERM happens, messages
        // will not be fully sent.
        for (const message of messages) {
            // TODO: remove userid & ws_uuid from message once we are transitioned to multi-cast
            const { context, ip, msg, userid, ws_uuid, subscriptions } = message;

            const messageValid = shouldSendMessage(msg?.msg, msg?.schema, context?.func);

            if (!messageValid) {
                return;
            }
            if (ip === host && !isLocalEnv) {
                // TODO: only use subscriptions once we are transitioned to multi-cast
                const subIdentities = subscriptions ?? [{ userid, ws_uuid }];
                for (const subIdentity of subIdentities) {
                    const messageToHandle = {
                        context,
                        msg,
                        userid: subIdentity.userid,
                        ws_uuid: subIdentity.ws_uuid,
                    };
                    handleClusterMessage(messageToHandle);
                    if (cluster.worker) {
                        message.workerId = cluster.worker.id;
                        cluster.worker.send(messageToHandle);
                    }
                }
            } else {
                (async function sendWithJobTracking() {
                    // start job on shutdown controller to make sure we send all messages before shutting down.
                    const jobId = `sendMsgToOtherLocalHosts_${Date.now()}_${uuid.v4()}`;
                    const jobTimeoutMs = 35000;
                    processShutdownController.jobStarted(jobId, {
                        jobTimeoutMs,
                        jobTimedOutCb: () => {
                            logger.error({
                                msg: 'Failed to send messages to another host in time.',
                                jobTimeoutMs,
                            });
                        },
                    });

                    await sendMsgToOtherLocalHostsViaHttp(message, isV2Endpoint).catch(err => {
                        logger.error({ msg: 'Failed to send message to other local hosts', err });
                    });
                    processShutdownController.jobDone(jobId);
                })();
            }
        }
    }
    resp.send({});
}

export function receiveMsgFromOtherHost(req, resp, next) {
    if (!config.ws.pw || req.headers.authorization !== config.ws.pw) {
        next(new WSError('Unauthorized', 'WSS_022', 401));
        return;
    }

    let { messages } = req.body;

    if (!messages) {
        messages = [req.body];
    }

    messages.forEach(message => {
        // TODO: remove userid & ws_uuid from message once we are transitioned to multi-cast
        const { context, msg, userid, ws_uuid, subscriptions } = message;

        const messageValid = shouldSendMessage(msg?.msg, msg?.schema, context?.func);

        if (!messageValid) {
            return;
        }

        // TODO: only use subscriptions once we are transitioned to multi-cast
        const subIdentities = subscriptions ?? [{ userid, ws_uuid }];
        for (const subIdentity of subIdentities) {
            const messageToHandle = {
                context,
                msg,
                userid: subIdentity.userid,
                ws_uuid: subIdentity.ws_uuid,
            };
            handleClusterMessage(messageToHandle);
            if (cluster.worker) {
                message.workerId = cluster.worker.id;
                cluster.worker.send(messageToHandle);
            }
        }
    });

    resp.send({});
}

function sendWSNotifEvent(userid, cb = () => {}) {
    sendMsgToOtherHosts(this?.context, { msg: 'newNotif', userid }, cb);
}

export { sendWSNotifEvent };

function sendWSImportComplete(userid, import_id, cb = () => {}) {
    sendMsgToOtherHosts(this?.context, { msg: 'importComplete', userid, import_id }, cb);
}

export { sendWSImportComplete };

function sendWSDarkThemeUpdate(userid, dark_theme, sidebar_theme, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(this?.context, { msg: 'darkThemeUpdate', userid, dark_theme, sidebar_theme, ws_key }, cb);
}

export { sendWSDarkThemeUpdate };

function sendBillingResolved(team_id, cb = () => {}) {
    sendMsgToOtherHosts(this?.context, { msg: 'billingResolved', team_id }, cb);
}

export { sendBillingResolved };

function sendBillingError(team_id, err, cb = () => {}) {
    sendMsgToOtherHosts(this?.context, { msg: 'billingError', team_id, err }, cb);
}

export { sendBillingError };

function sendSessionIdleTimeoutChange(userid, idle_timeout, cb = () => {}) {
    sendMsgToOtherHosts(this?.context, { msg: 'sessionIdleTimeoutChange', userid, idle_timeout }, cb);
}

export { sendSessionIdleTimeoutChange };

/**
 * Send Task Updated
 * @param {string} item_id - Task ID
 * @param {object} options - Options list is not complete
 * @param {number} [options.userid] - User ID
 * @param {string} [options.hist_id] - Hist ID
 * @param {string[]} [options.hist_ids] - Hist IDs
 * @param {function} [cb] - Callback
 */
function sendTaskUpdated(item_id, options, cb = () => {}) {
    let user = null;
    const messageName = 'taskUpdate';

    // Verify that the message should be processed or not
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    let time_estimate_rollup;
    let rollUpTimeEstimate;
    if (options && options.item_options) {
        ({ time_estimate_rollup, rollUpTimeEstimate } = options.item_options);
    }

    async.parallel(
        {
            hist_item(para_cb) {
                if (!options.hist_id && !options.hist_ids) {
                    para_cb();
                    return;
                }

                _getHistoryItems(
                    options.hist_ids || [options.hist_id],
                    {
                        includeTaskInfo: true,
                        type: options.type || TASK_TYPE,
                        get_hidden_hist_items: true,
                        userid: options.userid,
                    },
                    para_cb
                );
            },

            status_info(para_cb) {
                if (!options.mock_hist || options.mock_hist.field !== 'status') {
                    para_cb();
                    return;
                }

                options.mock_hist.after = options.mock_hist.after || {};

                const { status_id } = options.mock_hist.after;

                if (!status_id) {
                    para_cb();
                    return;
                }

                const query = `
                    SELECT *
                    FROM task_mgmt.statuses
                    WHERE id = $1`;
                const params = [status_id];

                db.replicaQuery(query, params, (err, result) => {
                    if (err) {
                        para_cb(new WSError(err, 'WSS_106'));
                        return;
                    }

                    const [row] = result.rows;

                    if (row) {
                        options.mock_hist.after.type = row.type;
                        options.mock_hist.after.color = row.color;
                        options.mock_hist.after.orderindex = row.orderindex;
                    }

                    para_cb();
                });
            },

            time_spent(para_cb) {
                if (!options.mock_hist || options.mock_hist.field !== 'time_spent' || !item_id) {
                    para_cb();
                    return;
                }

                timeSpentMod._getHistoryItem(
                    options.userid,
                    { obj_to_push: options.mock_hist, mock: true, task_id: item_id },
                    (err, hist_item) => {
                        if (err) {
                            para_cb(err);
                            return;
                        }

                        if (hist_item && hist_item.data) {
                            options.mock_hist.data = options.mock_hist.data || {};
                            options.mock_hist.data.total_time = hist_item.data.total_time;
                            options.mock_hist.data.rollup_time = hist_item.data.rollup_time;
                        }

                        para_cb();
                    }
                );
            },

            task_details(para_cb) {
                if (!item_id) {
                    para_cb();
                    return;
                }

                const query = `
                    SELECT items.parent, categories.project_id
                    FROM task_mgmt.items,
                         task_mgmt.subcategories,
                         task_mgmt.categories
                    WHERE items.id = $1
                      AND items.subcategory = subcategories.id
                      AND subcategories.category = categories.id`;
                db.replicaQuery(query, [item_id], (err, result) => {
                    if (err) {
                        para_cb(err);
                    } else {
                        para_cb(null, result.rows[0]);
                    }
                });
            },

            custom_task_ids(para_cb) {
                if (!item_id) {
                    para_cb();
                    return;
                }

                const query = `
                    SELECT custom_id
                    FROM task_mgmt.custom_task_ids
                    WHERE task_id = $1`;
                db.replicaQuery(query, [item_id], (err, result) => {
                    if (err) {
                        para_cb(err);
                    } else {
                        para_cb(null, result.rows[0]);
                    }
                });
            },

            user(para_cb) {
                if (!options.userid) {
                    para_cb();
                    return;
                }

                const query = `
                    SELECT id, color, email, username, profile_picture_key
                    FROM task_mgmt.users
                    WHERE id = $1`;
                const params = [options.userid];

                db.replicaQuery(query, params, (err, result) => {
                    if (err) {
                        para_cb(logWSError(err, 'WSS_026'));
                        return;
                    }

                    if (result.rows.length === 0) {
                        para_cb();
                        return;
                    }

                    const [row] = result.rows;

                    user = {
                        id: row.id,
                        username: row.username,
                        email: row.email,
                        color: row.color,
                        initials: userInitials.getInitials(row.email, row.username),
                        profilePicture: cf_sign.getCloudfrontAvatarUrl(row.profile_picture_key),
                    };

                    para_cb();
                });
            },

            coverimage(para_cb) {
                if (!options.coverimage || !item_id) {
                    para_cb();
                    return;
                }

                const query = `
                    SELECT task_id,
                           workspace_id,
                           cover_image_url,
                           cover_image_color,
                           cover_position_x,
                           cover_position_y,
                           edited_by,
                           date_created,
                           deleted,
                           date_updated
                    FROM task_mgmt.task_coverimage
                    WHERE task_id = $1
                      AND deleted = FALSE`;
                const params = [item_id];
                db.replicaQuery(query, params, (err, result) => {
                    if (err) {
                        para_cb(err);
                    } else {
                        para_cb(null, result.rows[0]);
                    }
                });
            },
        },
        (err, result) => {
            if (err) {
                cb();
                logWebsocketMessageProcessingError(err, 'WSS_023', 'Failed to process message', {
                    func: 'sendTaskUpdated',
                });
                return;
            }

            let hist_item = null;

            if (!result.task_details) {
                cb();
                return;
            }

            if (result.hist_item && result.hist_item.history && result.hist_item.history[0]) {
                if (options.hist_ids) {
                    hist_item = result.hist_item.history;
                } else {
                    [hist_item] = result.hist_item.history;
                }
            }

            if (options.mock_hist) {
                hist_item = options.mock_hist;
                hist_item.mock = true;
            }

            const { parent, project_id } = result.task_details;

            const time_estimate_options = {};

            if (time_estimate_rollup) {
                time_estimate_options.rolled_up_time_estimate = rollUpTimeEstimate;
            }

            sendMsgToOtherHosts(
                this?.context,
                {
                    ws_key: options.ws_key,
                    msg: messageName,
                    id: item_id,
                    project_id,
                    hist_item,
                    user,
                    task_id: item_id,
                    parent,
                    date_updated: options.date_updated,
                    ...time_estimate_options,
                    ...result.custom_task_ids,
                    coverimage: result.coverimage,
                },
                cb
            );
        }
    );
}

export { sendTaskUpdated };

function sendSubcategoryUpdate(context, msg, id, options, cb = () => {}) {
    async.parallel(
        {
            hist_item(para_cb) {
                if (!options.hist_id) {
                    para_cb();
                } else {
                    _getHistoryItems(
                        [options.hist_id],
                        {
                            includeTaskInfo: options.includeTaskInfo,
                            type: SUBCATEGORY_TYPE,
                        },
                        para_cb
                    );
                }
            },
        },
        (err, result) => {
            if (err) {
                cb();
                logWSError(err, options.error_code || 'WSS_111');
            } else {
                let hist_item = null;

                if (result.hist_item && result.hist_item.history && result.hist_item.history[0]) {
                    [hist_item] = result.hist_item.history;
                }
                sendMsgToOtherHosts(
                    context,
                    {
                        ws_key: options.ws_key,
                        msg,
                        hist_item,
                        subcategory_id: id,
                    },
                    cb
                );
            }
        }
    );
}

function sendSubcatComment(parent, options, cb = () => {}) {
    sendSubcategoryUpdate(
        this?.context,
        'subcategoryComment',
        parent,
        {
            ...options,
            includeTaskInfo: true,
            error_code: 'WSS_023',
        },
        cb
    );
}

export { sendSubcatComment };

function sendCategoryUpdate(context, msg, id, options, cb = () => {}) {
    async.parallel(
        {
            hist_item(para_cb) {
                if (!options.hist_id) {
                    para_cb();
                } else {
                    _getHistoryItems(
                        [options.hist_id],
                        { includeTaskInfo: options.includeTaskInfo, type: CATEGORY_TYPE },
                        para_cb
                    );
                }
            },
        },
        (err, result) => {
            if (err) {
                cb();
                logWSError(err, options.error_code || 'WSS_111');
            } else {
                let hist_item = null;

                if (result.hist_item && result.hist_item.history && result.hist_item.history[0]) {
                    [hist_item] = result.hist_item.history;
                }
                sendMsgToOtherHosts(
                    context,
                    {
                        ws_key: options.ws_key,
                        msg,
                        hist_item,
                        category_id: id,
                    },
                    cb
                );
            }
        }
    );
}

function sendCatComment(parent, options, cb = () => {}) {
    sendCategoryUpdate(
        this?.context,
        'categoryComment',
        parent,
        {
            ...options,
            includeTaskInfo: true,
            error_code: 'WSS_023',
        },
        cb
    );
}

export { sendCatComment };

function sendProjectUpdate(context, msg, id, options, cb = () => {}) {
    async.parallel(
        {
            hist_item(para_cb) {
                if (!options.hist_id) {
                    para_cb();
                } else {
                    _getHistoryItems(
                        [options.hist_id],
                        { includeTaskInfo: options.includeTaskInfo, type: PROJECT_TYPE },
                        para_cb
                    );
                }
            },
        },
        (err, result) => {
            if (err) {
                cb();
                logWSError(err, options.error_code || 'WSS_111');
            } else {
                let hist_item = null;

                if (result.hist_item && result.hist_item.history && result.hist_item.history[0]) {
                    [hist_item] = result.hist_item.history;
                }
                sendMsgToOtherHosts(
                    context,
                    {
                        ws_key: options.ws_key,
                        msg,
                        hist_item,
                        project_id: id,
                    },
                    cb
                );
            }
        }
    );
}

function sendProjectComment(parent, options, cb = () => {}) {
    sendProjectUpdate(
        this?.context,
        'projectComment',
        parent,
        {
            ...options,
            includeTaskInfo: true,
            error_code: 'WSS_023',
        },
        cb
    );
}

export { sendProjectComment };

function sendSubcatCommentDeleteUndo(parent, options, cb = () => {}) {
    sendSubcategoryUpdate(
        this?.context,
        'subcategoryCommentDeleteUndo',
        parent,
        {
            ...options,
            error_code: 'WSS_023',
        },
        cb
    );
}

export { sendSubcatCommentDeleteUndo };

function sendSubcategoryAttachment(subcategory_id, options, cb = () => {}) {
    sendSubcategoryUpdate(
        this?.context,
        'subcategoryAttachment',
        subcategory_id,
        {
            ...options,
            includeTaskInfo: true,
            error_code: 'WSS_112',
        },
        cb
    );
}

export { sendSubcategoryAttachment };

function sendSubcategoryAttachmentDeleted(subcategory_id, attachment_id, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key,
            msg: 'subcategoryAttachmentDeleted',
            id: attachment_id,
            subcategory_id,
        },
        cb
    );
}

export { sendSubcategoryAttachmentDeleted };

function sendViewCommentDeleteUndo(parent, options, cb = () => {}) {
    async.parallel(
        {
            hist_item(para_cb) {
                if (!options.hist_id) {
                    para_cb();
                } else {
                    _getHistoryItems(
                        [options.hist_id],
                        { includeTaskInfo: true, type: VIEW_TYPE, workspace_id: options.workspace_id },
                        para_cb
                    );
                }
            },
        },
        (err, result) => {
            if (err) {
                cb();
                logWebsocketMessageProcessingError(err, 'WSS_023', 'Failed to process message', {
                    func: 'sendViewCommentDeleteUndo',
                });
            } else {
                let hist_item = null;

                if (result.hist_item && result.hist_item.history && result.hist_item.history[0]) {
                    [hist_item] = result.hist_item.history;
                }

                const message = {
                    ws_key: options.ws_key,
                    msg: 'viewCommentDeleteUndo',
                    hist_item,
                    view_id: parent,
                };

                if (options.type === DOC_TYPE && hist_item.view_id) {
                    Object.assign(message, { view_id: hist_item.view_id, page_id: parent });
                }

                sendMsgToOtherHosts(this?.context, message, cb);
            }
        }
    );
}

export { sendViewCommentDeleteUndo };

function sendViewComment(parent, options, cb = () => {}) {
    async.parallel(
        {
            hist_item(para_cb) {
                if (!options.hist_id) {
                    para_cb();
                } else {
                    _getHistoryItems(
                        [options.hist_id],
                        {
                            includeTaskInfo: true,
                            type: options.threaded ? COMMENT_TYPE : VIEW_TYPE,
                            workspace_id: options.workspace_id,
                        },
                        para_cb
                    );
                }
            },
        },
        (err, result) => {
            if (err) {
                cb();
                logWebsocketMessageProcessingError(err, 'WSS_023', 'Failed to process message', {
                    func: 'sendViewComment',
                });
            } else {
                let hist_item = null;

                if (result.hist_item && result.hist_item.history && result.hist_item.history[0]) {
                    [hist_item] = result.hist_item.history;
                }
                sendMsgToOtherHosts(
                    this?.context,
                    {
                        ws_key: options.ws_key,
                        msg: 'viewComment',
                        hist_item,
                        threaded: options.threaded,
                        parent: options.thread_id,
                        view_id: hist_item ? hist_item.view_id || parent : parent,
                        page_id: hist_item && hist_item.comment && hist_item.comment.page_id,
                    },
                    cb
                );
            }
        }
    );
}

export { sendViewComment };

function sendSubcatCommentEdited(parent, comment_id, ws_key, cb = () => {}) {
    commentSearch.getComments(null, parent, { comment_ids: [comment_id], type: SUBCATEGORY_TYPE }, (err, result) => {
        let comment;
        if (result && result.comments[0]) {
            [comment] = result.comments;
        }
        if (comment) {
            sendMsgToOtherHosts(
                this?.context,
                {
                    ws_key,
                    msg: 'subcategoryCommentEdited',
                    comment,
                    subcategory_id: parent,
                },
                cb
            );
        }
    });
}

export { sendSubcatCommentEdited };

function sendCategoryCommentEdited(parent, comment_id, ws_key, cb = () => {}) {
    commentSearch.getComments(null, parent, { comment_ids: [comment_id], type: CATEGORY_TYPE }, (err, result) => {
        let comment;
        if (result && result.comments[0]) {
            [comment] = result.comments;
        }
        if (comment) {
            sendMsgToOtherHosts(
                this?.context,
                {
                    ws_key,
                    msg: 'categoryCommentEdited',
                    comment,
                    category_id: parent,
                },
                cb
            );
        }
    });
}

export { sendCategoryCommentEdited };

function sendProjectCommentEdited(parent, comment_id, ws_key, cb = () => {}) {
    commentSearch.getComments(null, parent, { comment_ids: [comment_id], type: PROJECT_TYPE }, (err, result) => {
        let comment;
        if (result && result.comments[0]) {
            [comment] = result.comments;
        }
        if (comment) {
            sendMsgToOtherHosts(
                this?.context,
                {
                    ws_key,
                    msg: 'projectCommentEdited',
                    comment,
                    project_id: parent,
                },
                cb
            );
        }
    });
}

export { sendProjectCommentEdited };

async function sendViewCommentEdited(parent, comment_id, ws_key, options, cb) {
    if (!cb) {
        cb = options;
    }

    let page_view_id;

    if (options && options.type === config.get('comments.types.doc')) {
        try {
            page_view_id = await pageHelpers.getPageViewId(parent);
        } catch (err) {
            cb(new WSError(err, 'WSS_107'));
        }
    }

    commentSearch.getComments(
        null,
        options.threaded ? options.thread_id : parent,
        { comment_ids: [comment_id], type: options.type || VIEW_TYPE },
        (err, result) => {
            let comment;
            if (result && result.comments[0]) {
                [comment] = result.comments;
            }
            if (comment) {
                const message = {
                    ws_key,
                    msg: 'viewCommentEdited',
                    comment,
                    view_id: parent,
                    threaded: options.threaded,
                    thread_id: options.thread_id,
                };

                if (page_view_id) {
                    Object.assign(message, { view_id: page_view_id, page_id: parent });
                }

                sendMsgToOtherHosts(this?.context, message, cb);
            } else {
                cb();
            }
        }
    );
}

export { sendViewCommentEdited };

function sendSubtaskRemoved(task_id, subtask_id, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'subtaskRemoved',
            ws_key,
            task_id,
            subtask_id,
        },
        cb
    );
}

export { sendSubtaskRemoved };

function sendSubtaskAdded(subtask_id, parent, old_parent, ws_key, cb = () => {}) {
    _getTaskProjectId(subtask_id, (err, project_id) => {
        sendMsgToOtherHosts(
            this?.context,
            {
                ws_key,
                msg: 'subtaskAdded',
                id: subtask_id,
                old_parent,
                parent,
                project_id,
                task_id: subtask_id,
            },
            cb
        );
    });
}

export { sendSubtaskAdded };

function sendTaskMoved(item_id, old_parent, ws_key, oldStatus, cb = () => {}) {
    _getTaskProjectId(item_id, (err, project_id) => {
        sendMsgToOtherHosts(
            this?.context,
            {
                ws_key,
                msg: 'taskMoved',
                id: item_id,
                old_parent,
                project_id,
                oldStatus,
                task_id: item_id,
            },
            cb
        );
    });
}

export { sendTaskMoved };

function sendTasksMoved(task_objs, old_parent_map, ws_key, oldStatuses, cb = () => {}) {
    const task_ids = task_objs.map(task => task.id);

    _getTasksProjectIds(task_ids, (err, project_ids_map) => {
        if (err) {
            cb();
            logWebsocketMessageProcessingError(err, 'WSS_078', 'Failed to process message', {
                func: 'sendTasksMoved',
            });
            return;
        }

        const tasks_by_project = {};

        task_objs.forEach(task_obj => {
            const { id: task_id } = task_obj;
            const project_id = project_ids_map[task_id];
            const old_parent = old_parent_map[task_id];

            if (!tasks_by_project[project_id]) {
                tasks_by_project[project_id] = [];
            }

            task_obj.old_parent = old_parent;
            tasks_by_project[project_id].push(task_obj);
        });

        async.each(
            Object.keys(tasks_by_project),
            (project_id, each_cb) => {
                sendMsgToOtherHosts(
                    this?.context,
                    {
                        ws_key,
                        msg: 'tasksMoved',
                        tasks: tasks_by_project[project_id],
                        project_id,
                        oldStatuses,
                    },
                    each_cb
                );
            },
            () => {
                cb();
            }
        );
    });
}

export { sendTasksMoved };

function sendTaskCreated(task_id, ws_key, cb = () => {}) {
    const messageName = 'taskCreated';

    // Verify that the message should be processed or not
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    _getTaskTeamId(task_id, (err, team_id) => {
        if (err) {
            cb();
            logWebsocketMessageProcessingError(err, 'WSS_028', 'Failed to process message', {
                func: 'sendTaskCreated',
            });
            return;
        }

        sendMsgToOtherHosts(
            this?.context,
            {
                ws_key,
                msg: 'taskCreated',
                id: task_id,
                team_id,
                task_id,
            },
            cb
        );
    });
}

export { sendTaskCreated };

function sendCommentDeleted(task_id, comment_id, ws_key, cb = () => {}) {
    const messageName = 'commentDeleted';

    // Verify that the message should be processed or not
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    _getTaskProjectId(task_id, (err, project_id) => {
        if (err) {
            cb();
            logWebsocketMessageProcessingError(err, 'WSS_029', 'Failed to process message', {
                func: 'sendCommentDeleted',
            });
            return;
        }

        sendMsgToOtherHosts(
            this?.context,
            {
                ws_key,
                msg: messageName,
                id: comment_id,
                project_id,
                task: { id: task_id },
                task_id,
            },
            cb
        );
    });
}

export { sendCommentDeleted };

function sendSubcategoryCommentDeleted(subcategory, comment_id, ws_key, cb) {
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key,
            msg: 'subcategoryCommentDeleted',
            id: comment_id,
            subcategory_id: subcategory,
        },
        cb
    );
}

export { sendSubcategoryCommentDeleted };

async function sendViewCommentDeleted(view_id, comment_id, ws_key, options, cb) {
    if (!cb) {
        cb = options;
        options = {};
    }

    let page_view_id;

    if (options && options.type === config.get('comments.types.doc')) {
        try {
            page_view_id = await pageHelpers.getPageViewId(view_id);
        } catch (err) {
            cb(new WSError(err, 'WSS_109'));
            return;
        }
    }

    const message = {
        ws_key,
        msg: 'viewCommentDeleted',
        id: comment_id,
        view_id,
        threaded: options.threaded,
    };

    if (page_view_id) {
        Object.assign(message, { view_id: page_view_id, page_id: view_id });
    }

    sendMsgToOtherHosts(this?.context, message, cb);
}

export { sendViewCommentDeleted };

function sendDependencyRemoved(task_id, depends_on, ws_key, cb) {
    if (!cb) {
        cb = ws_key;
    }

    async.parallel(
        {
            dependency(para_cb) {
                _getTaskProjectId(task_id, (err, project_id) => {
                    if (err) {
                        para_cb();
                        logWebsocketMessageProcessingError(err, 'WSS_030', 'Failed to process message', {
                            func: 'sendDependencyRemoved',
                        });
                        return;
                    }

                    sendMsgToOtherHosts(
                        this?.context,
                        {
                            ws_key,
                            msg: 'dependencyRemoved',
                            task_id,
                            project_id,
                            depends_on,
                            dependency: task_id,
                        },
                        para_cb
                    );
                });
            },
            dependsOn(para_cb) {
                _getTaskProjectId(depends_on, (err, project_id) => {
                    if (err) {
                        para_cb();
                        logWebsocketMessageProcessingError(err, 'WSS_031', 'Failed to process message', {
                            func: 'sendDependencyRemoved',
                        });
                        return;
                    }

                    sendMsgToOtherHosts(
                        this?.context,
                        {
                            ws_key,
                            msg: 'dependencyRemoved',
                            task_id: depends_on,
                            project_id,
                            dependency: task_id,
                            depends_on,
                        },
                        para_cb
                    );
                });
            },
        },
        () => {
            cb();
        }
    );
}

export { sendDependencyRemoved };

function sendCommentEdited(project_id, task_id, comment, ws_key, cb = () => {}) {
    const messageName = 'commentEdited';

    // Verify that the message should be processed or not
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key,
            msg: messageName,
            task_id,
            project_id,
            comment,
        },
        cb
    );
}

export { sendCommentEdited };

async function sendThreadedCommentEdited(userid, commentId, parent, ws_key, options, cb = () => {}) {
    try {
        const messageName = 'threadedCommentEdited';

        // Verify that the message should be processed or not
        if (shouldSkipWebsocketV1Message(messageName)) {
            cb();
            return;
        }

        const [taskParent, comments] = await Promise.all([
            getTaskParentIdForThreadedComment(parent),
            commentSearch._promiseGetComments(userid, parent, { comment_ids: [commentId], type: COMMENT_TYPE }),
        ]);

        sendMsgToOtherHosts(
            this?.context,
            {
                ws_key,
                msg: messageName,
                task_id: taskParent,
                comment: comments?.comments[0],
                threaded: true,
                thread_id: parent,
                options,
            },
            cb
        );
    } catch (err) {
        cb();
        logWebsocketMessageProcessingError(err, 'WSS_099', 'Failed to process message', {
            func: 'sendThreadedCommentEdited',
        });
    }
}

export { sendThreadedCommentEdited };

async function sendThreadedCommentDeleted(parent, commentId, ws_key, options, cb = () => {}) {
    try {
        const messageName = 'threadedCommentDeleted';

        // Verify that the message should be processed or not
        if (shouldSkipWebsocketV1Message(messageName)) {
            cb();
            return;
        }

        const task_id = await getTaskParentIdForThreadedComment(parent);

        if (!task_id) {
            throw new WSError(`not found task id for comment ${parent}`, 'WSS_116');
        }

        const projectId = await _promiseGetTaskProjectId(task_id);
        sendMsgToOtherHosts(
            this?.context,
            {
                ws_key,
                msg: messageName,
                id: commentId,
                project_id: projectId,
                task: { id: task_id },
                parent,
                task_id,
            },
            cb
        );
    } catch (err) {
        logWebsocketMessageProcessingError(err, 'WSS_100', 'Failed to process message', {
            func: 'sendThreadedCommentDeleted',
        });
    }
}

export { sendThreadedCommentDeleted };

async function sendThreadedCommentDeleteUndone(parent, commentId, ws_key, options, cb = () => {}) {
    const messageName = 'threadedCommentDeleteUndo';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    try {
        const task_id = await getTaskParentIdForThreadedComment(parent);

        if (!task_id) {
            throw new WSError(`not found task id for comment ${parent}`, 'WSS_115');
        }

        const projectId = await _promiseGetTaskProjectId(task_id);
        sendMsgToOtherHosts(
            this?.context,
            {
                ws_key,
                msg: messageName,
                id: commentId,
                project_id: projectId,
                task: { id: task_id },
                parent,
                task_id,
                comment: options.comment,
            },
            cb
        );
    } catch (err) {
        logWebsocketMessageProcessingError(err, 'WSS_114', 'Failed to process message', {
            func: 'sendThreadedCommentDeleteUndone',
        });
    }
}

export { sendThreadedCommentDeleteUndone };

function sendCommentDeleteUndone(project_id, task_id, hist_item, ws_key, cb = () => {}) {
    const messageName = 'commentDeleteUndo';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key,
            msg: messageName,
            task_id,
            project_id,
            hist_item,
        },
        cb
    );
}

export { sendCommentDeleteUndone };

function sendAttachmentDeleted(task_id, attachment_id, ws_key, cb = () => {}) {
    _getTaskProjectId(task_id, (err, project_id) => {
        if (err) {
            cb();
            logWebsocketMessageProcessingError(err, 'WSS_031', 'Failed to process message', {
                func: 'sendAttachmentDeleted',
            });
            return;
        }

        sendMsgToOtherHosts(
            this?.context,
            {
                ws_key,
                msg: 'attachmentDeleted',
                id: attachment_id,
                project_id,
                task: { id: task_id },
                task_id,
            },
            cb
        );
    });
}

export { sendAttachmentDeleted };

function sendTaskMerged(userid, task1_id, task2_id, ws_key, cb = () => {}) {
    async.parallel(
        {
            task1(para_cb) {
                _getItem(
                    userid,
                    task1_id,
                    {
                        mobile: false,
                        skipPosition: true,
                        skip_weights: true,
                        replica: true,
                    },
                    para_cb
                );
            },

            task2(para_cb) {
                _getItem(
                    userid,
                    task2_id,
                    {
                        mobile: false,
                        include_deleted: true,
                        skipPosition: true,
                        skip_weights: true,
                        replica: true,
                    },
                    para_cb
                );
            },
        },
        (err, result) => {
            if (err) {
                cb();
                logWebsocketMessageProcessingError(err, 'WSS_032', 'Failed to process message', {
                    func: 'sendTaskMerged',
                });
                return;
            }

            let { task1, task2 } = result;

            const project_id = task1.project.id;
            const query = 'SELECT id, color, email, username, profile_picture_key FROM task_mgmt.users WHERE id = $1';

            db.replicaQuery(query, [userid], (err1, result1) => {
                if (err1) {
                    cb();
                    logWebsocketMessageProcessingError(err, 'WSS_033', 'Failed to process message', {
                        func: 'sendTaskMerged',
                    });
                    return;
                }

                if (!result1.rows.length) {
                    cb();
                    return;
                }

                const [row] = result1.rows;

                const user = {
                    id: row.id,
                    username: row.username,
                    email: row.email,
                    color: row.color,
                    initials: userInitials.getInitials(row.email, row.username),
                    profilePicture: cf_sign.getCloudfrontAvatarUrl(row.profile_picture_key),
                };

                // make sure subscribers don't get empty permissions array.
                task1 = Object.assign({}, task1 || {}, {
                    permissions: undefined,
                    subcategory: undefined,
                    category: undefined,
                    project: undefined,
                });

                task2 = Object.assign({}, task2 || {}, {
                    permissions: undefined,
                    subcategory: undefined,
                    category: undefined,
                    project: undefined,
                });

                sendMsgToOtherHosts(
                    this?.context,
                    {
                        ws_key,
                        msg: 'taskMerged',
                        task: task2,
                        merged_into: task1,
                        merged_into_id: task1.id,
                        project_id,
                        user,
                        task_id: task2_id,
                    },
                    cb
                );
            });
        }
    );
}

export { sendTaskMerged };

function sendTagRemoved(userid, ws_key, task_ids, tag_name, cb = () => {}) {
    if (!task_ids) {
        cb();
        return;
    }

    async.each(
        task_ids,
        (task_id, each_cb) => {
            _getItem(userid, task_id, { skipPosition: true, skip_weights: true, replica: true }, (err, task) => {
                if (err) {
                    each_cb();
                    logWebsocketMessageProcessingError(err, 'WSS_034', 'Failed to process message', {
                        func: 'sendTagRemoved',
                    });
                    return;
                }

                sendMsgToOtherHosts(
                    this?.context,
                    {
                        ws_key,
                        msg: 'tagRemoved',
                        task_id,
                        project_id: task.project.id,
                        parent: task.parent,
                        tag_name,
                    },
                    each_cb
                );
            });
        },
        () => {
            cb();
        }
    );
}

export { sendTagRemoved };

function sendTagAdded(userid, ws_key, task_ids, tagObject, cb = () => {}) {
    async.each(
        task_ids,
        (task_id, each_cb) => {
            sendMsgToOtherHosts(
                this?.context,
                {
                    ws_key,
                    msg: 'tagAdded',
                    task_id,
                    tag: tagObject,
                },
                each_cb
            );
        },
        () => {
            cb();
        }
    );
}

export { sendTagAdded };

function sendTaskCopyComplete(userid, task_id, ws_key, template, tracing_id, is_template, cb) {
    if (!cb) {
        cb = is_template;
    }

    if (typeof task_id === 'object' && task_id != null && task_id.send_template) {
        const task = task_id;

        sendMsgToOtherHosts(
            this?.context,
            {
                ws_key,
                msg: 'taskCopyComplete',
                id: task.id,
                tracing_id,
                task,
                template,
                userid,
                is_template,
            },
            cb
        );
        return;
    }

    const options = {
        include_deleted: true,
        skipPosition: true,
        skip_weights: true,
        replica: true,
    };

    _getItem(userid, task_id, options, (err, task) => {
        if (err) {
            cb();
            logWebsocketMessageProcessingError(err, 'WSS_036', 'Failed to process message', {
                func: 'sendTaskCopyComplete',
            });
            return;
        }

        if (task.deleted) {
            metricsClient.increment(MetricNames.WEBSOCKET_MESSAGE_PROCESSING_SKIPPED, 1, {
                func: 'sendTaskCopyComplete',
                reason: 'task_deleted',
            });
            cb();
            return;
        }

        // this event gets sent to user who made the action ONLY
        // okay to send permissions object from this side

        const project_id = task.project.id;

        sendMsgToOtherHosts(
            this?.context,
            {
                ws_key,
                msg: 'taskCopyComplete',
                id: task.id,
                project_id,
                task,
                template,
                userid,
                is_template,
            },
            cb
        );
    });
}

export { sendTaskCopyComplete };

function sendTasksCopyComplete(userid, task_ids, ws_key, template, cb = () => {}) {
    _getItems(task_ids, { userid }, (err, tasks) => {
        if (!err) {
            // This gets sent to user who made event ONLY
            // okay to send permissions object and hierarchy

            let project_id;
            if (tasks && tasks.length) {
                project_id = tasks[0].project_id;
            }

            sendMsgToOtherHosts(
                this?.context,
                {
                    ws_key,
                    msg: 'tasksCopyComplete',
                    project_id,
                    tasks,
                    template,
                    userid,
                },
                cb
            );
        } else {
            cb();
        }
    });
}

export { sendTasksCopyComplete };

function sendViewsCopyComplete(userid, in_view_ids, ws_key, cb = () => {}) {
    viewMod._getViews(userid, { in_view_ids, skipAccess: true }, (err, views) => {
        if (!err) {
            sendMsgToOtherHosts(
                this?.context,
                {
                    ws_key,
                    msg: 'viewsCopyComplete',
                    views: views.views,
                    userid,
                },
                cb
            );
        } else {
            cb();
        }
    });
}

export { sendViewsCopyComplete };

// todo potential large argument
function sendSubcatCopyComplete(userid, subcat, ws_key, template, tracing_id, is_template, cb) {
    delete subcat.permissions;
    if (!cb) {
        cb = is_template;
    }

    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key,
            msg: 'subcategoryCopyComplete',
            userid,
            id: subcat.id,
            project_id: subcat.project_id,
            tracing_id,
            subcat,
            template,
            is_template,
        },
        cb
    );
}

export { sendSubcatCopyComplete };

function sendTemplateCreated(userid, template, template_type, ws_key, tracing_id, cb) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'templateCreated',
            id: template.id,
            templateName: template.templateName,
            template_type,
            tracing_id,
            ws_key,
            userid,
        },
        cb
    );
}

export { sendTemplateCreated };

function sendTemplateUpdateFailed(userid, error, template_type, ws_key, tracing_id, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'templateUpdateFailed',
            reason: error.message,
            ECODE: error.ECODE,
            templateName: error.templateName,
            template_type,
            tracing_id,
            ws_key,
            userid,
        },
        cb
    );
}

export { sendTemplateUpdateFailed };

function sendTemplateCreateFailed(userid, error, template_type, ws_key, tracing_id, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'templateCreateFailed',
            reason: error.message,
            ECODE: error.ECODE,
            templateName: error.templateName,
            template_type,
            tracing_id,
            ws_key,
            userid,
        },
        cb
    );
}

export { sendTemplateCreateFailed };

function sendTemplateUpdated(userid, template, template_type, ws_key, tracing_id, cb) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'templateUpdated',
            id: template.id,
            templateName: template.templateName,
            template_type,
            tracing_id,
            ws_key,
            userid,
        },
        cb
    );
}

export { sendTemplateUpdated };

// todo potential large argument
function sendCatCopyComplete(userid, category, ws_key, template, tracing_id, is_template, cb) {
    delete category.permissions;
    if (!cb) {
        cb = is_template;
    }
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key,
            msg: 'categoryCopyComplete',
            userid,
            id: category.id,
            tracing_id,
            category,
            template,
            is_template,
        },
        cb
    );
}

export { sendCatCopyComplete };

// todo potential large argument
function sendProjectCopyComplete(userid, project, ws_key, template, tracing_id, is_template, cb = () => {}) {
    if (project) {
        delete project.permissions;
    }
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key,
            msg: 'projectCopyComplete',
            userid,
            id: project ? project.id : null,
            tracing_id,
            project,
            template,
            is_template,
        },
        cb
    );
}

export { sendProjectCopyComplete };

function sendProjectCreated(options, cb = () => {}) {
    const messageName = 'projectCreated';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    if (!options?.project?.team?.id || !options?.ws_key || !options?.project?.id) {
        cb();
        logWebsocketMessageProcessingError(undefined, 'WSS_037', 'Failed to process message', {
            func: 'sendProjectCreated',
        });
        return;
    }

    const { project, ws_key } = options;
    const team = project.team.id;

    delete project.permissions;

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'projectCreated',
            project_id: project.id,
            project,
            team,
            ws_key,
        },
        cb
    );
}

export { sendProjectCreated };

function sendProjectEdited(userid, project_id, ws_key, cb = () => {}) {
    const messageName = 'projectEdited';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    projectMod._getProject(userid, project_id, { include_removed: false }, (err, project) => {
        if (err || !project) {
            cb();
            logWebsocketMessageProcessingError(err, 'WSS_037', 'Failed to process message', {
                func: 'sendProjectEdited',
            });
            return;
        }

        const team = project.team.id;
        delete project.permissions;

        sendMsgToOtherHosts(
            this?.context,
            {
                msg: 'projectEdited',
                project_id: project.id,
                project,
                team,
                ws_key,
            },
            cb
        );
    });
}

export { sendProjectEdited };

function sendProjectMoved(userid, project_id, position, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key,
            msg: 'projectMoved',
            project_id,
            userid,
            position,
        },
        cb
    );
}

export { sendProjectMoved };

function sendProjectDeleted(options, cb = () => {}) {
    const messageName = 'projectDeleted';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    const { project_id, team, ws_key } = options;

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'projectDeleted',
            project_id,
            team,
            ws_key,
        },
        cb
    );
}

export { sendProjectDeleted };

function sendLeftProject(options, cb = () => {}) {
    const { project_id, userid } = options;

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'leftProject',
            project_id,
            userid,
        },
        cb
    );
}

export { sendLeftProject };

function sendTeamInvite(
    userid,
    team_id,
    team_name,
    email,
    invite_code,
    invitedByName,
    invitedByPicture,
    invitedByColor,
    invitedByInitials,
    avatar,
    cb = () => {}
) {
    const msg = 'teamInvite';

    if (shouldSkipWebsocketV1Message(msg)) {
        cb();
        return;
    }

    sendMsgToOtherHosts(
        this?.context,
        {
            msg,
            userid,
            team: { id: team_id, name: team_name, avatar },
            invitedBy: {
                username: invitedByName,
                color: invitedByColor,
                initials: invitedByInitials,
                profilePicture: cf_sign.getCloudfrontAvatarUrl(invitedByPicture),
            },
        },
        cb
    );
}

export { sendTeamInvite };

function sendTeamInviteAccepted(userid, team_id, accept, cb = () => {}) {
    const msg = 'teamInviteAccepted';

    if (shouldSkipWebsocketV1Message(msg)) {
        cb();
        return;
    }

    sendMsgToOtherHosts(
        this?.context,
        {
            msg,
            userid,
            team_id,
            accept,
        },
        cb
    );
}

export { sendTeamInviteAccepted };

// todo removed from team members
function sendRemovedFromTeam(userid, team_id, ws_key, cb) {
    if (!cb) {
        cb = ws_key;
    }

    const msg = 'teamRemoved';

    if (shouldSkipWebsocketV1Message(msg)) {
        if (cb && typeof cb === 'function') {
            cb();
        }
        return;
    }

    sendMsgToOtherHosts(
        this?.context,
        {
            msg,
            userid,
            team_id,
            ws_key,
        },
        cb
    );
}

export { sendRemovedFromTeam };

function sendWSUserEdited(userid, options, cb = () => {}) {
    const data = { ...options };
    delete data.ws_key;
    delete data.ip;
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'userEdited',
            userid,
            ws_key: options.ws_key,
            ip: options.ip,
            data,
        },
        cb
    );
}

export { sendWSUserEdited };

function sendNoteEdited(userid, note, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'scratchpadNoteEdited',
            userid,
            note,
            ws_key,
        },
        cb
    );
}

export { sendNoteEdited };

function sendSaveContentResponse(options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'saveContentResponse',
            ...options,
        },
        cb
    );
}

export { sendSaveContentResponse };

function sendNoteCreated(userid, note, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'scratchpadNoteCreated',
            userid,
            note,
            ws_key,
        },
        cb
    );
}

export { sendNoteCreated };

function sendRoleChanged(userid, team_id, new_role, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'teamRoleChanged',
            userid,
            team_id,
            new_role,
            ws_key,
        },
        cb
    );
}

export { sendRoleChanged };

function sendNotesDeleted(userid, note_ids, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'scratchpadNotesDeleted',
            userid,
            note_ids,
            ws_key,
        },
        cb
    );
}

export { sendNotesDeleted };

function sendExportDone(userid, team_id, url, type, cb) {
    if (!cb) {
        cb = type;
    }
    const msg = {
        msg: 'exportDone',
        type,
        userid,
        team_id,
        url,
    };
    sendMsgToOtherHosts(this?.context, msg, cb);
}

export { sendExportDone };

function sendTaskTabsAdded(userid, options, cb = () => {}) {
    const { task_ids, ws_key, drafts } = options;

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'tabsAdded',
            userid,
            task_ids,
            drafts,
            ws_key,
        },
        cb
    );
}

export { sendTaskTabsAdded };

function sendTaskTabEdited(userid, options, cb = () => {}) {
    const { task_id, orderindex, ws_key, team_id, draft_data } = options;

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'tabEdited',
            userid,
            task_id,
            orderindex,
            team_id,
            ws_key,
            draft_data,
        },
        cb
    );
}

export { sendTaskTabEdited };

function sendTaskTabsRemoved(userid, options, cb = () => {}) {
    const { task_ids, ws_key } = options;

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'tabsRemoved',
            userid,
            task_ids,
            ws_key,
        },
        cb
    );
}

export { sendTaskTabsRemoved };

function sendTrayTabAdded(userid, options, cb = () => {}) {
    const { id, type, team_id, ws_key } = options;

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'trayTabAdded',
            userid,
            team_id,
            id,
            type,
            ws_key,
        },
        cb
    );
}

export { sendTrayTabAdded };

function sendTrayTabEdited(userid, options, cb = () => {}) {
    const { id, type, team_id, ws_key } = options;

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'trayTabEdited',
            userid,
            team_id,
            id,
            type,
            ws_key,
        },
        cb
    );
}

export { sendTrayTabEdited };

function sendTrayTabRemoved(userid, options, cb = () => {}) {
    const { id, type, team_id, draft, ws_key } = options;

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'trayTabRemoved',
            userid,
            tab: {
                id,
                type,
                team_id,
                draft,
            },
            ws_key,
        },
        cb
    );
}

export { sendTrayTabRemoved };

function sendTasksArchived(team_id, task_ids, ws_key, archived, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'tasksArchived',
            team_id,
            task_ids,
            archived,
            ws_key,
        },
        cb
    );
}

export { sendTasksArchived };

// Array of: {id: <item_id>, subcategory: <subactegory_id>}
function sendTaskDeleted(item_objs, options, cb = () => {}) {
    const messageName = 'taskDeleted';

    // Verify that the message should be processed or not
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    const query = 'SELECT id, color, email, username, profile_picture_key FROM task_mgmt.users WHERE id = $1';
    db.replicaQuery(query, [options.userid], (err, result) => {
        if (err) {
            cb();
            logWebsocketMessageProcessingError(err, 'WSS_038', 'Failed to process message', {
                func: 'sendTaskDeleted',
            });
            return;
        }

        if (!result.rows.length) {
            cb();
            return;
        }

        const [row] = result.rows;

        const user = {
            id: row.id,
            username: row.username,
            email: row.email,
            color: row.color,
            initials: userInitials.getInitials(row.email, row.username),
            profilePicture: cf_sign.getCloudfrontAvatarUrl(row.profile_picture_key),
        };

        const project_query = `
            SELECT categories.project_id,
                   subcategories.id
            FROM task_mgmt.subcategories
                     JOIN task_mgmt.categories
                          ON subcategories.category = categories.id
            WHERE subcategories.id = ANY ($1)
        `;
        const project_params = [item_objs.map(item_obj => item_obj.subcategory)];

        db.replicaQuery(project_query, project_params, (proj_err, project_result) => {
            if (proj_err) {
                cb();
                logWebsocketMessageProcessingError(proj_err, 'WSS_039', 'Failed to process message', {
                    func: 'sendTaskDeleted',
                });
            } else {
                const project_map = {};
                project_result.rows.forEach(project_row => {
                    project_map[project_row.id] = project_row.project_id;
                });

                async.each(
                    item_objs,
                    (item_obj, each_cb) => {
                        if (project_map[item_obj.subcategory]) {
                            sendMsgToOtherHosts(
                                this?.context,
                                {
                                    ws_key: options.ws_key,
                                    msg: 'taskDeleted',
                                    id: item_obj.id,
                                    parent: item_obj.parent,
                                    project_id: project_map[item_obj.subcategory],
                                    task_id: item_obj.id,
                                    user,
                                },
                                each_cb
                            );
                        } else {
                            each_cb();
                        }
                    },
                    () => {
                        cb();
                    }
                );
            }
        });
    });
}

export { sendTaskDeleted };

function sendChecklistCreated(userid, task_id, checklist, options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key: options.ws_key,
            msg: 'checklistCreated',
            task_id,
            checklist,
        },
        cb
    );
}

export { sendChecklistCreated };

function sendChecklistDeleted(userid, task_id, checklist_id, options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key: options.ws_key,
            msg: 'checklistDeleted',
            task_id,
            checklist_id,
        },
        cb
    );
}

export { sendChecklistDeleted };

function sendChecklistEdited(userid, checklist_id, options, cb = () => {}) {
    checklistMod._getChecklists(userid, null, { checklist_id, skip_access: true }, (err, result) => {
        if (err) {
            cb();
            logWebsocketMessageProcessingError(err, 'WSS_040', 'Failed to process message', {
                func: 'sendChecklistEdited',
            });
            return;
        }

        const [checklist] = result.checklists;
        const { task_id } = checklist || {};

        if (!checklist) {
            cb();
            return;
        }

        sendMsgToOtherHosts(
            this?.context,
            {
                ws_key: options.ws_key,
                msg: 'checklistEdited',
                task_id,
                checklist,
            },
            cb
        );
    });
}

export { sendChecklistEdited };

function sendChecklistItemDeleted(userid, task_id, checklist_id, checklist_item_id, options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key: options.ws_key,
            msg: 'checklistItemDeleted',
            task_id,
            checklist_id,
            checklist_item_id,
        },
        cb
    );
}

export { sendChecklistItemDeleted };

function sendChecklistItemMoved(userid, task_id, old_checklist_id, old_parent, checklist_item, options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key: options.ws_key,
            msg: 'checklistItemMoved',
            task_id,
            old_checklist_id,
            old_parent,
            checklist_item,
        },
        cb
    );
}

export { sendChecklistItemMoved };

function sendChecklistItemEdited(
    userid,
    task_id,
    checklist_id,
    old_checklist_parent,
    checklist_item,
    options,
    cb = () => {}
) {
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key: options.ws_key,
            msg: 'checklistItemEdited',
            task_id,
            checklist_id,
            old_checklist_parent,
            checklist_item,
        },
        cb
    );
}

export { sendChecklistItemEdited };

/**
 *
 * @param {number} userid
 * @param {string} ws_key
 * @param {string} favorite_id
 * @param {string} type - 'view' for view and doc, 'page', 'dashboard', 'user'
 *     for all other types
 */
function sendFavoriteCreated(userid, ws_key, favorite, type, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key,
            msg: 'favoriteCreated',
            favorite,
            type,
            userid,
        },
        cb
    );
}

export { sendFavoriteCreated };

/**
 *
 * @param {number} userid
 * @param {string} ws_key
 * @param {string} favorite_id
 * @param {string} type - 'view' for view and doc, 'page', 'dashboard', 'user'
 *     for all other types
 */
function sendFavoriteDeleted(userid, ws_key, favorite_id, type, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key,
            msg: 'favoriteDeleted',
            favorite_id,
            type,
            userid,
        },
        cb
    );
}

export { sendFavoriteDeleted };

/**
 *
 * @param {number} userid
 * @param {string} ws_key
 * @param {string} favorite_id
 * @param {string} type - 'view' for view and doc, 'page', 'dashboard', 'user'
 *     for all other types
 */
function sendFavoriteEdited(userid, ws_key, favorite, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key,
            msg: 'favoriteEdited',
            favorite,
            userid,
        },
        cb
    );
}

export { sendFavoriteEdited };

function sendFavoriteMoved(userid, ws_key, favorite_id, position, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key,
            msg: 'favoriteMoved',
            favorite_id,
            userid,
            position,
        },
        cb
    );
}

export { sendFavoriteMoved };

function sendTasksReordered(team_id, orderindex_map, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            ws_key,
            msg: 'tasksReordered',
            orderindex_map,
            team_id,
        },
        cb
    );
}

export { sendTasksReordered };

// todo potential large argument
function sendCategoriesCreated(userid, options, categories, cb = () => {}) {
    const messageName = 'categoriesCreated';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    categories.forEach(category => {
        delete category.permissions;
    });

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'categoriesCreated',
            project_id: options.project_id,
            categories,
            team_id: options.team_id,
            ws_key: options.ws_key,
            category_id: categories && categories[0] && categories[0].id,
        },
        cb
    );
}

export { sendCategoriesCreated };

function sendCategoriesDeleted(userid, options, category_ids, cb = () => {}) {
    const messageName = 'categoriesDeleted';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    db.replicaQuery(
        'SELECT team FROM task_mgmt.projects, task_mgmt.categories WHERE categories.id = $1 AND categories.project_id = projects.id',
        [category_ids[0]],
        (err, result) => {
            if (result && result.rows && result.rows[0]) {
                const team_id = result.rows[0].team;

                sendMsgToOtherHosts(
                    this?.context,
                    {
                        msg: 'categoriesDeleted',
                        categories: category_ids,
                        team_id,
                    },
                    cb
                );
            } else {
                cb();
            }
        }
    );
}

export { sendCategoriesDeleted };

function sendCategoryEdited(userid, options, category_id, cb = () => {}) {
    const messageName = 'categoryEdited';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    categoryMod._getCategory(userid, category_id, {}, (err, category) => {
        if (err) {
            cb();
            logWebsocketMessageProcessingError(err, 'WSS_058', 'Failed to process message', {
                func: 'sendCategoryEdited',
            });
            return;
        }

        delete category.permissions;
        sendMsgToOtherHosts(
            this?.context,
            {
                msg: 'categoryEdited',
                category,
                team_id: options.team_id,
                project_id: options.project_id,
                category_id: category.id,
                ws_key: options.ws_key,
            },
            cb
        );
    });
}

export { sendCategoryEdited };

function sendCategoryMoved(userid, options, category_id, project_id, orderindex, position, cb = () => {}) {
    const messageName = 'categoryMoved';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    db.replicaQuery(
        'SELECT team FROM task_mgmt.projects, task_mgmt.categories WHERE projects.id = categories.project_id AND categories.id = $1',
        [category_id],
        (err, result) => {
            if (result && result.rows && result.rows[0]) {
                const team_id = result.rows[0].team;
                sendMsgToOtherHosts(
                    this?.context,
                    {
                        msg: 'categoryMoved',
                        category_id,
                        project_id,
                        team_id,
                        orderindex,
                        position,
                        ws_key: options.ws_key,
                    },
                    cb
                );
            } else {
                cb();
            }
        }
    );
}

export { sendCategoryMoved };

// todo potential large argument
function sendSubcategoryCreated(options, cb = () => {}) {
    const messageName = 'subcategoryCreated';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    const { category_id, subcategory, ws_key } = options;
    db.replicaQuery(
        'SELECT team FROM task_mgmt.projects, task_mgmt.categories WHERE categories.id = $1 AND categories.project_id = projects.id',
        [category_id],
        (err, result) => {
            if (!err && result.rows.length > 0) {
                const { team } = result.rows[0];
                delete subcategory.permissions;
                sendMsgToOtherHosts(
                    this?.context,
                    {
                        msg: 'subcategoryCreated',
                        category_id,
                        subcategory,
                        ws_key,
                        team,
                        subcategory_id: subcategory.id,
                    },
                    cb
                );
            } else {
                cb();
            }
        }
    );
}

export { sendSubcategoryCreated };

function sendSubcategoryDeleted(options, cb = () => {}) {
    const messageName = 'subcategoryDeleted';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    const { subcategory_id } = options;
    db.replicaQuery(
        'SELECT team FROM task_mgmt.projects, task_mgmt.categories, task_mgmt.subcategories WHERE subcategories.id = $1 AND categories.id = subcategories.category AND categories.project_id = projects.id',
        [subcategory_id],
        (err, result) => {
            if (!err && result.rows.length > 0) {
                const { team } = result.rows[0];
                sendMsgToOtherHosts(
                    this?.context,
                    {
                        msg: 'subcategoryDeleted',
                        subcategory_id,
                        team,
                    },
                    cb
                );
            } else {
                cb();
            }
        }
    );
}

export { sendSubcategoryDeleted };

// todo potential large argument
function sendSubcategoryEdited(options, cb = () => {}) {
    const messageName = 'subcategoryEdited';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    const { subcategory, ws_key } = options;
    db.replicaQuery(
        'SELECT team FROM task_mgmt.projects, task_mgmt.categories, task_mgmt.subcategories WHERE subcategories.id = $1 AND categories.id = subcategories.category AND categories.project_id = projects.id',
        [subcategory.id],
        (err, result) => {
            if (!err && result.rows.length > 0) {
                const { team } = result.rows[0];
                delete subcategory.permissions;
                sendMsgToOtherHosts(
                    this?.context,
                    {
                        msg: 'subcategoryEdited',
                        subcategory,
                        team,
                        ws_key,
                        subcategory_id: subcategory.id,
                    },
                    cb
                );
            } else {
                cb();
            }
        }
    );
}

export { sendSubcategoryEdited };

// todo potential large argument
function sendSubcategoryMoved(options, cb = () => {}) {
    const messageName = 'subcategoryMoved';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    const { subcategory_id, category, position, ws_key } = options;
    db.replicaQuery(
        'SELECT team, project_id FROM tasK_mgmt.projects, task_mgmt.categories, task_mgmt.subcategories WHERE subcategories.id = $1 AND categories.id = subcategories.category AND categories.project_id = projects.id',
        [subcategory_id],
        (err, result) => {
            if (!err && result.rows.length > 0) {
                const { team, project_id } = result.rows[0];
                sendMsgToOtherHosts(
                    this?.context,
                    {
                        msg: 'subcategoryMoved',
                        subcategory_id,
                        category,
                        position,
                        team,
                        project_id,
                        ws_key,
                    },
                    cb
                );
            } else {
                cb();
            }
        }
    );
}

export { sendSubcategoryMoved };

function sendViewingViewUnset(ws_uuid, ws_key, userid, view_id, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'viewingViewUnset',
            ws_uuid,
            ws_key,
            userid,
            view_id,
        },
        cb
    );
}

export { sendViewingViewUnset };

function sendViewingViewSet(ws_uuid, ws_key, userid, view_id, cb = () => {}) {
    _getUsers([userid], (err, result) => {
        if (result && result[userid]) {
            sendMsgToOtherHosts(
                this?.context,
                {
                    msg: 'viewingViewSet',
                    ws_uuid,
                    ws_key,
                    userid,
                    user: result[userid],
                    view_id,
                },
                cb
            );
        } else {
            cb(err);
        }
    });
}

export { sendViewingViewSet };

function sendCommentingViewSet(ws_uuid, ws_key, userid, view_id, commenting, cb) {
    _getUsers([userid], (err, result) => {
        if (result && result[userid]) {
            sendMsgToOtherHosts(
                this?.context,
                {
                    msg: 'commentingViewSet',
                    ws_uuid,
                    ws_key,
                    userid,
                    user: result[userid],
                    view_id,
                    commenting,
                },
                cb
            );
        } else {
            cb(err);
        }
    });
}

export { sendCommentingViewSet };

function sendViewingPageUnset(ws_uuid, ws_key, userid, view_id, page_id, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'viewingPageUnset',
            ws_uuid,
            ws_key,
            userid,
            view_id,
            page_id,
        },
        cb
    );
}

export { sendViewingPageUnset };

function sendViewingPageSet(ws_uuid, ws_key, userid, view_id, page_id, cb = () => {}) {
    _getUsers([userid], (err, result) => {
        if (result?.[userid]) {
            sendMsgToOtherHosts(
                this?.context,
                {
                    msg: 'viewingPageSet',
                    ws_uuid,
                    ws_key,
                    userid,
                    user: result[userid],
                    view_id,
                    page_id,
                },
                cb
            );
        } else {
            cb(err);
        }
    });
}

export { sendViewingPageSet };

function sendCommentingPageSet(ws_uuid, ws_key, userid, view_id, page_id, commenting, cb) {
    _getUsers([userid], (err, result) => {
        if (result?.[userid]) {
            sendMsgToOtherHosts(
                this?.context,
                {
                    msg: 'commentingPageSet',
                    ws_uuid,
                    ws_key,
                    userid,
                    user: result[userid],
                    view_id,
                    page_id,
                    commenting,
                },
                cb
            );
        } else {
            cb(err);
        }
    });
}

export { sendCommentingPageSet };

function sendTakeoverPage(ws_uuid, ws_key, userid, view_id, page_id, cb = () => {}) {
    _getUsers([userid], (err, result) => {
        if (result?.[userid]) {
            sendMsgToOtherHosts(
                this?.context,
                {
                    msg: 'takeoverPage',
                    ws_uuid,
                    ws_key,
                    user: result[userid],
                    view_id,
                    page_id,
                },
                cb
            );
        } else {
            cb(err);
        }
    });
}

export { sendTakeoverPage };

function sendTakeoverPageAck(ws_uuid, ws_key, userid, view_id, page_id, cb = () => {}) {
    _getUsers([userid], (err, result) => {
        if (result?.[userid]) {
            sendMsgToOtherHosts(
                this?.context,
                {
                    msg: 'takeoverPageAck',
                    ws_uuid,
                    ws_key,
                    user: result[userid],
                    view_id,
                    page_id,
                },
                cb
            );
        } else {
            cb(err);
        }
    });
}

export { sendTakeoverPageAck };

function sendUserOnline(ws_uuid, ws_key, userid, team_id, online, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'userOnline',
            ws_uuid,
            ws_key,
            userid,
            team_id,
            online,
        },
        cb
    );
}

export { sendUserOnline };

function sendViewingUnsetOpts(options, cb = () => {}) {
    const { ws_uuid, ws_key, userid, task_id, team_id } = options;
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'viewingUnset',
            ws_uuid,
            ws_key,
            userid,
            task_id,
            team_id,
        },
        cb
    );
}

export { sendViewingUnsetOpts };

function sendViewingSetOpts(options, cb = () => {}) {
    const { ws_uuid, ws_key, userid, task_id, team_id } = options;

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'viewingSet',
            ws_uuid,
            ws_key,
            userid,
            task_id,
            team_id,
        },
        cb
    );
}

export { sendViewingSetOpts };

function sendViewingUnset(ws_uuid, ws_key, userid, task_id, cb = () => {}) {
    sendViewingUnsetOpts({ ws_uuid, ws_key, userid, task_id }, cb);
}

export { sendViewingUnset };

function sendViewingSet(ws_uuid, ws_key, userid, task_id, cb = () => {}) {
    sendViewingSetOpts({ ws_uuid, ws_key, userid, task_id }, cb);
}

export { sendViewingSet };

function sendCommentingSet(ws_uuid, ws_key, userid, task_id, commenting, thread_id, cb) {
    if (!cb) {
        cb = thread_id;
    }
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'commentingSet',
            ws_uuid,
            ws_key,
            userid,
            task_id,
            commenting,
            thread_id,
        },
        cb
    );
}

export { sendCommentingSet };

function sendMemberAction(resource, method, id, member, ws_key, isOwner, cb) {
    if (!cb) {
        cb = isOwner;
    }
    method = _.capitalize(method);
    resource = resource.toLowerCase();

    if (!['Added', 'Edited', 'Removed'].includes(method)) {
        cb();
        return;
    }

    if (!['task', 'subcategory', 'category', 'project', 'view'].includes(resource)) {
        cb();
        return;
    }

    const name = toBoolean(isOwner) ? 'Owner' : 'Member';

    const member_userid = method === 'Removed' || !member.user ? member : member.user.id;

    /*
      Possible Messages:
          - taskMemberAdded
          - taskMemberEdited
          - taskMemberRemoved
          - taskOwnerEdited
          - subcategoryMemberAdded
          - subcategoryMemberEdited
          - subcategoryMemberRemoved
          - subcategoryOwnerEdited
          - categoryMemberAdded
          - categoryMemberEdited
          - categoryMemberRemoved
          - categoryOwnerEdited
          - projectMemberAdded
          - projectMemberEdited
          - projectMemberRemoved
          - projectOwnerEdited
          - viewMemberAdded
          - viewMemberEdited
          - viewMemberRemoved
   */
    const member_msg = {
        msg: `${resource}${name}${method}`,
        [`${resource}_id`]: id,
        member,
        ws_key,
    };

    sendMsgToOtherHosts(this?.context, member_msg, () => {});

    if (method !== 'Added') {
        cb();
        return;
    }

    const getterFunc = {
        task(userid, task_id, getterCb) {
            _getItem(userid, task_id, { skipPosition: true, skip_weights: true, replica: true }, getterCb);
        },

        subcategory(userid, subcategory_id, getterCb) {
            subcatMod._getSubcategory(userid, subcategory_id, {}, getterCb);
        },

        category(userid, category_id, getterCb) {
            categoryMod._getCategory(userid, category_id, {}, getterCb);
        },

        project(userid, project_id, getterCb) {
            projectMod._getProject(userid, project_id, {}, getterCb);
        },

        view(userid, view_id, getterCb) {
            viewMod._getView(userid, view_id, {}, getterCb);
        },
    }[resource];

    getterFunc(member_userid, id, (err, result) => {
        if (err) {
            cb();
            logWebsocketMessageProcessingError(err, 'WSS_059', 'Failed to process message', {
                func: 'sendMemberAction',
            });
            return;
        }

        const removed = method === 'Removed';

        /*
        Possible Messages:
            - taskAccessChanged
            - subcategoryAccessChanged
            - categoryAccessChanged
            - projectAccessChanged
            - viewAccessChanged
     */
        const access_msg = {
            msg: `${resource}AccessChanged`,
            added: !removed,
            removed,
            [resource]: result,
            userid: member_userid,
            member,
            ws_key,
        };

        sendMsgToOtherHosts(this?.context, access_msg, cb);
    });
}

export { sendMemberAction };

/**
 * Sending ws messages to other host after delete task access
 * @param { string } task_id
 * @param { string } ws_key
 * @param { boolean } isPrivate
 * @param { function } cb
 */
function sendDeleteTaskAccess(task_id, isPrivate, ws_key, cb = () => {}) {
    const msg = {
        msg: 'taskAccessDeleted',
        task_id,
        private: isPrivate,
        ws_key,
    };

    sendMsgToOtherHosts(this?.context, msg, cb);
}

export { sendDeleteTaskAccess };

function sendTaskPrivacyChange(task_id, isPrivate, ws_key, cb = () => {}) {
    async.parallel(
        {
            team(para_cb) {
                const query = `
                    SELECT projects.team
                    FROM task_mgmt.items,
                         task_mgmt.subcategories,
                         task_mgmt.categories,
                         task_mgmt.projects
                    WHERE items.id = $1
                      AND items.subcategory = subcategories.id
                      AND subcategories.category = categories.id
                      AND categories.project_id = projects.id`;

                db.replicaQuery(query, [task_id], (err, result) => {
                    if (err) {
                        para_cb(logWSError(err, 'WSS_060'));
                    } else if (!result.rows.length) {
                        para_cb(logWSError('Task Not Found', 'WSS_061', 404));
                    } else {
                        para_cb(null, result.rows[0].team);
                    }
                });
            },

            members(para_cb) {
                const query = 'SELECT userid FROM task_mgmt.task_members WHERE task_id = $1';
                db.replicaQuery(query, [task_id], (err, result) => {
                    if (err) {
                        para_cb(logWSError(err, 'WSS_062'));
                    } else {
                        para_cb(
                            null,
                            result.rows.map(row => row.userid)
                        );
                    }
                });
            },
        },
        (err, result) => {
            if (err) {
                cb();
                logWebsocketMessageProcessingError(err, 'WSS_061', 'Failed to process message', {
                    func: 'sendTaskPrivacyChange',
                });
                return;
            }

            const msg = {
                msg: 'taskPrivacyChange',
                task_id,
                team_id: result.team,
                members: result.members,
                private: isPrivate,
                ws_key,
            };

            sendMsgToOtherHosts(this?.context, msg, cb);
        }
    );
}

export { sendTaskPrivacyChange };

function sendSubcategoryPrivacyChange(subcategory_id, isPrivate, ws_key, cb = () => {}) {
    async.parallel(
        {
            team(para_cb) {
                const query = `
                    SELECT projects.team
                    FROM task_mgmt.subcategories,
                         task_mgmt.categories,
                         task_mgmt.projects
                    WHERE subcategories.id = $1
                      AND subcategories.category = categories.id
                      AND categories.project_id = projects.id`;

                db.replicaQuery(query, [subcategory_id], (err, result) => {
                    if (err) {
                        para_cb(logWSError(err, 'WSS_062'));
                    } else if (!result.rows.length) {
                        para_cb(logWSError('Subcategory not found', 'WSS_063', 404));
                    } else {
                        para_cb(null, result.rows[0].team);
                    }
                });
            },

            members(para_cb) {
                const query = 'SELECT userid FROM task_mgmt.subcategory_members WHERE subcategory = $1';
                db.replicaQuery(query, [subcategory_id], (err, result) => {
                    if (err) {
                        para_cb(logWSError(err, 'WSS_063'));
                    } else {
                        para_cb(
                            null,
                            result.rows.map(row => row.userid)
                        );
                    }
                });
            },
        },
        (err, result) => {
            if (err) {
                cb();
                logWebsocketMessageProcessingError(err, 'WSS_064', 'Failed to process message', {
                    func: 'sendSubcategoryPrivacyChange',
                });
                return;
            }

            const msg = {
                msg: 'subcategoryPrivacyChange',
                subcategory_id,
                team_id: result.team,
                members: result.members,
                private: isPrivate,
                ws_key,
            };

            sendMsgToOtherHosts(this?.context, msg, cb);
        }
    );
}

export { sendSubcategoryPrivacyChange };

function sendCategoryPrivacyChange(category_id, isPrivate, ws_key, cb = () => {}) {
    async.parallel(
        {
            team(para_cb) {
                const query = `
                    SELECT projects.team
                    FROM task_mgmt.categories,
                         task_mgmt.projects
                    WHERE categories.id = $1
                      AND categories.project_id = projects.id`;

                db.replicaQuery(query, [category_id], (err, result) => {
                    if (err) {
                        para_cb(logWSError(err, 'WSS_065'));
                    } else if (!result.rows.length) {
                        para_cb(logWSError('Not found', 'WSS_066', 404));
                    } else {
                        para_cb(null, result.rows[0].team);
                    }
                });
            },

            members(para_cb) {
                const query = 'SELECT userid FROM task_mgmt.category_members WHERE category = $1';

                db.replicaQuery(query, [category_id], (err, result) => {
                    if (err) {
                        para_cb(logWSError(err, 'WSS_067'));
                    } else {
                        para_cb(
                            null,
                            result.rows.map(row => row.userid)
                        );
                    }
                });
            },
        },
        (err, result) => {
            if (err) {
                cb();
                logWebsocketMessageProcessingError(err, 'WSS_064', 'Failed to process message', {
                    func: 'sendCategoryPrivacyChange',
                });
                return;
            }

            const msg = {
                msg: 'categoryPrivacyChange',
                category_id,
                team_id: result.team,
                members: result.members,
                private: isPrivate,
                ws_key,
            };

            sendMsgToOtherHosts(this?.context, msg, cb);
        }
    );
}

export { sendCategoryPrivacyChange };

function sendProjectPrivacyChange(project_id, isPrivate, ws_key, cb = () => {}) {
    async.parallel(
        {
            team(para_cb) {
                const query = 'SELECT team FROM task_mgmt.projects WHERE id = $1';
                db.replicaQuery(query, [project_id], (err, result) => {
                    if (err) {
                        para_cb(logWSError(err, 'WSS_068'));
                    } else if (!result.rows.length) {
                        para_cb(logWSError('Not found', 'WSS_069', 404));
                    } else {
                        para_cb(null, result.rows[0].team);
                    }
                });
            },

            members(para_cb) {
                const query = 'SELECT userid FROM task_mgmt.project_members WHERE project_id = $1';
                db.replicaQuery(query, [project_id], (err, result) => {
                    if (err) {
                        para_cb(logWSError(err, 'WSS_070'));
                    } else {
                        para_cb(
                            null,
                            result.rows.map(row => row.userid)
                        );
                    }
                });
            },
        },
        (err, result) => {
            if (err) {
                cb();
                logWebsocketMessageProcessingError(err, 'WSS_071', 'Failed to process message', {
                    func: 'sendProjectPrivacyChange',
                });
                return;
            }

            const msg = {
                msg: 'projectPrivacyChange',
                project_id,
                team_id: result.team,
                members: result.members,
                private: isPrivate,
                ws_key,
            };

            sendMsgToOtherHosts(this?.context, msg, cb);
        }
    );
}

export { sendProjectPrivacyChange };

function sendPageUpdated(userid, page, options, cb = () => {}) {
    const { view_id, id: page_id } = page;
    const { ws_key, content_changed } = options;

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'pageUpdated',
            userid,
            page_id,
            view_id,
            page,
            ws_key,
            content_changed,
        },
        cb
    );
}

export { sendPageUpdated };

function sendPageCreated(userid, page, options, cb = () => {}) {
    const { view_id, id: page_id } = page;
    const { ws_key } = options;

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'pageCreated',
            userid,
            page_id,
            view_id,
            page,
            ws_key,
        },
        cb
    );
}

export { sendPageCreated };

function sendPageDeleted(userid, view_id, page_id, options, cb = () => {}) {
    const { ws_key } = options;

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'pageDeleted',
            userid,
            page_id,
            view_id,
            ws_key,
        },
        cb
    );
}

export { sendPageDeleted };

function sendReminderCreated(reminder_id, options, cb) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'reminderCreated',
            reminder_id,
            ws_key: options.ws_key,
            userids: options.userids,
        },
        cb
    );
}

export { sendReminderCreated };

function sendReminderEdited(reminder_id, options, cb) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'reminderEdited',
            reminder_id,
            ws_key: options.ws_key,
            userids: options.userids,
        },
        cb
    );
}

export { sendReminderEdited };

function sendReminderDeleted(reminder_id, options, cb) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'reminderDeleted',
            reminder_id,
            ws_key: options.ws_key,
            userids: options.userids,
        },
        cb
    );
}

export { sendReminderDeleted };

function sendViewCreated(view_id, parent_id, parent_type, ws_key, cb) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'viewCreated',
            view_id,
            parent: parent_id,
            parent_type: parseInt(parent_type, 10),
            ws_key,
        },
        cb
    );
}

export { sendViewCreated };

function sendViewDeleted(view_id, parent_id, parent_type, ws_key, cb) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'viewDeleted',
            view_id,
            parent: parent_id,
            parent_type: parseInt(parent_type, 10),
            ws_key,
        },
        cb
    );
}

export { sendViewDeleted };

function sendViewEdited(view_id, parent_id, parent_type, userid, ws_key, cb) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'viewUpdated',
            view_id,
            parent: parent_id,
            parent_type: parseInt(parent_type, 10),
            ws_key,
            userid,
        },
        cb
    );
}

export { sendViewEdited };

function sendFieldValueUpdated(task_id, field_id, options, cb) {
    const { hist_id, additional_task_ids } = options;

    if (!hist_id) {
        cb();
        return;
    }

    const hist_ids = [hist_id];

    _getHistoryItems(hist_ids, {}, (err, result) => {
        if (err) {
            cb();
            logWebsocketMessageProcessingError(err, 'WSS_084', 'Failed to process message', {
                func: 'sendFieldValueUpdated',
            });
            return;
        }

        sendMsgToOtherHosts(
            this?.context,
            {
                msg: 'fieldValueUpdated',
                task_id,
                field_id,
                hist_id,
                task_ids: additional_task_ids,
                hist_item: result.history[0],
                ws_key: options.ws_key,
                related_task: options.related_task,
            },
            cb
        );
    });
}

export { sendFieldValueUpdated };

/**
 * Sending ws messages to other host after changing comment reaction
 * @param { number } userid
 * @param { string } ws_key
 * @param { string } comment_id
 * @param { number } type - COMMENT_TYPE(2) || TASK_TYPE(1)
 * @param { string } reaction
 * @param { boolean } deleted
 * @param { function } cb
 * @param { string | number } parent
 */
async function sendReactionUpdated(userid, comment_id, parent, type, ws_key, reaction, deleted, cb = () => {}) {
    const commentParentAndUser = { parent };

    const getTaskParentId = async () => {
        const query = `SELECT parent
                       FROM task_mgmt.comments
                       WHERE id = $1
                         AND type = ${TASK_TYPE}`;
        const params = [parent];
        if (type !== COMMENT_TYPE) {
            return null;
        }
        const [row] = (await db.promiseReplicaQuery(query, params)).rows;
        if (row) {
            return row.parent;
        }

        return null;
    };

    const [taskParent, usersKeyedById] = await Promise.all([getTaskParentId(), _promiseGetUsers([userid])]);

    if (taskParent) {
        commentParentAndUser.parent = taskParent;
    }

    if (usersKeyedById && usersKeyedById[userid]) {
        commentParentAndUser.user = usersKeyedById[userid];
    }

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'reactionUpdated',
            userid,
            comment_id,
            ws_key,
            parent: commentParentAndUser.parent,
            reaction,
            user: commentParentAndUser.user,
            deleted,
            type,
        },
        cb
    );
}

export { sendReactionUpdated };

function sendFieldMessage(context, msg, field_id, options, cb) {
    const query = `SELECT team_id
                   FROM task_mgmt.fields
                   WHERE id = $1`;
    const params = [field_id];

    db.replicaQuery(query, params, (err, result) => {
        if (err) {
            logWSError(err, 'WSS_086');
            cb();
            return;
        }

        const { team_id } = result.rows[0] || {};

        if (!team_id) {
            cb();
            return;
        }

        sendMsgToOtherHosts(
            context,
            {
                msg,
                field_id,
                team_id,
                ws_key: options.ws_key,
            },
            cb
        );
    });
}

function sendFieldCreated(field_id, options, cb) {
    sendFieldMessage(this?.context, 'fieldCreated', field_id, options, cb);
}

export { sendFieldCreated };

function sendFieldUpdated(field_id, options, cb) {
    sendFieldMessage(this?.context, 'fieldUpdated', field_id, options, cb);
}

export { sendFieldUpdated };

function sendFieldDeleted(field_id, options, cb) {
    sendFieldMessage(this?.context, 'fieldDeleted', field_id, options, cb);
}

export { sendFieldDeleted };

let registeredHostAtLeastOnce = false;

async function registerHost() {
    if (registeredHostAtLeastOnce) {
        logger.warn({ msg: 'Already started host registration heartbeat loop.' });
        return;
    }

    const shutdownListener = configureHostRegistrationListener({ host, lb_url: config.lb_url });

    processShutdownController.shutdownRequested$.subscribe(() => {
        shutdownListener();
    });

    registeredHostAtLeastOnce = true;
}

export { registerHost };

function sendTimeTrackerStarted(userid, team_id, time_entry, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'timeTrackerStarted',
            userid,
            team_id,
            time_entry,
            ws_key,
        },
        cb
    );
}

export { sendTimeTrackerStarted };

function sendTimeTrackerStopped(userid, team_id, time_entry, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'timeTrackerStopped',
            userid,
            team_id,
            time_entry,
            ws_key,
        },
        cb
    );
}

export { sendTimeTrackerStopped };

function sendTimeTrackerCreated(userid, team_id, time_entry, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'timeTrackerCreated',
            userid,
            team_id,
            time_entry,
            ws_key,
        },
        cb
    );
}

export { sendTimeTrackerCreated };

function sendTimeTrackerRemoved(userid, team_id, time_entry, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'timeTrackerRemoved',
            userid,
            team_id,
            time_entry,
            ws_key,
        },
        cb
    );
}

export { sendTimeTrackerRemoved };

function sendTimeTrackerUpdated(userid, team_id, time_entry, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'timeTrackerUpdated',
            userid,
            team_id,
            time_entry,
            ws_key,
        },
        cb
    );
}

export { sendTimeTrackerUpdated };

function sendDocArchived(doc_id, parent_id, parent_type, archived, cb = () => {}) {
    const message = {
        msg: 'documentArchived',
        view_id: doc_id,
        parent: parent_id,
        parent_type: parseInt(parent_type, 10),
        archived,
    };

    sendMsgToOtherHosts(this?.context, message, cb);
}

export { sendDocArchived };

function sendPageArchived(doc_id, page_id, archived, cb = () => {}) {
    const message = {
        msg: 'pageArchived',
        view_id: doc_id,
        page_id,
        archived,
    };

    sendMsgToOtherHosts(this?.context, message, cb);
}

export { sendPageArchived };

function sendDocumentTagRemoved(team_id, doc_ids, tag_object, ws_key, cb = () => {}) {
    if (!doc_ids) {
        cb();
        return;
    }

    async.each(
        doc_ids,
        async (doc_id, each_cb) => {
            sendMsgToOtherHosts(
                this?.context,
                {
                    ws_key,
                    msg: 'documentTagRemoved',
                    view_id: doc_id,
                    team_id,
                    tag: tag_object,
                },
                each_cb
            );
        },
        () => {
            cb();
        }
    );
}

export { sendDocumentTagRemoved };

function sendDocumentTagAdded(team_id, doc_ids, tag_object, ws_key, cb = () => {}) {
    if (!doc_ids) {
        cb();
        return;
    }

    async.each(
        doc_ids,
        async (doc_id, each_cb) => {
            sendMsgToOtherHosts(
                this?.context,
                {
                    // ws_key,
                    msg: 'documentTagAdded',
                    view_id: doc_id,
                    team_id,
                    tag: tag_object,
                    ws_key,
                },
                each_cb
            );
        },
        () => {
            cb();
        }
    );
}

export { sendDocumentTagAdded };

function sendDocumentTagCreated(team_id, doc_id, tag_object, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'documentTagCreated',
            view_id: doc_id,
            team_id,
            tag: tag_object,
            ws_key,
        },
        cb
    );
}

export { sendDocumentTagCreated };

function sendDocumentTagDeleted(team_id, tag_object, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'documentTagDeleted',
            team_id,
            tag: tag_object,
            ws_key,
        },
        cb
    );
}

export { sendDocumentTagDeleted };

function sendDocumentTagUpdated(team_id, tag_object, ws_key, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'documentTagUpdated',
            team_id,
            tag: tag_object,
            ws_key,
        },
        cb
    );
}

export { sendDocumentTagUpdated };

function sendPageRelationshipCreated(
    user_id,
    view_id,
    date_created,
    id,
    page_id,
    link_type,
    other_id,
    other_type,
    item,
    ws_key,
    cb = () => {}
) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'pageRelationshipCreated',
            user_id,
            view_id,
            date_created,
            id,
            page_id,
            link_id: page_id,
            link_type,
            other_id,
            other_type,
            item,
            ws_key,
        },
        cb
    );
}

export { sendPageRelationshipCreated };

function sendPageRelationshipDeleted(
    user_id,
    view_id,
    date_created,
    id,
    page_id,
    link_type,
    other_id,
    other_type,
    item,
    ws_key,
    cb = () => {}
) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'pageRelationshipDeleted',
            user_id,
            ws_key,
            view_id,
            date_created,
            id,
            page_id,
            link_id: page_id,
            link_type,
            other_id,
            other_type,
            item,
        },
        cb
    );
}

export { sendPageRelationshipDeleted };

function sendPageLinkUpdated(view_id, page_id, links, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'pageLinkUpdated',
            view_id,
            page_id,
            links,
        },
        cb
    );
}

export { sendPageLinkUpdated };

function sendGoalCreated(item_id, options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            id: item_id,
            ws_key: options.ws_key,
            msg: 'goalCreated',
            goal_id: item_id,
            userid: options.userid,
            team_id: options.team_id,
        },
        cb
    );
}

export { sendGoalCreated };

function sendGoalDeleted(item_id, options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            id: item_id,
            ws_key: options.ws_key,
            msg: 'goalDeleted',
            goal_id: item_id,
            userid: options.userid,
            team_id: options.team_id,
        },
        cb
    );
}

export { sendGoalDeleted };

function sendGoalUpdated(item_id, options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            id: item_id,
            ws_key: options.ws_key,
            msg: 'goalUpdated',
            goal_id: item_id,
            userid: options.userid,
            team_id: options.team_id,
            date_updated: options.date_updated,
        },
        cb
    );
}

export { sendGoalUpdated };

function sendGoalFolderCreated(item_id, options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            id: item_id,
            ws_key: options.ws_key,
            msg: 'goalFolderCreated',
            goal_folder_id: item_id,
            userid: options.userid,
            team_id: options.team_id,
        },
        cb
    );
}

export { sendGoalFolderCreated };

function sendGoalFolderUpdated(item_id, options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        _.pickBy(
            {
                id: item_id,
                ws_key: options.ws_key,
                msg: 'goalFolderUpdated',
                goal_folder_id: item_id,
                userid: options.userid,
                team_id: options.team_id,
                date_updated: options.date_updated,
                rem_goals: options.rem_goals,
                add_goals: options.add_goals,
            },
            value => value !== null && value !== undefined
        ),
        cb
    );
}

export { sendGoalFolderUpdated };

function sendGoalFolderDeleted(item_id, options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            id: item_id,
            ws_key: options.ws_key,
            msg: 'goalFolderDeleted',
            goal_folder_id: item_id,
            userid: options.userid,
            team_id: options.team_id,
        },
        cb
    );
}

export { sendGoalFolderDeleted };

async function fetchKeyResultId(keyResultId) {
    try {
        return await getKeyResultTeamId(keyResultId);
    } catch (err) {
        logger.error({
            msg: 'Failed to get team id',
            ECODE: GoalErrorCodes.KeyResultTeamId,
            err,
        });
    }
    return null;
}

async function sendKeyResultCreated(item_id, options, cb = () => {}) {
    const team_id = await fetchKeyResultId(item_id);

    sendMsgToOtherHosts(
        this?.context,
        {
            id: item_id,
            ws_key: options.ws_key,
            msg: 'keyResultCreated',
            key_result_id: item_id,
            userid: options.userid,
            goal_id: options.goal_id,
            team_id,
        },
        cb
    );
}

export { sendKeyResultCreated };

async function sendKeyResultUpdated(item_id, options, cb = () => {}) {
    const team_id = await fetchKeyResultId(item_id);

    sendMsgToOtherHosts(
        this?.context,
        {
            id: item_id,
            ws_key: options.ws_key,
            msg: 'keyResultUpdated',
            key_result_id: item_id,
            userid: options.userid,
            date_updated: options.date_updated,
            goal_id: options.goal_id,
            team_id,
        },
        cb
    );
}

export { sendKeyResultUpdated };

async function sendKeyResultDeleted(item_id, options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            id: item_id,
            ws_key: options.ws_key,
            msg: 'keyResultDeleted',
            key_result_id: item_id,
            team_id: options.team_id,
            goal_id: options.goal_id,
        },
        cb
    );
}

export { sendKeyResultDeleted };

async function sendInboxMessageToUser(inboxMessage, cb = () => {}) {
    if (!inboxMessage?.msg) {
        inboxMessage.msg = 'inboxMessageToUser';
    }
    sendMsgToOtherHosts(this?.context, inboxMessage, cb);
}

export { sendInboxMessageToUser };

function sendTagUpdated(tag_name, options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        _.omitBy(
            {
                msg: 'tagUpdated',
                tag_name,
                project_id: options.project_id,
                ws_key: options.ws_key,
                tag_fg: options.tag_fg,
                tag_bg: options.tag_bg,
                new_tag_name: options.new_name,
            },
            _.isNil
        ),
        cb
    );
}

export { sendTagUpdated };

function sendWorkspaceMaintenanceStarted(workspaceId, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'workspaceMaintenanceStarted',
            team_id: workspaceId,
        },
        cb
    );
}

export { sendWorkspaceMaintenanceStarted };

function sendWorkspaceMaintenanceEnded(workspaceId, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        {
            msg: 'workspaceMaintenanceEnded',
            team_id: workspaceId,
        },
        cb
    );
}

export { sendWorkspaceMaintenanceEnded };

function sendAdminV3Updated(workspaceId, options, cb = () => {}) {
    sendMsgToOtherHosts(
        this?.context,
        _.omitBy(
            {
                msg: 'adminV3Updated',
                team_id: workspaceId,
                changed_by: options.changed_by,
                adminV3: options.adminV3,
                adminV2: options.adminV2,
            },
            _.isNil
        ),
        cb
    );
}

export { sendAdminV3Updated };

function sendAutomationsThrottled(workspaceId, cb = () => {}) {
    const messageName = 'automationsThrottled';
    if (shouldSkipWebsocketV1Message(messageName)) {
        cb();
        return;
    }

    sendMsgToOtherHosts(
        this?.context,
        {
            msg: messageName,
            team_id: workspaceId,
        },
        cb
    );
}

export { sendAutomationsThrottled };
