/* eslint-disable max-len */
import config from 'config';

import _ from 'lodash';
import { parseQuillContent } from '@time-loop/common-utils';
import { ObjectType, OperationType } from '@time-loop/ovm-object-version';

import {
    getAssignedToUserViewId,
    getSprintCategoryViewId,
    getEditViewPermissionsForOptions,
    validateLocationlessChannelNameAvailable,
} from '@clickup-legacy/models/views/viewsUtils';

import { getChatViewExtra } from '@clickup/chat/domain';
import uuid from 'node-uuid';
import { generateBoardExtra } from '@clickup-legacy/models/views/utils/generate-board-extra';
import { generateListExtra } from '@clickup-legacy/models/views/utils/generate-list-extra';
import { GlobalTableMigrationBatchId } from '@clickup-legacy/utils/pg/interfaces/GlobalDatabaseByBatchConfig';
import { formViewEntitlement } from '@clickup-legacy/libs/entitlements/helpers/formViewEntitlement';
import { isConversationView, isConversationDMView } from '@clickup/views/utils';
import {
    loadTemplatesUserUsageData,
    applyTemplatesPaginationByUsedCountUsingName,
} from '@clickup-legacy/models/templates/templateUserUsedCountHelper';
import { authzVerboseLogging } from '@clickup-legacy/models/integrations/split/squadTreatments/accessManagementTreatments';
import {
    getRoleValidationConfig,
    isLimitedMemberEnabledAsync,
} from '@clickup-legacy/models/team/helpers/roleValidationHelper';
import { getIndividualUserPermissions, isExternalGuest, getRoleKey } from '@clickup/utils/authorization';
import { AuthorizationTrace } from '@clickup/utils/authorization/models';
import { isNewCommentsDbEnabledForWorkspace } from '@clickup-legacy/utils/comment/db';
import { getAccessInfoCache } from '@clickup-legacy/utils/access/services/authorization/instances';
import { isParentedCard, checkAccessToParentedCard } from '@clickup-legacy/models/dashboard/cards/cards-permissions';
import { mapParent } from '@clickup-legacy/models/dashboard/cards/map-parent';
import { cardsEverywhere } from '@clickup-legacy/models/integrations/split/squadTreatments/cardsTreatments';
import { getWidgetParentInfo } from '@clickup-legacy/models/dashboard/widgets/datastores/widgets';
import { createAgentUser } from '@clickup-legacy/models/views/ai/createAgentUser';
import { getUserFromDecodedToken } from '@clickup-legacy/utils/auth/services/userIdProvider';
import { shouldSetParentTypeTeamForDocViews } from '../integrations/split/squadTreatments/userPlatformTreatments';
import * as async from '../../utils/asyncHelper';
import * as access from '../../utils/access2';
import * as cf_sign from '../../utils/cf_sign';
import { codoxTriggerPermissionCheck } from '../integrations/codox/codoxHelpers';
import * as db from '../../utils/db';
import { client as redis } from '../../utils/redis';
import * as elasticProducer from '../elastic/producer';
import { getUserFilterSettings, putUserFilterSettings } from '../user/userSettingsService';
import * as userInitials from '../../utils/userInitials';
import * as prettyID from '../../utils/pretty_ids';
import * as sqs from '../../utils/v1-ws-message-sending'; // eslint-disable-line import/no-cycle
import * as encrypt from '../../utils/encrypt';
import { ClickUpTracer } from '../../utils/tracer';
import { checkViewReadAccess } from './whiteboard/checkViewReadAccess';
import { formatUser } from '../user/userProvider';
import * as projectMod from '../project/CRUD';
import { replaceDocsInContent, replaceAttachmentsInContent, replaceSyncBlocksInContent } from '../../utils/quill';
import { invalidateCachedTeamDocCounts } from './documents/helpers/documentHelpers';
import { getViewParentSettings, updateViewParentSettings } from './view_parent_settings/viewParentSettingsService';
import { queryPageSettings } from './pages/datastores/getPageDataStore';
import { getViewPages, getMappedDocSettings } from './pages/services/getPageService';
import { createPage } from './pages/services/createPageService';
import { preparePageInsertQuery, prepareAuthorInsertQuery } from './pages/factories/createPageQueryFactory';

import {
    prepareDocSettingsUpdateQueries,
    prepareSinglePresentationDetailsUpdateQuery,
} from './pages/factories/updatePageQueryFactory';

import { updateDocSettingsQuery } from './documents/datastores/documentSettingsDatastore';
import {
    enableStaticViewsEndpoint,
    shouldSetShowRecurringTasksToFalseForFreePlan,
    shouldUseMasterAfterViewCreation,
} from '../integrations/split/squadTreatments/viewsTreatments';

import { getMyTasksWidgetViewId, isMyTasksViewId, getDefaultViewMemberPermissionLevel } from './viewsUtils';

import { prepareSyncBlockQueries } from '../sync_blocks/syncBlockQueryFactory';
import { getSyncBlocksByParentIds } from '../sync_blocks/syncBlockService';
import * as mindMapMod from './mindMap';
import * as attachmentMod from '../attachment/attachment';

import {
    bindAttachmentsToParent,
    bindAttachmentsToParentAsync,
    unbindAttachmentsToParentAsync,
} from '../attachment/services/updateAttachmentParent';

import * as viewSchema from './schema';
import * as helpers from './helpers';
import * as tasksMentioned from '../task/itemMentions';
import * as taskMembers from '../task/taskMember'; // eslint-disable-line import/no-cycle
import * as subcatMembers from '../subcategory/subcatMember';
import * as catMembers from '../category/catMember';
import * as projectMembers from '../project/projectMember'; // eslint-disable-line import/no-cycle
import * as teamMembers from '../team/teamMember'; // eslint-disable-line import/no-cycle
import { generateToken } from '../public_api/oauth';
import * as linkHelpers from '../link/services/linkHelpers';
import * as templateHelpers from '../templates/templateHelpers';
import * as templateTags from '../templates/templateTags';
import * as docHelpers from './documents/helpers/documentHelpers';
import * as pageHelpers from './pages/services/pageHelpers';
import * as groupMod from '../groups';
import * as commonHelpers from '../helpers';
import * as groupSharingPaywall from '../group_sharing/paywall';
import { embedPaywallService } from './paywalls/services/embedPaywallService';
import { defaultViewPaywallService } from './paywalls/services/defaultViewPaywallService';
import { viewCrudPaywallService } from './paywalls/services/viewCrudPaywallService';
import { getViewTeamInfoAsync } from './services/getViewTeamService';
import * as fieldMod from '../field/CRUD/CRUD'; // eslint-disable-line import/no-cycle
import * as fieldLocation from '../field/fieldLocation'; // eslint-disable-line import/no-cycle
import * as fieldHelpers from '../field/helpers/helpers'; // eslint-disable-line import/no-cycle
import { checkFieldExistsInHierarchy } from '../field/helpers/fieldValidation';
import { ClickUpError } from '../../utils/errors';
import { PublicSharingCodes, StatusErrorCodes, ViewErrorCodes } from '../../utils/errors/constants';
import { isSidebarEnabledView } from './sidebar_views/helpers/sidebarViewsHelper';
import { getFollowersAsync } from '../follower/services/viewFollowerService';
import { addFollowers } from '../follower/datastore/viewFollowerDatastore';
import { getMembers } from './group_members/services/viewGroupMemberService';
import { viewsBulkInsertParams } from './viewsBulkInsertParams';
import { batchQueriesSeriesAsync, writeAsync } from '../../utils/db2';
import { getPreferredLanguage } from '../../utils/i18n';
import { EditViewColumnPermissionGuard } from './services/edit-view-attribute-permissions/edit-view-column-permission.guard';
import { EditFormFieldPermissionGuard } from './services/edit-view-attribute-permissions/edit-form-field-permission.guard';

import {
    reportWhiteboardChanged,
    reportFormChanged,
    reportViewChanged,
    reportAssetViewed,
    shouldIndexView,
    shouldReportAssetViewed,
    reportDashboardViewChanged,
} from '../elastic/services/change-detector/hooks';

import { elasticParams } from '../integrations/split/squadTreatments/searchTreatments';

import {
    queryPageIdsForView,
    queryPageForViewSortByDateCreatedAsc,
    queryEmptySubPageParentIdForView,
} from './pages/datastores/pageDataStore';

import { replaceWhiteboardTemplate } from './templates/whiteboard-helpers/replace-whiteboard-template';
import { prepareSoftDeleteOfPages } from './pages/factories/deletePageQueryFactory';
import { JSON_COL_MAX_LENGTH, defaultSprintCardsSettings } from './constants';
import { EntityType, TemplateSource, TemplateAction } from '../templates/templateMetricsCommons';
import { TemplateTracer } from '../templates/templateTracer';
import { denormalization } from '../sharding/services/denormalization.service';
import { QueryIdentifiers } from '../sharding/lib/denormalization_checks/constants';
import { prettyIdsUseSerialGenerate } from '../integrations/split/squadTreatments/unclaimedTreatments';
import { createDashboardForView } from '../dashboard/datastores/dashboards/dashboard-create';
import { getViewDashboardId, isViewRelatedToDashboard } from '../dashboard/dashboard-view';
import {
    shouldFetchPageDefaults,
    checkOwnerRemovalAccessForGuestsOnDocs,
} from '../integrations/split/squadTreatments/docTreatments';

import { DocsAppContext } from './pages/DocsAppContext';
import { getOrderIndexColumnName } from './orderindex';
import { queryViewSorting, queryViewColumns, queryFormFields } from './datastores/CRUD/viewDataStore';
import { fieldLevelPermissionsConfig } from '../integrations/split/squadTreatments/fieldTreatments';
import { shouldIdentifyCanonicalChannel } from '../integrations/split/squadTreatments/chatTreatments';
import { entitlementService, EntitlementName } from '../entitlements/entitlementService';
import { getWikiCountForWorkspace } from './documents/datastores/docHelpersDatastore';
import { filterLocationOverviewSingleton } from '../dashboard/location-overview/views';
import { getFormFieldFactory } from './forms/form-field.factory';
import { isDrilldownView } from '../dashboard/widgets/drilldown/utils';
import { isVisibilityChanged } from './utils/is-visibility-changed';
import { isUserBeingRemovedAsViewMember } from './members/services/viewMemberService';
import { queryViewGroupsByParent } from './view_group/datastores';
import { copyAttachmentsAsync } from '../attachment/attachmentCopy';
import { hasCanonicalChatViews, getChatViewInsertQuery, updateCanonicalChannelInfo } from './chat/chatViewDataStore';

const ViewsError = ClickUpError.makeNamedError('views');
const logViewsError = ClickUpError.getIsolatedErrorHandler('views');

const { field_types } = config;
const { parent_types, view_types, doc_types, visibility: view_visibility } = config.views;
const { can_create_tasks, edit_widget, create_dashboards } = config.permission_constants;

const tracer = new ClickUpTracer();
const logger = ClickUpError.getBasicLogger('views');

const getViewColumnGuard = _.memoize(
    () =>
        new EditViewColumnPermissionGuard({
            getItemsByIds: ({ viewIds }) =>
                queryViewColumns({
                    viewIds,
                    additionalWhereStatements: [queryBuilder => queryBuilder.andWhereILike('view_cols.field', 'cf_%')],
                }),
        })
);
const getViewSortingGuard = _.memoize(
    () =>
        new EditViewColumnPermissionGuard({
            getItemsByIds: ({ viewIds }) =>
                queryViewSorting({
                    viewIds,
                    additionalWhereStatements: [queryBuilder => queryBuilder.andWhereILike('view_sort.field', 'cf_%')],
                }),
        })
);
const getFormFieldsGuard = _.memoize(
    () =>
        new EditFormFieldPermissionGuard({
            getItemsByIds: ({ viewIds }) =>
                queryFormFields({
                    formIds: viewIds,
                    additionalWhereStatements: [
                        queryBuilder => queryBuilder.andWhereILike('form_fields.field', 'cf_%'),
                        queryBuilder =>
                            queryBuilder.andWhereRaw(`(
                    form_fields.dependencies IS NULL
                        OR jsonb_array_length(form_fields.dependencies) = 0
                    )`),
                    ],
                }).then(formFields => formFields.map(row => _formFieldRowToModel(row))),
        })
);
const getConditionalFormFieldsGuard = _.memoize(
    () =>
        new EditFormFieldPermissionGuard({
            getItemsByIds: ({ viewIds }) =>
                queryFormFields({
                    formIds: viewIds,
                    additionalWhereStatements: [
                        queryBuilder => queryBuilder.andWhereILike('form_fields.field', 'cf_%'),
                        queryBuilder => queryBuilder.andWhereRaw('jsonb_array_length(form_fields.dependencies) != 0'),
                    ],
                }).then(formFields => formFields.map(row => _formFieldRowToModel(row))),
        })
);

function flattenPages(pages, flattened = []) {
    pages.forEach(page => {
        flattened.push(page);
        if (page.pages) {
            flattenPages(page.pages, flattened);
        }
    });

    return flattened;
}

async function getPageIDMap(team_id, pages, cb) {
    try {
        if (prettyIdsUseSerialGenerate()) {
            const page_id_map = {};
            for (const page of pages) {
                page_id_map[page.id] = (await prettyID.getPageIdAsync(team_id)).pretty_id;
            }
            cb(null, { page_id_map });
            return;
        }

        const page_id_map = await pages.reduce(
            async (acc, page) => ({ ...(await acc), [page.id]: (await prettyID.getPageIdAsync(team_id)).pretty_id }),
            Promise.resolve({})
        );
        cb(null, { page_id_map });
    } catch (err) {
        cb(err);
    }
}

function validateFormSubcategory(subcategory_id, parent_id, parent_type, cb) {
    if (!subcategory_id) {
        cb(null, { exists: false });
        return;
    }

    if (parent_type === parent_types.subcategory) {
        cb(null, { exists: String(subcategory_id) === String(parent_id) });
        return;
    }

    let query;

    if (parent_type === parent_types.category) {
        query = `
            SELECT 1
            FROM task_mgmt.subcategories
            INNER JOIN task_mgmt.categories
                ON categories.id = subcategories.category
            WHERE categories.id = $1
                AND subcategories.id = $2
                AND subcategories.deleted = FALSE
                AND subcategories.archived = FALSE
                AND subcategories.template = FALSE
                AND categories.deleted = FALSE
                AND categories.archived = FALSE
                AND categories.template = FALSE`;
    } else if (parent_type === parent_types.project) {
        query = `
            SELECT 1
            FROM task_mgmt.subcategories
            INNER JOIN task_mgmt.categories
                ON categories.id = subcategories.category
            INNER JOIN task_mgmt.projects
                ON projects.id = categories.project_id
            WHERE projects.id = $1
                AND subcategories.id = $2
                AND subcategories.deleted = FALSE
                AND subcategories.archived = FALSE
                AND subcategories.template = FALSE
                AND categories.deleted = FALSE
                AND categories.archived = FALSE
                AND categories.template = FALSE
                AND projects.deleted = FALSE
                AND projects.archived = FALSE
                AND projects.template = FALSE`;
    } else {
        query = `
            SELECT 1
            FROM task_mgmt.subcategories
            INNER JOIN task_mgmt.categories
                ON categories.id = subcategories.category
            INNER JOIN task_mgmt.projects
                ON projects.id = categories.project_id
            WHERE projects.team = $1
                AND subcategories.id = $2
                AND subcategories.deleted = FALSE
                AND subcategories.archived = FALSE
                AND subcategories.template = FALSE
                AND categories.deleted = FALSE
                AND categories.archived = FALSE
                AND categories.template = FALSE
                AND projects.deleted = FALSE
                AND projects.archived = FALSE
                AND projects.template = FALSE`;
    }

    const params = [parent_id, subcategory_id];

    db.readQuery(query, params, (err, result) => {
        if (err) {
            cb(new ViewsError(err, 'VIEWS_059'));
        } else {
            cb(null, { exists: !!result.rows.length });
        }
    });
}

function getProjectId(parent_id, parent_type, options, cb) {
    if (Number(parent_type) === parent_types.project) {
        cb(null, { project_id: parent_id });
        return;
    }

    let query;
    const params = [parent_id];

    if (Number(parent_type) === parent_types.category) {
        query = `
            SELECT project_id
            FROM task_mgmt.categories
            WHERE categories.id = $1`;
    } else if (Number(parent_type) === parent_types.subcategory) {
        query = `
            SELECT project_id
            FROM task_mgmt.subcategories
            INNER JOIN task_mgmt.categories
                ON categories.id = subcategories.category
            WHERE subcategories.id = $1`;
    } else if (options.all_projects) {
        query = `
            SELECT id AS project_id
            FROM task_mgmt.projects
            WHERE team = $1`;
    } else {
        cb(null, {});
        return;
    }

    db.readQuery(query, params, (err, result) => {
        if (err) {
            cb(new ViewsError(err, 'VIEWS_080'));
            return;
        }

        if (!result.rows.length) {
            cb(null, {});
            return;
        }

        if (
            options.all_projects &&
            ![parent_types.project, parent_types.category, parent_types.subcategory].includes(Number(parent_type))
        ) {
            cb(null, { project_ids: result.rows.map(row => row.project_id) });
            return;
        }

        cb(null, { project_id: result.rows[0].project_id });
    });
}

function validateMapSettings(map_settings = {}, options, cb) {
    if (!Array.isArray(map_settings.field_ids)) {
        cb();
        return;
    }

    const { parent = {} } = options;

    const query = `
        SELECT type
        FROM task_mgmt.fields
        WHERE id = ANY ($1)`;
    const params = [map_settings.field_ids];

    db.readQuery(query, params, (err, result) => {
        if (err) {
            cb(new ViewsError(err, 'VIEWS_208'));
            return;
        }

        if (result.rows.some(row => Number(row.type) !== field_types.location)) {
            cb(new ViewsError('Map view fields must be of type location', 'VIEWS_209', 400));
            return;
        }

        async.each(
            map_settings.field_ids,
            (field_id, each_cb) => {
                checkFieldExistsInHierarchy(field_id, parent.id, parent.type, {}, (err1, exists) => {
                    if (err1) {
                        each_cb(err1);
                        return;
                    }

                    if (!exists) {
                        map_settings.field_ids = map_settings.field_ids.filter(id => id !== field_id);
                    }

                    each_cb();
                });
            },
            cb
        );
    });
}

function checkParentAccess(userid, parent_id, type, options, cb) {
    let accessFunc;
    const permissions = options.permissions || [];

    type = parseInt(type, 10);

    if (
        [
            parent_types.team,
            parent_types.shared,
            parent_types.template,
            parent_types.widget,
            parent_types.shared_tasks,
            parent_types.team_unparented,
        ].includes(type)
    ) {
        accessFunc = access.checkAccessTeam;
    } else if (type === parent_types.subcategory) {
        accessFunc = access.checkAccessSubcategory;
    } else if (type === parent_types.category) {
        accessFunc = access.checkAccessCategory;
    } else if (type === parent_types.project) {
        accessFunc = access.checkAccessProject;
    } else if (type === parent_types.task) {
        accessFunc = access.checkAccessTask;
    } else if (type === parent_types.user_group) {
        accessFunc = access.checkAccessGroup;
    } else if (type === parent_types.view) {
        accessFunc = access.checkAccessView;
    } else {
        // not a valid type
        cb(new ViewsError('Parent type not valid', 'VIEWS_001', 400));
        return;
    }

    if (!options.view) {
        options.view = {};
    }

    accessFunc(
        userid,
        parent_id,
        { permissions, visibility: options.visibility, view: options.view, view_type: options.view.view_type },
        cb
    );
}
export { checkParentAccess };

function _promiseCheckParentAccess(userid, parent_id, type, options) {
    return new Promise((res, rej) => {
        checkParentAccess(userid, parent_id, type, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}
export { _promiseCheckParentAccess };

function getStandardViewId(parent_type, parent_id, view_type) {
    return `${parent_type}-${parent_id}-${view_type}`;
}

function formatViewRows(rows, parent_permissions, parent_info = {}, userid, role_validation_config) {
    const authz_trace = authzVerboseLogging(userid) ? new AuthorizationTrace() : null;

    rows = rows.map(row => {
        const task_view = !config.views.page_views.includes(row.type);
        let parent_permission_level;
        let team_role;
        let team_role_subtype;
        authz_trace?.append({
            code: 'FORMAT_VIEW_ROWS_INIT',
            key: row.view_id,
            data: {
                row,
            },
        });

        if (parent_permissions) {
            team_role = parent_permissions.team_role;
            team_role_subtype = parent_permissions.team_role_subtype;
            parent_permission_level = parent_permissions.permission_level;
            authz_trace?.append({
                code: 'PARENT_PERMISSIONS',
                key: row.view_id,
                data: {
                    userid,
                    team_role,
                    team_role_subtype,
                    parent_permission_level,
                },
            });
        } else if (parent_info && parent_info[row.view_id]) {
            team_role = parent_info[row.view_id].role;
            team_role_subtype = parent_info[row.view_id].role_subtype;
            parent_permission_level = parent_info[row.view_id].hierarchy_permission_level;
            authz_trace?.append({
                code: 'PARENT_PERMISSIONS_NULL',
                key: row.view_id,
                data: {
                    userid,
                    team_role,
                    team_role_subtype,
                    parent_permission_level,
                },
            });
        }

        const is_external_guest = isExternalGuest(team_role, team_role_subtype, role_validation_config);

        row.id = row.view_id;
        delete row.view_id;

        row.parent = {
            id: row.parent_id,
            type: row.parent_type,
        };

        delete row.parent_id;
        delete row.parent_type;

        row.dashboard = {
            id: row.dashboard_id,
            name: row.dashboard_name,
        };
        delete row.dashboard_id;
        delete row.dashboard_name;

        if (row.widget_name) {
            row.name = row.widget_name;
        }
        delete row.widget_name;

        if (!row.share_task_fields) {
            row.share_task_fields = config.sharing.public_fields;
        }

        if (row.type === view_types.form) {
            // form cannot be locked anymore
            row.locked = false;
        }

        if (row.locked_permission === 1 && row.permission_level == null && row.locked) {
            row.permission_level = 5;
            authz_trace?.append({ code: 'LOCKED_1', key: row.id, data: { permission_level: row.permission_level } });
        } else if (
            row.locked &&
            row.locked_permission === 2 &&
            (Number(row.creator) === Number(userid) || Number(team_role) === config.team_roles.owner)
        ) {
            // creator of form and owner of WS
            // should walways have full permissions
            row.permission_level = 5;
            authz_trace?.append({ code: 'LOCKED_2', key: row.id, data: { permission_level: row.permission_level } });
        } else if (row.locked_permission === 2 && row.permission_level == null && row.locked) {
            row.permission_level = 1;
            authz_trace?.append({ code: 'LOCKED_3', key: row.id, data: { permission_level: row.permission_level } });
        } else if (
            row.locked_permission === 3 &&
            row.permission_level == null &&
            team_role &&
            team_role > config.team_roles.admin &&
            row.locked
        ) {
            row.permission_level = 1;
            authz_trace?.append({ code: 'LOCKED_4', key: row.id, data: { permission_level: row.permission_level } });
        } else if (row.permission_level == null && parent_permission_level) {
            row.permission_level = parent_permission_level;
            authz_trace?.append({ code: 'LOCKED_5', key: row.id, data: { permission_level: row.permission_level } });
        } else if (row.permission_level == null) {
            row.permission_level = 5;
            authz_trace?.append({ code: 'LOCKED_6', key: row.id, data: { permission_level: row.permission_level } });
        }

        if (row.personal_team) {
            row.me_view = false;
        }
        delete row.personal_team;

        if (row.type !== view_types.form && team_role === config.team_roles.owner) {
            row.permission_level = 5;
            authz_trace?.append({ code: 'OWNER_NON_FORM', key: row.id, data: { type: row.type } });
        }

        row.permissions = {
            can_unprotect: row.permission_level === 5 && !is_external_guest,
        };

        if (row.locked && !row.permissions.can_unprotect) {
            row.permission_level = 1;
        }

        row.permissions = {
            can_unprotect: row.permissions.can_unprotect,
            team_role,
            ...config.views.permission_levels[row.permission_level],
        };

        authz_trace?.append({
            code: 'PERMISSIONS_INIT',
            key: row.id,
            data: {
                permission_level: row.permission_level,
                permissions: row.permissions,
                locked: row.locked,
                is_external_guest,
            },
        });

        if (task_view) {
            row.permissions.delete_view = parent_permissions ? parent_permissions.delete_view : false;
            authz_trace?.append({
                code: 'PERMISSIONS_TASK_VIEW',
                key: row.id,
                data: {
                    permissions: row.permissions,
                },
            });
        }

        if (Number(row.parent.type) === parent_types.team && team_role && team_role !== config.team_roles.guest) {
            row.permissions.delete_view = true;
            if (!row.permission_level || row.permission_level === 5) {
                row.permissions.edit_view = true;
            }
            authz_trace?.append({
                code: 'PERMISSIONS_TEAM_NON_GUEST',
                key: row.id,
                data: {
                    permissions: row.permissions,
                    team_role,
                },
            });
        }

        if (!parent_permissions || parent_permissions.create_view === false) {
            if (
                (!row.standard || ![parent_types.shared, parent_types.template].includes(Number(row.parent.type))) &&
                row.type !== view_types.doc
            ) {
                row.permissions.create_view = false;
                row.permissions.delete_view = row.creator === userid;
                row.permissions.edit_view = row.creator === userid;
                authz_trace?.append({
                    code: 'PERMISSIONS_RULE_1',
                    key: row.id,
                    data: {
                        permissions: row.permissions,
                        standard: row.standard,
                        parent_type: row.parent.type,
                        type: row.type,
                        creator: row.creator,
                    },
                });
            }
        }

        row.creator_user = formatUser({
            id: row.creator_userid,
            username: row.creator_username,
            email: row.creator_email,
            color: row.creator_color,
            profile_picture_key: row.creator_ppk,
        });

        delete row.creator_userid;
        delete row.creator_username;
        delete row.creator_email;
        delete row.creator_color;
        delete row.creator_ppk;

        row.permissions.permission_level = row.permission_level;
        delete row.permission_level;

        row.members = [];

        if (task_view) {
            // gantt always groups by subcategory
            const field = row.type === view_types.gantt ? 'subcategory' : row.grouping_field;

            row.grouping = {
                field,
                dir: row.grouping_dir,
                collapsed: row.collapsed_groups,
                ignore: row.ignore_grouping,
                single: !!row.group_by_single,
            };

            if (!row.grouping_field && row.type !== view_types.mind_map) {
                row.grouping.field = 'none';
            }

            row.divide = {
                field: row.division_field,
                dir: row.division_dir,
                by_subcategory: row.division_by_subcategory,
                collapsed: row.collapsed_divisions,
            };

            row.settings = {
                ...row.settings,
                show_task_locations: row.show_task_locations,
                show_subtasks: row.show_subtasks,
                show_subtask_parent_names: row.show_subtask_parent_names,
                show_closed_subtasks: row.show_closed_subtasks || false,
                show_assignees: row.show_assignees,
                show_images: row.show_images,
                show_timer: row.show_timer,
                collapse_empty_columns: row.collapse_empty_columns,
                me_comments: row.me_comments,
                me_subtasks: row.me_subtasks,
                me_checklists: row.me_checklists,
                show_empty_statuses: row.show_empty_statuses,
                auto_wrap: row.auto_wrap,
                time_in_status_view: row.time_in_status_view || 1,
                is_description_pinned: !!row.is_description_pinned,
                override_parent_hierarchy_filter: !!row.override_parent_hierarchy_filter,
                fast_load_mode: !!row.fast_load_mode,
            };

            row.sorting = {
                fields: [],
            };

            if (row.type && Number(row.type) === Number(config.views.view_types.activity)) {
                row.filters = {
                    fields: [],
                    op: row.filter_op,
                    search: row.search,
                    search_custom_fields: row.search_custom_fields == null ? true : row.search_custom_fields,
                    search_description: Boolean(row.search_description),
                    search_name: Boolean(row.search_name),
                };
            } else {
                row.filters = {
                    op: row.filter_op,
                    fields: [],
                    search: row.search,
                    search_custom_fields:
                        row.search_custom_fields == null && row.search ? true : row.search_custom_fields,
                    search_description: Boolean(row.search_description),
                    search_name: Boolean(row.search_name),
                    show_closed: row.show_closed || false,
                };
            }

            row.columns = {
                fields: [],
            };

            row.team_sidebar = {
                assignees: row.sidebar_assignees,
                group_assignees: row.sidebar_group_assignees || [],
                assigned_comments: row.sidebar_assigned_comments || false,
                unassigned_tasks: row.sidebar_unassigned_tasks || false,
            };
        }

        row.frozen_by = formatUser({
            id: row.frozen_by_id,
            username: row.username,
            email: row.email,
            color: row.color,
            profile_picture_key: row.profile_picture_key,
        });

        delete row.frozen_by_id;
        delete row.username;
        delete row.email;
        delete row.color;
        delete row.profile_picture_key;

        delete row.grouping_field;
        delete row.grouping_dir;
        delete row.collapsed_groups;
        delete row.ignore_grouping;
        delete row.group_by_single;

        delete row.division_field;
        delete row.division_dir;
        delete row.division_by_subcategory;
        delete row.collapsed_divisions;

        delete row.sidebar_assignees;
        delete row.sidebar_assigned_comments;
        delete row.sidebar_unassigned_tasks;
        delete row.sidebar_group_assignees;

        delete row.show_task_locations;
        delete row.show_subtasks;
        delete row.show_subtask_parent_names;
        delete row.show_closed_subtasks;
        delete row.show_assignees;
        delete row.show_images;
        delete row.show_timer;
        delete row.collapse_empty_columns;
        delete row.me_comments;
        delete row.me_subtasks;
        delete row.me_checklists;
        delete row.time_in_status_view;
        delete row.show_empty_statuses;
        delete row.auto_wrap;
        delete row.is_description_pinned;
        delete row.override_parent_hierarchy_filter;
        delete row.fast_load_mode;

        delete row.filter_op;
        delete row.search;
        delete row.search_custom_fields;
        delete row.search_description;
        delete row.search_name;
        delete row.show_closed;

        if (!task_view) {
            delete row.me_view;
        }

        return row;
    });

    if (authzVerboseLogging(userid)) {
        logger.info({
            userid,
            log_tags: ['access', 'view'],
            parent_permissions,
            parent_info,
            role_validation_config,
            trace: authz_trace.full(),
        });
    }

    return rows;
}

/**
 * @param {string} userid
 * @param {Object} options - Options list is not complete
 * @param {string|number} [options.type] - If provided, return only view of that type
 * @param {boolean} [options.skipAccess] - Used to access Public Views and other cases
 * @param {boolean} [options.most_used] - Return up to X most used Templates
 * @param {string[]} [options.in_view_ids] - If provided, return only view of that type
 * @param {number} [options.parent_type] - If provided, return only view of that type
 * @param {boolean} [options.include_pages] - Default to true
 * @param {boolean} [options.include_archived] - Default to false
 * @param {boolean} [options.system_views] - Include system views, default to false
 * @param {boolean} [options.use_replica] - Use replica for expensive queries, default true
 * @param {*} cb
 */
const _getViews = tracer.wrap('views._getViews', {}, __getViews);
function __getViews(userid, options, cb) {
    const {
        view_id,
        in_view_ids,
        include_pages = true,
        include_parent_info,
        deleted,
        exclude_types,
        exclude_canonical_channels,
        system_views,
        use_replica = true,
    } = options;
    let { parent_id, parent_type, parent_permissions, type: view_type } = options;

    const include_archived = Boolean(options.include_archived);
    const only_default_views = Boolean(options.only_default_views);

    if (parent_type) {
        parent_type = Number(parent_type);
    }

    if (
        parent_type === parent_types.team ||
        parent_type === parent_types.user_group ||
        parent_type === parent_types.view
    ) {
        options.required_templates = false;
    }

    if (view_type) {
        view_type = Number(view_type);
    }

    let storedViewGroups = [];

    let team_id;
    let role_id;
    let delete_items_cus_permission = null;
    let views_cus_permission = null;
    let embedViewLoad = false;
    let mapViewLoad = false;
    let isLimitedMemberEnabled = false;
    let has_canonical_channel = null;

    const TEMPLATE_VISIBILITY = config.get('views.template_visibility');

    const dbReadQuery = use_replica ? db.replicaQuery : db.readQuery;

    async.parallel(
        {
            async entitlements(para_cb) {
                // Can't derive the team from the arguments and no rows will be returned so
                // return empty entitlements.
                if (!view_id && (in_view_ids?.length ?? 0) < 1 && !(parent_id && parent_type)) {
                    para_cb(null, {});
                    return;
                }

                let params;
                if (view_id) {
                    params = [view_id, {}];
                } else if (parent_id && parent_type) {
                    params = [null, { parent_id, parent_type }];
                } else if (in_view_ids[0]) {
                    params = [in_view_ids[0], {}];
                } else {
                    params = [null, {}];
                }

                try {
                    const { team_id: workspaceId } = await getViewTeamInfoAsync(...params);
                    team_id = workspaceId;
                    isLimitedMemberEnabled = await isLimitedMemberEnabledAsync(workspaceId).catch(err =>
                        logger.warn({
                            msg: 'Fetch limited member enabled failed',
                            err,
                        })
                    );

                    const entitlements = await viewCrudPaywallService.getEntitlementsInfo(workspaceId);
                    para_cb(null, entitlements);
                } catch {
                    // bad data gets passed in often here, so we need to treat the entitlements as off here
                    para_cb(null, {});
                }
            },

            async parent_info(para_cb) {
                if (!include_parent_info) {
                    para_cb();
                    return;
                }
                try {
                    const result = await helpers.getParentInfo(userid, view_id ? [view_id] : in_view_ids, {
                        language: options.language,
                    });
                    para_cb(null, result);
                } catch (err) {
                    para_cb(err);
                }
            },

            async getCustomRolePerm(para_cb) {
                if (options.skipAccess) {
                    para_cb();
                    return;
                }

                if (view_id) {
                    let result;
                    try {
                        result = await access.promiseCheckAccessView(userid, view_id, { from_get: true });
                        parent_id = result.data.id;
                        parent_type = result.data.type;

                        if (result.parentPermissions) {
                            ({ team_id } = result.parentPermissions || {});
                            const { role, custom_role } = result.parentPermissions || {};
                            const { team_role } = result.parentPermissions?.permissions || {};
                            role_id = custom_role || role || team_role;
                            parent_permissions = result.parentPermissions.permissions ?? result.parentPermissions;
                        }
                    } catch (e) {
                        para_cb(e);
                        return;
                    }
                } else {
                    let result;
                    try {
                        result = await _promiseCheckParentAccess(userid, parent_id, parent_type, {
                            from_get: true,
                        });

                        ({ team_id } = result || {});
                        const { role, custom_role } = result || {};

                        role_id = custom_role || role;

                        parent_permissions = result?.permissions ?? result;
                    } catch (e) {
                        para_cb(e);
                        return;
                    }
                }

                const team_id_num = Number(team_id);

                if (Number.isNaN(team_id_num)) {
                    para_cb();
                    return;
                }

                const user = await getAccessInfoCache().getUserWorkspaceAccessInfo(userid, team_id_num);

                if (user) {
                    const permissions = getIndividualUserPermissions(user);
                    delete_items_cus_permission = permissions.delete_items;
                    views_cus_permission = permissions.views;

                    if (!role_id) {
                        role_id = user.workspaceCustomRole ?? user.workspaceRole;
                    }
                }
                para_cb();
            },

            views(para_cb) {
                const template_from = `
                        template_options.options AS template_options,
                        template_options.description,
                        template_options.settings_counts as counts,
                    `;

                const template_join = `
                    LEFT JOIN task_mgmt.team_members
                        ON views.team_id = team_members.team_id
                        AND team_members.userid = $2
                    LEFT JOIN task_mgmt.view_template_members
                        ON view_template_members.userid = $2
                        AND view_template_members.view_id = views.view_id
                    LEFT JOIN task_mgmt.template_options
                        ON template_options.template_id = views.view_id
                        AND template_options.type = 'view'
                    LEFT JOIN LATERAL (
                        SELECT
                            view_template_group_members.view_id
                        FROM
                            task_mgmt.group_members
                            JOIN task_mgmt.view_template_group_members ON
                                view_template_group_members.group_id = group_members.group_id AND
                                view_template_group_members.view_id = views.view_id
                        WHERE
                            group_members.userid = $2
                        LIMIT 1
                    ) AS view_template_group_member ON view_template_group_member.view_id = views.view_id
                `;

                const params = [config.views.visibility.public, userid, parent_types.widget, parent_types.template];

                let tagsJoin = ``;
                let tagsWhere = ``;
                if (options.tags && options.tags.length) {
                    tagsJoin = `
                        LEFT OUTER JOIN task_mgmt.template_tags ON template_tags.template_id = views.view_id AND template_tags.template_type = 'view'
                    `;
                    params.push(options.tags);
                    tagsWhere = ` AND template_tags.tag_id = ANY($${params.length})`;
                }

                let date_created_where = ``;
                if (options.date_created) {
                    params.push(options.date_created);
                    date_created_where = `AND views.date_created > $${params.length}`;
                }

                const archived_where = include_archived ? '' : 'AND views.archived IS NOT TRUE ';

                const only_default_views_where = only_default_views
                    ? 'AND (views.standard = TRUE OR views.default = TRUE)'
                    : '';

                const widgetsJoin = team_id
                    ? `
                        LEFT JOIN task_mgmt.widgets
                        ON
                        (
                            views.view_id = widgets.view_id
                            OR
                            (
                                views.view_id = widgets.drilldown_view_id
                                AND
                                widgets.drilldown_view_id IS NOT NULL
                            )
                        )
                        AND views.parent_type = $3
                        `
                    : 'LEFT JOIN task_mgmt.widgets ON views.view_id = widgets.view_id AND views.parent_type = $3';

                const excludeSystemViews = system_views
                    ? ''
                    : ` AND views.visibility != ${config.views.visibility.system}`;

                const includeSystemViews = system_views
                    ? ` OR views.visibility = ${config.views.visibility.system}`
                    : '';

                let query = `
                    SELECT
                    ${
                        options.only_counts
                            ? `count(*)::INT`
                            : `
                        views.*,
                        COALESCE(
                            view_members.permission_level,
                            view_group_member.permission_level
                        ) as permission_level,
                        teams.personal_team,
                        view_preferences.auto_save,
                        view_preferences.extra AS extra_user,
                        dashboards.name as dashboard_name,
                        dashboards.id as dashboard_id,
                        widgets.title as widget_name,
                        frozen_by.id AS frozen_by_id,
                        frozen_by.username,
                        frozen_by.email,
                        frozen_by.color,
                        frozen_by.profile_picture_key,
                        coalesce(teams.hours_per_day, 8) AS hours_per_day,
                        coalesce(teams.time_tracking_display_hours, true) AS time_tracking_display_hours,
                        coalesce(teams.time_estimate_display_hours, true) AS time_estimate_display_hours,
                        teams.time_estimate_rollup,
                        teams.time_tracking_rollup,
                        creator.id as creator_userid,
                        TRUE as task_relationships, --removing this may break the UI
                        creator.email creator_email,
                        creator.username as creator_username,
                        creator.color creator_color,
                        creator.profile_picture_key as creator_ppk,
                        ${template_from}
                        (
                            view_members.permission_level IS NOT NULL OR
                            view_group_member.permission_level IS NOT NULL
                        ) AS view_access
                        ${denormalization.buildSelectColumns(QueryIdentifiers.GetViews, {
                            leadingComma: true,
                            trailingComma: false,
                        })}
                        `
                    }
                    FROM task_mgmt.views
                    INNER JOIN task_mgmt.teams
                        ON teams.id = views.team_id
                    ${template_join}
                    ${tagsJoin}
                    LEFT JOIN task_mgmt.view_members
                        ON view_members.view_id = views.view_id
                        AND view_members.userid = $2


                    LEFT JOIN LATERAL (
                        SELECT
                            view_group_members.view_id,
                            MAX(permission_level) as permission_level
                        FROM
                            task_mgmt.group_members
                                JOIN task_mgmt.view_group_members ON
                                view_group_members.group_id = group_members.group_id AND
                                view_group_members.view_id = views.view_id
                        WHERE
                            group_members.userid = $2
                        GROUP BY
                            view_group_members.view_id
                    ) AS view_group_member ON view_group_member.view_id = views.view_id

                    LEFT JOIN task_mgmt.view_preferences
                        ON view_preferences.view_id = views.view_id
                            AND view_preferences.userid = $2
                    LEFT JOIN tasK_mgmt.users as frozen_by
                        ON frozen_by.id = views.frozen_by
                    LEFT JOIN tasK_mgmt.users as creator
                        ON creator.id = views.creator
                    ${widgetsJoin}
                    LEFT JOIN task_mgmt.dashboards ON widgets.dashboard_id = dashboards.id
                    ${
                        exclude_canonical_channels
                            ? `LEFT JOIN task_mgmt.chat_views ON views.view_id = chat_views.view_id`
                            : ''
                    }
                    WHERE (
                        (
                            views.parent_type != $4
                            AND
                            (
                                views.visibility = $1
                                OR views.visible_to = $2
                                OR ((view_members.userid IS NOT NULL OR view_group_member.view_id IS NOT NULL) )
                                ${options.check_public ? 'OR views.public = true OR views.form_active = true' : ''}
                            )
                        )
                        OR
                        (
                            views.parent_type = $4
                            AND
                            (
                                views.template_visibility = $${params.push(TEMPLATE_VISIBILITY.public)}
                                OR
                                    views.template_visible_to = $2
                                OR
                                (
                                    views.template_visibility = $${params.push(TEMPLATE_VISIBILITY.admins)}
                                    AND team_members.role <= $${params.push(config.team_roles.admin)}
                                )
                                OR
                                (
                                    views.template_visibility = $${params.push(TEMPLATE_VISIBILITY.users)}
                                    AND view_template_members.userid IS NOT NULL
                                )
                                OR
                                (
                                    views.template_visibility = $${params.push(TEMPLATE_VISIBILITY.users)}
                                    AND view_template_group_member.view_id IS NOT NULL
                                )
                                OR
                                (
                                    views.template_visibility = $${params.push(TEMPLATE_VISIBILITY.team_members)}
                                    AND team_members.role <= $${params.push(config.team_roles.user)}
                                )
                                OR   views.public = true
                            )
                        )
                        ${includeSystemViews}
                    )
                        ${exclude_canonical_channels ? ' AND chat_views.is_canonical_channel IS NOT TRUE' : ''}
                        ${deleted ? ' AND views.deleted = true' : ' AND views.deleted = false'}
                        ${parent_type === parent_types.shared ? ` AND views.creator = $2` : ``}
                        ${tagsWhere}
                        ${date_created_where}
                        ${archived_where}
                        ${only_default_views_where}
                        ${excludeSystemViews}
                    `;

                if (options.creator) {
                    params.push(options.creator);
                    query += ` AND views.creator = $${params.length}`;
                }

                if (view_id) {
                    params.push(view_id);
                    query += ` AND views.view_id = $${params.length}`;
                } else if (in_view_ids) {
                    params.push(in_view_ids);
                    query += ` AND views.view_id = ANY($${params.length})`;
                } else {
                    params.push(parent_id, parent_type);
                    query += `
                        AND views.parent_id = $${params.length - 1}
                        AND views.parent_type = $${params.length}`;
                }

                if (view_type) {
                    query += ` AND views.type = $${params.push(view_type)} `;
                }

                if (exclude_types) {
                    query += ` AND views.type != ALL($${params.push(exclude_types)})`;
                }

                if (options.name) {
                    if (options.desc && parent_type === parent_types.template) {
                        params.push(`%${options.name.toLowerCase()}%`);
                        params.push(`%${options.desc.toLowerCase()}%`);
                        query += ` AND (LOWER(views.name) LIKE $${
                            params.length - 1
                        } OR LOWER(template_options.description) ILIKE $${params.length})`;
                    } else {
                        params.push(`%${options.name.toLowerCase()}%`);
                        query += ` AND LOWER(views.name) LIKE $${params.length} `;
                    }
                }

                if (!options.only_counts && !options.most_used) {
                    if (options.paging) {
                        query += `
                            ORDER BY views.name ASC, views.view_id DESC
                            LIMIT ${config.page_length.templates + 1}`;
                    } else {
                        query += ` ORDER BY views.orderindex`;
                    }
                }

                async.parallel(
                    {
                        shard(shard_para_cb) {
                            db.readQuery(query, params, (err, result) => {
                                if (err) {
                                    shard_para_cb(new ViewsError(err, 'VIEWS_120'));
                                } else {
                                    shard_para_cb(null, result);
                                }
                            });
                        },
                        global(global_para_cb) {
                            const whiteLabelQuery = `SELECT rectangle_logo FROM task_mgmt.white_label_teams
                        WHERE team_id = $1
                        AND enabled = true
                        `;
                            db.globalReadQuery(
                                whiteLabelQuery,
                                [team_id],
                                { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_29 },
                                (err, result) => {
                                    if (err) {
                                        global_para_cb(new ViewsError(err, 'VIEWS_121'));
                                    } else {
                                        global_para_cb(null, result);
                                    }
                                }
                            );
                        },
                    },
                    (err, results) => {
                        if (err) {
                            para_cb(new ViewsError(err, 'VIEWS_122'));
                        } else {
                            const whiteLabel = results.global.rows[0] || {
                                rectangle_logo: null,
                            };
                            const views = results.shard.rows.map(row => ({
                                ...row,
                                rectangle_logo: whiteLabel.rectangle_logo,
                            }));
                            results.shard.rows = views;

                            if (!options.only_counts) {
                                loadTemplatesUserUsageData(userid, 'view', [...results.shard.rows], row => row.view_id)
                                    .then(userUsageDataResult => {
                                        const { result, isLastPage } = options.most_used
                                            ? applyTemplatesPaginationByUsedCountUsingName(
                                                  userUsageDataResult,
                                                  options.start
                                              )
                                            : { result: userUsageDataResult, isLastPage: undefined };
                                        para_cb(null, {
                                            rows: result,
                                            rowsBeforePagination: results.shard.rows,
                                            isLastPage,
                                        });
                                    })
                                    .catch(userUsageDataErr => para_cb(new ViewsError(userUsageDataErr, 'VIEWS_122')));
                            } else {
                                para_cb(null, results.shard);
                            }
                        }
                    }
                );
            },

            last_view(para_cb) {
                if (
                    view_id ||
                    [parent_types.task, parent_types.user_group, parent_types.view].includes(parent_type) ||
                    only_default_views
                ) {
                    para_cb();
                    return;
                }

                const query = `
                    SELECT view_type
                    FROM task_mgmt.last_standard_view
                    WHERE userid = $1
                        AND parent_type = $2
                        AND parent_id = $3`;
                const params = [userid, parent_type, parent_id];

                db.readQuery(query, params, (err, result) => {
                    if (err) {
                        para_cb(new ViewsError(err, 'VIEWS_014'));
                    } else {
                        para_cb(null, result.rows[0] && result.rows[0].view_type);
                    }
                });
            },
        },
        async (paraErr, paraResult) => {
            if (paraErr) {
                cb(new ViewsError(paraErr));
                return;
            }

            if (options.only_counts) {
                cb(null, {
                    full_count: paraResult.views.rows[0].count,
                    views: [],
                    last_page: true,
                    paging: {},
                });
                return;
            }

            for (const row of paraResult.views.rows) {
                denormalization.verifyRow(row, QueryIdentifiers.GetViews);
            }

            let role_validation_config;
            try {
                role_validation_config = await getRoleValidationConfig(team_id);
            } catch (err) {
                cb(new ViewsError(err, 'VIEWS_011'));
                return;
            }

            const viewRows = formatViewRows(
                paraResult.views.rows,
                parent_permissions,
                paraResult.parent_info,
                userid,
                role_validation_config
            );
            const allViewRows = paraResult.views.rowsBeforePagination || paraResult.views.rows;

            const { last_view } = paraResult;

            let last_page = true;
            let last_template;
            if (!_.isUndefined(paraResult.views.isLastPage)) {
                last_page = paraResult.views.isLastPage;
                last_template = viewRows[viewRows.length - 1];
            } else if (options.paging && allViewRows.length > config.page_length.templates) {
                last_page = false;
                viewRows.splice(config.page_length.templates);
                last_template = viewRows[viewRows.length - 1];
            }

            let views = {};
            const avatars = {};

            const form_templates = [];

            let has_board_settings = false;

            const workspace_ids = new Set();
            viewRows.forEach(view => {
                view.viewing = [];
                view.commenting = [];

                views[view.id] = view;

                if (view.extra && view.extra.board_settings) {
                    has_board_settings = true;
                }

                if (view.extra && view.extra.task_custom_type !== undefined) {
                    view.task_custom_type = view.extra.task_custom_type;
                }

                if (view.type === view_types.form && view.extra && view.extra.template_id) {
                    form_templates.push(view.extra.template_id);
                }

                workspace_ids.add(view.team_id);

                if (team_id && view_id && view.type === view_types.embed && !embedViewLoad) {
                    embedViewLoad = true;
                }
                if (view.type === view_types.map && view_id) {
                    mapViewLoad = true;
                }
            });

            if (!team_id && workspace_ids.size === 1) {
                [team_id] = workspace_ids;
            }

            if (has_board_settings) {
                options.project_features = true;
            }

            const view_ids = paraResult.views.rows.map(row => row.id);
            const avatar_ids = paraResult.views.rows.map(row => row.avatar_source).filter(Boolean);

            async.parallel(
                {
                    async getTeamClickApps(para_cb) {
                        if (!options.check_public && parent_type !== parent_types.team) {
                            para_cb();
                            return;
                        }

                        const { team_id: team_id_ } = views[view_id] || {};

                        if (!team_id_) {
                            para_cb();
                            return;
                        }

                        try {
                            const query = `
                                SELECT
                                    coalesce(teams.time_estimate_rollup, false) AS time_estimate_rollup,
                                    coalesce(teams.points_estimate_rollup, false) AS points_estimate_rollup,
                                    coalesce(teams.time_tracking_rollup, false) AS time_tracking_rollup,
                                    coalesce(teams.wip_limit, (SELECT bool_and(wip_limits) FROM task_mgmt.projects WHERE team = $1), false) AS team_wip_limits
                                FROM
                                    task_mgmt.teams
                                WHERE
                                    id = $1
                            `;

                            const result = await db.promiseReadQuery(query, [team_id_]);

                            if (!result && !result.rows) {
                                para_cb();
                                return;
                            }

                            const [
                                { time_estimate_rollup, points_estimate_rollup, time_tracking_rollup, team_wip_limits },
                            ] = result.rows;

                            para_cb(null, {
                                time_estimate_rollup,
                                points_estimate_rollup,
                                time_tracking_rollup,
                                team_wip_limits,
                            });
                        } catch (err) {
                            para_cb(err);
                        }
                    },

                    async template_names(para_cb) {
                        if (!form_templates.length || !userid) {
                            para_cb();
                            return;
                        }
                        try {
                            const perm_id_to_real_id = await templateHelpers._idByPermanentIds(form_templates);
                            const real_template_ids = form_templates.map(id => perm_id_to_real_id.get(id));
                            const query = `
                                SELECT
                                    items.id,
                                    items.name
                                FROM task_mgmt.team_members
                                JOIN task_mgmt.items
                                    ON items.team_id = team_members.team_id
                                JOIN task_mgmt.template_options
                                    ON items.id = template_options.template_id
                                    AND template_options.type = 'task'
                                LEFT OUTER JOIN task_mgmt.used_task_templates
                                    ON used_task_templates.userid = $2
                                    AND items.id = used_task_templates.id
                                LEFT OUTER JOIN task_mgmt.task_template_members
                                    ON task_template_members.task_id = items.id
                                    AND task_template_members.userid = $2
                                WHERE team_members.userid = $2
                                  AND items.id = ANY($1)
                                  AND items.template = TRUE
                                  AND items.deleted = FALSE
                                  AND (
                                        (items.permissions = 1 OR items.permissions IS NULL)
                                        OR (items.permissions = 4 AND team_members.role != 4)
                                        OR items.permissions = 2
                                        OR (items.permissions = 0 AND task_template_members.task_id IS NOT NULL)
                                        OR (items.permissions = 3 AND items.owner = $2)
                                  )

                                UNION

                                SELECT
                                    items.id,
                                    items.name
                                FROM task_mgmt.items
                                JOIN task_mgmt.subcategories
                                    ON subcategories.id = items.subcategory
                                JOIN task_mgmt.categories
                                    ON categories.id = subcategories.category
                                JOIN task_mgmt.projects
                                    ON projects.id = categories.project_id
                                JOIN tasK_mgmt.team_members
                                    ON team_members.team_id = projects.team
                                    AND team_members.userid = $2
                                JOIN task_mgmt.template_options
                                    ON items.id = template_options.template_id
                                    AND template_options.type = 'task'
                                LEFT OUTER JOIN task_mgmt.items as parent ON items.parent = parent.id
                                LEFT OUTER JOIN tasK_mgmt.task_members
                                    ON task_members.task_id = coalesce(items.parent, items.id)
                                    AND task_members.userid = $2
                                    AND task_members.permission_level = ANY('{4,5}')
                                LEFT OUTER JOIN tasK_mgmt.subcategory_members
                                    ON subcategory_members.subcategory = subcategories.id
                                    AND subcategory_members.userid = $2
                                    AND subcategory_members.permission_level = ANY('{4,5}')
                                LEFT OUTER JOIN tasK_mgmt.category_members
                                    ON category_members.category = categories.id
                                    AND category_members.userid = $2
                                    AND category_members.permission_level = ANY('{4,5}')
                                LEFT OUTER JOIN tasK_mgmt.project_members
                                    ON project_members.project_id = projects.id
                                    AND project_members.userid = $2
                                    AND project_members.permission_level = ANY('{4, 5}')
                                LEFT OUTER JOIN task_mgmt.used_task_templates
                                    ON used_task_templates.userid = $2
                                    AND items.id = used_task_templates.id
                                WHERE items.id = template_options.template_id
                                  AND template_options.type = 'task'
                                  AND items.template = true
                                  AND items.deleted = false
                                  AND items.team_id IS NULL
                                  AND items.subcategory = subcategories.id
                                  AND subcategories.category = categories.id
                                  AND categories.project_id = projects.id
                                  AND team_members.team_id = projects.team
                                  AND team_members.userid = $2
                                  AND items.id = ANY($1)
                                  AND (task_members.userid IS NOT NULL
                                    OR (((items.PRIVATE = FALSE AND items.parent IS NULL)
                                        OR parent.private = false)
                                        AND ( subcategory_members.userid IS NOT NULL
                                            OR ( subcategories.PRIVATE = FALSE
                                                AND ( category_members.userid IS NOT NULL
                                                    OR ( categories.PRIVATE = FALSE
                                                        AND ( project_members.userid IS NOT NULL
                                                            OR ( team_members.ROLE != 4
                                                                AND projects.PRIVATE = FALSE ) ) ) ) ) ) ) )`;
                            const { rows } = await db.promiseReadQuery(query, [real_template_ids, userid]);

                            const template_map = {};
                            form_templates.forEach(id => {
                                const template = rows.find(row => row.id === perm_id_to_real_id.get(id));
                                if (template && template.name) {
                                    template_map[id] = template.name;
                                }
                            });
                            para_cb(null, template_map);
                        } catch (err) {
                            para_cb(new ViewsError(err, 'VIEWS_002'));
                        }
                    },

                    async templateTags(para_cb) {
                        if (parent_type !== parent_types.template) {
                            para_cb();
                            return;
                        }

                        try {
                            const view_template_ids = viewRows.map(row => row.id);
                            const tags = await templateTags.getTemplateTags(view_template_ids, 'view');
                            viewRows.forEach(row => {
                                row.tags = tags[row.id] || [];
                            });
                            para_cb();
                        } catch (err) {
                            para_cb(err);
                        }
                    },

                    async pages(para_cb) {
                        if (!view_id || !include_pages) {
                            para_cb();
                            return;
                        }

                        if (!viewRows[0] || viewRows[0].type !== view_types.doc) {
                            para_cb();
                            return;
                        }

                        try {
                            const result = await getViewPages(userid, view_id, { ...options, skip_access: true });
                            para_cb(null, result.pages);
                        } catch (err) {
                            para_cb(err);
                        }
                    },

                    async pageDefaults(para_cb) {
                        if (!view_id || viewRows[0]?.type !== view_types.doc || parent_type === parent_types.template) {
                            para_cb();
                            return;
                        }

                        let pageDefaults = null;

                        if (shouldFetchPageDefaults(team_id)) {
                            try {
                                const docsV3Service = DocsAppContext.getDocsV3Service();
                                pageDefaults = await docsV3Service.getPageDefaults(userid, team_id, view_id);
                            } catch (err) {
                                para_cb(err);
                            }
                        }

                        viewRows[0].pageDefaults = pageDefaults;

                        para_cb();
                    },

                    useGantt(para_cb) {
                        if (
                            options.is_template ||
                            !options.use_gantt ||
                            !view_id ||
                            !(viewRows[0] && Number(viewRows[0].type) === view_types.gantt)
                        ) {
                            para_cb();
                            return;
                        }

                        helpers.incrementGanttUsage({ view_id, team_id }, para_cb);
                    },

                    validateGantt(para_cb) {
                        if (
                            options.is_template ||
                            !options.use_gantt ||
                            !view_id ||
                            !(viewRows[0] && Number(viewRows[0].type) === view_types.gantt)
                        ) {
                            para_cb();
                            return;
                        }

                        helpers.checkGanttPaywall({ view_id }, para_cb);
                    },

                    incrementEmbedUsage(para_cb) {
                        if (!embedViewLoad) {
                            para_cb();
                            return;
                        }

                        embedPaywallService
                            .incrementLimit({ teamId: team_id })
                            .then(() => {
                                para_cb();
                            })
                            .catch(err => {
                                para_cb(err);
                            });
                    },

                    async incrementMapUsage(para_cb) {
                        if (!mapViewLoad || !options.from_request) {
                            para_cb();
                            return;
                        }
                        try {
                            await helpers.checkMapPaywall({ team_id });
                            await helpers.incrementMapUsage({ team_id });
                            para_cb();
                        } catch (error) {
                            para_cb(error);
                        }
                    },

                    validateEmbedUsage(para_cb) {
                        if (!embedViewLoad) {
                            para_cb();
                            return;
                        }

                        embedPaywallService
                            .checkPaywall({ teamId: team_id })
                            .then(() => {
                                para_cb();
                            })
                            .catch(err => {
                                para_cb(err);
                            });
                    },

                    avatars(para_cb) {
                        if (!avatar_ids || !avatar_ids.length) {
                            para_cb();
                            return;
                        }

                        attachmentMod._getAttachmentsByID({ ids: avatar_ids }, (err, result) => {
                            if (err) {
                                para_cb(err);
                                return;
                            }

                            result.attachments.forEach(attachment => {
                                avatars[attachment.id] = attachment;
                            });

                            para_cb();
                        });
                    },

                    async attachments(para_cb) {
                        if (!options.include_attachments) {
                            para_cb();
                            return;
                        }

                        try {
                            const result = await attachmentMod.getAttachmentsOfParents(
                                view_ids,
                                config.attachments.types.view
                            );
                            para_cb(null, result);
                        } catch (e) {
                            para_cb(e);
                        }
                    },

                    required_templates(para_cb) {
                        if (view_id || in_view_ids || !options.required_templates) {
                            para_cb();
                            return;
                        }

                        helpers.getHierarchyTemplates(userid, parent_id, parent_type, { parent_permissions }, para_cb);
                    },

                    sorting(para_cb) {
                        const query = 'SELECT * FROM task_mgmt.view_sort WHERE view_id = ANY($1)';
                        const params = [view_ids];

                        db.readQuery(query, params, para_cb);
                    },

                    columns(para_cb) {
                        const query = 'SELECT * FROM task_mgmt.view_cols WHERE view_id = ANY($1)';
                        const params = [view_ids];

                        dbReadQuery(query, params, para_cb);
                    },

                    filters(para_cb) {
                        const query = 'SELECT * FROM task_mgmt.view_filters WHERE view_id = ANY($1)';
                        const params = [view_ids];

                        db.readQuery(query, params, para_cb);
                    },

                    filter_groups(para_cb) {
                        const query = 'SELECT * FROM task_mgmt.view_filter_groups WHERE view_id = ANY($1)';
                        const params = [view_ids];

                        db.readQuery(query, params, para_cb);
                    },

                    form_fields(para_cb) {
                        const query = `SELECT * FROM task_mgmt.form_fields WHERE form_id = ANY ($1) ORDER BY idx`;
                        const params = [view_ids];

                        db.readQuery(query, params, para_cb);
                    },

                    form_result_count(para_cb) {
                        const params = [view_ids];
                        let subcategoryFilter = '';
                        const view = views[view_id];
                        if (view?.type === view_types.form && view?.view_subcategory_id) {
                            subcategoryFilter = `AND items.subcategory = $${params.push(view.view_subcategory_id)}`;
                        }
                        const query = `
                            SELECT items.form_id, count(*)
                            FROM task_mgmt.items
                            WHERE items.form_id = ANY ($1)
                                AND items.deleted = false
                                AND items.archived = false
                                AND items.form_id IS NOT NULL
                                ${subcategoryFilter}
                            GROUP BY form_id`;
                        db.replicaQuery(query, params, (err, result) => {
                            if (err) {
                                para_cb(new ViewsError(err, 'VIEWS_061'));
                                return;
                            }

                            const result_map = {};

                            result.rows.forEach(row => {
                                result_map[row.form_id] = Number(row.count);
                            });

                            para_cb(null, result_map);
                        });
                    },

                    async followers(para_cb) {
                        try {
                            const result = await getFollowersAsync(view_ids);
                            para_cb(null, result);
                        } catch (err) {
                            para_cb(err);
                        }
                    },

                    hierarchy_members(para_cb) {
                        let type = parent_type;
                        let id = parent_id;
                        const view = Object.values(views)?.[0] || { parent: {} };

                        if (view_id && options.hierarchy_members) {
                            type = Number(view.parent.type);
                            id = view.parent.id;
                        }

                        // Share with team applies to docs when users
                        // decide to "share with workspace"
                        if (
                            view_id &&
                            view?.type === config.views.view_types.doc &&
                            view?.share_with_team === true &&
                            shouldSetParentTypeTeamForDocViews()
                        ) {
                            type = parent_types.team;
                            id = view.team_id;
                        }

                        if (options.skip_hierarchy_members) {
                            para_cb(null, { members: [] });
                            return;
                        }

                        let hierarchy_members_func = null;
                        if (type === parent_types.team || type === parent_types.team_unparented) {
                            hierarchy_members_func = teamMembers._getTeamMembers;
                        } else if (parent_type === parent_types.shared) {
                            para_cb(null, { members: [] });
                            return;
                        } else if (type === parent_types.project) {
                            hierarchy_members_func = projectMembers._getHierarchyMembers;
                        } else if (type === parent_types.category) {
                            hierarchy_members_func = catMembers._getHierarchyMembers;
                        } else if (type === parent_types.subcategory) {
                            hierarchy_members_func = subcatMembers._getHierarchyMembers;
                        } else if (type === parent_types.task) {
                            hierarchy_members_func = taskMembers._getHierarchyMembers;
                        } else {
                            para_cb(null, { members: [] });
                            return;
                        }

                        hierarchy_members_func(
                            userid,
                            id,
                            {
                                skipAccess: true,
                                skipTeamMemberAccessCheck: true,
                                v2: true,
                                respectLowerLevelPrivacy:
                                    type === parent_types.task && views[view_id]?.type === view_types.doc,
                            },
                            (err, result) => {
                                if (
                                    // you can have access to a doc or a whiteboard
                                    // even if you dont have access to its parent
                                    view_id &&
                                    views[view_id] &&
                                    (views[view_id].type === view_types.doc ||
                                        views[view_id].type === view_types.clickboard) &&
                                    err &&
                                    err.ECODE.includes('ACCESS') &&
                                    views[view_id].view_access
                                ) {
                                    para_cb(null, { members: {}, group_members: {} });
                                } else if (err) {
                                    para_cb(err);
                                } else {
                                    para_cb(null, result);
                                }
                            }
                        );
                    },

                    project_features(para_cb) {
                        if (!options.project_features) {
                            para_cb();
                            return;
                        }

                        const view = Object.values(views)[0] || { parent: {} };
                        const { type, id } = view.parent;

                        if (!type && !id && !options.parent_id && !options.parent_type && !view_id) {
                            para_cb();
                            return;
                        }

                        getProjectId(id, type, options, (err, result) => {
                            if (err) {
                                para_cb(err);
                                return;
                            }

                            const { project_id, project_ids } = result;

                            if (!project_id && !project_ids) {
                                para_cb();
                                return;
                            }

                            if (project_ids) {
                                projectMod._getProjects(
                                    userid,
                                    null,
                                    {
                                        skipAccess: true,
                                        project_ids,
                                        public: options.check_public,
                                        simple: true,
                                        team_time_in_status_feature: options.team_time_in_status_feature,
                                    },
                                    (err2, result2) => {
                                        if (err2) {
                                            para_cb(err2);
                                            return;
                                        }

                                        para_cb(null, {
                                            all_project_features: result2.map(row => {
                                                if (options.team_time_in_status_feature) {
                                                    row.features.time_in_status = row.time_in_status;
                                                }
                                                return {
                                                    id: row.id,
                                                    features: row.features,
                                                };
                                            }),
                                        });
                                    }
                                );
                                return;
                            }

                            projectMod._getProjects(
                                userid,
                                null,
                                {
                                    skipAccess: true,
                                    project_ids: [project_id],
                                    public: options.check_public,
                                    simple: true,
                                    team_time_in_status_feature: options.team_time_in_status_feature,
                                },
                                (err2, result2) => {
                                    if (err2) {
                                        para_cb(err2);
                                        return;
                                    }

                                    const [project] = result2;

                                    if (!project) {
                                        para_cb();
                                        return;
                                    }
                                    if (options.team_time_in_status_feature) {
                                        project.features.time_in_status = project.time_in_status;
                                    }
                                    para_cb(null, {
                                        project_id,
                                        features: project.features,
                                    });
                                }
                            );
                        });
                    },

                    members(para_cb) {
                        const query = `
                            SELECT
                                view_members.view_id,
                                COALESCE(view_members.permission_level, group_member.permission_level) AS permission_level,
                                team_members.role,
                                team_members.role_subtype,
                                views.type as view_type,
                                users.id,
                                users.username,
                                users.email,
                                users.color,
                                users.profile_picture_key,
                                users.date_joined
                            FROM
                                task_mgmt.view_members
                                    INNER JOIN task_mgmt.users ON users.id = view_members.userid
                                    INNER JOIN task_mgmt.team_members ON team_members.userid = users.id
                                    INNER JOIN task_mgmt.views ON views.team_id = team_members.team_id AND view_members.view_id = views.view_id
                                    LEFT JOIN LATERAL(
                                    SELECT
                                        view_group_members.view_id,
                                        COALESCE(MAX(view_group_members.permission_level), 1) as permission_level
                                    FROM task_mgmt.view_group_members
                                             JOIN task_mgmt.group_members ON
                                        group_members.group_id = view_group_members.group_id AND
                                        group_members.userid = view_members.userid
                                    WHERE
                                        view_group_members.view_id = view_members.view_id
                                    GROUP BY view_group_members.view_id
                                    ) AS group_member ON group_member.view_id = view_members.view_id
                            WHERE views.view_id = ANY($1)
                            AND users.user_type IS NULL`;
                        const params = [view_ids];

                        db.replicaQuery(query, params, (err, result) => {
                            if (err) {
                                para_cb(err);
                                return;
                            }

                            result.rows.forEach(row => {
                                if (
                                    Number(row.role) === config.team_roles.owner &&
                                    row.view_type === config.views.view_types.doc
                                ) {
                                    row.permission_level = 5;
                                }
                            });

                            para_cb(null, result);
                        });
                    },

                    async groupMembers(para_cb) {
                        try {
                            const grp_result = await getMembers(view_ids);
                            para_cb(null, grp_result);
                        } catch (e) {
                            para_cb(e);
                        }
                    },

                    fields(para_cb) {
                        const allowedParentTypes = [
                            config.views.parent_types.team,
                            config.views.parent_types.project,
                            config.views.parent_types.category,
                            config.views.parent_types.subcategory,
                        ];
                        const filterFieldColumnsInViews = parent_id && allowedParentTypes.includes(parent_type);

                        if (!options.fields && !filterFieldColumnsInViews) {
                            para_cb();
                            return;
                        }

                        const field_options = {
                            hierarchy_fields: true,
                            skipAccess: true,
                            use_replica,
                        };

                        if (Number(parent_type) === config.views.parent_types.team) {
                            field_options.team_id = parent_id;
                        }

                        if (Number(parent_type) === config.views.parent_types.project) {
                            field_options.project_id = parent_id;
                        }

                        if (Number(parent_type) === config.views.parent_types.category) {
                            field_options.category_id = parent_id;
                        }

                        if (Number(parent_type) === config.views.parent_types.subcategory) {
                            field_options.subcategory_id = parent_id;
                        }

                        fieldMod._getFields(userid, field_options, (err, result) => {
                            if (err) {
                                para_cb(err);
                            } else {
                                para_cb(null, result.fields);
                            }
                        });
                    },

                    tasksMentioned(para_cb) {
                        if (!view_id) {
                            para_cb();
                            return;
                        }

                        tasksMentioned._getViewMentions(
                            userid,
                            view_id,
                            {
                                viewType: views[view_id]?.type,
                                workspaceId: team_id,
                            },
                            para_cb
                        );
                    },

                    templateMembers(para_cb) {
                        const query = `
                            SELECT
                                   view_id,
                                   users.id,
                                   users.username,
                                   users.email,
                                   users.color,
                                   users.profile_picture_key
                            FROM task_mgmt.view_template_members
                            INNER JOIN task_mgmt.users
                                ON users.id = view_template_members.userid
                            WHERE view_id = ANY ($1)`;
                        const params = [view_ids];

                        db.readQuery(query, params, (err, result) => {
                            if (err) {
                                para_cb(new ViewsError(err, 'VIEWS_083'));
                                return;
                            }

                            const member_map = {};

                            result.rows.forEach(row => {
                                const { view_id: _view_id } = row;
                                const member = formatUser(row);

                                if (!member_map[_view_id]) {
                                    member_map[_view_id] = [];
                                }

                                member_map[_view_id].push(member);
                            });

                            para_cb(null, member_map);
                        });
                    },

                    async templateGroupMembers(para_cb) {
                        try {
                            const { rows: template_group_members } = await db.promiseReadQuery(
                                `
                                SELECT * FROM task_mgmt.view_template_group_members WHERE view_id = ANY($1)
                            `,
                                [view_ids]
                            );

                            const group_ids = [...new Set(template_group_members.map(row => row.group_id))];
                            const { groups } = await groupMod.promiseGetUserGroupsByIds(userid, group_ids, {
                                skipAccess: true,
                            });
                            const groups_map = commonHelpers.convertArrayToObjectWithKeys(groups, 'id');
                            const group_member_map = {};
                            template_group_members.forEach(row => {
                                const { view_id: _view_id, group_id } = row;
                                const group = groups_map[group_id];

                                if (!group) {
                                    return;
                                }

                                if (!group_member_map[_view_id]) {
                                    group_member_map[_view_id] = [];
                                }

                                group_member_map[_view_id].push(group);
                            });

                            para_cb(null, group_member_map);
                        } catch (e) {
                            para_cb(e);
                        }
                    },

                    async view_groups(para_cb) {
                        if (!options.group_views) {
                            para_cb();
                            return;
                        }
                        try {
                            storedViewGroups = await queryViewGroupsByParent(parent_id, parent_type);
                        } catch (err) {
                            logger.warn({
                                msg: 'Cannot get view groups',
                                err,
                            });
                        }
                        para_cb();
                    },

                    async doc_settings(para_cb) {
                        if (!view_ids.length) {
                            para_cb();
                            return;
                        }

                        try {
                            const docSettingsMap = await getMappedDocSettings(userid, view_ids);
                            para_cb(null, docSettingsMap);
                        } catch (err) {
                            para_cb(err);
                        }
                    },

                    async find_canonical_channels(para_cb) {
                        // Only fetch canonical channels if the request is from default_views endpoint
                        if (only_default_views && shouldIdentifyCanonicalChannel(team_id)) {
                            try {
                                has_canonical_channel = await hasCanonicalChatViews(parent_type, parent_id, team_id);
                            } catch (err) {
                                para_cb(err);
                                return;
                            }
                        }
                        para_cb();
                    },
                },
                async (propsErr, props) => {
                    if (propsErr) {
                        cb(new ViewsError(propsErr, 'VIEWS_003', 500));
                        return;
                    }

                    const propsToCheckWorkspace = ['sorting', 'columns', 'filters', 'filter_groups', 'form_fields'];
                    propsToCheckWorkspace.forEach(prop => {
                        if (props[prop]?.rows?.length) {
                            props[prop].rows.forEach(row => {
                                denormalization.verifyWorkspaceId(
                                    team_id,
                                    row.workspace_id,
                                    QueryIdentifiers.GetViewsProps,
                                    { prop }
                                );
                            });
                        }
                    });

                    let attachments = {};
                    if (options.include_attachments) {
                        ({ attachments } = props.attachments);
                    }

                    let time_estimate_rollup;
                    let points_estimate_rollup;
                    let time_tracking_rollup;
                    if (props.getTeamClickApps) {
                        ({ time_estimate_rollup, points_estimate_rollup, time_tracking_rollup } =
                            props.getTeamClickApps);
                    }

                    const getAccessibleCustomFieldIds = fields => {
                        if (!fields) {
                            return undefined;
                        }
                        const { team = [], project = [], category = [], subcategory = [] } = fields;
                        const getFieldIdWithPrefix = ({ id }) => `cf_${id}`;
                        return new Set([
                            ...team.map(getFieldIdWithPrefix),
                            ...project.map(getFieldIdWithPrefix),
                            ...category.map(getFieldIdWithPrefix),
                            ...subcategory.map(getFieldIdWithPrefix),
                        ]);
                    };

                    /**
                     * ReadonlySet<FieldId>
                     */
                    const accessible_custom_field_ids = getAccessibleCustomFieldIds(props.fields);

                    const form_fields = {};
                    const form_conditional_fields = {};

                    const formFieldFactory = getFormFieldFactory();

                    try {
                        const result = await formFieldFactory.create({
                            form_field_rows: props.form_fields.rows,
                            accessible_custom_field_ids,
                        });

                        Object.assign(form_fields, result.form_fields);
                        Object.assign(form_conditional_fields, result.form_conditional_fields);
                    } catch (err) {
                        cb(
                            new ViewsError(
                                'An error occurred while reading form fields.',
                                ViewErrorCodes.FormFieldFactoryError,
                                500
                            )
                        );
                        return;
                    }

                    props.sorting.rows.forEach(row => {
                        const view = views[row.view_id];

                        if (!view || !view.sorting) {
                            return;
                        }

                        let link;

                        if (row.field.includes('rollup:')) {
                            link = {
                                type: row.link_type,
                                field_id: row.linked_to,
                                func: row.linked_func,
                                hidden: row.hidden_types,
                            };
                        }

                        view.sorting.fields.push({
                            field: row.field,
                            dir: row.dir,
                            idx: row.idx,
                            link,
                        });
                    });

                    props.columns.rows.forEach(row => {
                        const view = views[row.view_id];

                        if (!view || !view.columns) {
                            return;
                        }

                        let calculation;

                        if (row.func) {
                            calculation = {
                                func: row.func,
                                unit: row.unit,
                                groups: row.groups || [],
                            };
                        }

                        let link;

                        if (options.check_public) {
                            if (
                                row.field === 'timeEstimate' &&
                                time_estimate_rollup &&
                                view.settings.show_subtasks !== 3
                            ) {
                                row.field = 'timeEstimateRollup';
                            } else if (
                                row.field === 'pointsEstimate' &&
                                points_estimate_rollup &&
                                view.settings.show_subtasks !== 3
                            ) {
                                row.field = 'pointsEstimateRollup';
                            } else if (
                                row.field === 'timeLogged' &&
                                time_tracking_rollup &&
                                view.settings.show_subtasks !== 3
                            ) {
                                row.field = 'timeLoggedRollup';
                            }
                        }

                        if (row.field.includes('rollup:')) {
                            link = {
                                type: row.link_type,
                                field_id: row.linked_to,
                                func: row.linked_func,
                                hidden: row.hidden_types,
                            };
                        }

                        const col = {
                            field: row.field,
                            idx: row.idx,
                            width: row.width,
                            hidden: row.hidden || false,
                            name: row.name,
                            display: row.display,
                            calculation,
                            link,
                        };

                        if (view.type === view_types.table) {
                            col.pinned = row.pinned;
                        }

                        const isCustomFieldColumn = row.field.startsWith('cf_');
                        const existsInTheLocation =
                            !accessible_custom_field_ids || accessible_custom_field_ids.has(row.field);

                        if (!isCustomFieldColumn || existsInTheLocation) {
                            view.columns.fields.push(col);
                        }
                    });

                    props.filter_groups.rows.forEach(row => {
                        const view = views[row.view_id];

                        if (!view?.filters) {
                            return;
                        }

                        view.filters.filter_groups = row.groups;
                        view.filters.filter_group_ops = row.ops;
                    });

                    props.filters.rows.forEach(row => {
                        const view = views[row.view_id];

                        if (!view || !view.filters) {
                            return;
                        }

                        if (
                            view.type &&
                            Number(view.type) === Number(config.views.view_types.activity) &&
                            (row.field === 'fields' || row.field === 'date' || row.field === 'assigned_comment_users')
                        ) {
                            if (!view.activity_settings) {
                                view.activity_settings = {};
                                view.activity_settings.filters = [];
                            }

                            view.activity_settings.filters.push({
                                field: row.field,
                                idx: row.idx,
                                values: row.values,
                            });

                            return;
                        }

                        const filter = {
                            field: row.field,
                            op: row.op,
                            determinor: row.determinor,
                            idx: row.idx,
                            values: row.values,
                        };

                        view.filters.fields.push(filter);
                    });

                    Object.keys(props.followers.followers).forEach(key => {
                        if (views[key]) {
                            views[key].followers = props.followers.followers[key];
                        }
                    });

                    props.members.rows.forEach(row => {
                        if (views[row.view_id]) {
                            views[row.view_id].members.push({
                                user: {
                                    id: Number(row.id),
                                    username: row.username,
                                    email: row.email,
                                    color: row.color,
                                    initials: userInitials.getInitials(row.email, row.username),
                                    date_joined: row.date_joined,
                                    profilePicture: cf_sign.getCloudfrontAvatarUrl(row.profile_picture_key),
                                },
                                role: row.role,
                                role_subtype: isLimitedMemberEnabled ? row.role_subtype : undefined,
                                role_key: getRoleKey(row.role, isLimitedMemberEnabled ? row.role_subtype : undefined),
                                permission_level: row.permission_level,
                                permissions: config.views.permission_levels[row.permission_level],
                            });
                        }
                    });

                    const { template_names = {}, templateMembers, templateGroupMembers } = props;
                    const now = new Date();

                    views = Object.values(views);
                    views.forEach(view => {
                        if (!config.views.page_views.includes(view.type)) {
                            view.sorting.fields.sort((a, b) => a.idx - b.idx);
                            view.columns.fields.sort((a, b) => a.idx - b.idx);
                            view.filters.fields.sort((a, b) => a.idx - b.idx);
                        }

                        const { entitlements } = paraResult;
                        const paywallFeaturesEnabled = _.mapValues(entitlements, value => value.entitled);

                        if (view.type === view_types.doc) {
                            if (include_pages) {
                                view.pages = props.pages;
                            }
                            if (props.doc_settings[view.id]) {
                                view.doc_settings = props.doc_settings[view.id];
                            } else {
                                view.doc_settings = {};
                            }
                        }

                        if (options.include_attachments) {
                            view.attachments = attachments[view.id] || [];
                        }

                        view.avatar = {
                            value: view.avatar_value,
                            avatar_color: view.avatar_color,
                            attachment_id: view.avatar_source,
                            attachment: avatars[view.avatar_source],
                        };

                        view.branding = {
                            rectangle_logo: null,
                        };

                        if (view.rectangle_logo && paywallFeaturesEnabled.white_labeling) {
                            view.branding.rectangle_logo = cf_sign.getCloudfrontPublicURL(view.rectangle_logo);
                        }

                        delete view.avatar_value;
                        delete view.avatar_source;
                        delete view.avatar_color;

                        if (view.template_visibility != null) {
                            view.template_members = templateMembers[view.id] || [];
                            view.template_group_members = templateGroupMembers[view.id] || [];
                        }

                        if (
                            (view.public || (view.type === view_types.form && view.form_active)) &&
                            view.public_share_expires_on &&
                            Number(view.public_share_expires_on) &&
                            paywallFeaturesEnabled.public_link_restrictions &&
                            Number(view.public_share_expires_on) < now.getTime()
                        ) {
                            view.public = false;

                            if (view.type === view_types.form && view.form_active) {
                                view.form_active = false;
                            }
                        }

                        view.hierarchy_members = [];

                        if (Array.isArray(props.hierarchy_members.members)) {
                            view.hierarchy_members = props.hierarchy_members.members;
                        } else {
                            view.hierarchy_members = [].concat(...Object.values(props.hierarchy_members.members));
                        }

                        if (Array.isArray(props.hierarchy_members.group_members)) {
                            view.hierarchy_group_members = props.hierarchy_members.group_members;
                        } else {
                            view.hierarchy_group_members = [].concat(
                                ...Object.values(props.hierarchy_members.group_members || {})
                            );
                        }

                        if (props.project_features) {
                            if (props.project_features.all_project_features) {
                                view.all_project_features = props.project_features.all_project_features;
                            } else {
                                view.project_features = props.project_features.features;
                                view.project_id = props.project_features.project_id;
                            }
                        }

                        const can_unfreeze = view.hierarchy_members.filter(member => {
                            if (member.role === config.team_roles.guest) {
                                return false;
                            }

                            const view_permission = view.members.find(
                                view_member => view_member.user.id === member.user.id
                            );

                            if (view_permission != null) {
                                return view_permission.permission_level === 5;
                            }

                            let member_permission_level = member.permission_level;

                            if (view.locked_permission === 1) {
                                member_permission_level = 5;
                            } else if (view.locked_permission === 2) {
                                member_permission_level = 1;
                            } else if (view.locked_permission === 3 && member.role > config.team_roles.admin) {
                                member_permission_level = 1;
                            }

                            return [4, 5].includes(member_permission_level);
                        });

                        const can_unfreeze_groups = view.hierarchy_group_members.filter(group_member => {
                            const view_permission = view.members.find(
                                view_group_member =>
                                    (view_group_member.group && view_group_member.group.id) ===
                                    (group_member.group && group_member.group.id)
                            );

                            if (view_permission != null) {
                                return view_permission.permission_level === 5;
                            }

                            let group_member_permission_level = group_member.permission_level;

                            if (view.locked_permission === 1) {
                                group_member_permission_level = 5;
                            } else if (view.locked_permission === 2) {
                                group_member_permission_level = 1;
                            }

                            return [4, 5].includes(group_member_permission_level);
                        });

                        view.can_unfreeze_count =
                            can_unfreeze.length + (can_unfreeze_groups && can_unfreeze_groups.length) || 0;

                        if (view.type === config.views.view_types.calendar) {
                            view.calendar_settings = view.extra;
                        }

                        if (view.type === config.views.view_types.board && view.extra && view.extra.board_settings) {
                            view.board_settings = view.extra.board_settings;
                        }

                        if (view.type === config.views.view_types.workload) {
                            view.workload_settings = view.extra || {};
                        }

                        if (view.type === config.views.view_types.timeline) {
                            view.timeline_settings = view.extra;
                        }

                        if (view.type === config.views.view_types.workload) {
                            view.workload_settings = view.extra;
                        }

                        if (view.type === config.views.view_types.embed) {
                            view.embed_settings = view.extra;
                        }

                        if (view.type === config.views.view_types.table) {
                            view.table_settings = view.extra;
                        }

                        if (view.type === config.views.view_types.gantt) {
                            view.gantt_settings = Object.assign({}, view.extra, view.extra_user || {});
                        }

                        if (view.type === config.views.view_types.box) {
                            view.box_settings = Object.assign({}, view.extra, view.extra_user || {});
                        }

                        // Clear the locked state if the plan does not permit any usage.
                        // This differs from paywallFeaturesEnabled because that will be false if the limit has been reached
                        // on a plan that does have a usage limit.
                        if (!entitlements.protect_views || entitlements.protect_views.limit === 0) {
                            view.locked = false;
                        }

                        if (view.type === view_types.form) {
                            view.form_settings = view.extra;
                            view.form_settings.append_all = view.form_settings.append_all || false;
                            view.form_settings.fields = form_fields[view.id];
                            view.form_settings.conditional_fields = form_conditional_fields[view.id];
                            view.form_settings.subcategory_id = view.form_subcategory_id;
                            view.form_settings.active = view.form_active;
                            view.form_settings.public_url = `${config.app.form_url}/${view.team_id}/f/${view.id}/${view.form_token}`;
                            view.form_settings.display.add_captcha = view.form_settings.display.add_captcha || false;
                            view.form_result_count = props.form_result_count[view.id] || 0;

                            const template_name = template_names[view.extra.template_id] || 'Private Template';

                            if (!view.form_settings.submission) {
                                view.form_settings.submission = {
                                    response_content:
                                        '{"ops":[{"insert":"Thank You"},{"attributes":{"align":"center","header":2},"insert":"\\n"},{"attributes":{"align":"center"},"insert":"\\n"},{"insert":"Your submission has been received."},{"attributes":{"align":"center"},"insert":"\\n"}]}',
                                    submit_text: '',
                                };
                            }

                            if (template_name) {
                                view.form_template = {
                                    id: view.extra.template_id,
                                    name: template_name,
                                };
                            }
                        }

                        if (view.type === view_types.form && view.form_settings.active) {
                            view.public_key = view.form_token;
                        }

                        if (config.views.public_views.includes(view.type) && view.public) {
                            view.public_key = helpers.getPublicKey(view.id);
                        }

                        if (view.type === view_types.board) {
                            view.settings.show_task_ids = view.extra?.show_task_ids || false;
                            view.settings.task_cover = view.extra?.task_cover || 1;
                            view.settings.field_rendering = view.extra?.field_rendering || 2;
                            view.settings.colored_columns = view.extra?.colored_columns ?? true;
                            view.settings.card_size = view.extra?.card_size || 2;
                            view.settings.show_empty_fields = view.extra?.show_empty_fields ?? true;
                            view.settings.show_task_properties = view.extra?.show_task_properties ?? true;
                        }

                        if (view.type === view_types.list) {
                            view.settings.show_task_properties = view.extra?.show_task_properties ?? true;
                            view.settings.show_sprint_cards =
                                view.extra?.show_sprint_cards ?? defaultSprintCardsSettings;
                        }

                        if (view.type === view_types.mind_map) {
                            view.mind_map_settings = Object.assign({}, view.extra || {});
                        }

                        if (view.type === view_types.clickboard) {
                            view.whiteboardsViewSettings = { whiteboardVersion: view.extra?.whiteboardVersion || 'v2' };
                        }

                        if (
                            view.type === view_types.agent_workspace ||
                            view.type === view_types.agent ||
                            view.type === view_types.agent_v1
                        ) {
                            view.agentSettings = view.extra;
                        }

                        if (view.type === view_types.map) {
                            view.map_settings = Object.assign({}, view.extra || {});

                            /** for map template return location CFs
                             *  so they can be added created to a new Location
                             */
                            if (
                                view.extra &&
                                view.extra.field_ids &&
                                view.extra.field_ids.length &&
                                view.parent &&
                                Number(view.parent.type) === parent_types.template
                            ) {
                                const fields = view.extra.field_ids.map(id => ({ field: `cf_${id}` }));
                                view.columns = { fields };
                            }
                        }

                        if (view.type === view_types.doc) {
                            view.doc_type = view.extra?.doc_type ?? doc_types.standard;
                        }

                        if (isViewRelatedToDashboard(view)) {
                            view.view_dashboard = {
                                id: getViewDashboardId(view),
                            };
                        }

                        view.parent.type = Number(view.parent.type);

                        if (include_parent_info) {
                            view.hierarchy = paraResult.parent_info[view.id];
                        }

                        if (paywallFeaturesEnabled.custom_roles && delete_items_cus_permission != null) {
                            if (
                                (delete_items_cus_permission === 3 || views_cus_permission === 0) &&
                                delete_items_cus_permission !== 1 &&
                                Number(view.creator) !== userid
                            ) {
                                view.permissions.delete_view = false;
                            } else if (delete_items_cus_permission === 3 && Number(view.creator) === userid) {
                                view.permissions.delete_view = true;
                            } else if (delete_items_cus_permission === 0) {
                                view.permissions.delete_view = false;
                            } else if (delete_items_cus_permission !== 1 && views_cus_permission === 0) {
                                view.permissions.delete_view = false;
                            }
                        }

                        if (
                            (paywallFeaturesEnabled.custom_roles || role_id === config.team_roles.guest) &&
                            views_cus_permission === 0 &&
                            Number(view.creator) === userid &&
                            view.visibility === view_visibility.private
                        ) {
                            view.permissions.edit_view = true;
                        }

                        if (view.permissions.edit_view) {
                            view.permissions.can_edit_privacy = 2;
                        }

                        delete view.wip_limits;
                        delete view.form_token;
                        delete view.form_active;
                        delete view.form_subcategory_id;
                        delete view.extra;
                        delete view.extra_user;
                        delete view.division_dir;
                        delete view.filter_op;

                        // group_members
                        view.group_members = (props.groupMembers && props.groupMembers[view.id]) || [];

                        // if doc is shared with the entire workspace
                        // then hierarchy members should have permission level 5
                        if (
                            view.type === view_types.doc &&
                            view.visibility === view_visibility.public &&
                            view.parent.type === parent_types.team &&
                            view.hierarchy_members &&
                            Array.isArray(view.hierarchy_members)
                        ) {
                            view.hierarchy_members.forEach(member => {
                                if (!member.permission_level) {
                                    member.permission_level = 5;
                                }
                            });
                        }
                    });

                    if (view_id && props.tasksMentioned && views[0]) {
                        views[0].mentions = props.tasksMentioned;
                    }

                    /**
                     * TODO:
                     * Consider removing fields property from the response.
                     */
                    const fields = options.fields ? props.fields : undefined;

                    if (view_id) {
                        cb(null, { views, fields });
                        return;
                    }

                    const {
                        list: list_type,
                        board: board_type,
                        calendar: calendar_type,
                        gantt: gantt_type,
                        box: box_type,
                        activity: activity_type,
                        timeline: timeline_type,
                        workload: workload_type_,
                        mind_map: mind_map_type,
                        table: table_type,
                        map: map_type,
                        dashboard: dashboard_type,
                    } = config.views.view_types;

                    const view_groups = {};
                    let groups;

                    if (options.group_views) {
                        /*
                         * We do have 2 types of view groups: 'view type groups' and 'custom groups' (currently we cannot create them from UI)
                         * We do not store all 'view type groups' since there is no need for that, we store them only after it was dragged (to store orderindex)
                         * Because of the fact we do not store all the groups, we do have a logic on API level which will
                         * order them by orderindex if it was persisted or by view type and default ordering by it
                         * */
                        const viewGroupsOrder = Object.values(view_types)
                            .filter(
                                viewType =>
                                    !storedViewGroups.some(
                                        viewGroup => !viewGroup.is_custom && +viewGroup.name === viewType
                                    )
                            )
                            .sort((a, b) => a - b);
                        const viewTypeViewGroupMap = {};
                        storedViewGroups
                            .sort((a, b) => a.orderindex - b.orderindex)
                            .forEach(viewGroup => {
                                if (!viewGroup.is_custom) {
                                    viewTypeViewGroupMap[viewGroup.name] = viewGroup.id;
                                }
                                viewGroupsOrder.splice(
                                    viewGroup.orderindex,
                                    0,
                                    viewGroup.isCustom ? viewGroup.name : +viewGroup.name
                                );
                            });
                        Object.keys(view_types).forEach(type_of_view => {
                            if (type_of_view.includes('_required')) {
                                return;
                            }

                            const int_type = view_types[type_of_view];

                            view_groups[int_type] = {
                                type: int_type,
                                type_name: type_of_view,
                                standard_view: null,
                                personal_views: [],
                                workspace_views: [],
                                orderindex: viewGroupsOrder.indexOf(int_type),
                                id: viewTypeViewGroupMap[int_type],
                            };
                        });

                        views.forEach(view => {
                            const type_of_view = _.findKey(view_types, int => int === Number(view.type));

                            if (!type_of_view || type_of_view.includes('_required')) {
                                return;
                            }

                            const meta_view = {
                                id: view.id,
                                default: Boolean(view.default),
                                last_view: view.id === last_view,
                            };

                            if (view.standard) {
                                view.default = Boolean(view.default);
                                view.last_view = view.id === last_view;
                                view_groups[view.type].standard_view = view;
                            } else if (view.visiblity !== view_visibility.public) {
                                view_groups[view.type].personal_views.push(meta_view);
                            } else {
                                view_groups[view.type].workspace_views.push(meta_view);
                            }
                        });

                        views = filterLocationOverviewSingleton(views);

                        groups = Object.values(view_groups).map(view_group => {
                            let count = 0;

                            count += view_group.workspace_views.length;
                            count += view_group.personal_views.length;

                            if (view_group.standard_view) {
                                count += 1;
                            }

                            view_group.count = count;

                            return view_group;
                        });
                    }

                    let standard_list_view;
                    let standard_board_view;
                    let standard_calendar_view;
                    let standard_gantt_view;
                    let standard_box_view;
                    let standard_activity_view;
                    let standard_timeline_view;
                    let standard_workload_view;
                    let standard_mind_map_view;
                    let standard_table_view;
                    let standard_map_view;
                    let standard_dashboard_view;
                    if (parent_type !== parent_types.template && !options.skip_standard) {
                        [standard_list_view] = _.remove(views, { type: list_type, standard: true });
                        [standard_board_view] = _.remove(views, { type: board_type, standard: true });
                        [standard_calendar_view] = _.remove(views, { type: calendar_type, standard: true });
                        [standard_gantt_view] = _.remove(views, { type: gantt_type, standard: true });
                        [standard_box_view] = _.remove(views, { type: box_type, standard: true });
                        [standard_activity_view] = _.remove(views, { type: activity_type, standard: true });
                        [standard_timeline_view] = _.remove(views, { type: timeline_type, standard: true });
                        [standard_workload_view] = _.remove(views, { type: workload_type_, standard: true });
                        [standard_mind_map_view] = _.remove(views, { type: mind_map_type, standard: true });
                        [standard_table_view] = _.remove(views, { type: table_type, standard: true });
                        [standard_map_view] = _.remove(views, { type: map_type, standard: true });
                        [standard_dashboard_view] = _.remove(views, { type: dashboard_type, standard: true });
                    }

                    const ret_val = {
                        views,
                        standard_views: {
                            board: standard_board_view || null,
                            list: standard_list_view || null,
                            calendar: standard_calendar_view || null,
                            gantt_type: standard_gantt_view || null,
                            box: standard_box_view || null,
                            activity: standard_activity_view || null,
                            timeline: standard_timeline_view || null,
                            workload: standard_workload_view || null,
                            mind_map: standard_mind_map_view || null,
                            table: standard_table_view || null,
                            map: standard_map_view || null,
                            dashboard: standard_dashboard_view || null,
                        },
                        fields,
                        default_view: views.find(view => view.default) || null,
                        has_canonical_channel,
                        last_view,
                        groups: groups || [],
                        last_page,
                        paging: {
                            ...(last_template && { start_id: last_template.id, start: last_template.name }),
                        },
                    };

                    if (options.required_templates) {
                        ret_val.required_templates = props.required_templates || {};
                        Object.values(ret_val.required_templates).forEach(view => {
                            if (!view) {
                                return;
                            }

                            if (Array.isArray(props.hierarchy_members.members)) {
                                view.hierarchy_members = props.hierarchy_members.members;
                            } else {
                                view.hierarchy_members = [].concat(...Object.values(props.hierarchy_members.members));
                            }

                            if (Array.isArray(props.hierarchy_members.group_members)) {
                                view.hierarchy_group_members = props.hierarchy_members.group_members;
                            } else {
                                view.hierarchy_group_members = [].concat(
                                    ...Object.values(props.hierarchy_members.group_members || {})
                                );
                            }

                            const can_unfreeze = view.hierarchy_members.filter(member => {
                                if (member.role === config.team_roles.guest) {
                                    return false;
                                }

                                const view_permission = view.members.find(
                                    view_member => view_member.user.id === member.user.id
                                );

                                if (view_permission != null) {
                                    return view_permission.permission_level === 5;
                                }

                                let member_permission_level = member.permission_level;

                                if (view.locked_permission === 1) {
                                    member_permission_level = 5;
                                } else if (view.locked_permission === 2) {
                                    member_permission_level = 1;
                                } else if (view.locked_permission === 3 && member.role > config.team_roles.admin) {
                                    member_permission_level = 1;
                                }

                                return [4, 5].includes(member_permission_level);
                            });

                            const can_unfreeze_groups = view.hierarchy_group_members.filter(group_member => {
                                const view_permission = view.members.find(
                                    view_group_member =>
                                        (view_group_member.group && view_group_member.group.id) ===
                                        (group_member.group && group_member.group.id)
                                );

                                if (view_permission != null) {
                                    return view_permission.permission_level === 5;
                                }

                                let group_member_permission_level = group_member.permission_level;

                                if (view.locked_permission === 1) {
                                    group_member_permission_level = 5;
                                } else if (view.locked_permission === 2) {
                                    group_member_permission_level = 1;
                                }

                                return [4, 5].includes(group_member_permission_level);
                            });

                            view.can_unfreeze_count =
                                can_unfreeze.length + (can_unfreeze_groups && can_unfreeze_groups.length) || 0;
                        });
                    }

                    cb(null, ret_val);
                }
            );
        }
    );
}
export { _getViews };

function _promiseGetViews(userid, options) {
    return new Promise((res, rej) => {
        _getViews(userid, options, (err, result) => {
            if (err) rej(err);
            else res(result);
        });
    });
}
export { _promiseGetViews };

function _getView(userid, view_id, options, cb) {
    options.view_id = view_id;

    _getViews(userid, options, (err, result) => {
        if (err) {
            cb(err);
        } else {
            cb(null, { view: result.views[0] });
        }
    });
}
export { _getView };

export function getViewAsync(userid, viewId, options = {}) {
    return new Promise((res, rej) => {
        _getView(userid, viewId, options, (err, result) => {
            if (err) rej(err);
            else res(result);
        });
    });
}

export function getViewReq(req, resp, next) {
    const userid = getUserFromDecodedToken(req, 'getView');
    const language = getPreferredLanguage(req);
    const user_agent = req.headers['user-agent'];
    const options = {
        from_request: true,
        view_id: req.params.view_id,
        fields: req.query.fields === 'true',
        required_templates: req.query.required_templates === 'true',
        skipAccess: req.query.skip_access_for_clickbot === 'true' && userid === config.get('clickbot_assignee'),
        include_parent_info: req.query.include_parent_info === 'true',
        use_gantt: req.query.use_gantt,
        include_pages: req.query.include_pages === 'true' || commonHelpers.isMobile(user_agent),
        include_archived: true, // FE requesting view by ID, they know what they are doing.
        language,
    };

    _getViews(userid, options, (err, result) => {
        if (err) {
            next(err);
        } else if (!result.views.length) {
            next(new ViewsError('View not found', 'VIEWS_007', 404));
        } else {
            const [view] = result.views;
            resp.json({ view, fields: result.fields });
            if (isConversationView(view.type, config.views.view_types)) {
                if (elasticParams().queueAssetOnView) {
                    reportViewChanged(view.id);
                }
                if (shouldReportAssetViewed(req.header('Referer'), req.header('User-Agent'))) {
                    reportAssetViewed(userid, req.params.view_id, 'view', 'chat_view');
                }
            } else if (
                view.type === config.views.view_types.form &&
                shouldReportAssetViewed(req.header('Referer'), req.header('User-Agent'))
            ) {
                reportAssetViewed(userid, req.params.view_id, 'form', 'form');
            } else if (
                view.type === config.views.view_types.clickboard &&
                shouldReportAssetViewed(req.header('Referer'), req.header('User-Agent'))
            ) {
                reportAssetViewed(userid, req.params.view_id, 'whiteboard', 'whiteboard');
            }

            if (view.type === config.views.view_types.doc) {
                docHelpers.markDocumentViewed(userid, req.params.view_id, view.team_id);
                const docsSearchCache = DocsAppContext.getDocsSearchCache();
                // run in background, only log error
                docsSearchCache
                    .invalidateDocsIdsCacheSortedByRecentlyViewed(view.team_id, userid)
                    .catch(e => logger.error({ msg: 'Error invalidating docs search cache', err: e }));
            }
        }
    });
}

export function getViewsReq(req, resp, next) {
    const userid = req.decoded_token.user;
    const options = req.query;

    options.from_request = true;
    options.skipAccess = req.query.skip_access_for_clickbot === 'true' && userid === config.get('clickbot_assignee');
    options.fields = options.fields === 'true';
    options.required_templates = true;
    options.group_views = true;
    options.include_archived = commonHelpers.toBooleanDefault(options.include_archived, false);

    _getViews(userid, options, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    });
}

// TODO: check if plan_id is passed in the options, it is not used here so possible free_trials queries to be removed
async function _getEditQueries(userid, _view_id, options, cb) {
    const queries = [];
    const now = new Date().getTime();
    const view_ids = options.view_ids ? options.view_ids : [_view_id];

    let fields_added = false;

    // populate throughout function and return at the end
    const versionUpdates = [];
    let hasAccessChanges = false;

    const contentQueryOptions = {};
    if (options.template_id) {
        try {
            contentQueryOptions.proxyClient = await templateHelpers.createProxyClient(
                options.template_id,
                'view',
                userid
            );
        } catch (e) {
            logger.error({
                msg: 'Failed to instantiate proxyClient',
                ECODE: 'VIEWS_119',
                err: e,
            });
        }
    }

    async.each(
        view_ids,
        (view_id, each_cb) => {
            let view;
            let parent_id;
            let parent_type;
            let cur_avatar_source;
            let extra;
            let team_id;
            let page_id_map;
            let page_content_map;
            let page_collapsed;
            let full_modal;
            let reset_public_share_expires = false;
            let entitlements = {};

            const { avatar = {}, unset_avatar, set_locked_userids = [], set_locked_groupids = [] } = options;

            options.avatar_color = avatar.color;
            options.avatar_source = avatar.attachment_id;
            options.avatar_value = avatar.value;

            async.series(
                [
                    function getParentInfo(series_cb) {
                        if (options.parent_override) {
                            parent_id = options.parent.id;
                            parent_type = options.parent.type;
                            series_cb();
                            return;
                        }

                        const query = `
                            SELECT parent_id, parent_type, avatar_source, extra, team_id
                            FROM task_mgmt.views
                            WHERE views.view_id = $1`;
                        const params = [view_id];

                        db.readQuery(query, params, (err, result) => {
                            if (err) {
                                series_cb(new ViewsError(err, 'VIEWS_053'));
                                return;
                            }

                            if (!result.rows.length) {
                                series_cb(new ViewsError('Not Found', 'VIEWS_054', 404));
                                return;
                            }

                            [{ parent_id, parent_type, avatar_source: cur_avatar_source, extra = {}, team_id }] =
                                result.rows;

                            parent_type = Number(parent_type);

                            series_cb();
                        });
                    },

                    async function getTeamId(series_cb) {
                        if (!team_id && !(parent_id && parent_type)) {
                            series_cb(new ViewsError('Not Found', 'VIEWS_054', 404));
                            return;
                        }

                        if (team_id) {
                            series_cb();
                            return;
                        }

                        try {
                            const result = await getViewTeamInfoAsync(null, {
                                parent_id,
                                parent_type,
                            });
                            team_id = result.team_id;
                            series_cb();
                        } catch (error) {
                            series_cb(error);
                        }
                    },

                    async function getEntitlements(series_cb) {
                        try {
                            entitlements = await viewCrudPaywallService.getEntitlementsEnabled(team_id);
                        } catch (error) {
                            series_cb(error);
                            return;
                        }

                        series_cb();
                    },

                    function validateAttachment(series_cb) {
                        if (!options.avatar_source) {
                            series_cb();
                            return;
                        }

                        access.checkAccessAttachment(userid, options.avatar_source, { permissions: [] }, series_cb);
                    },

                    function _validateMapSettings(series_cb) {
                        if (!options.map_settings) {
                            series_cb();
                            return;
                        }

                        const parent = {
                            id: parent_id,
                            type: parent_type,
                        };

                        validateMapSettings(options.map_settings, { parent }, series_cb);
                    },

                    async function validateColumnsAndSorting(series_cb) {
                        if (!options.columns && !options.sorting) {
                            series_cb();
                            return;
                        }

                        const { columns = {}, sorting = {} } = options;

                        const fields = [...(columns.fields || []), ...(sorting.fields || [])];

                        if (!fields.length) {
                            series_cb();
                            return;
                        }

                        const parent = {
                            id: parent_id,
                            type: parent_type,
                        };

                        try {
                            await helpers.validateColumnsAndSorting(userid, fields, parent, {});
                            series_cb();
                        } catch (e) {
                            series_cb(e);
                        }
                    },

                    function pages(series_cb) {
                        if (!options.pages || !options.pages.length) {
                            series_cb();
                            return;
                        }

                        options.pages = flattenPages(options.pages);

                        async.parallel(
                            {
                                pageMap(para_cb) {
                                    getPageIDMap(team_id, options.pages, (err, result) => {
                                        if (err) {
                                            para_cb(err);
                                            return;
                                        }

                                        ({ page_id_map } = result);

                                        para_cb();
                                    });
                                },

                                async page_content(para_cb) {
                                    if (
                                        !options.pages ||
                                        !options.pages.length ||
                                        (!options.template_id && !options.update_template_id)
                                    ) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        const page_ids = options.pages.map(page => page.id);
                                        page_content_map = await pageHelpers.getPageContent(
                                            page_ids,
                                            contentQueryOptions
                                        );
                                        para_cb();
                                    } catch (e) {
                                        para_cb(e);
                                    }
                                },

                                async collapsed(para_cb) {
                                    try {
                                        const {
                                            rows: [docSettings],
                                        } = await queryPageSettings(userid, view_id);
                                        page_collapsed = docSettings?.collapsed || [];
                                        full_modal = docSettings?.full_modal;
                                        para_cb();
                                    } catch (e) {
                                        para_cb(e);
                                    }
                                },

                                pageOffset(para_cb) {
                                    const query = `
                                        SELECT count(*)
                                        FROM task_mgmt.view_docs
                                        WHERE view_id = $1
                                            AND deleted = FALSE`;
                                    const params = [view_id];

                                    db.readQuery(query, params, (err, result) => {
                                        if (err) {
                                            para_cb(new ViewsError(err, 'VIEWS_095'));
                                            return;
                                        }

                                        if (!result.rows.length) {
                                            para_cb(new ViewsError('Not Found', 'VIEWS_096', 404));
                                            return;
                                        }

                                        const [{ count }] = result.rows;

                                        options.page_offset = Number(count || 0);

                                        para_cb();
                                    });
                                },
                            },
                            series_cb
                        );
                    },

                    function validateForm(series_cb) {
                        if (Number(options.type) !== view_types.form) {
                            series_cb();
                            return;
                        }

                        const { redirect_uri } = options.form_settings.submission || {};

                        if (redirect_uri) {
                            const link_match = linkHelpers.linkify.match(redirect_uri);

                            if (!link_match || !link_match[0]) {
                                series_cb(new ViewsError('Redirect URI is not a valid URL', 'VIEWS_071', 400));
                                return;
                            }
                        }

                        async.parallel(
                            {
                                subcategory(para_cb) {
                                    const { subcategory_id } = options.form_settings || {};

                                    if (!subcategory_id) {
                                        series_cb();
                                        return;
                                    }

                                    validateFormSubcategory(subcategory_id, parent_id, parent_type, (err, result) => {
                                        if (err) {
                                            para_cb(err);
                                            return;
                                        }

                                        const { exists } = result;

                                        if (!exists) {
                                            options.form_settings.active = false;
                                            options.form_settings.subcategory_id = null;
                                        }

                                        para_cb();
                                    });
                                },

                                async templateAccess(para_cb) {
                                    const { template_id } = options.form_settings;

                                    if (extra && extra.template_id === template_id) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        await templateHelpers._accessTemplate(userid, template_id);
                                        para_cb();
                                    } catch (err) {
                                        para_cb(err);
                                    }
                                },

                                templateMembers(para_cb) {
                                    if (options.template_visibility !== config.views.template_visibility.users) {
                                        para_cb();
                                        return;
                                    }

                                    const template_members = (options.template_members || []).map(
                                        member => member.id || member
                                    );

                                    if (!template_members.includes(userid)) {
                                        template_members.push(userid);
                                    }

                                    async.each(
                                        template_members,
                                        (member, _each_cb) => {
                                            access.checkAccessTeam(member, team_id, { permissions: [] }, _each_cb);
                                        },
                                        err => {
                                            if (err) {
                                                para_cb(err);
                                            } else {
                                                options.template_members = template_members;
                                                para_cb();
                                            }
                                        }
                                    );
                                },
                            },
                            series_cb
                        );
                    },

                    function addCustomFields(series_cb) {
                        if (
                            [
                                parent_types.shared,
                                parent_types.template,
                                parent_types.team_unparented,
                                parent_types.task,
                                parent_types.user_group,
                                parent_types.view,
                            ].includes(parent_type)
                        ) {
                            series_cb();
                            return;
                        }

                        options.columns = options.columns || {};
                        options.columns.fields = options.columns.fields || [];

                        const field_ids = options.columns.fields
                            .filter(field => !field.hidden)
                            .map(({ field }) => field)
                            .filter(field => field.includes('cf_') && !field.includes('rollup:'))
                            .map(field => field.replace('cf_', ''));

                        if (Number(options.type) === view_types.form) {
                            const { form_settings } = options;

                            form_settings.fields.forEach(({ field }) => {
                                if (field.includes('cf_')) {
                                    field_ids.push(field.replace('cf_', ''));
                                }
                            });
                        }

                        if (
                            Number(options.type) === view_types.calendar &&
                            options.calendar_settings &&
                            options.calendar_settings.showEventItems &&
                            options.calendar_settings.showEventItems.customFields
                        ) {
                            Object.keys(options.calendar_settings.showEventItems.customFields).forEach(cf_id => {
                                field_ids.push(cf_id);
                            });
                        }

                        if (Number(options.type) === view_types.timeline && options.timeline_settings) {
                            if (
                                options.timeline_settings.showEventItems &&
                                options.timeline_settings.showEventItems.customFields
                            ) {
                                Object.keys(options.timeline_settings.showEventItems.customFields).forEach(cf_id => {
                                    field_ids.push(cf_id);
                                });
                            }
                        }

                        if (Number(options.type) === view_types.workload && options.workload_settings) {
                            if (
                                options.workload_settings.showEventItems &&
                                options.workload_settings.showEventItems.customFields
                            ) {
                                Object.keys(options.workload_settings.showEventItems.customFields).forEach(cf_id => {
                                    field_ids.push(cf_id);
                                });
                            }

                            if (
                                options.workload_settings.capacity &&
                                options.workload_settings.capacity.customFieldId
                            ) {
                                field_ids.push(options.workload_settings.capacity.customFieldId);
                            }
                        }

                        if (Number(options.type) === view_types.workload && options.workload_settings) {
                            if (
                                options.workload_settings.showEventItems &&
                                options.workload_settings.showEventItems.customFields
                            ) {
                                Object.keys(options.workload_settings.showEventItems.customFields).forEach(cf_id => {
                                    field_ids.push(cf_id);
                                });
                            }

                            if (
                                options.workload_settings.capacity &&
                                options.workload_settings.capacity.customFieldId
                            ) {
                                field_ids.push(options.workload_settings.capacity.customFieldId);
                            }
                        }

                        if (
                            Number(options.type) === view_types.box &&
                            options.box_settings &&
                            options.box_settings.scrum_field_id
                        ) {
                            field_ids.push(options.box_settings.scrum_field_id);
                        }

                        if (!field_ids.length) {
                            series_cb();
                            return;
                        }

                        const opts = { skipAtParent: true };

                        if (parent_type === parent_types.team) {
                            opts.team_id = parent_id;
                        } else if (parent_type === parent_types.project) {
                            opts.project_id = parent_id;
                        } else if (parent_type === parent_types.category) {
                            opts.category_id = parent_id;
                        } else {
                            opts.subcategory_id = parent_id;
                        }

                        async.eachSeries(
                            field_ids,
                            (field_id, _each_cb) => {
                                fieldLocation._addFieldToParent(userid, field_id, opts, (err, result) => {
                                    if (err) {
                                        logViewsError(err);
                                    } else if (result && result.success) {
                                        fields_added = true;
                                    }

                                    _each_cb();
                                });
                            },
                            series_cb
                        );
                    },

                    function removeInvalid(series_cb) {
                        const opts = {
                            clone: true,
                            view_id,
                            parent_override: options.parent_override,
                            pass_if_belongs_to_hierarchy: true,
                        };

                        helpers.removeInvalidFields(userid, options, opts, (err, result) => {
                            if (err) {
                                series_cb(err);
                                return;
                            }

                            view = result;

                            series_cb();
                        });
                    },

                    async function bindOrUnbindAvatar(series_cb) {
                        try {
                            if (unset_avatar && cur_avatar_source) {
                                // Remove current avatar
                                await unbindAttachmentsToParentAsync(view_id, config.attachments.types.view, {
                                    attachmentIds: [cur_avatar_source],
                                    workspaceId: team_id,
                                });
                            } else if (!unset_avatar && view.avatar_source) {
                                if (cur_avatar_source && view.avatar_source !== cur_avatar_source) {
                                    // Remove current avatar
                                    await unbindAttachmentsToParentAsync(view_id, config.attachments.types.view, {
                                        attachmentIds: [cur_avatar_source],
                                        workspaceId: team_id,
                                    });
                                }

                                // Add new avatar
                                await bindAttachmentsToParentAsync(
                                    [view.avatar_source],
                                    view_id,
                                    config.attachments.types.view,
                                    {
                                        hidden: true,
                                        workspaceId: team_id,
                                    }
                                );
                            }
                        } catch (err) {
                            series_cb(err);
                            return;
                        }

                        series_cb();
                    },

                    async function checkGroupAccess(series_cb) {
                        if (
                            options.template_group_members &&
                            Array.isArray(options.template_group_members) &&
                            options.template_group_members.length
                        ) {
                            try {
                                await Promise.all([
                                    groupMod.checkGroupsHasAccessToTeam(
                                        options.template_group_members,
                                        'view',
                                        view_id,
                                        {}
                                    ),
                                    groupSharingPaywall.checkGroupSharingPaywall(view_id, 'view'),
                                ]);
                                series_cb();
                            } catch (e) {
                                series_cb(e);
                            }
                        } else {
                            series_cb();
                        }
                    },
                    async function checkForEmptyTemplateParent(series_cb) {
                        if (options.template_parent) {
                            try {
                                const result = await queryEmptySubPageParentIdForView(view_id, options.template_parent);
                                if (result.rows[0]?.page_id) {
                                    options.page_to_delete = options.template_parent;
                                    options.template_parent = result.rows[0].page_id;
                                } else if (result.rows[0] && result.rows[0].page_id === null) {
                                    options.page_to_delete = options.template_parent;
                                    options.template_parent = null;
                                }
                                series_cb();
                            } catch (e) {
                                series_cb(e);
                            }
                        } else {
                            series_cb();
                        }
                    },
                    async function getResetPublicShareExpires(series_cb) {
                        // we check and set reset_public_share_expires only for forms that user sets to public (activate)
                        if (Number(options.type) !== view_types.form || !options.public) {
                            series_cb();
                            return;
                        }

                        const query = `
                            SELECT public_share_expires_on
                            FROM task_mgmt.views
                            WHERE views.view_id = $1`;
                        const params = [view_id];

                        let result;
                        try {
                            result = await db.promiseReplicaQuery(query, params, {});

                            if (!result.rows.length) {
                                series_cb(new ViewsError('Not Found', 'VIEWS_112', 404));
                                return;
                            }

                            const [{ public_share_expires_on }] = result.rows;

                            reset_public_share_expires = public_share_expires_on
                                ? Number(public_share_expires_on) < now
                                : false;

                            series_cb();
                        } catch (err) {
                            series_cb(new ViewsError(err, 'VIEWS_111'));
                        }
                    },
                ],
                err => {
                    if (err) {
                        each_cb(err);
                        return;
                    }
                    const {
                        sorting = {},
                        columns = {},
                        filters = {},
                        grouping = {},
                        divide = {},
                        parent,
                        team_sidebar = {},
                        auto_save,
                        pages,
                        type,
                    } = view;

                    view = { ...view, ...view.settings };

                    // do not allow standard state to be changed
                    delete view.standard;

                    const paywallFeaturesEnabled = entitlements;

                    // only present for move view
                    if (view.parent_override) {
                        view.parent_id = parent.id;
                        view.parent_type = parent.type;
                    }

                    view.grouping_field = grouping.field;
                    view.grouping_dir = grouping.dir;
                    view.ignore_grouping = grouping.ignore || false;
                    view.collapsed_groups = grouping.collapsed || [];
                    view.group_by_single = grouping.single || false;
                    view.division_field = divide.field;
                    view.division_dir = divide.dir;
                    view.division_by_subcategory = divide.by_subcategory;
                    view.collapsed_divisions = divide.collapsed || [];
                    view.filter_op = filters.op;
                    view.search = filters.search;
                    view.search_custom_fields = filters.search_custom_fields;
                    view.search_description = filters.search_description;
                    view.search_name = filters.search_name;
                    view.show_closed = filters.show_closed || false;
                    view.sidebar_assignees = team_sidebar.assignees || [];
                    view.sidebar_group_assignees = team_sidebar.group_assignees || [];
                    view.sidebar_assigned_comments = team_sidebar.assigned_comments || false;
                    view.sidebar_unassigned_tasks = team_sidebar.unassigned_tasks || false;
                    view.show_closed_subtasks = view.show_closed_subtasks || false;
                    view.auto_wrap = view.auto_wrap || false;
                    view.is_description_pinned = view.is_description_pinned || false;
                    view.override_parent_hierarchy_filter = view.override_parent_hierarchy_filter || false;
                    view.time_in_status_view = view.time_in_status_view || 1;
                    view.sidebar_view = Boolean(options.sidebar_view);
                    view.fast_load_mode = view.fast_load_mode || false;

                    if (view.public == null) {
                        view.public = false;
                    }

                    if (view.type === view_types.form && view.form_active) {
                        view.public = true;
                    }

                    if (
                        paywallFeaturesEnabled.public_link_restrictions &&
                        view.public_share_expires_on === 0 &&
                        options.disable_never_expire_pub_links
                    ) {
                        each_cb({
                            err: 'Cannot set never expires on public link',
                            ECODE: 'VIEWS_087',
                            status: 400,
                        });
                        return;
                    }

                    if (
                        paywallFeaturesEnabled.public_link_restrictions &&
                        options.pub_links_max_year &&
                        new Date(new Date().setFullYear(new Date().getFullYear() + 1)).getTime() <
                            view.public_share_expires_on
                    ) {
                        each_cb({
                            err: 'Cannot set expires date on public link past one year',
                            ECODE: 'VIEWS_088',
                            status: 400,
                        });
                        return;
                    }

                    if (view.seo_optimized == null) {
                        view.seo_optimized = false;
                    }

                    if (view.public_duplication_enabled == null) {
                        view.public_duplication_enabled = false;
                    }

                    if (view.isStandard) {
                        view.visibility = config.views.visibility.public;
                        view.members = [];
                    }

                    if (Number(view.type) !== view_types.list) {
                        delete view.expanded_tasks;
                    }

                    if (Number(view.type) === view_types.list) {
                        view.extra = generateListExtra(view.settings, extra);
                    }

                    if (Number(view.type) === view_types.board) {
                        view.extra = generateBoardExtra(view.settings, extra?.board_settings, options.board_settings);
                    }

                    if (Number(view.type) === view_types.form) {
                        view.extra = view.form_settings;

                        const fields = view.extra.fields ?? [];
                        const conditional_fields = view.extra.conditional_fields ?? [];

                        view.form_fields = fields.concat(conditional_fields);
                        view.form_subcategory_id = view.extra.subcategory_id;
                        view.form_active = view.form_subcategory_id ? Boolean(view.extra.active) : false;

                        delete view.extra.fields;
                        delete view.extra.conditional_fields;
                        delete view.extra.subcategory_id;
                        delete view.extra.active;
                    }

                    if (Number(view.type) === view_types.calendar && view.calendar_settings) {
                        view.extra = view.calendar_settings;
                        delete view.calendar_settings;
                    }

                    if (Number(view.type) === view_types.timeline && view.timeline_settings) {
                        view.extra = view.timeline_settings;
                        delete view.timeline_settings;
                    }

                    if (Number(view.type) === view_types.workload && view.workload_settings) {
                        view.extra = view.workload_settings;
                        delete view.workload_settings;
                    }

                    if (Number(view.type) === view_types.table && view.table_settings) {
                        view.extra = view.table_settings;
                        delete view.table_settings;
                    }

                    if (Number(view.type) === view_types.embed && view.embed_settings) {
                        view.extra = view.embed_settings;
                        delete view.embed_settings;
                    }

                    if (Number(view.type) === view_types.map) {
                        view.extra = view.map_settings || {};
                        delete view.map_settings;
                    }

                    if (Number(view.type) === view_types.gantt && view.gantt_settings) {
                        view.extra = view.gantt_settings;
                        delete view.gantt_settings;

                        view.extra_user = {
                            viewSettingsUser: view.extra.viewSettingsUser,
                            openedItems: view.extra.openedItems,
                        };

                        delete view.extra.viewSettingsUser;
                        delete view.extra.openedItems;
                    }

                    if (Number(view.type) === view_types.box && view.box_settings) {
                        view.extra = view.box_settings;

                        view.extra_user = {
                            collapsed: view.extra.collapsed,
                        };

                        delete view.box_settings;
                        delete view.extra.collapsed;
                    }

                    if (Number(view.type) === view_types.mind_map && view.mind_map_settings) {
                        view.extra = view.mind_map_settings;
                        delete view.mind_map_settings;
                    }

                    if (Number(view.type) === view_types.doc && view.doc_type) {
                        view.extra = {
                            doc_type: view.doc_type,
                        };
                        delete view.doc_type;
                    }

                    if (
                        options.disable_public_sharing &&
                        (!options.admin_public_share_override || Number(options.team_role) > 2)
                    ) {
                        view.public = false;
                    }

                    if (view.share_task_fields && Array.isArray(view.share_task_fields)) {
                        view.share_task_fields = view.share_task_fields.filter(field =>
                            config.sharing.public_fields.includes(field)
                        );
                    }

                    if (!config.views.fast_load_mode_views.includes(Number(view.type))) {
                        view.fast_load_mode = false;
                    }

                    // edit base object
                    const validEditOptions = [
                        'name',
                        'template_public',
                        'visibility',
                        'template_visibility',
                        'me_view',
                        'me_comments',
                        'me_subtasks',
                        'me_checklists',
                        'grouping_field',
                        'grouping_dir',
                        'ignore_grouping',
                        'group_by_single',
                        'division_field',
                        'division_dir',
                        'division_by_subcategory',
                        'show_task_locations',
                        'show_subtasks',
                        'show_subtask_parent_names',
                        'show_closed_subtasks',
                        'show_empty_statuses',
                        'show_assignees',
                        'show_images',
                        'collapse_empty_columns',
                        'filter_op',
                        'extra',
                        'locked',
                        'search',
                        'search_custom_fields',
                        'search_description',
                        'search_name',
                        'collapsed_groups',
                        'collapsed_divisions',
                        'public',
                        'seo_optimized',
                        'default',
                        'sidebar_assignees',
                        'sidebar_group_assignees',
                        'sidebar_assigned_comments',
                        'sidebar_unassigned_tasks',
                        'frozen_note',
                        'parent_id',
                        'parent_type',
                        'team_id',
                        'show_closed',
                        'locked_permission',
                        'form_subcategory_id',
                        'form_active',
                        'share_tasks',
                        'share_task_fields',
                        'pinned',
                        'avatar_source',
                        'avatar_color',
                        'avatar_value',
                        'exclude_multiple_lists',
                        'exclude_sub_multiple_lists',
                        'show_timer',
                        'time_in_status_view',
                        'expanded_tasks',
                        'auto_wrap',
                        'is_description_pinned',
                        'override_parent_hierarchy_filter',
                        'share_with_team',
                        'sidebar_view',
                        'public_duplication_enabled',
                        'archived',
                        'fast_load_mode',
                    ];
                    // Field changes that will trigger a VIEW_ACCESS OVM event.
                    // See ViewAccessInfoRepository for details.
                    const accessEditOptions = ['parent_id', 'parent_type', 'visibility'];

                    let editQuery = `UPDATE task_mgmt.views SET date_updated = $2`;
                    const params = [view_id, Date.now()];

                    if (team_id) {
                        versionUpdates.push({
                            object_type: ObjectType.VIEW,
                            object_id: view_id,
                            workspace_id: team_id,
                            operation: OperationType.UPDATE,
                        });

                        if (view.type === view_types.clickboard) {
                            versionUpdates.push({
                                object_type: ObjectType.WHITEBOARD,
                                object_id: view_id,
                                workspace_id: team_id,
                                operation: OperationType.UPDATE,
                            });
                        }
                    }

                    Object.keys(view).forEach(key => {
                        if (unset_avatar && ['avatar_source', 'avatar_color', 'avatar_value'].includes(key)) {
                            return;
                        }

                        if (validEditOptions.includes(key)) {
                            if (params.length > 1) {
                                editQuery += `, `;
                            }
                            params.push(view[key]);
                            editQuery += ` "${key}" = $${params.length}`;

                            if (team_id && accessEditOptions.includes(key)) {
                                hasAccessChanges = true;
                            }
                        }
                    });

                    if (unset_avatar) {
                        if (params.length > 1) {
                            editQuery += `, `;
                        }

                        editQuery += `avatar_source = NULL, avatar_color = NULL, avatar_value = NULL`;
                    }
                    const valid_workload_types = Object.keys(config.views.workload_capacity_types).map(k =>
                        parseInt(k, 10)
                    );
                    if (
                        Number(view.type) === view_types.workload &&
                        options.workload_settings &&
                        valid_workload_types.includes(parseInt(options.workload_settings.capacity_type, 10))
                    ) {
                        if (params.length > 1) {
                            editQuery += `, `;
                        }
                        editQuery += `workload_capacity_type = $${params.push(
                            options.workload_settings.capacity_type
                        )}`;
                    }

                    if (view.locked && view.locked === true) {
                        params.push(userid, now);
                        editQuery += `, frozen_by = $${params.length - 1}, date_frozen = $${params.length}`;
                    } else if (view.locked === false) {
                        editQuery += `, frozen_by = null, date_frozen = null`;
                    }

                    if (
                        view.visibility &&
                        (view.visibility === config.views.visibility.private ||
                            view.visibility === config.views.visibility.personal)
                    ) {
                        params.push(userid);
                        editQuery += `, visible_to = $${params.length}`;
                    }

                    if (
                        view.template_visibility &&
                        (view.template_visibility === config.views.template_visibility.private ||
                            view.template_visibility === config.views.template_visibility.personal)
                    ) {
                        params.push(userid);
                        editQuery += `, template_visible_to = $${params.length}`;
                    }

                    if (
                        (options.public || (view.type === view_types.form && view.form_active)) &&
                        options.public_share_expires_on &&
                        paywallFeaturesEnabled.public_link_restrictions
                    ) {
                        params.push(options.public_share_expires_on);
                        editQuery += `, public_share_expires_on = $${params.length}`;
                    } else if (
                        ((options.public || (view.type === view_types.form && view.form_active)) &&
                            options.public_share_expires_on === 0 &&
                            paywallFeaturesEnabled.public_link_restrictions) ||
                        reset_public_share_expires
                    ) {
                        editQuery += `, public_share_expires_on = NULL`;
                    }

                    if (options.public || (view.type === view_types.form && view.form_active)) {
                        params.push(userid);
                        editQuery += `, made_public_by = $${params.length}`;

                        params.push(now);
                        editQuery += `, made_public_time = $${params.length}`;
                    } else if (!options.public || (view.type === view_types.form && !view.form_active)) {
                        editQuery += `, made_public_by = null, made_public_time = null`;
                    }

                    if (isConversationView(view.type, config.views.view_types)) {
                        editQuery += `, extra = $${params.push(
                            getChatViewExtra(extra, options.conversation_settings)
                        )}`;
                    }

                    editQuery += ` WHERE view_id = $1`;

                    // Not allowed to change a conversation into a conversation_dm, etc
                    if (isConversationView(view.type, config.views.view_types)) {
                        editQuery += ` AND type = $${params.push(view.type)}`;
                    }

                    queries.push({
                        query: editQuery,
                        params,
                    });

                    if (sorting.fields) {
                        // edit sorting
                        queries.push({
                            query: 'DELETE FROM task_mgmt.view_sort WHERE view_id = $1',
                            params: [view_id],
                        });

                        sorting.fields.forEach((sort, idx) => {
                            let { link } = sort;

                            const { field } = sort;
                            const sortFieldFromColumns = columns.fields.find(c => c.field === field);
                            // Skip if sort of rollup field doesn't exist in columns -- CLK-172583
                            if (!sortFieldFromColumns && type === view_types.list && field.includes('rollup:')) {
                                return;
                            }

                            if (!link) {
                                link = {};
                            }

                            if (link.type) {
                                // sort.field = `${idx}:${sort.field}`;
                            }

                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.view_sort(view_id, field, dir, idx, link_type, linked_to, hidden_types, linked_func, workspace_id)
                                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
                                params: [
                                    view_id,
                                    sort.field,
                                    sort.dir,
                                    idx,
                                    link.type,
                                    link.field_id,
                                    link.hidden,
                                    link.func,
                                    team_id,
                                ],
                            });
                        });
                    }

                    if (pages) {
                        if (parent_type === parent_types.template) {
                            queries.push({
                                query: `DELETE FROM task_mgmt.view_docs WHERE view_id = $1`,
                                params: [view_id],
                            });
                        }

                        pages.forEach(page => {
                            if (
                                (options.template_id || options.update_template_id) &&
                                page_content_map &&
                                page_content_map[page.id]
                            ) {
                                page.content = page_content_map[page.id].content;
                                page.text_content = page_content_map[page.id].text_content;
                            }

                            const createOptions = _.pick(page, [
                                'name',
                                'subtitle',
                                'color',
                                'text_content',
                                'avatar',
                                'full_width',
                                'deleted',
                                'deleted_by',
                                'public',
                                'seo_optimized',
                                'thumbnail_s3_key',
                                'cover_image_url',
                                'cover_image_color',
                                'cover_position_x',
                                'cover_position_y',
                            ]);

                            // page_id_map is a mapping of old page ids from options.pages to pretty ids for new pages
                            createOptions.parent = page.parent && page_id_map[page.parent];

                            // when applying Template the current User becomes Author & Creator
                            const creator = options.template_id ? userid : page.creator;

                            // for those pages not parented by new pages - try options.template_parent
                            if (!createOptions.parent && options.template_parent) {
                                createOptions.parent = options.template_parent;

                                if (!page_collapsed.includes(options.template_parent)) {
                                    queries.push(
                                        ...prepareDocSettingsUpdateQueries(
                                            userid,
                                            view_id,
                                            options.template_parent,
                                            page.team_id,
                                            page_collapsed,
                                            { collapsed: true, full_modal }
                                        )
                                    );
                                    page_collapsed = [...page_collapsed, options.template_parent];
                                }
                            }

                            createOptions.content =
                                ((options.template_id || options.update_template_id) && page.content) ||
                                (page.content && encrypt.encrypt(page.content));
                            createOptions.creator = creator;
                            createOptions.position = Number(page.orderindex || 0) + options.page_offset;
                            createOptions.date_deleted = page.date_deleted && new Date().getTime();
                            createOptions.team_id = team_id || view.team_id;

                            queries.push(preparePageInsertQuery(userid, view_id, page_id_map[page.id], createOptions));

                            // When applying template dropping existing Authors
                            const authorIds =
                                page.authors && page.authors.length && !options.template_id
                                    ? page.authors.map(a => a.id)
                                    : [];

                            queries.push(
                                prepareAuthorInsertQuery(
                                    creator,
                                    page_id_map[page.id],
                                    createOptions.team_id,
                                    authorIds
                                )
                            );

                            // copy over the presentation details to the new page
                            const detailsOptions = {
                                presentation_details: page.presentation_details,
                            };
                            const detailsQuery = prepareSinglePresentationDetailsUpdateQuery(
                                page_id_map[page.id],
                                createOptions.team_id,
                                detailsOptions
                            );
                            if (detailsQuery) {
                                queries.push(detailsQuery);
                            }
                        });
                    }

                    if (Number(view.type) === view_types.doc && view.creator) {
                        if (options.keep_creator) {
                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.view_members(view_id, userid, permission_level, workspace_id)
                                    VALUES ($1, $2, $3, $4)
                                    ON CONFLICT DO NOTHING
                                `,
                                params: [view_id, view.creator, 5, team_id || view.team_id],
                            });
                        } else {
                            // remove creator
                            queries.push({
                                query: `
                                    DELETE FROM task_mgmt.view_members
                                    WHERE view_id=$1 AND userid=$2 AND workspace_id=$3
                                `,
                                params: [view_id, view.creator, team_id || view.team_id],
                            });
                        }
                        hasAccessChanges = true;
                    }

                    if (view.locked_permission === config.views.locked_permission.cant_unlock) {
                        queries.push(
                            {
                                query: `
                                    INSERT INTO task_mgmt.view_members(view_id, userid, permission_level, workspace_id) (
                                        SELECT $1, $2, 5, $3 WHERE NOT EXISTS (
                                            SELECT 1
                                            FROM task_mgmt.view_members
                                            WHERE view_id = $1
                                            AND userid = $2
                                        )
                                    )`,
                                params: [view_id, userid, team_id],
                            },
                            {
                                query: `
                                UPDATE task_mgmt.view_members SET permission_level = 5 WHERE userid = $2 AND view_id = $1`,
                                params: [view_id, userid],
                            },
                            {
                                query: `
                                UPDATE task_mgmt.view_members SET permission_level = 1 WHERE userid != $2 AND view_id = $1 AND userid != ANY($3)`,
                                params: [view_id, userid, set_locked_userids || []],
                            },
                            {
                                query: `
                                    UPDATE task_mgmt.view_group_members SET permission_level = 1 WHERE view_id = $1 AND group_id != ANY($2)`,
                                params: [view_id, set_locked_groupids || []],
                            }
                        );
                        hasAccessChanges = true;
                    } else if (view.locked_permission === config.views.locked_permission.can_unlock) {
                        queries.push({
                            query: `
                                    UPDATE task_mgmt.view_members SET permission_level = 5 WHERE view_id = $1 ${
                                        set_locked_userids?.length ? `AND userid NOT IN (${set_locked_userids})` : ''
                                    }`,
                            params: [view_id],
                        });

                        // this block gives edit access to all users in the team, but avoiding this for docs CLK-615078
                        if (view.type !== view_types.doc) {
                            queries.push({
                                query: `
                                        UPDATE task_mgmt.view_group_members
                                        SET permission_level = 5
                                        WHERE view_id = $1 ${
                                            set_locked_groupids?.length
                                                ? `AND group_id::text NOT IN (SELECT unnest($2::text[]))`
                                                : ''
                                        }`,
                                params: [view_id, ...(set_locked_groupids.length ? [set_locked_groupids] : [])],
                            });
                        }

                        hasAccessChanges = true;

                        // The below check is used for comment-only doc users, to prevent them from gaining
                        // edit access when a doc's protection status is changed.
                        if (view.type === config.views.view_types.doc) {
                            const commentOnlyUsers = [];
                            view.members.forEach(member => {
                                if (
                                    Number(member.permission_level) === config.views.permission_level_constants.comment
                                ) {
                                    commentOnlyUsers.push(member.user.id);
                                }
                            });
                            if (commentOnlyUsers.length) {
                                queries.push({
                                    query: `
                                        UPDATE task_mgmt.view_members SET permission_level = 3 WHERE view_id = $1 AND userid= ANY($2) ${
                                            set_locked_userids?.length
                                                ? `AND userid NOT IN (${set_locked_userids})`
                                                : ''
                                        }`,
                                    params: [view_id, commentOnlyUsers],
                                });
                            }
                        }

                        if (set_locked_userids.length) {
                            queries.push({
                                query: `
                                    UPDATE task_mgmt.view_members SET permission_level = 1 WHERE view_id = $1 AND userid = ANY($2)`,
                                params: [view_id, set_locked_userids],
                            });
                        }
                        if (set_locked_groupids.length) {
                            queries.push({
                                query: `
                                    UPDATE task_mgmt.view_group_members SET permission_level = 1 WHERE view_id = $1 AND group_id = ANY($2)`,
                                params: [view_id, set_locked_groupids],
                            });
                        }
                    } else if (view.locked_permission === config.views.locked_permission.admins_only) {
                        queries.push(
                            {
                                query: `
                                UPDATE task_mgmt.view_members SET permission_level = 5 FROM tasK_mgmt.views, task_mgmt.team_members WHERE view_members.view_id = $1 AND views.view_id = view_members.view_id AND views.team_id = team_members.team_id AND team_members.userid = view_members.userid AND team_members.role <= $2`,
                                params: [view_id, config.team_roles.admin],
                            },
                            {
                                query: `
                                UPDATE task_mgmt.view_members SET permission_level = 1 FROM tasK_mgmt.views, task_mgmt.team_members WHERE view_members.view_id = $1 AND views.view_id = view_members.view_id AND views.team_id = team_members.team_id AND team_members.userid = view_members.userid AND team_members.role > $2`,
                                params: [view_id, config.team_roles.admin],
                            }
                        );
                        hasAccessChanges = true;
                    }

                    if (columns.fields) {
                        // edit columns
                        queries.push({
                            query: 'DELETE FROM task_mgmt.view_cols WHERE view_id = $1',
                            params: [view_id],
                        });

                        columns.fields.forEach((col, idx) => {
                            let { calculation, link } = col;

                            if (!calculation) {
                                calculation = {};
                            }

                            if (!link) {
                                link = {};
                            }

                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.view_cols(
                                        view_id, field, idx, width, hidden, func, unit, groups, pinned, link_type,
                                        linked_to, hidden_types, linked_func, name, display, workspace_id
                                    )
                                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16) ON CONFLICT (view_id, field) DO NOTHING;`,
                                params: [
                                    view_id,
                                    col.field,
                                    idx,
                                    col.width,
                                    col.hidden,
                                    calculation.func,
                                    calculation.unit,
                                    calculation.groups,
                                    col.pinned,
                                    link.type,
                                    link.field_id,
                                    link.hidden,
                                    link.func,
                                    col.name,
                                    col.display,
                                    team_id || options.team_id,
                                ],
                            });
                        });
                    }

                    if (auto_save != null || view.extra_user) {
                        queries.push({
                            query: `
                            INSERT INTO task_mgmt.view_preferences(view_id, userid, workspace_id) (
                                SELECT $1, $2, $3 WHERE NOT EXISTS (
                                    SELECT 1
                                    FROM task_mgmt.view_preferences
                                    WHERE userid = $2
                                        AND view_id = $1
                                )
                            )`,
                            params: [view_id, userid, team_id],
                        });

                        let update_query = `UPDATE task_mgmt.view_preferences SET `;
                        const update_params = [view_id, userid];

                        if (auto_save != null) {
                            update_params.push(auto_save);
                            update_query += ` auto_save = $${update_params.length}`;
                        }

                        if (view.extra_user) {
                            if (update_params.length > 2) {
                                update_query += `,`;
                            }

                            update_params.push(view.extra_user);
                            update_query += ` extra = $${update_params.length}`;
                        }

                        update_query += ` WHERE view_id = $1 AND userid = $2`;

                        queries.push({
                            query: update_query,
                            params: update_params,
                        });
                    }

                    if (filters.fields) {
                        // edit filters
                        queries.push({
                            query: 'DELETE FROM task_mgmt.view_filters WHERE view_id = $1',
                            params: [view_id],
                        });

                        queries.push({
                            query: 'DELETE FROM task_mgmt.view_filter_groups WHERE view_id = $1',
                            params: [view_id],
                        });

                        if (filters.filter_groups?.length && filters.filter_group_ops?.length) {
                            filters.filter_groups = JSON.stringify(filters.filter_groups);

                            queries.push({
                                query: `INSERT INTO task_mgmt.view_filter_groups(view_id, groups, ops, workspace_id) VALUES($1, $2, $3, $4) ON CONFLICT (view_id)
                                        DO UPDATE SET groups = EXCLUDED.groups, ops = EXCLUDED.ops;`,
                                params: [view_id, filters.filter_groups, filters.filter_group_ops, options.team_id],
                            });
                        }

                        filters.fields.forEach((filter, idx) => {
                            let values;
                            if (filter.values && filter.values.length === 0) {
                                values = '{}';
                            } else if (filter.values) {
                                values = filter.values.map(value => JSON.stringify(value));
                            }

                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.view_filters(view_id, field, idx, op, determinor, values, workspace_id)
                                    VALUES ($1, $2, $3, $4, $5, $6, $7)  ON CONFLICT (view_id, field, idx)
                                    DO UPDATE SET op = EXCLUDED.op, determinor = EXCLUDED.determinor, values = EXCLUDED.values`,
                                params: [
                                    view_id,
                                    filter.field,
                                    idx,
                                    filter.op,
                                    filter.determinor,
                                    values,
                                    options.team_id,
                                ],
                            });
                        });
                    }

                    const { activity_settings } = options;
                    if (
                        Number(view.type) === Number(config.views.view_types.activity) &&
                        activity_settings &&
                        activity_settings.filters &&
                        activity_settings.filters.length
                    ) {
                        if (!filters.fields) {
                            queries.push({
                                query: 'DELETE FROM task_mgmt.view_filters WHERE view_id = $1',
                                params: [view_id],
                            });
                        }

                        activity_settings.filters.forEach((filter, idx) => {
                            if (filter.values && filter.values.length === 0) {
                                filter.values = '{}';
                            } else if (filter.values) {
                                filter.values = filter.values.map(value => JSON.stringify(value));
                            }

                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.view_filters(view_id, field, idx, values, workspace_id)
                                    VALUES ($1, $2, $3, $4, $5)`,
                                params: [view_id, filter.field, idx, filter.values, options.team_id],
                            });
                        });
                    }

                    if (
                        view.type === config.views.view_types.doc &&
                        parent_type !== parent_types.template &&
                        (view?.doc_settings?.full_modal != null || view?.doc_settings?.sidebar_open != null)
                    ) {
                        queries.push(
                            updateDocSettingsQuery(userid, view_id, {
                                full_modal: view.doc_settings.full_modal,
                                sidebar_open: view.doc_settings.sidebar_open,
                                workspace_id: view.team_id,
                            })
                        );
                    }

                    if (view.parent_type === parent_types.task) {
                        queries.push({
                            query: `DELETE FROM task_mgmt.view_members WHERE view_id = $1`,
                            params: [view.id],
                        });
                        hasAccessChanges = true;
                    }

                    if (view.form_fields) {
                        queries.push({
                            query: `DELETE FROM task_mgmt.form_fields WHERE form_id = $1`,
                            params: [view_id],
                        });

                        view.form_fields.forEach((field, idx) => {
                            if (field.options) {
                                field.options = _.pick(field.options, ['due_date_time', 'start_date_time', 'time']);
                            }
                            const dependencies = JSON.stringify(field.dependencies ?? []);
                            const rules = JSON.stringify(field.rules ?? []);

                            if (dependencies.length > JSON_COL_MAX_LENGTH || rules.length > JSON_COL_MAX_LENGTH) {
                                throw new ViewsError('Payload size is too large.', 'VIEWS_113', 400);
                            }

                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.form_fields(
                                        form_id,
                                        field,
                                        idx,
                                        required,
                                        display,
                                        options,
                                        placeholder,
                                        hidden,
                                        append_description,
                                        content,
                                        conditions,
                                        dependencies,
                                        rules,
                                        rules_enabled,
                                        workspace_id
                                    )
                                    VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)`,
                                params: [
                                    view_id,
                                    field.field,
                                    idx,
                                    field.required || false,
                                    field.display_name,
                                    field.options,
                                    field.placeholder,
                                    field.hidden || false,
                                    field.append_description || false,
                                    field.content,
                                    JSON.stringify(field.conditions ?? []),
                                    dependencies,
                                    rules,
                                    field.rules_enabled ?? false,
                                    team_id,
                                ],
                            });
                        });
                    }

                    if (
                        view.visibility &&
                        (view.visibility === config.views.visibility.private ||
                            view.visibility === config.views.visibility.personal)
                    ) {
                        queries.push({
                            query: `
                                INSERT INTO task_mgmt.view_members(view_id, userid, permission_level, workspace_id) (
                                    SELECT $1, $2, 5, $3 WHERE NOT EXISTS (
                                        SELECT 1
                                        FROM task_mgmt.view_members
                                        WHERE view_id = $1
                                        AND userid = $2
                                    )
                                )`,
                            params: [view_id, userid, team_id],
                        });

                        if (options.keep_creator) {
                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.view_members(view_id, userid, permission_level, workspace_id)
                                    SELECT $1, creator, 5, $2
                                    FROM task_mgmt.views
                                    WHERE view_id = $1
                                    ON CONFLICT DO NOTHING
                                `,
                                params: [view_id, team_id],
                            });
                        }
                        hasAccessChanges = true;
                    }

                    if (parent_type === parent_types.template) {
                        const template_options = {};
                        const settings_counts = {};
                        if (Number(view.type) === view_types.doc) {
                            let page_names = [];
                            settings_counts.pages = 0;

                            if (pages) {
                                page_names = pages.map(page => page.name);
                                settings_counts.pages = pages.length;
                            }
                            template_options.pages = page_names;
                        } else {
                            if (filters.fields) {
                                template_options.view_filters = filters.fields.map((field, index) => ({
                                    field: field.field,
                                    idx: index,
                                    op: field.op,
                                    values: (field.values || []).map(value => value),
                                    determinor: field.determinor,
                                }));
                                settings_counts.view_filters = filters.fields.length || 0;
                            } else {
                                template_options.view_filters = [];
                                settings_counts.view_filters = 0;
                            }

                            if (columns && columns.fields) {
                                template_options.view_cols = columns.fields
                                    .map((col, index) => {
                                        if (!col.hidden) {
                                            return {
                                                field: col.field,
                                                idx: index,
                                                width: col.width,
                                                hidden: col.hidden,
                                                name: col.name,
                                                display: col.display,
                                            };
                                        }
                                        return null;
                                    })
                                    .filter(col => col);
                                settings_counts.view_cols = template_options.view_cols.length || 0;
                            } else {
                                template_options.view_cols = [];
                                settings_counts.view_cols = 0;
                            }
                        }

                        queries.push({
                            query: `
                                INSERT INTO task_mgmt.template_options
                                    ( template_id, type, options, description, settings_counts, workspace_id )
                                VALUES ( $1, $2, $3, $4, $5, $6)
                                ON CONFLICT ( template_id, type )
                                DO UPDATE SET options = $3, description = $4, settings_counts = $5 `,
                            params: [
                                view_id,
                                'view',
                                { count_details: template_options },
                                options.description,
                                settings_counts,
                                options.team_id,
                            ],
                        });
                    }

                    if (
                        options.template_members &&
                        Array.isArray(options.template_members) &&
                        options.template_members.length
                    ) {
                        queries.push({
                            query: `
                                DELETE FROM task_mgmt.view_template_members
                                WHERE view_id = $1`,
                            params: [view_id],
                        });

                        options.template_members.forEach(member => {
                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.view_template_members(view_id, userid, workspace_id)
                                    SELECT $1, $2, $3
                                    WHERE EXISTS (
                                        SELECT 1 FROM task_mgmt.team_members WHERE team_id = $3 AND userid = $2
                                    )`,
                                params: [view_id, member, team_id],
                            });
                        });
                    }

                    if (
                        options.template_group_members &&
                        Array.isArray(options.template_group_members) &&
                        options.template_group_members.length
                    ) {
                        queries.push({
                            query: `
                                DELETE FROM task_mgmt.view_template_group_members
                                WHERE view_id = $1`,
                            params: [view_id],
                        });

                        options.template_group_members.forEach(member => {
                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.view_template_group_members(view_id, group_id, workspace_id)
                                    SELECT $1, $2, $3
                                    WHERE EXISTS (
                                        SELECT 1 FROM task_mgmt.groups WHERE team_id = $3 AND id = $2
                                    )`,
                                params: [view_id, member, team_id],
                            });
                        });
                    } else if (
                        options.template_group_members &&
                        Array.isArray(options.template_group_members) &&
                        !options.template_group_members.length
                    ) {
                        queries.push({
                            query: `
                                DELETE FROM task_mgmt.view_template_group_members
                                WHERE view_id = $1`,
                            params: [view_id],
                        });
                    }

                    if (view.default) {
                        queries.push({
                            query: `
                            UPDATE task_mgmt.views
                            SET "default" = FALSE
                            WHERE parent_id = $1
                                AND parent_type = $2
                                AND view_id != $3`,
                            params: [parent.id, parent.type, view_id],
                        });
                    }

                    if (options.tags && options.tags.length) {
                        let values = `VALUES`;
                        const tag_params = [view_id, 'view', team_id];
                        options.tags.forEach((tag, idx) => {
                            if (idx > 0) {
                                values += `,`;
                            }
                            tag_params.push(tag.id);
                            values += `($1, $2, $${tag_params.length}, false, $3)`;
                        });

                        queries.push({
                            query: `
                        INSERT INTO task_mgmt.template_tags (template_id, template_type, tag_id, deleted, workspace_id) ${values}
                            ON CONFLICT (template_id, template_type, tag_id) DO UPDATE SET deleted = FALSE, deleted_by = NULL, date_deleted = NULL`,
                            params: tag_params,
                        });
                    }

                    if (hasAccessChanges) {
                        versionUpdates.push({
                            object_type: ObjectType.VIEW_ACCESS,
                            object_id: view_id,
                            workspace_id: team_id,
                            operation: OperationType.UPDATE,
                        });

                        if (view.type === view_types.clickboard) {
                            versionUpdates.push({
                                object_type: ObjectType.WHITEBOARD_ACCESS,
                                object_id: view_id,
                                workspace_id: team_id,
                                operation: OperationType.UPDATE,
                            });
                        }
                    }

                    each_cb();
                }
            );
        },
        err => {
            if (err) {
                cb(err);
            } else {
                cb(null, { queries, fields_added, versionUpdates, hasAccessChanges });
            }
        }
    );
}
export { _getEditQueries };

export function _getEditQueriesAsync(userid, view_id, options = {}) {
    return new Promise((res, rej) => {
        _getEditQueries(userid, view_id, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}

/**
 *
 * @param {number} userid
 * @param {Object} options
 * @param {boolean} [options.locked] - Lock View / Protect Doc
 * @param {boolean} [options.template_public] - Template is publicly shared
 * @param {string} [options.template_id] - Provided when applying Template, for usage tracking only
 * @param {number} [options.type] - View type
 * @param {object} [options.filters]
 * @param {*} cb
 */
const _editView = tracer.wrap('editView', {}, __editView);
function __editView(userid, view_id, options, cb) {
    let isStandard;
    let isLocked;
    let isPersonal;
    let isPublic;
    let isDefault;
    let locked_permission;
    let creator;
    let disable_public_sharing;
    let admin_public_share_override;
    let team_role;
    let disable_never_expire_pub_links;
    let team_id;
    let type;
    let wip_limits;
    let pub_links_max_year;
    let parent_type;
    let fields_added;
    let queries;
    let visibility;
    let template_visibility;
    let extra;

    /**
     * Workspace and team ID are both being set, but the eventual goal is to migrate all uses/queries for the team id
     * to workspace ID since it will always set at the start of the series after entitlements are enabled.
     */
    let workspaceId;
    let entitlements = {};
    const isChatView = isConversationView(options.type, config.views.view_types);

    async.series(
        [
            async function getEntitlements(series_cb) {
                try {
                    const team = await getViewTeamInfoAsync(view_id, {});
                    workspaceId = team.team_id;
                    entitlements = await viewCrudPaywallService.getEntitlementsEnabled(workspaceId);
                    series_cb();
                } catch (error) {
                    series_cb(error);
                }
            },
            function getExistingProperties(series_cb) {
                /** no need for existing props if saving template */
                if (options.is_template) {
                    series_cb();
                    return;
                }
                const query = `
                    SELECT
                        standard AS "isStandard",
                        locked AS "isLocked",
                        locked_permission,
                        public AS "isPublic",
                        "default" AS "isDefault",
                        task_mgmt.views.creator,
                        task_mgmt.views.name,
                        visibility,
                        template_visibility,
                        parent_type,
                        parent_id,
                        extra,
                        type
                    FROM task_mgmt.views
                    INNER JOIN task_mgmt.teams ON id = task_mgmt.views.team_id
                    WHERE view_id = $1`;

                db.readQuery(query, [view_id], (err, result) => {
                    if (err) {
                        series_cb(new ViewsError(err, 'VIEWS_022'));
                        return;
                    }

                    const { name } = result.rows[0] || {};
                    ({
                        isStandard,
                        isLocked,
                        isPublic,
                        locked_permission,
                        creator,
                        parent_type,
                        extra,
                        type,
                        isDefault,
                        visibility,
                        template_visibility,
                    } = result.rows[0] || {});

                    if (options.locked == null && !entitlements.protect_views) {
                        options.locked = false;
                    }

                    if (options.locked == null) {
                        // use the stored locked value if none is passed
                        options.locked = isLocked;
                    }

                    if (
                        options.visibility !== config.views.visibility.public &&
                        isVisibilityChanged(visibility, options.visibility) &&
                        (type === view_types.doc
                            ? !entitlements[EntitlementName.PrivateDocs]
                            : type === view_types.clickboard
                            ? !entitlements[EntitlementName.PrivateWhiteboards]
                            : !entitlements[EntitlementName.PrivateViews]) &&
                        !isChatView
                    ) {
                        series_cb(
                            new ViewsError('Please upgrade your plan to switch view visibility', 'VIEWS_107', 403)
                        );
                        return;
                    }

                    if (options.visibility === undefined) {
                        // use stored visibility value if none is passed
                        options.visibility = visibility;
                    }

                    // if the view is locked we should not be able to change its name
                    // (it's the only piece of "content" to validate at this time)
                    if (isLocked && options.locked && options.name && options.name !== name) {
                        series_cb(new ViewsError('cannot change the name of a protected document', 'VIEWS_025', 403));
                        return;
                    }

                    isPersonal = [config.views.visibility.personal, config.views.visibility.private].includes(
                        visibility
                    );

                    options.sidebar_view = Boolean(options.sidebar_view && isSidebarEnabledView(result.rows[0].type));

                    if (!options.parent) {
                        options.parent = {
                            id: result.rows[0].parent_id,
                            type: Number(result.rows[0].parent_type),
                        };
                    }

                    if (options.locked === true && !isLocked) {
                        options.locked_permission = config.views.locked_permission.can_unlock;
                    } else if (options.locked === false && isLocked) {
                        options.locked_permission = null;
                    }

                    const dbView = result.rows[0];
                    const dbEmbedSettings =
                        dbView && dbView.type === config.views.view_types.embed && dbView.extra
                            ? dbView.extra
                            : undefined;

                    options.embed_settings = options.embed_settings || dbEmbedSettings;

                    series_cb();
                });
            },

            function checkAccessAndValidate(series_cb) {
                if (options.parent_override) {
                    team_id = workspaceId;
                    series_cb();
                    return;
                }

                /**
                 * DrilldownView is a special case where it is tightly coupled with a specific Widget so we'd want to
                 * check Widget permissions instead of View permissions.
                 */
                if (isDrilldownView({ parent_type, extra })) {
                    team_id = workspaceId;
                    series_cb();
                    return;
                }

                const isMyTasksWidget = isMyTasksViewId(view_id, userid, options.team_id);
                const permissionsToCheck = isMyTasksWidget
                    ? []
                    : getEditViewPermissionsForOptions(options, config.permission_constants);

                const access_options = {
                    permissions: [...permissionsToCheck],
                    viewPermissions: [...permissionsToCheck],
                };

                if (isLocked !== options.locked || locked_permission !== options.locked_permission) {
                    access_options.viewPermissions = [config.permission_constants.edit_view];
                    access_options.hierarchyFallback = true;
                }

                if (
                    (options.public || options.seo_optimized) &&
                    options.type !== view_types.form &&
                    options.seo_optimized
                ) {
                    // only doc and list views can be public
                    access_options.permissions.push(config.permission_constants.make_views_public);
                    access_options.types = config.views.public_views;
                }

                if (options.apply_template && options.type === config.views.view_types.doc) {
                    access_options.apply_template = options.apply_template;
                }

                access_options.incoming_visibility_value = options.visibility;

                if (isViewRelatedToDashboard(options)) {
                    access_options.allow_locked_daav_modifications = options.allow_locked_daav_modifications;
                }

                access.checkAccessView(userid, view_id, access_options, (err, parent, parentPermissions) => {
                    if (err) {
                        series_cb(err);
                        return;
                    }

                    options[TemplateTracer.KEY]?.processSourceAndTargetVerifiedData({ savedId: view_id });

                    team_id = parentPermissions.team_id;
                    ({ team_role } = parentPermissions.permissions);

                    if (creator === userid && isPersonal) {
                        series_cb();
                        return;
                    }

                    if (
                        Number(options.locked_permission) === Number(config.views.locked_permission.admins_only) &&
                        Number(team_role) > Number(config.team_roles.admin)
                    ) {
                        delete options.locked_permission;
                    }

                    // parent needed for validation and cannot be overwritten
                    options.parent = parent;

                    series_cb();
                });
            },

            async function checkIfGuestRemovingDocCreator(seriesCb) {
                if (
                    type === view_types.doc &&
                    Number(team_role) === config.team_roles.guest &&
                    userid !== creator &&
                    options.keep_creator !== true &&
                    options.visibility === config.views.visibility.private &&
                    !options.template_id
                ) {
                    seriesCb(new ViewsError('You are not allowed to remove view creators', 'VIEWS_089', 401));
                } else {
                    seriesCb();
                }
            },

            async function checkIfUserRemovingDocCreator(seriesCb) {
                if (
                    type !== view_types.doc ||
                    !checkOwnerRemovalAccessForGuestsOnDocs() ||
                    Number(team_role) <= Number(config.team_roles.owner) ||
                    userid === creator ||
                    options.keep_creator
                ) {
                    seriesCb();
                    return;
                }

                try {
                    if (await isUserBeingRemovedAsViewMember(creator, view_id, options.members)) {
                        seriesCb(new ViewsError('You are not allowed to remove view creators', 'VIEWS_066', 401));
                    } else {
                        seriesCb();
                    }
                } catch (err) {
                    seriesCb(err);
                }
            },

            async function checkDrilldownViewEditAccess(series_cb) {
                if (!isDrilldownView({ parent_type, extra })) {
                    series_cb();
                    return;
                }

                if (cardsEverywhere(workspaceId)) {
                    try {
                        const widgetParentInfo = await getWidgetParentInfo(extra.widget_id);

                        if (
                            isParentedCard({
                                workspace_id: workspaceId,
                                parent_id: widgetParentInfo.parentId,
                                dashboard_id: widgetParentInfo.dashboardId,
                                parent_type: widgetParentInfo.parentType,
                            })
                        ) {
                            const widget = {
                                widget: {
                                    parent_type: widgetParentInfo.parentType,
                                    parent_id: widgetParentInfo.parentId,
                                },
                            };

                            await checkAccessToParentedCard({
                                userId: userid,
                                workspaceId,
                                parent: mapParent(widget),
                            });

                            series_cb(null);

                            return;
                        }
                    } catch (error) {
                        series_cb(error, null);

                        return;
                    }
                }

                access
                    .promiseCheckAccessWidget(userid, extra.widget_id, {
                        permissions: [create_dashboards, edit_widget],
                        return_dashboard: false,
                        exclude_deleted: true,
                        allow_locked_daav_modifications: true,
                    })
                    .then(result => series_cb(null, result))
                    .catch(error => series_cb(error, null));
            },

            async function checkCustomFieldAccess(series_cb) {
                const { viewColumnValidationSkip } = fieldLevelPermissionsConfig(workspaceId);

                const columns = options?.columns?.fields || [];
                const sorting = options?.sorting?.fields || [];
                const formFields = options?.form_settings?.fields || [];
                const conditionalFormFields = options?.form_settings?.conditional_fields || [];

                try {
                    const validated_columns = viewColumnValidationSkip?.columns
                        ? {}
                        : {
                              fields: (
                                  await getViewColumnGuard().revertChangesWithoutAccess({
                                      userId: userid,
                                      workspaceId,
                                      updatedAttributes: columns,
                                      viewIds: [view_id],
                                  })
                              ).attributes,
                          };

                    const validated_sorting = viewColumnValidationSkip?.sorting
                        ? {}
                        : {
                              fields: (
                                  await getViewSortingGuard().revertChangesWithoutAccess({
                                      userId: userid,
                                      workspaceId,
                                      updatedAttributes: sorting,
                                      viewIds: [view_id],
                                  })
                              ).attributes,
                          };

                    const validated_form_settings = viewColumnValidationSkip?.form_settings
                        ? {}
                        : {
                              fields: (
                                  await getFormFieldsGuard().revertChangesWithoutAccess({
                                      userId: userid,
                                      workspaceId,
                                      updatedAttributes: formFields,
                                      viewIds: [view_id],
                                  })
                              ).attributes,
                              conditional_fields: (
                                  await getConditionalFormFieldsGuard().revertChangesWithoutAccess({
                                      userId: userid,
                                      workspaceId,
                                      updatedAttributes: conditionalFormFields,
                                      viewIds: [view_id],
                                  })
                              ).attributes,
                          };

                    Object.assign(options, {
                        columns: {
                            ...options.columns,
                            ...validated_columns,
                        },
                        sorting: {
                            ...options.sorting,
                            ...validated_sorting,
                        },
                        form_settings: {
                            ...options.form_settings,
                            ...validated_form_settings,
                        },
                    });
                } catch (err) {
                    logger.warn({ err, msg: 'Error thrown when checking custom field access.' });
                    series_cb(new ViewsError('Access denied for the requested action', 'VIEWS_064', 400));
                    return;
                }

                series_cb();
            },

            async function checkLocationlessChannelName(series_cb) {
                if (!options.name) {
                    series_cb();
                    return;
                }
                try {
                    // Locationless chat channel names must be unique
                    await validateLocationlessChannelNameAvailable({
                        viewId: view_id,
                        workspaceId,
                        newName: options.name,
                        viewType: type,
                        viewParentType: parent_type,
                    });

                    series_cb();
                } catch (e) {
                    series_cb(e);
                }
            },

            function dataFetch(series_cb) {
                try {
                    viewSchema.validateView(options, true);
                } catch (e) {
                    series_cb(new ViewsError(e, 'VIEWS_018'));
                    return;
                }

                if (options.type === view_types.form && options.parent.type === parent_types.team) {
                    series_cb(new ViewsError('Forms cannot be created on the workspace level', 'VIEWS_063', 400));
                    return;
                }

                async.parallel(
                    {
                        useGantt(para_cb) {
                            if (!options.use_gantt || options.type !== view_types.gantt) {
                                para_cb();
                                return;
                            }

                            helpers.incrementGanttUsage({ view_id, team_id }, para_cb);
                        },

                        async teamView(para_cb) {
                            if (!options.from_request || options.type !== view_types.box) {
                                para_cb();
                                return;
                            }

                            try {
                                await helpers.incrementTeamViewUsage({ view_id });
                                para_cb();
                            } catch (error) {
                                para_cb(error);
                            }
                        },

                        async incrementDefaultViewUsage(para_cb) {
                            // do not enforce entitlement if view is not default or already was default
                            if (!options.default || isDefault) {
                                para_cb();
                                return;
                            }
                            try {
                                await defaultViewPaywallService.checkPaywall({ teamId: team_id });
                                await defaultViewPaywallService.incrementLimit({ teamId: team_id });
                                para_cb();
                            } catch (error) {
                                para_cb(error);
                            }
                        },

                        ganttPaywall(para_cb) {
                            if (options.type !== view_types.gantt) {
                                para_cb();
                                return;
                            }

                            helpers.checkGanttPaywall({ view_id }, para_cb);
                        },

                        async teamViewPaywall(para_cb) {
                            if (!options.from_request || options.type !== view_types.box) {
                                para_cb();
                                return;
                            }

                            try {
                                await helpers.checkTeamViewPaywall({ view_id });
                                para_cb();
                            } catch (error) {
                                para_cb(error);
                            }
                        },

                        async incrementProtectedViewsLimit(para_cb) {
                            // Enforce protected views limit when making a view protected.
                            // Docs are not subject to the protected views limit.
                            if (!options.from_request || !options.locked || isLocked || type === view_types.doc) {
                                para_cb();
                                return;
                            }

                            try {
                                await helpers.checkProtectedViewsLimit({ view_id });
                                await helpers.incrementProtectedViewsLimit({ view_id });
                                para_cb();
                            } catch (error) {
                                para_cb(error);
                            }
                        },

                        copyPagesAccess(para_cb) {
                            if (!options.copy_pages_from_template) {
                                para_cb();
                                return;
                            }

                            access.checkAccessView(
                                userid,
                                options.copy_pages_from_template,
                                { permissions: [] },
                                para_cb
                            );
                        },

                        getTeam(para_cb) {
                            helpers.getViewParentTeamId(
                                options.parent.id,
                                options.parent.type,
                                { team_id: options.team_id },
                                (err, result) => {
                                    if (err) {
                                        para_cb(new ViewsError(err, 'VIEWS_027'));
                                        return;
                                    }

                                    const { team_wip_limits, disable_template_pub_sharing } = result;
                                    ({
                                        disable_public_sharing,
                                        admin_public_share_override,
                                        disable_never_expire_pub_links,
                                        pub_links_max_year,
                                        team_id,
                                        wip_limits,
                                    } = result);

                                    const paywallFeaturesEnabled = entitlements;

                                    const available_wip_limits = [
                                        config.views.parent_types.team,
                                        config.views.parent_types.project,
                                        config.views.parent_types.category,
                                        config.views.parent_types.subcategory,
                                    ];

                                    // don't allow showRecurringTasks if less than Unlimited plan
                                    if (
                                        shouldSetShowRecurringTasksToFalseForFreePlan() &&
                                        options.type === view_types.calendar &&
                                        !paywallFeaturesEnabled.show_recurring_tasks &&
                                        options.calendar_settings?.showRecurringTasks
                                    ) {
                                        options.calendar_settings.showRecurringTasks = false;
                                    }

                                    if (available_wip_limits.includes(options.parent.type) && team_wip_limits) {
                                        wip_limits = team_wip_limits;
                                    }

                                    if (team_id) {
                                        options[TemplateTracer.KEY]?.processSourceAndTargetVerifiedData({
                                            workspaceId: team_id,
                                        });
                                    }

                                    if (
                                        options?.parent?.type === parent_types.template &&
                                        options.public &&
                                        disable_template_pub_sharing &&
                                        paywallFeaturesEnabled.public_link_restrictions
                                    ) {
                                        para_cb(
                                            new ViewsError(
                                                'Do not have permission to perform that action',
                                                'VIEWS_943',
                                                401
                                            )
                                        );
                                        return;
                                    }

                                    if (
                                        options.public &&
                                        !isPublic && // only do these checks if updating from not public to public
                                        disable_public_sharing === true &&
                                        options.type !== view_types.form &&
                                        !options.parent_override &&
                                        (!admin_public_share_override ||
                                            (Number(team_role) !== config.get('team_roles.owner') &&
                                                Number(team_role) !== config.get('team_roles.admin')))
                                    ) {
                                        para_cb(
                                            new ViewsError(
                                                'Do not have permission to perform that action',
                                                PublicSharingCodes.DisabledPublicSharingError,
                                                StatusErrorCodes.Unauthorized
                                            )
                                        );
                                        return;
                                    }

                                    // Doc protection have unique available_plans
                                    if (
                                        options.locked &&
                                        options.type === view_types.doc &&
                                        !isLocked &&
                                        !paywallFeaturesEnabled.protect_docs
                                    ) {
                                        const msg = 'Protected Doc available on Business+ and Enterprise plans only';
                                        para_cb(new ViewsError(msg, 'VIEWS_103', 403));
                                        return;
                                    }

                                    if (
                                        options.public &&
                                        !paywallFeaturesEnabled.public_views &&
                                        Number(parent_type) === parent_types.team
                                    ) {
                                        options.public = false;
                                    }

                                    if (
                                        options.type === view_types.form &&
                                        !paywallFeaturesEnabled.advanced_form_view
                                    ) {
                                        options.form_settings = options.form_settings || {};
                                        delete options.form_settings.display.avatar;
                                        delete options.form_settings.display.style;
                                        delete options.form_settings.display.color;
                                        delete options.form_settings.display.background;
                                        delete options.form_settings.display.show_cover_header;
                                        delete options.form_settings.display.cover_image_url;
                                        delete options.form_settings.display.cover_image_color;
                                        delete options.form_settings.display.cover_position_x;
                                        delete options.form_settings.display.cover_position_y;
                                        delete options.form_settings.display.add_captcha;
                                        delete options.form_settings.template_id;
                                        if (
                                            options.form_settings.submission &&
                                            options.form_settings.submission.redirect_uri
                                        ) {
                                            delete options.form_settings.submission.redirect_uri;
                                        }

                                        if (!options.form_settings.submission) {
                                            options.form_settings.submission = {};
                                        }

                                        options.form_settings.submission.response_content = JSON.stringify({
                                            ops: [
                                                { insert: 'Thank You!' },
                                                { attributes: { align: 'center', header: 2 }, insert: '\n' },
                                                { attributes: { align: 'center' }, insert: '\n' },
                                                { insert: 'Your submission has been received.' },
                                                { attributes: { align: 'center' }, insert: '\n' },
                                            ],
                                        });
                                    }

                                    const privacyEntitlement =
                                        type === view_types.doc
                                            ? paywallFeaturesEnabled.private_docs
                                            : paywallFeaturesEnabled.private_views;

                                    if (options.visibility == null && !privacyEntitlement) {
                                        options.visibility = config.views.visibility.public;
                                    }

                                    para_cb();
                                }
                            );
                        },

                        validateScrumFieldID(para_cb) {
                            const { scrum_field_id } = options.box_settings || {};

                            if (!scrum_field_id) {
                                para_cb();
                                return;
                            }
                            const opts = {
                                skipAtParent: true,
                            };

                            if (options.parent.type === parent_types.subcategory) {
                                opts.subcategory_id = options.parent.id;
                            } else if (options.parent.type === parent_types.category) {
                                opts.category_id = options.parent.id;
                            } else if (options.parent.type === parent_types.project) {
                                opts.project_id = options.parent.id;
                            } else {
                                opts.team_id = options.parent.id;
                            }

                            fieldLocation._addFieldToParent(userid, scrum_field_id, opts, (err, result) => {
                                if (err && err.status === 404) {
                                    options.box_settings.scrum_field_id = null;

                                    if (
                                        options.box_settings.workload &&
                                        options.box_settings.workload.field === 'scrum'
                                    ) {
                                        options.box_settings.workload.field = 'completed';
                                    }

                                    if (options.box_settings.sort && options.box_settings.sort.field === 'scrum') {
                                        options.box_settings.sort.field = 'name';
                                    }

                                    if (
                                        options.box_settings.unassigned_sort &&
                                        options.box_settings.unassigned_sort.field === 'scrum'
                                    ) {
                                        options.box_settings.unassigned_sort.field = 'name';
                                    }
                                    para_cb();
                                    return;
                                }

                                if (err) {
                                    para_cb(err);
                                    return;
                                }

                                if (![field_types.drop_down, field_types.number].includes(Number(result.type))) {
                                    const msg = 'Scrum field must be drop down or number';
                                    para_cb(new ViewsError(msg, 'VIEWS_039', 400));
                                    return;
                                }

                                para_cb();
                            });
                        },

                        templateMembers(para_cb) {
                            if (
                                (options.template_visibility ?? template_visibility) !==
                                config.views.template_visibility.users
                            ) {
                                para_cb();
                                return;
                            }

                            const template_members = (options.template_members || []).map(
                                member => member.id || member
                            );

                            if (!template_members.includes(userid)) {
                                template_members.push(userid);
                            }

                            async.each(
                                template_members,
                                (member, _each_cb) => {
                                    access.checkAccessTeam(member, team_id, { permissions: [] }, _each_cb);
                                },
                                err => {
                                    if (err) {
                                        para_cb(err);
                                    } else {
                                        if (options.template_members) {
                                            options.template_members = template_members;
                                        }
                                        para_cb();
                                    }
                                }
                            );
                        },
                    },
                    series_cb
                );
            },

            function editRecords(series_cb) {
                options.isStandard = isStandard;
                options.disable_public_sharing = disable_public_sharing;
                options.admin_public_share_override = admin_public_share_override;
                options.disable_never_expire_pub_links = disable_never_expire_pub_links;
                options.wip_limits = wip_limits;
                options.pub_links_max_year = pub_links_max_year;
                options.team_role = team_role;

                if (options.default && options.visibility !== config.views.visibility.public) {
                    series_cb(new ViewsError('Cannot make a private view default', 'VIEWS_020', 400));
                    return;
                }

                _getEditQueries(userid, view_id, options, async (err, result) => {
                    if (err) {
                        series_cb(err);
                        return;
                    }

                    try {
                        ({ fields_added = false } = result);

                        if (options.return_queries) {
                            queries = result.queries;
                            series_cb();
                            return;
                        }

                        const versionUpdates = [...result.versionUpdates];

                        if (type === view_types.doc && team_id) {
                            const { rows } = await queryPageIdsForView(view_id);
                            for (const row of rows) {
                                versionUpdates.push({
                                    object_type: ObjectType.PAGE,
                                    object_id: row.page_id,
                                    workspace_id: Number(team_id),
                                    operation: OperationType.UPDATE,
                                });
                                if (result.hasAccessChanges) {
                                    versionUpdates.push({
                                        object_type: ObjectType.PAGE_ACCESS,
                                        object_id: row.page_id,
                                        workspace_id: Number(team_id),
                                        operation: OperationType.UPDATE,
                                    });
                                    versionUpdates.push({
                                        object_type: ObjectType.DOC_ACCESS,
                                        object_id: row.page_id,
                                        workspace_id: Number(team_id),
                                        operation: OperationType.UPDATE,
                                    });
                                }
                            }

                            // after template creation of a doc, if the first page has an empty name and empty text content, then delete it
                            if (options.template_id) {
                                if (options.page_to_delete) {
                                    const res = await queryEmptySubPageParentIdForView(view_id, options.page_to_delete);
                                    if (res.rows) {
                                        const { query, params } = prepareSoftDeleteOfPages(userid, [
                                            options.page_to_delete,
                                        ]);
                                        result.queries.push({ query, params });
                                    }
                                } else {
                                    const firstPageAfterTemplateCreation = await queryPageForViewSortByDateCreatedAsc(
                                        view_id
                                    );
                                    if (firstPageAfterTemplateCreation.rows) {
                                        const pageId = firstPageAfterTemplateCreation.rows[0]?.page_id;
                                        if (pageId !== options.template_parent) {
                                            const { query, params } = prepareSoftDeleteOfPages(userid, [pageId]);
                                            result.queries.push({ query, params });
                                        }
                                    }
                                }
                            }
                        }

                        for (const [index, queryResult] of result.queries.entries()) {
                            const { query, params } = queryResult;
                            if (index === 0) await writeAsync(query, params, versionUpdates);
                            else await db.writeQueryAsync(query, params);
                        }
                        series_cb();
                    } catch (editErr) {
                        series_cb(new ViewsError(editErr, 'VIEWS_020'));
                    }
                });
            },

            async function whiteboardCalls(series_cb) {
                if (options.type !== view_types.clickboard) {
                    series_cb();
                    return;
                }

                try {
                    await replaceWhiteboardTemplate(options.update_template_id, options.type, {
                        userId: userid,
                        whiteboardId: view_id,
                        workspaceId: team_id,
                    });

                    series_cb();
                } catch (whiteboardErr) {
                    series_cb(whiteboardErr);
                }
            },
        ],
        err => {
            if (err) {
                cb(new ViewsError(err, 'VIEWS_017'));
                return;
            }

            if (options.return_queries) {
                cb(null, queries);
                return;
            }

            putUserFilterSettings(userid, options.filters);

            _getViews(
                userid,
                { view_id, include_parent_info: true, include_archived: true, use_replica: false },
                (getErr, getResult) => {
                    if (getErr) {
                        cb(getErr);
                    } else {
                        const view = getResult.views[0];
                        cb(null, { view, fields_added });
                        if (view) {
                            if (!options.became_public) {
                                sqs.sendWSMessage('sendViewEdited', [
                                    view_id,
                                    view.parent.id,
                                    view.parent.type,
                                    userid,
                                    options.ws_key,
                                ]);
                            }
                            // sending to ES only if the view is a doc with at least one page
                            const page_id = view.pages && view.pages[0] && view.pages[0].id;
                            if (view.type === config.get('views.view_types.doc') && page_id) {
                                elasticProducer.addDocsToES(userid, [page_id], {
                                    description_changed: false,
                                });
                            }
                            if (options.template_id) {
                                templateHelpers.storeTemplateUsed(
                                    userid,
                                    options.template_id, // View Templates doesn't have permanent template id
                                    'view',
                                    options.type,
                                    team_id
                                );
                            }

                            if (options.type === view_types.doc && options.locked !== isLocked && view.pages?.length) {
                                codoxTriggerPermissionCheck(null, view.pages);
                            }
                        }
                    }
                }
            );

            const docsSearchCache = DocsAppContext.getDocsSearchCache();

            // invalidate all doc ids caches sorted by date updated, run in background, only log error
            docsSearchCache
                .invalidateDocsIdsCacheSortedByDateUpdated(Number(team_id))
                .catch(e => logger.error({ msg: 'Error invalidating docs search cache', err: e }));

            // invalidate all doc ids caches for sharing/private sections if visibility changed
            if (visibility !== options.visibility) {
                // run in background, only log error
                docsSearchCache
                    .invalidateDocsIdsCachesAffectedBySharingChanges(Number(team_id))
                    .catch(e => logger.error({ msg: 'Error invalidating docs search cache', err: e }));
            }
        }
    );
    if (options.type === view_types.clickboard) {
        reportWhiteboardChanged(userid, view_id);
    } else if (options.type === view_types.form) {
        reportFormChanged(userid, view_id);
    } else if (isViewRelatedToDashboard(options.type)) {
        reportDashboardViewChanged(userid, view_id);
    } else if (shouldIndexView(options.type)) {
        reportViewChanged(view_id);
    }
}

export { _editView };

async function _mergeTemplateIntoView(userid, view_id, options, cb) {
    const templateTracer = new TemplateTracer(EntityType.VIEW, TemplateAction.TEMPLATE_APPLICATION, {
        userId: userid,
        sourceTemplateId: options.template_id,
        templateSource: TemplateSource[TemplateSource.VIEW_TEMPLATE],
    });
    options[TemplateTracer.KEY] = templateTracer;

    await templateTracer.withTracing(async () => {
        if (options.template_id) {
            try {
                await access.promiseCheckAccessView(userid, options.template_id, { permissions: [] });
                templateTracer.processSourceAndTargetVerifiedData({ sourceId: options.template_id });
            } catch (templateAccessErr) {
                // No template access - log a warning and proceed.
                logger.warn({
                    msg: 'Template access denied while merging a template into a view.',
                    err: templateAccessErr,
                });
            }
        }

        _editView(userid, view_id, options, (err, result) => {
            cb(err, result);
            templateTracer.finishTracing(err);
        });
    });
}

export function editViewReq(req, resp, next) {
    const userid = req.decoded_token.user;
    const { view_id } = req.params;
    const options = Object.assign({}, req.body);
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;

    options.ws_key = ws_key;
    options.return_queries = false;
    options.use_gantt = req.query.use_gantt;
    options.from_request = true;

    if (req.query.update_locked === 'false') {
        // if update_locked is set and 'false' don't update locked state
        delete options.locked;
    }

    const cb = (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    };

    if (!options.locked && options.template_id) {
        options.apply_template = true;
    }

    if (options.template_id) {
        _mergeTemplateIntoView(userid, view_id, options, cb);
    } else {
        _editView(userid, view_id, options, cb);
    }
}

export function editViewAsync(userid, view_id, options) {
    return new Promise((res, rej) => {
        _editView(userid, view_id, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}

/**
 * @param {number} userid
 * @param {Object} options
 * @param {boolean} [options.template_public] - Template is publicly shared
 * @param {boolean} [options.user_filter_settings] - Apply User's filter settings
 * @param {boolean} [options.sidebar_view] - Create as a sidebar view
 * @param {boolean} [options.my_tasks_widget] - Create as a My Tasks widget
 * @param {boolean} [options.assigned_to_user_widget] - Create as an Assigned to User view
 * @param {string} [options.category_sprint_backlog] - Category of created sprint backlog view
 * @param {boolean} [options.assigned_to_user_widget_with_changes] - Assigned To User view saved with changes
 * @param {string} [options.user_view_id] - User id for assigned to user widget
 * @param {*} cb
 */
const _createView = tracer.wrap('views._createView', {}, __createView);
function __createView(userid, options, cb) {
    options.type = Number(options.type || view_types.list);

    if (!options.name) {
        options.name = '';
    }

    if (!options.description) {
        options.description = '';
    }

    const view = options;
    // Only needed for calendar view.
    // Hash commit: 3c53eb8
    if (view.filters && view.filters.fields && options.type === view_types.calendar) {
        const fields_to_remove = [];
        view.filters.fields.forEach(field => {
            if (field.field.startsWith('cf_')) {
                if (field.values && field.values.length === 0) {
                    fields_to_remove.push(field.field);
                }
            }
        });

        view.filters.fields = view.filters.fields.filter(field => !fields_to_remove.includes(field.field));
    }

    try {
        viewSchema.validateView(options);
    } catch (err) {
        cb(err);
        return;
    }

    const { id: parent_id, type: parent_type } = options.parent;
    const myTasksWidget = options.my_tasks_widget ?? false;
    const assignedToUserWidget = options.assigned_to_user_widget ?? false;
    const categorySprintBacklog = options.category_sprint_backlog;
    const assignedToUserWidgetWithChanges = options.assigned_to_user_widget_with_changes ?? false;
    const isChatView = isConversationView(options.type, config.views.view_types);

    let {
        visibility,
        standard,
        members,
        locked,
        public: isPublic,
        share_task_fields,
        pages,
        template_members = [],
        default: is_default,
        sidebar_view: isSidebarView,
    } = options;
    let team_role;

    const {
        id: source_view_id,
        parent,
        name,
        description,
        type,
        grouping = {},
        divide = {},
        settings = {},
        me_view,
        sorting,
        columns,
        filters,
        activity_settings,
        frozen_note,
        calendar_settings,
        conversation_settings,
        timeline_settings,
        workload_settings,
        embed_settings,
        table_settings,
        gantt_settings,
        box_settings = {},
        form_settings = {},
        mind_map_settings,
        map_settings = {},
        board_settings,
        locationOverviewSettings = {},
        drilldownViewSettings,
        whiteboardsViewSettings,
        team_sidebar = {},
        auto_save,
        template_public,
        template_visibility,
        locked_permission,
        share_with_team,
        share_tasks,
        pinned,
        avatar = {},
        exclude_multiple_lists,
        exclude_sub_multiple_lists,
        public_share_expires_on,
        expanded_tasks,
        tags,
        template_group_members = [],
        followers = [],
        task_custom_type,
    } = options;

    const avatar_value = avatar.value;
    const avatar_source = avatar.attachment_id;
    const avatar_color = avatar.color;

    template_members = template_members.map(member => (member && member.id ? member.id : member));
    isSidebarView = Boolean(isSidebarView && isSidebarEnabledView(type));

    if (type === view_types.form && parent_type === parent_types.team) {
        cb(new ViewsError('Forms cannot be created on the workspace level', 'VIEWS_062', 400));
        return;
    }

    if (share_task_fields && Array.isArray(share_task_fields)) {
        share_task_fields = share_task_fields.filter(field => config.sharing.public_fields.includes(field));
    }

    if (!config.views.public_views.includes(Number(type))) {
        isPublic = false;
    }

    async.series(
        [
            function validateForm(series_cb) {
                if (options.type !== view_types.form) {
                    series_cb();
                    return;
                }

                const { redirect_uri } = form_settings.submission || {};

                if (redirect_uri) {
                    const link_match = linkHelpers.linkify.match(redirect_uri);

                    if (!link_match || !link_match[0]) {
                        series_cb(new ViewsError('Redirect URI is not a valid URL', 'VIEWS_070', 400));
                        return;
                    }
                }

                async.parallel(
                    {
                        subcategory(para_cb) {
                            const { subcategory_id } = form_settings;

                            if (!subcategory_id) {
                                para_cb();
                                return;
                            }

                            validateFormSubcategory(subcategory_id, parent_id, parent_type, (err, result) => {
                                if (err) {
                                    para_cb(err);
                                    return;
                                }

                                const { exists } = result;

                                if (!exists) {
                                    form_settings.active = false;
                                    form_settings.subcategory_id = null;
                                }

                                para_cb();
                            });
                        },

                        async template(para_cb) {
                            const { template_id } = form_settings;
                            try {
                                await templateHelpers._accessTemplate(userid, template_id);
                                para_cb();
                            } catch (err) {
                                para_cb(err);
                            }
                        },
                    },
                    series_cb
                );
            },

            function addFields(series_cb) {
                if (
                    [
                        parent_types.shared,
                        parent_types.template,
                        parent_types.widget,
                        parent_types.shared_tasks,
                        parent_types.team_unparented,
                        parent_types.task,
                        parent_types.user_group,
                        parent_types.view,
                    ].includes(parent_type) ||
                    isChatView
                ) {
                    series_cb();
                    return;
                }

                const { fields = [] } = options.columns || {};

                const field_ids = fields
                    .filter(field => !field.hidden)
                    .map(({ field }) => field)
                    .filter(field => field.includes('cf_') && !field.includes('rollup:'))
                    .map(field => field.replace('cf_', ''));

                if (options.type === view_types.form) {
                    form_settings.fields.forEach(({ field }) => {
                        if (field.includes('cf_')) {
                            field_ids.push(field.replace('cf_', ''));
                        }
                    });
                }

                if (!field_ids.length) {
                    series_cb();
                    return;
                }

                const opts = { skipAtParent: true };

                if (parent_type === parent_types.team) {
                    opts.team_id = parent_id;
                } else if (parent_type === parent_types.project) {
                    opts.project_id = parent_id;
                } else if (parent_type === parent_types.category) {
                    opts.category_id = parent_id;
                } else {
                    opts.subcategory_id = parent_id;
                }

                async.eachSeries(
                    field_ids,
                    (field_id, _each_cb) => {
                        fieldLocation._addFieldToParent(userid, field_id, opts, err => {
                            if (err) {
                                logViewsError(err, 'VIEWS_056');
                            }

                            _each_cb();
                        });
                    },
                    series_cb
                );
            },

            function removeInvalidFields(series_cb) {
                if (
                    [
                        parent_types.widget,
                        parent_types.shared_tasks,
                        parent_types.team_unparented,
                        parent_types.task,
                        parent_types.user_group,
                        parent_types.view,
                    ].includes(parent_type) ||
                    isChatView
                ) {
                    series_cb();
                    return;
                }

                helpers.removeInvalidFields(userid, options, {}, (err, result) => {
                    if (err) {
                        series_cb(err);
                        return;
                    }

                    options = result;

                    series_cb();
                });
            },
        ],
        _err => {
            if (_err) {
                cb(_err);
                return;
            }

            const { collapsed: collapsed_divisions = [] } = divide || {};
            const { collapsed: collapsed_groups = [] } = grouping || {};
            const { scrum_field_id } = box_settings;

            let disable_public_sharing;
            let admin_public_share_override;
            let team_id;
            let page_id_map;
            const sync_blocks_old_to_new = {};
            const syncBlocks = [];
            let attachmentMap = {};
            let entitlements = {};
            let view_id =
                standard && parent.type !== parent_types.template
                    ? getStandardViewId(parent.type, parent.id, type)
                    : null;
            let getViewIdPromise;

            // Locations can have one and only one "canonical channel" that represents the primary chat for that location
            // These views also have use the 'standard' view_id format.
            const isCanonicalChatView = isChatView && view_id === getStandardViewId(parent_type, parent_id, type);

            let permissions = [];
            if (isChatView) {
                permissions.push(config.permission_constants.can_use_chat);
            }

            // Any users allowed to use chat can send direct messages (and thus create a DM/Group DM chat view)
            const isDirectMessageChatView = isChatView && isConversationDMView(options.type, config.views.view_types);

            if (!myTasksWidget && !assignedToUserWidget && !categorySprintBacklog && !isDirectMessageChatView) {
                permissions.push(config.permission_constants.create_view);
            }

            // The new chat service enforces 1-to-1 relationship between locations and chat rooms and uses the
            // standard ID format for all views it creates.
            // To avoid breaking existing chat UI (which allows multiple chats per location), using the standard view ID
            // format will be optional for now.
            if (
                type === view_types.conversation &&
                source_view_id === getStandardViewId(parent.type, parent.id, type)
            ) {
                view_id = source_view_id; // ID may optionally be set to the standard format
            }

            if (myTasksWidget) {
                view_id = getMyTasksWidgetViewId(userid, parent.id);
            }

            if (assignedToUserWidget) {
                view_id = getAssignedToUserViewId(
                    userid,
                    options.user_view_id,
                    parent.id,
                    assignedToUserWidgetWithChanges
                );
                delete options.user_view_id;
            }

            if (categorySprintBacklog) {
                view_id = getSprintCategoryViewId(categorySprintBacklog, parent.id);
            }

            const now = new Date().getTime();

            const { op, search, show_closed } = filters || {};
            let { search_custom_fields, search_description, search_name = true } = filters || {};
            const {
                show_task_locations,
                show_subtasks,
                show_closed_subtasks,
                me_comments,
                me_subtasks,
                me_checklists,
                show_timer,
                collapse_empty_columns,
                time_in_status_view,
                show_empty_statuses,
                auto_wrap,
                is_description_pinned,
                override_parent_hierarchy_filter,
            } = settings;
            let { show_subtask_parent_names, show_assignees, show_images, fast_load_mode } = settings;

            if (
                parent.type === parent_types.shared &&
                ![view_types.list, view_types.board, view_types.calendar].includes(type)
            ) {
                standard = false;
            }

            if (options.is_template) {
                // do not save templates as standard views
                standard = false;
            }

            if (standard) {
                permissions = [];
            }

            if (show_subtask_parent_names == null) {
                show_subtask_parent_names = true;
            }

            if (show_assignees == null) {
                show_assignees = true;
            }

            if (show_images == null) {
                show_images = true;
            }

            if (standard) {
                visibility = config.views.visibility.public;
                members = [];
            }

            if (visibility == null) {
                visibility = config.views.visibility.public;
            }

            if (is_default && visibility !== config.views.visibility.public) {
                is_default = false;
            }

            if (visibility === config.views.visibility.private || visibility === config.views.visibility.personal) {
                members = options.members.filter(member => member.user.id !== userid);
                members.push({
                    user: { id: userid },
                    permission_level: 5,
                });
            }

            if (type === view_types.workload) {
                permissions.push(config.permission_constants.can_create_workload);
            }

            if (!config.views.fast_load_mode_views.includes(Number(type))) {
                fast_load_mode = false;
            }

            const versionUpdates = [];

            async.series(
                [
                    function getTeam(series_cb) {
                        helpers.getViewParentTeamId(parent.id, parent.type, {}, async (err, result) => {
                            if (err) {
                                series_cb(new ViewsError(err, 'VIEWS_021'));
                                return;
                            }

                            ({ team_id, disable_public_sharing, admin_public_share_override } = result);

                            const { disable_template_pub_sharing, parent_type: parent_view_parent_type } = result;

                            if (team_id) {
                                options[TemplateTracer.KEY]?.processSourceAndTargetVerifiedData({
                                    workspaceId: team_id,
                                });

                                try {
                                    entitlements = await viewCrudPaywallService.getEntitlementsEnabled(team_id);
                                } catch (error) {
                                    series_cb(error);
                                    return;
                                }
                            }

                            const paywallFeaturesEnabled = entitlements;

                            if (parent.type === parent_types.widget) {
                                series_cb();
                                return;
                            }

                            if (
                                Number(parent_type) === parent_types.view &&
                                Number(parent_view_parent_type) === parent_types.view
                            ) {
                                series_cb(new ViewsError('Cannot have nested views', 'VIEWS_946', 401));
                                return;
                            }

                            // don't allow customization if less than business plan
                            if (form_settings && !paywallFeaturesEnabled.advanced_form_view) {
                                if (form_settings.display) {
                                    delete form_settings.display.avatar;
                                    delete form_settings.display.style;
                                    delete form_settings.display.color;
                                    delete form_settings.display.background;
                                    delete form_settings.display.show_cover_header;
                                    delete form_settings.display.cover_image_url;
                                    delete form_settings.display.cover_image_color;
                                    delete form_settings.display.cover_position_x;
                                    delete form_settings.display.cover_position_y;
                                    delete form_settings.display.add_captcha;
                                }
                                delete form_settings.template_id;
                                if (form_settings.submission) {
                                    delete form_settings.submission.redirect_uri;
                                    form_settings.submission.response_content = JSON.stringify({
                                        ops: [
                                            { insert: 'Thank You!' },
                                            { attributes: { align: 'center', header: 2 }, insert: '\n' },
                                            { attributes: { align: 'center' }, insert: '\n' },
                                            { insert: 'Your submission has been received.' },
                                            { attributes: { align: 'center' }, insert: '\n' },
                                        ],
                                    });
                                }
                            }

                            // don't allow showRecurringTasks if less than Unlimited plan
                            if (
                                shouldSetShowRecurringTasksToFalseForFreePlan() &&
                                type === view_types.calendar &&
                                !paywallFeaturesEnabled.show_recurring_tasks &&
                                calendar_settings.showRecurringTasks
                            ) {
                                calendar_settings.showRecurringTasks = false;
                            }

                            if (
                                options?.parent?.type === parent_types.template &&
                                (options.public || options.public_sharing) &&
                                disable_template_pub_sharing &&
                                paywallFeaturesEnabled.public_link_restrictions
                            ) {
                                series_cb(new ViewsError('Public template sharing is disabled', 'VIEWS_945', 401));
                                return;
                            }

                            let privacyEntitlement = paywallFeaturesEnabled.private_views;
                            if (type === view_types.doc) {
                                privacyEntitlement = paywallFeaturesEnabled.private_docs;
                            } else if (type === view_types.clickboard) {
                                privacyEntitlement = paywallFeaturesEnabled.private_whiteboards;
                            }

                            if (
                                ![config.views.visibility.public, config.views.visibility.hidden].includes(
                                    visibility
                                ) &&
                                !options.skip_privacy_check &&
                                !isConversationView(type, config.views.view_types) &&
                                !privacyEntitlement
                            ) {
                                series_cb(new ViewsError('Upgrade your team to make views private', 'VIEWS_026', 403));
                                return;
                            }

                            if (isPublic && !paywallFeaturesEnabled.public_views && parent_type === parent_types.team) {
                                const msg = 'Only business plans or higher can share everything views';
                                series_cb(new ViewsError(msg, 'VIEWS_099', 403));
                                return;
                            }

                            if (type === view_types.box && !paywallFeaturesEnabled.team_view) {
                                series_cb(new ViewsError('Upgrade your team to use box views', 'VIEWS_041', 403));
                                return;
                            }

                            if (type === view_types.form && !paywallFeaturesEnabled[formViewEntitlement(team_id)]) {
                                series_cb(new ViewsError('Upgrade your team to use forms', 'VIEWS_065', 403));
                                return;
                            }

                            series_cb();
                        });
                    },

                    function pretty_id(series_cb) {
                        getViewIdPromise = view_id
                            ? Promise.resolve({ pretty_id: view_id })
                            : prettyID.getViewIdAsync(team_id, {});
                        series_cb();
                    },

                    function parallelCalls(series_cb) {
                        async.parallel(
                            {
                                access(para_cb) {
                                    if (parent_type === parent_types.widget) {
                                        // TODO : fix this skip
                                        para_cb();
                                        return;
                                    }

                                    if (userid === config.get('clickbot_assignee')) {
                                        para_cb();
                                        return;
                                    }

                                    checkParentAccess(
                                        userid,
                                        parent.id,
                                        parent.type,
                                        {
                                            permissions,
                                            visibility: options.visibility,
                                            view: {
                                                view_type: type,
                                                creator: options.creator,
                                                visibility,
                                                visible_to: options.visible_to,
                                            },
                                        },
                                        (access_err, parentPermissions) => {
                                            if (access_err) {
                                                para_cb(access_err);
                                                return;
                                            }

                                            ({ role: team_role } = parentPermissions);

                                            if (!standard) {
                                                para_cb();
                                                return;
                                            }

                                            if (
                                                [
                                                    parent_types.template,
                                                    parent_types.team,
                                                    parent_types.shared,
                                                    parent_types.widget,
                                                    parent_types.user_group,
                                                    parent_types.view,
                                                ].includes(parent_type)
                                            ) {
                                                para_cb();
                                                return;
                                            }

                                            helpers.getHierarchyTemplates(
                                                userid,
                                                parent_id,
                                                parent_type,
                                                { parent_permissions: parentPermissions.permissions },
                                                (req_err, req_templates) => {
                                                    if (req_err) {
                                                        para_cb(req_err);
                                                        return;
                                                    }

                                                    let req_template;
                                                    if (type === view_types.list) {
                                                        req_template = req_templates.list_view_template;
                                                    } else if (type === view_types.board) {
                                                        req_template = req_templates.board_view_template;
                                                    } else if (type === view_types.calendar) {
                                                        req_template = req_templates.calendar_view_template;
                                                    } else if (type === view_types.gantt) {
                                                        req_template = req_templates.gantt_view_template;
                                                    } else if (type === view_types.box) {
                                                        req_template = req_templates.box_view_template;
                                                    } else if (type === view_types.activity) {
                                                        req_template = req_templates.activity_view_template;
                                                    } else if (type === view_types.mind_map) {
                                                        req_template = req_templates.mind_map_view_template;
                                                    } else if (type === view_types.timeline) {
                                                        req_template = req_templates.timeline_view_template;
                                                    } else if (type === view_types.table) {
                                                        req_template = req_templates.table_view_template;
                                                    } else if (type === view_types.workload) {
                                                        req_template = req_templates.workload_view_template;
                                                    }

                                                    if (options.default_view_created_from_project_type_list) {
                                                        para_cb(null, { required_template: req_template });
                                                        return;
                                                    }

                                                    if (
                                                        !req_template ||
                                                        !req_template.locked ||
                                                        options.skip_req_template
                                                    ) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    if (
                                                        req_template.locked &&
                                                        !req_template.permissions.can_unprotect
                                                    ) {
                                                        // we want to replace it with the template and just create the template as a view
                                                        para_cb(null, { required_template: req_template });
                                                    } else {
                                                        para_cb();
                                                    }
                                                }
                                            );
                                        }
                                    );
                                },

                                mindMapLimitCheck(para_cb) {
                                    if (type !== view_types.mind_map) {
                                        para_cb();
                                        return;
                                    }

                                    mindMapMod.limitCheck(team_id, para_cb);
                                },

                                pages(para_cb) {
                                    if (!pages || !pages.length) {
                                        para_cb();
                                        return;
                                    }

                                    pages = flattenPages(pages);

                                    getPageIDMap(team_id, pages, (err, result) => {
                                        if (err) {
                                            para_cb(err);
                                            return;
                                        }

                                        ({ page_id_map } = result);

                                        para_cb();
                                    });
                                },

                                async page_content(para_cb) {
                                    if (!pages?.length || !options.is_template) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        const page_ids = pages.map(page => page.id);
                                        const result = await pageHelpers.getPageContent(page_ids);

                                        pages.forEach(page => {
                                            if (result[page.id]) {
                                                page.content = result[page.id].content;
                                                page.text_content = result[page.id].text_content;
                                            }
                                        });

                                        para_cb();
                                    } catch (e) {
                                        para_cb(e);
                                    }
                                },

                                async syncBlocks(para_cb) {
                                    if (parent_type !== parent_types.template || !pages?.length) {
                                        para_cb();
                                        return;
                                    }
                                    try {
                                        syncBlocks.push(
                                            ...(await getSyncBlocksByParentIds(
                                                pages.map(page => page.id),
                                                ObjectType.DOC
                                            ))
                                        );
                                        para_cb();
                                    } catch (err) {
                                        para_cb(err);
                                    }
                                },

                                async attachments(para_cb) {
                                    if (parent_type !== parent_types.template || !pages?.length) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        const { pretty_id: newViewId } = await getViewIdPromise;
                                        ({ attachmentMap } = await copyAttachmentsAsync(
                                            source_view_id,
                                            newViewId,
                                            null,
                                            {
                                                team_id,
                                                type: config.attachments.types.view,
                                                attachments: true,
                                                copy_attachments_from_s3: true,
                                            }
                                        ));
                                        para_cb();
                                    } catch (err) {
                                        para_cb(err);
                                    }
                                },

                                template_members(para_cb) {
                                    if (template_visibility !== config.views.template_visibility.users) {
                                        para_cb();
                                        return;
                                    }

                                    if (!template_members.includes(userid)) {
                                        template_members.push(userid);
                                    }

                                    async.each(
                                        template_members,
                                        (member, each_cb) => {
                                            access.checkAccessTeam(member, team_id, { permissions: [] }, each_cb);
                                        },
                                        para_cb
                                    );
                                },

                                async pretty_id(para_cb) {
                                    if (view_id) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        ({ pretty_id: view_id } = await getViewIdPromise);
                                        para_cb();
                                    } catch (err) {
                                        para_cb(err);
                                    }
                                },

                                async template_group_members(para_cb) {
                                    if (
                                        template_visibility !== config.views.template_visibility.users ||
                                        !template_group_members.length
                                    ) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        await groupSharingPaywall.checkGroupSharingPaywall(team_id, 'team');
                                    } catch (error) {
                                        para_cb(error);
                                        return;
                                    }

                                    const query = `SELECT DISTINCT userid FROM task_mgmt.group_members WHERE group_id = ANY($1)`;
                                    const params = [template_group_members];
                                    db.replicaQuery(query, params, (err, grpMemResult) => {
                                        if (err) {
                                            para_cb(err);
                                            return;
                                        }

                                        if (!grpMemResult.rows || !grpMemResult.rows.length) {
                                            para_cb();
                                            return;
                                        }

                                        const userids = grpMemResult.rows.map(row => row.userid);

                                        async.each(
                                            userids,
                                            (member, each_cb) => {
                                                access.checkAccessTeam(member, team_id, { permissions: [] }, each_cb);
                                            },
                                            each_err => {
                                                if (each_err) {
                                                    para_cb(each_err);
                                                    return;
                                                }

                                                template_members = [...new Set([...template_members, ...userids])];
                                                para_cb();
                                            }
                                        );
                                    });
                                },

                                attachmentAccess(para_cb) {
                                    if (!avatar_source) {
                                        para_cb();
                                        return;
                                    }

                                    access.checkAccessAttachment(userid, avatar_source, { permissions: [] }, para_cb);
                                },

                                useGantt(para_cb) {
                                    if (!options.use_gantt || options.type !== view_types.gantt) {
                                        para_cb();
                                        return;
                                    }

                                    helpers.incrementGanttUsage({ parent_id, parent_type, team_id }, para_cb);
                                },

                                async teamView(para_cb) {
                                    if (!options.from_request || options.type !== view_types.box) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        await helpers.incrementTeamViewUsage({ parent_id, parent_type });
                                        para_cb();
                                    } catch (error) {
                                        para_cb(error);
                                    }
                                },

                                async timeline(para_cb) {
                                    if (!options.from_request || options.type !== view_types.timeline) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        await helpers.timelineIncreaseTeamLimit(null, { parent_id, parent_type });
                                        para_cb();
                                    } catch (error) {
                                        para_cb(error);
                                    }
                                },

                                async workload(para_cb) {
                                    if (!options.from_request || options.type !== view_types.workload) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        await helpers.workloadIncreaseTeamLimit(null, { parent_id, parent_type });
                                        para_cb();
                                    } catch (error) {
                                        para_cb(error);
                                    }
                                },

                                ganttPaywall(para_cb) {
                                    if (options.type !== view_types.gantt) {
                                        para_cb();
                                        return;
                                    }

                                    helpers.checkGanttPaywall({ parent_id, parent_type }, para_cb);
                                },

                                async embedPaywall(para_cb) {
                                    if (options.type !== view_types.embed) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        await embedPaywallService.checkPaywall({
                                            parentId: parent_id,
                                            parentType: parent_type,
                                        });
                                        para_cb();
                                    } catch (error) {
                                        para_cb(error);
                                    }
                                },

                                async timelinePaywall(para_cb) {
                                    if (options.type !== view_types.timeline) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        await helpers.timelineLimitCheck(null, { parent_id, parent_type });
                                        para_cb();
                                    } catch (error) {
                                        para_cb(error);
                                    }
                                },

                                async workloadPaywall(para_cb) {
                                    if (options.type !== view_types.workload) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        await helpers.workloadLimitCheck(null, { parent_id, parent_type });
                                        para_cb();
                                    } catch (error) {
                                        para_cb(error);
                                    }
                                },

                                whiteboardPaywall(para_cb) {
                                    if (options.type !== view_types.clickboard) {
                                        para_cb();
                                        return;
                                    }
                                    helpers.checkWhiteboardPaywall({ parent_id, parent_type }, para_cb);
                                },

                                async mapViewPaywall(para_cb) {
                                    if (!options.from_request || options.type !== view_types.map) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        await helpers.checkMapPaywall({ team_id });
                                        para_cb();
                                    } catch (error) {
                                        para_cb(error);
                                    }
                                },

                                async teamViewPaywall(para_cb) {
                                    if (
                                        !options.from_request ||
                                        options.type !== view_types.box ||
                                        categorySprintBacklog
                                    ) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        await helpers.checkTeamViewPaywall({ parent_id, parent_type });
                                        para_cb();
                                    } catch (error) {
                                        para_cb(error);
                                    }
                                },

                                async incrementDefaultViewUsage(para_cb) {
                                    if (
                                        !options.default ||
                                        [view_types.location_overview, view_types.form].includes(options.type)
                                    ) {
                                        para_cb();
                                        return;
                                    }
                                    try {
                                        await defaultViewPaywallService.checkPaywall({ teamId: team_id });
                                        await defaultViewPaywallService.incrementLimit({ teamId: team_id });
                                        para_cb();
                                    } catch (error) {
                                        para_cb(error);
                                    }
                                },

                                async protectedViewPaywall(para_cb) {
                                    // Docs are not subject to the protected views limit.
                                    if (!options.from_request || !locked || type === view_types.doc) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        await helpers.checkProtectedViewsLimit({ parent_id, parent_type });
                                        await helpers.incrementProtectedViewsLimit({ parent_id, parent_type });
                                        para_cb();
                                    } catch (error) {
                                        para_cb(error);
                                    }
                                },

                                formSubcategory(para_cb) {
                                    if (options.type !== view_types.form || !form_settings.subcategory_id) {
                                        para_cb();
                                        return;
                                    }

                                    access.checkAccessSubcategory(
                                        userid,
                                        form_settings.subcategory_id,
                                        { permissions: [can_create_tasks] },
                                        para_cb
                                    );
                                },

                                formAssignees(para_cb) {
                                    if (options.type !== view_types.form) {
                                        para_cb();
                                        return;
                                    }

                                    const { assignees = [] } = form_settings;

                                    async.each(
                                        assignees,
                                        (assignee_id, each_cb) =>
                                            access.checkAccessSubcategory(
                                                assignee_id,
                                                form_settings.subcategory_id,
                                                { permissions: [] },
                                                each_cb
                                            ),
                                        para_cb
                                    );
                                },

                                validateScrumFieldID(para_cb) {
                                    if (!scrum_field_id || parent_type === parent_types.template) {
                                        para_cb();
                                        return;
                                    }

                                    fieldHelpers.getParentFields(parent.id, parent.type, {}, (err, result) => {
                                        if (err) {
                                            para_cb(err);
                                            return;
                                        }

                                        const field = result.find(row => row.field_id === scrum_field_id);

                                        if (!field) {
                                            const msg = 'Scrum field does not exist at this location';
                                            para_cb(new ViewsError(msg, 'VIEWS_037', 400));
                                            return;
                                        }

                                        if (![field_types.drop_down, field_types.number].includes(Number(field.type))) {
                                            const msg = 'Scrum field must be drop down or number';
                                            para_cb(new ViewsError(msg, 'VIEWS_039', 400));
                                            return;
                                        }

                                        para_cb();
                                    });
                                },

                                modifyMembers(para_cb) {
                                    if (!members || !members.length) {
                                        para_cb();
                                        return;
                                    }

                                    const member_ids = [userid, ...members.map(member => member.user.id)];

                                    const query = `
                                        SELECT userid, role
                                        FROM task_mgmt.team_members
                                        WHERE team_id = $1
                                            AND userid = ANY($2)`;
                                    const params = [team_id, member_ids];

                                    db.readQuery(query, params, (err, result) => {
                                        if (err) {
                                            para_cb(new ViewsError(err, 'VIEWS_042'));
                                            return;
                                        }

                                        const member_map = {};

                                        result.rows.forEach(row => {
                                            member_map[row.userid] = row;
                                        });

                                        if (member_map[userid] && member_map[userid].role === config.team_roles.guest) {
                                            locked = false;
                                        }

                                        members = members
                                            .map(member => {
                                                const { role } = member_map[member.user.id] || {};

                                                if (role == null) {
                                                    return null; // member not on team
                                                }

                                                if (
                                                    role === config.team_roles.guest &&
                                                    ![
                                                        config.views.visibility.private,
                                                        config.views.visibility.personal,
                                                    ].includes(visibility) &&
                                                    !isConversationView(type)
                                                ) {
                                                    member.permission_level = 1;
                                                }

                                                return member;
                                            })
                                            .filter(Boolean);

                                        para_cb();
                                    });
                                },

                                async columnsAndSoring(para_cb) {
                                    if (!columns && !sorting) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        const [validColumnFields, validSortFields] = await Promise.all([
                                            await helpers.validateColumnsAndSorting(
                                                userid,
                                                columns.fields || [],
                                                parent
                                            ),
                                            await helpers.validateColumnsAndSorting(
                                                userid,
                                                sorting.fields || [],
                                                parent
                                            ),
                                        ]);

                                        columns.fields = validColumnFields;
                                        sorting.fields = validSortFields;

                                        para_cb();
                                    } catch (err) {
                                        para_cb(err);
                                    }
                                },

                                mapSettingsCheck(para_cb) {
                                    validateMapSettings(map_settings, { parent }, para_cb);
                                },

                                async userFilterSettings(para_cb) {
                                    if (!options.user_filter_settings) {
                                        para_cb();
                                        return;
                                    }
                                    try {
                                        const filter_settings = await getUserFilterSettings(userid);

                                        ({
                                            search_custom_fields,
                                            search_description,
                                            search_name = true,
                                        } = filter_settings);

                                        para_cb();
                                    } catch (err) {
                                        cb(new ViewsError(err, 'VIEWS_101'));
                                    }
                                },

                                async docWikiEntitlementCheck(para_cb) {
                                    if (
                                        options.is_template ||
                                        !(type === view_types.doc && options.doc_type === doc_types.wiki)
                                    ) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        const wikiCount = await getWikiCountForWorkspace(team_id);
                                        await entitlementService.checkEntitlementLimit(
                                            team_id,
                                            EntitlementName.DocWikiLimit,
                                            wikiCount + 1,
                                            { throwOnDeny: true }
                                        );
                                        para_cb();
                                    } catch (err) {
                                        para_cb(err);
                                    }
                                },

                                async checkWhiteboardSourceAccess(para_cb) {
                                    if (type !== view_types.clickboard || !source_view_id) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        await checkViewReadAccess(userid, source_view_id);
                                        para_cb();
                                    } catch (err) {
                                        para_cb(err);
                                    }
                                },

                                async createAgentUser(para_cb) {
                                    if (
                                        options.type !== view_types.agent_workspace &&
                                        options.type !== view_types.agent
                                    ) {
                                        para_cb();
                                        return;
                                    }
                                    try {
                                        options.agent_id = await createAgentUser(
                                            userid,
                                            team_id,
                                            options.type === view_types.agent_workspace ? undefined : options.name
                                        );
                                        para_cb();
                                    } catch (err) {
                                        para_cb(err);
                                    }
                                },
                            },
                            (para_err, para_result) => {
                                if (para_result.access && para_result.access.required_template) {
                                    const { default_view_created_from_project_type_list } = options;
                                    options = para_result.access.required_template;
                                    options.skip_req_template = true;
                                    options.standard = true;

                                    if (default_view_created_from_project_type_list) {
                                        options.default = true;
                                    }

                                    delete options.id;
                                    _createView(userid, options, cb);
                                    return;
                                }

                                series_cb(para_err);
                            }
                        );
                    },
                ],
                async err => {
                    if (err) {
                        cb(err);
                        return;
                    }

                    let extra_settings;
                    let extra_user_settings;
                    let expanded_tasks_state;

                    let form_fields;
                    let form_subcategory_id;
                    let form_active;
                    let form_token;
                    let workload_capacity_type;
                    let made_public_by;
                    let made_public_time;
                    const valid_workload_types = Object.keys(config.views.workload_capacity_types).map(k =>
                        parseInt(k, 10)
                    );
                    if (Number(type) === view_types.form) {
                        extra_settings = form_settings;
                        const fields = extra_settings.fields ?? [];
                        const conditional_fields = extra_settings.conditional_fields ?? [];

                        form_fields = fields.concat(conditional_fields);
                        form_subcategory_id = extra_settings.subcategory_id;
                        form_active = form_subcategory_id ? Boolean(extra_settings.active) : false;
                        form_token = generateToken(18);

                        delete extra_settings.fields;
                        delete extra_settings.conditional_fields;
                        delete extra_settings.subcategory_id;
                        delete extra_settings.active;
                    } else if (
                        Number(type) === view_types.workload &&
                        workload_settings &&
                        valid_workload_types.includes(parseInt(workload_settings.capacity_type, 10))
                    ) {
                        workload_capacity_type = workload_settings.capacity_type;
                    } else if (Number(type) === view_types.list) {
                        expanded_tasks_state = expanded_tasks;

                        /**
                         * @important
                         * View with type = view_types.list might be a one of multiple Views that are used under the hood of
                         * Cards/Widgets (Drilldown View, Task List Widget View), which has some business logic
                         * that depends on some extra_settings values.
                         *
                         * Highly do not recommend to override these values, because it will lead to a production regression.
                         */
                        if (Number(parent_type) === parent_types.widget && drilldownViewSettings) {
                            extra_settings = {
                                ...drilldownViewSettings,
                                is_drilldown: true,
                                show_task_properties: settings.show_task_properties ?? true,
                            };
                        } else {
                            extra_settings = {
                                show_task_properties: settings.show_task_properties ?? true,
                                show_sprint_cards: settings.show_sprint_cards ?? defaultSprintCardsSettings,
                            };
                        }

                        if (task_custom_type) {
                            extra_settings.task_custom_type = task_custom_type;
                        }
                    } else if (Number(type) === view_types.map) {
                        extra_settings = map_settings;
                    } else if (Number(type) === view_types.board) {
                        extra_settings = {
                            show_task_ids: settings.show_task_ids || false,
                            task_cover: settings.task_cover || 1,
                            field_rendering: settings.field_rendering || 1,
                            colored_columns: settings.colored_columns || false,
                            card_size: settings.card_size || 2,
                            show_empty_fields: settings.show_empty_fields ?? true,
                            show_task_properties: settings.show_task_properties ?? true,
                        };
                    } else if (Number(type) === view_types.mind_map && mind_map_settings) {
                        const custom = mind_map_settings.custom || false;
                        extra_settings = { custom };
                    } else if (Number(type) === view_types.calendar) {
                        extra_settings = calendar_settings;
                    } else if (Number(type) === view_types.timeline) {
                        extra_settings = timeline_settings;
                    } else if (Number(type) === view_types.workload) {
                        extra_settings = workload_settings;
                    } else if (Number(type) === view_types.embed) {
                        extra_settings = embed_settings;
                    } else if (Number(type) === view_types.table) {
                        extra_settings = table_settings;
                    } else if (Number(type) === view_types.gantt) {
                        extra_settings = gantt_settings;

                        extra_user_settings = {
                            viewSettingsUser: extra_settings.viewSettingsUser,
                            openedItems: extra_settings.openedItems,
                        };

                        delete extra_settings.viewSettingsUser;
                        delete extra_settings.openedItems;
                    } else if (Number(type) === view_types.box) {
                        extra_settings = box_settings;

                        extra_user_settings = {
                            collapsed: extra_settings.collapsed,
                        };

                        delete extra_settings.collapsed;
                    } else if (isConversationView(type, view_types)) {
                        if (conversation_settings && typeof conversation_settings === 'object') {
                            const whitelist = [
                                'description',
                                'topic',
                                'roomType',
                                'version',
                                'users',
                                'chat_room_category',
                            ];
                            extra_settings = Object.fromEntries(
                                Object.entries(conversation_settings).filter(([key]) => whitelist.includes(key))
                            );
                        }
                    } else if (Number(type) === view_types.agent || Number(type) === view_types.agent_workspace) {
                        extra_settings = {
                            agent_id: options.agent_id,
                            trigger_id: options.extra?.trigger_id,
                            trigger_ids: options.extra?.trigger_ids,
                        };
                    } else if (Number(type) === view_types.agent_v1) {
                        extra_settings = {
                            agent_id: options.extra?.agent_id,
                            trigger_ids: options.extra?.trigger_ids,
                        };
                    }

                    if (Number(type) === view_types.board && board_settings) {
                        if (extra_settings) {
                            extra_settings.board_settings = board_settings;
                        } else {
                            extra_settings = { board_settings };
                        }
                    }
                    if (Number(type) === view_types.clickboard) {
                        if (whiteboardsViewSettings) {
                            extra_settings = { whiteboardVersion: whiteboardsViewSettings.whiteboardVersion };
                        }
                    }

                    if (Number(type) === view_types.doc) {
                        extra_settings = {
                            doc_type: options.doc_type ?? doc_types.standard,
                        };
                    }

                    if (disable_public_sharing && (!admin_public_share_override || Number(team_role) > 2)) {
                        isPublic = false;
                    }

                    if (isPublic) {
                        made_public_by = userid;
                        made_public_time = new Date().getTime();
                    }

                    const orderindexColumn = isSidebarView ? 'sidebar_orderindex' : 'orderindex';

                    const commentsDbQueries = [];
                    const queries = [
                        {
                            query: `
                                INSERT INTO task_mgmt.views(
                                    view_id, date_created, creator, name, type, parent_id, parent_type, visibility, grouping_field,
                                    grouping_dir, division_field, division_dir, show_task_locations, show_subtasks,
                                    show_subtask_parent_names, me_view, me_comments, me_subtasks, me_checklists, filter_op, extra,
                                    search, standard, locked, collapsed_groups, collapsed_divisions, ignore_grouping, ${orderindexColumn},
                                    show_assignees, show_images, team_id, deleted, public, seo_optimized, "default",
                                    sidebar_assignees, sidebar_assigned_comments, sidebar_unassigned_tasks, frozen_note,
                                    date_frozen, frozen_by, visible_to, collapse_empty_columns, template_visibility,
                                    template_visible_to, show_closed_subtasks, show_closed, date_updated, locked_permission,
                                    form_subcategory_id, form_active, form_token, share_tasks, share_task_fields, pinned,
                                    avatar_source, avatar_value, avatar_color, made_public_by, made_public_time,
                                    public_share_expires_on, workload_capacity_type, show_timer, exclude_multiple_lists,
                                    exclude_sub_multiple_lists, sidebar_group_assignees, show_empty_statuses, time_in_status_view,
                                    expanded_tasks, template_public, auto_wrap, search_custom_fields, search_description, search_name, sidebar_view,
                                    division_by_subcategory, is_description_pinned, override_parent_hierarchy_filter, group_by_single, share_with_team, fast_load_mode
                                ) VALUES (
                                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16,
                                    $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27,
                                    (
                                        SELECT coalesce(max(${orderindexColumn}), 0) + 1
                                        FROM task_mgmt.views
                                        WHERE parent_id = $6
                                        AND parent_type = $7
                                    ),
                                    $28, $29, $30, false, $49, false, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40,
                                    $41, $42, $43, $44, $45, $46, $47, $48, $50, $51, $52, $53, $54, $55, $56, $57, $58,
                                    $59, $60, $61, $62, $63, $64, $65, $66, $67, $68, $69, $70, $71, $72, $73, $74, $75,
                                    $76, $77, $78
                            )`,
                            params: [
                                view_id,
                                now,
                                userid,
                                name,
                                type,
                                parent.id,
                                parent.type,
                                visibility,
                                grouping.field,
                                grouping.dir,
                                divide.field,
                                divide.dir,
                                show_task_locations,
                                show_subtasks,
                                show_subtask_parent_names,
                                me_view,
                                me_comments,
                                me_subtasks,
                                me_checklists,
                                op,
                                extra_settings,
                                search,
                                standard || false,
                                locked || false,
                                collapsed_groups,
                                collapsed_divisions,
                                grouping.ignore || false,
                                show_assignees,
                                show_images,
                                team_id,
                                is_default,
                                team_sidebar.assignees || [],
                                team_sidebar.assigned_comments || false,
                                team_sidebar.unassigned_tasks || false,
                                frozen_note,
                                locked ? now : null,
                                locked ? userid : null,
                                visibility !== config.views.visibility.public ? userid : null,
                                collapse_empty_columns,
                                template_visibility,
                                template_visibility !== config.views.template_visibility.public ? userid : null,
                                show_closed_subtasks || false,
                                show_closed || false,
                                now,
                                locked_permission,
                                form_subcategory_id || null,
                                form_active,
                                form_token,
                                isPublic,
                                share_tasks || false,
                                share_task_fields,
                                pinned || false,
                                avatar_source,
                                avatar_value,
                                avatar_color,
                                made_public_by,
                                made_public_time,
                                public_share_expires_on,
                                workload_capacity_type,
                                show_timer,
                                exclude_multiple_lists,
                                exclude_sub_multiple_lists,
                                team_sidebar.group_assignees || [],
                                show_empty_statuses || false,
                                time_in_status_view || 1,
                                expanded_tasks_state,
                                template_public,
                                auto_wrap || false,
                                search_custom_fields == null ? false : search_custom_fields,
                                search_description,
                                search_name,
                                isSidebarView,
                                divide?.by_subcategory,
                                is_description_pinned || false,
                                override_parent_hierarchy_filter || false,
                                grouping.single,
                                share_with_team,
                                fast_load_mode || false,
                            ],
                        },
                    ];
                    versionUpdates.push({
                        object_type: ObjectType.VIEW,
                        object_id: view_id,
                        workspace_id: team_id,
                        operation: OperationType.CREATE,
                    });

                    if (auto_save != null || extra_user_settings) {
                        queries.push({
                            query: `
                                INSERT INTO task_mgmt.view_preferences(view_id, userid, workspace_id) (
                                    SELECT $1, $2, $3 WHERE NOT EXISTS (
                                        SELECT 1
                                        FROM task_mgmt.view_preferences
                                        WHERE userid = $2
                                            AND view_id = $1
                                    )
                                )`,
                            params: [view_id, userid, team_id],
                        });

                        let update_query = `UPDATE task_mgmt.view_preferences SET `;
                        const update_params = [view_id, userid];

                        if (auto_save != null) {
                            update_params.push(auto_save);
                            update_query += ` auto_save = $${update_params.length}`;
                        }

                        if (extra_user_settings) {
                            if (update_params.length > 2) {
                                update_query += `,`;
                            }

                            update_params.push(extra_user_settings);
                            update_query += ` extra = $${update_params.length}`;
                        }

                        update_query += ` WHERE view_id = $1 AND userid = $2`;

                        queries.push({
                            query: update_query,
                            params: update_params,
                        });
                    }

                    if (sorting && sorting.fields) {
                        sorting.fields.forEach((sort, idx) => {
                            let { link } = sort;

                            if (!link) {
                                link = {};
                            }

                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.view_sort(view_id, field, dir, idx, link_type, linked_to, hidden_types, linked_func, workspace_id)
                                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
                                params: [
                                    view_id,
                                    sort.field,
                                    sort.dir,
                                    idx,
                                    link.type,
                                    link.field_id,
                                    link.hidden,
                                    link.func,
                                    team_id,
                                ],
                            });
                        });
                    }

                    if (columns?.fields?.length) {
                        const batches = viewsBulkInsertParams(columns, view_id, team_id);
                        batches.forEach(({ bulkInsertQuery, bulkInsertParams }) => {
                            queries.push({
                                query: bulkInsertQuery,
                                params: bulkInsertParams,
                            });
                        });
                    }

                    if (filters && filters.fields) {
                        if (filters.filter_groups?.length && filters.filter_group_ops?.length) {
                            filters.filter_groups = JSON.stringify(filters.filter_groups);

                            queries.push({
                                query: `INSERT INTO task_mgmt.view_filter_groups(view_id, groups, ops, workspace_id) VALUES($1, $2, $3, $4)`,
                                params: [view_id, filters.filter_groups, filters.filter_group_ops, team_id],
                            });
                        }

                        filters.fields.forEach((filter, idx) => {
                            if (filter.values && filter.values.length === 0) {
                                filter.values = '{}';
                            } else if (filter.values) {
                                filter.values = filter.values.map(value => JSON.stringify(value));
                            }

                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.view_filters(view_id, field, idx, op, determinor, values, workspace_id)
                                    VALUES ($1, $2, $3, $4, $5, $6, $7)`,
                                params: [
                                    view_id,
                                    filter.field,
                                    idx,
                                    filter.op,
                                    filter.determinor,
                                    filter.values,
                                    team_id,
                                ],
                            });
                        });
                    }

                    if (
                        Number(type) === Number(config.views.view_types.activity) &&
                        activity_settings &&
                        activity_settings.filters &&
                        activity_settings.filters.length
                    ) {
                        activity_settings.filters.forEach((filter, idx) => {
                            if (filter.values && filter.values.length === 0) {
                                filter.values = '{}';
                            } else if (filter.values) {
                                filter.values = filter.values.map(value => JSON.stringify(value));
                            }

                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.view_filters(view_id, field, idx, values, workspace_id)
                                    VALUES ($1, $2, $3, $4, $5)`,
                                params: [view_id, filter.field, idx, filter.values, team_id],
                            });
                        });
                    }

                    if (form_fields) {
                        form_fields.forEach((field, idx) => {
                            if (field.options) {
                                field.options = _.pick(field.options, ['due_date_time', 'start_date_time', 'time']);
                            }

                            queries.push({
                                query: `
                                    INSERT INTO task_mgmt.form_fields(
                                        form_id,
                                        field,
                                        idx,
                                        required,
                                        display,
                                        options,
                                        placeholder,
                                        hidden,
                                        append_description,
                                        content,
                                        conditions,
                                        workspace_id
                                    )
                                    VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)`,
                                params: [
                                    view_id,
                                    field.field,
                                    idx,
                                    field.required || false,
                                    field.display_name,
                                    field.options,
                                    field.placeholder,
                                    field.hidden || false,
                                    field.append_description || false,
                                    field.content,
                                    field.conditions,
                                    team_id,
                                ],
                            });
                        });
                    }

                    if (pages) {
                        syncBlocks.forEach(syncBlock => {
                            sync_blocks_old_to_new[syncBlock.id] = uuid.v4();
                        });
                        pages.forEach(page => {
                            const createOptions = _.pick(page, [
                                'name',
                                'subtitle',
                                'color',
                                'text_content',
                                'avatar',
                                'full_width',
                                'creator',
                                'deleted',
                                'deleted_by',
                                'public',
                                'seo_optimized',
                                'thumbnail_s3_key',
                                'cover_image_url',
                                'cover_image_color',
                                'cover_position_x',
                                'cover_position_y',
                            ]);

                            createOptions.parent = page.parent && page_id_map[page.parent];
                            let pageContent = page.content;
                            if (options.is_template && page.content) {
                                try {
                                    const quillContent = parseQuillContent(encrypt.decrypt(page.content));
                                    pageContent = replaceDocsInContent(quillContent, page_id_map, view_id);
                                    if (parent_type === parent_types.template) {
                                        if (syncBlocks.length) {
                                            pageContent = replaceSyncBlocksInContent(
                                                pageContent,
                                                sync_blocks_old_to_new
                                            );
                                        }
                                        if (Object.keys(attachmentMap).length) {
                                            pageContent = replaceAttachmentsInContent(pageContent, attachmentMap);
                                        }
                                    }
                                    pageContent = encrypt.encrypt(JSON.stringify(pageContent));
                                } catch (quillErr) {
                                    // do nothing
                                }
                            }
                            createOptions.content =
                                (options.is_template && page.content && pageContent) ||
                                (page.content && encrypt.encrypt(page.content));
                            createOptions.position = page.orderindex;
                            createOptions.date_deleted = page.date_deleted && new Date().getTime();
                            createOptions.team_id = team_id;

                            queries.push(preparePageInsertQuery(userid, view_id, page_id_map[page.id], createOptions));

                            // copy over the presentation details to the new page
                            const detailsOptions = {
                                presentation_details: page.presentation_details,
                            };
                            const detailsQuery = prepareSinglePresentationDetailsUpdateQuery(
                                page_id_map[page.id],
                                team_id,
                                detailsOptions
                            );
                            if (detailsQuery) {
                                queries.push(detailsQuery);
                            }

                            const authorIds = page.authors && page.authors.length ? page.authors.map(a => a.id) : [];
                            queries.push(
                                prepareAuthorInsertQuery(
                                    page.creator || userid,
                                    page_id_map[page.id],
                                    team_id,
                                    authorIds
                                )
                            );
                        });

                        if (parent_type === parent_types.template && syncBlocks.length > 0) {
                            syncBlocks.forEach(syncBlock => {
                                syncBlock.parent_id = page_id_map[syncBlock.parent_id];
                                syncBlock.id = sync_blocks_old_to_new[syncBlock.id];
                            });
                            queries.push(...prepareSyncBlockQueries(userid, team_id, syncBlocks));
                        }
                    }

                    const defaultPermissionLevel = getDefaultViewMemberPermissionLevel(team_id, type);
                    members.forEach(member => {
                        queries.push({
                            query: 'INSERT INTO task_mgmt.view_members(view_id, userid, permission_level, workspace_id) VALUES ($1, $2, $3, $4)',
                            params: [
                                view_id,
                                member.user.id,
                                member.permission_level ?? defaultPermissionLevel,
                                team_id,
                            ],
                        });
                    });

                    if (is_default) {
                        queries.push({
                            query: `
                            UPDATE task_mgmt.views
                            SET "default" = false
                            WHERE parent_id = $1
                                AND parent_type = $2
                                AND view_id != $3`,
                            params: [parent.id, parent.type, view_id],
                        });
                    }

                    if (!standard) {
                        queries.push(
                            {
                                query: `
                                    INSERT INTO task_mgmt.team_limits(team_id)
                                    VALUES($1)
                                    ON CONFLICT (team_id) DO NOTHING
                                    `,
                                params: [team_id],
                            },
                            {
                                query: `
                                    UPDATE task_mgmt.team_limits
                                    SET views = coalesce(views, 0) + 1
                                    WHERE team_id = $1`,
                                params: [team_id],
                            }
                        );
                    }

                    template_members.forEach(member => {
                        queries.push({
                            query: `
                            INSERT INTO task_mgmt.view_template_members(view_id, userid, workspace_id)
                            SELECT $1, $2, $3
                            WHERE EXISTS (
                                SELECT 1 FROM task_mgmt.team_members WHERE team_id = $3 AND userid = $2
                            )`,
                            params: [view_id, member, team_id],
                        });
                    });

                    template_group_members.forEach(grp_member => {
                        queries.push({
                            query: `
                            INSERT INTO task_mgmt.view_template_group_members(view_id, group_id, workspace_id)
                            SELECT $1, $2, $3
                            WHERE EXISTS (
                                SELECT 1 FROM task_mgmt.groups WHERE team_id = $3 AND id = $2
                            )`,
                            params: [view_id, grp_member, team_id],
                        });
                    });

                    if (parent_type === parent_types.template) {
                        const params = [view_id, 'view', description];

                        // view template options
                        const template_options = {};
                        let settings_counts = {};
                        if (type === view_types.doc) {
                            let page_names = [];
                            if (pages) {
                                page_names = pages.map(page => page.name);
                            }

                            template_options.count_details = {
                                pages: page_names,
                            };

                            settings_counts = {
                                pages: (pages && pages.length) || 0,
                            };
                        } else {
                            if (filters && filters.fields) {
                                template_options.view_filters = filters.fields.map((field, index) => ({
                                    field: field.field,
                                    idx: index,
                                    op: field.op,
                                    values: Array.isArray(field.values)
                                        ? field.values.map(value => JSON.parse(value))
                                        : JSON.parse(field.values),
                                    determinor: field.determinor,
                                }));
                                settings_counts.view_filters = filters.fields.length || 0;
                            } else {
                                template_options.view_filters = [];
                                settings_counts.view_filters = 0;
                            }

                            if (columns && columns.fields) {
                                template_options.view_cols = columns.fields
                                    .map((col, index) => {
                                        if (!col.hidden) {
                                            return {
                                                field: col.field,
                                                idx: index,
                                                width: col.width,
                                                hidden: col.hidden,
                                                name: col.name,
                                                display: col.display,
                                            };
                                        }
                                        return null;
                                    })
                                    .filter(col => col);
                                settings_counts.view_cols = template_options.view_cols.length || 0;
                            } else {
                                template_options.view_cols = [];
                                settings_counts.view_cols = 0;
                            }
                        }

                        params.push({ count_details: template_options });
                        params.push(settings_counts);
                        params.push(team_id);

                        queries.push({
                            query: `
                                INSERT INTO task_mgmt.template_options
                                    ( template_id, type, description, options, settings_counts, workspace_id )
                                VALUES ( $1, $2, $3, $4, $5, $6 )`,
                            params,
                        });
                    }

                    if (tags && tags.length) {
                        let values = `VALUES`;
                        const params = [view_id, 'view', team_id];
                        options.tags.forEach((tag, idx) => {
                            if (idx > 0) {
                                values += `,`;
                            }
                            params.push(tag.id);
                            values += `($1, $2, $${params.length}, false, $3)`;
                        });

                        queries.push({
                            query: `
                                INSERT INTO task_mgmt.template_tags (template_id, template_type, tag_id, deleted, workspace_id) ${values}
                                ON CONFLICT (template_id, template_type, tag_id) DO UPDATE SET deleted = FALSE, deleted_by = NULL, date_deleted = NULL`,
                            params,
                        });
                    }

                    let is_canonical_channel = false;
                    if (isConversationView(type, view_types)) {
                        // Locations can have one and only one "canonical channel" that represents the primary chat for that location
                        // These views also have use the 'standard' view_id format.
                        is_canonical_channel =
                            type === view_types.conversation &&
                            view_id === getStandardViewId(parent_type, parent_id, type);

                        try {
                            const shouldUseCommentsDb = await isNewCommentsDbEnabledForWorkspace(team_id);
                            const query = getChatViewInsertQuery(
                                view_id,
                                team_id,
                                is_canonical_channel,
                                options,
                                shouldUseCommentsDb
                            );
                            (shouldUseCommentsDb ? commentsDbQueries : queries).push(query);
                        } catch (error) {
                            logger.error({
                                msg: 'Failed to populate chat view query',
                                ECODE: 'CHAT_VIEW_003',
                            });
                            is_canonical_channel = false;
                        }
                    }

                    if (options.return_queries) {
                        cb(null, { view_id, queries });
                        return;
                    }

                    if (parent_type === parent_types.task) {
                        if (team_id) {
                            versionUpdates.push({
                                object_type: ObjectType.TASK,
                                object_id: parent_id,
                                workspace_id: team_id,
                                operation: OperationType.UPDATE,
                            });
                        } else {
                            logger.error({
                                msg: 'Failed to write OVM update, Missing workspace ID',
                                ECODE: 'OVM_WS_949',
                                OVM_CRITICAL: true,
                            });
                        }
                    }

                    try {
                        for (const { query, params } of commentsDbQueries) {
                            await db.commentsWriteQueryAsync(query, params);
                        }

                        if (isConversationView(type, view_types) && is_canonical_channel) {
                            await updateCanonicalChannelInfo(view_id, parent_id, parent_type, team_id);
                        }

                        await batchQueriesSeriesAsync(queries, versionUpdates);
                        if (type === view_types.clickboard) {
                            /**
                             * @important
                             * When creating a template of a whiteboard, we need to make sure that the underlying view was created
                             * before, so when we are making a call to Whiteboards Service API on behalf of a user
                             * (either by propagating the user's token or by generating a short-term token)
                             * we will pass all the permission checks.
                             *
                             * So, please, make sure to keep the order of the operations.
                             */

                            await replaceWhiteboardTemplate(source_view_id, type, {
                                userId: userid,
                                whiteboardId: view_id,
                                workspaceId: team_id,
                            });
                            reportWhiteboardChanged(userid, view_id);
                        } else if (isViewRelatedToDashboard({ type })) {
                            await createDashboardForView({
                                userId: userid,
                                viewId: view_id,
                                workspaceId: team_id,
                            });
                        } else if (type === view_types.form) {
                            reportFormChanged(userid, view_id);
                        } else if (shouldIndexView(options.type)) {
                            reportViewChanged(view_id);
                        }

                        // todos after view is successfully created
                        let createdPage = null;

                        if (isConversationDMView(type)) {
                            followers.push(userid);
                        }
                        if (followers.length) {
                            const follower_ids = followers.map(follower => follower.id || follower);
                            await addFollowers(follower_ids, view_id, userid, team_id);
                        }

                        async.parallel(
                            {
                                bindAvatar(para_cb) {
                                    if (!avatar_source) {
                                        para_cb();
                                        return;
                                    }

                                    bindAttachmentsToParent(
                                        avatar_source,
                                        view_id,
                                        config.attachments.types.view,
                                        {
                                            hidden: true,
                                            workspaceId: team_id,
                                        },
                                        para_cb
                                    );
                                },
                                increaseMindMapLimit(para_cb) {
                                    if (type !== view_types.mind_map) {
                                        para_cb();
                                        return;
                                    }

                                    mindMapMod.increaseTeamLimit(team_id, para_cb);
                                },
                                invalidateDocCache(para_cb) {
                                    if (type === view_types.doc && parent_type !== parent_types.template) {
                                        invalidateCachedTeamDocCounts(team_id);
                                    }

                                    para_cb();
                                },
                                async createPage(para_cb) {
                                    if (
                                        type !== view_types.doc ||
                                        parent_type === parent_types.template ||
                                        !options.create_page
                                    ) {
                                        para_cb();
                                        return;
                                    }

                                    try {
                                        createdPage = await createPage(userid, view_id, {
                                            name: options.page_name || '',
                                            content: {},
                                        });
                                        para_cb();
                                    } catch (error) {
                                        para_cb(new ViewsError(error, 'VIEWS_102'));
                                    }
                                },
                            },
                            para_err => {
                                if (para_err) {
                                    cb(para_err);
                                    return;
                                }

                                _getViews(
                                    userid,
                                    {
                                        view_id,
                                        system_views: true,
                                        use_replica: !shouldUseMasterAfterViewCreation(),
                                        skipAccess: userid === config.get('clickbot_assignee'),
                                    },
                                    (getErr, getResult) => {
                                        if (getErr) {
                                            cb(getErr);
                                        } else {
                                            const result = { view: getResult.views[0] };

                                            if (options.returnCreatedPage && createdPage) {
                                                result.createdPage = createdPage;
                                            }

                                            cb(null, result);
                                            reportViewChanged(view_id);
                                            sqs.sendWSMessage('sendViewCreated', [
                                                view_id,
                                                parent.id,
                                                parent.type,
                                                options.ws_key,
                                            ]);
                                        }
                                    }
                                );
                            }
                        );
                    } catch (batchErr) {
                        // handle race condition for standard views
                        if (batchErr && standard && batchErr.constraint === 'views_pkey') {
                            _editView(userid, view_id, options, cb);
                            return;
                        }

                        if (batchErr) {
                            logger.error({
                                msg: 'Failed to create a view',
                                ECODE: 'VIEWS_100',
                                err: batchErr,
                            });
                            cb(new ViewsError('Failed to create a view', 'VIEWS_100'));
                        }
                    }
                }
            );
        }
    );
}
export { _createView };

export function createViewAsync(userid, options = {}) {
    return new Promise((res, rej) => {
        _createView(userid, options, (err, result) => {
            if (err) rej(err);
            else res(result);
        });
    });
}

function _editViewPosition(userid, view_id, options, cb) {
    const { next, sidebar_view } = options;
    let orderindex;
    let nextRowDateCreated;
    let otherViewsToUpdate;
    let groupedViewsType;
    // Helps with updating position and parent at the same time (dragging sidebar views).
    // Allows this to update order indexes under a different parent. Ideally this should be
    // refactored to be bundled like with subcategories, but doing this to have a minimal
    // change for now to avoid breaking things.
    let { parent } = options;
    const orderindexColumn = getOrderIndexColumnName(options);

    async.series(
        [
            async function checkAccess(series_cb) {
                const access_options = { permissions: [config.permission_constants.change_description] };
                const accessChecks = [];

                accessChecks.push(access.promiseCheckAccessView(userid, view_id, access_options));
                if (parent) {
                    accessChecks.push(
                        _promiseCheckParentAccess(userid, parent.id, parent.type, {
                            permissions: [config.permission_constants.create_view],
                        })
                    );
                }

                try {
                    const [result] = await Promise.all(accessChecks);
                    parent = parent || result.data;
                    series_cb();
                } catch (err) {
                    series_cb(new ViewsError(err, 'VIEWS_009'));
                }
            },

            async function getGroupedViewsType(series_cb) {
                if (!options.grouped_views) {
                    series_cb();
                    return;
                }
                const query = `SELECT type FROM task_mgmt.views WHERE view_id = $1`;
                const params = [view_id];
                try {
                    const { rows } = await db.replicaQueryAsync(query, params);
                    if (!rows.length) {
                        series_cb(new ViewsError('View not found', 'VIEWS_126'));
                        return;
                    }
                    groupedViewsType = rows[0].type;
                } catch (err) {
                    logger.warn({
                        msg: 'Cannot get view type',
                        err,
                    });
                    series_cb(new ViewsError('Cannot get view type', 'VIEWS_125'));
                    return;
                }
                series_cb();
            },

            function getOrderindexFromNext(series_cb) {
                if (!next) {
                    series_cb();
                    return;
                }

                const query = `
                    SELECT views.${orderindexColumn}, view_id, date_created
                    FROM task_mgmt.views
                    WHERE views.view_id = $1`;
                const params = [next];

                db.readQuery(query, params, (err, result) => {
                    if (err) {
                        series_cb(new ViewsError(err, 'VIEWS_012'));
                        return;
                    }

                    if (result.rows.length) {
                        const nextRow = result.rows[0];
                        nextRowDateCreated = nextRow.date_created;
                        orderindex = nextRow[orderindexColumn];
                    }

                    series_cb();
                });
            },

            async function getOtherViewsToUpdate(series_cb) {
                if (!options.grouped_views || orderindex !== null || !nextRowDateCreated) {
                    series_cb();
                    return;
                }

                const query = `
                    SELECT view_id
                    FROM task_mgmt.views
                    WHERE parent_id = $1 AND parent_type=$2 AND type=$3 AND grouped_orderindex IS NULL AND date_created < $4 AND view_id != $5
                    ORDER BY date_created;`;

                const params = [parent.id, parent.type, groupedViewsType, nextRowDateCreated, view_id];
                try {
                    const { rows } = await db.readQueryAsync(query, params);
                    otherViewsToUpdate = rows.map(row => row.view_id);
                } catch (err) {
                    series_cb(new ViewsError('Cannot get views to update viewGrouped index', 'VIEWS_124'));
                    return;
                }
                series_cb();
            },

            function getLastIfNoOrderindex(series_cb) {
                if (orderindex != null) {
                    series_cb();
                    return;
                }
                const params = [parent.id, parent.type];
                const viewTypeWhere = options.grouped_views ? `AND type = $${params.push(groupedViewsType)}` : '';

                const query = `
                    SELECT coalesce(max(${orderindexColumn}), 0) + 1 AS count
                    FROM task_mgmt.views
                    WHERE parent_id = $1
                        AND parent_type = $2
                        ${viewTypeWhere}`;

                db.readQuery(query, params, (err, result) => {
                    if (err) {
                        series_cb(new ViewsError(err, 'VIEWS_013'));
                        return;
                    }

                    if (result.rows.length) {
                        orderindex = result.rows[0].count;
                    } else {
                        orderindex = 0;
                    }

                    series_cb();
                });
            },

            function updatePosition(series_cb) {
                const sidebarViewsWhere = `AND sidebar_view IS ${sidebar_view ? '' : 'NOT'} TRUE`;
                const viewTypeWhere = options.grouped_views ? `AND type = ${groupedViewsType}` : '';
                const otherViewsToUpdateQueries = [];
                let orderindexOfTargetElement = orderindex;
                if (otherViewsToUpdate) {
                    orderindexOfTargetElement += otherViewsToUpdate.length;
                    for (let i = 0; i < otherViewsToUpdate.length; i++) {
                        otherViewsToUpdateQueries.push({
                            query: `UPDATE task_mgmt.views SET ${orderindexColumn} = $1 WHERE view_id = $2`,
                            params: [orderindex + i, otherViewsToUpdate[i]],
                        });
                    }
                }
                const queries = [
                    {
                        query: `UPDATE task_mgmt.views SET ${orderindexColumn} = $1 WHERE view_id = $2`,
                        params: [orderindexOfTargetElement, view_id],
                    },
                    {
                        query: `
                            UPDATE task_mgmt.views
                            SET ${orderindexColumn} = ${orderindexColumn} + 1
                            WHERE parent_id = $1
                                AND parent_type = $2
                                AND ${orderindexColumn} >= $3
                                AND view_id != $4
                                ${viewTypeWhere}
                                ${sidebarViewsWhere}`,
                        params: [parent.id, parent.type, orderindexOfTargetElement, view_id],
                    },
                    ...otherViewsToUpdateQueries,
                ];

                db.batchQueries(queries, err => {
                    if (err) {
                        series_cb(new ViewsError(err, 'VIEWS_010'));
                    } else {
                        series_cb();
                    }
                });
            },
        ],
        err => {
            if (err) {
                cb(new ViewsError(err, 'VIEWS_008'));
            } else {
                cb(null, {});
            }
        }
    );
}

export function editViewPosition(req, resp, next) {
    const { user: userid } = req.decoded_token;
    const { view_id } = req.params;
    const { next: nextView, sidebar_view, grouped_views, parent } = req.body;

    _editViewPosition(userid, view_id, { next: nextView, sidebar_view, parent, grouped_views }, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.json(result);
        }
    });
}

function _getDeletedViews(userid, team_id, options, cb) {
    access.checkAccessTeam(userid, team_id, { permission: [] }, teamAccessErr => {
        if (teamAccessErr) {
            cb(teamAccessErr);
            return;
        }

        db.readQuery(
            'SELECT view_id FROM task_mgmt.views WHERE team_id = $1 AND deleted = true',
            [team_id],
            (err, result) => {
                if (err) {
                    cb(new ViewsError(err, 500));
                } else {
                    const view_ids = result.rows.map(row => row.view_id);
                    const accessible_view_ids = [];
                    async.each(
                        view_ids,
                        (view_id, each_cb) => {
                            access.checkAccessView(userid, view_id, {}, viewAccessErr => {
                                if (!viewAccessErr) {
                                    accessible_view_ids.push(view_id);
                                }

                                each_cb();
                            });
                        },
                        () => {
                            _getViews(
                                userid,
                                {
                                    in_view_ids: accessible_view_ids,
                                    skipAccess: true,
                                    deleted: true,
                                    include_parent_info: true,
                                },
                                (viewsErr, viewsResult) => {
                                    if (viewsErr) {
                                        cb(viewsErr);
                                    } else {
                                        cb(null, { views: viewsResult.views });
                                    }
                                }
                            );
                        }
                    );
                }
            }
        );
    });
}

export function getDeletedViewsReq(req, resp, next) {
    const { user: userid } = req.decoded_token;
    const { team_id } = req.params;

    _getDeletedViews(userid, team_id, {}, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.json(result);
        }
    });
}

export async function getTeamConvosAndDocsReq(req, resp, next) {
    const { user: userid } = req.decoded_token;
    const { team_id } = req.params;
    const { project_id } = req.query;
    const include_archived = commonHelpers.toBooleanDefault(req.query.include_archived, false);

    const options = { project_id, include_archived };

    try {
        const result = await helpers.getTeamConvosAndDocs(userid, team_id, options);
        resp.json(result);
    } catch (err) {
        next(err);
    }
}

async function createMyTasksView(userId, teamId) {
    const redisKey = `VIEWS:MY_TASKS_WIDGET_SAVED:${userId}:${teamId}`;
    const redisCheck = await redis.setnx(redisKey, Date.now());

    if (redisCheck === 0) {
        throw new ViewsError('View exists but not available.', 'VIEWS_114');
    }

    // Throttle attempts to create view since this is lazy initialized
    await redis.expire(redisKey, 30);
    return createViewAsync(userId, {
        skip_privacy_check: true,
        my_tasks_widget: true,
        me_view: true,
        visibility: config.get('views.visibility.hidden'),
        parent: {
            id: teamId,
            type: config.get('views.parent_types.team_unparented'),
        },
        divide: {
            by_subcategory: false,
        },
        grouping: {
            field: 'status',
            dir: 1,
        },
        columns: {
            fields: [
                {
                    field: 'name',
                    hidden: false,
                },
                {
                    field: 'priority',
                    hidden: false,
                },
                {
                    field: 'dueDate',
                    hidden: false,
                },
            ],
        },
        sorting: {
            fields: [
                {
                    dir: -1,
                    field: 'dueDate',
                },
            ],
        },
        auto_save: true,
        name: 'My Tasks Widget',
        type: config.get('views.view_types.list'),
        members: [],
        group_members: [],
    });
}

async function getMyTasksView(userId, teamId) {
    let result;

    try {
        result = await getViewAsync(userId, getMyTasksWidgetViewId(userId, teamId));
    } catch (err) {
        // ACCESS_118 = View not found
        if (err.ECODE !== 'ACCESS_118') {
            throw err;
        }

        result = await createMyTasksView(userId, teamId);
    }

    return result;
}

export { getMyTasksView };

export async function getMyTasksViewReq(req, res, next) {
    if (!enableStaticViewsEndpoint()) {
        logger.warn('getMyTasksViewReq called but not enabled via split.');
        next();
        return;
    }

    const { user: userId } = req.decoded_token;
    try {
        const result = await getMyTasksView(userId, req.params.team_id);
        res.status(200).json(result);
    } catch (err) {
        next(err);
    }
}

export async function setViewParentSettingsReq(req, res, next) {
    const userId = req.decoded_token.user;
    const { settings, parent_type: parentType, parent_id: parentId } = req.body;

    if (typeof parentId !== 'number' || typeof parentType !== 'number') {
        next(new ViewsError('parent_id or parent_type is invalid', 'VIEWS_116', 400));
        return;
    }

    try {
        const updatedViewParentSettings = await updateViewParentSettings(
            userId,
            parentId,
            parentType,
            settings,
            Number(req.params.team_id)
        );
        res.send(updatedViewParentSettings);
    } catch (err) {
        next(err);
    }
}

export { getViewParentSettings as _getViewParentSettings };

function _formFieldRowToModel(row) {
    const dependencies = row.dependencies ?? [];
    const rules = row.rules ?? [];

    const baseField = {
        field: row.field,
        display_name: row.display,
        required: row.required,
        options: row.options,
        content: row.content,
        placeholder: row.placeholder,
        hidden: row.hidden || false,
        append_description: row.append_description || false,
    };

    if (dependencies.length === 0) {
        return {
            ...baseField,
            rules,
            rules_enabled: row.rules_enabled ?? false,
        };
    }

    return {
        ...baseField,
        dependencies,
    };
}
