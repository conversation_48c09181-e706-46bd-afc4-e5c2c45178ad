// The main generic view query is in this file.
// For an accurate history of this file, check out the file it was separated out at this commit:
// https://github.com/time-loop/clickup/blob/83d3f85176fb46bc31c2fbf4e8fd57ad0e75a05c/src/models/views/viewTaskIDFetch/index.js

import { isShadowHierarchyTaskType } from '@clickup/shadow-tasks';
import {
    genericViewQueryOptimizations,
    shouldUseTimlCte,
    shouldSortByStatusByType,
    shouldUseNotMaterializedOptimizationForPeopleCF,
    shouldInlinePeopleCFValuesCTE,
    shouldUseSubqueryForTimlHomeHierarchy,
} from '@clickup-legacy/models/integrations/split/squadTreatments/genericViewTreatments';
import * as db from '@clickup-legacy/utils/db';
import config from 'config';
import * as viewHelpers from '@clickup-legacy/models/views/helpers';
import { EntitlementName } from '@clickup-legacy/libs/entitlements/types';
import { isMyTasksViewId } from '@clickup-legacy/models/views/viewsUtils';
import { memoize, snakeCase, find, findIndex } from 'lodash';
import { FieldType } from '@clickup-legacy/models/field/interfaces/FieldType';
import {
    refactorViewsEgressQueries,
    shouldUseGenericViewSimlConditionForViewType,
    shouldCalculatePointsAndTimeRollupUsingRecursion,
} from '@clickup-legacy/models/integrations/split/squadTreatments/viewsTreatments';
import { getConditionForMultipleLists } from '@clickup-legacy/models/helpers_v2';
import moment from 'moment-timezone';
import * as rollupMod from '@clickup-legacy/models/views/rollup';
import { StopSeriesEvent } from '@clickup-legacy/models/views/viewTaskIDFetch/stop-series-event';
import * as async from '@clickup-legacy/utils/asyncHelper';
import { getAccessInfoCache } from '@clickup-legacy/utils/access/services/authorization/instances';
import * as access from '@clickup-legacy/utils/access2';
import * as reminderMod from '@clickup-legacy/models/inbox/reminders';
import * as userInitials from '@clickup-legacy/utils/userInitials';
import * as cf_sign from '@clickup-legacy/utils/cf_sign';
import * as reactionsMod from '@clickup-legacy/models/comment/commentReaction';
import * as attachmentMod from '@clickup-legacy/models/attachment/attachment';
import { ClickUpError } from '@clickup-legacy/utils/errors';
import { ClickUpTracer } from '@clickup-legacy/utils/tracer';
import { FormulaV2ConfigurationProvider } from '@clickup-legacy/models/field/formulas/services/formulaV2ConfigurationProvider';
import { FormulaReturnTypeMapper } from '@clickup-legacy/models/field/formulas/services/formulaReturnTypeMapper';
import { getFieldValueAndParentJoinCondition } from '@clickup-legacy/models/field/views/calculation/query-builders/fieldValueAndParentJoinQueryBuilder';
import { encrypt } from '@clickup-legacy/utils/encrypt';
import { isLimitedMemberEnabledAsync } from '@clickup-legacy/models/team/helpers/roleValidationHelper';
import { whereNotGuest } from '@clickup-legacy/utils/access/factories/where-not-guest.factory';
import { TaskAsObjectFilterFactory } from '@clickup-legacy/models/task_as_object/TaskAsObjectFilterFactory';
import { TaskAsObjectGenericViewQueryService } from '@clickup-legacy/models/task_as_object/TaskAsObjectGenericViewQueryService';
import { TaskAsObjectType } from '@clickup-legacy/models/task_as_object/TaskAsObjectType';
import { makeHierarchyScopeParameter } from '@clickup/utils/authorization';
import { ObjectType } from '@time-loop/ovm-object-version';
import { PagingError } from './error';
import { WidgetType } from '../../dashboard/widgets/interfaces';
import { buildFilterConditions } from './filters/filterConditions';
import { shouldUseGenericViewRecurringTasksOptimization } from '../../integrations/split/squadTreatments/workAnalyticsTreatments';
import { buildGenericViewCTEString, buildGenericViewNewCTEString } from './utils';
import { field_type_to_sql_type } from './helpers';
import { getPrivacyConditions } from './getPrivacyConditions';
import { getPropertyGroupings } from './groupings/propertyGroupings';

const logger = ClickUpError.getBasicLogger('paging');
const tracer = new ClickUpTracer();

const {
    field_types,
    views: { parent_types, view_types, show_subtasks_types },
} = config;
const { view: VIEW, pie_chart: PIE_CHART, bar_chart: BAR_CHART, battery_chart: BATTERY_CHART } = WidgetType;

const formulaFieldViewConfig = new FormulaV2ConfigurationProvider();
const formulaTypeMapper = new FormulaReturnTypeMapper();

function getGroupMemberLeftLateralJoins(limitedMemberEnabled) {
    return `
    LEFT JOIN LATERAL (
        SELECT
            task_group_members.task_id,
            MAX(permission_level) as permission_level
        FROM
            task_mgmt.group_members
            JOIN task_mgmt.task_group_members ON
                task_group_members.group_id = group_members.group_id AND
                task_group_members.task_id = COALESCE(items.parent, items.id)
        WHERE
            group_members.userid = $1
        GROUP BY
            task_group_members.task_id
    ) AS task_group_member ON task_group_member.task_id = COALESCE(items.parent, items.id)
    LEFT JOIN LATERAL (
        SELECT
            subcategory_group_members.subcategory,
            MAX(permission_level) as permission_level
        FROM
            task_mgmt.group_members
            JOIN task_mgmt.subcategory_group_members ON
                subcategory_group_members.group_id = group_members.group_id AND
                subcategory_group_members.subcategory = subcategories.id
        WHERE
            group_members.userid = $1
        GROUP BY
            subcategory_group_members.subcategory
    ) AS subcat_group_member ON subcat_group_member.subcategory = subcategories.id
    LEFT JOIN LATERAL (
        SELECT
            category_group_members.category,
            MAX(permission_level) as permission_level
        FROM
            task_mgmt.group_members
            JOIN task_mgmt.category_group_members ON
                category_group_members.group_id = group_members.group_id AND
                category_group_members.category = categories.id
        WHERE
            group_members.userid = $1
        GROUP BY
            category_group_members.category
    ) AS cat_group_member ON cat_group_member.category = categories.id
    LEFT JOIN LATERAL (
        SELECT
            project_group_members.project_id,
            MAX(permission_level) as permission_level
        FROM
            task_mgmt.group_members
            JOIN task_mgmt.project_group_members ON
                project_group_members.group_id = group_members.group_id AND
                project_group_members.project_id = projects.id
        WHERE
            group_members.userid = $1
            ${whereNotGuest(limitedMemberEnabled)}
        GROUP BY
            project_group_members.project_id
    ) AS project_group_member ON project_group_member.project_id = projects.id
`;
}

function getPrivacyJoins(limitedMemberEnabled, usePrivacyEgress) {
    if (usePrivacyEgress) {
        // The list_acls and task_acls are joined in the subcategory joins
        return '';
    }
    return `
    ${getGroupMemberLeftLateralJoins(limitedMemberEnabled)}
    LEFT JOIN task_mgmt.task_members
        ON coalesce(items.parent, items.id) = task_members.task_id
        AND task_members.userid = $1
    LEFT JOIN task_mgmt.subcategory_members
        ON subcategory_members.subcategory = subcategories.id
        AND subcategory_members.userid = $1
    LEFT JOIN task_mgmt.category_members
        ON category_members.category = categories.id
        AND category_members.userid = $1
    LEFT JOIN task_mgmt.project_members
        ON project_members.project_id = projects.id
        AND project_members.userid = $1
`;
}

const getPrivacySelect = (usePrivacyEgress, getGroupIdsParamNumber) => {
    if (usePrivacyEgress) {
        // For any given user or group, the list_acls will have the highest permission level from the hierarchy above.
        return `
            list_acls.workspace_id AS team_id,
            COALESCE(
                COALESCE((task_acls.user_acl->>$1)::int, (SELECT MAX(value::int) FROM jsonb_each(task_acls.group_acl) WHERE key = ANY($${getGroupIdsParamNumber()}))),
                COALESCE((list_acls.user_acl->>$1)::int, (SELECT MAX(value::int) FROM jsonb_each(list_acls.group_acl) WHERE key = ANY($${getGroupIdsParamNumber()}))),
                5
            ) AS permission_level,
        `;
    }
    return `
        team_members.team_id,
        coalesce(
            COALESCE(task_members.permission_level, task_group_member.permission_level),
            COALESCE(subcategory_members.permission_level, subcat_group_member.permission_level),
            COALESCE(category_members.permission_level, cat_group_member.permission_level),
            COALESCE(project_members.permission_level, project_group_member.permission_level),
            5
        ) AS permission_level,
    `;
};

const getPrivacyGroupByCols = usePrivacyEgress => {
    if (usePrivacyEgress) {
        return [
            `list_acls.workspace_id`,
            `task_acls.user_acl`,
            `task_acls.group_acl`,
            `list_acls.user_acl`,
            `list_acls.group_acl`,
        ];
    }
    return [
        `team_members.team_id`,
        `task_members.permission_level`,
        `subcategory_members.permission_level`,
        `category_members.permission_level`,
        `project_members.permission_level`,
        `task_group_member.permission_level`,
        `subcat_group_member.permission_level`,
        `cat_group_member.permission_level`,
        `project_group_member.permission_level`,
    ];
};

function getSubSortingJoins(tasksTableName, limitedMemberEnabled, useSubSortingEgress, workspaceIdParamNumber) {
    if (useSubSortingEgress) {
        return `
        INNER JOIN task_mgmt.get_hierarchy_object_acls('list', $${workspaceIdParamNumber}, NULL) list_acls
            ON list_acls.object_id_int = subcategories.id
        LEFT JOIN task_mgmt.get_hierarchy_object_acls('task', $${workspaceIdParamNumber}, NULL) task_acls
            ON task_acls.object_id = coalesce(tasks.parent, tasks.id)
        INNER JOIN task_mgmt.categories
            ON subcategories.category = categories.id
        INNER JOIN task_mgmt.projects
            ON categories.project_id = projects.id
    `;
    }

    return `
    INNER JOIN task_mgmt.categories
        ON subcategories.category = categories.id
    INNER JOIN task_mgmt.projects
        ON categories.project_id = projects.id
    INNER JOIN task_mgmt.team_members
        ON team_members.team_id = projects.team
        AND team_members.userid = $1
    LEFT JOIN task_mgmt.${tasksTableName} AS parent
        ON parent.id = tasks.parent
    LEFT JOIN task_mgmt.task_members
        ON coalesce(tasks.parent, tasks.id) = task_members.task_id
        AND task_members.userid = $1
    LEFT JOIN task_mgmt.subcategory_members
        ON subcategory_members.subcategory = subcategories.id
        AND subcategory_members.userid = $1
    LEFT JOIN task_mgmt.category_members
        ON category_members.category = categories.id
        AND category_members.userid = $1
    LEFT JOIN task_mgmt.project_members
        ON project_members.project_id = projects.id
        AND project_members.userid = $1
    ${getGroupMemberLeftLateralJoins(limitedMemberEnabled)}
`;
}

function getSubSortingConditions(useSubSortingEgress, getTaskListAclConditions) {
    let conditions = `
    AND tasks.deleted = FALSE
    AND tasks.archived = FALSE
    AND tasks.template = FALSE
    AND subcategories.deleted = FALSE
    AND subcategories.template = FALSE
    AND subcategories.archived = FALSE
    AND categories.deleted = FALSE
    AND categories.template = FALSE
    AND categories.archived = FALSE
    AND projects.deleted = FALSE
    AND projects.template = FALSE
    AND projects.archived = FALSE
    `;

    if (useSubSortingEgress) {
        const teamMembershipCondition = `
            AND EXISTS ( SELECT 1 FROM task_mgmt.team_members WHERE team_members.team_id = projects.team AND team_members.userid = $1 )
        `;
        conditions += `
            AND (
                ${getTaskListAclConditions()}
            )
            ${teamMembershipCondition}
        `;
    } else {
        conditions += `
    AND
    (
        (task_members.userid IS NOT NULL OR task_group_member.task_id IS NOT NULL)
        OR
        (
            (
                (
                    tasks.private = false
                    AND
                    tasks.parent IS NULL
                )
                OR
                parent.private = false
            )
            AND
            (
                (subcategory_members.userid IS NOT NULL OR subcat_group_member.subcategory IS NOT NULL)
                OR
                (
                    subcategories.private = false
                    AND
                    (
                        (category_members.userid IS NOT NULL OR cat_group_member.category IS NOT NULL)
                        OR
                        (
                            categories.private = false
                            AND
                            (
                                (project_members.userid IS NOT NULL OR project_group_member.project_id IS NOT NULL)
                                OR
                                (
                                    team_members.role != 4
                                    AND
                                    projects.private = false
                                )
                            )
                        )
                    )
                )
            )
        )
    )
`;
    }

    return conditions;
}

function formatTaskListAclConditions(
    userIdParamNumber,
    getGroupIdsParamNumber,
    getWorkspaceRoleParamNumber,
    tableAlias
) {
    // Normal access to a task via hierarchy or task permissions
    return `
        task_acls.user_acl ? $${userIdParamNumber}::text
        OR task_acls.group_acl ?| $${getGroupIdsParamNumber()}
        OR (
            COALESCE(task_acls.private, ${tableAlias}.private, FALSE) IS FALSE
            AND (
                (
                    list_acls.private IS NOT TRUE
                        AND $${getWorkspaceRoleParamNumber()} != 4
                )
                OR list_acls.user_acl ? $${userIdParamNumber}::text
                OR list_acls.group_acl ?| $${getGroupIdsParamNumber()}
            )
        )
    `;
}

function formatSharedTaskListAclConditions(
    userIdParamNumber,
    getGroupIdsParamNumber,
    getWorkspaceRoleParamNumber,
    tableAlias
) {
    // Access is only via task_acls and not list_acls
    return `
        task_acls.user_acl ? $${userIdParamNumber}::text
        OR task_acls.group_acl ?| $${getGroupIdsParamNumber()}
        AND NOT (
            COALESCE(task_acls.private, ${tableAlias}.private, FALSE) IS FALSE
            AND (
                (
                    list_acls.private IS NOT TRUE
                        AND $${getWorkspaceRoleParamNumber()} != 4
                )
                OR list_acls.user_acl ? $${userIdParamNumber}::text
                OR list_acls.group_acl ?| $${getGroupIdsParamNumber()}
            )
        )
    `;
}

function formatOrderColumn(name) {
    return `${name
        .replace(/\(/g, '_')
        .replace(/\)/g, '_')
        .replace(/->>/g, '_')
        .replace(/::/g, '_')
        .replace(/'/g, '_')
        .replace(/,/g, '_')
        .replace(/ /g, '_')
        .replace(/__/g, '_')
        .replace(/\./g, '_')
        .replace('.', '_')
        .replace(/\[/g, '')
        .replace(/]/g, '')
        .replace(/->/g, '')}_sort`;
}

function formatOrderColumnSelect(name) {
    return `${name} as ${formatOrderColumn(name)}`;
}

function getWidgetParams(
    userid,
    widget_calculation,
    calculation_field,
    filters,
    estimates_per_assignee,
    points_per_assignee,
    task_params,
    tasksTableName,
    fieldParentFilterFactory
) {
    const { field } = widget_calculation;
    let widget_select = '';
    let widget_join = '';
    const widget_group_by_cols = [];
    const unassignedFilterValue = 'unassigned';

    if (field === 'time_estimate') {
        widget_select = ', items.time_estimate AS widget_value';
        let assignee_filter;

        if (filters && filters.fields && filters.fields.length) {
            assignee_filter = filters.fields.find(
                field_ =>
                    field_.field === 'assignee' &&
                    field_.values.find(value => value !== unassignedFilterValue) !== undefined
            );
        }

        if (estimates_per_assignee) {
            if (assignee_filter) {
                const includeUnassigned =
                    assignee_filter.values.find(value => value === unassignedFilterValue) !== undefined;
                widget_select = includeUnassigned
                    ? `,
                        CASE
                        WHEN assignee_per_estimate.task_id IS NOT NULL
                            THEN assignee_per_estimate.time_estimate
                        WHEN NOT EXISTS (
                            SELECT 1
                            FROM task_mgmt.assignees
                            WHERE assignees.task_id = items.id

                            UNION

                            SELECT 1
                            FROM task_mgmt.group_assignees
                            WHERE group_assignees.task_id = items.id
                        )
                            THEN items.time_estimate
                        END AS widget_value`
                    : ', assignee_per_estimate.time_estimate AS widget_value';

                const assignedUserIds = assignee_filter.values
                    .filter(value => value !== unassignedFilterValue)
                    .map(value => (value && value.me ? userid : value));
                task_params.push(assignedUserIds);

                widget_join = `
                    LEFT JOIN (
                        SELECT task_id, sum(time_estimate) as time_estimate
                        FROM task_mgmt.assignees
                        WHERE assignees.userid = ANY($${task_params.length})
                        GROUP BY task_id
                    ) AS assignee_per_estimate
                        ON assignee_per_estimate.task_id = items.id`;

                widget_group_by_cols.push('widget_value');
            }
        }
    }

    if (field === 'task_count') {
        widget_select = ', items.id AS widget_value';
    }

    if (field === 'time_spent') {
        widget_select = `, widget_time_spent.sum AS widget_value`;
        widget_join = `
                LEFT JOIN LATERAL (
                    SELECT item_id, sum(time)
                    FROM task_mgmt.time_spent
                    WHERE item_id = items.id AND (deleted = FALSE OR deleted IS NULL)
                    GROUP BY item_id
                )
                    AS widget_time_spent
                    ON items.id = widget_time_spent.item_id`;
        widget_group_by_cols.push('widget_time_spent.sum');
    }

    if (field === 'points_estimate') {
        widget_select = ', items.points AS widget_value';

        if (filters && filters.fields && filters.fields.length && points_per_assignee) {
            const assignee_filter = filters.fields.find(
                field_ =>
                    field_.field === 'assignee' &&
                    field_.values.find(value => value !== unassignedFilterValue) !== undefined
            );
            if (assignee_filter) {
                const assignedUserIds = assignee_filter.values
                    .filter(value => value !== unassignedFilterValue)
                    .map(value => (value && value.me ? userid : value));
                task_params.push(assignedUserIds);

                const includeUnassigned =
                    assignee_filter.values.find(value => value === unassignedFilterValue) !== undefined;
                widget_select = includeUnassigned
                    ? `,
                        CASE
                        WHEN widget_points_estimate.task_id IS NOT NULL
                            THEN widget_points_estimate.sum
                        WHEN NOT EXISTS (
                            SELECT 1
                            FROM task_mgmt.assignees
                            WHERE assignees.task_id = items.id

                            UNION

                            SELECT 1
                            FROM task_mgmt.group_assignees
                            WHERE group_assignees.task_id = items.id
                        )
                            THEN items.points
                        END AS widget_value`
                    : ', widget_points_estimate.sum AS widget_value';
                widget_join = `
                        LEFT JOIN LATERAL (
                            SELECT task_id, sum(points_float)
                            FROM task_mgmt.assignees
                            WHERE task_id = items.id
                                AND userid = ANY($${task_params.length})
                            GROUP BY task_id
                        )
                            AS widget_points_estimate
                            ON items.id = widget_points_estimate.task_id`;
                widget_group_by_cols.push('widget_value');
            }
        }
    }

    if (field && field.includes('cf_')) {
        const { id: field_id, type } = calculation_field;

        task_params.push(field_id);
        widget_join = `
                LEFT JOIN task_mgmt.field_values AS widget_field_values
                    ON items.id = widget_field_values.item_id
                    AND widget_field_values.field_id = $${task_params.length}
                    ${getFieldValueAndParentJoinCondition(
                        fieldParentFilterFactory,
                        'widget_field_values',
                        task_params.length
                    )}`;

        if (Number(type) === config.field_types.manual_progress) {
            widget_select = `, coalesce((widget_field_values.value->>'value'), 0::text)::numeric AS widget_value`;
            widget_group_by_cols.push('widget_field_values.value');
        } else if (Number(type) === config.field_types.drop_down) {
            widget_select = `, (widget_dd_options.value->>'value')::numeric AS widget_value`;
            widget_join += `
                    LEFT JOIN task_mgmt.drop_down_options AS widget_dd_options
                        ON widget_dd_options.field_id = widget_field_values.field_id
                        AND widget_dd_options.id = (widget_field_values.value->>'value')::uuid`;
            widget_group_by_cols.push('widget_dd_options.value');
        } else if (Number(type) === config.field_types.automatic_progress) {
            const { type_config } = calculation_field;
            const resolved = [];
            const totals = [];

            widget_join = '';
            task_params.pop();

            if (type_config.tracking.assigned_comments) {
                widget_join += `
                        LEFT JOIN LATERAL (
                            SELECT
                                comments.task_id AS item_id,
                                count(comments.*) AS total_comments,
                                count(comments.*)
                                    FILTER (WHERE comments.resolved = TRUE) AS resolved_comments
                            FROM task_mgmt.comments
                            WHERE items.id = comments.task_id
                            GROUP BY comments.task_id
                        )
                            AS comment_progress
                            ON items.id = comment_progress.item_id`;

                resolved.push('coalesce(comment_progress.resolved_comments, 0)');
                totals.push('coalesce(comment_progress.total_comments, 0)');
            }

            if (type_config.tracking.subtasks) {
                widget_join += `
                        LEFT JOIN LATERAL (
                            SELECT
                                subtasks.parent AS item_id,
                                subtasks.subtask_parent AS subtask_item_id,
                                count(subtasks.*) AS total_subtasks,
                                count(subtasks.*)
                                    FILTER (WHERE statuses.type IN ('done', 'closed')) AS done_subtasks
                            FROM task_mgmt.${tasksTableName} AS subtasks
                            INNER JOIN task_mgmt.statuses
                                ON statuses.id = subtasks.status_id
                            WHERE ((subtasks.parent = items.id AND subtasks.subtask_parent IS NULL) OR (subtasks.parent = items.parent AND subtasks.subtask_parent = items.id))
                                AND subtasks.deleted = FALSE
                                ${type_config.tracking.archived_subtasks ? '' : 'AND subtasks.archived = FALSE'}
                                AND subtasks.template = FALSE
                            GROUP BY subtasks.parent, subtasks.subtask_parent
                        )
                            AS subtask_progress
                            ON items.id = subtask_progress.item_id OR items.id = subtask_progress.subtask_item_id`;

                resolved.push('coalesce(subtask_progress.done_subtasks, 0)');
                totals.push('coalesce(subtask_progress.total_subtasks, 0)');
            }

            if (type_config.tracking.checklists) {
                widget_join += `
                        LEFT JOIN LATERAL (
                            SELECT
                                checklists.task_id AS item_id,
                                count(checklist_items.*) AS total_checklist_items,
                                count(checklist_items.*)
                                    FILTER (WHERE checklist_items.resolved = TRUE) AS resolved_checklist_items
                            FROM task_mgmt.checklists
                            INNER JOIN task_mgmt.checklist_items
                                ON checklist_items.checklist_id = checklists.id
                                AND checklist_items.deleted = FALSE
                            WHERE checklists.task_id = items.id
                                AND checklists.deleted = FALSE
                            GROUP BY checklists.task_id
                        )
                            AS checklist_progress
                            ON items.id = checklist_progress.item_id`;

                resolved.push('coalesce(checklist_progress.resolved_checklist_items, 0)');
                totals.push('coalesce(checklist_progress.total_checklist_items, 0)');
            }

            widget_select = `
                    , ((${resolved.join(' + ')})::float /
                        (CASE
                            WHEN (${totals.join(' + ')}) = 0 THEN 1
                            ELSE (${totals.join(' + ')})
                        END)::float
                    ) AS widget_value`;
            widget_group_by_cols.push('widget_value');
        } else {
            widget_select = `, (widget_field_values.value->>'value')::numeric AS widget_value`;
            widget_group_by_cols.push('widget_field_values.value');
        }
    }
    return { widget_select, widget_join, widget_group_by_cols };
}

function getTargetFieldParams(
    target_fields,
    join_fields,
    cf_filter,
    box_scrum,
    scrum_field_access,
    scrum_field_id,
    map_color_field_id,
    task_params,
    tasksTableName,
    useIsNotBoolOptimization,
    usePeopleCFNotMaterializedOptimization,
    inlinePeopleCFValuesCTE,
    fieldParentFilterFactory
) {
    let custom_fields_join = '';
    let scrum_select = '';
    let color_select = '';
    let scrum_error = false;
    const tf_group_by_cols = [];
    const peopleCFCTEs = {};

    target_fields.forEach(field => {
        const { id: field_id } = field;

        if (!join_fields.includes(field_id) && (!cf_filter || cf_filter.id !== field_id)) {
            return;
        }

        const _field_id = field_id.replace(/-/g, '_');

        const table_name = `cf_${_field_id}`;
        const fieldIdParam = task_params.push(field_id);
        if (Number(field.type) !== field_types.list_relationship) {
            custom_fields_join += `
                    LEFT JOIN task_mgmt.field_values AS ${table_name}
                        ON ${table_name}.field_id = $${task_params.length}
                        AND ${table_name}.item_id = items.id
                        AND ${
                            useIsNotBoolOptimization
                                ? `${table_name}.deleted IS NOT TRUE`
                                : `(${table_name}.deleted = FALSE OR ${table_name}.deleted IS NULL)`
                        }
                        AND ${table_name}.value->>'value' IS NOT NULL
                        ${getFieldValueAndParentJoinCondition(fieldParentFilterFactory, table_name, fieldIdParam)}`;
        }

        if (box_scrum && scrum_field_access && scrum_field_id === field_id) {
            if (![field_types.drop_down, field_types.number].includes(Number(field.type))) {
                scrum_error = true;
                return;
            }

            scrum_select = `, (${table_name}.value->>'value')::numeric AS scrum_value`;
            tf_group_by_cols.push(`${table_name}.value`);
        }

        if (Number(field.type) === field_types.drop_down) {
            const dd_table_name = `dd_${_field_id}`;
            custom_fields_join += `
                LEFT JOIN task_mgmt.drop_down_options AS ${dd_table_name}
                    ON ${dd_table_name}.field_id = $${task_params.length}
                    AND ${dd_table_name}.id = (${table_name}.value->>'value')::uuid`;

            if (scrum_select) {
                tf_group_by_cols.push(`${dd_table_name}.value`);
                scrum_select = `, (${dd_table_name}.value->>'value')::numeric AS scrum_value`;
            }

            if (map_color_field_id === field_id) {
                tf_group_by_cols.push(`${dd_table_name}.color`);
                color_select = `, ${dd_table_name}.color AS cf_${_field_id}_color`;
            }
        }

        if (Number(field.type) === field_types.labels) {
            const ll_table_name = `ll_${_field_id}`;
            custom_fields_join += `
                    LEFT JOIN task_mgmt.label_options AS ${ll_table_name}
                        ON ${ll_table_name}.field_id = $${task_params.length}
                        AND ${ll_table_name}.id = (
                            ARRAY(SELECT jsonb_array_elements_text(${table_name}.value->'value'))
                        )[1]::uuid`;
        }

        // new task relationship join table
        if (Number(field.type) === field_types.tasks) {
            const tt_table_name = `tt_${_field_id}`;
            custom_fields_join += `
                    LEFT JOIN task_mgmt.${tasksTableName} AS ${tt_table_name}
                        ON ${table_name}.field_id = $${task_params.length}
                        AND ${tt_table_name}.id = (
                            ARRAY(SELECT jsonb_array_elements_text(${table_name}.value->'value'))
                        )[1]::text
                        AND ${tt_table_name}.deleted IS NOT TRUE`;
        }

        // list relationship join table: task_mgmt.linked_subcategory_tasks
        if (Number(field.type) === field_types.list_relationship) {
            const lr_join_name = `lr_join_${_field_id}`;
            const lr_table_name = `lr_${_field_id}`;
            // Transform the linked_subcategory_tasks table into a virtual field_values table.
            // The resulting field_values.value will be of the form {"value": ["task_id", "task_id"]}.
            // The bidirectional graph is stored under a single custom field and in only one direction,
            // so we need to union in the reverse links.
            custom_fields_join += `
                LEFT JOIN LATERAL (
                    SELECT ${lr_join_name}.task_id as item_id, ${lr_join_name}.field_id as field_id, json_object(ARRAY['value'], ARRAY[json_agg(${lr_join_name}.link_id)::text]) AS value, null::numeric as orderindex
                    FROM task_mgmt.linked_subcategory_tasks AS ${lr_join_name}
                    JOIN task_mgmt.${tasksTableName} AS ${lr_table_name}
                        ON ${lr_table_name}.id = ${lr_join_name}.link_id
                    WHERE ${lr_join_name}.field_id = $${task_params.length}
                    AND ${lr_table_name}.deleted IS NOT TRUE
                    AND ${lr_join_name}.task_id = items.id
                    GROUP BY ${lr_join_name}.field_id, ${lr_join_name}.task_id
                    UNION ALL
                    SELECT ${lr_join_name}.link_id as item_id, ${lr_join_name}.field_id as field_id, json_object(ARRAY['value'], ARRAY[json_agg(${lr_join_name}.task_id)::text]) AS value, null::numeric as orderindex
                    FROM task_mgmt.linked_subcategory_tasks AS ${lr_join_name}
                    JOIN task_mgmt.${tasksTableName} AS ${lr_table_name}
                        ON ${lr_table_name}.id = ${lr_join_name}.task_id
                    WHERE ${lr_join_name}.field_id = $${task_params.length}
                    AND ${lr_table_name}.deleted IS NOT TRUE
                    AND ${lr_join_name}.link_id = items.id
                    GROUP BY ${lr_join_name}.field_id, ${lr_join_name}.link_id
                ) ${table_name} ON TRUE`;
        }

        if (Number(field.type) === field_types.users) {
            const uuidRegexp = '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$';

            // Setting the _actor_ids CTE to `NOT MATERIALIZED` is a substantial performance boost but requires Postgres v12+ (local runs postgres v11 but QA, Staging, Prod run v13 at the time of writing)
            //  This Split FF can be removed once we've updated local Postgres to 13 to match other environments
            const notMaterializedActorsOptimization = usePeopleCFNotMaterializedOptimization ? 'NOT MATERIALIZED ' : '';

            // Setting the _values_list CTE to `NOT MATERIALIZED` prevents the db from materializing the full list of custom field values, which is slow to read against (CTE Scan vs Index Scan) and contains far more data than we need
            //   Note: Can't make this permanent until we've updated local Postgres to v12+
            const inlinePeopleCFValuesOptimization = inlinePeopleCFValuesCTE ? 'NOT MATERIALIZED ' : '';

            if (!peopleCFCTEs[table_name]) {
                peopleCFCTEs[table_name] = `
                    ${table_name}_values_list as ${inlinePeopleCFValuesOptimization}(
                        SELECT item_id, replace(text(jsonb_array_elements(value -> 'value')), '"', '') as el
                        FROM task_mgmt.field_values
                        WHERE field_id = $${task_params.length}
                    ),
                    ${table_name}_actor_ids as ${notMaterializedActorsOptimization}(
                        SELECT item_id, userid::text as actor_id 
                        FROM ${table_name}_values_list
                        LEFT JOIN task_mgmt.group_members
                        ON ${table_name}_values_list.el::uuid = group_members.group_id
                        WHERE ${table_name}_values_list.el ~ '${uuidRegexp}' 
                        UNION
                        SELECT item_id, el as actor_id
                        FROM ${table_name}_values_list
                        ORDER BY item_id, actor_id
                    )`;
            }

            const uu_table_name = `uu_${_field_id}`;
            custom_fields_join += `
                LEFT JOIN task_mgmt.users AS ${uu_table_name}
                ON ${table_name}.field_id = $${task_params.length}
                AND ${uu_table_name}.id = (
                    SELECT ${table_name}_cf_userid
                    FROM jsonb_array_elements(${table_name}.value->'value')
                    AS ${table_name}_cf_userid
                    WHERE jsonb_typeof(${table_name}_cf_userid) = 'number'
                    LIMIT 1)::int
            `;
        }

        if (Number(field.type) === field_types.attachment) {
            const aa_table_name = `aa_${_field_id}`;
            custom_fields_join += `
                    LEFT JOIN LATERAL (
                        SELECT count(*) AS attachment_count, item_id
                        FROM task_mgmt.attachments
                        INNER JOIN task_mgmt.field_values
                            ON attachments.parent_id::uuid = field_values.field_id
                            AND items.id = field_values.item_id
                            ${getFieldValueAndParentJoinCondition(
                                fieldParentFilterFactory,
                                'field_values',
                                fieldIdParam
                            )}
                        WHERE attachments.type = ${config.attachments.types.custom_field}
                            AND attachments.parent_id = $${task_params.length}::text
                            AND attachments.id = ANY(ARRAY(SELECT jsonb_array_elements_text(field_values.value->'value')))
                        GROUP BY item_id
                    )
                        AS ${aa_table_name}
                        ON ${aa_table_name}.item_id = items.id`;
        }
    });
    return { custom_fields_join, scrum_select, color_select, tf_group_by_cols, scrum_error, peopleCFCTEs };
}

function getMapSettingsParams(map_settings, target_field_ids) {
    let map_select = '';
    let map_conditions = '';
    const ms_group_by_cols = [];
    if (Array.isArray(map_settings.field_ids) && map_settings.field_ids.length) {
        map_settings.field_ids.forEach(field_id => {
            if (!target_field_ids.includes(field_id)) {
                return;
            }
            const _field_id = field_id.replace(/-/g, '_');
            const column = `cf_${_field_id}.value`;
            map_select += `,
                     ${column} AS map_cf_${_field_id}`;
            ms_group_by_cols.push(column);

            if (map_settings.sidebar_only) {
                map_conditions += ` AND ${column} IS NULL`;
            }
        });
    }
    return { map_select, map_conditions, ms_group_by_cols };
}

function getTeamSidebarParams(options, isBoxViewType, assigneeJoinConfig, task_params, tasksTableName) {
    let team_sidebar_select = '';
    let team_sidebar_join = '';
    let team_sidebar_filter = '';
    const box_assignee_cols = [];
    const ts_group_by_cols = [];

    if (options.assignees || options.unassigned_tasks) {
        let join_added = false;

        (options.assignees || []).forEach((assignee, idx) => {
            if (!join_added) {
                join_added = true;

                team_sidebar_join += `
                        LEFT JOIN LATERAL (
                            SELECT
                                tasks.id,
                                array_agg(assignees.userid) || array_agg(group_members.userid) AS userids
                            FROM task_mgmt.${tasksTableName} AS tasks
                            LEFT JOIN task_mgmt.assignees
                                ON assignees.task_id = tasks.id
                            LEFT JOIN task_mgmt.group_assignees
                                ON group_assignees.task_id = tasks.id
                            LEFT JOIN task_mgmt.group_members
                                ON group_members.group_id = group_assignees.group_id
                            WHERE tasks.id = items.id
                            GROUP BY tasks.id
                        )
                            AS ts_assignee
                            ON ts_assignee.id = items.id`;
            }

            task_params.push(assignee);

            if (isBoxViewType) {
                assigneeJoinConfig(null, true, null, {
                    array_joins: [`ts_assignee.userids`],
                    users: [assignee],
                });
            }

            if (options.assigned_comments) {
                const box_id = `t${idx}_comment.userids`;

                if (isBoxViewType) {
                    assigneeJoinConfig(null, true, null, { array_joins: [box_id] });
                }

                team_sidebar_join += `
                        LEFT JOIN LATERAL (
                            SELECT
                                comments.task_id AS id,
                                count(DISTINCT comments.id) AS comment_count,
                                array_agg(group_members.userid) || ARRAY[comments.assignee] AS userids
                            FROM task_mgmt.comments
                            LEFT JOIN task_mgmt.group_members
                                ON group_members.group_id = comments.group_assignee
                            WHERE comments.task_id = items.id
                                AND comments.deleted = false
                                AND (comments.resolved = FALSE OR comments.resolved IS NULL)
                                AND (
                                    comments.assignee = $${task_params.length}
                                    OR group_members.userid = $${task_params.length}
                                )
                            GROUP BY comments.task_id, comments.assignee
                        )
                            AS t${idx}_comment
                            ON t${idx}_comment.id = items.id`;

                ts_group_by_cols.push(`ts_assignee.userids`, `t${idx}_comment.comment_count`);
                box_assignee_cols.push(`(
                        ARRAY[$${task_params.length}::integer] <@ ts_assignee.userids
                        OR t${idx}_comment.comment_count > 0
                    )`);
            } else {
                ts_group_by_cols.push(`ts_assignee.userids`);
                box_assignee_cols.push(`ARRAY[$${task_params.length}::integer] <@ ts_assignee.userids`);
            }
        });

        if (options.unassigned_tasks) {
            const table_name = `t_unassigned`;
            const count_column = `${table_name}_count`;

            team_sidebar_select += `,
                    count(${table_name}.userid) AS ${count_column}`;

            team_sidebar_join += `
                    LEFT JOIN task_mgmt.assignees AS ${table_name}
                        ON ${table_name}.task_id = items.id`;

            if (isBoxViewType) {
                assigneeJoinConfig(null, true, null, { users: [null] });
            }

            if (options.assigned_comments) {
                const _table_name = `t_unassigned_comments`;
                const _count_column = `${_table_name}_count`;
                const box_id = `${_table_name}.userid`;

                if (isBoxViewType) {
                    assigneeJoinConfig(null, true, null, { joins: [box_id] });
                }

                team_sidebar_select += `,
                        count(${_table_name}) AS ${_count_column}`;

                team_sidebar_join += `
                        LEFT JOIN task_mgmt.comments AS ${_table_name}
                            ON ${_table_name}.task_id = items.id
                            AND ${_table_name}.assignee IS NOT NULL
                            AND ${_table_name}.deleted = false
                            AND (${_table_name}.resolved = FALSE OR ${_table_name} IS NULL)`;

                box_assignee_cols.push(`(count(${table_name}.userid) = 0 AND count(${_table_name}) = 0)`);
            } else {
                box_assignee_cols.push(`count(${table_name}.userid) = 0`);
            }
        }
    }

    if (options.group_assignees && options.box_team) {
        let group_join_added = false;

        (options.group_assignees || []).forEach((group_assignee, idx) => {
            if (!group_join_added) {
                group_join_added = true;

                team_sidebar_join += `
                        LEFT JOIN LATERAL (
                            SELECT
                                tasks.id,
                                array_agg(group_assignees.group_id) AS group_ids
                            FROM task_mgmt.${tasksTableName} AS tasks
                            LEFT JOIN task_mgmt.group_assignees
                                ON group_assignees.task_id = tasks.id
                            WHERE tasks.id = items.id
                            GROUP BY tasks.id
                        )
                            AS ts_group_assignee
                            ON ts_group_assignee.id = items.id`;
            }

            task_params.push(group_assignee);

            if (isBoxViewType) {
                assigneeJoinConfig(null, true, null, {
                    group_array_joins: [`ts_group_assignee.group_ids`],
                    groups: [group_assignee],
                });
            }

            if (options.assigned_comments) {
                const box_id = `tg${idx}_comment.group_ids`;

                if (isBoxViewType) {
                    assigneeJoinConfig(null, true, null, { group_array_joins: [box_id] });
                }

                team_sidebar_join += `
                        LEFT JOIN LATERAL (
                            SELECT
                                comments.task_id AS id,
                                count(DISTINCT comments.id) AS comment_count,
                                array_agg(groups.id) AS group_ids
                            FROM task_mgmt.comments
                            LEFT JOIN task_mgmt.groups
                                ON groups.id = comments.group_assignee
                            WHERE comments.task_id = items.id
                                AND comments.deleted = false
                                AND (comments.resolved = FALSE OR comments.resolved IS NULL)
                                AND (
                                    comments.group_assignee = $${task_params.length}
                                )
                            GROUP BY comments.task_id, comments.group_assignee
                        )
                            AS tg${idx}_comment
                            ON tg${idx}_comment.id = items.id`;

                ts_group_by_cols.push(`ts_group_assignee.group_ids`, `tg${idx}_comment.comment_count`);
                box_assignee_cols.push(`(
                        ARRAY[$${task_params.length}::uuid] <@ ts_group_assignee.group_ids
                        OR tg${idx}_comment.comment_count > 0
                    )`);
            } else {
                ts_group_by_cols.push(`ts_group_assignee.group_ids`);
                box_assignee_cols.push(`ARRAY[$${task_params.length}::uuid] <@ ts_group_assignee.group_ids`);
            }
        });
    }

    if (box_assignee_cols.length) {
        team_sidebar_filter = box_assignee_cols.join(' OR ');
    }

    return { team_sidebar_select, team_sidebar_join, team_sidebar_filter, ts_group_by_cols };
}

export default async function getIDs(
    options,
    userid,
    view,
    target_fields,
    estimates_per_assignee,
    points_per_assignee,
    calculation_field,
    join_fields,
    scrum_field_access,
    scrum_field_id,
    map_color_field_id,
    wip_limits,
    time_estimate_rollup,
    points_estimate_rollup,
    cf_filter_field,
    week_start_day,
    timezone,
    unstarted_status_group,
    filter_group_ids,
    division_field,
    division_dir,
    group_field,
    group_dir,
    group_cf,
    group_cf_id,
    group_by_single,
    team_id,
    filteredTaskIds,
    or_filter_present,
    fresh_req,
    gantt_output,
    subcategory_statuses,
    entitlements,
    rootSpan,
    userGroupLocations,
    fieldParentFilterFactory,
    splitTreatmentsToTrack,
    series_cb
) {
    const genericViewSimlConditionForViewTypeConfig = shouldUseGenericViewSimlConditionForViewType();
    const useGenericViewSimlConditionForViewType = genericViewSimlConditionForViewTypeConfig.viewTypes.includes(
        view.type
    );
    const calculatePointsAndTimeRollupUsingRecursionEnabled =
        shouldCalculatePointsAndTimeRollupUsingRecursion().viewTypes.includes(view.type);
    const useRecursiveCTEForTaskHierarchy =
        useGenericViewSimlConditionForViewType || calculatePointsAndTimeRollupUsingRecursionEnabled;
    const queryOptimizations = genericViewQueryOptimizations();
    const tasksTableName = queryOptimizations?.renameItemsToTasks ? 'tasks' : 'items';
    const useExtraWorkspaceIdCondition = queryOptimizations?.extraWorkspaceIdConditions && Boolean(team_id);
    const useExtraWorkspaceIdConditionForTIML =
        queryOptimizations?.extraWorkspaceIdConditionsForTIML && useExtraWorkspaceIdCondition;
    const useIsNotBoolOptimization = queryOptimizations?.useIsNotBoolOptimization;
    const useSubqueryForTimlHomeHierarchy = shouldUseSubqueryForTimlHomeHierarchy(team_id);
    const refactorViewsEgressQueriesConfig = refactorViewsEgressQueries(team_id);
    const useSubSortingEgress = refactorViewsEgressQueriesConfig?.viewTaskIdFetch?.getIdsSubSorting && Boolean(team_id);
    const usePrivacyEgress = refactorViewsEgressQueriesConfig?.viewTaskIdFetch?.getIdsPrivacy && Boolean(team_id);
    let encloseMainQueryInCte = false;

    splitTreatmentsToTrack.usePrivacyEgress = usePrivacyEgress;
    splitTreatmentsToTrack.useSubqueryForTimlHomeHierarchy = useSubqueryForTimlHomeHierarchy;

    const sortByStatusByType = shouldSortByStatusByType(team_id);

    const query_func = db.longReplicaQuery; // just always use the replica.

    if (refactorViewsEgressQueriesConfig?.viewTaskIdFetch?.getIdsSubSorting && !team_id) {
        // This should never happen. team_id should always be present in support of egress queries.
        logger.debug('egress query is enabled but team_id is not set, skipping egress query');
    }

    const task_params = [];
    const userIdParamNumber = task_params.push(userid);

    let workspaceIdParamNumber;
    if (useExtraWorkspaceIdCondition || useSubSortingEgress || usePrivacyEgress) {
        workspaceIdParamNumber = task_params.push(team_id);
    }

    let groupIds = [];
    let workspaceRole = 0;
    if (useSubSortingEgress) {
        ({ groupIds, workspaceRole } = await getAccessInfoCache().getUserWorkspaceAccessInfo(userid, team_id));
    }

    // Query params must only be set if used, so they are added just in time and memoized.
    const getGroupIdsParamNumber = memoize(() => task_params.push(groupIds));

    const getWorkspaceRoleParamNumber = memoize(() => task_params.push(workspaceRole));

    const getTaskListAclConditions = memoize(() =>
        formatTaskListAclConditions(userIdParamNumber, getGroupIdsParamNumber, getWorkspaceRoleParamNumber, 'tasks')
    );

    const getItemsTaskListAclConditions = memoize(() =>
        formatTaskListAclConditions(userIdParamNumber, getGroupIdsParamNumber, getWorkspaceRoleParamNumber, 'items')
    );

    const getSharedTaskListAclConditions = memoize(() =>
        formatSharedTaskListAclConditions(
            userIdParamNumber,
            getGroupIdsParamNumber,
            getWorkspaceRoleParamNumber,
            'items'
        )
    );

    const usePeopleCFNotMaterializedOptimization = shouldUseNotMaterializedOptimizationForPeopleCF(team_id);
    const inlinePeopleCFValuesCTE = shouldInlinePeopleCFValuesCTE(team_id);

    const {
        parent,
        sorting = { fields: [] },
        filters = { fields: [] },
        columns = { fields: [] },
        settings,
        tasks_shared_with_me,
        shared_with_me,
        no_date,
        overdue,
        no_effort,
        recurring_v2,
        box_settings = {},
        map_settings = {},
        calendar_settings = {},
        widget,
    } = view;
    const filters_bool_operator = filters?.op === 'AND' ? ' AND ' : ' OR ';
    const { any_date_lt, any_date_gt } = view;
    const map_tasks = {};

    if (settings.override_parent_hierarchy_filter) {
        parent.type = parent_types.team;
        parent.id = team_id;
    }

    const target_field_ids = target_fields.map(field => field.id);

    const group_by_cols = [
        `items.id`,
        `items.name`,
        `items.date_created`,
        `items.date_updated`,
        `items.date_done`,
        `items.date_closed`,
        `items.priority`,
        `items.subcategory`,
        `task_subcategories.subcategory`,
        `subcategories.id`,
        'subcategories.color',
        `items.due_date`,
        `items.custom_type`,
        `items.start_date`,
        `items.time_estimate`,
        'items.parent',
        `items.points`,
        `items.subtask_parent`,
        `items.duration`,
        `items.duration_is_elapsed`,
        `statuses.status`,
        `statuses.orderindex`,
        `statuses.type`,
        `statuses.color`,
        `statuses.id`,
        `projects.priorities`,
        `timl_home_hier.priorities`,
        `timl_home_hier.tags`,
        `projects.tags`,
        ...getPrivacyGroupByCols(usePrivacyEgress),
    ];

    const box_time_estimate = Boolean(
        box_settings.show_time_estimate ||
            (box_settings.workload && box_settings.workload.field === 'timeEstimate') ||
            (box_settings.sort && box_settings.sort.field === 'timeEstimate')
    );

    const box_points_estimate = Boolean(
        box_settings.show_points_estimate ||
            (box_settings.workload && box_settings.workload.field === 'pointsEstimate') ||
            (box_settings.sort && box_settings.sort.field === 'pointsEstimate')
    );

    const box_scrum = Boolean(
        box_settings.show_scrum_points ||
            (box_settings.workload && box_settings.workload.field === 'scrum') ||
            (box_settings.sort && box_settings.sort.field === 'scrum')
    );

    const { assigneeJoinConfig, getAssigneeJoins } = viewHelpers.getAssigneeJoinsMaker();

    const calculations = entitlements[EntitlementName.CalculationColumns]
        ? viewHelpers.parseCalculations(columns, target_fields)
        : [];

    const should_show_personal_list_tasks = isMyTasksViewId(view?.id, userid, team_id) && view.me_view;
    let parent_condition = ``;
    let recursiveCTEParentJoin = '';

    if (usePrivacyEgress) {
        recursiveCTEParentJoin = `
            INNER JOIN task_mgmt.get_hierarchy_object_acls('list', $${workspaceIdParamNumber}, NULL) list_acls
                ON list_acls.object_id_int = task_subcategories.subcategory
        `;

        if ([parent_types.team, parent_types.widget, parent_types.template].includes(parent.type)) {
            // No specific parent_condition is needed to scope the list_acls to the workspace.
            parent_condition = ` 1 = 1`;

            if (!should_show_personal_list_tasks) {
                parent_condition += ` AND projects.personal_list IS NOT TRUE`;

                // These joins are only needed to make parent_condition on projects work.
                recursiveCTEParentJoin += `
                    INNER JOIN task_mgmt.subcategories
                        ON subcategories.id = task_subcategories.subcategory
                    INNER JOIN task_mgmt.categories
                        ON subcategories.category = categories.id
                    INNER JOIN task_mgmt.projects
                        ON categories.project_id = projects.id
                `;
            }
        } else if (parent.type === parent_types.shared) {
            // No specific parent_condition is needed to scope the list_acls to the workspace.
            parent_condition = ` 1 = 1`;
        } else if (parent.type === config.views.parent_types.project) {
            task_params.push(
                makeHierarchyScopeParameter({ object_type: ObjectType.SPACE, object_id: String(parent.id) })
            );
            parent_condition = ` list_acls.hierarchy_scopes @> $${task_params.length}::jsonb`;
        } else if (parent.type === config.views.parent_types.category) {
            task_params.push(
                makeHierarchyScopeParameter({ object_type: ObjectType.FOLDER, object_id: String(parent.id) })
            );
            parent_condition = ` list_acls.hierarchy_scopes @> $${task_params.length}::jsonb`;
        } else if (parent.type === config.views.parent_types.subcategory) {
            task_params.push(
                makeHierarchyScopeParameter({ object_type: ObjectType.LIST, object_id: String(parent.id) })
            );
            parent_condition = ` list_acls.hierarchy_scopes @> $${task_params.length}::jsonb`;
        } else {
            series_cb(new PagingError('Missing parent info', 'PAGE_002', 400));
            return;
        }
    } else {
        recursiveCTEParentJoin = `
            INNER JOIN task_mgmt.subcategories
                ON subcategories.id = task_subcategories.subcategory
            INNER JOIN task_mgmt.categories
                ON subcategories.category = categories.id
            INNER JOIN task_mgmt.projects
                ON categories.project_id = projects.id
        `;

        if ([parent_types.team, parent_types.widget, parent_types.template].includes(parent.type)) {
            task_params.push(parent.id);
            parent_condition = should_show_personal_list_tasks
                ? `projects.team = $${task_params.length}`
                : `
                projects.team = $${task_params.length} AND
                projects.personal_list IS NOT TRUE
            `;
        } else if (parent.type === parent_types.shared) {
            task_params.push(parent.id);
            parent_condition = `projects.team = $${task_params.length}`;
        } else if (parent.type === config.views.parent_types.project) {
            task_params.push(parent.id);
            parent_condition = ` projects.id = $${task_params.length}`;
        } else if (parent.type === config.views.parent_types.category) {
            task_params.push(parent.id);
            parent_condition = ` categories.id = $${task_params.length}`;
            recursiveCTEParentJoin = `
                INNER JOIN task_mgmt.subcategories
                    ON subcategories.id = task_subcategories.subcategory
                INNER JOIN task_mgmt.categories
                    ON subcategories.category = categories.id
            `;
        } else if (parent.type === config.views.parent_types.subcategory) {
            task_params.push(parent.id);
            parent_condition = ` subcategories.id = $${task_params.length}`;
            recursiveCTEParentJoin = `
                INNER JOIN task_mgmt.subcategories
                    ON subcategories.id = task_subcategories.subcategory
            `;
        } else {
            series_cb(new PagingError('Missing parent info', 'PAGE_002', 400));
            return;
        }
    }

    let limitedMemberEnabled = false;
    try {
        limitedMemberEnabled = await isLimitedMemberEnabledAsync(team_id);
    } catch (err) {
        series_cb(new PagingError(err, 'PAGE_006'));
        return;
    }

    let widget_select = '';
    let widget_join = '';

    if (options.widget_calculation) {
        let widget_group_by_cols = [];
        ({ widget_select, widget_join, widget_group_by_cols } = getWidgetParams(
            userid,
            options.widget_calculation,
            calculation_field,
            filters,
            estimates_per_assignee,
            points_per_assignee,
            task_params,
            tasksTableName,
            fieldParentFilterFactory
        ));
        group_by_cols.push(...widget_group_by_cols);
    }

    const { custom_fields_join, scrum_select, color_select, tf_group_by_cols, scrum_error, peopleCFCTEs } =
        getTargetFieldParams(
            target_fields,
            join_fields,
            view.cf_filter,
            box_scrum,
            scrum_field_access,
            scrum_field_id,
            map_color_field_id,
            task_params,
            tasksTableName,
            useIsNotBoolOptimization,
            usePeopleCFNotMaterializedOptimization,
            inlinePeopleCFValuesCTE,
            fieldParentFilterFactory
        );
    if (scrum_error) {
        series_cb(new PagingError('Invalid scrum field type', 'PAGE_046', 400));
        return;
    }
    group_by_cols.push(...tf_group_by_cols);

    const { map_select, map_conditions, ms_group_by_cols } = getMapSettingsParams(map_settings, target_field_ids);
    group_by_cols.push(...ms_group_by_cols);

    const { team_sidebar_select, team_sidebar_join, team_sidebar_filter, ts_group_by_cols } = getTeamSidebarParams(
        options,
        view.type === view_types.box,
        assigneeJoinConfig,
        task_params,
        tasksTableName
    );
    group_by_cols.push(...ts_group_by_cols);

    let closed_filter = '';

    let time_spent_join = '';
    let time_spent_from = '';

    let created_by_join = '';
    let created_by_select = '';

    let time_spent_rollup_join = '';
    let time_spent_rollup_join_timl_or_siml = '';
    let time_spent_rollup_from = '';

    let comment_count_join = '';
    let comment_count_from = '';

    let incomplete_comment_join = '';
    let incomplete_comment_from = '';

    let time_estimate_rollup_join = '';
    let time_estimate_rollup_join_timl_or_siml = '';
    let time_estimate_rollup_from = '';

    let points_estimate_rollup_join = '';
    let points_estimate_rollup_join_timl_or_siml = '';
    let points_estimate_rollup_from = '';

    let cf_sort_join = '';
    let cf_sort_select = '';

    let top_level_cte_join = '';
    let top_level_cte_select = '';
    let top_level_cte_order_column_names = [];

    let points_assignees_join = '';

    let date_of_last_status_change_join = '';

    let recurring_v2_join = '';

    let time_in_status_join = '';
    let time_in_status_from = '';

    let cf_effort_join = '';

    let date_cf_join = '';

    let gantt_links_join = '';
    let gantt_links_select = '';

    const status_filter_names = ['statusType', 'isClosed', 'dateClosed', 'status'];

    if (Number(view.type) === view_types.gantt && gantt_output) {
        gantt_links_join = `
            LEFT JOIN LATERAL (
                SELECT task_dependencies.task_id AS item_id, array_agg(task_dependencies.depends_on) AS depends_on
                FROM task_mgmt.task_dependencies
                WHERE task_dependencies.task_id = items.id
                GROUP BY task_dependencies.task_id
            ) 
                AS gantt_links
                ON gantt_links.item_id = items.id`;
        gantt_links_select = `, gantt_links.depends_on`;

        group_by_cols.push('gantt_links.depends_on');
    }

    if ((box_time_estimate || (wip_limits && view.type === view_types.board)) && time_estimate_rollup) {
        time_estimate_rollup_join = `
                    LEFT JOIN LATERAL (
                        SELECT items.id as item_id, sum(time_estimate)
                        FROM task_mgmt.${tasksTableName} as estimates
                        WHERE (estimates.id = items.id OR estimates.parent = items.id)
                        AND estimates.deleted = false AND estimates.template = false
                        GROUP BY coalesce(estimates.parent, estimates.id)
                    )
                        AS time_estimate_rollup
                        ON items.id = time_estimate_rollup.item_id`;
        time_estimate_rollup_from = ', time_estimate_rollup.sum AS time_estimate_rollup';
        group_by_cols.push('time_estimate_rollup.sum');
    }

    if ((box_points_estimate || (wip_limits && view.type === view_types.board)) && points_estimate_rollup) {
        points_estimate_rollup_join = `
                    LEFT JOIN LATERAL (
                        SELECT items.id as item_id, sum(points)
                        FROM task_mgmt.${tasksTableName} as estimates
                        WHERE (estimates.id = items.id OR estimates.parent = items.id)
                        AND estimates.deleted = false AND estimates.template = false
                        GROUP BY coalesce(estimates.parent, estimates.id)
                    )
                        AS points_estimate_rollup
                        ON items.id = points_estimate_rollup.item_id`;
        points_estimate_rollup_from = ', points_estimate_rollup.sum AS points_estimate_rollup';
        group_by_cols.push('points_estimate_rollup.sum');
    }

    // list view always filters out closed unless there is a status filter
    // board view does not filter out closed when group is status, otherwise follows the filter rule above
    const status_filter_exists = filters.fields.some(({ field }) => status_filter_names.includes(field));
    const board_view_show_closed = view.type === config.views.view_types.board && group_field === 'status';

    /**
     * publicViewAccessClosedSubtasks only need for accessing to public view closed subtasks
     * we need to skip closed statuses filtering for tasks/subtasks that user opened on public view
     * since we rely on the fact that if they are visible, then user have access to them
     * if user don't have access to them, it should be filtered in other place of query
     */
    const publicViewAccessClosedSubtasks =
        options.isPublic &&
        options.publicTask &&
        (view.type === view_types.list ||
            view.type === view_types.gantt ||
            view.type === view_types.timeline ||
            view.type === view_types.table);
    if (
        !status_filter_exists &&
        !board_view_show_closed &&
        !filters.show_closed &&
        !publicViewAccessClosedSubtasks &&
        view.type !== view_types.box
    ) {
        task_params.push(config.closed_status_types);
        closed_filter = `AND statuses.type != ALL ($${task_params.length})`;
    }

    let cf_filter_join = '';
    if (view.cf_filter && view.cf_filter.id && cf_filter_field) {
        const { cf_filter } = view;

        const custom_field = cf_filter_field;
        const value_column = `cf_filter.value->>'value'`;

        const is_text =
            [field_types.text, field_types.url, field_types.phone, field_types.email, field_types.short_text].includes(
                Number(custom_field.type)
            ) || ['text', 'url', 'phone', 'email'].includes(custom_field.type);

        const is_array =
            [field_types.labels, field_types.users, field_types.tasks].includes(Number(custom_field.type)) ||
            ['labels', 'users', 'tasks'].includes(custom_field.type);

        const is_numeric =
            [field_types.currency, field_types.emoji, field_types.number].includes(Number(custom_field.type)) ||
            ['currency', 'emoji', 'number'].includes(custom_field.type);

        const is_bool = field_types.checkbox === Number(custom_field.type) || custom_field.type === 'checkbox';

        let value;

        if (is_text) {
            value = `%${cf_filter.values}%`;
        } else if (is_array) {
            value = cf_filter.values.map(String);
        } else if (is_bool) {
            value = true;
        } else {
            value = cf_filter.values;
        }

        task_params.push(cf_filter.id, value);
        cf_filter_join = `
                    JOIN task_mgmt.field_values as cf_filter ON items.id = cf_filter.item_id AND cf_filter.field_id = $${
                        task_params.length - 1
                    }                             
                    ${getFieldValueAndParentJoinCondition(
                        fieldParentFilterFactory,
                        'cf_filter',
                        task_params.length - 1
                    )}
                    AND
                `;

        // TODO: Support App Fields filtering. See CLK-539584.

        const sql_type =
            field_type_to_sql_type[custom_field.type] || field_type_to_sql_type[field_types[custom_field.type]];

        if (is_text) {
            cf_filter_join += `(${value_column})::${sql_type} ILIKE $${task_params.length}`;
        } else if (is_numeric) {
            cf_filter_join += `(${value_column})::${sql_type} = ANY($${task_params.length})`;
        } else if (is_array) {
            cf_filter_join += `
                    ARRAY(SELECT jsonb_array_elements_text(cf_filter.value->'value'))
                    && $${task_params.length}`;
        } else {
            cf_filter_join += `(${value_column})::${sql_type} = ANY($${task_params.length})`;
        }
    }

    let archived_filter = options.include_archived
        ? ''
        : `AND items.archived = FALSE AND ${
              useIsNotBoolOptimization
                  ? 'parent.archived IS NOT TRUE'
                  : '(parent.archived = FALSE OR parent.archived IS NULL)'
          }`;

    let list_relationship_linked_tasks_select = '';
    let list_relationship_linked_tasks_group_by = '';
    let has_non_strict_list_relationship_filter = false;
    let list_relationship_filter_op_by_field_id = {};

    let filter_joins = '';
    let filter_conditions = '';
    let recurring_join = '';

    let location_filters = {
        subcategories: [],
        categories: [],
        projects: [],
        sprint_categories: [],
    };
    let location_filters_op = null;

    // Overrides from filters
    let override_archived_filter = null;
    let override_closed_filter = null;
    let override_time_estimate_rollup_join = null;
    let override_time_estimate_rollup_join_timl_or_siml = null;
    let override_time_spent_join = null;
    let override_time_spent_rollup_join = null;
    let override_time_spent_rollup_join_timl_or_siml = null;
    let override_points_assignees_join = null;
    let override_points_estimate_rollup_join = null;
    let override_points_estimate_rollup_join_timl_or_siml = null;
    let override_date_of_last_status_change_join = null;
    ({
        filter_conditions,
        filter_joins,
        location_filters,
        location_filters_op,
        has_non_strict_list_relationship_filter,
        list_relationship_linked_tasks_select,
        list_relationship_linked_tasks_group_by,
        list_relationship_filter_op_by_field_id,
        time_estimate_rollup,
        override_archived_filter,
        override_closed_filter,
        override_time_estimate_rollup_join,
        override_time_estimate_rollup_join_timl_or_siml,
        override_time_spent_join,
        override_time_spent_rollup_join,
        override_time_spent_rollup_join_timl_or_siml,
        override_points_assignees_join,
        override_points_estimate_rollup_join,
        override_points_estimate_rollup_join_timl_or_siml,
        override_date_of_last_status_change_join,
        recurring_join,
    } = buildFilterConditions({
        userid,
        team_id,
        options,
        view,
        task_params,
        target_fields,
        assigneeJoinConfig,
        filter_group_ids,
        points_per_assignee,
        splitTreatmentsToTrack,
        userGroupLocations,
        timezone,
        week_start_day,
        time_estimate_rollup,
        unstarted_status_group,
    }));

    // Apply overrides from filters.
    if (override_archived_filter !== null) {
        archived_filter = override_archived_filter;
    }
    if (override_closed_filter !== null) {
        closed_filter = override_closed_filter;
    }
    if (override_time_estimate_rollup_join !== null) {
        time_estimate_rollup_join = override_time_estimate_rollup_join;
    }
    if (override_time_estimate_rollup_join_timl_or_siml !== null) {
        time_estimate_rollup_join_timl_or_siml = override_time_estimate_rollup_join_timl_or_siml;
    }
    if (override_time_spent_join !== null) {
        time_spent_join = override_time_spent_join;
    }
    if (override_time_spent_rollup_join !== null) {
        time_spent_rollup_join = override_time_spent_rollup_join;
    }
    if (override_time_spent_rollup_join_timl_or_siml !== null) {
        time_spent_rollup_join_timl_or_siml = override_time_spent_rollup_join_timl_or_siml;
    }
    if (override_points_assignees_join !== null) {
        points_assignees_join = override_points_assignees_join;
    }
    if (override_points_estimate_rollup_join !== null) {
        points_estimate_rollup_join = override_points_estimate_rollup_join;
    }
    if (override_points_estimate_rollup_join_timl_or_siml !== null) {
        points_estimate_rollup_join_timl_or_siml = override_points_estimate_rollup_join_timl_or_siml;
    }
    if (override_date_of_last_status_change_join !== null) {
        date_of_last_status_change_join = override_date_of_last_status_change_join;
    }

    const shouldUseRecurringTasksOptimization = shouldUseGenericViewRecurringTasksOptimization(team_id);
    if (recurring_v2) {
        if (shouldUseRecurringTasksOptimization) {
            recurring_v2_join = `
                JOIN task_mgmt.task_recur_settings ON
                    items.id = task_recur_settings.last_task AND
                    task_recur_settings.settings_id IS NOT NULL
                    AND task_recur_settings.recur_next >= $${task_params.push(new Date().getTime())}
                    AND task_recur_settings.recur_next <= $${task_params.push(any_date_lt)}
                    AND task_recur_settings.workspace_id = $${task_params.push(team_id)}
            `;
        } else {
            recurring_v2_join = `
                JOIN task_mgmt.task_recur_settings ON
                    items.id = task_recur_settings.last_task AND
                    task_recur_settings.settings_id IS NOT NULL
            `;
        }
    }

    // TODO: Remove this log after the issue has been resolved
    // log 'NaN' values passed to any_date_gt and any_date_lt for a potential fix
    if (any_date_gt && any_date_lt && (Number.isNaN(Number(any_date_gt)) || Number.isNaN(Number(any_date_lt)))) {
        logger.warn({
            msg: 'NaN values passed to genericViews as date ranges',
            view_obj: JSON.stringify({
                view_type: view.type,
                any_date_lt: view.any_date_lt,
                any_date_gt: view.any_date_gt,
            }),
            view_task_params: JSON.stringify(task_params),
        });
    }
    const addDueDateFilter = !recurring_v2 || !shouldUseRecurringTasksOptimization;
    if (
        any_date_gt &&
        any_date_lt &&
        !Number.isNaN(Number(any_date_gt)) &&
        !Number.isNaN(Number(any_date_lt)) &&
        addDueDateFilter
    ) {
        if (!calendar_settings.disableStartAndDueDates || calendar_settings.customFieldDates) {
            // For workload view, if the start of the week is Monday, we need to adjust the start and end date ranges
            //      to account for timezone differences
            if (Number(view.type) === view_types.workload && week_start_day === 1) {
                const adjustedGt = moment(+any_date_gt)
                    .subtract(1, 'days')
                    .valueOf();
                const adjustedLt = moment(+any_date_lt)
                    .add(1, 'days')
                    .valueOf();
                task_params.push(adjustedGt.toString(), adjustedLt.toString());
            } else {
                task_params.push(any_date_gt, any_date_lt);
            }
        }

        if (!calendar_settings.disableStartAndDueDates) {
            filter_conditions += ` AND
                    (
                        (
                            items.due_date >= $${task_params.length - 1}
                            AND
                            items.due_date <= $${task_params.length}
                        )
                        OR
                        (
                            items.start_date >= $${task_params.length - 1}
                            AND
                            items.start_date <= $${task_params.length}
                        )
                        OR
                        (
                            items.start_date <= $${task_params.length - 1}
                            AND
                            items.due_date >= $${task_params.length}
                        )
                    `;
        }

        if (calendar_settings.customFieldDates) {
            // loop through all the cfs provided
            // and add filter params and joins for them

            if (calendar_settings.disableStartAndDueDates) {
                filter_conditions += ` AND ( `;
            }

            calendar_settings.customFieldDates.forEach((date_cf, index) => {
                const join_name = `date_cf_join_${index}`;
                task_params.push(date_cf);

                if (calendar_settings.disableStartAndDueDates && index === 0) {
                    // do nothing
                } else {
                    filter_conditions += ` OR`;
                }

                filter_conditions += `
                            (
                                (${join_name}.value->>'value')::bigint >= $${task_params.length - (index + 2)}
                                AND
                                (${join_name}.value->>'value')::bigint <= $${task_params.length - (index + 1)}
                            )
                    `;

                date_cf_join += `
                       LEFT JOIN task_mgmt.field_values AS  ${join_name} ON
                            items.id = ${join_name}.item_id AND ${join_name}.field_id = $${task_params.length}
                            ${getFieldValueAndParentJoinCondition(
                                fieldParentFilterFactory,
                                join_name,
                                task_params.length
                            )}
                    `;
            });
        }

        // if any of these options are provided close the
        // filter conditions
        if (!calendar_settings.disableStartAndDueDates || calendar_settings.customFieldDates) {
            filter_conditions += `)`;
        }
    }

    let time_estimate_table = 'items';
    let select_time_estimate = 'items.time_estimate';
    if (estimates_per_assignee && view.type === view_types.box && box_time_estimate) {
        time_estimate_table = 'assignees';
        select_time_estimate = `CASE WHEN COUNT(assignees.userid) != 0 THEN assignees.time_estimate ELSE items.time_estimate END`;
        group_by_cols.push('assignees.time_estimate');
    }

    let select_points_estimate = 'items.points';
    if (points_per_assignee && view.type === view_types.box && box_points_estimate) {
        select_points_estimate = `CASE WHEN COUNT(assignees.userid) != 0 THEN assignees.points_float ELSE items.points END as points`;
        group_by_cols.push('assignees.points_float');
    }

    if (no_effort) {
        if (no_effort === 'points') {
            filter_conditions += ' AND (items.points IS NULL OR items.points = 0)';
        } else if (no_effort === 'time_estimate') {
            filter_conditions += ` AND (${time_estimate_table}.time_estimate IS NULL OR ${time_estimate_table}.time_estimate = 0)`;
        } else if (no_effort.indexOf('cf_') === 0) {
            const fieldValueAndParentJoinCondition = getFieldValueAndParentJoinCondition(
                fieldParentFilterFactory,
                'cf_effort_val',
                task_params.length + 1
            );

            if (fieldValueAndParentJoinCondition) {
                task_params.push(no_effort.substring(3));
            }

            cf_effort_join = ` LEFT OUTER JOIN task_mgmt.field_values AS cf_effort_val ON items.id = cf_effort_val.item_id                             
                ${fieldValueAndParentJoinCondition}`;
            filter_conditions += '  AND cf_effort_val.value IS NULL';
        }
    }

    if (no_date) {
        filter_conditions += ' AND items.due_date IS NULL AND items.start_date IS NULL ';
    }

    if (overdue) {
        task_params.push(config.done_status_types);
        filter_conditions += ` AND statuses.type != ALL($${task_params.length})`;
        const beginning_of_day = moment()
            .tz(timezone || 'UTC')
            .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
            .valueOf();
        task_params.push(new Date().getTime(), beginning_of_day);
        filter_conditions += ` AND (
                    (
                        items.due_date <= $${task_params.length - 1}
                        AND items.due_date_time = true
                    )
                  OR (
                    items.due_date <= $${task_params.length}
                    AND
                    items.due_date_time = false
                  )
                ) `;
    }

    const order_columns = [];
    let order_column_names = [];

    let cf_select = '';

    let tag_select = '';
    let tag_join = '';

    if (division_field) {
        const direction = division_dir === -1 ? 'DESC' : 'ASC';

        if (division_field === 'priority') {
            order_columns.push(`items_priority_sort ${direction} ${division_dir === 1 ? 'NULLS LAST' : ''}`);
            group_by_cols.push('items.priority');
            order_column_names.push(`items.priority`);
        }

        if (division_field === 'subcategory') {
            order_columns.push(
                `coalesce_project_indexes_orderindex_projects_orderindex__sort ${direction}`,
                `categories_orderindex_sort ${direction}`,
                `subcategories_orderindex_sort ${direction}`
            );
            group_by_cols.push(
                'coalesce(project_indexes.orderindex, projects.orderindex)',
                'categories.orderindex',
                'subcategories.orderindex'
            );
            order_column_names.push(
                'coalesce(project_indexes.orderindex, projects.orderindex)',
                'categories.orderindex',
                'subcategories.orderindex'
            );
        }

        if (division_field === 'assignee' || division_field === 'group') {
            const assignee_order_columns = assigneeJoinConfig(division_dir, true);
            order_columns.push(...assignee_order_columns);
        }
    }

    let status_select = ', statuses.status';

    if (group_field) {
        const count_dir = group_dir === -1 ? 'ASC NULLS LAST' : 'DESC NULLS FIRST';
        const direction = group_dir === -1 ? 'DESC' : 'ASC';

        if (group_field === 'status' && division_field === 'subcategory') {
            // something weird was going on with the types here
            status_select = `, concat(coalesce((CASE
                        WHEN items.subcategory = subcategories.id
                        THEN null
                        ELSE task_subcategories.subcategory
                    END)::text, items.subcategory::text)::text, '_', statuses.status) AS status`;
            group_by_cols.push('statuses.id');
        }

        if (group_field === 'priority') {
            order_columns.push(`${formatOrderColumn(`items.priority`)} ${direction}`);
            group_by_cols.push('items.priority');
            order_column_names.push('items.priority');
        }

        if (group_field === 'subcategory') {
            order_columns.push(
                `coalesce_project_indexes_orderindex_projects_orderindex__sort ${direction}`,
                `categories_orderindex_sort ${direction}`,
                `subcategories_orderindex_sort ${direction}`
            );

            order_column_names.push(
                'coalesce(project_indexes.orderindex, projects.orderindex)',
                'categories.orderindex',
                'subcategories.orderindex'
            );

            group_by_cols.push(
                'coalesce(project_indexes.orderindex, projects.orderindex)',
                'categories.orderindex',
                'subcategories.orderindex'
            );
        }

        if (group_field === 'assignee' || group_field === 'group') {
            const assignee_order_columns = assigneeJoinConfig(group_dir);
            order_columns.push(...assignee_order_columns);
        }

        if (group_field === 'tag') {
            tag_join = `
                    LEFT JOIN LATERAL (
                        SELECT
                            tags.task_id,
                            count(tags.name) AS tag_count,
                            string_agg(tags.name, ',' ORDER BY tags.name) AS tag_concat,
                            array_agg(tags.name ORDER BY tags.name) AS tag_names
                        FROM task_mgmt.tags
                        INNER JOIN task_mgmt.${tasksTableName} AS tasks
                            ON tags.task_id = tasks.id
                        INNER JOIN task_mgmt.subcategories
                            ON subcategories.id = tasks.subcategory
                        INNER JOIN task_mgmt.categories
                            ON categories.id = subcategories.category
                        INNER JOIN task_mgmt.project_tags
                            ON project_tags.project_id = categories.project_id
                            AND project_tags.name = tags.name
                        WHERE tags.task_id = items.id
                            AND project_tags.deleted = FALSE
                        GROUP BY tags.task_id
                    )
                        AS q_tag
                        ON q_tag.task_id = items.id`;

            tag_select = ', q_tag.tag_concat AS tag, projects.id AS project_id, q_tag.tag_names';
            order_columns.push(
                `${formatOrderColumn(`q_tag.tag_count`)} ${count_dir}`,
                `${formatOrderColumn(`q_tag.tag_concat`)} ${direction}`
            );
            order_column_names.push('q_tag.tag_count', 'q_tag.tag_concat', 'projects.id', 'q_tag.tag_names');
            group_by_cols.push('q_tag.tag_count', 'q_tag.tag_concat, projects.id, q_tag.tag_names');
        }

        if (group_field === 'cf') {
            let column_name = `cf_${group_cf.id.replace(/-/g, '_')}.value->>'value'`;
            if (group_cf.type === field_types.votes) {
                column_name = `coalesce(jsonb_array_length(cf_${group_cf.id.replace(
                    /-/g,
                    '_'
                )}.value->'value'), 0) > 0`;
            }
            cf_select = `, ${column_name} AS cf`;
            group_by_cols.push(column_name);
        }

        if (group_field === 'custom_items') {
            order_columns.push(`${formatOrderColumn(`items.custom_type`)} ${direction}`);
            group_by_cols.push('items.custom_type');
            order_column_names.push('items.custom_type');
        }
    }

    let subtask_progress = '';
    let checklist_progress = '';
    let comment_progress = '';

    let auto_progress_select = '';
    let manual_progress_select = '';

    let rollup_join = ``;
    let rollup_select = ``;

    let sorting_join = ``;

    try {
        await Promise.all(
            sorting.fields.map(async (sort, idx) => {
                let { field, dir } = sort;

                const field_id = field.includes('cf_') && !field.includes('rollup:') ? field.replace('cf_', '') : null;

                if (field === 'status') {
                    dir *= -1;
                }

                let direction = `${dir === -1 ? 'DESC' : 'ASC'} NULLS LAST`;

                if (field_id) {
                    if (!target_field_ids.includes(field_id)) {
                        return;
                    }

                    const _field_id = field_id.replace(/-/g, '_');
                    const table_name = `cf_${_field_id}`;
                    const custom_field = find(target_fields, { id: field_id });
                    if (field_types[custom_field.type] != null) {
                        custom_field.type = field_types[custom_field.type];
                    }

                    let sorting_field_type = custom_field.type;

                    const fallbackToCalculatedFormula = sorting_field_type === field_types.formula;
                    if (fallbackToCalculatedFormula) {
                        const sortingConfig = formulaFieldViewConfig.get(
                            'sorting',
                            custom_field.type_config,
                            custom_field.team_id
                        );

                        if (sortingConfig.supported) {
                            // Formulas without values are treated as numbers by default,
                            // because for numbers no custom logic is applied to the column.
                            sorting_field_type = formulaTypeMapper.toFieldType(
                                sortingConfig.returnTypes,
                                FieldType.number
                            );
                        } else {
                            logger.warn({
                                msg: '[formula-calculation] View sorting was requested for an unsupported formula',
                                team_id: custom_field.team_id,
                                field_id: custom_field.id,
                            });

                            // Ignore unsupported formula field sorting
                            return;
                        }
                    }

                    // TODO: Support App Fields filtering. See CLK-539584.

                    const sql_type = field_type_to_sql_type[sorting_field_type];

                    const value_column = `(${table_name}.value->>'value')::${sql_type}`;

                    if (Number(sorting_field_type) === field_types.drop_down) {
                        order_columns.push(`${formatOrderColumn(`dd_${_field_id}.orderindex`)} ${direction}`);
                        order_column_names.push(`dd_${_field_id}.orderindex`);
                        group_by_cols.push(`dd_${_field_id}.orderindex`);
                    } else if (Number(sorting_field_type) === field_types.attachment) {
                        order_columns.push(`${formatOrderColumn(`aa_${_field_id}.attachment_count`)} ${direction}`);
                        order_column_names.push(`aa_${_field_id}.attachment_count`);
                        group_by_cols.push(`aa_${_field_id}.attachment_count`);
                    } else if (Number(sorting_field_type) === field_types.checkbox) {
                        order_columns.push(`${formatOrderColumn(`coalesce(${value_column}, false)`)} ${direction}`);
                        order_column_names.push(`coalesce(${value_column}, false)`);
                        group_by_cols.push(`${value_column}`);
                    } else if (Number(sorting_field_type) === field_types.labels) {
                        order_columns.push(`${formatOrderColumn(`ll_${_field_id}.orderindex`)} ${direction}`);
                        order_column_names.push(`ll_${_field_id}.orderindex`);
                        group_by_cols.push(`ll_${_field_id}.orderindex`);
                    } else if (Number(sorting_field_type) === field_types.tasks) {
                        const sortDisplayType = columns.fields.find(f => f.field === field)?.display;
                        const linked_tasks_sort_column_name = `cf_linked_tasks_sort_${_field_id}`;
                        const sort_table_name = `cf_sort_${_field_id}`;
                        const sort_column_name = `cf_sort_col_${_field_id}`;
                        const sort_column = `coalesce(${sort_table_name}.count, 0)`;
                        const sort_by = sortDisplayType === 'count' ? sort_column_name : linked_tasks_sort_column_name;

                        const timlQuery = `
                            UNION

                            SELECT field_task_links.task_id AS task_id, tasks.id AS link_id,
                            linked_tasks.aggregated_tasks_names AS linked_tasks
                            FROM task_mgmt.field_task_links
                            INNER JOIN task_mgmt.${tasksTableName} AS tasks
                                ON field_task_links.link_id = tasks.id
                            INNER JOIN task_mgmt.task_subcategories
                                ON task_subcategories.task_id = tasks.id
                                AND task_subcategories.subcategory != tasks.subcategory
                            INNER JOIN task_mgmt.subcategories
                                ON subcategories.id = task_subcategories.subcategory
                            ${getSubSortingJoins(
                                tasksTableName,
                                limitedMemberEnabled,
                                useSubSortingEgress,
                                workspaceIdParamNumber
                            )}
                            LEFT JOIN
                                (SELECT task_id,
                                        STRING_AGG(items.name, ',' ORDER BY items.name)
                                        AS aggregated_tasks_names
                                FROM task_mgmt.field_task_links
                                INNER JOIN task_mgmt.${tasksTableName} AS items ON link_id = items.id
                                GROUP BY task_id) AS linked_tasks ON items.id = linked_tasks.task_id
                            WHERE field_task_links.field_id = $${task_params.push(field_id)}
                                AND field_task_links.task_id = items.id
                                ${getSubSortingConditions(useSubSortingEgress, getTaskListAclConditions)}
                        `;

                        cf_sort_select += `, ${sort_column} AS ${sort_column_name}, ${linked_tasks_sort_column_name}`;
                        cf_sort_join += `
                                LEFT JOIN LATERAL (
                                    SELECT links.task_id, count(DISTINCT links.link_id) AS count,
                                    links.linked_tasks AS ${linked_tasks_sort_column_name}
                                    FROM (
                                        SELECT field_task_links.task_id AS task_id, tasks.id AS link_id,
                                        linked_tasks.aggregated_tasks_names AS linked_tasks
                                        FROM task_mgmt.field_task_links
                                        INNER JOIN task_mgmt.${tasksTableName} AS tasks
                                            ON field_task_links.link_id = tasks.id
                                        INNER JOIN task_mgmt.subcategories
                                            ON subcategories.id = tasks.subcategory
                                        ${getSubSortingJoins(
                                            tasksTableName,
                                            limitedMemberEnabled,
                                            useSubSortingEgress,
                                            workspaceIdParamNumber
                                        )}
                                        LEFT JOIN
                                            (SELECT task_id,
                                                    STRING_AGG(items.name, ',' ORDER BY items.name)
                                                    AS aggregated_tasks_names
                                            FROM task_mgmt.field_task_links
                                            INNER JOIN task_mgmt.${tasksTableName} AS items ON link_id = items.id
                                            GROUP BY task_id) AS linked_tasks ON items.id = linked_tasks.task_id
                                        WHERE field_task_links.field_id = $${task_params.push(field_id)}
                                            AND field_task_links.task_id = items.id
                                            ${getSubSortingConditions(useSubSortingEgress, getTaskListAclConditions)}

                                        ${timlQuery}
                                    ) AS links
                                    GROUP BY links.task_id, links.linked_tasks
                                )
                                    AS ${sort_table_name}
                                    ON items.id = ${sort_table_name}.task_id`;
                        order_columns.push(`${sort_by} ${direction}`);
                        order_column_names.push(sort_column, linked_tasks_sort_column_name);
                        group_by_cols.push(sort_column, linked_tasks_sort_column_name);
                    } else if (Number(sorting_field_type) === field_types.list_relationship) {
                        const sortDisplayType = columns.fields.find(f => f.field === field)?.display;
                        const linked_relationships_sort_column_name = `cf_linked_tasks_sort_${_field_id}`;
                        const sort_table_name = `cf_sort_${_field_id}`;
                        const sort_column_name = `cf_sort_col_${_field_id}`;
                        const sort_column = `coalesce(${sort_table_name}.count, 0)`;
                        const sort_by =
                            sortDisplayType === 'count' ? sort_column_name : linked_relationships_sort_column_name;

                        const inverted =
                            parent.type === parent_types.subcategory &&
                            custom_field.type_config.subcategory_id === parent.id;

                        const target_column = !inverted ? 'task_id' : 'link_id';
                        const link_column = !inverted ? 'link_id' : 'task_id';

                        const timlQuery = `
                            UNION

                            SELECT linked_subcategory_tasks.${target_column} AS task_id, tasks.id AS link_id,
                            linked_tasks.aggregated_relationships_names AS relationships
                            FROM task_mgmt.linked_subcategory_tasks
                            INNER JOIN task_mgmt.${tasksTableName} AS tasks
                                ON linked_subcategory_tasks.${link_column} = tasks.id
                                ${
                                    useExtraWorkspaceIdCondition
                                        ? `AND tasks.workspace_id = $${workspaceIdParamNumber}`
                                        : ''
                                }
                            INNER JOIN task_mgmt.task_subcategories
                                ON task_subcategories.task_id = tasks.id
                                AND task_subcategories.subcategory != tasks.subcategory
                            INNER JOIN task_mgmt.subcategories
                                ON subcategories.id = task_subcategories.subcategory
                            ${getSubSortingJoins(
                                tasksTableName,
                                limitedMemberEnabled,
                                useSubSortingEgress,
                                workspaceIdParamNumber
                            )}
                            LEFT JOIN
                                (SELECT ${target_column},
                                        STRING_AGG(items.name, ',' ORDER BY items.name)
                                        AS aggregated_relationships_names
                                FROM task_mgmt.linked_subcategory_tasks AS lst
                                INNER JOIN task_mgmt.${tasksTableName} AS items ON ${link_column} = items.id
                                ${
                                    useExtraWorkspaceIdCondition
                                        ? `WHERE items.workspace_id = $${workspaceIdParamNumber} AND lst.workspace_id = $${workspaceIdParamNumber}`
                                        : ''
                                }
                                GROUP BY ${target_column}) AS linked_tasks ON items.id = linked_tasks.${target_column}
                            WHERE linked_subcategory_tasks.field_id = $${task_params.push(field_id)}
                                AND linked_subcategory_tasks.${target_column} = items.id
                                ${
                                    useExtraWorkspaceIdCondition
                                        ? `AND items.workspace_id = $${workspaceIdParamNumber} AND linked_subcategory_tasks.workspace_id = $${workspaceIdParamNumber}`
                                        : ''
                                }
                                ${getSubSortingConditions(useSubSortingEgress, getTaskListAclConditions)}
                        `;

                        const linked_tasks_join = `
                            LEFT JOIN LATERAL (
                                SELECT links.task_id, count(DISTINCT links.link_id) AS count,
                                links.relationships AS ${linked_relationships_sort_column_name}
                                FROM (
                                    SELECT linked_subcategory_tasks.${target_column} AS task_id, tasks.id AS link_id,
                                    linked_tasks.aggregated_relationships_names AS relationships
                                    FROM task_mgmt.linked_subcategory_tasks
                                    INNER JOIN task_mgmt.${tasksTableName} AS tasks
                                        ON linked_subcategory_tasks.${link_column} = tasks.id
                                        ${
                                            useExtraWorkspaceIdCondition
                                                ? `AND tasks.workspace_id = $${workspaceIdParamNumber}`
                                                : ''
                                        }
                                    INNER JOIN task_mgmt.subcategories
                                        ON subcategories.id = tasks.subcategory
                                    ${getSubSortingJoins(
                                        tasksTableName,
                                        limitedMemberEnabled,
                                        useSubSortingEgress,
                                        workspaceIdParamNumber
                                    )}
                                    LEFT JOIN
                                        (SELECT ${target_column},
                                                STRING_AGG(items.name, ',' ORDER BY items.name)
                                                AS aggregated_relationships_names
                                        FROM task_mgmt.linked_subcategory_tasks AS lst
                                        INNER JOIN task_mgmt.${tasksTableName} AS items ON ${link_column} = items.id
                                        ${
                                            useExtraWorkspaceIdCondition
                                                ? `WHERE items.workspace_id = $${workspaceIdParamNumber} AND lst.workspace_id = $${workspaceIdParamNumber}`
                                                : ''
                                        }
                                        GROUP BY ${target_column}) AS linked_tasks ON items.id = linked_tasks.${target_column}
                                    WHERE linked_subcategory_tasks.field_id = $${task_params.push(field_id)}
                                        AND linked_subcategory_tasks.${target_column} = items.id
                                        ${
                                            useExtraWorkspaceIdCondition
                                                ? `AND items.workspace_id = $${workspaceIdParamNumber} AND linked_subcategory_tasks.workspace_id = $${workspaceIdParamNumber}`
                                                : ''
                                        }
                                        ${getSubSortingConditions(useSubSortingEgress, getTaskListAclConditions)}

                                    ${timlQuery}
                                ) AS links
                                GROUP BY links.task_id,
                                links.relationships
                            )
                                AS ${sort_table_name}
                                ON items.id = ${sort_table_name}.task_id`;

                        if (queryOptimizations.useCteWithListRelationshipCfSort) {
                            encloseMainQueryInCte = true;
                            top_level_cte_select += `, ${sort_column} AS ${sort_column_name}, ${linked_relationships_sort_column_name}`;
                            top_level_cte_join += linked_tasks_join;
                            if (useExtraWorkspaceIdCondition) {
                                group_by_cols.push('items.workspace_id');
                            }
                            order_columns.push(`${sort_by} ${direction}`);
                            top_level_cte_order_column_names.push(sort_column, linked_relationships_sort_column_name);
                        } else {
                            cf_sort_select += `, ${sort_column} AS ${sort_column_name}, ${linked_relationships_sort_column_name}`;
                            cf_sort_join += linked_tasks_join;
                            order_columns.push(`${sort_by} ${direction}`);
                            order_column_names.push(sort_column, linked_relationships_sort_column_name);
                            group_by_cols.push(sort_column, linked_relationships_sort_column_name);
                        }
                    } else if (Number(sorting_field_type) === field_types.users) {
                        order_columns.push(`${formatOrderColumn(`uu_${_field_id}.username`)} ${direction}`);
                        order_column_names.push(`uu_${_field_id}.username`);
                        group_by_cols.push(`uu_${_field_id}.username`);
                    } else if (Number(sorting_field_type) === field_types.automatic_progress) {
                        const resolved = [];
                        const totals = [];

                        if (custom_field.type_config.tracking.assigned_comments) {
                            comment_progress = `
                                    LEFT JOIN LATERAL (
                                        SELECT
                                            comments.task_id AS item_id,
                                            count(comments.*) AS total_comments,
                                            count(comments.*) FILTER (WHERE comments.resolved = TRUE) AS resolved_comments
                                        FROM task_mgmt.comments
                                        WHERE items.id = comments.task_id
                                            AND comments.assignee IS NOT NULL
                                        GROUP BY comments.task_id
                                    ) AS comment_progress
                                        ON items.id = comment_progress.item_id`;

                            resolved.push('coalesce(comment_progress.resolved_comments, 0)');
                            totals.push('coalesce(comment_progress.total_comments, 0)');
                        }

                        if (custom_field.type_config.tracking.subtasks) {
                            subtask_progress = `
                                    LEFT JOIN LATERAL (
                                        SELECT
                                            subtasks.parent AS item_id,
                                            subtasks.subtask_parent AS subtask_item_id,
                                            count(subtasks.*) AS total_subtasks,
                                            count(subtasks.*)
                                                FILTER (WHERE statuses.type IN ('done', 'closed')) AS done_subtasks
                                        FROM task_mgmt.${tasksTableName} AS subtasks
                                        INNER JOIN task_mgmt.statuses
                                            ON statuses.id = subtasks.status_id
                                        WHERE ((subtasks.parent = items.id AND subtasks.subtask_parent IS NULL) OR (subtasks.parent = items.parent AND subtasks.subtask_parent = items.id))
                                            AND subtasks.deleted = FALSE
                                            AND subtasks.template = FALSE
                                            ${
                                                custom_field.type_config.tracking.archived_subtasks
                                                    ? ''
                                                    : 'AND subtasks.archived = FALSE'
                                            }
                                        GROUP BY subtasks.parent, subtasks.subtask_parent
                                    ) AS subtask_progress
                                        ON items.id = subtask_progress.item_id OR items.id = subtask_progress.subtask_item_id`;

                            resolved.push('coalesce(subtask_progress.done_subtasks, 0)');
                            totals.push('coalesce(subtask_progress.total_subtasks, 0)');
                        }

                        if (custom_field.type_config.tracking.checklists) {
                            checklist_progress = `
                                    LEFT JOIN LATERAL (
                                        SELECT
                                            checklists.task_id AS item_id,
                                            count(checklist_items.*) AS total_checklist_items,
                                            count(checklist_items.*)
                                                FILTER (WHERE checklist_items.resolved = TRUE) AS resolved_checklist_items
                                        FROM task_mgmt.checklists
                                        INNER JOIN task_mgmt.checklist_items
                                            ON checklist_items.checklist_id = checklists.id
                                            AND checklist_items.deleted = FALSE
                                        WHERE checklists.task_id = items.id
                                            AND checklists.deleted = FALSE
                                        GROUP BY checklists.task_id
                                    ) AS checklist_progress
                                        ON items.id = checklist_progress.item_id`;

                            resolved.push('coalesce(checklist_progress.resolved_checklist_items, 0)');
                            totals.push('coalesce(checklist_progress.total_checklist_items, 0)');
                        }

                        auto_progress_select += `,
                                ((${resolved.join(' + ')})::float /
                                    (CASE WHEN ${totals.join(' + ')} = 0 THEN 1 ELSE ${totals.join(' + ')} END)::float
                                ) AS auto_${_field_id}_sort`;

                        order_columns.push(`${formatOrderColumn(`auto_${_field_id}`)} ${dir === 1 ? 'DESC' : 'ASC'}`);
                        group_by_cols.push(`auto_${_field_id}_sort`);
                    } else if (Number(sorting_field_type) === field_types.manual_progress) {
                        const { start, end } = custom_field.type_config;

                        manual_progress_select += `,
                                (coalesce(${value_column}, 0) - ${start}) /
                                    (${end} - ${start}) AS man_${_field_id}_sort`;

                        order_columns.push(`${formatOrderColumn(`man_${_field_id}`)} ${dir === 1 ? 'DESC' : 'ASC'}`);
                        group_by_cols.push(`man_${_field_id}_sort`);
                    } else if (
                        [field_types.text, field_types.email, field_types.short_text].includes(
                            Number(sorting_field_type)
                        )
                    ) {
                        order_columns.push(`${formatOrderColumn(`lower(${value_column})`)} ${direction}`);
                        group_by_cols.push(value_column);
                        order_column_names.push(`lower(${value_column})`);
                    } else if (Number(sorting_field_type) === field_types.location) {
                        const location_column = `(${table_name}.value->>'value')::jsonb->>'formatted_address'`;
                        direction = dir === -1 ? 'DESC NULLS FIRST' : 'ASC';
                        order_columns.push(`${formatOrderColumn(`lower(${location_column})`)} ${direction}`);
                        group_by_cols.push(location_column);
                        order_column_names.push(`lower(${location_column})`);
                    } else if (Number(sorting_field_type) === field_types.votes) {
                        const votes_column = `jsonb_array_length(${table_name}.value->'value')`;
                        order_columns.push(`${formatOrderColumn(`coalesce(${votes_column}, 0)`)} ${direction}`);
                        order_column_names.push(`coalesce(${votes_column}, 0)`);
                        group_by_cols.push(votes_column);
                    } else {
                        order_columns.push(`${formatOrderColumn(value_column)} ${direction}`);
                        group_by_cols.push(`${value_column}`);
                        order_column_names.push(`${value_column}`);
                    }

                    return;
                }

                if (field.includes('rollup:')) {
                    const sortFieldFromColumns = columns.fields.find(c => c.field === field);
                    // Skip if sort of rollup field doesn't exist in columns -- CLK-172583
                    if (!sortFieldFromColumns && view.type === view_types.list && field.includes('rollup:')) {
                        return;
                    }
                    const table_name = `r${idx}${field.split(':')[1].replace(/-/g, '_')}`;
                    const value_column = `${table_name}.value`;
                    const { query: rollup_query } = await rollupMod.generateQuery(userid, sort, {
                        params: task_params,
                        team_id,
                    });

                    rollup_join += `
                            LEFT JOIN LATERAL (${rollup_query})
                                AS ${table_name}
                                ON ${table_name}.id = items.id`;
                    rollup_select += `, ${value_column}`;

                    order_columns.push(`${formatOrderColumn(value_column)} ${direction}`);
                    group_by_cols.push(value_column);
                    order_column_names.push(value_column);
                    return;
                }

                field = snakeCase(field);

                if (field === 'assignee' || field === 'group') {
                    const assignee_order_columns = assigneeJoinConfig(-dir);
                    order_columns.push(...assignee_order_columns);
                }

                if (field === 'created_by') {
                    created_by_join = `
                            LEFT JOIN task_mgmt.users AS creator
                                ON creator.id = items.creator`;
                    created_by_select = `, coalesce(creator.username, creator.email) AS creator_name`;
                }

                if (field === 'comment_count') {
                    comment_count_join = `
                            LEFT JOIN LATERAL (
                                SELECT task_id, COUNT(task_id) AS count
                                FROM task_mgmt.comments
                                WHERE
                                    comments.deleted = FALSE
                                    AND comments.task_id = items.id
                                GROUP BY task_id
                            ) AS comment_count ON items.id = comment_count.task_id
                        `;
                    comment_count_from = ', comment_count.count';
                }

                if (field === 'incomplete_comment_count') {
                    incomplete_comment_join = `
                            LEFT JOIN LATERAL (
                                SELECT
                                    comments.task_id,
                                    COUNT(DISTINCT comments.id) FILTER(WHERE (comments.assignee IS NOT NULL OR comments.group_assignee IS NOT NULL) AND (comments.resolved = FALSE OR comments.resolved IS NULL))
                                        + COUNT(DISTINCT threaded_comments.id) AS count
                                FROM
                                    task_mgmt.comments
                                LEFT JOIN task_mgmt.comments AS threaded_comments
                                    ON threaded_comments.parent = comments.id::TEXT
                                    AND threaded_comments.deleted = FALSE
                                    AND (threaded_comments.assignee IS NOT NULL OR threaded_comments.group_assignee IS NOT NULL)
                                    AND (threaded_comments.resolved = FALSE OR threaded_comments.resolved IS NULL)
                                WHERE
                                    comments.deleted = FALSE
                                    AND comments.task_id = items.id
                                GROUP BY comments.task_id
                            ) AS incomplete_comment_count ON items.id = incomplete_comment_count.task_id
                        `;
                    incomplete_comment_from = `, incomplete_comment_count.count`;
                }

                if (field === 'time_in_status') {
                    time_in_status_join = `
                            LEFT JOIN LATERAL (
                                SELECT  task_history.task_id,
                                        max(task_history.date)
                                FROM task_mgmt.task_history
                                WHERE task_history.task_id = items.id
                                    AND task_history.field = 'status'
                                    AND task_history.hidden IS NOT TRUE
                                GROUP BY task_history.task_id
                            )
                                AS time_status
                                ON items.id = time_status.task_id`;
                    time_in_status_from = ', time_status.max';
                }

                if (field === 'time_logged') {
                    time_spent_join = `
                            LEFT JOIN LATERAL (
                                SELECT item_id, sum(time)
                                FROM task_mgmt.time_spent
                                WHERE item_id = items.id AND (deleted = FALSE OR deleted IS NULL)
                                GROUP BY item_id
                            )
                                AS time_logged
                                ON items.id = time_logged.item_id`;
                    time_spent_from = ', time_logged.sum';
                }

                if (field === 'time_logged_rollup') {
                    time_spent_rollup_join = `
                        LEFT JOIN LATERAL (
                            SELECT coalesce(tasks.parent, tasks.id) as item_id, sum(time)
                            FROM task_mgmt.time_spent,
                                task_mgmt.${tasksTableName} as tasks
                            WHERE time_spent.item_id = tasks.id
                            AND (time_spent.deleted = FALSE OR time_spent.deleted IS NULL)
                            AND (tasks.id = items.id OR tasks.parent = items.id)
                            AND tasks.deleted = false AND tasks.template = false
                            GROUP BY coalesce(tasks.parent, tasks.id)
                        )
                            AS time_logged_rollup
                            ON true`;
                    time_spent_rollup_join_timl_or_siml = `
                        LEFT JOIN LATERAL (
                            SELECT
                                task_subcategories.linked_item_id as item_id,
                                task_subcategories.subcategory,
                                SUM(time_spent.time) as sum
                            FROM task_subcategories
                            INNER JOIN task_mgmt.time_spent
                                ON task_subcategories.task_id = time_spent.item_id
                            GROUP BY task_subcategories.linked_item_id, task_subcategories.subcategory
                        )
                        AS time_logged_rollup
                        ON items.id = time_logged_rollup.item_id
                            AND task_subcategories.subcategory = time_logged_rollup.subcategory`;
                    time_spent_rollup_from = ', time_logged_rollup.sum';
                }

                if (field === 'time_estimate_rollup') {
                    time_estimate_rollup_join = `
                        LEFT JOIN LATERAL (
                            SELECT coalesce(estimates.parent, estimates.id) as item_id, sum(time_estimate)
                            FROM task_mgmt.${tasksTableName} as estimates
                            WHERE (estimates.id = items.id OR estimates.parent = items.id)
                            AND estimates.deleted = false AND estimates.template = false
                            GROUP BY coalesce(estimates.parent, estimates.id)
                        )
                            AS time_estimate_rollup
                            ON true`;

                    time_estimate_rollup_join_timl_or_siml = `
                        LEFT JOIN LATERAL (
                            SELECT
                                estimates.linked_item_id as item_id,
                                estimates.subcategory,
                                SUM(estimates.time_estimate) as sum
                            FROM task_subcategories as estimates
                            GROUP BY estimates.linked_item_id, estimates.subcategory
                        )
                        AS time_estimate_rollup
                        ON items.id = time_estimate_rollup.item_id
                            AND task_subcategories.subcategory = time_estimate_rollup.subcategory`;
                    time_estimate_rollup_from = ', time_estimate_rollup.sum';
                }

                if (field === 'points_estimate_rollup') {
                    points_estimate_rollup_join = `
                        LEFT JOIN LATERAL (
                            SELECT coalesce(estimates.parent, estimates.id) as item_id, sum(points)
                            FROM task_mgmt.${tasksTableName} as estimates
                            WHERE (estimates.id = items.id OR estimates.parent = items.id)
                            AND estimates.deleted = false AND estimates.template = false
                            GROUP BY coalesce(estimates.parent, estimates.id)
                        )
                            AS points_estimate_rollup
                            ON true`;
                    points_estimate_rollup_join_timl_or_siml = `
                        LEFT JOIN LATERAL (
                            SELECT
                                estimates.linked_item_id as item_id,
                                estimates.subcategory,
                                SUM(estimates.points) as sum
                            FROM task_subcategories as estimates
                            GROUP BY estimates.linked_item_id, estimates.subcategory
                        )
                        AS points_estimate_rollup
                        ON items.id = points_estimate_rollup.item_id 
                            AND task_subcategories.subcategory = points_estimate_rollup.subcategory`;
                    points_estimate_rollup_from = ', points_estimate_rollup.sum';
                }

                if (field === 'pages') {
                    sorting_join += `
                            LEFT JOIN LATERAL (
                                SELECT links.item_id,
                                       count(links.page_id) AS page_count
                                FROM (
                                    SELECT generic_links.left_id AS item_id,
                                           generic_links.right_id AS page_id,
                                           view_docs.name AS page_name
                                    FROM task_mgmt.generic_links
                                    INNER JOIN task_mgmt.view_docs
                                        ON generic_links.right_id = view_docs.id
                                        AND generic_links.right_type = ${config.generic_links.types.page.int}
                                    WHERE generic_links.left_id = items.id
                                        AND generic_links.left_type = ${config.generic_links.types.task.int}
                                        AND view_docs.deleted = FALSE

                                    UNION

                                    SELECT generic_links.right_id AS item_id,
                                           generic_links.left_id AS page_id,
                                           view_docs.name AS page_name
                                    FROM task_mgmt.generic_links
                                    INNER JOIN task_mgmt.view_docs
                                        ON generic_links.left_id = view_docs.id
                                        AND generic_links.left_type = ${config.generic_links.types.page.int}
                                    WHERE generic_links.right_id = items.id
                                        AND generic_links.right_type = ${config.generic_links.types.task.int}
                                        AND view_docs.deleted = FALSE
                                ) AS links
                                GROUP BY links.item_id
                            )
                                AS pages_links_sort
                                ON pages_links_sort.item_id = items.id`;
                }

                if (field === 'linked') {
                    sorting_join += `
                            LEFT JOIN LATERAL (
                                SELECT links.item_id, count(DISTINCT links.link_id) AS link_count
                                FROM (
                                    SELECT task_links.link_id AS item_id, task_links.task_id AS link_id
                                    FROM task_mgmt.task_links
                                    INNER JOIN task_mgmt.${tasksTableName} AS tasks
                                        ON task_links.task_id = tasks.id
                                    INNER JOIN task_mgmt.subcategories
                                               ON subcategories.id = tasks.subcategory
                                    ${getSubSortingJoins(
                                        tasksTableName,
                                        limitedMemberEnabled,
                                        useSubSortingEgress,
                                        workspaceIdParamNumber
                                    )}
                                    WHERE task_links.link_id = items.id
                                    ${getSubSortingConditions(useSubSortingEgress, getTaskListAclConditions)}

                                    UNION

                                    SELECT task_links.link_id AS item_id, task_links.task_id AS link_id
                                    FROM task_mgmt.task_links
                                    INNER JOIN task_mgmt.${tasksTableName} AS tasks
                                        ON task_links.task_id = tasks.id
                                    INNER JOIN task_mgmt.task_subcategories
                                        ON task_subcategories.task_id = tasks.id
                                        AND task_subcategories.subcategory != tasks.subcategory
                                    INNER JOIN task_mgmt.subcategories
                                               ON subcategories.id = task_subcategories.subcategory
                                    ${getSubSortingJoins(
                                        tasksTableName,
                                        limitedMemberEnabled,
                                        useSubSortingEgress,
                                        workspaceIdParamNumber
                                    )}
                                    WHERE task_links.link_id = items.id
                                    ${getSubSortingConditions(useSubSortingEgress, getTaskListAclConditions)}

                                    UNION

                                    SELECT task_links.task_id AS item_id, task_links.link_id
                                    FROM task_mgmt.task_links
                                    INNER JOIN task_mgmt.${tasksTableName} AS tasks
                                        ON task_links.link_id = tasks.id
                                    INNER JOIN task_mgmt.subcategories
                                               ON subcategories.id = tasks.subcategory
                                    ${getSubSortingJoins(
                                        tasksTableName,
                                        limitedMemberEnabled,
                                        useSubSortingEgress,
                                        workspaceIdParamNumber
                                    )}
                                    WHERE task_links.task_id = items.id
                                    ${getSubSortingConditions(useSubSortingEgress, getTaskListAclConditions)}

                                    UNION

                                    SELECT task_links.task_id AS item_id, task_links.link_id
                                    FROM task_mgmt.task_links
                                    INNER JOIN task_mgmt.${tasksTableName} AS tasks
                                        ON task_links.link_id = tasks.id
                                    INNER JOIN task_mgmt.task_subcategories
                                        ON task_subcategories.task_id = tasks.id
                                        AND task_subcategories.subcategory != tasks.subcategory
                                    INNER JOIN task_mgmt.subcategories
                                               ON subcategories.id = task_subcategories.subcategory
                                    ${getSubSortingJoins(
                                        tasksTableName,
                                        limitedMemberEnabled,
                                        useSubSortingEgress,
                                        workspaceIdParamNumber
                                    )}
                                    WHERE task_links.task_id = items.id
                                    ${getSubSortingConditions(useSubSortingEgress, getTaskListAclConditions)}
                                ) AS links
                                GROUP BY links.item_id
                            )
                                AS linked_sort
                                ON linked_sort.item_id = items.id`;
                }

                if (field === 'dependencies') {
                    sorting_join += `
                            LEFT JOIN LATERAL (
                                SELECT links.item_id, count(DISTINCT links.link_id) AS link_count
                                FROM (
                                    SELECT task_dependencies.task_id AS item_id, task_dependencies.depends_on AS link_id
                                    FROM task_mgmt.task_dependencies
                                    INNER JOIN task_mgmt.${tasksTableName} AS tasks
                                        ON task_dependencies.depends_on = tasks.id
                                    INNER JOIN task_mgmt.subcategories
                                               ON subcategories.id = tasks.subcategory
                                    ${getSubSortingJoins(
                                        tasksTableName,
                                        limitedMemberEnabled,
                                        useSubSortingEgress,
                                        workspaceIdParamNumber
                                    )}
                                    WHERE task_dependencies.task_id = items.id
                                    ${getSubSortingConditions(useSubSortingEgress, getTaskListAclConditions)}

                                    UNION

                                    SELECT task_dependencies.task_id AS item_id, task_dependencies.depends_on AS link_id
                                    FROM task_mgmt.task_dependencies
                                    INNER JOIN task_mgmt.${tasksTableName} AS tasks
                                        ON task_dependencies.depends_on = tasks.id
                                    INNER JOIN task_mgmt.task_subcategories
                                        ON task_subcategories.task_id = tasks.id
                                        AND task_subcategories.subcategory != tasks.subcategory
                                    INNER JOIN task_mgmt.subcategories
                                               ON subcategories.id = task_subcategories.subcategory
                                    ${getSubSortingJoins(
                                        tasksTableName,
                                        limitedMemberEnabled,
                                        useSubSortingEgress,
                                        workspaceIdParamNumber
                                    )}
                                    WHERE task_dependencies.task_id = items.id
                                    ${getSubSortingConditions(useSubSortingEgress, getTaskListAclConditions)}

                                    UNION

                                    SELECT task_dependencies.depends_on AS item_id, task_dependencies.task_id AS link_id
                                    FROM task_mgmt.task_dependencies
                                    INNER JOIN task_mgmt.${tasksTableName} AS tasks
                                        ON task_dependencies.task_id = tasks.id
                                    INNER JOIN task_mgmt.subcategories
                                               ON subcategories.id = tasks.subcategory
                                    ${getSubSortingJoins(
                                        tasksTableName,
                                        limitedMemberEnabled,
                                        useSubSortingEgress,
                                        workspaceIdParamNumber
                                    )}
                                    WHERE task_dependencies.depends_on = items.id
                                    ${getSubSortingConditions(useSubSortingEgress, getTaskListAclConditions)}

                                    UNION

                                    SELECT task_dependencies.depends_on AS item_id, task_dependencies.task_id AS link_id
                                    FROM task_mgmt.task_dependencies
                                    INNER JOIN task_mgmt.${tasksTableName} AS tasks
                                        ON task_dependencies.task_id = tasks.id
                                    INNER JOIN task_mgmt.task_subcategories
                                        ON task_subcategories.task_id = tasks.id
                                        AND task_subcategories.subcategory != tasks.subcategory
                                    INNER JOIN task_mgmt.subcategories
                                               ON subcategories.id = task_subcategories.subcategory
                                    ${getSubSortingJoins(
                                        tasksTableName,
                                        limitedMemberEnabled,
                                        useSubSortingEgress,
                                        workspaceIdParamNumber
                                    )}
                                    WHERE task_dependencies.depends_on = items.id
                                    ${getSubSortingConditions(useSubSortingEgress, getTaskListAclConditions)}
                                ) AS links
                                GROUP BY links.item_id
                            ) AS dependencies_sort ON dependencies_sort.item_id = items.id`;
                }

                if (field === 'status' && sortByStatusByType) {
                    // Primary sort by status type to fold in timl'd statuses.
                    // Where they fall within the status group is a little arbitrary since
                    // the orderindexes are not really comparable.
                    sorting_join += `
                        LEFT JOIN LATERAL (
                            SELECT CASE 
                                WHEN statuses.type = 'open' THEN 0
                                WHEN statuses.type = 'unstarted' THEN 1
                                WHEN statuses.type = 'custom' THEN 2
                                WHEN statuses.type = 'done' THEN 3
                                WHEN statuses.type = 'closed' THEN 4
                                ELSE 5
                                END AS status_type_idx
                            FROM task_mgmt.statuses
                            WHERE statuses.id = items.status_id
                        ) AS status_type_idx ON true`;
                    const column = 'status_type_idx.status_type_idx';
                    order_columns.push(`${formatOrderColumn(column)} ${direction}`);
                    group_by_cols.push(column);
                    order_column_names.push(column);
                }

                const column_map = {
                    status: sortByStatusByType ? 'statuses.orderindex' : 'lower(items.status)',
                    priority: 'items.priority',
                    subcategory: 'items.subcategory',
                    category: 'subcategories.category',
                    project: 'categories.project_id',
                    due_date: 'items.due_date',
                    start_date: 'items.start_date',
                    duration: 'items.duration',
                    comment_count: 'comment_count.count',
                    incomplete_comment_count: 'incomplete_comment_count.count',
                    time_logged: 'time_logged.sum',
                    time_logged_rollup: 'time_logged_rollup.sum',
                    time_estimate: 'items.time_estimate',
                    time_estimate_rollup: 'time_estimate_rollup.sum',
                    points_estimate: 'items.points',
                    points_estimate_rollup: 'points_estimate_rollup.sum',
                    date_created: 'items.date_created',
                    date_updated: 'items.date_updated',
                    date_delegated: 'items.date_delegated',
                    date_done: 'items.date_done',
                    date_closed: 'items.date_closed',
                    id: 'items.id',
                    name: 'lower(items.name)',
                    assignee: `assignee_names`,
                    created_by: `coalesce(creator.username, creator.email)`,
                    time_in_status: 'time_status.max',
                    linked: `coalesce(linked_sort.link_count, 0)`,
                    dependencies: `coalesce(dependencies_sort.link_count, 0)`,
                    pages: 'coalesce(pages_links_sort.page_count, 0)',
                };

                const column = column_map[field];
                const existing_idx = findIndex(order_columns, existing => existing.includes(column));
                direction = dir === -1 ? 'DESC' : 'ASC';

                // Reverse for time_in_status only.
                if (field === 'time_in_status') {
                    direction = dir === -1 ? 'ASC' : 'DESC';
                }

                if (column !== column_map.comment_count && column !== column_map.incomplete_comment_count) {
                    direction += ' NULLS LAST';
                } else if (dir === -1) {
                    direction += ' NULLS LAST';
                } else {
                    direction += ' NULLS FIRST';
                }

                if (existing_idx < 0) {
                    order_columns.push(`${formatOrderColumn(column)} ${direction}`);
                    group_by_cols.push(column);
                    order_column_names.push(column);
                } else {
                    order_columns[existing_idx] = `${column} ${direction}`;
                }
            })
        );
    } catch (e) {
        series_cb(e);
        return;
    }

    order_column_names = order_column_names.filter((name, idx) => order_column_names.indexOf(name) === idx);
    order_column_names = order_column_names.map(name => (name ? formatOrderColumnSelect(name) : ''));

    const legacy_columns = {
        assignee: ['items.assignee_orderindex'],
        priority: ['items.priority_orderindex'],
        tag: ['items.tag_orderindex'],
        due_date: ['items.due_date_orderindex'],
        none: ['items.none_orderindex'],
        cf: [`cf_${(group_cf_id || '').replace(/-/g, '_')}.orderindex`, 'items.field_orderindex'],
    }[group_field] || ['items.orderindex'];

    let orderindex_table = {
        assignee: `
                LEFT JOIN task_mgmt.assignee_orderindexes AS orderindexes
                    ON orderindexes.task_id = items.id
                    AND orderindexes.team_id = items.workspace_id`,
        group: `
                LEFT JOIN task_mgmt.assignee_orderindexes AS orderindexes
                    ON orderindexes.task_id = items.id
                    AND orderindexes.team_id = items.workspace_id`,
        priority: `
                LEFT JOIN task_mgmt.priority_orderindexes AS orderindexes
                    ON orderindexes.task_id = items.id
                    AND orderindexes.team_id = items.workspace_id`,
        tag: `
                LEFT JOIN task_mgmt.tag_orderindexes AS orderindexes
                    ON orderindexes.task_id = items.id
                    AND orderindexes.team_id = items.workspace_id`,
        due_date: `
                LEFT JOIN task_mgmt.due_date_orderindexes AS orderindexes
                    ON orderindexes.task_id = items.id
                    AND orderindexes.team_id = items.workspace_id`,
        none: `
                LEFT JOIN task_mgmt.none_orderindexes AS orderindexes
                    ON orderindexes.task_id = items.id
                    AND orderindexes.team_id = items.workspace_id`,
        cf: `
                LEFT JOIN task_mgmt.field_orderindexes AS orderindexes
                    ON orderindexes.task_id = items.id
                    AND orderindexes.team_id = items.workspace_id
                    AND orderindexes.type = 'field_orderindex:${group_cf_id || ''}' AND ${`cf_${(
            group_cf_id || ''
        ).replace(/-/g, '_')}.value is NOT NULL`}`,
        subcategory: `
                LEFT JOIN task_mgmt.status_orderindexes AS orderindexes
                    ON orderindexes.task_id = items.id
                    AND orderindexes.type = 'orderindex'
                    AND orderindexes.team_id = items.workspace_id`,
        status: `
                LEFT JOIN task_mgmt.status_orderindexes AS orderindexes
                    ON orderindexes.task_id = items.id
                    AND orderindexes.type = 'orderindex'
                    AND orderindexes.team_id = items.workspace_id`,
        custom_items: `
                LEFT JOIN task_mgmt.custom_items_orderindexes AS orderindexes
                    ON orderindexes.task_id = items.id
                    AND orderindexes.team_id = items.workspace_id`,
    }[group_field || 'status'];

    let multi_subcat_orderindex_table = orderindex_table;

    const findBoardViewOrderindexes = view.type === view_types.board && parent.type !== parent_types.subcategory;

    if (!group_field || ['status', 'subcategory'].includes(group_field)) {
        if (findBoardViewOrderindexes) {
            switch (parent.type) {
                case parent_types.category:
                    multi_subcat_orderindex_table = `
                            LEFT JOIN LATERAL (
                                SELECT task_id, type, orderindex, team_id, date
                                FROM task_mgmt.status_orderindexes
                                WHERE task_id = items.id
                                    AND team_id = items.workspace_id
                                    AND type = (
                                        SELECT 'orderindex:' || task_subcategories.subcategory
                                        FROM task_mgmt.task_subcategories
                                        INNER JOIN task_mgmt.subcategories
                                            ON subcategories.id = task_subcategories.subcategory
                                        WHERE task_subcategories.task_id = items.id
                                            AND subcategories.category = categories.id
                                            AND task_subcategories.subcategory != items.subcategory
                                        ORDER BY date
                                        LIMIT 1
                                    )
                            ) AS orderindexes ON true`;
                    break;
                case parent_types.project:
                    multi_subcat_orderindex_table = `
                            LEFT JOIN LATERAL (
                                SELECT task_id, type, orderindex, team_id, date
                                FROM task_mgmt.status_orderindexes
                                WHERE task_id = items.id
                                    AND team_id = items.workspace_id
                                    AND type = (
                                        SELECT 'orderindex:' || task_subcategories.subcategory
                                        FROM task_mgmt.task_subcategories
                                        INNER JOIN task_mgmt.subcategories
                                            ON subcategories.id = task_subcategories.subcategory
                                        INNER JOIN task_mgmt.categories
                                            ON categories.id = subcategories.category
                                        WHERE task_subcategories.task_id = items.id
                                            AND categories.project_id = projects.id
                                            AND task_subcategories.subcategory != items.subcategory
                                        ORDER BY date
                                        LIMIT 1
                                    )
                            ) AS orderindexes ON true`;
                    break;
                case parent_types.team:
                default:
                    multi_subcat_orderindex_table = `
                            LEFT JOIN LATERAL (
                                SELECT task_id, type, orderindex, team_id, date
                                FROM task_mgmt.status_orderindexes
                                WHERE task_id = items.id
                                    AND team_id = items.workspace_id
                                    AND type = (
                                        SELECT 'orderindex:' || subcategory
                                        FROM task_mgmt.task_subcategories
                                        WHERE task_subcategories.task_id = items.id
                                            AND task_subcategories.subcategory != items.subcategory
                                        ORDER BY date
                                        LIMIT 1
                                    )
                            ) AS orderindexes ON true`;
                    break;
            }
        } else {
            multi_subcat_orderindex_table = `
                    LEFT JOIN task_mgmt.status_orderindexes AS orderindexes
                        ON orderindexes.task_id = items.id
                        AND orderindexes.type = 'orderindex:' || task_subcategories.subcategory
                        AND orderindexes.team_id = items.workspace_id`;
        }
    }

    let group_index = `coalesce(orderindexes.orderindex, ${legacy_columns.join(', ')})`;
    const sort_index = formatOrderColumn(group_index);

    group_index = formatOrderColumnSelect(group_index);
    let multi_subcat_group_index = group_index;

    if (!group_field || ['status', 'subcategory'].includes(group_field)) {
        // perform the status orderindex join as a correlated subquery
        group_index = `
            coalesce(
                (
                    SELECT orderindexes.orderindex FROM task_mgmt.status_orderindexes AS orderindexes 
                    WHERE orderindexes.task_id = items.id 
                    AND orderindexes.type = 'orderindex' 
                    AND orderindexes.team_id = items.workspace_id
                    LIMIT 1
                ), ${legacy_columns.join(', ')}
            ) AS ${sort_index}`;
        orderindex_table = '';

        if (!findBoardViewOrderindexes) {
            multi_subcat_group_index = `
                coalesce(
                    (
                        SELECT orderindexes.orderindex FROM task_mgmt.status_orderindexes AS orderindexes 
                        WHERE orderindexes.task_id = items.id 
                        AND orderindexes.type = 'orderindex:' || task_subcategories.subcategory 
                        AND orderindexes.team_id = items.workspace_id
                        LIMIT 1
                    ), ${legacy_columns.join(', ')}
                ) AS ${sort_index}`;
            multi_subcat_orderindex_table = '';
        }
    }

    if (!order_columns.includes(sort_index)) {
        order_columns.push(sort_index);
    }

    const multi_subcat_order_column_names = [...order_column_names];
    if (!order_column_names.includes(group_index)) {
        order_column_names.push(group_index);
    }
    if (!multi_subcat_order_column_names.includes(multi_subcat_group_index)) {
        multi_subcat_order_column_names.push(multi_subcat_group_index);
    }

    if (!group_by_cols.includes(sort_index)) {
        group_by_cols.push(sort_index);
    }

    let subtask_filter = '';
    let multi_subtask_filter = '';

    const showSubtasksAndSIMLforWidgets =
        (widget?.type === VIEW ||
            widget?.type === PIE_CHART ||
            widget?.type === BAR_CHART ||
            widget?.type === BATTERY_CHART) &&
        settings.show_subtasks === show_subtasks_types.stand_alone;

    // if we don't want to show subtasks as separate entities, we filter them out
    if (settings.show_subtasks !== show_subtasks_types.stand_alone || showSubtasksAndSIMLforWidgets) {
        subtask_filter = showSubtasksAndSIMLforWidgets ? '' : `AND items.parent IS NULL`;
    }

    // filter for subtasks from different lists
    if (settings.show_subtasks === show_subtasks_types.stand_alone && view.exclude_sub_multiple_lists) {
        multi_subtask_filter = `
        AND (
            items.parent IS NULL OR (
                items.parent IS NOT NULL
                AND items.id != task_subcategories.task_id
                AND parent.id = task_subcategories.task_id
            )
        )
        `;
    } else {
        multi_subtask_filter = getConditionForMultipleLists(
            !view.exclude_multiple_lists,
            !view.exclude_sub_multiple_lists,
            settings.show_subtasks === show_subtasks_types.stand_alone && !view.exclude_multiple_lists,
            'AND',
            useRecursiveCTEForTaskHierarchy
        );
    }

    if (!options.include_archived) {
        if (parent.type !== parent_types.project) {
            archived_filter += ` AND projects.archived = FALSE`;
        }

        // TODO: this may not work for subfolders
        if (parent.type !== parent_types.category) {
            archived_filter += ` AND categories.archived = FALSE`;
        }

        if (parent.type !== parent_types.subcategory) {
            archived_filter += ` AND subcategories.archived = FALSE`;
        }
    } else {
        archived_filter = '';
    }

    let hide_private = ``;

    if (options.isPublic) {
        if (parent.type === parent_types.subcategory) {
            hide_private = ` AND items.private = FALSE`;
        } else if (parent.type === parent_types.category) {
            hide_private = `
                    AND items.private = FALSE
                    AND subcategories.private = FALSE`;
        } else if (parent.type === parent_types.project) {
            hide_private = `
                    AND items.private = FALSE
                    AND subcategories.private = FALSE
                    AND categories.private = FALSE`;
        } else {
            hide_private = `
                    AND items.private = FALSE
                    AND subcategories.private = FALSE
                    AND categories.private = FALSE
                    AND projects.private = FALSE`;
        }
    }

    let form_condition = ``;

    if (options.form_id) {
        form_condition = ` AND items.form_id = $${task_params.push(options.form_id)}`;
    }

    const assigneeJoinsOptions = {
        timeline_view: Number(view.type) === config.views.view_types.timeline,
        workload_view: Number(view.type) === config.views.view_types.workload,
        group_field,
        box_team: options.box_team,
        group_by_single,
        team_id,
    };

    let task_as_object_select_columns = '';
    let task_as_object_extra_filters = '';
    let task_as_object_filter_factory;
    const task_as_object_type =
        view.task_custom_type && isShadowHierarchyTaskType(view.task_custom_type) ? TaskAsObjectType.LIST : null;

    if (task_as_object_type) {
        const task_as_object_query_service = new TaskAsObjectGenericViewQueryService();
        task_as_object_filter_factory = new TaskAsObjectFilterFactory();

        task_as_object_select_columns = task_as_object_query_service.getExtraSelectColumns({
            taskAsObjectType: task_as_object_type,
        });
        task_as_object_query_service.updateOrderColumns({
            orderColumns: order_columns, // mutates, if needed
            taskAsObjectType: task_as_object_type,
        });
        task_as_object_query_service.updateGroupByColumns({
            groupByColumns: group_by_cols, // mutates, if needed
            taskAsObjectType: task_as_object_type,
        });
        task_as_object_extra_filters = task_as_object_filter_factory.getExtraTaskFilters({
            tableAlias: 'items',
            taskAsObjectType: task_as_object_type,
        });
    }

    const { assignee_from, assignee_join, assignee_filters, box_filters } = getAssigneeJoins(
        task_params,
        filters.op,
        group_by_cols,
        assigneeJoinsOptions
    );

    let task_filter = ``;
    if (filteredTaskIds != null) {
        task_filter = ` AND items.id = ANY($${task_params.push(filteredTaskIds)}) `;
    }

    const order_by = order_columns.length ? `ORDER BY ${order_columns.join(', ')}` : '';
    const group_by = group_by_cols.length ? `GROUP BY ${group_by_cols.join(', ')}` : '';
    const order_by_select = order_column_names.length ? `, ${order_column_names.join(', ')}` : ``;
    const multi_subcat_order_by_select = multi_subcat_order_column_names.length
        ? `, ${multi_subcat_order_column_names.join(', ')}`
        : ``;

    const task_query_select = `
        SELECT
            DISTINCT
            items.id,
            items.id AS task_id,
            items.name,
            items.date_created,
            items.date_updated,
            items.date_done,
            items.date_closed,
            case
                when ${
                    useIsNotBoolOptimization
                        ? 'projects.priorities IS NOT FALSE'
                        : 'projects.priorities = true OR projects.priorities IS NULL'
                }
                THEN coalesce(items.priority, 'none')
                ELSE null::text
            end as priority,
            items.subcategory::bigint,
            NULL::bigint AS added_subcat,
            subcategories.color AS subcat_color,
            items.custom_type,
            items.due_date,
            items.start_date,
            items.parent,
            items.subtask_parent,
            items.duration,
            items.duration_is_elapsed,
            items.subcategory AS home_list,
            ${encloseMainQueryInCte && useExtraWorkspaceIdCondition ? 'items.workspace_id,' : ''}
            ${`${select_time_estimate},
            ${select_points_estimate},
            statuses.type AS status_type,`}
            statuses.status AS status_name,
            statuses.orderindex AS status_index,
            statuses.color AS status_color,
            statuses.id AS status_id,
            ${getPrivacySelect(usePrivacyEgress, getGroupIdsParamNumber)}
            projects.tags
            ${cf_sort_select}
            ${widget_select}
            ${scrum_select}
            ${color_select}
            ${status_select}
            ${assignee_from}
            ${tag_select}
            ${comment_count_from}
            ${incomplete_comment_from}
            ${time_in_status_from}
            ${rollup_select}
            ${time_spent_from}
            ${time_spent_rollup_from}
            ${time_estimate_rollup_from}
            ${points_estimate_rollup_from}
            ${auto_progress_select}
            ${manual_progress_select}
            ${team_sidebar_select}
            ${order_by_select}
            ${created_by_select}
            ${cf_select}
            ${map_select}
            ${gantt_links_select}
            ${task_as_object_select_columns}`;

    const multiple_list_select = `
            SELECT
                DISTINCT
                items.id,
                items.id AS task_id,
                items.name,
                items.date_created,
                items.date_updated,
                items.date_done,
                items.date_closed,
                case
                    when timl_home_hier.priorities = true OR timl_home_hier.priorities IS NULL
                    THEN coalesce(items.priority, 'none')
                    ELSE null::text
                end as priority,
                CASE
                    WHEN items.subcategory = subcategories.id
                    THEN items.subcategory
                    ELSE NULL
                END AS subcategory,
                CASE
                    WHEN items.subcategory = subcategories.id
                    THEN null
                    ELSE task_subcategories.subcategory
                END AS added_subcat,
                subcategories.color AS subcat_color,
                items.custom_type,
                items.due_date,
                items.start_date,
                items.parent,
                items.subtask_parent,
                items.duration,
                items.duration_is_elapsed,
                items.subcategory AS home_list,
                ${encloseMainQueryInCte && useExtraWorkspaceIdCondition ? 'items.workspace_id,' : ''}
                ${`${select_time_estimate},
                ${select_points_estimate},
                statuses.type AS status_type,`}
                statuses.status AS status_name,
                statuses.orderindex AS status_index,
                statuses.color AS status_color,
                statuses.id AS status_id,
                ${getPrivacySelect(usePrivacyEgress, getGroupIdsParamNumber)}
                timl_home_hier.tags
                ${cf_sort_select}
                ${widget_select}
                ${scrum_select}
                ${color_select}
                ${status_select}
                ${assignee_from}
                ${tag_select}
                ${comment_count_from}
                ${incomplete_comment_from}
                ${time_in_status_from}
                ${rollup_select}
                ${time_spent_from}
                ${time_spent_rollup_from}
                ${time_estimate_rollup_from}
                ${points_estimate_rollup_from}
                ${auto_progress_select}
                ${manual_progress_select}
                ${team_sidebar_select}
                ${multi_subcat_order_by_select}
                ${created_by_select}
                ${cf_select}
                ${map_select}
                ${gantt_links_select}
                ${task_as_object_select_columns}`;

    const task_subcategories_join = usePrivacyEgress
        ? `
        LEFT JOIN task_mgmt.get_hierarchy_object_acls('task', $${workspaceIdParamNumber}, NULL) task_acls
            ON task_acls.object_id = COALESCE(items.parent, items.id)
        INNER JOIN task_mgmt.get_hierarchy_object_acls('list', $${workspaceIdParamNumber}, NULL) list_acls
            ON list_acls.object_id_int = items.subcategory
        INNER JOIN task_mgmt.subcategories
            ON list_acls.object_id_int = subcategories.id
    `
        : `
        INNER JOIN task_mgmt.subcategories
            ON items.subcategory = subcategories.id
    `;

    const status_join = `
        INNER JOIN task_mgmt.statuses
            ON items.status = statuses.status
            AND items.status_id = statuses.id
            AND statuses.status_group = subcategories.status_group
    `;

    // When using privacy egress, the team membership is asserted via the privacy_conditions.
    const teamMembershipJoin = usePrivacyEgress
        ? ``
        : `
        INNER JOIN task_mgmt.team_members
            ON team_members.team_id = projects.team
            AND team_members.userid = $1
    `;

    const task_query_join = `
            FROM task_mgmt.${tasksTableName} AS items
            ${task_subcategories_join}
            ${shouldUseRecurringTasksOptimization ? recurring_v2_join : ''}
            INNER JOIN task_mgmt.categories
                ON subcategories.category = categories.id
            INNER JOIN task_mgmt.projects
                ON categories.project_id = projects.id
            ${status_join}
            INNER JOIN (SELECT null::bigint as subcategory, null::numeric as orderindex)
                as task_subcategories ON 1 = 1
            INNER JOIN (SELECT null::boolean as priorities, null::boolean as tags)
                as timl_home_hier ON 1 = 1
            ${teamMembershipJoin}
            ${cf_filter_join}
            LEFT JOIN task_mgmt.${tasksTableName} AS parent
                ON items.parent = parent.id
            ${custom_fields_join}
            ${gantt_links_join}
        `;

    const showTIMLorSIMLinWidget = !view.exclude_multiple_lists && !view.exclude_sub_multiple_lists;
    const VALID_TIML_WIDGET_TYPES = [VIEW, PIE_CHART, BAR_CHART, BATTERY_CHART];
    const showTIMLforWidgetType = showTIMLorSIMLinWidget && VALID_TIML_WIDGET_TYPES.includes(widget?.type);
    const showTIMLtasks = !view.exclude_multiple_lists || !view.exclude_sub_multiple_lists || showTIMLforWidgetType;
    const maxNestingLevel = Number(config.max_nesting_level);
    const simlRecursiveCTE =
        useRecursiveCTEForTaskHierarchy && showTIMLtasks
            ? ` 
            RECURSIVE task_hierarchy AS (
                SELECT 
               		items.id as linked_item_id,
                    items.id AS task_id,
                    items.name,
                    COALESCE(items.points, 0) as points,
                    COALESCE(items.time_estimate, 0) as time_estimate,
                    items.subtask_parent,
                    TRUE as is_linked_item,
                    1 as level,
                    ARRAY[items.id] as path,
                    task_subcategories.subcategory as subcategory
                FROM task_mgmt.${tasksTableName} as items
                INNER JOIN task_mgmt.task_subcategories
                    ON task_subcategories.task_id = items.id
                ${recursiveCTEParentJoin}
                WHERE ${parent_condition}
                    AND items.subcategory != task_subcategories.subcategory
                    AND items.deleted IS NOT TRUE AND items.template IS NOT TRUE
                    AND items.archived IS NOT TRUE AND items.importing IS NOT TRUE
                    ${task_filter}
                UNION
                SELECT 
               		th.linked_item_id,
                    items.id AS task_id,
                    items.name,
                    items.points,
					items.time_estimate,
                    items.subtask_parent,
                    FALSE as is_linked_item,
                    th.level + 1,
                    th.path || items.id,
                    th.subcategory
                FROM task_mgmt.${tasksTableName} items
                INNER JOIN task_hierarchy th ON items.subtask_parent = th.task_id
                    OR (items.parent = th.task_id AND items.subtask_parent IS NULL)
                WHERE NOT items.id = ANY(th.path) AND th.level < $${task_params.push(maxNestingLevel)}
                    AND items.deleted IS NOT TRUE AND items.template IS NOT TRUE
                    AND items.archived IS NOT TRUE AND items.importing IS NOT TRUE
                    ${task_filter}
            )
        `
            : '';

    const timlTaskSubcategoriesJoin =
        useRecursiveCTEForTaskHierarchy && showTIMLtasks
            ? `INNER JOIN task_hierarchy as task_subcategories ON task_subcategories.task_id = items.id`
            : `
        INNER JOIN task_mgmt.task_subcategories
            ON (items.parent = task_subcategories.task_id OR items.id = task_subcategories.task_id)
            AND items.subcategory != task_subcategories.subcategory
    `;

    let timlTaskTableJoin = `
            task_mgmt.${tasksTableName} AS items
            ${timlTaskSubcategoriesJoin}
        `;

    if (shouldUseTimlCte(userid)) {
        const taskSubcategoriesTaskIdColumn = useRecursiveCTEForTaskHierarchy ? 'task_id' : 'id';
        timlTaskTableJoin = `
            task_subcategories
            INNER JOIN task_mgmt.${tasksTableName} AS items
                ON task_subcategories.${taskSubcategoriesTaskIdColumn} = items.id
        `;
    }

    const timlAclJoin = usePrivacyEgress
        ? `
        LEFT JOIN task_mgmt.get_hierarchy_object_acls('task', $${workspaceIdParamNumber}, NULL) task_acls
            ON task_acls.object_id = COALESCE(items.parent, items.id)
        INNER JOIN task_mgmt.get_hierarchy_object_acls('list', $${workspaceIdParamNumber}, NULL) list_acls
            ON list_acls.object_id_int = task_subcategories.subcategory
    `
        : ``;

    // Join on the home hierarchy of the timld tasks to get tags, statuses, etc.
    // A subquery reduces the number of inner joins and improves performance.
    const timlHomeHierarchyJoin = useSubqueryForTimlHomeHierarchy
        ? `
        INNER JOIN (
            SELECT
                ths.id AS subcategory_id,
                ths.status_group AS status_group,
                thp.team AS team,
                thp.priorities AS priorities,
                thp.tags AS tags
            FROM task_mgmt.subcategories AS ths
            INNER JOIN task_mgmt.categories AS thc ON ths.category = thc.id
            INNER JOIN task_mgmt.projects AS thp ON thc.project_id = thp.id
        ) AS timl_home_hier ON items.subcategory = timl_home_hier.subcategory_id
        INNER JOIN task_mgmt.statuses ON items.status = statuses.status
            AND items.status_id = statuses.id
            AND statuses.status_group = timl_home_hier.status_group
    `
        : `
        INNER JOIN task_mgmt.subcategories AS timl_home_subcat
            ON items.subcategory = timl_home_subcat.id
        INNER JOIN task_mgmt.statuses
            ON items.status = statuses.status
            AND items.status_id = statuses.id
            AND statuses.status_group = timl_home_subcat.status_group
        INNER JOIN task_mgmt.categories as timl_home_cat
            ON timl_home_subcat.category = timl_home_cat.id
        INNER JOIN task_mgmt.projects as timl_home_hier
            ON timl_home_cat.project_id = timl_home_hier.id
    `;

    // When using privacy egress, the team membership is asserted via the privacy_conditions.
    const multiListTeamMembershipJoin = usePrivacyEgress
        ? ``
        : `
        INNER JOIN task_mgmt.team_members
            ON team_members.team_id = timl_home_hier.team
            AND team_members.userid = $1
    `;

    const multiple_list_join = `
            FROM
            ${timlTaskTableJoin}
            ${shouldUseRecurringTasksOptimization ? recurring_v2_join : ''}
            ${timlAclJoin}
            INNER JOIN task_mgmt.subcategories
                ON task_subcategories.subcategory = subcategories.id
            INNER JOIN task_mgmt.categories
                ON subcategories.category = categories.id
            INNER JOIN task_mgmt.projects
                ON categories.project_id = projects.id
            ${timlHomeHierarchyJoin}
            ${multiListTeamMembershipJoin}
            ${cf_filter_join}
            LEFT JOIN task_mgmt.${tasksTableName} AS parent
                ON items.parent = parent.id
            ${custom_fields_join}
            ${gantt_links_join}
        `;

    const privacy_joins = getPrivacyJoins(limitedMemberEnabled, usePrivacyEgress);
    const projectIndexJoin = `
        LEFT OUTER JOIN task_mgmt.project_indexes ON project_indexes.userid = $1 AND project_indexes.project_id = projects.id
    `;

    const task_query_outter_join = `
            ${orderindex_table}
            ${projectIndexJoin}
            ${privacy_joins}
            ${assignee_join}
            ${date_cf_join}
            ${tag_join}
            ${filter_joins}
            ${team_sidebar_join}
            ${comment_count_join}
            ${incomplete_comment_join}
            ${rollup_join}
            ${time_in_status_join}
            ${time_spent_join}
            ${time_spent_rollup_join}
            ${time_estimate_rollup_join}
            ${points_estimate_rollup_join}
            ${points_assignees_join}
            ${recurring_join}
            ${!shouldUseRecurringTasksOptimization ? recurring_v2_join : ''}
            ${date_of_last_status_change_join}
            ${cf_effort_join}
            ${checklist_progress}
            ${subtask_progress}
            ${comment_progress}
            ${created_by_join}
            ${widget_join}
            ${cf_sort_join}
            ${sorting_join}`;

    const multiSubcatPointsEstimateRollupJoin =
        calculatePointsAndTimeRollupUsingRecursionEnabled && points_estimate_rollup_join_timl_or_siml
            ? points_estimate_rollup_join_timl_or_siml
            : points_estimate_rollup_join;
    const multiSubcatTimeEstimateRollupJoin =
        calculatePointsAndTimeRollupUsingRecursionEnabled && time_estimate_rollup_join_timl_or_siml
            ? time_estimate_rollup_join_timl_or_siml
            : time_estimate_rollup_join;
    const multiSubcatTimeSpentRollupJoin =
        calculatePointsAndTimeRollupUsingRecursionEnabled && time_spent_rollup_join_timl_or_siml
            ? time_spent_rollup_join_timl_or_siml
            : time_spent_rollup_join;
    const multi_subcat_task_query_outter_join = `
            ${multi_subcat_orderindex_table}
            ${projectIndexJoin}
            ${privacy_joins}
            ${assignee_join}
            ${date_cf_join}
            ${tag_join}
            ${filter_joins}
            ${team_sidebar_join}
            ${comment_count_join}
            ${incomplete_comment_join}
            ${rollup_join}
            ${time_spent_join}
            ${time_in_status_join}
            ${multiSubcatTimeSpentRollupJoin}
            ${multiSubcatTimeEstimateRollupJoin}
            ${multiSubcatPointsEstimateRollupJoin}
            ${points_assignees_join}
            ${recurring_join}
            ${!shouldUseRecurringTasksOptimization ? recurring_v2_join : ''}
            ${date_of_last_status_change_join}
            ${cf_effort_join}
            ${checklist_progress}
            ${subtask_progress}
            ${comment_progress}
            ${created_by_join}
            ${widget_join}
            ${cf_sort_join}
            ${sorting_join}`;
    let unassigned_query = '';
    if (options.unassigned_tasks) {
        unassigned_query = `
              AND NOT EXISTS(
                     SELECT *
                     FROM task_mgmt.group_assignees g
                     WHERE items.id = g.task_id
                )`;
    }

    let privacy_conditions;
    if (options.skip_access_results) {
        privacy_conditions = ``;
    } else {
        try {
            const extra_task_privacy_allowed_conditions = task_as_object_type
                ? task_as_object_filter_factory.getExtraTaskPrivacyAllowedConditions({
                      tableAlias: 'items',
                      taskAsObjectType: task_as_object_type,
                      scope: 'genericView',
                  })
                : '';

            privacy_conditions = await getPrivacyConditions(
                parent,
                shared_with_me,
                tasks_shared_with_me,
                team_id,
                extra_task_privacy_allowed_conditions,
                usePrivacyEgress,
                getItemsTaskListAclConditions,
                getSharedTaskListAclConditions
            );
        } catch (e) {
            series_cb(e);
            return;
        }
    }

    let task_query_conditions = `
            WHERE ${parent_condition}
                AND items.id IS NOT NULL
                ${useExtraWorkspaceIdCondition ? `AND items.workspace_id = $${workspaceIdParamNumber}` : ''}
                AND projects.template = FALSE
                AND categories.template = FALSE
                AND subcategories.template = FALSE
                AND items.template = FALSE
                AND ${
                    useIsNotBoolOptimization
                        ? 'parent.template IS NOT TRUE'
                        : '(parent.template IS NULL OR parent.template = FALSE)'
                }
                AND projects.deleted = FALSE
                AND categories.deleted = FALSE
                AND subcategories.deleted = FALSE
                AND items.deleted = FALSE
                AND ${
                    useIsNotBoolOptimization
                        ? 'projects.importing IS NOT TRUE'
                        : '(projects.importing IS NULL OR projects.importing = FALSE)'
                }
                AND ${
                    useIsNotBoolOptimization
                        ? 'categories.importing IS NOT TRUE'
                        : '(categories.importing IS NULL OR categories.importing = FALSE)'
                }
                AND ${
                    useIsNotBoolOptimization
                        ? 'subcategories.importing IS NOT TRUE'
                        : '(subcategories.importing IS NULL OR subcategories.importing = FALSE)'
                }
                AND (items.importing = FALSE)
                AND ${
                    useIsNotBoolOptimization
                        ? 'parent.deleted IS NOT TRUE'
                        : '(parent.deleted = false OR parent.id IS NULL)'
                }
                ${unassigned_query}
            ${privacy_conditions}
            ${task_as_object_extra_filters}
            ${closed_filter}
            ${archived_filter}
            ${subtask_filter}
            ${filter_conditions}
            ${assignee_filters}
            ${hide_private}
            ${form_condition}
            ${task_filter}
            ${map_conditions}`;

    let multi_task_query_conditions = `
        WHERE ${parent_condition}
            AND items.id IS NOT NULL
            ${useExtraWorkspaceIdConditionForTIML ? `AND items.workspace_id = $${workspaceIdParamNumber}` : ''}
            AND projects.template = FALSE
            AND categories.template = FALSE
            AND subcategories.template = FALSE
            AND items.template = FALSE
            AND ${
                useIsNotBoolOptimization
                    ? 'parent.template IS NOT TRUE'
                    : '(parent.template IS NULL OR parent.template = FALSE)'
            }
            AND projects.deleted = FALSE
            AND categories.deleted = FALSE
            AND subcategories.deleted = FALSE
            AND items.deleted = FALSE
            AND ${
                useIsNotBoolOptimization
                    ? 'projects.importing IS NOT TRUE'
                    : '(projects.importing IS NULL OR projects.importing = FALSE)'
            }
            AND ${
                useIsNotBoolOptimization
                    ? 'categories.importing IS NOT TRUE'
                    : '(categories.importing IS NULL OR categories.importing = FALSE)'
            }
            AND ${
                useIsNotBoolOptimization
                    ? 'subcategories.importing IS NOT TRUE'
                    : '(subcategories.importing IS NULL OR subcategories.importing = FALSE)'
            }
            AND (items.importing = FALSE)
        ${unassigned_query}
        ${privacy_conditions}
        ${task_as_object_extra_filters}
        ${closed_filter}
        ${archived_filter}
        ${multi_subtask_filter}
        ${filter_conditions}
        ${assignee_filters}
        ${task_filter}
        ${hide_private}`;

    if (box_filters.length) {
        task_query_conditions += ` AND ${box_filters}`;
        multi_task_query_conditions += ` AND ${box_filters}`;
    }

    const task_query_group_by = `
            ${group_by}`;

    let task_query_having = ``;
    if (team_sidebar_filter.length) {
        task_query_having = ` HAVING ${team_sidebar_filter}`;
    }

    const task_params_without_timl_cte = [...task_params];
    let timlCTE = ``;
    if (
        (!view.exclude_multiple_lists ||
            !view.exclude_sub_multiple_lists ||
            (widget &&
                widget.type &&
                widget.type === VIEW &&
                view.exclude_multiple_lists &&
                view.exclude_sub_multiple_lists)) &&
        shouldUseTimlCte(userid)
    ) {
        const timlCTETaskSubcategoriesJoin = useRecursiveCTEForTaskHierarchy
            ? `INNER JOIN task_hierarchy as task_subcategories ON task_subcategories.task_id = items.id`
            : `
                INNER JOIN task_mgmt.task_subcategories
                    ON (items.parent = task_subcategories.task_id OR items.id = task_subcategories.task_id)
                    AND items.subcategory != task_subcategories.subcategory
            `;
        const prefixTimCTE = useRecursiveCTEForTaskHierarchy ? '' : 'WITH';
        const timlCTEParentJoins = usePrivacyEgress
            ? `
            INNER JOIN task_mgmt.get_hierarchy_object_acls('list', $${workspaceIdParamNumber}, NULL) list_acls
                ON list_acls.object_id_int = task_subcategories.subcategory
        `
            : ``;
        timlCTE = `
            ${prefixTimCTE} task_subcategories as (
                SELECT task_subcategories.*,
                       items.id
                        FROM task_mgmt.${tasksTableName} AS items
                        ${timlCTETaskSubcategoriesJoin}
                        ${timlCTEParentJoins}
                        INNER JOIN task_mgmt.subcategories
                            ON subcategories.id = task_subcategories.subcategory
                        INNER JOIN task_mgmt.categories
                            ON subcategories.category = categories.id
                        INNER JOIN task_mgmt.projects
                            ON categories.project_id = projects.id
                    where
                        ${parent_condition}
                        ${simlRecursiveCTE ? '' : task_filter}
            )
        `;
    }

    let task_query;
    let top_level_cte_order_select;

    const peopleCFCTEsString = Object.values(peopleCFCTEs).join(',');
    if (peopleCFCTEsString) {
        tracer.addTagsToRootSpan({ new_people_cf_request: true });
        tracer.addTagsToActiveScope({ new_people_cf_request: true });
    }
    if (encloseMainQueryInCte) {
        top_level_cte_order_column_names = top_level_cte_order_column_names.filter(
            (name, idx) => top_level_cte_order_column_names.indexOf(name) === idx
        );
        top_level_cte_order_column_names = top_level_cte_order_column_names.map(name =>
            name ? formatOrderColumnSelect(name) : ''
        );
        top_level_cte_order_select = top_level_cte_order_column_names.length
            ? `, ${top_level_cte_order_column_names.join(', ')}`
            : '';
        const prefixCTE = useRecursiveCTEForTaskHierarchy ? 'WITH' : '';
        const timlCTEWith = useRecursiveCTEForTaskHierarchy ? '' : 'WITH';
        task_query = `
            ${prefixCTE}
            ${simlRecursiveCTE}${simlRecursiveCTE ? ',' : ''}
            ${timlCTE}${timlCTE ? ',' : timlCTEWith}
            ${peopleCFCTEsString}${peopleCFCTEsString ? ',' : ''}
            items AS (
                ${task_query_select}
                ${list_relationship_linked_tasks_select}
                ${task_query_join}
                ${task_query_outter_join}
                ${task_query_conditions}
                ${task_query_group_by}
                ${list_relationship_linked_tasks_group_by}
                ${task_query_having}
            )
            SELECT
                items.*
                ${top_level_cte_select}
                ${top_level_cte_order_select}
            FROM
                items
                ${top_level_cte_join}`;
    } else {
        const CTEs = useRecursiveCTEForTaskHierarchy
            ? buildGenericViewNewCTEString(timlCTE, peopleCFCTEsString, simlRecursiveCTE)
            : buildGenericViewCTEString(timlCTE, peopleCFCTEsString);

        task_query = `
            ${CTEs}
            ${task_query_select}
            ${list_relationship_linked_tasks_select}
            ${task_query_join}
            ${task_query_outter_join}
            ${task_query_conditions}
            ${task_query_group_by}
            ${list_relationship_linked_tasks_group_by}
            ${task_query_having}`;
    }

    // parent.type === parent_types.widget && view.exclude_multiple_lists
    // Condition for Task List Widget. User Turn Off Show Task In Multiple List.
    if (
        !view.exclude_multiple_lists ||
        !view.exclude_sub_multiple_lists ||
        ((widget?.type === VIEW ||
            widget?.type === PIE_CHART ||
            widget?.type === BAR_CHART ||
            widget?.type === BATTERY_CHART) &&
            showTIMLorSIMLinWidget)
    ) {
        if (encloseMainQueryInCte) {
            task_query += `
                UNION
                (WITH items AS (
                    ${multiple_list_select}
                    ${list_relationship_linked_tasks_select}
                    ${multiple_list_join}
                    ${multi_subcat_task_query_outter_join}
                    ${multi_task_query_conditions}
                    ${task_query_group_by}
                    ${list_relationship_linked_tasks_group_by}
                    ${task_query_having}
                )
                SELECT
                    items.*
                    ${top_level_cte_select}
                    ${top_level_cte_order_select}
                FROM
                    items
                    ${top_level_cte_join})`;
        } else {
            task_query += `
                UNION
                ${multiple_list_select}
                ${list_relationship_linked_tasks_select}
                ${multiple_list_join}
                ${multi_subcat_task_query_outter_join}
                ${multi_task_query_conditions}
                ${task_query_group_by}
                ${list_relationship_linked_tasks_group_by}
                ${task_query_having}`;
        }
    }
    task_query += `
            ${order_by}`;

    if ('offset' in options && Number.isInteger(Number(options.offset))) {
        task_query += `
            OFFSET ${options.offset}`;
    }

    let queryLimit;
    if ('limit' in options && Number.isInteger(Number(options.limit))) {
        queryLimit = Number(options.limit);
        task_query += `
            LIMIT ${options.limit}`;
    }

    if (options.return_query) {
        series_cb(new StopSeriesEvent({ query: task_query, params: task_params }));
        return;
    }

    let hasMoreResults = false;
    async.parallel(
        {
            task_ids(para_cb) {
                query_func(task_query, task_params, async (err, result) => {
                    if (err) {
                        logger.warn({
                            msg: 'Failed to get view',
                            view_obj: JSON.stringify(view),
                            view_task_query: task_query,
                            view_task_params: JSON.stringify(task_params),
                        });
                        para_cb(new PagingError(err, 'PAGE_003'));
                    } else {
                        tracer.addTagsToActiveScope({ rows_returned: result.rows.length });
                        tracer.addTagsToRootSpan({
                            'genericView.db_results_returned.task_ids': result.rows.length,
                        });

                        if (has_non_strict_list_relationship_filter) {
                            const tasks_to_check_access = new Set();

                            result.rows.forEach(row => {
                                Object.keys(list_relationship_filter_op_by_field_id).forEach(field_id => {
                                    const task_ids = row[`${field_id}_link_ids`] || [];

                                    task_ids.forEach(task_id => {
                                        tasks_to_check_access.add(task_id);
                                    });
                                });
                            });

                            const { task_ids } = await access.getAccessibleTasksAsync(
                                userid,
                                [...tasks_to_check_access],
                                { permissions: [] }
                            );

                            result.rows = result.rows.filter(row => {
                                const filter_result_by_field_id = {};

                                Object.keys(list_relationship_filter_op_by_field_id).forEach(field_id => {
                                    filter_result_by_field_id[field_id] = true;
                                    const linked_tasks_ids = row[`${field_id}_link_ids`] || [];
                                    const op = list_relationship_filter_op_by_field_id[field_id];

                                    if (
                                        op === 'IS SET' &&
                                        !linked_tasks_ids.some(task_id => task_ids.includes(task_id))
                                    ) {
                                        filter_result_by_field_id[field_id] = false;
                                    } else if (
                                        op === 'NOT SET' &&
                                        linked_tasks_ids.some(task_id => task_ids.includes(task_id))
                                    ) {
                                        filter_result_by_field_id[field_id] = false;
                                    }
                                });

                                return filters_bool_operator.includes('AND')
                                    ? Object.values(filter_result_by_field_id).every(Boolean)
                                    : Object.values(filter_result_by_field_id).some(Boolean);
                            });
                        }

                        if (queryLimit && result.rows.length >= queryLimit) {
                            hasMoreResults = true;
                        }
                        if (
                            (division_field !== 'subcategory' && group_field !== 'subcategory') ||
                            view.exclude_dupes ||
                            (widget &&
                                widget.type &&
                                widget.type === VIEW &&
                                view.exclude_multiple_lists &&
                                view.exclude_sub_multiple_lists)
                        ) {
                            // Condition for Task List Widget. User Turn Off Show Task In Multiple List.
                            // remove dupes
                            const indexes = {};
                            result.rows.forEach((row, idx) => {
                                let { id } = row;

                                if (row.box_userid) {
                                    id += `_${row.box_userid}`;
                                }

                                if ((group_field === 'assignee' || group_field === 'group') && group_by_single) {
                                    // treat each assignee as its own task
                                    id += `_${row.assignee}`;
                                }

                                const in_home_list = Boolean(row.subcategory);

                                if (indexes[id] == null || (!indexes[id].in_home_list && in_home_list)) {
                                    indexes[id] = {
                                        idx,
                                        in_home_list,
                                    };
                                }
                            });
                            result.rows = result.rows.filter((row, idx) => {
                                let { id } = row;

                                if (row.box_userid) {
                                    id += `_${row.box_userid}`;
                                }

                                if ((group_field === 'assignee' || group_field === 'group') && group_by_single) {
                                    id += `_${row.assignee}`;
                                }

                                return idx === (indexes[id] && indexes[id].idx);
                            });
                        }

                        if (Number(view.type) === view_types.map && Array.isArray(map_settings.field_ids)) {
                            map_settings.field_ids.forEach(field_id => {
                                const location_tasks = [];

                                result.rows.forEach(row => {
                                    const value = row[`map_cf_${field_id.replace(/-/g, '_')}`];

                                    if (!value) {
                                        return;
                                    }

                                    const task_info = {
                                        id: row.id,
                                        value,
                                    };

                                    if (map_settings.color) {
                                        if (map_settings.color === 'status') {
                                            task_info.color = row.status_color;
                                        }

                                        if (['list', 'subcategory'].includes(map_settings.color)) {
                                            task_info.color = row.subcat_color;
                                        }

                                        if (map_settings.color === 'priority') {
                                            switch (String(row.priority)) {
                                                case '1':
                                                    task_info.color = config.priorities.map[1].color;
                                                    break;
                                                case '2':
                                                    task_info.color = config.priorities.map[2].color;
                                                    break;
                                                case '3':
                                                    task_info.color = config.priorities.map[3].color;
                                                    break;
                                                case '4':
                                                    task_info.color = config.priorities.map[4].color;
                                                    break;
                                                default:
                                                    break;
                                            }
                                        }

                                        if (map_settings.color.includes('cf_')) {
                                            task_info.color = row[`${map_settings.color.replace(/-/g, '_')}_color`];
                                        }

                                        if (map_settings.color.includes('#')) {
                                            task_info.color = map_settings.color;
                                        }
                                    }

                                    location_tasks.push(task_info);
                                });

                                map_tasks[field_id] = location_tasks;
                            });
                        }

                        para_cb(null, result.rows);
                    }
                });
            },

            lastView(para_cb) {
                para_cb();
                if (!options.last_view) {
                    return;
                }

                viewHelpers.insertLastView(userid, parent, options.last_view, () => {});
            },

            async propertyGroupings(para_cb) {
                const propertyGroupingsInput = {
                    division_dir,
                    division_field,
                    fresh_req,
                    group_dir,
                    group_field,
                    limitedMemberEnabled,
                    location_filters,
                    location_filters_op,
                    options,
                    or_filter_present,
                    parent,
                    shared_with_me,
                    subcategory_statuses,
                    tasks_shared_with_me,
                    team_id,
                    timezone,
                    userid,
                    view,
                };
                const groupings = await getPropertyGroupings(propertyGroupingsInput);
                para_cb(null, groupings);
            },

            reminders(para_cb) {
                if (!options.include_reminders) {
                    para_cb();
                    return;
                }

                reminderMod.getReminderIds(
                    options.fetching_user || options.userid || userid,
                    options.reminder_options,
                    para_cb
                );
            },

            boxCalculations(para_cb) {
                if (view.type !== view_types.box) {
                    para_cb();
                    return;
                }

                let filter_out_timl = '';
                if ([parent_types.team, parent_types.project, parent_types.category].includes(parent.type)) {
                    filter_out_timl = ` AND box.added_subcat IS NULL`;
                }

                const box_params = [...task_params, config.done_status_types];

                let time_estimate = '';

                if (box_time_estimate) {
                    const time_estimate_field =
                        time_estimate_rollup && settings.show_subtasks !== show_subtasks_types.stand_alone
                            ? 'time_estimate_rollup'
                            : 'time_estimate';

                    time_estimate = `,
                            sum(box.${time_estimate_field})
                                FILTER (WHERE box.status_type != ALL($${box_params.length}) ${filter_out_timl})
                                AS incomplete_time,
                            sum(box.${time_estimate_field})
                                FILTER (WHERE box.status_type = ANY($${box_params.length}) ${filter_out_timl})
                                AS complete_time,
                            array_agg(box.id)
                                FILTER (WHERE box.time_estimate IS NULL)
                                AS without_time_estimate`;
                }

                let scrum_progress = '';

                if (box_scrum && scrum_field_access) {
                    scrum_progress = `,
                            sum(box.scrum_value) FILTER (WHERE box.status_type != ALL($${box_params.length}) ${filter_out_timl})
                                AS incomplete_scrum,
                            sum(box.scrum_value) FILTER (WHERE box.status_type = ANY($${box_params.length}) ${filter_out_timl})
                                AS complete_scrum`;
                }

                let points_estimate = '';

                if (box_points_estimate) {
                    const points_estimate_field =
                        points_estimate_rollup && settings.show_subtasks !== show_subtasks_types.stand_alone
                            ? 'points_estimate_rollup'
                            : 'points';

                    points_estimate = `,
                            sum(box.${points_estimate_field}) FILTER (WHERE box.status_type != ALL($${box_params.length}) ${filter_out_timl})
                                AS incomplete_points,
                            sum(box.${points_estimate_field}) FILTER (WHERE box.status_type = ANY($${box_params.length}) ${filter_out_timl})
                                AS complete_points,
                            array_agg(box.id) FILTER (WHERE box.points IS NULL)
                                AS without_points_estimate`;
                }

                const box_query = `
                        WITH box AS (${task_query})

                        SELECT
                            box.box_userid,
                            ${options.box_team ? `box.box_groupid,` : ``}
                            count(*) FILTER (WHERE box.status_type != ALL($${
                                box_params.length
                            }) ${filter_out_timl}) AS incomplete_tasks,
                            count(*) FILTER (WHERE box.status_type = ANY($${
                                box_params.length
                            }) ${filter_out_timl}) AS complete_tasks
                            ${time_estimate}
                            ${scrum_progress}
                            ${points_estimate}
                        FROM box
                        GROUP BY
                            box.box_userid ${options.box_team ? `,box.box_groupid` : ``}`;

                query_func(box_query, box_params, (err, result) => {
                    if (err) {
                        para_cb(new PagingError(err, 'PAGE_044'));
                        return;
                    }
                    tracer.addTagsToActiveScope({ rows_returned: result.rows.length });
                    tracer.addTagsToRootSpan({
                        'genericView.db_results_returned.box_calculations': result.rows.length,
                    });

                    const user_map = {};
                    const group_map = {};

                    result.rows.forEach(row => {
                        if (!row.box_groupid) {
                            user_map[row.box_userid] = row;
                        } else if (row.box_groupid) {
                            group_map[row.box_groupid] = row;
                        }
                    });

                    para_cb(null, { user_map, group_map });
                });
            },

            boxCaclculationsPerAssignee(para_cb) {
                if (
                    view.type !== view_types.box ||
                    ((!box_time_estimate || !estimates_per_assignee) && (!box_points_estimate || !points_per_assignee))
                ) {
                    para_cb();
                    return;
                }

                const points_estimate_roll_up_per_assignee_query = `
                    SELECT
                        box.status_type,
                        assignees.*
                    FROM box
                    JOIN task_mgmt.assignees ON assignees.task_id = box.id
                    GROUP BY assignees.task_id, assignees.userid, box.status_type`;
                let box;
                if (
                    view.type === view_types.box &&
                    points_estimate_rollup &&
                    points_per_assignee &&
                    box_points_estimate &&
                    settings.show_subtasks !== show_subtasks_types.stand_alone
                ) {
                    box = `
                        ${buildGenericViewCTEString(timlCTE, peopleCFCTEsString)}
                        ${task_query_select}
                        ${task_query_join}
                        ${task_query_outter_join}
                            WHERE ${parent_condition}
                                AND items.id IS NOT NULL
                                ${
                                    useExtraWorkspaceIdCondition
                                        ? `AND items.workspace_id = $${workspaceIdParamNumber}`
                                        : ''
                                }
                                AND projects.template = FALSE
                                AND categories.template = FALSE
                                AND subcategories.template = FALSE
                                AND items.template = FALSE
                                AND parent.template IS NOT TRUE
                                AND projects.deleted = FALSE
                                AND categories.deleted = FALSE
                                AND subcategories.deleted = FALSE
                                AND items.deleted = FALSE
                                AND projects.importing IS NOT TRUE
                                AND categories.importing IS NOT TRUE
                                AND subcategories.importing IS NOT TRUE
                                AND (items.importing = FALSE)
                                AND (parent.deleted = false OR parent.id IS NULL)
                                ${unassigned_query}
                                ${privacy_conditions}
                                ${task_as_object_extra_filters}
                                ${closed_filter}
                                ${archived_filter}
                                ${filter_conditions}
                                ${assignee_filters}
                                ${hide_private}
                                ${form_condition}
                                ${task_filter}
                                ${map_conditions}
                                ${box_filters?.length ? ` AND ${box_filters}` : ''}
                        ${task_query_group_by}
                        ${task_query_having}

                         UNION
                        ${multiple_list_select}
                        ${multiple_list_join}
                        ${multi_subcat_task_query_outter_join}
                        WHERE ${parent_condition}
                            AND items.id IS NOT NULL
                            ${
                                useExtraWorkspaceIdConditionForTIML
                                    ? `AND items.workspace_id = $${workspaceIdParamNumber}`
                                    : ''
                            }
                            AND projects.template = FALSE
                            AND categories.template = FALSE
                            AND subcategories.template = FALSE
                            AND items.template = FALSE
                            AND parent.template IS NOT TRUE
                            AND projects.deleted = FALSE
                            AND categories.deleted = FALSE
                            AND subcategories.deleted = FALSE
                            AND items.deleted = FALSE
                            AND projects.importing IS NOT TRUE
                            AND categories.importing IS NOT TRUE
                            AND subcategories.importing IS NOT TRUE
                            AND items.importing IS NOT TRUE
                            ${unassigned_query}
                            ${privacy_conditions}
                            ${task_as_object_extra_filters}
                            ${closed_filter}
                            ${archived_filter}
                            ${filter_conditions}
                            ${assignee_filters}
                            ${task_filter}
                            ${hide_private}
                            ${box_filters?.length ? ` AND ${box_filters}` : ''}
                        ${task_query_group_by}
                        ${task_query_having}
                `;
                } else {
                    box = task_query;
                }

                const box_query = `
                        WITH box AS (
                            ${box}
                        )

                       ${points_estimate_roll_up_per_assignee_query}
                    `;

                query_func(box_query, task_params, (err, result) => {
                    if (err) {
                        para_cb(new PagingError(err, 'PAGE_231'));
                        return;
                    }
                    tracer.addTagsToActiveScope({ rows_returned: result.rows.length });
                    tracer.addTagsToRootSpan({
                        'genericView.db_results_returned.box_calculation_per_assignee': result.rows.length,
                    });
                    if (!result || !result.rows) {
                        para_cb();
                        return;
                    }

                    const user_map = {};

                    let incomplete_prop = `incomplete_points`;
                    let complete_prop = `complete_points`;
                    let without_prop = `without_points_estimate`;
                    let estimate_prop = `points_float`;
                    if (box_time_estimate) {
                        incomplete_prop = `incomplete_time`;
                        complete_prop = `complete_time`;
                        without_prop = `without_time_estimate`;
                        estimate_prop = `time_estimate`;
                    }

                    result.rows.forEach(row => {
                        if (!user_map[row.userid]) {
                            user_map[row.userid] = {};
                            user_map[row.userid][incomplete_prop] = 0;
                            user_map[row.userid][complete_prop] = 0;
                            user_map[row.userid][without_prop] = [];
                        }

                        if (!row[estimate_prop]) {
                            user_map[row.userid][without_prop].push(row.task_id);
                        } else if (config.done_status_types.includes(row.status_type)) {
                            user_map[row.userid][complete_prop] += Number(row[estimate_prop]) || 0;
                        } else {
                            user_map[row.userid][incomplete_prop] += Number(row[estimate_prop]) || 0;
                        }
                    });

                    para_cb(null, user_map);
                });
            },

            assignedComments(para_cb) {
                if (!box_settings.show_assigned_comments) {
                    para_cb(null, {});
                    return;
                }

                const params = [...task_params_without_timl_cte];
                const query = `
                        ${peopleCFCTEsString ? `WITH ${peopleCFCTEsString}` : ''}
                        ${task_query_select},
                            comments.id AS comment_id,
                            comments.date,
                            comments.comment,
                            comments.comment_text,
                            task_history.id AS hist_id,
                            task_history.via,
                            users.id AS userid,
                            users.username,
                            users.email,
                            users.profile_picture_key,
                            users.color,
                            c_assignees.id AS assignee_id,
                            c_assignees.username AS assignee_username,
                            c_assignees.email AS assignee_email,
                            c_assignees.profile_picture_key AS assignee_ppk,
                            c_assignees.color AS assignee_color,
                            groups.id AS group_assignee_id,
                            groups.name AS group_assignee_name,
                            groups.handle AS group_assignee_handle,
                            groups.color AS group_assignee_color,
                            groups.avatar_id AS group_avatar_id,
                            groups.avatar_source AS group_avatar_source,
                            groups.avatar_icon AS group_avatar_icon,
                            assigned_by.id AS assigned_by_id,
                            assigned_by.username AS assigned_by_username,
                            assigned_by.email AS assigned_by_email,
                            assigned_by.profile_picture_key AS assigned_by_ppk,
                            assigned_by.color AS assigned_by_color
                            ${task_query_join}
                            INNER JOIN task_mgmt.comments
                                ON comments.task_id = items.id
                            INNER JOIN task_mgmt.task_history
                                ON task_history.task_id = items.id
                                AND task_history.task_id = comments.parent
                                AND task_history.after = comments.id::text
                                AND task_history.field = 'comment'
                            ${task_query_outter_join}
                        LEFT JOIN task_mgmt.users
                            ON users.id = comments.userid
                        LEFT JOIN task_mgmt.users AS c_assignees
                            ON comments.assignee = c_assignees.id
                        LEFT JOIN task_mgmt.groups
                            ON comments.group_assignee = groups.id
                        LEFT JOIN task_mgmt.users AS assigned_by
                            ON comments.assigned_by = assigned_by.id
                        ${task_query_conditions}
                            AND (comments.assignee IS NOT NULL OR comments.group_assignee IS NOT NULL)
                            AND comments.resolved = FALSE
                            AND comments.deleted = FALSE
                        ${task_query_group_by},
                            comments.id,
                            comments.date,
                            comments.comment,
                            comments.comment_text,
                            task_history.id,
                            task_history.via,
                            users.id,
                            users.username,
                            users.email,
                            users.profile_picture_key,
                            users.color,
                            c_assignees.id,
                            c_assignees.username,
                            c_assignees.email,
                            c_assignees.profile_picture_key,
                            c_assignees.color,
                            assigned_by.id,
                            assigned_by.username,
                            assigned_by.email,
                            assigned_by.profile_picture_key,
                            assigned_by.color,
                            groups.id,
                            groups.name,
                            groups.handle,
                            groups.color,
                            groups.avatar_source,
                            groups.avatar_icon
                        ${task_query_having}
                        ORDER BY comments.date DESC, comments.id DESC`;

                query_func(query, params, (err, result) => {
                    if (err) {
                        para_cb(new PagingError(err, 'PAGE_048'));
                        return;
                    }
                    tracer.addTagsToActiveScope({ rows_returned: result.rows.length });
                    tracer.addTagsToRootSpan({
                        'genericView.db_results_returned.assigned_comments': result.rows.length,
                    });

                    const comment_ids = result.rows.map(row => row.comment_id);
                    const comments = [];
                    const attachments_needed = [];

                    const comment_map = {};

                    result.rows.forEach(row => {
                        const assignee_id = row.assignee_id || row.group_assignee_id;
                        if (!comment_map[assignee_id]) {
                            comment_map[assignee_id] = {};
                        }

                        if (!comment_map[assignee_id][row.id]) {
                            comment_map[assignee_id][row.id] = {
                                task: {
                                    id: row.id,
                                    name: row.name,
                                    status: {
                                        status: row.status_name,
                                        color: row.status_color,
                                    },
                                    permission_level: row.permission_level,
                                },
                                comments: [],
                            };
                        }

                        const comment = {
                            id: row.comment_id,
                            date: row.date,
                            comment: row.comment,
                            hist_id: row.hist_id,
                            source: row.via,
                            text_content: row.comment_text,
                        };

                        if (row.userid) {
                            comment.user = {
                                id: row.userid,
                                username: row.username,
                                email: row.email,
                                color: row.color,
                                initials: userInitials.getInitials(row.email, row.username),
                                profilePicture: cf_sign.getCloudfrontAvatarUrl(row.profile_picture_key),
                            };
                        }

                        if (row.source_username) {
                            comment.user = {};
                            comment.user.source = row.source;
                            comment.user.username = row.source_username;
                        }

                        if (row.assignee_id) {
                            comment.resolved = row.resolved;
                            comment.assignee = {
                                id: row.assignee_id,
                                username: row.assignee_username,
                                email: row.assignee_email,
                                color: row.assignee_color,
                                initials: userInitials.getInitials(row.assignee_email, row.assignee_username),
                                profilePicture: cf_sign.getCloudfrontAvatarUrl(row.assignee_ppk),
                            };
                        }

                        if (row.group_assignee_id) {
                            comment.resolved = row.resolved;
                            comment.group_assignee = {
                                id: row.group_assignee_id,
                                name: row.group_assignee_name,
                                handle: row.group_assignee_handle,
                                initials: userInitials.getInitials(null, row.group_assignee_name),
                                avatar: {
                                    attachment: null,
                                    attachment_id: row.group_avatar_id,
                                    color: row.group_assignee_color,
                                    source: row.group_avatar_source,
                                    icon: row.group_avatar_icon,
                                },
                            };
                        }

                        if (row.assigned_by) {
                            comment.assigned_by = {
                                id: row.assigned_by_id,
                                username: row.assigned_by_username,
                                email: row.assigned_by_email,
                                color: row.assigned_by_color,
                                initials: userInitials.getInitials(row.assigned_by_email, row.assigned_by_username),
                                profilePicture: cf_sign.getCloudfrontAvatarUrl(row.assigned_by_ppk),
                            };
                        }

                        if (row.encrypted) {
                            comment.comment = encrypt.decrypt(comment.comment);
                            comment.comment = JSON.parse(comment.comment);
                        }

                        if (typeof comment.comment === 'string') {
                            comment.comment = JSON.parse(comment.comment);
                        }
                        // https://staging.clickup.com/t/333/CLK-240481
                        if (typeof comment.comment === 'string') {
                            comment.comment = JSON.parse(comment.comment);
                        }

                        comment.comment.forEach(commentPart => {
                            if (commentPart.type === 'attachment' && commentPart.attachment) {
                                attachments_needed.push(commentPart.attachment.id);
                            }
                        });

                        if (
                            !comment_map[assignee_id][row.id].comments.map(_comment => _comment.id).includes(comment.id)
                        ) {
                            comment_map[assignee_id][row.id].comments.push(comment);
                        }
                        comments.push(comment);
                    });

                    async.parallel(
                        {
                            taggedUsers(_para_cb) {
                                const q = `
                                        SELECT id, username, email, color, profile_picture_key
                                        FROM task_mgmt.users, task_mgmt.comment_tags
                                        WHERE comment_tags.comment_id = ANY($1)
                                        AND users.id = comment_tags.userid`;
                                query_func(q, [comment_ids], _para_cb);
                            },

                            attachments(_para_cb) {
                                if (attachments_needed.length === 0) {
                                    _para_cb(null, { rows: [] });
                                    return;
                                }

                                const q = `
                                        SELECT attachments.*, users.email, users.username, users.color, users.profile_picture_key
                                        FROM task_mgmt.attachments, task_mgmt.users
                                        WHERE attachments.id = ANY($1)
                                            AND attachments.deleted = false
                                            AND users.id = attachments.userid`;

                                query_func(q, [attachments_needed], _para_cb);
                            },

                            reactions(_para_cb) {
                                reactionsMod._getReactions(comment_ids, _para_cb);
                            },
                        },
                        async (paraErr, paraResult) => {
                            if (paraErr) {
                                para_cb(paraErr);
                                return;
                            }

                            const users = {};
                            const attachments = {};
                            const { reactions } = paraResult.reactions;

                            tracer.addTagsToRootSpan({
                                'genericView.db_results_returned.tagged_users': paraResult.taggedUsers.rows.length,
                                'genericView.db_results_returned.attachments': paraResult.attachments.rows.length,
                                'genericView.db_results_returned.reactions': Object.values(reactions).flat().length,
                            });

                            paraResult.taggedUsers.rows.forEach(row => {
                                users[row.id] = {
                                    id: row.id,
                                    username: row.username,
                                    email: row.email,
                                    initials: userInitials.getInitials(row.email, row.username),
                                    profilePicture: cf_sign.getCloudfrontAvatarUrl(row.profile_picture_key),
                                };
                            });

                            try {
                                for (let i = 0; i < paraResult.attachments.rows.length; i += 1) {
                                    const row = paraResult.attachments.rows[i];
                                    attachments[row.id] = await attachmentMod.formatAttachment(row);
                                }
                            } catch (e) {
                                para_cb(e);
                                return;
                            }

                            comments.forEach(comment => {
                                comment.comment.forEach(commentPart => {
                                    if (commentPart.type === 'tag' && commentPart.user) {
                                        commentPart.user = users[commentPart.user.id];
                                        if (commentPart.user && commentPart.user.username) {
                                            commentPart.text = `@${commentPart.user.username}`;
                                        } else if (commentPart.user && commentPart.user.email) {
                                            commentPart.text = `@${commentPart.user.email}`;
                                        }
                                    } else if (commentPart.type === 'attachment') {
                                        if (attachments[commentPart.attachment.id]) {
                                            commentPart.text = attachments[commentPart.attachment.id].title;
                                            commentPart.attachment = attachments[commentPart.attachment.id];
                                        } else {
                                            commentPart.attachment = null;
                                            if (!commentPart.text) {
                                                commentPart.text = '';
                                            }
                                            commentPart.text += ' (Removed)';
                                        }
                                    }
                                });

                                comment.reactions = reactions[comment.id] || [];
                            });

                            para_cb(null, comment_map);
                        }
                    );
                });
            },
        },

        (err, result) => {
            if (err) {
                series_cb(new PagingError(err, 'PAGE_025'));
            } else {
                const { propertyGroupings, ...para_result } = result;
                Object.assign(para_result, propertyGroupings);
                const list_statuses = para_result.status?.list_statuses || [];
                series_cb(null, { calculations, map_tasks, list_statuses, para_result, hasMoreResults });
            }
        }
    );
}
