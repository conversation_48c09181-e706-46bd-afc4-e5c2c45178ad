import config from 'config';
import { ObjectType, OperationType } from '@time-loop/ovm-object-version';
import { getLogger } from '@clickup/shared/utils-logging';
import { shouldUseChatCommentPermissionsForMemberAdd } from '@clickup-legacy/models/integrations/split/squadTreatments/chatTreatments';
import { getViewTypeByViewId } from '@clickup-legacy/utils/comment/db';
import { isConversationChannelView, isConversationDMView, ViewTypesConfig } from '@clickup/views/utils';
import { ChatViewTypes, isChatView, ViewType } from '@clickup/utils/constants';
import * as sqs from '../../../../utils/v1-ws-message-sending'; // eslint-disable-line import/no-cycle
import * as viewMemberDatastore from '../datastores/viewMemberDatastore';
import { isViewMember } from '../datastores/viewMemberDatastore';
import * as helpers from '../helpers';
import { ClickUpError } from '../../../../utils/errors';
import { checkAccessViewAsync } from '../../../../utils/access2';
import { queryViewsInfo } from '../../datastores/CRUD/viewDataStore';
import { ViewErrorCodes, ViewMemberErrorCodes } from '../../../../utils/errors/constants';
import { promiseProps } from '../../../../utils/promises';
import { FormattedAddedViewMember, FormattedViewMember, FormattedViewMemberRole } from '../interfaces/ViewMember';
import { getTeamId } from '../../../../utils/entities/services/entitiesService';
import { checkPermissionIfReadOnlyGuestRole } from '../../../custom_roles/services/readOnlyRole';
import { DocsAppContext } from '../../pages/DocsAppContext';
import { _promiseCheckUsersOnTeam } from '../../../team/teamMember'; // eslint-disable-line import/no-cycle
import { queryPageIdsForView } from '../../pages/datastores/pageDataStore';
import { getDefaultViewMemberPermissionLevel } from '../../viewsUtils';

const logger = getLogger('viewMember');

const ViewMemberError = ClickUpError.makeNamedError('viewMember');
const ViewsError = ClickUpError.makeNamedError('view');

async function getAccessVersionUpdates(view_id: string, team_id: number, view_type: number) {
    const versionUpdates = [
        {
            object_type: ObjectType.VIEW_ACCESS,
            object_id: view_id,
            workspace_id: team_id,
            operation: OperationType.UPDATE,
        },
    ];

    if (view_type === config.get<number>('views.view_types.doc')) {
        const { rows } = await queryPageIdsForView(view_id);
        for (const row of rows) {
            versionUpdates.push({
                object_type: ObjectType.PAGE_ACCESS,
                object_id: row.page_id,
                workspace_id: team_id,
                operation: OperationType.UPDATE,
            });
            versionUpdates.push({
                object_type: ObjectType.DOC_ACCESS,
                object_id: row.page_id,
                workspace_id: team_id,
                operation: OperationType.UPDATE,
            });
        }
    }

    return versionUpdates;
}

/**
 * Checks if the caller has permission to (share) the view, and ensures that the requested permission level is no
 * higher than their current permission level. IOW, a read-only user cannot share a view with edit permission to another
 * user.
 * @param userid - the user making the request
 * @param view_id - the view to share
 * @param target_permission_level - the permission level to give to another user when sharing
 */
async function assertCallerHasPermissionToShareView(userid: number, view_id: string, target_permission_level: number) {
    const permissionsToShareView = await getPermissionConstantForSharingView(view_id, userid);
    const accessResult = await checkAccessViewAsync(userid, view_id, {
        permissions: [permissionsToShareView],
        viewPermissions: [permissionsToShareView],
        hierarchyFallback: true,
    });
    const currentPermissionLevel = accessResult.data.permission_level;
    if (currentPermissionLevel < target_permission_level) {
        throw new ViewMemberError(
            'Cannot share view with a higher permission level than the current user',
            ViewMemberErrorCodes.CannotShareHigherPermissions,
            400
        );
    }
    return accessResult;
}

/**
 * Returns the permission constant to use when sharing a view.  By default, this is edit_view, but for chat channels
 * we allow users to invite others to the channel if they can comment.
 * @see
 * @param view_id
 * @param userid
 */
async function getPermissionConstantForSharingView(view_id: string, userid: number): Promise<string> {
    if (shouldUseChatCommentPermissionsForMemberAdd(userid)) {
        const viewType = await getViewTypeByViewId(view_id);
        const viewTypesConfig = config.get<ViewTypesConfig>('views.view_types');

        if (isConversationDMView(viewType, viewTypesConfig)) {
            logger.error({
                msg: 'Cannot invite members to a DM',
                view_id,
                userid,
                ECODE: ViewMemberErrorCodes.CannotInviteMembersToDMFail,
            });

            throw new ViewMemberError(
                'Cannot invite members to a DM',
                ViewMemberErrorCodes.CannotInviteMembersToDMFail,
                400
            );
        }

        // For chat channels, anyone who can comment in the channel has permission to invite others to the channel
        const isChatChannel = isConversationChannelView(viewType, viewTypesConfig);
        if (isChatChannel) {
            return config.get<string>('permission_constants.comment');
        }
    }
    return config.get<string>('permission_constants.edit_view');
}

export async function _getMembers(userids: number[], view_id: string): Promise<FormattedViewMember[]> {
    try {
        const result = await viewMemberDatastore.queryMembers(userids, view_id);
        if (!result.rows.length) {
            throw new ViewMemberError('Not found', ViewMemberErrorCodes.MemberNotFoundFail, 404);
        }

        return result.rows.map(row => helpers.formatMember(row));
    } catch (err) {
        throw new ViewMemberError(err, ViewMemberErrorCodes.GetMemberFail);
    }
}

export async function editMember(
    userid: number,
    view_id: string,
    target_userid: number,
    permission_level: number
): Promise<FormattedViewMember> {
    try {
        await checkPermissionIfReadOnlyGuestRole([{ userId: target_userid, permissionLevel: permission_level }], {
            view_ids: [view_id],
        });

        await assertCallerHasPermissionToShareView(userid, view_id, permission_level);
        const result = await queryViewsInfo([view_id], ['creator', 'team_id', 'type'], true);
        const { team_id, type: view_type } = result.rows?.[0] ?? {};
        const versionUpdates = await getAccessVersionUpdates(view_id, Number(team_id), view_type);
        await viewMemberDatastore.editMember(userid, view_id, target_userid, permission_level, versionUpdates);
        const member = await _getMembers([target_userid], view_id);
        sqs.sendWSMessage('sendMemberAction', ['view', 'edited', view_id, { user: member[0] }, null]);
        return member[0];
    } catch (err) {
        throw new ViewMemberError(err, ViewMemberErrorCodes.EditMemberFail);
    }
}

export async function deleteMember(userid: number, view_id: string, target_userid: number): Promise<void> {
    try {
        const access_options = {
            permissions: [] as string[],
            viewPermissions: [config.get<string>('permission_constants.edit_view')],
            hierarchyFallback: true,
        };
        const res = await checkAccessViewAsync(userid, view_id, access_options);
        const {
            data: { view_type, team_id },
        } = res;
        const versionUpdates = await getAccessVersionUpdates(view_id, Number(team_id), view_type);
        await viewMemberDatastore.deleteViewMembers(userid, view_id, [target_userid], versionUpdates);
        sqs.sendWSMessage('sendMemberAction', ['view', 'removed', view_id, target_userid, null]);
        const docsSearchCache = DocsAppContext.getDocsSearchCache();
        // run in background, only log error
        docsSearchCache
            .invalidateDocsIdsCachesAffectedBySharingChanges(Number(res.data.team_id))
            .catch(e => logger.error({ msg: 'Error invalidating docs search cache', err: e }));
    } catch (err) {
        throw new ViewMemberError(err, ViewMemberErrorCodes.DeleteMemberFail);
    }
}

export async function addMember(
    userid: number,
    view_id: string,
    target_userid: number,
    permission_level: number,
    group_id?: string
): Promise<FormattedViewMember> {
    let view_type;
    let team_id;

    await checkPermissionIfReadOnlyGuestRole([{ userId: target_userid, permissionLevel: permission_level }], {
        view_ids: [view_id],
    });
    await assertCallerHasPermissionToShareView(userid, view_id, permission_level);
    try {
        const access_result = await checkAccessViewAsync(target_userid, view_id, {
            add_member: true,
            checkJoined: false,
        });
        ({
            data: { view_type, team_id },
        } = access_result);
        await _promiseCheckUsersOnTeam([target_userid], team_id, {});
    } catch (err) {
        const error = err as { status?: number };
        if (error.status === 401) {
            if (group_id) {
                return null;
            }
            throw new ViewMemberError(
                'Member does not have access to view parent',
                ViewMemberErrorCodes.ViewParentAccessFail,
                400
            );
        }
        throw err;
    }

    try {
        const workspace_id = await getTeamId({ view_ids: [view_id] }, {});
        if (!workspace_id) {
            throw new ViewsError(
                'Cannot find team_id from view_id',
                ViewErrorCodes.CannotFindTeamIdInViewMemberDatastore
            );
        }
        const versionUpdates = await getAccessVersionUpdates(view_id, Number(team_id), view_type);

        permission_level ??= getDefaultViewMemberPermissionLevel(workspace_id, view_type);
        await viewMemberDatastore.addMember(target_userid, view_id, workspace_id, permission_level, versionUpdates);
        const result = await _getMembers([target_userid], view_id);
        const shouldInsertHistoryItem = [ViewType.doc, ...ChatViewTypes].includes(view_type);
        if (shouldInsertHistoryItem) {
            await helpers.createViewSharedHistoryItem({
                userid: target_userid,
                added_by: userid,
                team_id,
                view_id,
                permission_level,
                options: { sendNotification: view_type === ViewType.doc },
            });
        }
        sqs.sendWSMessage('sendMemberAction', ['view', 'added', view_id, { user: result[0] }, null]);
        const docsSearchCache = DocsAppContext.getDocsSearchCache();
        // run in background, only log error
        docsSearchCache
            .invalidateDocsIdsCachesAffectedBySharingChanges(workspace_id)
            .catch(e => logger.error({ msg: 'Error invalidating docs search cache', err: e }));
        return result[0];
    } catch (err) {
        throw new ViewMemberError(err, ViewMemberErrorCodes.AddMemberFail);
    }
}

export async function getMembers(view_ids: string[]): Promise<{ members: Record<string, FormattedViewMemberRole[]> }> {
    try {
        const result = await viewMemberDatastore.queryMembersByView(view_ids);
        const members: Record<string, FormattedViewMemberRole[]> = {};
        result.rows.forEach(row => {
            if (!members[row.view_id]) {
                members[row.view_id] = [];
            }

            members[row.view_id].push(helpers.formatMemberRole(row));
        });

        return { members };
    } catch (err) {
        throw new ViewMemberError(err, ViewMemberErrorCodes.GetMembersFail);
    }
}

export async function addMembers(
    userid: number,
    view_id: string,
    members: FormattedAddedViewMember[]
): Promise<{ members: FormattedViewMember[] }> {
    try {
        await checkPermissionIfReadOnlyGuestRole(
            members.map(member => ({ userId: member.id, permissionLevel: member.permission_level })),
            { view_ids: [view_id] }
        );
        const member_ids = members.map(member => member.id);
        const max_permission_level = Math.max(...members.map(member => member.permission_level));
        const result = await promiseProps({
            access: assertCallerHasPermissionToShareView(userid, view_id, max_permission_level),
            members: Promise.all([
                ...member_ids.map(id => checkAccessViewAsync(id, view_id, { add_member: true, checkJoined: false })),
            ]),
        });
        const {
            access: {
                data: { view_type, team_id },
            },
        } = result;

        await _promiseCheckUsersOnTeam(member_ids, team_id, {});
        const versionUpdates = await getAccessVersionUpdates(view_id, team_id, view_type);
        const defaultPermissionLevel = getDefaultViewMemberPermissionLevel(team_id, view_type);
        members.forEach(member => {
            member.permission_level ??= defaultPermissionLevel;
        });
        await viewMemberDatastore.addViewMembers(view_id, members, team_id, versionUpdates);

        const shouldInsertHistoryItems = (members.length === 1 && view_type === ViewType.doc) || isChatView(view_type);
        if (shouldInsertHistoryItems) {
            await helpers.createViewSharedHistoryItems({
                members,
                added_by: userid,
                team_id,
                view_id,
                options: { sendNotification: view_type === ViewType.doc },
            });
        }

        const _members = await _getMembers(member_ids, view_id);
        const docsSearchCache = DocsAppContext.getDocsSearchCache();
        // run in background, only log error
        docsSearchCache
            .invalidateDocsIdsCachesAffectedBySharingChanges(team_id)
            .catch(e => logger.error({ msg: 'Error invalidating docs search cache', err: e }));
        return { members: _members };
    } catch (err) {
        throw new ViewMemberError(err, ViewMemberErrorCodes.AddMembersFail);
    }
}

export async function editMembers(
    userid: number,
    view_id: string,
    members: FormattedAddedViewMember[]
): Promise<{ members: FormattedViewMember[] }> {
    try {
        await checkPermissionIfReadOnlyGuestRole(
            members.map(member => ({ userId: member.id, permissionLevel: member.permission_level })),
            { view_ids: [view_id] }
        );
        const member_ids = members.map(member => member.id);
        const max_permission_level = Math.max(...members.map(member => member.permission_level));
        const result = await promiseProps({
            view: queryViewsInfo([view_id], ['creator'], true),
            access: assertCallerHasPermissionToShareView(userid, view_id, max_permission_level),
        });

        const {
            access: {
                data: { view_type, team_id },
            },
        } = result;

        const versionUpdates = await getAccessVersionUpdates(view_id, Number(team_id), view_type);
        await viewMemberDatastore.editViewMembers(userid, view_id, members, versionUpdates);
        const _members = await _getMembers(member_ids, view_id);
        return { members: _members };
    } catch (err) {
        throw new ViewMemberError(err, ViewMemberErrorCodes.EditMembersFail);
    }
}

export async function deleteMembers(userid: number, view_id: string, members: number[]): Promise<void> {
    try {
        const res = await checkAccessViewAsync(userid, view_id, {
            permissions: [config.get<string>('permission_constants.edit_view')],
            viewPermissions: [config.get<string>('permission_constants.edit_view')],
            hierarchyFallback: true,
        });
        const {
            data: { view_type, team_id },
        } = res;
        const versionUpdates = await getAccessVersionUpdates(view_id, Number(team_id), view_type);
        await viewMemberDatastore.deleteViewMembers(userid, view_id, members, versionUpdates);
        const docsSearchCache = DocsAppContext.getDocsSearchCache();
        // run in background, only log error
        docsSearchCache
            .invalidateDocsIdsCachesAffectedBySharingChanges(Number(res.data.team_id))
            .catch(e => logger.error({ msg: 'Error invalidating docs search cache', err: e }));
    } catch (err) {
        throw new ViewMemberError(err, ViewMemberErrorCodes.DeleteMembersFail);
    }
}

export async function isUserBeingRemovedAsViewMember(
    userId: number,
    viewId: string,
    newViewMembers: FormattedViewMemberRole[]
): Promise<boolean> {
    if (!newViewMembers || !Array.isArray(newViewMembers)) {
        throw new ViewMemberError('Bad input for newViewMembers', ViewMemberErrorCodes.BadNewViewMembersFail, 400);
    }
    return !newViewMembers.some(nvm => nvm.user.id === userId) && isViewMember(userId, viewId);
}
