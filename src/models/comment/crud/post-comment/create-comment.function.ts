import { isHumanAssignee } from '@clickup-legacy/models/assignee';
import { getAttachmentParentRelationship } from '@clickup-legacy/models/attachment/attachmentHelpers';
import { bindAttachmentsToParentAsync } from '@clickup-legacy/models/attachment/services/updateAttachmentParent';
import { checkUserMembersAccess } from '@clickup-legacy/models/comment/auth/checkUserMembersAccess';
import { getCommentDataForWebsocketMessage } from '@clickup-legacy/models/comment/chat-websockets-utils';
import * as mentionMod from '@clickup-legacy/models/task/itemMentions';
import {
    checkUsagePaywall,
    shouldCheckChatUsagePaywall,
    updateChatUsageEntitlements,
} from '@clickup-legacy/models/comment/chatPaywallUtils';
import { logger } from '@clickup-legacy/models/comment/comment-logger.const';
import { _validateCommentTypeData } from '@clickup-legacy/models/comment/comment-type.utils';
import { getUsersWithAccessToComment } from '@clickup-legacy/models/comment/comment-user-access';
import { CommentError } from '@clickup-legacy/models/comment/comment.error';
import {
    APPROVAL_TYPE,
    ATTACHMENT_TYPE,
    CATEGORY_TYPE,
    COMMENT_TYPE,
    DOC_TYPE,
    PROJECT_TYPE,
    SUBCATEGORY_TYPE,
    TASK_TYPE,
    VIEW_TYPE,
    WHITEBOARD_TYPE,
} from '@clickup-legacy/models/comment/constants/comment-types.const';
import { parent_types } from '@clickup-legacy/models/comment/constants/parent-types.const';
import { clickBotUserId, TAGGED_USERS_OVM_LIMIT } from '@clickup-legacy/models/comment/crud-consts';
import { checkUserTaggedAccess, getFollowersToNotifyQuery } from '@clickup-legacy/models/comment/crud-utils';
import { _promiseDeleteComment } from '@clickup-legacy/models/comment/crud/delete-comment/deleteComment';
import * as dataStore from '@clickup-legacy/models/comment/datastores/commentDataStore';
import { updateCommentScheduledToSent } from '@clickup-legacy/models/comment/datastores/commentDataStore';
import * as helpers from '@clickup-legacy/models/comment/helpers';
import {
    calculateUsersInNeedOfLastReadAtUpdates,
    craftVersionUpdatesForLastReadAtUpdates,
    getGroupAtMentionedUsersWithAccess,
    makeCommentsLastReadAtUpdates,
    makeLastReadAtParentThreadCountUpdates,
    sendWebsocketsForAffectedRoomCountBadges,
    sendWebsocketsForAffectedThreadCountBadges,
} from '@clickup-legacy/models/comment/last-read-at-updates';
import { updateLatestCommentAt } from '@clickup-legacy/models/comment/latest-comments-updates';
import {
    getTaggedUsersToNotifyForPost,
    isClip,
    sendCommentAssignedNotifications,
    sendCommentNotification,
    sendCommentTagNotifications,
    shouldSendClipCommentNotification,
    shouldSendClipCommentNotificationForCreator,
} from '@clickup-legacy/models/comment/notification-utils';
import { ChatCommentAttachmentReplacementService } from '@clickup-legacy/models/comment/services/replaceChatCommentEmbedAttachments';
import { updateSourceComment } from '@clickup-legacy/models/comment/services/sendReplyToChannelUtils';
import * as threadMod from '@clickup-legacy/models/comment/thread';
import { commentTypeToParentObjectTypeMap, getNewCommentNumberAssignments } from '@clickup-legacy/models/comment/util';
import { sendWebhookMessageForThreadedComment } from '@clickup-legacy/models/comment/webhooks/sendWebhookMessageForThreadedComment';
import { checkForXssInCommentParts } from '@clickup-legacy/models/comment/xss/checkForXssInCommentParts';
import * as elasticProducer from '@clickup-legacy/models/elastic/producer';
import {
    reportChatChanged,
    reportCommentChanged,
    reportDocChanged,
} from '@clickup-legacy/models/elastic/services/change-detector/hooks';
import * as subcatFollowersMod from '@clickup-legacy/models/follower/services/subcatFollowerService';
import { defaultSharedEntityService as sharedEntityService } from '@clickup-legacy/models/shared_entity_util/services/sharedEntity.service';
import { EntitlementName, entitlementService } from '@clickup-legacy/models/entitlements/entitlementService';
import * as viewFollowersService from '@clickup-legacy/models/follower/services/viewFollowerService';
import * as userGroupMod from '@clickup-legacy/models/groups';
import { GET_GROUP_BY_HANDLE_AND_TEAM_ID } from '@clickup-legacy/models/groups/datastores/groupsDatastore';
import { allowInviteFromMention } from '@clickup-legacy/models/integrations/split/squadTreatments/accessManagementTreatments';
import {
    getBadgeCountWSConfig,
    isChatCreateUpdateDeleteDisabled,
    shouldLogChatViewLatestCommentDateChanges,
} from '@clickup-legacy/models/integrations/split/squadTreatments/chatTreatments';
import {
    getShortCircuitConfig,
    inboxCodeRefactor,
} from '@clickup-legacy/models/integrations/split/squadTreatments/inboxTreatments';
import * as link from '@clickup-legacy/models/link/services/commentLinksService';
import * as historyDatastoreService from '@clickup-legacy/models/notifications/datastores/historyDatastore';
import { getThreadFollowersAsync } from '@clickup-legacy/models/notifications/datastores/notificationFollowersDatastore';
import { postTaskCommentAddedNotification } from '@clickup-legacy/models/integrations/notifications/notifications';
import { htmlToText } from 'html-to-text';
import { sendChatMessageBadgingUpdateToSQS } from '@clickup-legacy/models/notifications/notificationUpdate/badgingUpdate';
import { _promiseCheckUsersOnTeam } from '@clickup-legacy/models/team/teamMember';
import * as getUsersDatastore from '@clickup-legacy/models/user/datastores/CRUD/getUsers';
import { isValidUserId, validateUserIds } from '@clickup-legacy/models/user/validation';
import * as viewDataStore from '@clickup-legacy/models/views/datastores/CRUD/viewDataStore';
import * as viewMembersService from '@clickup-legacy/models/views/members/services/viewMemberService';
import * as access from '@clickup-legacy/utils/access2';
import { generateNewCommentIdAsync } from '@clickup-legacy/utils/comment/commentIds';
import { CommentsClientWrapper } from '@clickup-legacy/utils/comment/CommentsClientWrapper';
import { CommentWorkspaceLookupService } from '@clickup-legacy/utils/comment/CommentWorkspaceLookup.service';
import {
    getCommentsDBContextForCommentCreation,
    isCommentIdFromCommentsDb,
    readFromCorrectCommentsDbAsync,
} from '@clickup-legacy/utils/comment/db';
import * as db from '@clickup-legacy/utils/db';
import * as encrypt from '@clickup-legacy/utils/encrypt';
import { EntitiesSupportingWatcher } from '@clickup-legacy/models/shared_entity_util/util/Constants';
import { AddEntityFollowersRequest } from '@clickup-legacy/models/shared_entity_util/dtos/addEntityFollowers.request.dto';
import { AddEntityGroupFollowersRequest } from '@clickup-legacy/models/shared_entity_util/dtos/AddEntityGroupFollowers.request.dto';
import {
    getCommentParentWorkspaceId,
    getTeamIdUsingCommentId,
} from '@clickup-legacy/utils/entities/helpers/entityHelpers';
import { GlobalTableMigrationBatchId } from '@clickup-legacy/utils/pg/interfaces/GlobalDatabaseByBatchConfig';
import * as sqsNotif from '@clickup-legacy/utils/sqs-notif';
import * as followersMod from '@clickup-legacy/models/follower/follower';
import * as sqsWs from '@clickup-legacy/utils/v1-ws-message-sending';
import { updateObjectVersionUpdatesFromHistoryRows } from '@clickup-legacy/utils/version/helpers/HistoryTableHelpers';
import * as webhook from '@clickup-legacy/utils/webhook';
import { ChatServiceUnavailableException } from '@clickup/comment/error';
import { mapEntityTypeToObjectType } from '@clickup/core';
import * as customerEmail from '@clickup/customer-email';
import { SystemUserId } from '@clickup/utils/constants/users/system-user-ids';
import type { DbQuery, QueryParams } from '@clickup/utils/db-types';
import { isConversationDMView, isConversationPrivateDMView, isConversationView } from '@clickup/views/utils';
import { sanitizeXssAttackForCommentParts } from '@time-loop/common-utils';
import {
    ObjectRelationshipType,
    ObjectType,
    ObjectVersionChangeEvent,
    ObjectVersionUpdateRequest,
    OperationType,
} from '@time-loop/ovm-object-version';

import config from 'config';
import uniqBy from 'lodash/uniqBy';
import uuid from 'node-uuid';
import type { Message } from 'nylas-v3-sdk';

import { approvalsClient } from '@clickup-legacy/clients/ApprovalsClient';
import { getUserRowFromTeamMembers } from '@clickup-legacy/models/teamMember/datastores/teamMemberDatastore';
import { shouldCommenterFollowCommentedPage } from '@clickup-legacy/models/views/pages/services/watcherSettingsService';
import { getAttachmentById } from '@clickup-legacy/models/attachment/attachment';
import { DD_SERVICE_NAME, tracer } from '@clickup/utils/metrics';
import * as pageDataStore from '@clickup-legacy/models/views/pages/datastores/pageDataStore';
import * as subcatHelper from '@clickup-legacy/models/subcategory/helpers';
import { DocsAppContext } from '@clickup-legacy/models/views/pages/DocsAppContext';
import { isTeamClip } from '@clickup-legacy/models/attachment/clipsHelpers';
import { aiBrainAsAgent } from '@clickup-legacy/models/integrations/split/squadTreatments/aiTreatments';
import { sendWebhookMessageForMentionInComment } from '@clickup-legacy/models/comment/webhooks/sendWebhookMessageForMentionInComment';
import { runInVoid } from '@clickup-legacy/utils/magic-types';
import { safeGetConfig } from '@clickup-legacy/utils/configUtil';
import { metricsClient } from '@clickup-legacy/metrics/metricsClient';
import promiseLimit from 'promise-limit';
import { checkAttachmentParentAccess } from '../../auth/checkAttachmentParentAccess';
import { _getPageContentUpdateQueries } from '../../doc-update/getPageContentUpdateQueries';
import { increment_team_limits } from '../../team/increment-team-limits.function';

const { comment: can_comment, can_delete_comments, send_email } = config.get<any>('permission_constants');

export const createComment = tracer.wrap(
    'comment.crud.post-comment-ts',
    {
        service: DD_SERVICE_NAME,
    },
    _createComment
);

interface CommentUser {
    id: number;
    username?: string;
    email?: string;
}

interface GroupMention {
    group_id: number;
    text: string;
    team_id?: number;
}

interface Attachment {
    id: string;
    title?: string;
    userid?: string | number;
}

type CommentPart = (
    | { type: 'tag'; user: CommentUser }
    | { type: 'user_mention'; user_mention: CommentUser }
    | { type: 'attachment'; attachment: Attachment }
    | { type: 'group_tag'; group_id?: number; team_id?: number; user_group?: any }
    | { type: 'assignees_tag' }
    | { type: 'followers_tag' }
    | { type: 'doc_mention'; doc_mention?: { pageTitle?: string } }
    | { type: 'here_tag' }
    | { type: 'bookmark'; bookmark: { url: string; service: string } }
    | { type: 'view_mention'; view_mention: { viewUrl: string } }
    | { type: 'chat_comment_embed' }
    | { type?: string }
) & {
    text?: string;
    user?: CommentUser;
    user_mention?: CommentUser;
    group_id?: string;
    team_id?: number;
    group_mention?: GroupMention;
    user_group?: any;
};

// TODO: if we pass wsid/ root_parent_id/type/etc - there will be a requirement to handle cases where those are not passed - automations/etc?
interface CreateCommentOptions {
    parent: string;
    comment: CommentPart[];
    attachment?: Attachment | undefined;
    attachment_ids?: string[];
    unverifiedGroupsTagged?: string[];
    usersTagged?: number[];
    email?: any;
    skip_for_import?: boolean;
    workspace_id?: number; // TODO: SHOULD BE REQUIRED - we should only be invoking this function with a workspaceId for view/thread comments ONLY - still need fallback/automations/imports/etc
    // We don't want to have to fallback to old post function/maintain two diff impls for chat!
    [key: string]: any;
}

function getAssigneeFromOptions(assignee: any): number | null {
    if (assignee === 'unassigned' || assignee === null || assignee === undefined) {
        return null;
    }

    const potentialAssignee = Number(assignee);
    if (!isValidUserId(assignee)) {
        throw new CommentError('Assignee ID invalid', 'COMM_049', 400);
    }

    return potentialAssignee;
}

export function validateCreateCommentInput(userId: number, options: CreateCommentOptions) {
    try {
        const { comment: rawComment, type } = options;

        const comment: CommentPart[] = sanitizeXssAttackForCommentParts(rawComment);

        checkForXssInCommentParts(comment);

        if (config.get<number[]>('comments.validTypes').indexOf(options.type) < 0) {
            throw new CommentError('Type not valid', 'COMM_037', 400);
        }

        const unverifiedAssigneeFromOptions = getAssigneeFromOptions(options.assignee);
        const unverifiedGroupAssigneeFromOptions = options.group_assignee;

        if (unverifiedAssigneeFromOptions && unverifiedGroupAssigneeFromOptions) {
            throw new CommentError('Only one assignee type is allowed', 'COMM_048', 400);
        }

        /**
         * @todo - align how attachments are handled between editComment and postComment
         *
         * This API currently expects the FE to pass a list of attachment IDs, but the
         * editComment endpoint extracts attachment IDs from the comment JSON blob. At
         * some point, we should align these two endpoints to use a consistent approach.
         */
        const attachmentIds: string[] =
            Array.isArray(options.attachment) && typeof options.attachment[0] === 'string'
                ? [...options.attachment_ids, ...options.attachment]
                : [];

        return {
            sanitizedComment: comment,
            unverifiedAssigneeFromOptions,
            unverifiedGroupAssigneeFromOptions,
            attachmentIds,
        };
    } catch (err: any) {
        logger.error({
            msg: 'Failed to validate comment',
            ECODE: 'COMM_304',
            err,
        });
        throw err;
    }
}

async function parseCommentBlocks(
    sanitzedComment: CommentPart[],
    options: {
        notifyAll: boolean;
        parent: string;
        userId: number;
        unverifiedUsersTaggedInComment: number[];
        unverifiedGroupsTagged: string[];
    }
) {
    const { notifyAll, parent, userId, unverifiedUsersTaggedInComment, unverifiedGroupsTagged } = options;
    let comment_text = '';
    let notify_assignees = false;
    let notify_followers = false;
    let notify_online_followers = false;
    let from_tag = false;
    let contains_chat_comment_embed = false;
    const agents_mentioned = new Set<string>();

    for (const commentPart of sanitzedComment) {
        if (commentPart.text && typeof commentPart.text === 'string') {
            commentPart.text = commentPart.text.replace(/\0/g, '').replace(/\\u0000/g, '');
        }

        if (commentPart.type === 'tag') {
            const userInfoAttributes = ['username', 'email'];
            const safeUserObjectKeys = Object.keys(commentPart.user ?? {});
            const missingFields = userInfoAttributes.filter(key => !safeUserObjectKeys.includes(key));
            if (missingFields.length && commentPart.user?.id) {
                try {
                    validateUserIds([commentPart.user.id], CommentError);
                } catch (err: any) {
                    delete commentPart.type;
                    delete commentPart.user;
                }
            }
            if (missingFields.length && commentPart.user?.id) {
                try {
                    // TODO: collect all userids and do a single fetch of all users in one go in a parallel block
                    const { rows } = await db.globalReplicaQueryAsync(
                        `SELECT ${missingFields.join(', ')} FROM task_mgmt.users WHERE id = $1`,
                        [commentPart.user.id],
                        { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_11 }
                    );
                    if (rows.length) {
                        const [user] = rows;
                        Object.keys(user).forEach(userKey => {
                            (commentPart.user as any)[userKey] = user[userKey];
                        });
                    } else {
                        logger.warn({
                            msg: 'No user info found',
                            userid: commentPart.user.id,
                            parent,
                            ECODE: 'COMM_141',
                        });
                    }
                } catch (err: any) {
                    logger.error({
                        msg: "Couldn't retrieve user info",
                        userid: commentPart.user.id,
                        parent,
                        ECODE: 'COMM_140',
                        err,
                    });
                }
            }
            if (commentPart.user?.username) {
                comment_text += `@${commentPart.user.username}`;
            } else if (commentPart.user?.email) {
                comment_text += `@${commentPart.user.email}`;
            }
            if (
                commentPart.user &&
                !unverifiedUsersTaggedInComment.includes(commentPart.user.id) &&
                (notifyAll || Number(userId) !== commentPart.user.id)
            ) {
                unverifiedUsersTaggedInComment.push(commentPart.user.id);
            }
        } else if (commentPart.type === 'user_mention') {
            const userInfoAttributes = ['username', 'email'];
            const safeUserObjectKeys = Object.keys(commentPart.user_mention ?? {});
            const missingFields = userInfoAttributes.filter(key => !safeUserObjectKeys.includes(key));

            if (missingFields.length && commentPart.user_mention?.id) {
                try {
                    const { rows } = await db.globalReplicaQueryAsync(
                        `SELECT ${missingFields.join(', ')} FROM task_mgmt.users WHERE id = $1`,
                        [commentPart.user_mention.id],
                        { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_11 }
                    );

                    if (rows.length) {
                        const [user] = rows;
                        commentPart.user = { id: commentPart.user_mention.id };
                        Object.keys(user).forEach(userKey => {
                            (commentPart.user as any)[userKey] = user[userKey];
                        });
                    } else {
                        logger.warn({
                            msg: 'No user info found from user_mention',
                            userid: commentPart.user_mention.id,
                            parent,
                            ECODE: 'COMM_142',
                        });
                    }
                } catch (err: any) {
                    logger.error({
                        msg: "Couldn't retrieve user info from user_mention",
                        userid: commentPart.user_mention.id,
                        parent,
                        ECODE: 'COMM_143',
                        err,
                    });
                }
            }
            if (commentPart.user_mention?.username) {
                comment_text += `@${commentPart.user_mention.username}`;
            } else if (commentPart.user_mention?.email) {
                comment_text += `@${commentPart.user_mention.email}`;
            }
            if (
                commentPart.user_mention &&
                !unverifiedUsersTaggedInComment.includes(commentPart.user_mention.id) &&
                (notifyAll || Number(userId) !== commentPart.user_mention.id)
            ) {
                unverifiedUsersTaggedInComment.push(commentPart.user_mention.id);
            }
        } else if (commentPart.type === 'attachment' && 'attachment' in commentPart && commentPart.attachment?.title) {
            comment_text += commentPart.attachment.title;
        } else if (commentPart.type === 'group_tag') {
            if (!commentPart.group_id && commentPart.text && commentPart.team_id) {
                const { rows } = await db.readQueryAsync(GET_GROUP_BY_HANDLE_AND_TEAM_ID, [
                    commentPart.text.replace('@', ''),
                    commentPart.team_id,
                ]);
                if (rows.length) {
                    const [firstRow] = rows;
                    commentPart.group_id = firstRow.id;
                    commentPart.user_group = firstRow;
                }
            }
            unverifiedGroupsTagged.push(commentPart.group_id);
            comment_text += commentPart.text;
            if (!commentPart.text) {
                logger.warn({
                    msg: 'No text found in group_tag',
                    commentPart,
                    parent,
                });
            }
        } else if (commentPart.type === 'assignees_tag') {
            notify_assignees = true;
            comment_text += `@assignees`;
            commentPart.text = `@assignees`;
        } else if (commentPart.type === 'followers_tag') {
            notify_followers = true;
            from_tag = true;
            comment_text += `@everyone`; // NOTE: Swapped from @watchers to @everyone
            commentPart.text =
                commentPart.text === `@followers` || commentPart.text === `@Followers` ? `@followers` : `@everyone`;
        } else if (commentPart.type === 'doc_mention' && 'doc_mention' in commentPart && commentPart.doc_mention) {
            commentPart.doc_mention.pageTitle = '';
        } else if (commentPart.type === 'here_tag') {
            notify_online_followers = true;
            from_tag = true;
            comment_text += `@here`;
            commentPart.text = `@here`;
        } else if (
            commentPart.type === 'bookmark' &&
            'bookmark' in commentPart &&
            commentPart.bookmark &&
            commentPart.bookmark.service === 'custom' &&
            commentPart.bookmark.url
        ) {
            // Add the URL of github links to the comment text
            comment_text += `${commentPart.bookmark.url.replace('https://', '')} \n `;
        } else if (
            commentPart.type === 'view_mention' &&
            'view_mention' in commentPart &&
            commentPart.view_mention &&
            commentPart.view_mention.viewUrl
        ) {
            // Add the URL of channel embed to the comment text
            comment_text += `${commentPart.view_mention.viewUrl.replace('https://', '')} \n `;
        } else if (commentPart.type === 'chat_comment_embed') {
            contains_chat_comment_embed = true;
        } else if (commentPart.type === 'brain_tag' && aiBrainAsAgent()?.enabled) {
            // TODO(bszygenda): Fix missing @Brain value in the comment_text
            // https://app.clickup-stg.com/t/8xdfmq0hz
            agents_mentioned.add(commentPart.text);
        } else {
            comment_text += commentPart.text;
        }
    }

    return {
        comment_text,
        notify_assignees,
        notify_followers,
        notify_online_followers,
        from_tag,
        contains_chat_comment_embed,
        unverifiedGroupsTagged,
        unverifiedUsersTaggedInComment,
        agents_mentioned,
    };
}

// TODO: trace wrap this - add that this is the new comment function to root span
/**
 * a typescript implementation of monolith postComment
 *
 * @param options.useClickBot - Create comment as bot, but keep all the
 * permission checks. !WARNING Client won't be able to delete/update comment.
 */
async function _createComment(
    userid: number,
    options: CreateCommentOptions
): Promise<
    | {
          queries: {
              query: DbQuery;
              params?: QueryParams;
          }[];
          success: null;
          version: ObjectVersionChangeEvent | undefined;
      }
    | {
          id: number;
          date: number;
          comment_number: number | undefined;
          email: Message | undefined;
          version: ObjectVersionChangeEvent | undefined;
          hist_id?: string;
      }
> {
    let comment_id: number | null = null;
    let user_groups_tagged: any;
    let team_id: number | undefined;
    const unverifiedWorkspaceIdFromOptions: number | undefined = options.workspace_id;
    let group_members_access: Record<number, any> = {};
    let attachment: Attachment | undefined;

    let attachment_task: string | undefined;
    let attachment_view: string | undefined;
    let comment_task: string | undefined;

    const usersWithAccess: number[] = [];
    const chatCommentAttachmentReplacementService = new ChatCommentAttachmentReplacementService();
    let verifiedAssignee: number | null = null;
    let verifiedGroupAssignee: string | null = null;
    // TODO: same pattern with groupmentions

    const {
        sanitizedComment: comment,
        unverifiedAssigneeFromOptions,
        unverifiedGroupAssigneeFromOptions,
        attachmentIds,
    } = validateCreateCommentInput(userid, options);

    const parsedCommentBlocks = await parseCommentBlocks(comment, {
        notifyAll: options.notify_all,
        parent: options.parent,
        userId: userid,
        unverifiedUsersTaggedInComment: options.usersTagged ?? [],
        unverifiedGroupsTagged: options.unverifiedGroupsTagged ?? [],
    });

    const {
        comment_text,
        notify_assignees,
        notify_followers,
        notify_online_followers,
        from_tag,
        contains_chat_comment_embed,
        unverifiedGroupsTagged,
        agents_mentioned,
    } = parsedCommentBlocks;

    let { unverifiedUsersTaggedInComment } = parsedCommentBlocks;

    let assignee_access_func;

    switch (options.type) {
        case TASK_TYPE:
            assignee_access_func = access.checkAccessTaskAsync;
            break;
        case PROJECT_TYPE:
            assignee_access_func = access.checkAccessProjectAsync;
            break;
        case CATEGORY_TYPE:
            assignee_access_func = access.checkAccessCategoryAsync;
            break;
        case SUBCATEGORY_TYPE:
            assignee_access_func = access.checkAccessSubcategoryAsync;
            break;
        case WHITEBOARD_TYPE:
        case VIEW_TYPE:
            assignee_access_func = access.checkAccessViewAsync;
            break;
        case COMMENT_TYPE:
            assignee_access_func = access.checkAccessCommentAsync;
            break;
        case DOC_TYPE:
            assignee_access_func = access.checkAccessPageAsync;
            break;
        case ATTACHMENT_TYPE:
            assignee_access_func = access.checkAccessAttachmentAsync;
            break;
        case APPROVAL_TYPE:
            assignee_access_func = async (assignee: number, parent: string, _: any) => {
                let result: any;
                try {
                    result = await approvalsClient.getApproval(assignee, unverifiedWorkspaceIdFromOptions, parent);
                } catch (err: any) {
                    throw new CommentError(err, 'COMM_072');
                }
                if (!result) {
                    throw new CommentError('Parent Not Found', 'COMM_071', 400);
                }
            };
            break;
        default:
            throw new CommentError('Invalid type', 'COMM_045');
    }

    let userWantsToFollowDocAfterCommenting = true;
    const isParentCommentIdFromCommentsDb = isCommentIdFromCommentsDb(options.parent);
    const new_group_followers: string[] = [];

    // TODO: Can kill if we pass workspaceId in from options - something like chat_workspace_id, know its chat + its wsid
    let workspaceIdFromCommentWorkspaceLookupService: number | undefined;
    if (COMMENT_TYPE === options.type && isParentCommentIdFromCommentsDb) {
        try {
            // Tables in new comments DB are partitioned by workspace ID
            workspaceIdFromCommentWorkspaceLookupService = await (
                await CommentWorkspaceLookupService.getInstance()
            ).getForCommentId(options.parent);
        } catch (err: any) {
            logger.error({
                msg: 'Failed to get workspace id from CommentWorkspaceLookupService',
                ECODE: 'COMM_069',
                commentId: options.parent,
                err,
            });
        }
    }

    // PARALLEL START
    const parallelSteps = {
        checkDuplicate: async () => {
            if (!options.trigger_id || !options.auto_id || !options.action_id) {
                return null;
            }
            // check for duplicates due to reliablequeue
            const result = await db.replicaQueryAsync(
                `
                    SELECT data
                    FROM task_mgmt.task_history t
                    WHERE
                        t.task_id = $1 AND
                        t.field = 'comment' AND
                        t.userid = -1 AND
                        data @> $2 AND
                        data @> $3 AND
                        data @> $4`,
                [
                    options.parent,
                    JSON.stringify({ trigger_id: options.trigger_id }),
                    JSON.stringify({ auto_id: options.auto_id }),
                    JSON.stringify({ action_id: options.action_id }),
                ]
            );

            return result.rows;
        },

        accessParent: async () => {
            if (options.skipAccess) {
                return null;
            }

            const permissions = [can_comment];

            if (options && options.email) {
                permissions.push(send_email);
            }

            let accessResult;
            let attachmentAccessResult;

            switch (options.type) {
                case WHITEBOARD_TYPE:
                case VIEW_TYPE:
                    accessResult = await access.promiseCheckAccessView(userid, options.parent, {
                        permissions,
                        viewPermissions: [],
                        exclude_deleted: true,
                    });
                    break;

                case COMMENT_TYPE:
                    accessResult = await access.checkAccessCommentAsync(userid, options.parent, {
                        permissions,
                        check_parent_not_comment: true,
                        include_view_id: true,
                        viewPermissions: permissions,
                        exclude_deleted: true,
                    });
                    break;

                case TASK_TYPE:
                    accessResult = await access.checkAccessTaskAsync(userid, options.parent, { permissions });
                    break;
                case PROJECT_TYPE:
                    accessResult = await access.checkAccessProjectAsync(userid, options.parent, { permissions });
                    break;
                case CATEGORY_TYPE:
                    accessResult = await access.checkAccessCategoryAsync(userid, options.parent, { permissions });
                    break;
                case SUBCATEGORY_TYPE:
                    accessResult = await access.checkAccessSubcategoryAsync(userid, options.parent, {
                        permissions,
                    });
                    break;

                case ATTACHMENT_TYPE:
                    attachmentAccessResult = await checkAttachmentParentAccess(userid, options.parent, {
                        permissions,
                    });

                    team_id = attachmentAccessResult.team_id;
                    attachment_view = attachmentAccessResult.attachment_view;
                    attachment_task = attachmentAccessResult.attachment_task;
                    comment_task = attachmentAccessResult.comment_task;

                    // Sad
                    accessResult = attachmentAccessResult.result;

                    break;

                case DOC_TYPE:
                    accessResult = await access.checkAccessPageAsync(userid, options.parent, {
                        permissions,
                        viewPermissions: permissions,
                        include_view_id: true,
                    });
                    break;
                case APPROVAL_TYPE: {
                    try {
                        accessResult = await approvalsClient.getApproval(
                            userid,
                            unverifiedWorkspaceIdFromOptions,
                            options.parent
                        );
                    } catch (err: any) {
                        throw new CommentError(err, 'COMM_073');
                    }
                    if (!accessResult) {
                        throw new CommentError('Parent Not Found', 'COMM_071', 400);
                    }
                    break;
                }
                default:
                    throw new CommentError('Invalid Type', 'COMM_046');
            }

            return accessResult;
        },

        accessAttachments: async () => {
            if (options.skipAccess) {
                return;
            }

            // temporary copy so we don't modify the original attachment_ids array
            let ids: string[] = [];
            if (attachmentIds && attachmentIds?.length > 0) {
                ids = [...attachmentIds];
            }
            if (options?.attachment?.id) {
                ids = [...ids, options.attachment.id];
            }

            if (ids.length === 0) {
                return;
            }

            await access.checkAccessAttachmentsAsync(userid, ids, { permissions: [] });
        },

        assigneeInTeam: async () => {
            if (
                options.skipAccess ||
                !unverifiedAssigneeFromOptions ||
                unverifiedAssigneeFromOptions === SystemUserId.Anyone
            ) {
                return;
            }

            try {
                if (assignee_access_func) {
                    await assignee_access_func(options.assignee, options.parent, {
                        permissions: [],
                        checkJoined: false,
                    });
                    verifiedAssignee = unverifiedAssigneeFromOptions;
                }
            } catch (err) {
                if (err) {
                    verifiedAssignee = null;
                }
            }
        },

        // TODO: if passing more info in options, this becomes validation only instead of requiring it to complete first
        teamInfo: async () => {
            switch (options.type) {
                case WHITEBOARD_TYPE:
                case VIEW_TYPE:
                    return db.replicaQueryAsync(
                        `
                                SELECT teams.id AS team, should_encrypt, views.type AS view_type, views.parent_type
                                FROM task_mgmt.views
                                INNER JOIN task_mgmt.teams
                                    ON teams.id = views.team_id
                                WHERE view_id = $1`,
                        [options.parent]
                    );
                    break;
                case COMMENT_TYPE:
                case ATTACHMENT_TYPE:
                case DOC_TYPE:
                    break;
                case PROJECT_TYPE:
                    return db.replicaQueryAsync(
                        `SELECT
                                teams.id AS team,
                                teams.should_encrypt
                            FROM
                                task_mgmt.teams,
                                task_mgmt.projects
                            WHERE
                                projects.id = $1 AND
                                projects.team = teams.id`,
                        [options.parent]
                    );
                    break;
                case CATEGORY_TYPE:
                    return db.replicaQueryAsync(
                        `SELECT
                                teams.id AS team,
                                teams.should_encrypt
                            FROM
                                task_mgmt.teams,
                                task_mgmt.projects,
                                task_mgmt.categories
                            WHERE
                                categories.id = $1 AND
                                categories.project_id = projects.id AND
                                projects.team = teams.id`,
                        [options.parent]
                    );
                    break;
                case SUBCATEGORY_TYPE:
                    return db.replicaQueryAsync(
                        `SELECT
                                teams.id AS team,
                                teams.should_encrypt
                            FROM
                                task_mgmt.teams,
                                task_mgmt.projects,
                                task_mgmt.categories,
                                task_mgmt.subcategories
                            WHERE
                                subcategories.id = $1 AND
                                subcategories.category = categories.id AND
                                categories.project_id = projects.id AND
                                projects.team = teams.id`,
                        [options.parent]
                    );
                    break;
                case APPROVAL_TYPE:
                    return db.replicaQueryAsync(
                        `SELECT
                                teams.id AS team,
                                teams.should_encrypt
                            FROM task_mgmt.teams
                            WHERE teams.id = $1`,
                        [unverifiedWorkspaceIdFromOptions]
                    );
                    break;
                case TASK_TYPE:
                default:
                    return db.replicaQueryAsync(
                        `SELECT
                                teams.id AS team,
                                teams.should_encrypt,
                                subcategory
                            FROM
                                task_mgmt.teams,
                                task_mgmt.projects,
                                task_mgmt.categories,
                                task_mgmt.subcategories,
                                task_mgmt.items
                            WHERE
                                items.id = $1 AND
                                items.subcategory = subcategories.id AND
                                subcategories.category = categories.id AND
                                categories.project_id = projects.id AND
                                projects.team = teams.id`,
                        [options.parent]
                    );
            }
            return null;
        },

        threadedCommentsClickApp: async () => {
            if (options.type !== COMMENT_TYPE) {
                return null;
            }
            return db.promiseReplicaQuery(
                `SELECT coalesce(teams.threaded_comments, true) as threaded_comments FROM task_mgmt.comments JOIN task_mgmt.items ON items.id = comments.parent JOIN task_mgmt.teams ON items.workspace_id = teams.id WHERE comments.id = $1 AND comments.type = $2`,
                [options.parent, TASK_TYPE]
            );
        },

        taggedUserGroups: async () => {
            if (!unverifiedGroupsTagged.length) {
                return;
            }
            // TODO: type this
            const result = await userGroupMod.promiseGetUserGroupsByIds(userid, unverifiedGroupsTagged, {
                skipAccess: true,
            });

            user_groups_tagged = result.groups || [];

            const group_members = uniqBy(
                user_groups_tagged.flatMap((usr: any) => usr.members),
                'id'
            );
            if (group_members && group_members.length) {
                await new Promise<void>((resolve, reject) => {
                    // TODO: This should be the authorization service.filter ...
                    checkUserMembersAccess(group_members, options, (access_err: any, access_result: any) => {
                        if (access_err) {
                            reject(access_err);
                        } else {
                            group_members_access = access_result;
                            resolve();
                        }
                    });
                });
            }
        },

        userGroups: async () => {
            if (!unverifiedGroupAssigneeFromOptions) {
                return;
            }

            const user_group = await userGroupMod.promiseGetUserGroup(userid, unverifiedGroupAssigneeFromOptions, {});
            if (user_group) {
                new_group_followers.push(user_group.id);
            }
        },

        attachmentCommentNumber: async () => {
            if (options.type !== ATTACHMENT_TYPE) {
                return;
            }

            const result = await db.replicaQueryAsync(
                'SELECT (max(coalesce(comments.comment_number, 0)) + 1) as comment_number FROM tasK_mgmt.comments WHERE parent = $1 AND type = $2 AND deleted = false',
                [options.parent, ATTACHMENT_TYPE]
            );

            // TODO: stop mutating options
            if (result.rows.length === 0) {
                options.comment_number = 1;
            } else {
                options.comment_number = Number(result.rows[0].comment_number);
            }
        },
        getEmailReplyUsersByEmail: async () => {
            if (!options.email_reply || !options.email_reply.from || !Array.isArray(options.email_reply.from)) {
                return null;
            }

            // TODO: type this
            const emails = options.email_reply.from.map((from_address: any) => from_address.email);

            const result = await getUsersDatastore.getUsersInfoByEmail(emails, ['id'], true);
            if (options.email_reply) {
                return result.rows.map(row => row.id);
            }
            return null;
        },

        threadedAttachmentTask: async () => {
            if (
                options.type === COMMENT_TYPE &&
                options.comment.some(options_comment => options_comment?.type === 'attachment')
            ) {
                const res = await db.promiseReplicaQuery(`SELECT task_id FROM task_mgmt.comments WHERE id = $1`, [
                    options.parent,
                ]);
                return res.rows[0]?.task_id;
            }
            return null;
        },

        commentTeamId: async () => {
            if (options.type === COMMENT_TYPE) {
                // TODO: How are we using comment_id here? its not defined until way later??: generateNewCommentIdAsync
                return getTeamIdUsingCommentId(+(options.parent ?? comment_id), {
                    workspaceIdCandidate: workspaceIdFromCommentWorkspaceLookupService, // Tables in new comments DB are partitioned by workspace ID
                });
            }
            return null;
        },

        // TODO: if pass root_parent_id - can skip - or at least replace with isDeleted check etc - may suffice to keep comment level access checks to do the deletion check for us
        threadParent: async () => {
            if (options.type === COMMENT_TYPE) {
                const {
                    rows: [row],
                } = await dataStore.getComment(options.parent, {
                    columns: ['type', 'parent'],
                    workspaceId: team_id, // TODO: Opinionated at this point - maybe add guard at top of function that REQUIRES wsid/rootpid/rootptype?
                });
                if (!row) {
                    throw new CommentError('Parent comment not found', 'COMM_075', 404);
                }
                return {
                    type: row.type,
                    parent: row.parent,
                };
            }
            return null;
        },

        getCommentParentWorkspaceId: async () => {
            if (COMMENT_TYPE !== options.type) {
                try {
                    return getCommentParentWorkspaceId(options.parent, options.type);
                } catch (err) {
                    logger.warn({
                        msg: 'Failed to get workspace id',
                        err,
                        parent: options.parent,
                        type: options.type,
                    });
                }
            }

            return null;
        },

        commentAttachmentParent: async () => {
            if (options.type === ATTACHMENT_TYPE) {
                try {
                    const { rows } = await getAttachmentById(options.parent);

                    if (rows.length === 0) {
                        return null;
                    }

                    const [attachment_by_id] = rows;

                    return attachment_by_id;
                } catch (error) {
                    logger.warn({
                        msg: 'Failed to get comment attachment parent by id',
                        err: error,
                        parent: options.parent,
                        type: options.type,
                    });
                }
            }
            return null;
        },

        // TODO: Move to success function - nothing has been sent yet - TX not even started at this point
        markScheduledCommentAsPosted: async () => {
            if (options.comment_scheduled_id) {
                try {
                    await updateCommentScheduledToSent(
                        options.workspace_id,
                        options.type,
                        options.parent as any, // TODO: TYPE IS WRONG, parent is not always number (thread), it can be string (viewid)
                        options.comment_scheduled_id,
                        userid,
                        clickBotUserId,
                        options?.comment_scheduled_via
                    );
                } catch (error) {
                    logger.error({
                        msg: 'Failed to mark scheduled comment as posted',
                        err: error,
                        commentScheduledId: options.comment_scheduled_id,
                    });
                }
            }
        },
    };

    const parallelStepEntries = Object.entries(parallelSteps);
    const settled = await Promise.allSettled(parallelStepEntries.map(([_, fn]) => fn()));
    const errors: any[] = [];
    const parallelTasksResult: { [K in keyof typeof parallelSteps]: Awaited<ReturnType<typeof parallelSteps[K]>> } =
        parallelStepEntries.reduce((acc, [key], idx) => {
            const outcome = settled[idx];
            if (outcome.status === 'fulfilled') {
                acc[key as keyof typeof parallelSteps] = outcome.value;
            } else {
                logger.error({
                    msg: 'Error checking user tagged access',
                    error: outcome.reason,
                    ECODE: 'COMM_300',
                });
                errors.push(outcome.reason);
            }
            return acc;
        }, {} as any);
    if (errors.length > 0) {
        throw errors[0];
    }
    // // PARALLEL END

    // TODO: root_parent_type will always be 8 no? can again pass this from v3 and just verify here
    // denormalized root parent info for optimized reply fetching by entity
    const { parent: root_parent_id, type: root_parent_type } =
        options.type === COMMENT_TYPE ? parallelTasksResult.threadParent : options;

    let teamEncrypted = false;
    let subcategory_id: number | undefined;

    let view_type: number;
    let view_parent_type: number;
    let viewMembers: { user: { id: number } }[] = [];
    let approvalRole: string | null = null;
    let is_canonical_channel: boolean | undefined;
    const chatChannelFollowers: number[] = [];

    const { threadedAttachmentTask, commentAttachmentParent } = parallelTasksResult;

    if (commentAttachmentParent) {
        attachment = commentAttachmentParent;
    }

    if (options.trigger_id && options.auto_id && options.action_id && parallelTasksResult.checkDuplicate.length) {
        throw new CommentError('Re-run of automation', 'COMM_093', 400);
    }

    const now = options.timestamp ?? new Date().getTime();
    if (
        Array.isArray(parallelTasksResult.accessParent) &&
        parallelTasksResult.accessParent.length &&
        options.type === COMMENT_TYPE
    ) {
        const workspaceIdFromAccess = parallelTasksResult.accessParent[0].team_id;
        if (team_id && team_id !== workspaceIdFromAccess) {
            throw new CommentError('Workspace ID mismatch', 'COMM_305', 400);
        }
        team_id ??= workspaceIdFromAccess;
    }

    if (
        parallelTasksResult.accessParent &&
        (parallelTasksResult.accessParent?.data?.team_id ||
            parallelTasksResult.accessParent?.data?.workspaceId ||
            parallelTasksResult.accessParent?.data?.workspace_id)
    ) {
        team_id ??=
            parallelTasksResult.accessParent?.data?.team_id ??
            parallelTasksResult.accessParent?.data?.workspaceId ??
            parallelTasksResult.accessParent?.data?.workspace_id;
    }

    if (options.type === APPROVAL_TYPE && parallelTasksResult?.accessParent?.data?.start_of_week) {
        approvalRole = parallelTasksResult.accessParent.user.id === userid ? 'submitter' : 'approver';
    }

    if (parallelTasksResult.teamInfo && parallelTasksResult.teamInfo.rows && parallelTasksResult.teamInfo.rows[0]) {
        if (parallelTasksResult.teamInfo.rows[0].team) {
            team_id ??= parallelTasksResult.teamInfo.rows[0].team;
        }
        teamEncrypted = parallelTasksResult.teamInfo.rows[0].should_encrypt;
        subcategory_id = parallelTasksResult.teamInfo.rows[0].subcategory;

        view_type = parallelTasksResult.teamInfo.rows[0].view_type;
        view_parent_type = parallelTasksResult.teamInfo.rows[0].parent_type;
    }

    if (!team_id && parallelTasksResult.getCommentParentWorkspaceId) {
        team_id ??= parallelTasksResult.getCommentParentWorkspaceId;
    }

    if (!team_id && parallelTasksResult.commentTeamId) {
        team_id ??= parallelTasksResult.commentTeamId;
    }

    // TODO: might need to remove
    team_id = +team_id;

    if (view_type) {
        is_canonical_channel = (await viewDataStore.queryCanonicalChatViews([options.parent], team_id)).length === 1;
    }

    // TODO: Skip once passing workspaceId and root_parent_id/type in options from v3
    // Threaded comments won't have root_parent_type until parallel block finishes - fetch view data
    if (!view_type && root_parent_type === VIEW_TYPE) {
        try {
            const view_info = await viewDataStore.queryViewTypeAndChatViewData(root_parent_id, team_id);
            is_canonical_channel = view_info.is_canonical_channel;
            view_type = view_info.type;
            view_parent_type = view_info.parent_type;
        } catch (viewTypeLookupErr) {
            throw new CommentError(viewTypeLookupErr, 'COMM_068');
        }
    }

    // widget comments are problematic since they are type 8 (VIEW) and view_type 8 (conversation)
    // so the view parent_type is the only differentiator
    const isWidgetComment = +(view_parent_type ?? 0) === parent_types.widget;
    const isChatComment = isConversationView(view_type);
    // TODO: Move both of these as far up as possible in validation!!!
    //
    // if (!isChatComment || isWidgetComment) {
    //     throw new CommentError('Function can only be used for non-widget Chat comments!', 'COMM_301');
    // }

    // TODO: normalize usage everywhere - string vs number vs workspaceId vs team_id - extract any logic to function
    const workspaceId = team_id;
    if (isChatComment && !isWidgetComment && isChatCreateUpdateDeleteDisabled(workspaceId)) {
        // TODO: move the very top when we have wsid from options
        logger.warn({
            msg: 'Chat is currently disabled, blocking comment creation',
            root_parent_type,
            root_parent_id,
            workspaceId,
        });
        throw new ChatServiceUnavailableException();

        if (shouldCheckChatUsagePaywall(workspaceId)) {
            await checkUsagePaywall(workspaceId.toString(), options.comment_type);
        }
    }

    let commentsDBContext: Awaited<ReturnType<typeof getCommentsDBContextForCommentCreation>> | null = null;
    try {
        commentsDBContext = await getCommentsDBContextForCommentCreation({
            commentType: options.type,
            isChatComment,
            parentCommentContext:
                options.type === COMMENT_TYPE
                    ? {
                          parentCommentId: options.parent,
                          parentCommentType: root_parent_type,
                          parentCommentParentId: root_parent_id,
                      }
                    : null,
            workspaceId,
        });
    } catch (commentsDBContextErr) {
        throw new CommentError(commentsDBContextErr, 'COMM_067');
    }

    // TODO: ADD log on queries_only usage - who/how is it being used - can remove - or swap to diff pattern?
    if (commentsDBContext && options.queries_only) {
        throw new CommentError('Cannot use queries_only with the new chat experience', 'COMM_066');
    }

    // Threaded comments ClickApp must be enabled for threaded replies except email replies.
    if (parallelTasksResult.threadedCommentsClickApp?.rows.length && !options.email_reply) {
        const [{ threaded_comments }] = parallelTasksResult.threadedCommentsClickApp.rows;
        if (!threaded_comments && options.type === COMMENT_TYPE) {
            throw new CommentError('Threaded comments ClickApp must be enabled', 'COMM_050', 400);
        }
        // TODO: move this throw directly into the threadedCommentsClickApp parallel step if needed
    }

    const from_email_user_ids = parallelTasksResult.getEmailReplyUsersByEmail;

    let view_id: string | undefined;
    let permission_level: number | undefined;

    if ((options.type === DOC_TYPE || options.type === COMMENT_TYPE) && parallelTasksResult.accessParent) {
        ({ view_id, permission_level } = Array.isArray(parallelTasksResult.accessParent)
            ? parallelTasksResult.accessParent[0]
            : parallelTasksResult.accessParent);
    }

    if (options.type === DOC_TYPE && !parallelTasksResult.accessParent && !view_id) {
        const { rows } = await pageDataStore.queryPageTeamAndDocId(options.parent);
        view_id = rows[0]?.doc_id;
    }

    // TODO: Classify if we can operate on this comment/all ops supported? has attachments? has tags? is encrypted? is forward? is X? is task/is doc/etc?
    // IF there's something we don't like - return something - that will call hte original post function - split configurable
    // TODO: ADD TO LOG CONTEXT - we're in new comment guy

    let client: CommentsClientWrapper | null = null;
    let done: () => void;

    const comment_date = options.comment_date ?? new Date().getTime();

    let versionEventsForCommentsDb: ObjectVersionChangeEvent[];
    let versionEventsForMainDb: ObjectVersionChangeEvent[];

    const sqsSendAfterCommitPromises: (() => Promise<void>)[] = [];
    const versionUpdatesForCommentsDb: ObjectVersionUpdateRequest[] = [];
    const versionUpdatesForMainDb: ObjectVersionUpdateRequest[] = [];
    const historyRows: unknown[] = [];
    const queries: {
        query: DbQuery;
        params?: QueryParams;
    }[] = [];

    const new_followers: number[] = [];
    // TODO: replace similar logic throughout - we only need to do it once!
    if (isHumanAssignee(verifiedAssignee) && verifiedAssignee !== userid) {
        if (new_followers.indexOf(verifiedAssignee) < 0) {
            new_followers.push(verifiedAssignee);
        }
    }

    if (new_followers.indexOf(userid) < 0) {
        new_followers.push(userid);
    }

    let hist_id: string | undefined;
    let sent_email: Message | undefined;
    let authorizedChatChannelFollowers: number[] = [];

    // TODO: Evaluate sequencing of these - series isn't required for all of these
    const seriesSteps: (() => Promise<any> | any)[] = [
        async function checkMentionedUsersHaveAccessToWorkspace() {
            if (!unverifiedUsersTaggedInComment.length || options.skipAccess || !allowInviteFromMention()) {
                return;
            }

            // TODO: move to validation layer
            unverifiedUsersTaggedInComment = validateUserIds(unverifiedUsersTaggedInComment, CommentError).map(Number);

            // TODO: can roll own query here? heavy import?
            await _promiseCheckUsersOnTeam(unverifiedUsersTaggedInComment, team_id, {});
        },
        async function checkMentionedUsersHaveAccessToParent() {
            const uniqueTaggedUsers = new Set(unverifiedUsersTaggedInComment);
            try {
                if (notify_followers) {
                    const query = getFollowersToNotifyQuery({
                        commentType: root_parent_type,
                        fromTag: from_tag,
                        viewType: view_type.toString(),
                        viewTypes: config.get<any>('views.view_types'), // TODO: use const lib types
                        entityTypes: config.get<any>('comment.types'), // TODO: use const lib types
                        ErrorClass: CommentError,
                    });

                    const followersToNotifyQueryResult = await db.replicaQueryAsync(query, [root_parent_id]);

                    followersToNotifyQueryResult.rows.forEach(row => uniqueTaggedUsers.add(row.userid));

                    // Add the users tagged via @everyone/@watchers to the list of users tagged in the comment
                    unverifiedUsersTaggedInComment = Array.from(uniqueTaggedUsers);
                }

                const checkUserTaggedAccessResult = await checkUserTaggedAccess(
                    Array.from(uniqueTaggedUsers),
                    options.parent,
                    assignee_access_func,
                    allowInviteFromMention()
                );

                const uniqueTaggedUsersWithAccess = new Set(checkUserTaggedAccessResult);
                usersWithAccess.push(...uniqueTaggedUsersWithAccess);
                new_followers.push(...uniqueTaggedUsersWithAccess);
            } catch (validateUsersTaggedErr) {
                logger.error({
                    msg: 'Error checking user tagged access',
                    error: validateUsersTaggedErr,
                    ECODE: 'COMM_035',
                });

                throw new CommentError('All tagged users must have access to the parent', 'COMM_035', 400);
            }
        },

        async function checkUserWantsToFollowTaskAfterCommenting() {
            const userNotificationSetting = await getUserRowFromTeamMembers([userid], team_id, [
                'follow_on_task_comment',
            ]);
            let userWantsToFollowTask = true;
            if (userNotificationSetting.rows.length) {
                userWantsToFollowTask = userNotificationSetting.rows[0].follow_on_task_comment;
            }

            // we use the same logic for following a task, subcategory, category, or project
            const userWantsToFollowAfterComment =
                userWantsToFollowTask ||
                [TASK_TYPE, SUBCATEGORY_TYPE, CATEGORY_TYPE, PROJECT_TYPE].indexOf(options.type) < 0;

            if (userWantsToFollowAfterComment && new_followers.indexOf(userid) < 0) {
                new_followers.push(userid);
            }
        },

        async function checkIfUserWantsToFollowDocAfterCommenting() {
            if (options.type === DOC_TYPE) {
                userWantsToFollowDocAfterCommenting = await shouldCommenterFollowCommentedPage(userid, team_id);
            }
        },

        // TODO: can likely do these three as well in parallel - tradeoff between doing work prior to access check/validations
        async function getViewMembers() {
            if (options.type === VIEW_TYPE && isConversationDMView(view_type)) {
                const { members } = await viewMembersService.getMembers([options.parent]);
                viewMembers = members[options.parent] || [];
            }
        },
        async function getChannelFollowers() {
            if (!isChatComment || isWidgetComment || options.type !== VIEW_TYPE) {
                return;
            }

            const channelFollowersResult = await viewFollowersService.getFollowersAsync([options.parent]);

            chatChannelFollowers.push(...(channelFollowersResult.followers?.[options.parent]?.map(u => u.id) ?? []));
        },
        async function getChatThreadFollowers() {
            if (!isChatComment || isWidgetComment || options.type !== COMMENT_TYPE) {
                return;
            }

            const threadFollowersResult = await getThreadFollowersAsync(options.parent, [], {
                workspace_id: team_id,
            });

            chatChannelFollowers.push(...threadFollowersResult.rows.map(row => row.userid));
        },

        // TODO: move to validations
        async function validateUserGroups() {
            if (!unverifiedGroupAssigneeFromOptions && !unverifiedGroupsTagged.length) {
                return;
            }

            const group_ids = [...unverifiedGroupsTagged];

            if (unverifiedGroupAssigneeFromOptions) {
                group_ids.push(unverifiedGroupAssigneeFromOptions);
            }

            try {
                for (const group_id of group_ids) {
                    if (!group_id) {
                        continue;
                    }

                    await userGroupMod.checkGroupAvailableToLocationAsync(group_id, team_id, parent_types.team, {});
                }
                verifiedGroupAssignee = unverifiedGroupAssigneeFromOptions;
            } catch (err: any) {
                // swallow
            }
        },

        async function getID() {
            if (!workspaceId) {
                logger.warn({
                    msg: 'Missing team_id for comments',
                    type: options.type,
                    parent: options.parent,
                });
            }

            comment_id = await generateNewCommentIdAsync({
                workspace_id: workspaceId.toString(),
                // If double write is on, we're creating a child comment of a comment from the main DB (BDR/PRR) and the id should start with the BDR prefix (usually 9).
                use_new_comments_db: Boolean(commentsDBContext && !commentsDBContext.doubleWrite),
            });
        },

        // TODO: make parallel - isn't even in the tx/ is just a side effect
        async function addCommentTags() {
            if (usersWithAccess.length) {
                await dataStore.addCommentTags(comment_id.toString(), team_id.toString(), usersWithAccess);
            }
        },

        async function duplicateCommentIdToNewCommentsDb() {
            // TODO: Necessary? Think so for ooooooold chats?
            if (!commentsDBContext?.doubleWrite || !workspaceId) {
                return;
            }

            // TODO: DANGER - on conflict do nothing? we could be stomping a different comment?
            const query = 'INSERT INTO task_mgmt.comments (id, workspace_id) VALUES ($1, $2) ON CONFLICT DO NOTHING';
            await db.commentsWriteQueryAsync(query, [comment_id, workspaceId]);
        },

        // TODO: Move up as far as possible
        /**
         * Workspace ID is required to efficiently query the new comments DB.
         * Because it isn't always included in requests to the Monolith API, we save a mapping of
         * comment ID -> workspace ID in cache here so we can grab it later when working with
         * the new comment DB.
         */
        async function storeCommentWorkspaceId() {
            // Only save to cache if this comment is in the new DB shard
            if (!commentsDBContext || options.skip_for_import) {
                return;
            }
            try {
                const workspaceLookupService = await CommentWorkspaceLookupService.getInstance();
                await workspaceLookupService.setForCommentId(comment_id, workspaceId);
            } catch (commentWorkspaceServiceErr) {
                logger.warn({
                    msg: 'Unable to save workspace ID in CommentWorkspaceLookupService',
                    comment_id,
                    workspace_id: workspaceId,
                    err: commentWorkspaceServiceErr,
                });
            }
        },

        // TODO: Why is this down here - should be with other validations
        // TODO: Create parallel block in the series steps for all post teamId fetch validations/checks
        async function checkEntitlement() {
            if (!team_id || !options.cloud_attachments || options.cloud_attachments.length <= 0) {
                return;
            }

            try {
                await entitlementService.checkEntitlement(team_id, EntitlementName.CloudAttachments, {
                    throwOnDeny: true,
                });
            } catch (err: any) {
                throw new CommentError(err, 'COMM_064', 400);
            }
        },

        // TODO: can move to paralell validations above?
        /**
         * Validate the subtype and any extra JSON data that is specific to the current comment type.
         * Once validated, this will set options.comment_type, options.comment_subtype_id, and options.extra.
         */
        async function validateCommentTypeData() {
            const { comment_type, comment_subtype_id, extra } = await _validateCommentTypeData({
                workspace_id: team_id.toString(),
                comment_type: options.comment_type,
                comment_type_data: options.comment_type_data,
                type: options.type,
            });
            options.comment_type = comment_type;
            options.comment_subtype_id = comment_subtype_id;
            options.extra = extra;
        },

        async function getClient() {
            if (options.queries_only) {
                return;
            }

            [client, done] = await CommentsClientWrapper.getClientWrapper({
                commentsDBContext,
                poolOptions: { label: 'post comment v2' },
            });
            logger.debug(`Getting client for post comment v2 with context ${JSON.stringify(commentsDBContext)}`);
        },

        async function begin() {
            if (options.queries_only) {
                return;
            }

            try {
                await client.beginAsync();
            } catch (err: any) {
                throw new CommentError(err, 'COMM_008');
            }
        },

        async function insert() {
            let encrypted = false;
            let in_comment = JSON.stringify(options.comment);
            let in_text = comment_text;
            let in_lower_text = comment_text.toLowerCase();

            if (config.get<boolean>('encryption.comments') && teamEncrypted) {
                encrypted = true;
                in_comment = JSON.stringify(encrypt.encrypt(JSON.stringify(in_comment)));
                in_text = encrypt.encrypt(in_text);
                if (in_lower_text) {
                    in_lower_text = encrypt.encryptLowerTextInput(in_lower_text);
                }
            }

            let query = `
                UPDATE task_mgmt.comments SET
                    parent = $1,
                    type = $2,
                    task_id = $3,
                    userid = $4,
                    date = $5,
                    comment = $6::jsonb,
                    attachment = $7,
                    x = $8,
                    y = $9,
                    assignee = $10,
                    assigned_by = $11,
                    date_assigned = $12,
                    resolved = false,
                    deleted = false,
                    comment_text = $13,
                    encrypted = $14,
                    lower_comment_text = $15,
                    image_x = $17,
                    image_y = $18,
                    comment_number = $19,
                    group_assignee = $20,
                    page = $21,
                    team_id = $22,
                    comment_time = $23,
                    referenced_content = $24,
                    comment_type = $25,
                    root_parent_id = $26,
                    root_parent_type = $27,
                    comment_category_id = $28,
                    extra = $29,
                    has_attachments = $30
                WHERE id = $16`;

            if (client.usingSeparateDbForComments && team_id) {
                query = `${query} AND workspace_id = $22`; // Tables in new comments DB are partitioned by workspace ID
            }

            /**
             * Make it easy to find comments with attachments, even when attachments might be
             * in a different DB preventing joins.
             */
            const hasAttachment = attachmentIds && attachmentIds.length > 0;

            const params = [
                options.parent,
                options.type,
                options.type === TASK_TYPE ? options.parent : null,
                options?.useClickBot ? clickBotUserId : userid,
                comment_date,
                JSON.stringify(in_comment), // TODO: we have a jsonb column, but because of the extra stringify, its essentially a text column
                options.attachment,
                options.x,
                options.y,
                verifiedAssignee,
                verifiedAssignee || verifiedGroupAssignee ? userid : null,
                comment_date,
                in_text,
                encrypted,
                in_lower_text,
                comment_id,
                options.image_x,
                options.image_y,
                options.comment_number,
                verifiedGroupAssignee,
                options.page,
                team_id,
                options.comment_time,
                options.referenced_content,
                options.comment_type,
                root_parent_id,
                root_parent_type,
                options.comment_subtype_id,
                options.extra, // TODO: Add extra for made via this new function etc - makes debugging easier/inc handling
                hasAttachment,
            ];

            if (threadedAttachmentTask) {
                // task_id
                params[2] = threadedAttachmentTask;
            }

            if (options.queries_only) {
                queries.push({ query, params });
            }

            await client.writeCommentsAsync(query, params);
        },

        // TODO: extract to function, passing options, chatChannelFollowers, wsid, viewType, oh yikes - lots...
        // TODO: refactor into two steps - 1 to get the users to update + access check, then the second for the actual update
        // TODO: could be 3 steps, from updated users -> updating versions
        async function updateCommentsLastReadAt() {
            if (
                !isChatComment ||
                isWidgetComment ||
                chatChannelFollowers.length === 0 ||
                options.queries_only ||
                options.suppress_notifications
            ) {
                return;
            }

            const badgeCountWSConfig = getBadgeCountWSConfig(workspaceId);
            const maxAuthorizedUsersToAddToWS = badgeCountWSConfig?.max_followers_to_add_to_ws ?? 0;

            let {
                usersToUpdateBadgeCount: usersToUpdateLastReadAtBadgeCount,
                usersToUpdateHasUnread: usersToUpdateLastReadAtHasUnread,
                usersToUpdateMentionCount: usersToUpdateLastReadAtMentionCount,
            } = calculateUsersInNeedOfLastReadAtUpdates(
                userid,
                view_type,
                config.get<any>('views.view_types'), // TODO: use const lib types
                viewMembers,
                chatChannelFollowers,
                usersWithAccess,
                getGroupAtMentionedUsersWithAccess(
                    user_groups_tagged ?? [],
                    Object.keys(group_members_access ?? {}).map(Number)
                ),
                options.type === COMMENT_TYPE && isParentCommentIdFromCommentsDb,
                isHumanAssignee(verifiedAssignee) && verifiedAssignee !== userid ? verifiedAssignee : undefined
            );

            const usersToUpdateLastReadAtParentThreadCount: number[] = [];

            if (options.type === COMMENT_TYPE && isParentCommentIdFromCommentsDb) {
                usersToUpdateLastReadAtParentThreadCount.push(...usersToUpdateLastReadAtHasUnread);
            }

            const authorizedUsers = await getUsersWithAccessToComment({
                rootParentId: root_parent_id,
                rootParentType: mapEntityTypeToObjectType(root_parent_type),
                userIds: Array.from(
                    new Set([
                        ...usersToUpdateLastReadAtBadgeCount,
                        ...usersToUpdateLastReadAtHasUnread,
                        ...usersToUpdateLastReadAtMentionCount,
                        ...(maxAuthorizedUsersToAddToWS > 0 ? chatChannelFollowers : []),
                    ])
                ),
                workspaceId,
            });

            // TODO: OUTSIDE SCOPE - access instead via returning + series step of named objects - alternatively, pass in via ref, mutate?
            authorizedChatChannelFollowers =
                authorizedUsers.size < maxAuthorizedUsersToAddToWS ? Array.from(authorizedUsers) : null;

            if (usersToUpdateLastReadAtHasUnread.length === 0) {
                return;
            }

            usersToUpdateLastReadAtMentionCount = usersToUpdateLastReadAtMentionCount.filter(user =>
                authorizedUsers.has(user)
            );
            usersToUpdateLastReadAtBadgeCount = usersToUpdateLastReadAtBadgeCount.filter(user =>
                authorizedUsers.has(user)
            );
            usersToUpdateLastReadAtHasUnread = usersToUpdateLastReadAtHasUnread.filter(user =>
                authorizedUsers.has(user)
            );

            let latestCommentDatePromise;
            let usersToUpdateParentThreadCountsPromise;

            // TODO: TYPING
            const usersThatGotUpdated: any[] = await makeCommentsLastReadAtUpdates({
                usersToUpdateLastReadAtHasUnread,
                usersToUpdateLastReadAtBadgeCount,
                usersToUpdateLastReadAtMentionCount,
                workspaceId,
                parentId: options.parent,
                parentType: options.type,
                parentSubType: view_type,
                defaultDate: comment_date - 1,
                dbQueryFunction: client.writeOnlyCommentsDbAsync.bind(client) as any,
            });

            if (options.type === COMMENT_TYPE) {
                usersToUpdateParentThreadCountsPromise = makeLastReadAtParentThreadCountUpdates({
                    usersToUpdateLastReadAtParentThreadCount: usersThatGotUpdated.map(row => row.user_id),
                    workspaceId,
                    threadParentId: root_parent_id,
                    threadParentType: root_parent_type,
                    threadParentSubtype: view_type,
                    defaultDate: comment_date - 1,
                    dbQueryFunction: client.writeOnlyCommentsDbAsync.bind(client) as any,
                    threadParentCommentId: +options.parent,
                });
            }

            if (commentsDBContext) {
                latestCommentDatePromise = updateLatestCommentAt({
                    workspaceId,
                    parentId: options.parent,
                    parentType: options.type,
                    date: comment_date,
                    dbQueryFunction: client.writeOnlyCommentsDbAsync.bind(client) as any,
                });
            }

            const lastReadAtFieldUpdatesByUser: any = {};
            const lastReadAtParentFieldUpdatesByUser: any = {};

            const [latestCommentDate, usersToUpdateParentThreadCounts] = await Promise.all([
                latestCommentDatePromise,
                usersToUpdateParentThreadCountsPromise,
            ]);

            if (usersToUpdateParentThreadCounts) {
                for (const row of usersToUpdateParentThreadCounts) {
                    lastReadAtParentFieldUpdatesByUser[row.user_id] = {
                        thread_count: row.thread_count,
                    };
                }

                if (badgeCountWSConfig?.thread_count) {
                    sqsSendAfterCommitPromises.push(() =>
                        sendWebsocketsForAffectedThreadCountBadges({
                            workspaceId,
                            userIds: usersToUpdateParentThreadCounts.map(row => +row.user_id),
                            sendToSQSFunction: sendChatMessageBadgingUpdateToSQS,
                        })
                    );
                }
            }

            if (options.type === VIEW_TYPE && badgeCountWSConfig?.room_count) {
                sqsSendAfterCommitPromises.push(() =>
                    sendWebsocketsForAffectedRoomCountBadges({
                        workspaceId,
                        userIds: usersThatGotUpdated.map(row => +row.user_id),
                        sendToSQSFunction: sendChatMessageBadgingUpdateToSQS,
                    })
                );
            }

            const newLatestCommentDate = latestCommentDate?.rows[0]?.latest_comment_date;

            for (const row of usersThatGotUpdated) {
                lastReadAtFieldUpdatesByUser[row.user_id] = {
                    has_unread: row.has_unread,
                    badge_count: row.badge_count,
                    mention_count: row.mention_count,
                    last_updated_at: row.last_updated_at,
                    date: row.date,
                    parent_id: options.parent,
                    parent_type: mapEntityTypeToObjectType(options.type),
                    root_parent_type: mapEntityTypeToObjectType(root_parent_type),
                    root_parent_id,
                    ...(newLatestCommentDate && { latest_comment_date: newLatestCommentDate }),
                };
            }

            // TODO: OUTSIDE SCOPE: versionUpdatesForCommentsDb - pass in via ref, mutate?

            if (Object.keys(lastReadAtFieldUpdatesByUser).length > 0) {
                versionUpdatesForCommentsDb.push(
                    ...craftVersionUpdatesForLastReadAtUpdates(
                        lastReadAtFieldUpdatesByUser,
                        team_id,
                        options.parent,
                        options.type
                    )
                );
            }

            if (Object.keys(lastReadAtParentFieldUpdatesByUser).length > 0) {
                versionUpdatesForCommentsDb.push(
                    ...craftVersionUpdatesForLastReadAtUpdates(
                        lastReadAtParentFieldUpdatesByUser,
                        team_id,
                        root_parent_id,
                        root_parent_type
                    )
                );
            }
        },

        async function registerVersionUpdates() {
            if (!team_id) {
                throw new CommentError('Missing Team Id', 'COMM_059');
            }

            const addCommentDataToVersionUpdateSizeThreshold =
                getBadgeCountWSConfig(team_id)?.send_comment_data_with_ovm_threshold_bytes;

            const commentDataForWebsocketMessage =
                isChatComment &&
                (await getCommentDataForWebsocketMessage(
                    comment,
                    addCommentDataToVersionUpdateSizeThreshold,
                    authorizedChatChannelFollowers,
                    safeGetConfig('chat_comments_via_ws.secrets'),
                    metricsClient
                ));

            // Potential to match the parallel block with an object of named promises vs an array of promises, and await the step you care about in subsequent steps!

            const versionUpdate = {
                object_type: ObjectType.COMMENT,
                object_id: comment_id.toString(),
                workspace_id: team_id,
                operation: OperationType.CREATE,
                data: {
                    context: {
                        ws_key: options.ws_key,
                        root_parent_type,
                    },
                    relationships: [
                        {
                            type: ObjectRelationshipType.COMMENT_AUTHOR,
                            object_type: ObjectType.USER,
                            object_id: userid.toString(),
                            workspace_id: team_id,
                        },
                    ],
                    changes: [
                        {
                            field: 'date_created',
                            after: comment_date,
                        },
                        ...(commentDataForWebsocketMessage || []),
                    ],
                },
            };

            if (options.comment_scheduled_id) {
                versionUpdate.data.relationships.push({
                    type: ObjectRelationshipType.SCHEDULED_COMMENT_TRIGGER,
                    object_type: ObjectType.SCHEDULED_COMMENT,
                    object_id: options.comment_scheduled_id.toString(),
                    workspace_id: team_id,
                });
            }

            if (options.type && options.parent) {
                versionUpdate.data.relationships.push({
                    type: ObjectRelationshipType.COMMENT_PARENT,
                    object_type: commentTypeToParentObjectTypeMap.get(options.type.toString()),
                    object_id: options.parent.toString(),
                    workspace_id: team_id,
                });

                if (options.type === DOC_TYPE && view_id) {
                    versionUpdate.data.relationships.push({
                        type: ObjectRelationshipType.PAGE_DOC,
                        object_type: ObjectType.DOC,
                        object_id: view_id,
                        workspace_id: team_id,
                    });
                }

                if (options.type === COMMENT_TYPE && parallelTasksResult.threadParent) {
                    const objectType = commentTypeToParentObjectTypeMap.get(
                        parallelTasksResult.threadParent.type.toString()
                    );
                    versionUpdate.data.relationships.push({
                        type: ObjectRelationshipType.THREAD_PARENT,
                        object_type: objectType,
                        object_id: parallelTasksResult.threadParent.parent.toString(),
                        workspace_id: team_id,
                    });

                    if (objectType === ObjectType.PAGE && view_id) {
                        versionUpdate.data.relationships.push({
                            type: ObjectRelationshipType.PAGE_DOC,
                            object_type: ObjectType.DOC,
                            object_id: view_id,
                            workspace_id: team_id,
                        });
                    }
                }
            }

            if (unverifiedUsersTaggedInComment.length) {
                // limit to 10 users so we don't overload ovm event size
                versionUpdate.data.relationships.push(
                    ...unverifiedUsersTaggedInComment.slice(0, TAGGED_USERS_OVM_LIMIT).map(u => ({
                        type: ObjectRelationshipType.COMMENT_TAGGED_USER,
                        object_type: ObjectType.USER,
                        object_id: u.toString(),
                        workspace_id: team_id,
                    }))
                );

                // if we did have to slice the tagged users, log a warning
                if (unverifiedUsersTaggedInComment.length > TAGGED_USERS_OVM_LIMIT) {
                    logger.warn({
                        msg: 'Tagged users limited for OVM event',
                        comment_id,
                        limit: TAGGED_USERS_OVM_LIMIT,
                    });
                }
            }

            versionUpdatesForCommentsDb.push(versionUpdate as any); // TODO: types are wrong object id is assumed ot be a number, not always true!

            if (options.type === COMMENT_TYPE && options.parent) {
                /**
                 * If we're adding a threaded comment, the parent could be from a different DB:
                 *  Child from comments Aurora (id starting with 8) and parent from BDR (id starting with 9)
                 *  In this case, we need to create a version update for the parent comment in the main db
                 */
                const versionUpdatesContainer = isParentCommentIdFromCommentsDb
                    ? versionUpdatesForCommentsDb
                    : versionUpdatesForMainDb;

                versionUpdatesContainer.push({
                    object_type: ObjectType.COMMENT,
                    object_id: options.parent.toString(),
                    workspace_id: team_id,
                    operation: OperationType.UPDATE,
                    data: {
                        context: {
                            ws_key: options.ws_key,
                            root_parent_type,
                        },
                    },
                });
            }
        },

        async function setPageContent() {
            // this section is only applicable to comment only permission when the content of the
            // document page is being sent along with the comment
            if (options.type !== DOC_TYPE || !options.page_content || permission_level !== 3) {
                return;
            }

            let contentStr =
                typeof options.page_content === 'object' ? JSON.stringify(options.page_content) : options.page_content;

            contentStr = contentStr.replace(/\* \| COMMENT_ID \| \*/g, comment_id);
            const docContentQueries = await _getPageContentUpdateQueries(userid, options.parent, contentStr);

            if (options.queries_only) {
                queries.push(...docContentQueries);
                return;
            }

            // create a new version for the updated page
            versionUpdatesForMainDb.push({
                object_type: ObjectType.PAGE,
                object_id: options.parent,
                workspace_id: team_id,
                operation: OperationType.UPDATE,
                data: {
                    context: {
                        ws_key: options.ws_key,
                        root_parent_type,
                    },
                },
            });

            for (const queryObj of docContentQueries) {
                const { query, params } = queryObj;
                await client.queryAsync(query, params);
            }
        },

        // TODO: move out of tx?
        async function insertAssigneeWeight() {
            if (!verifiedAssignee || !team_id || userid === verifiedAssignee) {
                return;
            }
            const params = [];
            let query = `
                            INSERT INTO task_mgmt.team_user_weights
                                (team_id, userid, assigned_userid, type, date) VALUES
                                (
                                    $${params.push(team_id)},
                                    $${params.push(userid)},
                                    $${params.push(verifiedAssignee)},
                                    $${params.push(config.get<number>('user_weight_type.assigned_comment'))},
                                    $${params.push(now)}
                                )
                        `;
            query = query.slice(0, -2);
            // TODO: figure out wf pass through on these results/if used anywhere
            await client.queryAsync(query, params);
        },

        // TODO: move out of tx?
        async function insertTagWeights() {
            if (!unverifiedUsersTaggedInComment || !unverifiedUsersTaggedInComment.length || !team_id) {
                return;
            }
            const users_tagged_without_self = unverifiedUsersTaggedInComment.filter(t => t !== userid);
            if (!users_tagged_without_self.length) {
                return;
            }
            const params: QueryParams = [];
            let query = `
                            INSERT INTO task_mgmt.team_user_weights
                                (team_id, userid, assigned_userid, type, date) VALUES
                        `;
            users_tagged_without_self.forEach(tag => {
                query += `(
                                $${params.push(team_id)},
                                $${params.push(userid)},
                                $${params.push(tag)},
                                $${params.push(config.get<number>('user_weight_type.mention'))},
                                $${params.push(now)}
                            ), `;
            });
            query = query.slice(0, -2);
            // TODO: figure out wf pass through on these results/if used anywhere

            await client.queryAsync(query, params);
        },

        async function question() {
            // TODO: should never happen?
            if (
                ![
                    TASK_TYPE,
                    SUBCATEGORY_TYPE,
                    CATEGORY_TYPE,
                    PROJECT_TYPE,
                    VIEW_TYPE,
                    COMMENT_TYPE,
                    DOC_TYPE,
                    ATTACHMENT_TYPE,
                    APPROVAL_TYPE,
                    WHITEBOARD_TYPE,
                ].includes(options.type)
            ) {
                return;
            }

            let query;
            let comment_data:
                | {
                      groupsTagged?: string[];
                      comment_by_email?: string;
                      email_subject?: string;
                      via?: string;
                      cc?: string;
                      trigger_id?: string;
                      auto_id?: string;
                      action_id?: string;
                  }
                | undefined;
            if (unverifiedGroupsTagged.length) {
                comment_data = { groupsTagged: unverifiedGroupsTagged };
            }

            if (options.via === 'email' && options.comment_by_email) {
                if (!comment_data) {
                    comment_data = {
                        comment_by_email: options.comment_by_email,
                        email_subject: options.email_subject,
                        via: 'email',
                        cc: options.cc,
                    };
                } else {
                    comment_data.comment_by_email = options.comment_by_email;
                    comment_data.email_subject = options.email_subject;
                    comment_data.via = 'email';
                    comment_data.cc = options.cc;
                }
            }

            if (options.trigger_id && options.auto_id && options.action_id) {
                if (!comment_data) {
                    comment_data = {
                        trigger_id: options.trigger_id,
                        auto_id: options.auto_id,
                        action_id: options.action_id,
                    };
                } else {
                    comment_data.trigger_id = options.trigger_id;
                    comment_data.auto_id = options.auto_id;
                    comment_data.action_id = options.action_id;
                }
            }

            // TODO: Needed?
            if (options.type === VIEW_TYPE && options.trigger_entity_type === TASK_TYPE) {
                query = `
                                    INSERT INTO task_mgmt.task_history(task_id, field, date, after, userid, via, data)
                                    VALUES($1, $2, $3, $4, $5, $6, $7)
                                    RETURNING id`;
                const params = [
                    options.trigger_entity_id,
                    options.action_type,
                    comment_date,
                    comment_id,
                    options.useClickBot ? clickBotUserId : userid,
                    options.via,
                    comment_data,
                ];
                if (options.queries_only) {
                    queries.push({ query, params });
                    return;
                }

                const queryFunc = client.queryAsync.bind(client);

                const res = await queryFunc(query, params);

                versionUpdatesForMainDb.push({
                    object_type: ObjectType.TASK,
                    object_id: options.trigger_entity_id,
                    workspace_id: team_id,
                    operation: OperationType.UPDATE,
                    data: {
                        context: {
                            ws_key: options.ws_key,
                        },
                    },
                });
            }
            const params = [
                options.parent,
                'comment',
                comment_date,
                comment_id,
                options.useClickBot ? clickBotUserId : userid,
                options.via,
                comment_data,
            ];

            const taskQuery = `
                    INSERT INTO task_mgmt.task_history(task_id, field, date, after, userid, via, data)
                    VALUES($1, $2, $3, $4, $5, $6, $7)
                    RETURNING id, id as cdc_history_id, field as cdc_field, task_id as cdc_object_id, 'task' as cdc_object_type`;

            const viewQuery = `
                    INSERT INTO task_mgmt.view_history(id, view_id, field, date, after, userid, via, data, workspace_id)
                    VALUES($8, $1, $2, $3, $4, $5, $6, $7, $9)
                    RETURNING id`;

            if (options.type === TASK_TYPE) {
                query = taskQuery;
            } else if (options.type === SUBCATEGORY_TYPE) {
                query = `
                            INSERT INTO task_mgmt.subcat_history(id, subcategory, field, date, after, userid, via, data, workspace_id)
                            VALUES($8, $1, $2, $3, $4, $5, $6, $7, $9)
                            RETURNING id`;
                params.push(uuid.v4(), team_id);
            } else if (options.type === CATEGORY_TYPE) {
                query = `
                            INSERT INTO task_mgmt.category_history(id, category, field, date, after, userid, via, data, workspace_id)
                            VALUES($8, $1, $2, $3, $4, $5, $6, $7, $9)
                            RETURNING id`;
                params.push(uuid.v4(), team_id);
            } else if (options.type === PROJECT_TYPE) {
                query = `
                            INSERT INTO task_mgmt.project_history(id, project, field, date, after, userid, via, data)
                            VALUES($8, $1, $2, $3, $4, $5, $6, $7)
                            RETURNING id`;
                params.push(uuid.v4());
            } else if (options.type === VIEW_TYPE || options.type === WHITEBOARD_TYPE) {
                query = viewQuery;
                params.push(uuid.v4(), team_id);
            } else if (options.type === DOC_TYPE) {
                query = viewQuery;
                params.push(uuid.v4(), team_id);
                params[0] = view_id;
            } else if (options.type === COMMENT_TYPE) {
                query = `
                            INSERT INTO task_mgmt.thread_history(id, parent_id, field, date, after, userid, via, data, workspace_id)
                            VALUES($8, $1, $2, $3, $4, $5, $6, $7, $9)
                            RETURNING id`;
                params.push(uuid.v4());
                params.push(team_id);
            } else if (options.type === ATTACHMENT_TYPE && (attachment_task || comment_task)) {
                query = taskQuery;
                params[0] = attachment_task || comment_task;
                params[1] = 'attachment_comment';
                params[6] = { ...(comment_data || {}), attachment_id: options.parent };
            } else if (options.type === ATTACHMENT_TYPE && attachment_view) {
                query = viewQuery;
                params.push(uuid.v4(), team_id);
                params[0] = attachment_view;
            } else if (options.type === APPROVAL_TYPE) {
                query = `
                            INSERT INTO task_mgmt.approval_history(id, userid, approval_id, workspace_id, field, role, before, after, created_at)
                            VALUES ($6, $5, $1, $7, $2, $8, null, $4, $3)
                            RETURNING id`;
                params.splice(5, 2, uuid.v4(), team_id, approvalRole);
            } else {
                return;
            }

            if (options.queries_only) {
                queries.push({ query, params });
                return;
            }

            // If the parent comment comes from the main db, we always update the thread history in the main db
            const queryFunc =
                options.type === COMMENT_TYPE && !isParentCommentIdFromCommentsDb
                    ? client.queryAsync.bind(client)
                    : client.writeCommentsAsync.bind(client);

            const res2 = await queryFunc(query, params);

            if (res2.rows[0] && res2.rows[0].id) {
                historyRows.push(...res2.rows);
                hist_id = res2.rows[0].id;
            }
        },

        async function updateItem() {
            if (TASK_TYPE !== options.type) {
                return;
            }

            const query = 'UPDATE task_mgmt.items SET date_updated = $1 WHERE id = $2';
            const params = [comment_date, options.parent];

            if (options.queries_only) {
                queries.push({ query, params });
                return;
            }

            await client.queryAsync(query, params);
        },

        // TODO: Whats the motiviation for this? what does it power? and only 10 users at that? DM fanout?
        // TODO: Assuming this is for sidebar ordering prior to latestCommentDate being a thing/all DMs will trigger CLRA -> containing latestCommentDate
        async function updateConversation() {
            if (options.type !== VIEW_TYPE) {
                return;
            }

            versionUpdatesForCommentsDb.push({
                object_type: ObjectType.VIEW,
                object_id: options.parent,
                workspace_id: team_id,
                operation: OperationType.UPDATE,
                data: {
                    context: {
                        ws_key: options.ws_key,
                        root_parent_type,
                    },
                    // limit to 10 members to avoid overloading OVM byte limit
                    relationships: viewMembers.slice(0, 10).map(member => ({
                        type: ObjectRelationshipType.VIEW_COMMENT_USER,
                        object_type: ObjectType.USER,
                        object_id: member.user.id.toString(),
                        workspace_id: team_id,
                    })),
                },
            });

            if (client.usingSeparateDbForComments) {
                // No need to update the views table in the new chat experience.
                return;
            }

            // Only update date_updated on comment creation for legacy conversation views - skip this for canonical chat views
            const query =
                'UPDATE task_mgmt.views SET date_updated = $1 WHERE view_id = $2 AND type NOT IN (30, 31) AND EXISTS (SELECT 1 FROM task_mgmt.chat_views WHERE chat_views.view_id = $2 AND chat_views.is_canonical_channel IS NOT TRUE)';
            const params = [comment_date, options.parent];

            if (options.queries_only) {
                queries.push({ query, params });
                return;
            }

            await client.queryAsync(query, params);
        },

        async function updateChatViewLatestCommentDate() {
            if (options.type !== VIEW_TYPE) {
                return;
            }

            const onConflictCondition = client.usingSeparateDbForComments ? '(view_id, workspace_id)' : '(view_id)';
            const query = `INSERT INTO task_mgmt.chat_views (view_id, workspace_id, latest_comment_date)
                                       VALUES ($1, $2, $3)
                                       ON CONFLICT ${onConflictCondition}
                                       DO UPDATE SET latest_comment_date = $3`;

            const params = [options.parent, team_id, comment_date];

            // TODO: Can remove now?
            if (shouldLogChatViewLatestCommentDateChanges()) {
                logger.info({
                    msg: 'Updated chat_views.latest_comment_date from postComment',
                    comment_id,
                    view_id: options.parent,
                    workspace_id: team_id,
                    latest_comment_date: comment_date,
                    is_using_comment_db: client.usingSeparateDbForComments,
                    queries_only: options.queries_only,
                    ECODE: 'CHAT_DEBUG_100',
                });
            }

            if (options.queries_only) {
                queries.push({ query, params });
                return;
            }

            await client.writeOnlyCommentsDbAsync(query, params);
        },
        async function updateAttachments() {
            // TODO: figure out wf pass through on these results/if used anywhere

            if (!attachmentIds || attachmentIds?.length === 0) {
                return;
            }

            let attachmentsClient;
            try {
                attachmentsClient = await client.getClientForMainDb();
            } catch (attachmentsClientErr) {
                return;
            }

            await bindAttachmentsToParentAsync(
                attachmentIds,
                comment_id,
                config.get<number>('attachments.types.comment'),
                {
                    client: attachmentsClient,
                    parentParentInformation: {
                        parentParentId: options.parent,
                        parentParentType: options.type,
                    },
                    workspaceId: team_id,
                }
            );
        },

        async function setMentions() {
            // TODO: figure out wf pass through on these results/if used anywhere

            const result: any = await mentionMod.setCommentMentionsAsync(comment_id.toString(), comment as any[], {
                client: client as any, // TODO: fix typing
                queries_only: options.queries_only,
            });
            if (options.queries_only) {
                queries.push(...result.queries);
            }
        },

        async function cloudAttachments() {
            // TODO: LOOK AT result2
            // TODO: figure out wf pass through on these results/if used anywhere

            const result: {
                queries?: {
                    query: DbQuery;
                    params: QueryParams;
                }[];
                attachment_ids?: string[];
            } = await helpers.addCloudAttachmentsAsync(userid, comment_id, team_id, options.cloud_attachments, {
                client,
                queries_only: options.queries_only,
            });

            if (options.queries_only) {
                queries.push(...result.queries);
                return;
            }

            if (!result?.attachment_ids?.length) {
                return;
            }

            const relationships = await getAttachmentParentRelationship(
                comment_id,
                config.get<number>('attachments.types.comment'),
                team_id,
                {
                    parentParentInformation: {
                        parentParentId: options.parent,
                        parentParentType: options.type,
                    },
                }
            );
            result.attachment_ids.forEach(attachment_id => {
                versionUpdatesForMainDb.push({
                    object_type: ObjectType.ATTACHMENT,
                    object_id: attachment_id,
                    workspace_id: team_id,
                    operation: OperationType.CREATE,
                    data: {
                        relationships,
                        context: {
                            ws_key: options.ws_key,
                            root_parent_type,
                        },
                    },
                });
            });
        },

        async function addLinks() {
            const link_options = {
                text: comment_text,
                comment_id: comment_id.toString(),
                client: client as any, // TODO: fix typing
                quill_opts: comment,
                queries_only: options.queries_only,
                page_id: options.type === DOC_TYPE ? options.parent : null,
                workspaceId: team_id.toString(),
            };

            const result2 = await link.insertLinksFromComment(userid, link_options);

            if (options.queries_only && result2 && result2.queries) {
                queries.push(...result2.queries);
            }
        },

        async function addEmailReply() {
            if (!options.email_reply) {
                return;
            }

            const { email_reply } = options;
            const { account_id, id, date, thread_id, grantId, threadId } = email_reply;

            const email_queries = customerEmail.getInsertEmailQueries({
                account_id: account_id || grantId,
                email_id: id,
                date: String(date * 1000),
                thread_id: thread_id || threadId,
                open_count: 0,
                full_data: email_reply,
                workspace_id: team_id.toString(),
                id_v3: grantId ? id : null,
                thread_id_v3: grantId ? threadId : null,
                comment_id,
            });

            const emailQueriesClient = await client.getClientForMainDb();

            try {
                await db.promiseBatchQueriesWithClient(email_queries, {
                    client: emailQueriesClient,
                    create_transaction: false,
                });
            } catch (batch_err) {
                throw new CommentError(batch_err, 'COMM_002');
            }
        },

        async function maybeUpdateSourceComment() {
            if (options.extra?.source_comment_id && comment_id) {
                await updateSourceComment({
                    source_comment_id: options.extra.source_comment_id,
                    workspace_id: team_id,
                    stub_comment_id: comment_id.toString(),
                    client,
                    versionUpdatesForCommentsDb,
                });
            }
        },

        // NOTE: KEEP AS LAST STEP PRIOR TO COMMIT
        async function addObjectVersions() {
            if (!versionUpdatesForCommentsDb?.length && !versionUpdatesForMainDb?.length) {
                return;
            }

            updateObjectVersionUpdatesFromHistoryRows(versionUpdatesForCommentsDb, historyRows);
            ({ versionEventsForCommentsDb, versionEventsForMainDb } = await client.updateOVMVersions({
                versionUpdatesForCommentsDb,
                versionUpdatesForMainDb,
            }));
        },

        async function commit() {
            try {
                await client.commitAsync();
            } catch (err3: any) {
                if (err3) {
                    throw new CommentError(err3, 'COMM_009');
                }
            } finally {
                done();
                // NOTE: Intentionally not awaiting this!
                // eslint-disable-next-line @typescript-eslint/no-empty-function
                client
                    .notifyOVMChanges({
                        versionEventsForCommentsDb,
                        versionEventsForMainDb,
                    })
                    // TODO: SUS - should throw?
                    .catch(err => {
                        logger.error({
                            msg: 'Error notifying OVM changes',
                            err,
                            ECODE: 'COMM_303',
                        });
                    });
            }
        },
        async function sendPostCommitSQSMessages() {
            if (sqsSendAfterCommitPromises.length === 0) {
                return;
            }

            const limit = promiseLimit(5);

            sqsSendAfterCommitPromises.forEach(sqsSendFunc => {
                limit(() =>
                    sqsSendFunc().catch(err => {
                        logger.error({
                            msg: 'Error sending SQS message after commit',
                            err,
                            ECODE: 'COMM_306',
                        });
                    })
                );
            });
        },
        // TODO: is this used at all? We should access check this list of followers way above with the rest - why in success func !?
        async function addParentCommentWatcher() {
            if (options.type === COMMENT_TYPE || !options.followers) {
                return;
            }

            await threadMod.addFollowersToThreadWithFollowerAccessCheckAsync(
                userid,
                options.followers,
                comment_id.toString()
            );
        },

        // TODO: Do this for DM thread followership - smarter than adding at time of parent comment creation - although follower race conditions
        /**
         * The first time someone replies to a comment add the author of the original
         * comment as a thread follower.
         */
        async function addThreadParentAuthorAsThreadFollower() {
            if (options.type !== COMMENT_TYPE) {
                return;
            }

            if (Number.isNaN(Number(options.parent))) {
                return;
            }

            /**
             * Lookup the parent comment's author ID, but only
             * if this is the first reply.
             */
            const query = `
                            SELECT comments.userid
                            FROM task_mgmt.comments
                            WHERE
                                id = $1
                                AND NOT EXISTS (
                                    SELECT 1
                                    FROM task_mgmt.comments
                                    WHERE
                                        parent = $1::text
                                        AND type = $2
                                        ${isParentCommentIdFromCommentsDb ? 'AND workspace_id = $3' : ''}
                                )
                                ${isParentCommentIdFromCommentsDb ? 'AND workspace_id = $3' : ''}
                            `;

            // Workspace ID filter is required for effecient querying of content in the Aurora
            // comment DB. Just in-case the workspace_id column is not set universally for all
            // other comments, we're skipping including it in the query for now.
            const queryParams = isParentCommentIdFromCommentsDb
                ? [options.parent, COMMENT_TYPE, team_id]
                : [options.parent, COMMENT_TYPE];

            const commenter_result = await readFromCorrectCommentsDbAsync({
                context: { commentId: options.parent },
                mainDbQueryFunc: db.replicaQueryAsync,
                query,
                params: queryParams,
            });

            if (commenter_result?.rows?.[0]) {
                // TODO: if Dm/GDM, add viewMembers as followers - TEST with chatChannelFollowers (threads)
                await threadMod.addFollowersToThreadAsync(userid, [commenter_result.rows[0].userid], options.parent);
            }
        },
        async function sendEmail() {
            if (!options.email) {
                return;
            }

            options.email.comment_id = comment_id;
            options.email.team_id = team_id;
            options.email.triggered_by = options.triggered_by;
            const user = options.email_auto_user ? options.email_auto_user : userid;

            try {
                const emailResult = await customerEmail.sendEmail(user, options.email.account_id, {
                    ...options.email,
                    attachment_ids: attachmentIds,
                });

                sent_email = emailResult.email;
            } catch (email_err) {
                // Always delete comment
                await _promiseDeleteComment(null, comment_id, null, null);
            }
        },

        // on rapid-fire comment adds, a race condition can occur that results in duplicate comment_numbers,
        // detect and fix them here
        async function dedupeAttachmentCommentOrdering() {
            if (options.type !== ATTACHMENT_TYPE) {
                return;
            }

            // fetch all non-deleted attachment comments ordered by asc date and verify comment numbers
            try {
                const attachmentComments = (await dataStore.searchComments(options.parent, options.type)).rows as any[];

                if (attachmentComments.length > 1) {
                    const newCommentNumberAssignments = getNewCommentNumberAssignments(attachmentComments);

                    if (newCommentNumberAssignments.length) {
                        logger.debug(
                            `Identified ${newCommentNumberAssignments.length} comment(s) whose comment numbers need to be updated`
                        );
                        await dataStore.updateCommentNumbers(newCommentNumberAssignments);
                    }
                }
            } catch (error) {
                throw new CommentError(error, 'COMM_058');
            }
        },

        async function maybeUpdateChatUsageEntitlement() {
            if (shouldCheckChatUsagePaywall(team_id)) {
                await updateChatUsageEntitlements(team_id.toString(), options.comment_type, options.parent);
            }
        },
    ];

    try {
        // END WATERFALL
        for (const step of seriesSteps) {
            await step();
        }
    } catch (err2: any) {
        let skipLogging;
        if (err2.ECODE === 'COMM_002' && err2.err.includes('duplicate key value')) {
            skipLogging = true;
        }
        throw new CommentError(err2, 'COMM_001', 500, {
            skipLogging,
        });
    } finally {
        try {
            if (client && !options.queries_only) {
                client.rollbackWithTryDone();
            } else if (done) {
                done();
            }
        } catch (doneErr) {
            logger.error({
                msg: 'Error in done callback',
                err: doneErr,
                ECODE: 'COMM_086',
            });
        }
    }

    // Gross onDone function - somewhat required so we can "return" and still run this after
    async function success(success_hist_id?: string) {
        if (subcategory_id) {
            subcatHelper.invalidateCachedSubcatTaskCount([subcategory_id]);
        }

        if (TASK_TYPE === options.type) {
            elasticProducer.addTasksToES(userid, [options.parent]);
        }

        const successSteps: (() => Promise<any> | any)[] = [
            /* Add view members as followers to chat DMs/chat group DMs
             * We want to notify all view members when a new comment is added to a chat DM
             * but these users might not be followers already. When a DM is created we don't automatically
             * add all view members as followers to prevent empty DMs from showing up in their sidebars (only the
             * creator is added on init of a DM).
             * new_followers is used to add these users as followers to the chat DM.
             */
            async function addViewMembersAsFollowersToChatDms() {
                if (options.type === VIEW_TYPE && isConversationDMView(view_type)) {
                    const viewMembersIds = viewMembers.map(member => member?.user?.id).filter(Boolean);
                    if (viewMembersIds.length) {
                        new_followers.push(...viewMembersIds);
                        /*
                         * We also want to add all members of a private DM (non-group DM) to all threads
                         * usersWithAccess is used to add followers for comments parented to a view
                         */
                        if (isConversationPrivateDMView(view_type)) {
                            usersWithAccess.push(...viewMembersIds);
                        }
                    }
                }
            },

            // TODO: Why do we do this in the success funciton instead of prior to writing the comment?
            async function replaceChatCommentEmbedAttachments() {
                let commentClient;
                let commentClientDone;
                try {
                    if (!contains_chat_comment_embed) {
                        return;
                    }

                    [commentClient, commentClientDone] = await CommentsClientWrapper.getClientWrapper({
                        commentsDBContext,
                        poolOptions: { label: 'post comment v2' },
                    });
                    logger.debug(
                        `Getting client for replace chat comment with context ${JSON.stringify(commentsDBContext)}`
                    );

                    const updatedCommentParts =
                        await chatCommentAttachmentReplacementService.replaceChatCommentEmbedAttachments(
                            comment,
                            comment_id,
                            team_id.toString(),
                            userid
                        );
                    if (updatedCommentParts) {
                        await dataStore.updateCommentText(
                            updatedCommentParts,
                            comment_id.toString(),
                            team_id.toString(),
                            commentClient
                        );

                        const versionUpdate = {
                            object_type: ObjectType.COMMENT,
                            object_id: comment_id.toString(),
                            workspace_id: team_id,
                            operation: OperationType.UPDATE,
                            data: {
                                context: {
                                    ws_key: options.ws_key,
                                    root_parent_type,
                                },
                            },
                        };

                        const updates = [];
                        updates.push(versionUpdate);

                        const {
                            versionEventsForCommentsDb: commentsDbVersionEvents,
                            versionEventsForMainDb: mainDbVersionEvents,
                        } = await commentClient.updateOVMVersions({
                            versionUpdatesForCommentsDb: updates,
                            versionUpdatesForMainDb: updates,
                        });

                        await commentClient.notifyOVMChanges({
                            versionEventsForCommentsDb: commentsDbVersionEvents,
                            versionEventsForMainDb: mainDbVersionEvents,
                        });
                    }
                } finally {
                    if (commentClientDone) {
                        commentClientDone();
                    }
                }
            },

            async function notifyOnlineFollowers() {
                if (!notify_online_followers) {
                    return;
                }

                const onlineFollowers = await viewFollowersService.getOnlineFollowers(
                    options.type === VIEW_TYPE || options.type === WHITEBOARD_TYPE ? view_id : options.parent,
                    team_id.toString()
                );
                // used to prevent duplicate comment tags
                const commentTaggedUsers: number[] = [];

                onlineFollowers.forEach(follower => {
                    if (!unverifiedUsersTaggedInComment.includes(follower)) {
                        commentTaggedUsers.push(follower);
                        new_followers.push(follower);
                        unverifiedUsersTaggedInComment.push(follower);
                    }
                });

                // Add comment tags for followers of conversation views
                if (commentTaggedUsers.length) {
                    await dataStore.addCommentTags(comment_id.toString(), team_id.toString(), commentTaggedUsers);
                }
            },

            // adds comment_tag entries for group followers of the groups mentioned - chat specific
            async function getMentionedGroupMembers() {
                if (!user_groups_tagged) {
                    return;
                }

                const commentTaggedUsers: number[] = [];
                user_groups_tagged.forEach((group: { id: number; members: { id: number }[] }) => {
                    group.members.forEach(member => {
                        if (member.id !== userid) {
                            if (!unverifiedUsersTaggedInComment.includes(member.id)) {
                                commentTaggedUsers.push(member.id);
                                if (!isConversationDMView(view_type)) {
                                    new_followers.push(member.id);
                                }
                            }
                        }
                    });
                });

                // Add comment tags for followers of conversation views
                if (commentTaggedUsers.length > 0) {
                    await dataStore.addCommentTags(comment_id.toString(), team_id.toString(), commentTaggedUsers);
                }
            },

            async function notifyAssignees() {
                if (!notify_assignees || ![TASK_TYPE, COMMENT_TYPE].includes(options.type)) {
                    return;
                }

                let query = `
                    SELECT userid
                    FROM task_mgmt.assignees
                    WHERE task_id = $1

                    UNION

                    SELECT userid
                    FROM task_mgmt.group_assignees
                    INNER JOIN task_mgmt.group_members
                        ON group_members.group_id = group_assignees.group_id
                    WHERE group_assignees.task_id = $1`;
                const params = [options.parent];

                if (options.type === COMMENT_TYPE) {
                    query = `
                        SELECT userid
                        FROM task_mgmt.assignees
                        WHERE task_id = (
                            SELECT parent
                            FROM task_mgmt.comments
                            WHERE id = $1
                        )

                        UNION

                        SELECT userid
                        FROM task_mgmt.group_assignees
                        INNER JOIN task_mgmt.group_members
                            ON group_members.group_id = group_assignees.group_id
                        WHERE group_assignees.task_id = (
                            SELECT parent
                            FROM task_mgmt.comments
                            WHERE id = $1
                        )`;
                }

                try {
                    const result1 = await db.replicaQueryAsync(query, params);
                    result1.rows.forEach((row: any) => {
                        if (!unverifiedUsersTaggedInComment.includes(row.userid)) {
                            unverifiedUsersTaggedInComment.push(row.userid);
                        }
                    });
                } catch (err: any) {
                    throw new CommentError(err, 'COMM_055');
                }
            },

            // TODO: consider doing this inside the transaction to clean up complex follower logic elsewhere/race conditions etc
            // TODO: consider using a Set instead of an array
            async function addFollowers() {
                // add followers if necessary
                if (
                    options.type === TASK_TYPE ||
                    options.type === SUBCATEGORY_TYPE ||
                    options.type === CATEGORY_TYPE ||
                    options.type === PROJECT_TYPE ||
                    options.type === VIEW_TYPE ||
                    options.type === COMMENT_TYPE ||
                    options.type === ATTACHMENT_TYPE ||
                    options.type === DOC_TYPE ||
                    options.type === WHITEBOARD_TYPE
                ) {
                    if (options.type === TASK_TYPE || (options.type === ATTACHMENT_TYPE && attachment_task)) {
                        await followersMod._addFollowersToTaskAsync(new_followers, attachment_task || options.parent, {
                            new_group_followers,
                            workspace_id: team_id,
                            ws_key: options.ws_key,
                        });
                        await threadMod.addFollowersToThread(
                            userid,
                            [userid, ...new_followers].map(String),
                            comment_id.toString()
                        );
                    } else if (
                        options.type === VIEW_TYPE ||
                        (options.type === ATTACHMENT_TYPE && attachment_view) ||
                        options.type === WHITEBOARD_TYPE
                    ) {
                        await viewFollowersService.addFollowersAsync(
                            new_followers,
                            attachment_view || options.parent,
                            userid,
                            team_id,
                            {
                                skipAccess: true,
                            }
                        );
                        if (attachment_view) {
                            await threadMod.addFollowersToThread(
                                userid,
                                [userid, ...new_followers].map(String),
                                comment_id.toString()
                            );
                        }
                    } else if (options.type === SUBCATEGORY_TYPE) {
                        await subcatFollowersMod.addFollowersToSubcategoryAsync(new_followers, options.parent, {
                            skipAccess: true,
                        });
                    } else if (options.type === CATEGORY_TYPE) {
                        await sharedEntityService.addEntityFollowers(
                            AddEntityFollowersRequest.from({
                                entityId: options.parent,
                                entityType: EntitiesSupportingWatcher.Category,
                                followersToAdd: new_followers,
                            })
                        );
                    } else if (options.type === PROJECT_TYPE) {
                        await sharedEntityService.addEntityFollowers(
                            AddEntityFollowersRequest.from({
                                entityId: options.parent,
                                entityType: EntitiesSupportingWatcher.Project,
                                followersToAdd: new_followers,
                            })
                        );
                    } else if (options.type === DOC_TYPE) {
                        const followersToAdd = [...new_followers];

                        if (userWantsToFollowDocAfterCommenting && followersToAdd.indexOf(userid) < 0) {
                            followersToAdd.push(userid);
                        }

                        const addFollowersPromises = [];

                        if (followersToAdd.length) {
                            addFollowersPromises.push(
                                sharedEntityService.addEntityFollowers(
                                    AddEntityFollowersRequest.from({
                                        entityId: options.parent,
                                        entityType: EntitiesSupportingWatcher.Page,
                                        followersToAdd,
                                    })
                                )
                            );
                        }

                        if (new_group_followers.length) {
                            addFollowersPromises.push(
                                sharedEntityService.addEntityGroupFollowers(
                                    userid,
                                    new AddEntityGroupFollowersRequest(
                                        EntitiesSupportingWatcher.Page,
                                        options.parent,
                                        new_group_followers
                                    )
                                )
                            );
                        }
                        await Promise.all(addFollowersPromises);
                    } else if (options.type === COMMENT_TYPE) {
                        await threadMod.addFollowersToThread(userid, new_followers.map(String), options.parent);
                    } else if (options.type === ATTACHMENT_TYPE && comment_task) {
                        await threadMod.addFollowersToThread(
                            userid,
                            [userid, ...new_followers].map(String),
                            comment_id.toString()
                        );
                    }
                }
            },

            // TODO: same as above followers
            async function addFollowersToThread() {
                if (options.type === COMMENT_TYPE || options.type === ATTACHMENT_TYPE) {
                    return;
                }

                const new_thread_followers = usersWithAccess;
                if (isHumanAssignee(verifiedAssignee) && verifiedAssignee !== userid) {
                    new_thread_followers.push(verifiedAssignee);
                }

                await threadMod.addFollowersToThreadAsync(
                    userid,
                    [userid, ...new_thread_followers].map(String),
                    comment_id.toString()
                );
            },
        ];

        try {
            for (const step of successSteps) {
                await step();
            }
        } catch (err: any) {
            // swallow
        }

        if (options.type === DOC_TYPE && options.parent && isHumanAssignee(options.assignee)) {
            reportDocChanged(userid, options.parent);
        }

        if (isChatComment) {
            reportChatChanged(userid, comment_id, team_id);
        } else {
            reportCommentChanged(userid, comment_id, team_id);
        }

        if (options.attachment_ids) {
            elasticProducer.addAttachmentsToES(userid, options.attachment_ids);
        }

        if (options.type === DOC_TYPE && isHumanAssignee(options.assignee)) {
            const docsSearchCache = DocsAppContext.getDocsSearchCache();
            // do work in background so we don't block on cache invalidation, only log errors
            try {
                await docsSearchCache.invalidateDocsIdsCachesForAssigned(team_id, options.assignee);
            } catch (e: any) {
                logger.error({
                    msg: 'Error invalidating docs search cache',
                    err: e,
                });
            }
        }

        if (options.suppress_notifications) {
            return;
        }

        const userMentions = getTaggedUsersToNotifyForPost({
            unverifiedUsersTaggedInComment,
            usersWithAccess,
            unverifiedGroupsTagged: unverifiedGroupsTagged.map(String),
            group_members_access,
            notifyOptions: { type: options.type, notify_all: options.notify_all },
            user_groups_tagged,
            userid,
        });

        const commentAuthor =
            options.useClickBot && inboxCodeRefactor().use_clickbot_for_zapier ? SystemUserId.ClickBot : userid;

        // If there is a clip, we need to notify the clip creator of the comment
        let userIdsToNotify;
        if (isClip(attachment as any)) {
            userIdsToNotify = [+attachment.userid];
        }

        let historyItem;
        if (success_hist_id) {
            historyItem = sendCommentNotification({
                assignee: verifiedAssignee,
                attachment: {
                    ...attachment,
                    parentView: attachment_view,
                    parentTask: attachment_task ?? comment_task,
                } as any,

                commentAuthor,
                commentDate: comment_date,
                commentId: comment_id,
                commentParentId: options.parent,
                commentParentType: options.type,
                commentParts: comment,
                excluded_notif_channels: options.excluded_notif_channels,
                fromEmailUserIds: from_email_user_ids,
                groupAssignee: verifiedGroupAssignee,
                groupMentions: unverifiedGroupsTagged,
                historyItemId: success_hist_id as any, // TODO: Wrong type, hist id isn't always number
                isChatComment,
                notifyAll: options.notify_all,
                rootParentId: root_parent_id,
                rootParentType: root_parent_type,
                userMentions,
                userIdsToNotify,
                workspaceId: team_id,
            });

            runInVoid(() =>
                sendWebhookMessageForMentionInComment({
                    agentsMentioned: agents_mentioned,
                    rootParent: {
                        id: root_parent_id,
                        type: root_parent_type,
                    },
                    success_hist_id,
                    comment_id,
                    team_id: team_id.toString(),
                    userid,
                    auto_id: options.auto_id,
                    trigger_id: options.trigger_id,
                    type: options.type,
                })
            );

            if (options.type === TASK_TYPE) {
                webhook.sendWebhookMessage('taskUpdated', {
                    task_id: options.parent,
                    hist_id: success_hist_id,
                    auto_id: options.auto_id,
                    trigger_id: options.trigger_id,
                    team_id,
                    comment_id,
                    userid,
                });

                sqsWs.sendWSMessage('sendTaskUpdated', [
                    options.parent,
                    {
                        userid,
                        hist_id: success_hist_id,
                        ws_key: options.ws_key,
                        date_updated: new Date().getTime(),
                        commentMessage: true,
                    },
                ]);
            } else if (options.type === ATTACHMENT_TYPE && (attachment_task || comment_task)) {
                webhook.sendWebhookMessage('taskUpdated', {
                    task_id: attachment_task || comment_task,
                    hist_id: success_hist_id,
                    auto_id: options.auto_id,
                    trigger_id: options.trigger_id,
                    team_id,
                    userid,
                });

                sqsWs.sendWSMessage('sendTaskUpdated', [
                    attachment_task || comment_task,
                    {
                        userid,
                        hist_id: success_hist_id,
                        ws_key: options.ws_key,
                        date_updated: new Date().getTime(),
                        commentMessage: true,
                    },
                ]);
            } else if (options.type === SUBCATEGORY_TYPE) {
                webhook.sendWebhookMessage('listUpdated', {
                    subcategory_id: options.parent,
                    hist_id: success_hist_id,
                    auto_id: options.auto_id,
                    trigger_id: options.trigger_id,
                    team_id,
                });

                sqsWs.sendWSMessage('sendSubcatComment', [
                    options.parent,
                    {
                        userid,
                        hist_id: success_hist_id,
                        ws_key: options.ws_key,
                        date_updated: new Date().getTime(),
                        commentMessage: true,
                    },
                ]);
            } else if (options.type === CATEGORY_TYPE) {
                webhook.sendWebhookMessage('folderUpdated', {
                    category_id: options.parent,
                    hist_id: success_hist_id,
                    auto_id: options.auto_id,
                    trigger_id: options.trigger_id,
                    team_id,
                });

                sqsWs.sendWSMessage('sendCatComment', [
                    options.parent,
                    {
                        userid,
                        hist_id: success_hist_id,
                        ws_key: options.ws_key,
                        date_updated: new Date().getTime(),
                        commentMessage: true,
                    },
                ]);
            } else if (options.type === PROJECT_TYPE) {
                webhook.sendWebhookMessage('spaceUpdated', {
                    project_id: options.parent,
                    hist_id: success_hist_id,
                    auto_id: options.auto_id,
                    trigger_id: options.trigger_id,
                    team_id,
                });

                sqsWs.sendWSMessage('sendProjectComment', [
                    options.parent,
                    {
                        userid,
                        hist_id: success_hist_id,
                        ws_key: options.ws_key,
                        date_updated: new Date().getTime(),
                        commentMessage: true,
                    },
                ]);
            }
        } else if (options.type === VIEW_TYPE || options.type === DOC_TYPE || options.type === WHITEBOARD_TYPE) {
            sqsWs.sendWSMessage('sendViewComment', [
                options.parent,
                {
                    userid,
                    hist_id: success_hist_id,
                    ws_key: options.ws_key,
                    date_updated: new Date().getTime(),
                    commentMessage: true,
                    workspace_id: team_id,
                },
            ]);
            webhook.sendWebhookMessage('viewCommentPosted', {
                view_id: options.parent,
                hist_id: success_hist_id,
                auto_id: options.auto_id,
                trigger_id: options.trigger_id,
                team_id,
                comment_id,
                userid,
            });
        } else if (options.type === ATTACHMENT_TYPE && attachment_view) {
            sqsWs.sendWSMessage('sendViewComment', [
                attachment_view,
                {
                    userid,
                    hist_id: success_hist_id,
                    ws_key: options.ws_key,
                    date_updated: new Date().getTime(),
                    commentMessage: true,
                    workspace_id: team_id,
                },
            ]);
        } else if (options.type === COMMENT_TYPE) {
            sendWebhookMessageForThreadedComment(root_parent_id, root_parent_type, {
                hist_id: success_hist_id,
                auto_id: options.auto_id,
                trigger_id: options.trigger_id,
                team_id,
                comment_id,
                userid,
            });

            if (view_id) {
                sqsWs.sendWSMessage('sendViewComment', [
                    view_id,
                    {
                        userid,
                        hist_id: success_hist_id,
                        threaded: true,
                        thread_id: options.parent,
                        ws_key: options.ws_key,
                        date_updated: new Date().getTime(),
                        commentMessage: true,
                        workspace_id: team_id,
                    },
                ]);
            } else {
                db.replicaQuery(
                    `SELECT parent FROM task_mgmt.comments WHERE id = $1 AND type = ${TASK_TYPE}`,
                    [options.parent],
                    (task_err, task_result) => {
                        if (task_result && task_result.rows && task_result.rows[0]) {
                            let parsedEmailReply;
                            if (options.type === COMMENT_TYPE) {
                                if (
                                    comment &&
                                    comment.length === 0 &&
                                    options &&
                                    options.email_reply &&
                                    options.email_reply.body
                                ) {
                                    parsedEmailReply = htmlToText(options.email_reply.body, {
                                        ignoreHref: true,
                                        ignoreImage: true,
                                    } as any);
                                    parsedEmailReply.split('\n').forEach((c: any) => {
                                        if (c) {
                                            comment.push({ text: c, attributes: {} } as any);
                                            comment.push({
                                                text: '\n',
                                                attributes: {},
                                            } as any);
                                        }
                                    });
                                }
                                (comment as any).comment_id = comment_id;
                                postTaskCommentAddedNotification(
                                    userid,
                                    task_result.rows[0].parent,
                                    comment as any,
                                    parsedEmailReply
                                );
                            }
                            sqsWs.sendWSMessage('sendTaskUpdated', [
                                task_result.rows[0].parent,
                                {
                                    userid,
                                    type: COMMENT_TYPE,
                                    hist_id: success_hist_id,
                                    ws_key: options.ws_key,
                                    date_updated: new Date().getTime(),
                                    commentMessage: true,
                                },
                            ]);
                        }
                    }
                );
            }
        }

        if (
            (usersWithAccess.length || unverifiedGroupsTagged.length) &&
            [
                TASK_TYPE,
                SUBCATEGORY_TYPE,
                CATEGORY_TYPE,
                PROJECT_TYPE,
                VIEW_TYPE,
                DOC_TYPE,
                COMMENT_TYPE,
                ATTACHMENT_TYPE,
                WHITEBOARD_TYPE,
            ].includes(options.type)
        ) {
            let usersToNotify = [...usersWithAccess];

            (user_groups_tagged || []).forEach((group: { id: number; members: { id: number }[] }) => {
                group.members.forEach(member => {
                    if (
                        member.id !== userid &&
                        !usersToNotify.includes(member.id) &&
                        group_members_access &&
                        group_members_access[member.id]
                    ) {
                        usersToNotify.push(member.id);
                    }
                });
            });

            if (!options.notify_all) {
                usersToNotify = usersToNotify.filter(member_id => member_id !== userid);
            }

            if (
                unverifiedGroupsTagged.length &&
                [TASK_TYPE, COMMENT_TYPE, ATTACHMENT_TYPE, DOC_TYPE, VIEW_TYPE, WHITEBOARD_TYPE].includes(options.type)
            ) {
                const parent = options.type === COMMENT_TYPE ? options.parent : comment_id.toString();
                // TODO: No async version -> unhandled rejection possiblity
                threadMod.addGroupFollowersToThread(unverifiedGroupsTagged, parent);
            }

            let notif_parent: string;
            let notif_type: number;
            if (options.type === DOC_TYPE || options.type === WHITEBOARD_TYPE) {
                notif_parent = view_id;
                notif_type = VIEW_TYPE;
            } else if (options.type === ATTACHMENT_TYPE && attachment_view) {
                notif_parent = attachment_view;
                notif_type = VIEW_TYPE;
            } else if (options.type === ATTACHMENT_TYPE && attachment_task) {
                notif_parent = attachment_task;
                notif_type = TASK_TYPE;
            } else if (options.type === ATTACHMENT_TYPE && comment_task) {
                notif_parent = comment_task;
                notif_type = TASK_TYPE;
            } else {
                notif_parent = options.parent;
                notif_type = options.type;
            }

            // This latest refactor no longer sends createCommentTagNotifications ingestion messages
            //
            // This call below will only fire createClipCommentTagNotification messages if
            // skipCommentTaggedIngestionMessage is ON
            //
            // If skipCommentTaggedIngestionMessage is OFF, this call will also fire createCommentTagNotifications
            // ingestion messages
            sendCommentTagNotifications({
                historyItem: historyItem as any,
                usersToNotify,
                userid,
                notif_parent,
                notif_type,
                type: options.type,
                comment_id,
                team_id,
                success_hist_id,
                attachment: attachment as any,
            });
        }

        const shortCircuitConfig = getShortCircuitConfig(userid);

        if (
            isHumanAssignee(options.assignee) &&
            (options.notify_all || options.assignee !== userid) &&
            (options.type === TASK_TYPE ||
                options.type === SUBCATEGORY_TYPE ||
                options.type === CATEGORY_TYPE ||
                options.type === PROJECT_TYPE ||
                options.type === VIEW_TYPE ||
                options.type === DOC_TYPE ||
                options.type === COMMENT_TYPE ||
                (options.type === ATTACHMENT_TYPE && comment_task) ||
                isTeamClip(attachment as any) ||
                options.type === WHITEBOARD_TYPE)
        ) {
            let { parent } = options;
            if (options.type === DOC_TYPE) {
                parent = view_id;
            } else if (options.type === ATTACHMENT_TYPE && comment_task) {
                parent = comment_task;
            }
            if (isTeamClip(attachment as any) && shouldSendClipCommentNotification(userid, options.type)) {
                sqsNotif.sendNotifMessage('createClipCommentAssignedNotification', [
                    options.assignee,
                    userid,
                    parent,
                    comment_id,
                    team_id,
                ]);
            } else if (isChatComment && shortCircuitConfig?.shouldUseRefactoredCommentAssignedNotifLogic) {
                try {
                    await sendCommentAssignedNotifications({
                        commentAuthor: userid,
                        commentParts: comment,
                        actor: userid,
                        assignee: verifiedAssignee,
                        commentId: comment_id,
                        commentParentId: parent,
                        commentParentType: options.type,
                        rootParentId: root_parent_id,
                        rootParentType: root_parent_type,
                        workspaceId: team_id,
                        notify_all: options.notify_all,
                        on_create: true,
                        isChatComment,
                        historyDatastoreService,
                        newHistId: uuid.v4(),
                        commentTypes: config.get('comments.types'),
                    });
                } catch (e) {
                    logger.error({
                        msg: 'Error sending comment assigned notifications',
                        err: e,
                        ECODE: 'COMM_087',
                        userid,
                        commentId: comment_id,
                        assignee: verifiedAssignee,
                        commentParentId: parent,
                        commentParentType: options.type,
                    });
                    sqsNotif.sendNotifMessage('createCommentAssignedNotifications', [
                        verifiedAssignee,
                        userid,
                        parent,
                        comment_id,
                        true,
                        {
                            notify_all: options.notify_all,
                            type: options.type,
                            on_create: true,
                        },
                    ]);
                }
            } else {
                sqsNotif.sendNotifMessage('createCommentAssignedNotifications', [
                    verifiedAssignee,
                    userid,
                    parent,
                    comment_id,
                    true,
                    {
                        notify_all: options.notify_all,
                        type: options.type,
                        on_create: true,
                    },
                ]);
            }
        } else if (
            verifiedGroupAssignee &&
            verifiedGroupAssignee !== 'unassigned' &&
            [TASK_TYPE, COMMENT_TYPE, ATTACHMENT_TYPE, DOC_TYPE].includes(options.type)
        ) {
            if (success_hist_id && shortCircuitConfig?.shouldUseRefactoredCommentAssignedNotifLogic) {
                try {
                    await sendCommentAssignedNotifications({
                        commentAuthor: userid,
                        commentParts: comment,
                        actor: userid,
                        assignee: verifiedGroupAssignee as any,
                        group_assignee: verifiedGroupAssignee,
                        commentId: comment_id,
                        commentParentId: options.parent,
                        commentParentType: options.type,
                        rootParentId: root_parent_id,
                        rootParentType: root_parent_type,
                        workspaceId: team_id,
                        on_create: true,
                        isChatComment,
                        historyDatastoreService,
                        newHistId: uuid.v4(),
                        commentTypes: config.get('comments.types'),
                    });
                } catch (err: any) {
                    logger.error({
                        msg: 'Error sending comment assigned notifications',
                        err,
                        ECODE: 'COMM_088',
                        userid,
                        commentId: comment_id,
                        assignee: options.group_assignee,
                        commentParentId: options.parent,
                        commentParentType: options.type,
                    });
                    sqsNotif.sendNotifMessage('createCommentAssignedNotifications', [
                        options.group_assignee,
                        userid,
                        options.parent,
                        comment_id,
                        true,
                        { group_assignee: options.group_assignee, type: options.type },
                    ]);
                }
            } else if (success_hist_id) {
                sqsNotif.sendNotifMessage('createCommentAssignedNotifications', [
                    options.group_assignee,
                    userid,
                    options.parent,
                    comment_id,
                    true,
                    { group_assignee: options.group_assignee, type: options.type },
                ]);
            }

            if (options.type !== DOC_TYPE) {
                await followersMod._addFollowersToTaskAsync([], options.parent, {
                    new_group_followers: [options.group_assignee],
                    workspace_id: team_id,
                    ws_key: options.ws_key,
                });
            }
            if (options.type === COMMENT_TYPE) {
                // TODO: no async version -> unhandled rejection possibility
                threadMod.addGroupFollowersToThread([options.group_assignee], options.parent);
            }

            if (options.type === ATTACHMENT_TYPE || options.type === DOC_TYPE) {
                // TODO: no async version -> unhandled rejection possibility
                threadMod.addGroupFollowersToThread([options.group_assignee], comment_id.toString());
            }
        }

        if (options.type === TASK_TYPE) {
            let parsedEmailReply;
            if (comment && comment.length === 0 && options && options.email_reply && options.email_reply.body) {
                parsedEmailReply = htmlToText(options.email_reply.body, {
                    ignoreHref: true,
                    ignoreImage: true,
                } as any);
                parsedEmailReply.split('\n').forEach((c: any) => {
                    if (c) {
                        comment.push({ text: c, attributes: {} } as any);
                        comment.push({ text: '\n', attributes: {} } as any);
                    }
                });
            }
            (comment as any).comment_id = comment_id;
            postTaskCommentAddedNotification(userid, options.parent, comment as any, parsedEmailReply, usersWithAccess);

            // This is an attachment comment, likely a clip comment, and we don't have a success_hist_id because the original clip has a parent of the workspace.
            if (attachment && !success_hist_id) {
                const { userid: clip_creator_id } = attachment;

                if (shouldSendClipCommentNotificationForCreator(userid, options.type, attachment as any)) {
                    sendCommentNotification({
                        assignee: options.assignee,
                        attachment: attachment as any,
                        commentAuthor: userid,
                        commentDate: comment_date,
                        commentId: comment_id,
                        commentParentId: options.parent,
                        commentParentType: options.type,
                        commentParts: comment,
                        excluded_notif_channels: options.excluded_notif_channels,
                        fromEmailUserIds: from_email_user_ids,
                        groupAssignee: options.group_assignee,
                        groupMentions: unverifiedGroupsTagged,
                        historyItemId: `${attachment.id}#${comment_id}` as any,
                        isChatComment,
                        notifyAll: options.notify_all,
                        rootParentId: root_parent_id,
                        rootParentType: root_parent_type,
                        userIdsToNotify: [clip_creator_id].map(Number),
                        userMentions: unverifiedUsersTaggedInComment,
                        workspaceId: team_id,
                    });
                }
            }
        }
    }

    const version = versionEventsForCommentsDb?.find(
        e =>
            e?.operation === OperationType.CREATE &&
            e.object_type === ObjectType.COMMENT &&
            e.object_id === String(comment_id)
    );

    if (version?.data?.changes && version.data.changes?.length > 0) {
        version.data.changes = version.data.changes.filter(
            change => change.field !== 'encrypted_comment' && change.field !== 'authorized_followers'
        );
    }

    if (options.queries_only) {
        return { queries, success: null, version }; // TODO: find what calls and make sure we can snip this
    }

    // TODO: move...
    if (options.type === ATTACHMENT_TYPE) {
        increment_team_limits(team_id);
    }

    success(hist_id).catch((err: any) => {
        logger.error({
            msg: 'Error in success callback',
            err,
            ECODE: 'COMM_302',
        });
    });

    if (hist_id) {
        return {
            id: comment_id,
            hist_id,
            date: comment_date,
            comment_number: options.comment_number,
            email: sent_email,
            version,
        };
    }
    return {
        id: comment_id,
        date: comment_date,
        comment_number: options.comment_number,
        email: sent_email,
        version,
    };
}
