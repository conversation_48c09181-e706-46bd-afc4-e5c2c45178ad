{"2fa": {"max_number_of_attempts": "4", "validation_code_expiration_time": "600000"}, "account_create_blacklist": ["vmware.com", "avinetworks.com", "cloudlive.com", "heptio.com", "mesh7.com", "airw.com", "carbonblack.com", "pivotal.io", "bit9.com", "air-watch.com", "vmwareone.com", "hept.io", "gopivotal.com", "pivotallabs.com", "confer.net", "nyulangone.org", "nyumc.org"], "active_status_types": ["custom"], "aggregation_handler": {"batch_size": 600, "batch_ttl": 200, "retry_limit": 3}, "ahrefs": {"password": "", "username": ""}, "amazon_ses": {"alert_email_sending_address": "", "incoming_domain": "local-tasks.clickup.com", "outbound_region": "us-west-2", "s3_bucket": "clickup-inbound-email-dev", "s3_bucket_region": "us-west-2", "secret": ""}, "amplitude": {"api_key": ""}, "anyone_assignee": -2, "app": {"app_url": "https://local-fe.clickup-qa.com", "attachment_url": "http://localhost:9004/v1", "coeditor_url": "http://localhost:9004", "doc_url": "https://local-doc.clickup-qa.com", "export_url": "http://localhost:9004", "form_submit_url": "http://localhost:9004", "form_url": "https://local-forms.clickup-qa.com", "gantt_export_url": "https://gantt-export-staging.clickup.com/gantt", "global_region_api": "localhost:9004", "global_region_url": "http://localhost:9004", "host": "127.0.0.1", "invoice_url": "http://localhost:9004", "name": "clickup_local", "oauth_callback_url": "https://local-fe.clickup-qa.com", "port": 9005, "share_url": "https://local-share.clickup-qa.com", "url": "http://localhost:9004", "version": "0.0.1", "websocket_url": "ws://localhost:9004/ws"}, "asana": {"auth_endpoint": "", "client_id": "", "client_secret": "", "refresh_token_uri": "", "token_ep": ""}, "attachments": {"authz": {"cors_origin": "*.clickup-attachments.com", "token_signing_key": "blahbla<PERSON>blah"}, "aws": {"public_bucket": "clickup-local-public", "shard_buckets": {"g001": {"signed_attachments_bucket": "clickup-attachments-confidential-local", "team_bucket": "clickup-local-public"}}, "signed_attachments_bucket": "clickup-attachments-confidential-local", "team_bucket": "clickup-local-public"}, "checkInterval": 900000, "default_attachment_expire": 3600000, "exp": 2592000000, "extensionMaxLength": 50, "largeThumbnailHeight": 1200, "largeThumbnailWidth": 1600, "mediumThumbnailHeight": 300, "mediumThumbnailWidth": 600, "smallThumbnailHeight": 35, "smallThumbnailLength": 80, "smallThumbnailWidth": 200, "sources": {"ai_notetaker": 10, "box": 6, "clickup": 1, "clickup_audio_recording": 7, "clip": 8, "drive": 2, "dropbox": 3, "email": 4, "onedrive": 5, "syncup": 9}, "types": {"attachment": 16, "category": 5, "comment": 2, "custom_field": 11, "doc": 14, "post": 3, "project": 4, "reminder": 9, "subcategory": 6, "task": 1, "team": 7, "user": 10, "user_group": 15, "view": 8, "widget": 12}, "userDataTypes": [9, 10]}, "auth": {"visibility": {"admin": 2, "everyone": 3, "private": 1}}, "auto_auditlog_service": {"host": "http://localhost:3011", "route": "auto-auditlog-service/v1/"}, "auto_paywall_service": {"host": "http://localhost:3334", "route": "auto-paywall-service/v3/"}, "auto_webhooks_service": {"host": "http://localhost:3000", "route": "auto-webhooks-service/v1/"}, "automation": {"action_count_limit": 6, "addons": {"automations_100": {"actions": 100}, "automations_1000": {"actions": 1000}, "automations_10000": {"actions": 10000}, "automations_100000": {"actions": 100000}, "automations_1000000": {"actions": 1000000}, "automations_2500": {"actions": 2500}, "automations_25000": {"actions": 25000}, "automations_unlimited": {"actions": 0, "unlimited": true}}, "auth": {"visibility": {"admin": 2, "everyone": 3, "user": 1}}, "hubspot": {"registration_url": "https://************************************.trayapp.io", "solution_id": "", "workflows": {"initializer": "[Tray Solutions] Parent: Initialize Hubspot SolutionInstance (Webhook)", "receiver": "[Tray Solutions] Parent: Hubspot Webhook Receiver"}}, "internal_limits": {"1": {"action": 100, "plan": "free forever"}, "10": {"action": 25000, "plan": "business plus"}, "11": {"action": 250000, "plan": "enterprise"}, "12": {"action": 250000, "plan": "enterprise"}, "13": {"action": 100, "plan": "free forever"}, "14": {"action": 1000, "plan": "unlimited"}, "15": {"action": 5000, "plan": "business"}, "16": {"action": 25000, "plan": "business plus"}, "17": {"action": 250000, "plan": "enterprise"}, "3": {"action": 10000, "plan": "business"}, "4": {"action": 250000, "plan": "enterprise"}, "5": {"action": 25000, "plan": "business plus"}, "6": {"action": 1000, "plan": "unlimited"}, "7": {"action": 100, "plan": "free forever"}, "8": {"action": 1000, "plan": "unlimited"}, "9": {"action": 10000, "plan": "business"}}, "lanes": {"ai_agent": {"actions": [{"type": "launch_ai_agent"}, {"type": "mention_brain"}]}, "bulk": {"hist_limit": 5}, "long_running": {"actions": [{"resource": "app:googlecalendar", "type": "create_event"}, {"resource": "app:googlecalendar", "type": "delete_event"}, {"resource": "app:googlecalendar", "type": "update_event"}, {"resource": "task", "type": "ai_task_property"}, {"resource": "task", "type": "apply_template"}, {"resource": "task", "type": "copy"}, {"resource": "task", "type": "create_task"}, {"resource": "task", "type": "create_subtask"}, {"resource": "task", "type": "create_list"}, {"resource": "task", "type": "subcategory"}, {"resource": "task", "type": "webhook"}, {"resource": "view", "type": "webhook"}, {"resource": "email", "type": "email"}]}}, "lb_url": "http://localhost:9004", "limits": {"action": 1000, "trigger": 1000}, "queue_processors": {"application": 4, "interval": 12}, "shortcuts": {"assignee": 1, "follower": 2}, "tray": {"clickup_client_id": "", "connector": {"serviceEnvironmentId": "", "serviceId": ""}, "host": "https://tray.io/graphql", "secret": "", "token": ""}}, "automation_entry": {"queue_start_delay": 1000}, "automation_local_lb": "", "automation_rate_limiting_config": {"action": "delay_rate_limited_messages", "action_type": {"message_expiry": 86400, "message_expiry_key": "first_delay_ts"}, "duration": 60, "max_counter": 1000, "notification": false}, "avalara": {"endpoint": "", "password": "", "username": ""}, "aws": {"account_ids_by_env": {"local": "************", "prod": "************", "qa": "************", "staging": "************"}, "awis_password": "", "awis_user": "", "bucket": "clickup-local", "cloudfront_access_key": "", "cloudfront_pem": "", "cloudfront_public_team_url": "l.clickup-attachments.com/", "cloudfront_public_url": "dev-attachments-public.clickup.com/", "cloudfront_signed_attachments_url": "ls.clickup-attachments.com/", "cloudfront_url": "dev-attachments.clickup.com/", "kms_token_arn": "", "public_bucket": "clickup-local-public", "region": "us-west-2", "secret_test": "0", "signedURLExp": *********, "signed_attachments_bucket": "clickup-attachments-confidential-local", "team_bucket": "clickup-local-public"}, "azure_sso": {"idp_certificates": ["certs/clickup-local.cer"], "idp_login_url": "https://login.microsoftonline.com/a6b44062-7852-445c-aba7-c33a826ee1a9/saml2", "idp_logout_url": "https://login.microsoftonline.com/common/wsfederation?wa=wsignout1.0", "sp_assert_endpoint": "https://929a9454.ngrok.io/v1/azure/assert", "sp_certificate": "certs/saml_cert.pem", "sp_host": "https://929a9454.ngrok.io/v1/cu_azure_metadata.xml", "sp_private_key": "certs/saml_privkey.pem"}, "basecamp": {"client_id": "", "client_secret": "", "user_agent": ""}, "batch_workspace_storage_reset_producer": {"kafka_client_id": "be-attachment-storage-reset-consumer", "send_to_topic_retry_options": {"initialRetryTime": 1000, "maxRetryTime": 6000, "multiplier": 1, "retries": 5}, "topic_name": "AttachmentStorageResetQueue"}, "billing_usage_service": {"local_base_url": "http://localhost:5113/billing-usage-service-v3/"}, "bitbucket": {"hmac_secret": "80bddd1426594a84841e83bb2c0456d6", "v1": {"client_id": "", "client_secret": ""}, "v2": {"client_id": "", "client_secret": ""}}, "bogo_teams": [], "braintree": {"enablePayments": false, "environment": "sandbox", "interval": 600000, "merchantId": "", "privateKey": "", "publicKey": ""}, "broadcast_lb_urls": [], "browser_notifications": {"enabled": true}, "bugsnag_integration": {"webhook_whitelisted_ips": ["***************", "***************"]}, "bypass_rate_limit_test_userids": ["666663", "666667", "777777", "800020", "7777782"], "bytesInGB": 1073741824, "canny_private_key": "************************************", "capterra_ads": {"token": ""}, "cat_status_types": {"hidden": 2, "visible": 1}, "change_notification_manager": {"bounded_memory_queue_size": 100, "consumer_processor_timeout_ms": 20000, "kafka_client_id": "be-version-pub", "kafka_consumer_group_name": "ChangeNotificationManager", "kafka_consumer_max_bytes": 24000, "kafka_message_batch_size": 100, "send_to_topic_retry_options": {"initialRetryTime": 1000, "maxRetryTime": 6000, "multiplier": 1, "retries": 5}, "topic_name": "ObjectVersionCDC", "update_db_batch_size": 100, "update_db_batch_time_ms": 1000, "use_kafka": false}, "chrome_extension": {"client_id": ""}, "clearbit": {"api_key": ""}, "clickbot_assignee": -1, "clips": {"url": "http://localhost:4040"}, "closed_status_types": ["closed"], "cloudfront": {"ip_address_header_secret": "managedSecret/v1/env/true/infra_app_shared_trust_cloudfront_ip_address_header"}, "cluster": {"workerHealthUpdateWaitTimeMs": 30000}, "coEditorMaxDebounce": 5000, "coEditorPort": 5678, "coEditorProvider": "yjs", "coEditorSessionDisconnectCheckInterval": 5000, "codox": {"apiKey": "", "expiration": "7d", "token": {"expirationSeconds": 604800, "sharedKey": ""}, "url": "https://smd.stage.codox.io/sessions"}, "colors": ["#ff5251", "#ff897f", "#304ffe", "#536cfe", "#4466ff", "#827718", "#afb42b", "#cddc39", "#e16b16", "#f57c01", "#ffa727", "#3e2724", "#5d4037", "#795548", "#263238", "#455963", "#5f7c8a", "#02579b", "#0388d1", "#1090e0", "#006063", "#0197a7", "#0f9d9f", "#1b5e20", "#388d3c", "#8ac34a", "#c51162", "#ee5e99", "#aa2fff", "#e040fb", "#622aea", "#5f55ee", "#b388ff", "#d60800"], "comments": {"attachment_comments_limit": 100, "comment_types": {"ai": 2, "post": 1, "syncup": 3}, "deleteCheckInterval": 900000, "types": {"approval": 18, "attachment": 16, "category": 5, "comment": 2, "doc": 14, "project": 4, "subcategory": 6, "task": 1, "view": 8, "whiteboard": 27}, "validTypes": [1, 2, 4, 5, 6, 8, 10, 14, 16, 18, 27]}, "companion": {"secret": "6a656d4b-7336-4d34-9f92-52b0e10d4436"}, "competitors_custom_app_client_ids": ["M0B2TA5M91J5T8DEKJPU5G2IB90V19SY", "RR40E23WFE3CRO06U3ALNLITIPW2JZLF", "XM5FGDMLAEVDBA6LXIGZBOUI4QZZVPPF"], "cookies": {"app": {"domain": "localhost"}, "forms": {"domain": "localhost", "sameSite": "None", "secure": true}, "identity": {"domain": "local-fe.clickup-qa.com"}}, "create_task_dependencies_types": {"blocking": 2, "waiting_on": 1}, "crm_url": "https://crm.clickup.com/crm", "csrf": {"value": "1"}, "custom_id_queue_url": "", "custom_item_types": {"1": "milestone", "3": "form_response", "4": "meeting_note"}, "custom_item_types_hidden": {"100": "shadow_list_task", "500": "workspace_user_profile"}, "dailyco_auth": {"hipaa_key": "", "key": ""}, "dashboard_first_date": 1576090436022, "dashboards": {"permission_levels": {"1": {"create_dashboards": true, "delete_dashboards": false, "edit_dashboard": false, "edit_widget": false}, "3": {"create_dashboards": true, "delete_dashboards": false, "edit_dashboard": true, "edit_widget": true}, "5": {"create_dashboards": true, "delete_dashboards": true, "edit_dashboard": true, "edit_widget": true}}, "stats_query_row_count": 1000}, "date_format_no_time": "dddd, MMMM Do YYYY", "date_format_w_time": "dddd, MMMM Do YYYY, h:mm:ss a Z", "db": {"aws_rds_port": 5432, "aws_rds_pw": "1234abc", "aws_rds_user": "clickup_admin", "backup_host": "localhost", "client_maxes": {"coldStorageRead": 36, "coldStorageWrite": 36, "comments_db": 50, "longReplica": 14, "longtx": 5, "rds": 10, "read": 14, "read_activity_rds": 36, "read_card": 30, "read_email_rds": 15, "read_history_rds": 15, "read_pulse_rds": 15, "read_ws": 36, "redshift": 5, "replica": 36, "scratchpad_reader": 10, "scratchpad_writer": 10, "stats": 50, "taskState": 80, "tx": 10, "write": 14, "write_activity_rds": 36, "write_card": 15, "write_email_rds": 15, "write_history_rds": 15, "write_pulse_rds": 15, "write_ws": 36}, "cold_storage_db": "clickup_cold_storage", "cold_storage_db_host": "localhost", "cold_storage_db_port": 5432, "cold_storage_manager_pw": "", "cold_storage_manager_user": "cold_storage_manager", "cold_storage_reader_pw": "", "cold_storage_reader_user": "cold_storage_reader", "cold_storage_writer_pw": "", "cold_storage_writer_user": "cold_storage_writer", "comments_db_config": {"master_id": 0}, "connection_timeout_millis": 90000, "connections": {"dashboardsReportingRead": {"max_clients": 80, "secret": "local-only", "statement_timeout": 480000}, "dashboardsReportingWrite": {"max_clients": 80, "secret": "local-only", "statement_timeout": 60000}, "docHistoryReader": {"dbname": "clickup", "max_clients": 10, "secret": "local-only"}, "docHistoryWriter": {"dbname": "clickup", "max_clients": 10, "secret": "local-only"}, "emailsSentWriter": {"secret": "local-only"}, "emojiDbRead": {"max_clients": 50, "secret": "local-only"}, "emojiDbWrite": {"max_clients": 50, "secret": "local-only"}, "syncupDbRead": {"dbname": "clickup", "max_clients": 50, "secret": "local-only"}, "syncupDbWrite": {"dbname": "clickup", "max_clients": 50, "secret": "local-only"}, "taskReadQueryPool": {"dbname": "clickup", "max_clients": 80, "secret": "local-only"}, "taskReadWriteQueryPool": {"dbname": "clickup", "max_clients": 80, "secret": "local-only"}, "teamActivityReader": {"dbname": "clickup", "max_clients": 10, "secret": "local-only"}, "teamActivityWriter": {"dbname": "clickup", "max_clients": 10, "secret": "local-only"}, "templates": {"dbname": "clickup", "max_clients": 10, "secret": "local-only"}, "tmpIslandReader": {"dbname": "clickup", "max_clients": 36, "secret": "local-only"}, "tmpIslandWriter": {"dbname": "clickup", "max_clients": 36, "secret": "local-only"}, "websocketDistributorServiceReader": {"dbname": "clickup", "max_clients": 50, "secret": "local-only"}, "websocketsReader": {"dbname": "clickup", "max_clients": 30, "secret": "local-only"}, "websocketsWriter": {"dbname": "clickup", "max_clients": 30, "secret": "local-only"}, "wmsDbReader": {"dbname": "clickup", "max_clients": 50, "secret": "local-only"}, "wmsDbWriter": {"dbname": "clickup", "max_clients": 50, "secret": "local-only"}, "workSchedulesRWPool": {"dbname": "clickup", "max_clients": 50, "secret": "local-only"}}, "db": "clickup", "default_replica_percent": 0.2, "documentdb_cluster": "localhost", "documentdb_collection": "imports", "documentdb_db": "clickup", "documentdb_params": "clickup?", "documentdb_port": "27017", "documentdb_pw": "", "documentdb_ssl_cert": "rds-combined-ca-bundle.pem", "documentdb_user": "", "documentdb_usessl": "0", "host": "localhost", "idle_timeout_millis": 5000, "master_id": 0, "master_prefix": "9999", "master_prefix_chat_comments": "8999", "master_user": "clickup_admin", "microsharding": {"database": "clickup", "dbCluster": "cu_main", "enabled": false, "password": "1234abc", "port": "5432", "reloadIslandsIntervalMs": 60000, "serviceRoles": ["rw", "api", "int"], "shouldLoadLoginsFromSecret": false, "shouldSwitchPools": false, "ssl": false, "user": "clickup_admin"}, "migrate_from_host": "localhost", "migrate_from_hosts": ["localhost"], "migration_host": "localhost", "migration_remap_userids": false, "migration_server_db_pw": "", "notif_state": {"redis_host": "localhost", "redis_port": 6379, "redis_tls": false}, "physical_replicas": [], "port": "5432", "pw": "1234abc", "reader_pw": "1234abc", "reader_user": "clickup_app_local_reader", "redis_host": "localhost", "redis_port": 6379, "removed_master_ids": [], "scratchpad_db_name": "clickup", "scratchpad_db_reader": {"password": "1234abc", "port": "5432", "username": "clickup_admin"}, "scratchpad_db_writer": {"password": "1234abc", "port": "5432", "username": "clickup_admin"}, "ssl": false, "stats_port": "5432", "syncup": {"redis_host": "localhost", "redis_password": null, "redis_port": 6379, "redis_tls": false, "redis_username": null}, "user": "clickup_admin", "wms_legacy_write_default": "localhost", "writer_pw": "1234abc", "writer_user": "clickup_app_local_writer"}, "db_bounds": [[0, 4294967295]], "db_minimums": {"task_mgmt.categories": "1000000000", "task_mgmt.comment_ids": "1************", "task_mgmt.comments": "1************", "task_mgmt.items": "100000000000", "task_mgmt.projects": "1000000000", "task_mgmt.subcategories": "10000000000", "task_mgmt.tasks": "100000000000", "task_mgmt.teams": "100000000", "task_mgmt.users": "100000000"}, "default_closed_status": {"color": "#008844", "statues": "Closed"}, "default_in_progress_status": {"color": "#5f55ee", "statues": "in progress"}, "default_onboarding_template_id": "t-300139", "default_open_status": {"color": "#87909e", "statues": "Open"}, "default_statuses": [{"color": "#87909e", "status": "Open"}, {"color": "#008844", "status": "Closed"}], "default_subcat_statuses": [{"color": "#d33d44", "hide_label": true, "status": "maroon", "type": "custom"}, {"color": "#d33d44", "default": true, "hide_label": true, "status": "red", "type": "custom"}, {"color": "#ee5e99", "hide_label": true, "status": "hot pink", "type": "custom"}, {"color": "#ee5e99", "hide_label": true, "status": "pink", "type": "custom"}, {"color": "#ee5e99", "hide_label": true, "status": "magenta", "type": "custom"}, {"color": "#ee5e99", "hide_label": true, "status": "lavender", "type": "custom"}, {"color": "#b660e0", "hide_label": true, "status": "purple", "type": "custom"}, {"color": "#b660e0", "hide_label": true, "status": "plum", "type": "custom"}, {"color": "#5f55ee", "hide_label": true, "status": "orchid", "type": "custom"}, {"color": "#4466ff", "hide_label": true, "status": "royal blue", "type": "custom"}, {"color": "#4466ff", "hide_label": true, "status": "light blue", "type": "custom"}, {"color": "#1090e0", "hide_label": true, "status": "blue", "type": "custom"}, {"color": "#1090e0", "hide_label": true, "status": "navy", "type": "custom"}, {"color": "#1090e0", "hide_label": true, "status": "baby blue", "type": "custom"}, {"color": "#0f9d9f", "hide_label": true, "status": "cyan", "type": "custom"}, {"color": "#64c6a2", "hide_label": true, "status": "turquoise", "type": "custom"}, {"color": "#f8ae00", "default": true, "hide_label": true, "status": "yellow", "type": "custom"}, {"color": "#008844", "default": true, "hide_label": true, "status": "green", "type": "custom"}, {"color": "#aa8d80", "hide_label": true, "status": "bronze", "type": "custom"}, {"color": "#e16b16", "hide_label": true, "status": "orange", "type": "custom"}, {"color": "#e16b16", "hide_label": true, "status": "pumpkin", "type": "custom"}, {"color": "#656f7d", "hide_label": true, "status": "light grey", "type": "custom"}, {"color": "#656f7d", "hide_label": true, "status": "gray", "type": "custom"}], "default_task_fields": ["attachments_count", "assignees", "assigned_comments_count", "assigned_checklist_items", "fallback_coverimage", "attachments_thumbnail_count", "tags", "followers", "subtasks_by_status", "dependency_state", "parent_task", "totalTimeSpent", "subtasks_count", "simple_statuses", "rolledUpTimeSpent", "rolledUpTimeEstimate", "rolledUpPointsEstimate", "incompleteCommentCount"], "dependencies": {"states": {"blocked": 3, "blocker": 2, "clear": 1}}, "deprecatedCode": {"disabledInterval": 3600000}, "discord": {"client_id": "", "client_secret": "", "token": ""}, "docsLogging": {"local": {"endpoint": "http://localhost:8000", "region": "us-east-1", "tableName": "OperationLogs"}, "prod": {"region": "us-west-2", "tableName": ""}, "qa": {"region": "us-west-2", "tableName": "UsqaUsqaorDocsLoggingStorageDynamodbCoeditingTable"}, "staging_eu": {"region": "eu-central-1", "tableName": "StagingEuDocsLoggingStorageDynamodbCoeditingTable"}, "staging_us_east": {"region": "us-east-1", "tableName": "StagingUsEast1DocsLoggingStorageDynamodbCoeditingTable"}, "staging_us_west": {"region": "us-west-2", "tableName": "StagingUsDocsLoggingStorageDynamodbCoeditingTable"}}, "docsSearch": {"docIdsCache": {"ttl": 7200}}, "document": {"page_synopsis": {"size": 1000}}, "done_status_types": ["closed", "done"], "drip": {"api_user1": "", "api_user10": "", "api_user11": "", "api_user12": "", "api_user2": "", "api_user3": "", "api_user4": "", "api_user5": "", "api_user6": "", "api_user7": "", "api_user8": "", "api_user9": "", "segment_api_keys": [], "trigger_api_keys": []}, "dropbox": {"key": "", "secret": ""}, "dynamodb": {"local": {"endpoint": "http://localhost:8000", "region": "us-east-1"}, "qa": {"region": "us-west-2"}}, "editor_types": {"doc": 5, "folder": 3, "goal": 6, "list": 2, "notepad": 7, "space": 4, "task": 1, "widget": 8}, "elastic": {"max_content_length": 262144, "max_name_length": 1024, "queue_url": "http://localhost:9004"}, "elastic_connector": {"dlq_topic": "DeadLetterQueueESSink", "no_op_write": false, "processor_timeout_ms": 60000, "send_to_topic_retry_options": {"initialRetryTime": 1000, "maxRetryTime": 6000, "multiplier": 1, "retries": 5}, "use_dlq": false}, "email": {"webhook": {"bearer": ""}}, "email_code_validation_max_attempts": 15, "email_notifications": {"enabled": true}, "emails_from_workspace": ["TASK_CLOSED_NOTIF", "ASSIGNED_TASK_COMMENT_NOTIF", "TASK_ATTACHMENTS_NOTIF", "TASK_CREATE_NOTIF", "TASK_ASSIGNEE_NOTIF", "TASK_STATUS_NOTIF", "TASK_COMMENT_NOTIF", "TASK_OVERDUE", "COMMENT_REACTION_NOTIF", "SECTION_MOVED_NOTIF", "CONTENT_CHANGED_NOTIF", "NAME_CHANGED_NOTIF", "TASK_MERGED_NOTIF", "DEPENDS_ON_NOTIF", "DEPENDENCY_OF_NOTIF", "UNBLOCKED_NOTIF", "RECUR_NOTIF", "CHECKLIST_ITEM_ASSIGNEE", "CHECKLIST_ITEM_RESOLVED", "CHECKLIST_ITEMS_ADDED", "DUE_DATE_CHANGED", "START_DATE_CHANGED", "PRIORITY_CHANGED", "TIME_ESTIMATE_CHANGED", "CLICKBOT_RESOLVED_NOTIF", "TIME_TRACKING", "TAG_TEMPLATE", "DUE_DATE_REMINDER", "START_DATE_REMINDER", "COMMENT_MENTION_NOTIF", "RECUR_CREATED", "CUSTOM_FIELD_CHANGED", "SHARED_FOLDER_NOTIF", "SHARED_TASK_NOTIF", "ASSIGNED_TASK_COMMENT_RESOLVED", "SHARED_VIEW_NOTIF"], "enable_new_relic": false, "encryption": {"attachments": true, "checklists": true, "comments": true, "keys": {"default_encryption_key": "", "v1_encryption_key": "", "validation_code_encryption_key": "", "validation_code_mac_key": ""}, "subcategories": true, "tasks": true}, "env": "local", "es_server": false, "eu_emails_to_proxy": [], "eu_userids_to_proxy": [], "event_loop_block_dd_span_threshold_ms": 2000, "event_loop_block_log_threshold_ms": 2000, "exchange_rate": {"api_key": "", "url": "https://api.apilayer.com/exchangerates_data/"}, "export_service": {"aws": {"bucket": "clickup-local-public", "folder_prefix": "t"}}, "exports": {"exports_bucket": "clickup-exports-local", "limit": 5, "workspace_export_expires_ms": *********}, "facebook_ads": {"ads_account": "", "token": ""}, "features": {"check_unresolved": {"checklists": true, "comments": true, "subtasks": true}, "checklists": {}, "custom_fields": {}, "custom_items": {}, "dependency_warning": {}, "due_dates": {"remap_closed_due_dates": false, "remap_due_dates": false, "start_date": false}, "emails": {"enabled": false}, "milestones": {}, "points": {"points_scale": [1, 2, 3, 5, 8]}, "points_estimate_rollup": {}, "portfolios": {}, "priorities": {}, "remap_dependencies": {}, "sprints": {}, "status_pies": {}, "tags": {}, "time_estimates": {"rollup": true}, "time_in_status": {"enabled": false}, "time_tracking": {"rollup": true}, "wip_limits": {}, "zoom": {}}, "field_types": {"app_object": 24, "app_object_property": 25, "attachment": 16, "automatic_progress": 13, "button": 23, "checkbox": 6, "currency": 8, "date": 4, "drop_down": 1, "email": 2, "email_list": 20, "emoji": 11, "formula": 17, "labels": 12, "list_relationship": 18, "location": 19, "manual_progress": 14, "number": 7, "phone": 3, "short_text": 15, "signature": 22, "tasks": 9, "text": 5, "url": 0, "users": 10, "votes": 21}, "fields": {"auto_progress_options": {"always_complete": 2, "always_incomplete": 1, "done_status_complete": 3}, "change_event_log": {"dynamodb": {"table_name": "field-change-event-log-db", "ttl_days": 90}}, "max_value_size": {"short_text": 2048}, "permission_levels": {"1": {"can_pin_fields": false, "can_read": true, "convert_custom_fields": false, "delete": false, "delete_custom_fields": false, "edit_custom_fields": false, "edit_view": true, "merge_custom_fields": false, "move_custom_fields": false, "set_custom_field_values": false}, "3": {"can_pin_fields": false, "can_read": true, "convert_custom_fields": false, "delete": false, "delete_custom_fields": false, "edit_custom_fields": false, "edit_view": true, "merge_custom_fields": false, "move_custom_fields": false, "set_custom_field_values": true}, "4": {"can_pin_fields": true, "can_read": true, "convert_custom_fields": true, "delete": false, "delete_custom_fields": false, "edit_custom_fields": true, "edit_view": true, "merge_custom_fields": false, "move_custom_fields": true, "set_custom_field_values": true}, "5": {"can_pin_fields": true, "can_read": true, "convert_custom_fields": true, "delete": true, "delete_custom_fields": true, "edit_custom_fields": true, "edit_view": true, "merge_custom_fields": true, "move_custom_fields": true, "set_custom_field_values": true}}, "relationship_types": {"direct": 1, "list": 3, "task": 2}, "searchable": [0, 1, 2, 3, 5, 7, 15, 19], "signature_field": {"max_size": {"base64": 307200, "typed": 1024}}}, "first_task": {"content": {"ops": [{"insert": "ClickUp was created out of frustration with everything else. We believe great design makes you more efficient, more productive, and generally happier. \n\nWe'll be releasing a new version of ClickUp every week loaded with awesome new upgrades. \n24/7 Support. No exceptions. "}, {"attributes": {"header": 2}, "insert": "\n"}, {"insert": "We pride ourselves on having the highest rated customer service in the industry. Go ahead, test us out! \n"}, {"attributes": {"link": "http://localhost:4201/support/demo"}, "insert": "Get a personalized demo or walkthrough"}, {"attributes": {"list": "ordered"}, "insert": "\n"}, {"attributes": {"link": "http://localhost:4201/support/call"}, "insert": "Call us: 888-625-4258"}, {"attributes": {"list": "ordered"}, "insert": "\n"}, {"attributes": {"link": "http://localhost:4201/support"}, "insert": "Ask a Question"}, {"attributes": {"list": "ordered"}, "insert": "\n"}, {"attributes": {"link": "http://localhost:4201/support/chat"}, "insert": "Live Chat"}, {"attributes": {"list": "ordered"}, "insert": "\n"}, {"attributes": {"link": "http://docs.clickup.com/features/priorities"}, "insert": "Priorities"}, {"insert": " "}, {"attributes": {"list": "ordered"}, "insert": "\n"}, {"insert": "Features"}, {"attributes": {"header": 2}, "insert": "\n"}, {"insert": "ClickUp's philosophy is "}, {"attributes": {"underline": true}, "insert": "everything"}, {"insert": " should be customizable. This means you have the power to keep the features you want, and get rid of what you don't use. \nWe're releasing at least one new feature every single week. Bear with us while we rollout 37 new features over the next couple of months. "}, {"attributes": {"blockquote": true}, "insert": "\n"}, {"attributes": {"link": "http://help.clickup.com/features/features-roadmap"}, "insert": "Take a look at our features roadmap"}, {"insert": " for the next 30 days: \nIntegrations"}, {"attributes": {"header": 2}, "insert": "\n"}, {"attributes": {"link": "https://clickup.com/blog/clickup-slack/"}, "insert": "<PERSON><PERSON>ck"}, {"insert": " "}, {"attributes": {"list": "ordered"}, "insert": "\n"}, {"attributes": {"link": "https://clickup.com/blog/clickup-gets-github/"}, "insert": "<PERSON><PERSON><PERSON>"}, {"attributes": {"list": "ordered"}, "insert": "\n"}, {"insert": "Time Tracking (coming soon)"}, {"attributes": {"list": "ordered"}, "insert": "\n"}, {"insert": "22 more integrations on the way"}, {"attributes": {"list": "ordered"}, "insert": "\n"}, {"insert": "Importing"}, {"attributes": {"header": 2}, "insert": "\n"}, {"insert": "Do you use another project management solution? ClickUp can automatically import from most popular platforms. Simply visit your team settings page to get started or "}, {"attributes": {"link": "https://calendly.com/clickup/call"}, "insert": "get an expert to do this for you"}, {"insert": ". \nOur mission"}, {"attributes": {"header": 2}, "insert": "\n"}, {"attributes": {"background": "#ffff00"}, "insert": "Our mission is to make the world more productive. If there's anything we can do to increase your efficiency or general happiness using ClickUp, "}, {"attributes": {"background": "#ffff00", "link": "http://localhost:4201/feature"}, "insert": "let us know!"}, {"attributes": {"background": "#ffff00"}, "insert": " "}, {"insert": "\n\n\n"}]}, "name": "Welcome to ClickUp!"}, "front": {"auth_key": ""}, "front_internal": {"hub_subcategory": "************", "hub_task_relationship_cf_name": "Hub Tasks <> Defect Reports", "internal_team_id": 333, "luna_bot_id": 53652, "plan_mapping": {"Business": "f0b2512c-77c5-40d3-9d2f-f7d5a1f7e9ca", "Enterprise": "326e2507-d0b7-4e1e-8d46-0382dd968dc2", "Free Forever": "bd7205e8-adf3-4512-b1a7-63a535fc4f94", "Trial": "01aef94b-dbcd-4b38-baf7-c6367ac8c13c", "Unlimited": "9af6a141-00c4-4bcc-96d8-3290418664a9"}, "sfdc_account_id_field": "51bd829c-decb-4dad-ae5f-ea2ae8fb74a2", "templates": {"cancellation": "9jpeg1", "delete_my_data": "1vgvu58", "hotfix": "2xuxtk", "operations_request": "d4vd2j", "report": "8bqan7h"}, "total_reports_cf": "236037b5-901a-4ad2-856d-3d391c90c576"}, "frontdoor": {"eligibility_cache_ttl": 10800, "prefix": "frontdoor-"}, "gantt": {"api_server_limit": 200, "local_port": 9005, "workers": {"local": 2, "prod": 4, "staging": 2}}, "gantt_export": {"cu_gantt_export_access_key_private": "", "expiration": "5m"}, "generic_links": {"types": {"page": {"int": 2, "name": "page", "parent": 1}, "task": {"int": 3, "name": "task"}, "view": {"int": 1, "name": "view", "query_only": true}}}, "github": {"client_id": "", "client_secret": "", "hmac_secret": ""}, "gitlab": {"client_id": "", "client_secret": ""}, "global_db": {"db": "clickup_global", "enable": true}, "global_region": true, "global_table_checks": {"enable": true, "logging_interval_ms": 60000, "logging_samples": 10, "type_to_tables": {"type1": ["task_mgmt.asana_codes", "task_mgmt.basecamp_tokens", "task_mgmt.monday_tokens", "task_mgmt.trello_oauth_tokens", "task_mgmt.trello_tokens", "task_mgmt.wrike_tokens", "task_mgmt.subtasks_column_view_settings", "task_mgmt.anti_cancellation_flow_teams", "crm.anti_cancellation_flow_teams", "task_mgmt.promo_code_authorizations", "crm.promo_code_authorizations", "task_mgmt.promo_code_history", "crm.promo_code_history", "task_mgmt.subscription_activity", "crm.subscription_activity", "task_mgmt.team_edit_audit", "crm.team_edit_audit", "task_mgmt.teams_stats", "crm.teams_stats", "task_mgmt.transactions", "crm.transactions", "task_mgmt.credit_used", "crm.credit_used", "task_mgmt.failed_transactions_to_submit", "task_mgmt.team_addons", "task_mgmt.team_plan_history", "task_mgmt.teams_to_charge", "task_mgmt.default_projects", "task_mgmt.discord_settings", "task_mgmt.team_role_permissions", "task_mgmt.nylas_account_members", "task_mgmt.users_invited", "task_mgmt.checklist_template_members", "task_mgmt.preset_task_templates", "task_mgmt.dashboard_templates", "task_mgmt.orderindexes", "task_mgmt.workspace_data_replicator_dlq"], "type2": ["crm.allowed_email", "crm.calendly_meetings", "crm.email_actions", "crm.email_templates", "crm.ip_info", "crm.refresh_tokens", "crm.team_notes", "crm.user_notes", "crm.user_tags", "crm.users", "crm.blacklisted_users", "crm.resource_center_forms", "crm.users_stats", "crm.sales_form_submissions", "crm.view_fields", "crm.view_filters", "crm.views", "crm.temporary_login_permissions", "task_mgmt.af_promoters", "task_mgmt.af_signups", "task_mgmt.browser_subscriptions", "task_mgmt.bounced_emails", "task_mgmt.churn_reason", "task_mgmt.emails_skip_recaptcha", "task_mgmt.login_failures", "task_mgmt.onboarding_bonus", "task_mgmt.push_tokens", "task_mgmt.ticket_stats", "task_mgmt.scratchpad_notes", "task_mgmt.team_session_settings", "task_mgmt.workspace_backfill_state", "task_mgmt.workspace_datastore_deletion_plan", "task_mgmt.workspace_datastore_migration_plan", "task_mgmt.workspace_shard_requirements", "task_mgmt.workspace_migration_batches", "task_mgmt.workspace_migration_objects", "task_mgmt.workspace_migrations", "task_mgmt.deleted_teams", "task_mgmt.authed_cards", "task_mgmt.email_ma_status", "task_mgmt.dev_api_keys", "task_mgmt.user_settings", "task_mgmt.discord", "task_mgmt.front_internal", "task_mgmt.front_ca", "task_mgmt.ghangouts", "task_mgmt.front_snooze", "task_mgmt.integration_external_resources", "task_mgmt.integration_oauth", "task_mgmt.intercom_auth", "task_mgmt.intercom_link", "task_mgmt.sentry", "task_mgmt.tableau", "task_mgmt.zendesk", "task_mgmt.admin_login", "task_mgmt.email_domain_ownership", "task_mgmt.email_leads", "task_mgmt.saml", "task_mgmt.user_onboarding_segmentation", "task_mgmt.user_calculated_attributes", "task_mgmt.user_workspace_onboarding_segmentation", "task_mgmt.user_sessions", "task_mgmt.saml_certs", "crm.promo_codes", "crm.promo_code_addon", "task_mgmt.user_audit", "task_mgmt.user_agents", "task_mgmt.user_email_change_requests", "task_mgmt.two_factor_codes", "task_mgmt.oauth_access_tokens", "task_mgmt.oauth_access_token_teams", "task_mgmt.oauth_clients", "task_mgmt.oauth_grant_tokens", "task_mgmt.oauth_redirect_uris", "task_mgmt.oauth_integrations_settings", "task_mgmt.google_ids", "task_mgmt.azure_ids", "task_mgmt.okta_ids", "task_mgmt.saml_ids", "task_mgmt.stopped_sms", "task_mgmt.referrers", "task_mgmt.session_tokens", "task_mgmt.white_label_teams", "task_mgmt.user_status", "task_mgmt.user_status_recents", "task_mgmt.config_vars", "task_mgmt.bb_pull_requests", "task_mgmt.bitbucket_branches", "task_mgmt.bitbucket_commits", "task_mgmt.bitbucket_repos", "task_mgmt.bitbucket_settings", "task_mgmt.bitbucket_users", "task_mgmt.gitlab_branches", "task_mgmt.gitlab_commits", "task_mgmt.gitlab_repos", "task_mgmt.gitlab_settings", "task_mgmt.gl_pull_requests", "task_mgmt.project_bitbucket_repos", "task_mgmt.project_gitlab_repos", "task_mgmt.user_gitlab_teams", "task_mgmt.user_team_bitbucket_authorizations", "task_mgmt.category_template_groups", "task_mgmt.template_usage_stat_user", "task_mgmt.template_usage_stat_global", "task_mgmt.ical_subscriptions", "task_mgmt.google_calendar_oauth", "task_mgmt.gcal_synced_calendars", "task_mgmt.gcal_event_task", "task_mgmt.user_submitted_templates_for_review", "task_mgmt.harvest_tokens", "task_mgmt.toggl_tokens", "task_mgmt.public_templates", "task_mgmt.template_use_cases", "task_mgmt.template_to_use_case", "task_mgmt.used_templates", "task_mgmt.public_template_tags", "task_mgmt.public_template_to_tag", "task_mgmt.reminder_recur_settings", "task_mgmt.reminder_future_recurrences", "task_mgmt.delegated_reminders", "task_mgmt.inbox_reminders", "task_mgmt.teams_global", "task_mgmt.team_members_global", "task_mgmt.free_trials_global", "task_mgmt.team_billing_info_global", "task_mgmt.banned_teams_global", "crm.banned_teams_global", "task_mgmt.team_orderindex", "task_mgmt.user_data_replicator_dlq", "task_mgmt.sso_identity_providers", "task_mgmt.sso_identity_provider_workspaces"], "type3": ["task_mgmt.teams", "task_mgmt.team_members", "task_mgmt.team_billing_info", "task_mgmt.vw_active_team_billing_info", "task_mgmt.free_trials", "task_mgmt.banned_teams", "crm.banned_teams"], "type4": ["task_mgmt.users", "task_mgmt.user_profiles", "crm.banned_users"], "type5": ["alerts_stats.user_sessions", "alerts_stats.user_sessions2", "crm.data_subject_requests", "crm.emails_sent", "crm.permission_access_log", "crm.permission_titles", "crm.resource_permission_audit_log", "crm.resource_permissions", "crm.segment_history", "crm.segment_history_teams", "crm.status_message_history", "crm.team_tags", "crm.touches", "crm.user_layout_history", "crm.user_permission_audit_log", "crm.user_permissions", "task_mgmt.custom_user_attribute_results", "task_mgmt.inactive_session_tokens", "task_mgmt.seat_utilization_history", "task_mgmt.subscription_history", "task_mgmt.subscriptions", "task_mgmt.transaction_history", "task_mgmt.user_lifecycle_data"], "type6": ["task_mgmt.bdr_migration_job", "task_mgmt.replica_service_blacklist", "task_mgmt.google_drive_oauth", "task_mgmt.td_tasks", "task_mgmt.saml_sessions", "task_mgmt.task_email_tokens", "task_mgmt.empty_template_searches", "task_mgmt.hosts", "task_mgmt.ws_subscriptions", "task_mgmt.ws_subs", "task_mgmt.replica_blacklist", "task_mgmt.blocked_queries", "task_mgmt.category_template_group_templates_uses", "task_mgmt.scratchpad_history"], "type7": ["crm.task_plans", "task_mgmt.task_plans", "crm.plan_addons", "task_mgmt.plan_addons", "task_mgmt.max_prefix_id", "task_mgmt.replication_dates", "task_mgmt.workspace_to_shard"]}}, "goals": {"key_result_types": {"automatic": 5, "boolean": 2, "currency": 4, "number": 1, "percentage": 3}, "permission_levels": {"edit": 3, "read": 1}}, "google_ads": {"account_id": "", "client_id": "", "client_secret": "", "developer_token": "", "ga_user": "", "redirect_url": "http://localhost:9004/v1/google_ads/authorize", "refresh_token": "", "salted_hashed_password": ""}, "google_analytics": {"id": "UA-********-1"}, "google_calendar": {"client_id": "", "client_secret": "", "gmail_redirect_uri": "https://local-fe.clickup-qa.com", "redirect_uri": "https://app.clickup.com", "token": ""}, "google_sso": {"client_audiences": ["************-jhpu9qukj7r9f39c7nm0rikhhmmfntce.apps.googleusercontent.com", "************-4n9ckjvj3g7lk3sgvs7hrpc2ht4ff964.apps.googleusercontent.com", "************-ogbgmmjqgd3ju6cpt33m9her3irl6fqa.apps.googleusercontent.com", "************-vij357cvl6b3ap5f962g3f8guogn6t61.apps.googleusercontent.com", "************-bn2uvov352u5bpvc0mr90v0afbm1fu6l.apps.googleusercontent.com", "************-7irc173konr41mit6u2ti26fo4vfncd8.apps.googleusercontent.com", "************-1ahr4fbj0ionbhf9inacq74378pm7f5k.apps.googleusercontent.com", "629831864745-8bkkv3u1q9bccmf6epo375h3eo2ifo1m.apps.googleusercontent.com", "772659000704-acv4ofemn9uchfdb5evoq5studnr4t7d.apps.googleusercontent.com", "************-e1ck321td9huhrmcu2j73s02kp8oqhpo.apps.googleusercontent.com", "************-nqkrjeur3dgpvmj73ln03q1tt55m1eeo.apps.googleusercontent.com", "************-l7lfnjr1jmhivav780mhhe8cae644p4h.apps.googleusercontent.com"], "client_id": "", "client_secret": ""}, "growth": {"openai": {"api_key": "dummy"}}, "harvest": {"client_id": "", "client_secret": "", "interval": 90000}, "hierarchy_permission_levels": {"can_comment": 3, "can_create_and_edit": 5, "can_edit": 4, "can_read": 1}, "hierarchy_set_version": {"pipelines": {"folder": {"maxBatchSize": 100, "maxDelayMs": 1000}, "space": {"maxBatchSize": 100, "maxDelayMs": 1000}, "workspace": {"maxBatchSize": 100, "maxDelayMs": 1000}}}, "hierarchy_tables": {"checklists": "checklists", "folders": "categories", "lists": "subcategories", "spaces": "projects", "tasks": "items"}, "hist_fields": ["name", "new_subtask", "duplicate", "task_creation", "reaction", "status", "section_moved", "comment", "comment_resolved", "gh_commit", "content", "text_content", "attachments", "follower", "assignee"], "holiday": {"api_key": ""}, "hubspot": {"app_id": "", "client_id": "", "client_secret": ""}, "import_colors": ["#51e898", "#00c2e0", "#0079bf", "#c377e0", "#eb5a46", "#ffab4a", "#f2d600", "#61bd4f"], "import_server": false, "imports": {"finished": ["complete", "deleted", "failed", "canceled", "complete_warnings"], "jira": {"oauth_consumer_key": "clickupclient"}, "max_attachment_size_mb": 1000, "max_examples": 10, "s3": {"accessKeyId": "minioadmin", "endpoint": "http://localhost:9000", "region": "us-east-1", "s3ForcePathStyle": true, "secretAccessKey": "minioadmin", "signatureVersion": "v4"}, "successful": ["complete", "completed", "complete_warnings"], "uploads_bucket": "cu-imports"}, "integrations": ["asana", "jira"], "integrations_secrets": "managedSecret/v1/env/true/flat-json/managed_integrations_secrets", "intercom": {"bearer": "", "chat_secret": "", "client_id": "", "client_secret": "", "oauth_client_id": "", "oauth_client_secret": ""}, "interval_marketing_server": false, "interval_server": false, "interval_sysid": null, "interval_url": "http://localhost:9004", "invite_email_blacklist": ["gettyimages.com", "vmware.com"], "ipstack": {"access_key": ""}, "items": {"deleteCheckInterval": 300000, "permanentDeleteLimit": 5000}, "iterable": {"emails_integration": {"base_url": "https://api.iterable.com", "credentials": {"api_key": "", "workflow_ids": {"ai_autopilot_purchased": 0, "productivity_pack_cancellation_workflow_id": 0, "productivity_pack_purchase_workflow_id": 0}}, "trigger_worklow_url": "/api/workflows/triggerWorkflow"}}, "jwt": {"emailRevalidation": "5m", "expiration": "2d", "magic": "5m", "private_keys": {"signing_keys": "managedSecret/v1/env/true/json/user_platform_auth", "twoFA": "default"}, "recent_refresh_token_threshold": "60s", "recoverExp": "1d", "refresh_token_rate_limiting_window": 60000, "refresh_token_rotation_time": "7d", "replication_lag_tolerance_threshold_millis": 120000, "s2s": {"keys": "managedSecret/v1/env/true/json/s2s_keys"}, "same_refresh_token_threshold": "60s", "ws_key_lower_bound": 1, "ws_key_upper_bound": 9000000000}, "kafka": {"auth": {"SCRAM": {"bootstrap_servers": {"foundationaljobs": ["127.0.0.1:9096", "127.0.0.1:9096", "127.0.0.1:9096"], "ovm": ["b-1-public.test-cluster-b-pub.skl5r4.c21.kafka.us-east-1.amazonaws.com:9196", "b-2-public.test-cluster-b-pub.skl5r4.c21.kafka.us-east-1.amazonaws.com:9196", "b-3-public.test-cluster-b-pub.skl5r4.c21.kafka.us-east-1.amazonaws.com:9196"]}, "secrets": {"be-ai": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-attachment-storage-reset-consumer": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-attachments-event-listener": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-auditlog": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-authz-worker": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-auto-ovm-consumer": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-automation": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-clips": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-comments-last-read-at-consumer": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-content-consumer": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-dashboard": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-docs-sub": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-field-sub": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-foundational-pub": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-hierarchy": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-hierarchy-worker": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-integration": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-search": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-shadow-task-consumer": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-task-sub": {"secretName": "kafka-user", "secretRegion": "us-east-2"}, "be-user-data-replicator-consumer": {"secretName": "kafka-user", "secretRegion": "us-east-2"}, "be-version-pub": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-view-pub-sub": {"secretName": "kafka-user", "secretRegion": "us-east-2"}, "be-websocket-distributor-service": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-websocket-ovm-consumer": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "be-workspace-data-replicator-consumer": {"secretName": "kafka-user", "secretRegion": "us-east-2"}, "be-workspace-user-worker": {"secretName": "kafka-user", "secretRegion": "us-west-2"}, "default-client-id": {"secretName": "kafka-user", "secretRegion": "us-west-2"}}}, "method": "NONE"}, "confluent_cloud": false, "consumer": {"allowAutoTopicCreation": false, "errorRetryDelayMaxMs": 10000, "errorRetryDelayMinMs": 200, "errorRetryMax": 10, "maxWaitTimeInMs": 1000, "pauseThreshold": 100, "processTimeoutMs": 10000}, "logLevel": "INFO", "password": "", "producer": {"allowAutoTopicCreation": false}, "username": ""}, "kafka_import_server": false, "lb_url": "localhost:9004", "lb_urls": [], "legacy_emoji_code_points": ["263a", "2639", "2620", "2763", "2764", "270b", "270c", "261d", "270a", "270d", "26f7", "26f9", "2618", "2615", "26f0", "26ea", "26e9", "26f2", "26fa", "2668", "26fd", "2693", "26f5", "26f4", "2708", "231b", "23f3", "231a", "23f0", "23f1", "23f2", "2600", "2b50", "2601", "26c5", "26c8", "2602", "2614", "26f1", "26a1", "2744", "2603", "26c4", "2604", "2728", "26bd", "26be", "26f3", "26f8", "2660", "2665", "2666", "2663", "265f", "26d1", "260e", "2328", "2709", "270f", "2712", "2702", "26cf", "2692", "2694", "2699", "2696", "26d3", "2697", "26b0", "26b1", "267f", "26a0", "26d4", "2622", "2623", "2b06", "2197", "27a1", "2198", "2b07", "2199", "2b05", "2196", "2195", "2194", "21a9", "21aa", "2934", "2935", "269b", "2721", "2638", "262f", "271d", "2626", "262a", "262e", "2648", "2649", "264a", "264b", "264c", "264d", "264e", "264f", "2650", "2651", "2652", "2653", "26ce", "25b6", "23e9", "23ed", "23ef", "25c0", "23ea", "23ee", "23eb", "23ec", "23f8", "23f9", "23fa", "23cf", "2640", "2642", "2695", "267e", "267b", "269c", "2b55", "2705", "2611", "2714", "2716", "274c", "274e", "2795", "2796", "2797", "27b0", "27bf", "303d", "2733", "2734", "2747", "203c", "2049", "2753", "2754", "2755", "2757", "3030", "00a9", "00ae", "2122", "2139", "24c2", "3297", "3299", "26ab", "26aa", "2b1b", "2b1c", "25fc", "25fb", "25fe", "25fd", "25aa", "25ab", "1f43f"], "legacy_shard_url": "http://localhost:9004", "links": {"types": {"checklist": 3, "comment": 0, "doc": 4, "subcategory": 2, "task": 1}}, "live_view": {"permissions": {"admin": 3, "member": 2, "none": 0, "team": 1}}, "local_node_sysid": null, "localstack": {"endpoint": "http://localhost:4566"}, "localtunnel_server": false, "logDNA": {"key": "6d89553d85214a0769de1d983df2dc26"}, "log_min_duration": 7000, "loginsBackoffMaxTimeoutSeconds": 60, "loginsBackoffNoTimeoutCount": 3, "loginsBackoffTimeoutAddSeconds": 1, "loginsBackoffTimeoutMult": 1.5, "maintenance_mode": false, "max": {"categoriesInProject": 400, "subcategoriesInCategory": 400, "task_batch": 3, "tasks": 100000, "tasks_move": 10}, "maxFileSize": 1000000000, "maxIPLogins": 100, "maxItemViews": 100, "maxLogins": 10, "maxLoginsTimeoutMinutes": 30, "max_nesting_level": 7, "maxmind": {"pass": "", "timeout": 5000, "user": ""}, "maxmind_accuracy_type": {"city": "city", "country": "country", "postal": "postal"}, "microsoft": {"teams": {"app_url": "", "ms_teams_bot_id": "", "ms_teams_bot_password": "", "ms_teams_oauth_client": ""}}, "microsoft_integrations": {"client_id": "", "client_secret": ""}, "min_plan": {"signed_attachments": 1}, "mq_server": true, "mulesoft": {"base_url": "https://bussys-systems-eapi-dev.us-w2.cloudhub.io", "client_id": "10d31cfa27aa462699fa49cd9736fc72", "client_secret": "565D7c7b8F654faAB5a2cb1e3C2002F4"}, "notif_queue_use_redis": true, "notifications": {"default_no_emails": 1618345624265, "default_no_emails_revert": 1627920465456, "default_settings": {"access_request_approved": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 1, "mobile_push": 0, "web_app": 1}, "access_requested": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "added_to_priorities": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "archived": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "assigned_to_me": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "assigned_to_me_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "assignee": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "assignee_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "attachments": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "attachments_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "bb_commit": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "bb_issue": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "bb_issue_updated": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "bb_pr_approved": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "bb_pr_created": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "bb_pr_fulfilled": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "bb_pr_rejected": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "bb_pr_unapproved": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "bb_pr_updated": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "bb_pull_request": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "bb_task_branch": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "chat_thread_link": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "checklist_item_added": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "checklist_item_assignee": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "checklist_item_assignee_to_me": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "checklist_item_resolved": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "checklist_items_added": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "clickbot": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "comment": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "comment_assigned": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "comment_assigned_chat": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "comment_assigned_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 1, "web_app": 1}, "comment_assigned_to_me": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "comment_assigned_to_me_chat": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 0, "mobile_push": 1, "web_app": 1}, "comment_assigned_to_me_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 1, "web_app": 1}, "comment_assigned_to_me_view": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 1, "web_app": 1}, "comment_assigned_view": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "comment_chat": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 0, "mobile_push": 1, "web_app": 0}, "comment_dms_chat": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 0}, "comment_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "comment_resolved": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "comment_resolved_chat": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 0, "mobile_push": 1, "web_app": 0}, "comment_resolved_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 1, "web_app": 1}, "comment_resolved_view": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 1, "web_app": 1}, "comment_tagged": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "comment_tagged_chat": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 0}, "comment_tagged_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "comment_tagged_view": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "comment_threaded_chat": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 0, "mobile_push": 1, "web_app": 0}, "comment_view": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 1, "mobile_push": 0, "web_app": 1}, "content": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "content_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "custom_field": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "custom_type": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 0, "mobile_push": 1, "web_app": 1}, "dependency_of": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "depends_on": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "description_group_tag": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "description_group_tag_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "description_group_tag_view": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "description_tag": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 0, "web_app": 1}, "description_tag_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "description_tag_view": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "due_date": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "due_date_missed": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "due_date_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "due_date_reminder": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "due_date_reminder2": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "due_date_summary": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "email_my_tasks": 1, "mobile_push": 1, "time_of_day_hour": 7, "time_of_day_minute": 0, "web_app": 1}, "duplicate": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "duration": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "follower": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "follower_chat": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 0, "mobile_push": 1, "web_app": 1}, "follower_view": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 0, "mobile_push": 1, "web_app": 1}, "gh_commit": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "gh_pull_request": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "gh_task_branch": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "github_pull_request": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "gl_commit": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "gl_issue": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "gl_merge_request": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "gl_task_branch": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "group_assignee": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 0, "mobile_push": 1, "web_app": 1}, "linked_task": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "name": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "name_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "new_subtask": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "orderindex": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "points": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "priority": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "priority_overview": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "private": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "reaction": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "reaction_chat": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 0, "mobile_push": 1, "web_app": 0}, "reaction_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "reaction_view": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "recur": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "reminder_v2": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "reminders": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 0, "mobile_push": 1, "web_app": 1}, "removed_from_priorities": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "resolved_items": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "section_moved": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "shared_with_me": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 1, "mobile_push": 0, "web_app": 1}, "shared_with_me_cat": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 1, "mobile_push": 0, "web_app": 1}, "shared_with_me_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 1, "mobile_push": 0, "web_app": 1}, "shared_with_me_view": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "start_date": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "start_date_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "start_date_reminder": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "status": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "status_overview": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "subtask_parent": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "summary_task": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "tag": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "tag_removed": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "task_creation": {"browser_enabled": 1, "desktop_enabled": 1, "email_enabled": 1, "mobile_push": 1, "web_app": 1}, "template_merged": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "time_estimate": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "time_spent": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 0}, "timesheet_approval_reminder": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "unblocked": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "upgrade_access_request_approved": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "upgrade_access_requested": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "zoom_meeting": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "zoom_meeting_ended": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "zoom_meeting_started": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}, "zoom_recording_completed": {"browser_enabled": 0, "desktop_enabled": 0, "email_enabled": 0, "mobile_push": 0, "web_app": 1}}, "mandatory_web_app": ["status", "comment_assigned", "comment_assigned_to_me", "comment_tagged", "checklist_item_assignee", "task_creation", "comment", "assigned_to_me", "assigned_to_me_overview", "comment_tagged_overview", "description_tag", "description_tag_overview", "description_tag_view", "comment_tagged_view", "comment_assigned_view", "comment_assigned_to_me_view"], "max_loop_count": 100, "valid_hour_intervals_in_minutes": [-720, -660, -600, -540, -480, -420, -360, -300, -240, -180, -120, -60, 0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720], "valid_minutes_after": [5, 15, 30, 60, 120, 180, 360, 480, 720, 1440, 2880, 4320, -5, -15, -30, -60, -120, -180, -360, -480, -720, -1440, -2880, -4320], "valid_settings": ["access_request_approved", "access_requested", "added_to_priorities", "assigned_to_me", "assigned_to_me_overview", "assignee", "assignee_overview", "attachments", "attachments_overview", "bb_commit", "bb_issue", "bb_issue_updated", "bb_pr_approved", "bb_pr_created", "bb_pr_fulfilled", "bb_pr_rejected", "bb_pr_unapproved", "bb_pr_updated", "chat_thread_link", "checklist_item_added", "checklist_item_assignee", "checklist_item_assignee_to_me", "checklist_item_resolved", "checklist_items_added", "clickbot", "comment", "comment_assigned", "comment_assigned_chat", "comment_assigned_to_me", "comment_assigned_to_me_chat", "comment_assigned_to_me_view", "comment_assigned_view", "comment_chat", "comment_dms_chat", "comment_overview", "comment_resolved", "comment_resolved_chat", "comment_resolved_view", "comment_tagged", "comment_tagged_chat", "comment_tagged_overview", "comment_tagged_view", "comment_threaded_chat", "comment_view", "content", "content_overview", "custom_field", "custom_type", "follower_chat", "follower_view", "dependency_of", "depends_on", "description_tag", "description_tag_overview", "description_tag_view", "due_date", "due_date_missed", "due_date_overview", "due_date_reminder", "due_date_reminder2", "due_date_summary", "duplicate", "gh_commit", "gh_pull_request", "gh_task_branch", "github_pull_request", "gl_commit", "gl_issue", "gl_merge_request", "gl_task_branch", "linked_task", "name", "name_overview", "new_subtask", "points", "priority", "priority_overview", "reaction", "reaction_chat", "reaction_overview", "reaction_view", "recur", "removed_from_priorities", "reminders", "resolved_items", "section_moved", "shared_with_me", "shared_with_me_cat", "shared_with_me_overview", "shared_with_me_view", "start_date", "start_date_overview", "start_date_reminder", "status", "tag", "tag_removed", "task_creation", "time_estimate", "time_spent", "unblocked", "zoom_meeting_ended", "zoom_meeting_started", "zoom_recording_completed"]}, "nylas": {"backsync": {"batch_size": 100, "batch_timeout": 2000, "period_in_months": 3}, "client_id": "", "client_secret": "", "microsoft_client_id": "", "microsoft_client_secret": "", "send_email_retry_count": 6, "send_email_retry_factor": 2, "send_email_retry_min_timeout": 2000, "static_ip": {"client_id": "", "client_secret": ""}, "v3": {"api_key": "", "microsoft_client_id": "", "microsoft_client_secret": "", "webhook_secret": ""}}, "oauth": {"code_expiration": 1200000}, "oauth_apps_rate_limit_increased": ["FQUGBA4DVXNTXWR3NU1BI7X10RLJ7NQL", "MVAP0BJFPSFZJUND3YE19VN51RKYOBJZ"], "object_cache_manager": {"object_change_versions_ttl_days": 90, "redis": {"host": "localhost", "port": 6379, "useTls": false}}, "object_cache_manager_comments": {"object_change_versions_ttl_days": 90, "redis": {"host": "localhost", "port": 6379, "useTls": false}}, "object_version": {"cache_ttl_seconds": 900, "change_cache_ttl_seconds": 86400, "delete_age_seconds": 604800, "delete_check_interval_ms": 30000, "delete_chunk_size": 250, "event_batch_size": 100, "max_deletes_per_interval": 25000, "max_retry_cdc_per_interval": 500, "object_factory_batch_size": 200, "publish_version_vector": false, "publish_version_vector_topic": "ObjectVersionVectorCDC", "redis_batch_size": 500, "retry_cdc_age_max_seconds": 604800, "retry_cdc_age_seconds": 15, "retry_cdc_check_interval_ms": 15000, "retry_cdc_chunk_size": 100, "send_to_topic_retry_options": {"initialRetryTime": 1000, "maxRetryTime": 6000, "multiplier": 1, "retries": 5}, "version_redis_batch_size": 200, "version_update_request_filters": {"skip_version_update_requests_with_long_object_ids": ["attachment"]}}, "onboarding_bonus": {"credit": "10", "storage": "1"}, "onboarding_template_picker_ids": [4404185, 4392236, 6463144, 4418933, 4372640, 4389472, 1545110], "onetool": {"api_key": ""}, "open_status_types": ["open"], "orderindexes": {"repair_form": {"target_env": "http://localhost:9004", "workspace_field": "5f02b879-48e8-4ac0-9794-c74769c52ee7"}, "valid_types": ["orderindex", "subtask_orderindex", "assignee_orderindex", "due_date_orderindex", "priority_orderindex", "tag_orderindex", "none_orderindex", "field_orderindex", "custom_items_orderindex"]}, "outlook": {"client_id": {"dev": "", "prod": ""}}, "outreach": {"client_id": "", "client_secret": "", "redirect_uri": "https://api.clickup.com/v1/outreach/authorize"}, "overrideEmails": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "k<PERSON><PERSON><PERSON>@gmail.com", "<EMAIL>", "<EMAIL>"], "page_length": {"activity_items": 30, "assigned_comments": 20, "attachments": 10, "automations": 30, "comments": 25, "custom_item_portfolio_widget": 10, "custom_items": 30, "dashboards": 30, "doc_tags": 30, "filter_items": 10, "history": 10, "home_view_comments": 10, "inbox": 30, "invoices": 25, "item_views": 25, "me_tasks": 20, "most_used_templates": 10, "notifications": 30, "notificationsPerTask": 10, "notificationsPerTaskArchived": 3, "notificationsPerTaskShowMore": 30, "project_members": 100, "projects": 10, "public_items": 30, "recent": 20, "revisions": 20, "search": 20, "table": 30, "tags": 30, "tasks": 30, "team_members": 100, "teams": 10, "templates": 15, "timeline": 25, "trash": 30}, "parent_types": {"category": 5, "dashboard": 20, "project": 4, "shared": 8, "subcategory": 6, "task": 1, "team": 7, "template": 9, "view": 14}, "paywalls": {"activity_view": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "limits": {"1": 1, "13": 1, "14": 7, "2": 7, "6": 7, "7": 1, "8": 7, "*********": 2}}, "admin_can_manage": {"available_plans": [4, 11, 12, 17], "limits": {}}, "admin_control_private_fields": {"available_plans": [4, 11, 12, 17]}, "advanced_card_views": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17], "limits": {"1": 0, "13": 0, "14": 0, "15": 0, "2": 0, "6": 0, "7": 0, "8": 0, "*********": 2}}, "advanced_form_view": {"available_plans": [3, 4, 5, 10, 11, 12, 16, 17]}, "advanced_views": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 14, 15, 16]}, "aggregated_auto_ai": {"free": {"available_plans": [], "limits": {"1": 50, "10": 0, "11": 0, "12": 0, "13": 50, "14": 0, "15": 0, "16": 0, "17": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 50, "8": 0, "9": 0, "*********": 2}}, "paid": {"addon_10000_limits": {"1": 0, "10": 10000, "11": 10000, "12": 10000, "13": 0, "14": 10000, "15": 10000, "16": 10000, "17": 10000, "2": 10000, "3": 10000, "4": 10000, "5": 10000, "6": 10000, "7": 0, "8": 10000, "9": 10000, "*********": 2}, "addon_1000_limits": {"1": 0, "10": 1000, "11": 1000, "12": 1000, "13": 0, "14": 1000, "15": 1000, "16": 1000, "17": 1000, "2": 1000, "3": 1000, "4": 1000, "5": 1000, "6": 1000, "7": 0, "8": 1000, "9": 1000, "*********": 2}, "ai_addon_available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17], "ai_addon_per_user_limits": {"1": 0, "13": 0, "7": 0, "*********": 2}, "available_plans": [], "base_per_user_limits": {"1": 0, "10": 50, "11": 50, "12": 50, "13": 0, "14": 50, "15": 50, "16": 50, "17": 50, "2": 50, "3": 50, "4": 50, "5": 50, "6": 50, "7": 0, "8": 50, "9": 50, "*********": 2}, "limits": {"1": 0, "10": 50, "11": 50, "12": 50, "13": 0, "14": 50, "15": 50, "16": 50, "17": 50, "2": 50, "3": 50, "4": 50, "5": 50, "6": 50, "7": 0, "8": 50, "9": 50, "*********": 2}}}, "ai_enabled": {"available_plans": [1, 2, 6, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}, "ai_meeting_bot": {"lifetime": {"available_plans": [], "limits": {"1": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "*********": 0}}, "recurring": {"ai_meeting_addon_limits": {"ai_meeting_a_2412": {"1": 0, "10": 216000, "11": 216000, "12": 216000, "13": 0, "14": 216000, "15": 216000, "16": 216000, "17": 216000, "2": 216000, "3": 216000, "4": 216000, "5": 216000, "6": 216000, "7": 0, "8": 216000, "9": 216000, "*********": 300}, "ai_meeting_b_2412": {"1": 0, "10": 432000, "11": 432000, "12": 432000, "13": 0, "14": 432000, "15": 432000, "16": 432000, "17": 432000, "2": 432000, "3": 432000, "4": 432000, "5": 432000, "6": 432000, "7": 0, "8": 432000, "9": 432000, "*********": 600}, "ai_meeting_c_2412": {"1": 0, "10": 864000, "11": 864000, "12": 864000, "13": 0, "14": 864000, "15": 864000, "16": 864000, "17": 864000, "2": 864000, "3": 864000, "4": 864000, "5": 864000, "6": 864000, "7": 0, "8": 864000, "9": 864000, "*********": 900}, "ai_meeting_d_2412": {"1": 0, "10": 1800000, "11": 1800000, "12": 1800000, "13": 0, "14": 1800000, "15": 1800000, "16": 1800000, "17": 1800000, "2": 1800000, "3": 1800000, "4": 1800000, "5": 1800000, "6": 1800000, "7": 0, "8": 1800000, "9": 1800000, "*********": 1200}, "ai_meeting_e_2501": {"1": 0, "10": 3600000, "11": 3600000, "12": 3600000, "13": 0, "14": 3600000, "15": 3600000, "16": 3600000, "17": 3600000, "2": 3600000, "3": 3600000, "4": 3600000, "5": 3600000, "6": 3600000, "7": 0, "8": 3600000, "9": 3600000, "*********": 1500}, "ai_meeting_f_2501": {"1": 0, "10": 7200000, "11": 7200000, "12": 7200000, "13": 0, "14": 7200000, "15": 7200000, "16": 7200000, "17": 7200000, "2": 7200000, "3": 7200000, "4": 7200000, "5": 7200000, "6": 7200000, "7": 0, "8": 7200000, "9": 7200000, "*********": 1800}, "ai_meeting_g_2501": {"1": 0, "10": 14400000, "11": 14400000, "12": 14400000, "13": 0, "14": 14400000, "15": 14400000, "16": 14400000, "17": 14400000, "2": 14400000, "3": 14400000, "4": 14400000, "5": 14400000, "6": 14400000, "7": 0, "8": 14400000, "9": 14400000, "*********": 2100}, "ai_meeting_h_2501": {"1": 0, "10": 43200000, "11": 43200000, "12": 43200000, "13": 0, "14": 43200000, "15": 43200000, "16": 43200000, "17": 43200000, "2": 43200000, "3": 43200000, "4": 43200000, "5": 43200000, "6": 43200000, "7": 0, "8": 43200000, "9": 43200000, "*********": 2400}}, "available_plans": [], "limits": {"1": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "*********": 0}}}, "ai_usage": {"available_plans": [], "limits": {"1": 25, "10": 100, "11": 100, "12": 100, "13": 25, "14": 50, "15": 50, "16": 100, "17": 100, "2": 50, "3": 50, "4": 100, "5": 100, "6": 50, "7": 25, "8": 50, "9": 50, "*********": 2}}, "attachment_comments": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "err_code": "COMM_006", "limits": {"1": 100, "13": 100, "14": 100, "2": 100, "6": 100, "7": 100, "8": 100, "*********": 2}, "title": "Proofing"}, "audit_logs": {"available_plans": [4, 11, 12, 17]}, "audit_logs_limit": {"available_plans": [], "limits": {"1": 1, "10": 30, "11": 180, "12": 180, "13": 1, "14": 1, "15": 1, "16": 30, "17": 180, "2": 1, "3": 1, "4": 180, "5": 30, "6": 1, "7": 1, "8": 1, "9": 1}}, "automated_ai_agent": {"available_plans": [1, 2, 6, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "err_code": "AUTO_967", "limits": {}}, "automated_integrations": {"available_plans": [1, 2, 6, 3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "err_code": "AUTO_924"}, "automation_actions": {"available_plans": [], "limits": {"1": 100, "10": 25000, "11": 250000, "12": 250000, "13": 100, "14": 1000, "15": 5000, "16": 25000, "17": 250000, "2": 1000, "3": 10000, "4": 250000, "5": 25000, "6": 1000, "7": 100, "8": 1000, "9": 10000, "*********": 2}}, "automation_actions_paywall": {"available_plans": [2, 6, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "err_code": "AUTO_152"}, "automation_addons": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17], "err_code": "AUTO_926"}, "automation_conditions_paywall": {"available_plans": [2, 6, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "err_code": "AUTO_152"}, "automation_trigger_limit": {"available_plans": [1, 2, 3, 4, 5, 6, 9, 10, 11, 12, 15, 16, 17], "err_code": "AUTO_923", "limits": {"13": 5, "14": 500, "7": 5, "8": 500}, "title": "Automations"}, "automation_webhooks_action": {"available_plans": [1, 2, 6, 3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "err_code": "AUTO_925"}, "azure_sso": {"available_plans": [4, 11, 12, 17]}, "basic_card_views": {"available_plans": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "limits": {"*********": 2}}, "box_view": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "limits": {}}, "calculation_columns": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 15, 16, 17], "err_code": "PAGE_043"}, "cards_ai": {"automated_ai_limits": {}, "available_plans": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}, "change_task_priority": {"available_plans": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}, "chat_history_limit": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "limits": {"1": 30, "13": 30, "14": 90, "2": 90, "6": 90, "7": 30, "8": 90}}, "chat_usage_message_limit": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "limits": {"1": 1000, "13": 1000, "7": 1000}}, "chat_usage_post_limit": {"available_plans": [4, 5, 10, 11, 12, 16, 17], "limits": {"1": 30, "13": 30, "14": 30, "15": 100, "2": 30, "3": 100, "6": 30, "7": 30, "8": 30, "9": 100}}, "chat_usage_syncup_limit": {"available_plans": [4, 5, 10, 11, 12, 16, 17], "limits": {"1": 18000, "13": 18000, "14": 108000, "15": 360000, "2": 108000, "3": 360000, "6": 108000, "7": 18000, "8": 108000, "9": 360000}}, "checklist_item_length_limit": {"available_plans": [], "err_code": "CHECK_054", "limits": {"1": 2000, "10": 2000, "11": 2000, "12": 2000, "13": 2000, "14": 2000, "15": 2000, "16": 2000, "17": 2000, "2": 2000, "3": 2000, "4": 2000, "5": 2000, "6": 2000, "7": 2000, "8": 2000, "9": 2000, "*********": 500}}, "checklist_item_limit": {"available_plans": [], "err_code": "CHECK_052", "limits": {"1": 500, "10": 500, "11": 500, "12": 500, "13": 500, "14": 500, "15": 500, "16": 500, "17": 500, "2": 500, "3": 500, "4": 500, "5": 500, "6": 500, "7": 500, "8": 500, "9": 500, "*********": 3}}, "cloud_attachments": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "custom_branding": {"available_plans": [4, 5, 10, 11, 12, 16, 17], "err_code": "WL_001"}, "custom_fields": {"automated_ai_limits": {"1": 20, "10": 100, "11": 100, "12": 100, "13": 20, "14": 100, "15": 100, "16": 100, "17": 100, "2": 100, "3": 100, "4": 100, "5": 100, "6": 100, "7": 20, "8": 100, "9": 100, "*********": 2}, "available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "err_code": "FIELD_033", "limits": {"1": 100, "13": 60, "7": 60, "*********": 2}, "manual_ai_limits": {"*********": 2}}, "custom_items": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "err_code": "CUSTOM_TYPE_002", "limits": {"1": 20, "13": 20, "7": 20, "*********": 2}}, "custom_permission_levels": {"available_plans": [4, 11, 12, 17]}, "custom_roles": {"available_and_limited_plans": [4, 5, 10, 11, 12, 16, 17], "available_plans": [4, 11, 12, 17], "err_code": "CUSROLE_019", "limits": {"1": 0, "10": 1, "13": 0, "14": 0, "15": 0, "16": 1, "2": 0, "3": 0, "5": 1, "6": 0, "7": 0, "8": 0, "9": 0, "*********": 2}}, "custom_task_ids": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "dashboards": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 15, 16, 17], "limits": {"1": 100, "13": 60, "14": 100, "7": 100, "*********": 2}}, "default_views": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17], "err_code": "VIEWS_129", "limits": {"15": 5}}, "delegate_reminders": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "doc_wikis": {"available_plans": [4, 5, 10, 11, 12, 16, 17], "limits": {"1": 1, "13": 1, "14": 1, "15": 1, "2": 1, "3": 1, "6": 1, "7": 1, "8": 1, "9": 1}, "title": "Document Wikis"}, "document_tags": {"available_plans": [4, 5, 10, 11, 12, 16, 17], "limits": {"1": 100, "13": 100, "14": 100, "15": 100, "2": 100, "3": 100, "6": 100, "7": 100, "8": 100, "9": 100, "*********": 2}, "title": "Document Tags"}, "durations": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "edit_resource_permissions": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "email_add_account": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "email_free_accounts": {"available_plans": [], "limits": {"1": 1, "10": 2, "11": 2, "12": 2, "13": 1, "14": 1, "15": 2, "16": 2, "17": 2, "2": 1, "3": 2, "4": 2, "5": 2, "6": 1, "7": 1, "8": 1, "9": 2, "*********": 2}}, "email_signatures": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "email_templates": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "emails": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "limits": {"1": 100, "13": 100, "7": 100, "*********": 2}}, "embed_view": {"available_plans": [1, 2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 15, 16, 17], "err_code": "VIEWS_115", "limits": {"13": 40, "14": 100, "7": 60, "*********": 2}}, "expire_public_link": {"available_plans": [4, 11, 12, 17]}, "export_tasks": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "field_level_permissions": {"available_plans": [4, 5, 10, 11, 12, 16, 17]}, "field_merge": {"available_plans": [4, 5, 10, 11, 12, 16, 17]}, "field_move": {"available_plans": [4, 5, 10, 11, 12, 16, 17]}, "field_pin": {"available_plans": [1, 2, 3, 4, 5, 6, 9, 10, 11, 12, 15, 16, 17]}, "file_storage": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "folder_limits": {"available_plans": [], "limits": {"1": 100, "10": 1000, "11": 1000, "12": 1000, "13": 100, "14": 200, "15": 400, "16": 1000, "17": 1000, "2": 200, "3": 400, "4": 1000, "5": 1000, "6": 200, "7": 100, "8": 200, "9": 400, "*********": 2}, "overrides": {"grandfathered": {"available_plans": [4, 5, 10, 11, 12, 16, 17], "limits": {"1": 100, "13": 100, "14": 400, "15": 400, "2": 400, "3": 400, "6": 400, "7": 100, "8": 400, "9": 400, "*********": 2}}}}, "form_view": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "form_view_utilization": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17], "limits": {"1": 1, "13": 1, "7": 1, "*********": 1}}, "future_recurring_tasks": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "gantt_critical_path": {"available_plans": [1, 2, 6, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "limits": {}}, "gantt_view": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "err_code": "VIEWS_093", "limits": {"1": 100, "13": 60, "7": 60, "*********": 2}}, "gantt_view_exports": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "goals": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "err_code": "GOAL_077", "limits": {"1": 100, "13": 100, "7": 100, "*********": 2}}, "goals_folders": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "google_calendar_advanced": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "google_sso": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "granular_default_permission_level": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "granular_field_permissions": {"available_plans": [4, 5, 10, 11, 12, 16, 17]}, "granular_space_permissions": {"available_plans": [4, 11, 12, 17]}, "granular_time_estimates": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "granular_views_permissions": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "guest_card_visibility_restrictions": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "guest_field_visibility_restrictions": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "guest_public_api": {"available_plans": [4, 11, 12, 17]}, "guest_seats_restrictions": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "hidden_cf_guests": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "hubspot": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "invite_guest_article_restrictions": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "invite_guest_restrictions": {"available_plans": [4, 11, 12, 17]}, "invite_permissions": {"available_plans": [4, 5, 10, 11, 12, 16, 17]}, "list_assignee": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "list_exports": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "err_code": "EXPORT_007", "limits": {"1": 5, "13": 0, "14": 3, "2": 5, "6": 5, "7": 5, "8": 5, "*********": 2}}, "list_limits": {"available_plans": [], "limits": {"1": 100, "10": 1000, "11": 1500, "12": 1500, "13": 40, "14": 200, "15": 400, "16": 1000, "17": 1500, "2": 200, "3": 400, "4": 1500, "5": 1000, "6": 200, "7": 100, "8": 200, "9": 400, "*********": 2}, "overrides": {"grandfathered": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "limits": {"1": 100, "13": 40, "7": 100, "*********": 2}}}}, "list_priority": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "list_task_limit": {"available_plans": [], "err_code": "CHECK_056", "limits": {"1": 50000, "10": 50000, "11": 50000, "12": 50000, "13": 50000, "14": 50000, "15": 50000, "16": 50000, "17": 50000, "2": 50000, "3": 50000, "4": 50000, "5": 50000, "6": 50000, "7": 50000, "8": 50000, "9": 50000, "*********": 10}}, "live_view": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "err_code": "MEMBER_028", "limits": {"1": 100, "13": 100, "7": 100, "*********": 2}}, "location_overview": {"available_plans": [1, 2, 6, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "limits": {"*********": 2}}, "logic_in_forms": {"available_plans": [4, 5, 10, 11, 12, 16, 17]}, "make_new_spaces_private": {"available_plans": [4, 11, 12, 17]}, "manage_workspace_public_sharing": {"available_plans": [4, 11, 12, 17]}, "map_view": {"available_plans": [2, 6, 3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "err_code": "VIEWS_212", "limits": {"1": 100, "13": 0, "14": 5, "7": 0, "8": 100, "*********": 2}}, "milestones": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "limits": {"1": 10, "13": 10, "7": 10, "*********": 2}}, "mindmap_view": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "limits": {"1": 100, "13": 0, "14": 40, "2": 100, "6": 100, "7": 60, "8": 100, "*********": 2}}, "okta_sso": {"available_plans": [4, 11, 12, 17]}, "onedrive": {"available_plans": [5, 10, 4, 11, 12, 16, 17]}, "owner_control_private_spaces": {"available_plans": [4, 11, 12, 17]}, "paid_plan": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "plug_and_play": {"available_plans": [4, 5, 10, 11, 12, 16, 17]}, "points": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "err_code": "POINTS_099", "limits": {"1": 100, "13": 100, "14": 100, "2": 100, "6": 100, "7": 100, "8": 100, "*********": 2}}, "points_per_assignee": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "portfolio_permissions": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "portfolios": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "err_code": "FOLIO_031", "limits": {"1": 100, "13": 100, "7": 100, "*********": 2}}, "private_docs": {"available_plans": [2, 3, 4, 5, 6, 9, 10, 11, 12, 15, 16, 17]}, "private_views": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "private_whiteboards": {"available_plans": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 15, 16, 17]}, "productivity_power_pack": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "productivity_starter_pack": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "project_limits": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "limits": {"1": 5, "13": 5, "7": 5, "*********": 2}}, "protect_docs": {"available_plans": [4, 5, 10, 11, 12, 16, 17]}, "protect_views": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17], "err_code": "VIEWS_048", "limits": {"15": 10}}, "public_link_restrictions": {"available_plans": [4, 11, 12, 17]}, "public_template_restrictions": {"available_plans": [4, 11, 12, 17]}, "public_views": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "publicly_shared_items": {"available_plans": [4, 11, 12, 17]}, "reporting": {"available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "required_cf": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "rollup": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "sales_assisted_team": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "salesforce": {"available_plans": [5, 10, 4, 11, 12, 16, 17]}, "saml_sso": {"available_plans": [4, 11, 12, 17]}, "scheduled_reports": {"available_plans": [4, 5, 10, 11, 12, 16, 17], "err_code": "SCHED_001", "limits": {"1": 40, "13": 40, "14": 40, "15": 40, "2": 40, "3": 40, "6": 40, "7": 40, "8": 40, "9": 40}, "title": "Scheduled Reports"}, "scim": {"available_plans": [4, 5, 10, 11, 12, 16, 17]}, "send_trial_email": {"available_plans": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}, "session_settings": {"available_plans": [4, 11, 12, 17], "title": "Restrictive Session Settings"}, "sharepoint": {"available_plans": [5, 10, 4, 11, 12, 16, 17]}, "show_recurring_tasks": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "siml": {"available_plans": [4, 5, 10, 11, 12], "err_code": "SIML_001", "limits": {"1": 100, "2": 100, "3": 1000, "6": 100, "7": 0, "8": 100, "9": 1000, "*********": 2}, "title": "Subtask In Multiple Lists"}, "skip_non_working_days": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "sms_two_factor_authentication": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "space_granular_default_permission_level": {"available_plans": [4, 11, 12, 17]}, "sprint_automation": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "sprint_rollover": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "sso_required": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "storage_override": {"available_plans": [2, 6, 3, 4, 5]}, "subtask_archived_limit": {"available_plans": [], "err_code": "ITEM_336", "limits": {"1": 10000, "10": 10000, "11": 10000, "12": 10000, "13": 10000, "14": 10000, "15": 10000, "16": 10000, "17": 10000, "2": 10000, "3": 10000, "4": 10000, "5": 10000, "6": 10000, "7": 10000, "8": 10000, "9": 10000, "*********": 3}}, "subtask_limit": {"available_plans": [], "err_code": "ITEM_327", "limits": {"1": 1000, "10": 1000, "11": 1000, "12": 1000, "13": 1000, "14": 1000, "15": 1000, "16": 1000, "17": 1000, "2": 1000, "3": 1000, "4": 1000, "5": 1000, "6": 1000, "7": 1000, "8": 1000, "9": 1000, "*********": 3}}, "sync_up": {"available_plans": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}, "table_exports": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "err_code": "EXPORT_007", "limits": {"1": 5, "13": 5, "14": 5, "2": 5, "6": 5, "7": 5, "8": 5, "*********": 2}}, "tableau": {"available_plans": [4, 11, 12, 17]}, "task_ai": {"automated_ai_limits": {"1": 20, "10": 100, "11": 100, "12": 100, "13": 20, "14": 100, "15": 100, "16": 100, "17": 100, "2": 100, "3": 100, "4": 100, "5": 100, "6": 100, "7": 20, "8": 100, "9": 100, "*********": 2}, "available_plans": [2, 6, 3, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "err_code": "TASK_342", "manual_ai_limits": {"*********": 2}}, "task_attachment_limit": {"available_plans": [], "err_code": "CHECK_054", "limits": {"1": 1000, "10": 1000, "11": 1000, "12": 1000, "13": 1000, "14": 1000, "15": 1000, "16": 1000, "17": 1000, "2": 1000, "3": 1000, "4": 1000, "5": 1000, "6": 1000, "7": 1000, "8": 1000, "9": 1000, "*********": 3}}, "task_checklist_limit": {"available_plans": [], "err_code": "CHECK_051", "limits": {"1": 250, "10": 250, "11": 250, "12": 250, "13": 250, "14": 250, "15": 250, "16": 250, "17": 250, "2": 250, "3": 250, "4": 250, "5": 250, "6": 250, "7": 250, "8": 250, "9": 250, "*********": 3}}, "task_linked_tasks_limit": {"available_plans": [], "err_code": "CHECK_055", "limits": {"1": 6500, "10": 6500, "11": 6500, "12": 6500, "13": 6500, "14": 6500, "15": 6500, "16": 6500, "17": 6500, "2": 6500, "3": 6500, "4": 6500, "5": 6500, "6": 6500, "7": 6500, "8": 6500, "9": 6500, "*********": 3}}, "task_tag_limit": {"available_plans": [], "err_code": "CHECK_053", "limits": {"1": 600, "10": 600, "11": 600, "12": 600, "13": 600, "14": 600, "15": 600, "16": 600, "17": 600, "2": 600, "3": 600, "4": 600, "5": 600, "6": 600, "7": 600, "8": 600, "9": 600, "*********": 3}}, "team_analytics": {"available_plans": [17], "limits": {"13": 1, "14": 1, "15": 7, "16": 30}}, "team_public_items": {"available_plans": [4, 5, 10, 11, 12, 16, 17]}, "team_sharing": {"available_plans": [4, 5, 10, 11, 12, 16, 17]}, "team_sharing_space": {"available_plans": [4, 11, 12, 17]}, "team_view": {"available_plans": [2, 6, 3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "err_code": "VIEWS_041", "limits": {"1": 0, "13": 20, "14": 100, "7": 0, "8": 100, "*********": 2}}, "teams": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17], "err_code": "GROUP_027", "limits": {"1": 2, "13": 2, "14": 3, "15": 10, "2": 3, "6": 3, "7": 2, "8": 3, "*********": 2}, "title": "Additional Teams"}, "time_estimates_per_assignee": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "time_in_status": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "err_code": "TIS_027", "title": "Time In Status"}, "time_tracking_reporting": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "timeline_view": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "err_code": "VIEWS_202", "limits": {"1": 100, "13": 60, "14": 100, "2": 100, "6": 100, "7": 60, "8": 100, "*********": 2}}, "timesheet_approvals_usage": {"available_plans": [5, 4, 10, 11, 12, 16, 17], "limits": {"1": 100, "13": 100, "14": 100, "15": 100, "2": 100, "3": 100, "6": 100, "7": 100, "8": 100, "9": 100, "*********": 2}}, "timl": {"available_plans": [1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 12], "limits": {"7": 0}}, "timl_and_siml": {"available_plans": [15, 16, 17], "limits": {"13": 10, "14": 30}}, "twofa_required": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "universal_search_private": {"available_plans": [5, 10, 4, 11, 12, 16, 17]}, "universal_search_shared": {"available_plans": [3, 9, 5, 10, 4, 11, 12, 15, 16, 17]}, "unlimited_individual_guest_permissions": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "unlimited_permission_items": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "user_advanced_time_tracking": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17], "err_code": "TIMEENTRY_064", "limits": {"1": 100, "13": 0, "14": 0, "15": 100, "2": 100, "6": 100, "7": 100, "8": 100, "*********": 2}}, "user_bonus_claimed_restrictions": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "user_time_tracking": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17], "limits": {"1": 100, "13": 100, "7": 100, "*********": 2}}, "user_timesheet": {"available_plans": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}, "views": {"available_plans": [1, 2, 6, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "limits": {}}, "white_labeling": {"available_plans": [4, 11, 12, 17]}, "whiteboards_view": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "limits": {"1": 3, "13": 3, "14": 10, "2": 10, "6": 10, "7": 3, "8": 10}}, "widgets": {"action_report_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "billable_report_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "cumulative_flow_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "custom_bar_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "custom_battery_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "custom_calculation_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "custom_line_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "custom_pie_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "cycle_time_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "lead_time_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "legacy_burndown_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "legacy_burnup_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "legacy_velocity_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "portfolio_widget": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "portfolio_widget_filter_by_category": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "priority_over_time_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "search_box_files_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "search_confluence_files_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "search_dropbox_files_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "search_figma_files_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "search_g_drive_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "search_github_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "search_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "sprint_burndown_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "sprint_burnup_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "sprint_reporting_dashboard": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "sprint_velocity_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "status_over_time_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "tags_usage_over_time_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "time_estimated_widget": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "time_in_status_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "time_reporting_widget": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "time_tracked_widget": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "timesheet_widget": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 16, 17]}, "whos_behind_widget": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "worked_on_widget": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "workspace_points_widget": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}}, "widgets_priority_queue": {"available_plans": [4, 11, 12, 17]}, "wip_limits": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "workload_groupings": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17]}, "workload_view": {"available_plans": [3, 4, 5, 9, 10, 11, 12, 15, 16, 17], "err_code": "VIEWS_252", "limits": {"1": 100, "13": 60, "14": 100, "2": 100, "6": 100, "7": 60, "8": 100, "*********": 2}}, "workload_view_capacity": {"available_plans": [4, 5, 10, 11, 12, 16, 17]}, "workspace_churn_survey": {"available_plans": [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 17]}, "workspace_scoped_chat_comment_retention_setting": {"available_plans": [4, 11, 12, 17]}, "workspace_users_export": {"available_plans": [4, 11, 12, 17]}}, "pdf_browser_export": {"goto_timeout": 60000, "pdf_timeout": 60000, "retry_limit": 3, "threads": 4}, "pendo": {"api_key": ""}, "permission_constants": {"add_attachments": "add_attachments", "add_checklists": "add_checklists", "add_dependencies": "add_dependencies", "add_email_account": "add_email_account", "add_followers": "add_followers", "add_self_follower": "add_self_follower", "add_status": "add_status", "add_subtasks": "add_subtasks", "add_tags": "add_tags", "archive": "archive", "billing": "billing", "can_add_automation": "can_add_automation", "can_add_team_guests": "can_add_team_guests", "can_add_team_members": "can_add_team_members", "can_add_workspace_attachments": "can_add_workspace_attachments", "can_be_added_to_spaces": "can_be_added_to_spaces", "can_be_added_to_user_groups": "can_be_added_to_user_groups", "can_change_subtask_columns": "can_change_subtask_columns", "can_change_task_links": "can_change_task_links", "can_convert_item": "can_convert_item", "can_create_folders": "can_create_folders", "can_create_folders_pl": "can_create_folders_pl", "can_create_goals": "can_create_goals", "can_create_lists": "can_create_lists", "can_create_lists_pl": "can_create_lists_pl", "can_create_milestone": "can_create_milestone", "can_create_personal_list": "can_create_personal_list", "can_create_portfolios": "can_create_portfolios", "can_create_projects": "can_create_projects", "can_create_relationships": "can_create_relationships", "can_create_spaces": "can_create_spaces", "can_create_tasks": "can_create_tasks", "can_create_workload": "can_create_workload", "can_create_workspace_doc": "can_create_workspace_doc", "can_delete_checklist_item": "can_delete_checklist_item", "can_delete_comments": "can_delete_comments", "can_delete_no_access": "can_delete_no_access", "can_edit_description": "can_edit_description", "can_edit_integrations": "can_edit_integrations", "can_edit_list_statuses": "can_edit_list_statuses", "can_edit_privacy": "can_edit_privacy", "can_edit_project_settings": "can_edit_project_settings", "can_edit_space_settings": "can_edit_space_settings", "can_edit_team": "can_edit_team", "can_edit_team_members": "can_edit_team_members", "can_edit_team_owner": "can_edit_team_owner", "can_edit_trial": "can_edit_trial", "can_edit_user_groups": "can_edit_user_groups", "can_edit_view_protection": "can_edit_view_protection", "can_enable_sso": "can_enable_sso", "can_export_tasks": "can_export_tasks", "can_gdpr_export": "can_gdpr_export", "can_import": "can_import", "can_list_inaccessible_spaces": "can_list_inaccessible_spaces", "can_make_tasks_public": "can_make_tasks_public", "can_pin_fields": "can_pin_fields", "can_read": "can_read", "can_resolve_checklist_item_if_assigned": "can_resolve_checklist_item_if_assigned", "can_see_data_retention_settings": "can_see_data_retention_settings", "can_see_team_members": "can_see_team_members", "can_see_workload": "can_see_workload", "can_send_login_bypass_link": "can_send_login_bypass_link", "can_set_approval_settings": "can_set_approval_settings", "can_set_data_retention_settings": "can_set_data_retention_settings", "can_set_default_permission_level": "can_set_default_permission_level", "can_use_chat": "can_use_chat", "can_use_git": "can_use_git", "can_use_public_api_dev_key": "can_use_public_api_dev_key", "can_view_audit_logs": "can_view_audit_logs", "can_view_reporting": "can_view_reporting", "can_view_team_timesheet": "can_view_team_timesheet", "change_assignee": "change_assignee", "change_clickapps": "change_clickapps", "change_description": "change_description", "change_due_date": "change_due_date", "change_incoming_address": "change_incoming_address", "change_points_estimate": "change_points_estimate", "change_priority": "change_priority", "change_recurring": "change_recurring", "change_status": "change_status", "change_time_estimate": "change_time_estimate", "change_title": "change_title", "chat_add_followers": "chat_add_followers", "chat_add_members": "chat_add_members", "chat_add_self_follower": "chat_add_self_follower", "chat_comment": "chat_comment", "chat_create_channel": "chat_create_channel", "chat_create_dm": "chat_create_dm", "chat_delete_channel": "chat_delete_channel", "chat_manage_tiles": "chat_manage_tiles", "chat_remove_followers": "chat_remove_followers", "chat_remove_members": "chat_remove_members", "chat_remove_self_follower": "chat_remove_self_follower", "chat_reply": "chat_reply", "comment": "comment", "convert_custom_fields": "convert_custom_fields", "create_automation": "create_automation", "create_custom_emojis": "create_custom_emojis", "create_custom_fields": "create_custom_fields", "create_dashboards": "create_dashboards", "create_private_view": "create_private_view", "create_public_view": "create_public_view", "create_view": "create_view", "custom_roles": "custom_roles", "delete": "delete", "delete_custom_fields": "delete_custom_fields", "delete_dashboards": "delete_dashboards", "delete_view": "delete_view", "duplicate": "duplicate", "edit_attachments": "edit_attachments", "edit_checklists": "edit_checklists", "edit_custom_fields": "edit_custom_fields", "edit_dashboard": "edit_dashboard", "edit_goal": "edit_goal", "edit_list_details": "edit_list_details", "edit_view": "edit_view", "edit_widget": "edit_widget", "like_comments": "like_comments", "make_views_public": "make_views_public", "manage_custom_emojis": "manage_custom_emojis", "manage_custom_fields": "manage_custom_fields", "manage_custom_items": "manage_custom_items", "manage_statuses": "manage_statuses", "manage_tags": "manage_tags", "merge": "merge", "merge_custom_fields": "merge_custom_fields", "move_custom_fields": "move_custom_fields", "move_goal": "move_goal", "move_task": "move_task", "oauth_apps": "oauth_apps", "profile": "profile", "public_spaces_visible": "public_spaces_visible", "remove_attachments": "remove_attachments", "remove_dependencies": "remove_dependencies", "remove_followers": "remove_followers", "remove_self_follower": "remove_self_follower", "remove_status": "remove_status", "remove_tags": "remove_tags", "send_email": "send_email", "set_custom_field_values": "set_custom_field_values", "share": "share", "team_permissions": "team_permissions", "template": "template", "track_time": "track_time", "use_syncups": "use_syncups"}, "permission_levels": {"1": {"add_attachments": false, "add_checklists": false, "add_dependencies": false, "add_followers": false, "add_self_follower": false, "add_status": false, "add_subtasks": false, "add_tags": false, "archive": false, "can_add_automation": false, "can_change_subtask_columns": false, "can_change_task_links": false, "can_convert_item": false, "can_create_folders_pl": false, "can_create_lists_pl": false, "can_create_relationships": false, "can_create_tasks": false, "can_create_workspace_doc": false, "can_delete_checklist_item": false, "can_make_tasks_public": false, "can_pin_fields": false, "can_read": true, "can_resolve_checklist_item_if_assigned": false, "can_set_default_permission_level": false, "change_assignee": 1, "change_clickapps": false, "change_description": false, "change_due_date": false, "change_incoming_address": false, "change_points_estimate": false, "change_priority": false, "change_recurring": false, "change_status": false, "change_time_estimate": false, "change_title": false, "chat_add_followers": true, "chat_add_self_follower": true, "chat_comment": true, "chat_delete_channel": false, "chat_manage_tiles": false, "chat_remove_followers": false, "chat_remove_members": false, "chat_remove_self_follower": true, "chat_reply": true, "comment": false, "create_private_view": true, "create_public_view": false, "create_view": false, "delete": false, "delete_view": false, "display_name": "view", "duplicate": false, "edit_attachments": false, "edit_checklists": false, "edit_goal": false, "edit_list_details": false, "edit_view": false, "like_comments": false, "manage_custom_fields": false, "merge": false, "move_goal": false, "move_task": false, "name": "can read", "permission_level": 1, "remove_attachments": false, "remove_dependencies": false, "remove_followers": false, "remove_self_follower": false, "remove_status": false, "remove_tags": false, "set_custom_field_values": false, "template": false, "track_time": false}, "3": {"add_attachments": true, "add_checklists": false, "add_dependencies": false, "add_followers": true, "add_self_follower": true, "add_status": false, "add_subtasks": false, "add_tags": false, "archive": false, "can_add_automation": false, "can_change_subtask_columns": false, "can_change_task_links": false, "can_create_folders_pl": false, "can_create_lists_pl": false, "can_create_relationships": false, "can_create_tasks": false, "can_create_workspace_doc": false, "can_delete_checklist_item": false, "can_make_tasks_public": false, "can_pin_fields": false, "can_read": true, "can_resolve_checklist_item_if_assigned": true, "can_set_default_permission_level": false, "change_assignee": 1, "change_clickapps": false, "change_description": false, "change_due_date": false, "change_incoming_address": false, "change_points_estimate": false, "change_priority": false, "change_recurring": false, "change_status": false, "change_time_estimate": false, "change_title": false, "chat_add_followers": true, "chat_add_self_follower": true, "chat_comment": true, "chat_delete_channel": false, "chat_manage_tiles": true, "chat_remove_followers": false, "chat_remove_members": false, "chat_remove_self_follower": true, "chat_reply": true, "comment": true, "create_private_view": true, "create_public_view": true, "create_view": false, "delete": false, "delete_view": false, "display_name": "comment", "duplicate": false, "edit_attachments": false, "edit_checklists": false, "edit_goal": false, "edit_list_details": false, "edit_view": false, "like_comments": true, "manage_custom_fields": false, "merge": false, "move_goal": false, "move_task": false, "name": "can comment", "permission_level": 3, "remove_attachments": true, "remove_dependencies": false, "remove_followers": true, "remove_self_follower": true, "remove_status": false, "remove_tags": false, "set_custom_field_values": false, "template": false, "track_time": false}, "4": {"add_attachments": true, "add_checklists": true, "add_dependencies": true, "add_followers": true, "add_self_follower": true, "add_status": true, "add_subtasks": false, "add_tags": true, "archive": true, "can_add_automation": true, "can_change_subtask_columns": true, "can_change_task_links": true, "can_create_folders_pl": false, "can_create_lists_pl": true, "can_create_relationships": true, "can_create_tasks": false, "can_create_workspace_doc": true, "can_delete_checklist_item": true, "can_make_tasks_public": true, "can_pin_fields": true, "can_read": true, "can_resolve_checklist_item_if_assigned": true, "can_set_default_permission_level": false, "change_assignee": 2, "change_clickapps": true, "change_description": true, "change_due_date": true, "change_incoming_address": false, "change_points_estimate": true, "change_priority": true, "change_recurring": true, "change_status": true, "change_time_estimate": true, "change_title": true, "chat_add_followers": true, "chat_add_self_follower": true, "chat_comment": true, "chat_delete_channel": false, "chat_manage_tiles": true, "chat_remove_followers": true, "chat_remove_members": true, "chat_remove_self_follower": true, "chat_reply": true, "comment": true, "create_private_view": true, "create_public_view": true, "create_view": true, "delete": false, "delete_view": true, "display_name": "edit", "duplicate": true, "edit_attachments": true, "edit_checklists": true, "edit_goal": true, "edit_list_details": true, "edit_view": true, "like_comments": true, "manage_custom_fields": true, "merge": true, "move_goal": true, "move_task": true, "name": "can edit", "permission_level": 4, "remove_attachments": true, "remove_dependencies": true, "remove_followers": true, "remove_self_follower": true, "remove_status": true, "remove_tags": true, "set_custom_field_values": true, "template": true, "track_time": true}, "5": {"add_attachments": true, "add_checklists": true, "add_dependencies": true, "add_followers": true, "add_self_follower": true, "add_status": true, "add_subtasks": true, "add_tags": true, "archive": true, "can_add_automation": true, "can_change_subtask_columns": true, "can_change_task_links": true, "can_create_folders_pl": true, "can_create_lists_pl": true, "can_create_relationships": true, "can_create_tasks": true, "can_create_workspace_doc": true, "can_delete_checklist_item": true, "can_make_tasks_public": true, "can_pin_fields": true, "can_read": true, "can_resolve_checklist_item_if_assigned": true, "can_set_default_permission_level": true, "change_assignee": 2, "change_clickapps": true, "change_description": true, "change_due_date": true, "change_incoming_address": true, "change_points_estimate": true, "change_priority": true, "change_recurring": true, "change_status": true, "change_time_estimate": true, "change_title": true, "chat_add_followers": true, "chat_add_self_follower": true, "chat_comment": true, "chat_delete_channel": true, "chat_manage_tiles": true, "chat_remove_followers": true, "chat_remove_members": true, "chat_remove_self_follower": true, "chat_reply": true, "comment": true, "create_private_view": true, "create_public_view": true, "create_view": true, "delete": true, "delete_view": true, "display_name": "edit", "duplicate": true, "edit_attachments": true, "edit_checklists": true, "edit_goal": true, "edit_list_details": true, "edit_view": true, "like_comments": true, "manage_custom_fields": true, "merge": true, "move_goal": true, "move_task": true, "name": "can create and edit", "permission_level": 5, "remove_attachments": true, "remove_dependencies": true, "remove_followers": true, "remove_self_follower": true, "remove_status": true, "remove_tags": true, "set_custom_field_values": true, "template": true, "track_time": true}}, "permission_requirements": {"create_private_view": {"entitlements": ["private_views"]}}, "platform": {"mobile": "Mobile", "web": "Web"}, "portfolios": {"default_columns": ["list_status", "progress", "completed", "overdue", "start_date", "end_date", "priority", "assignee"]}, "position_conversion_constant": 1000000, "preset_status_list": [{"name": "Normal", "statuses": [{"color": "#87909e", "status": "Open"}, {"color": "#5f55ee", "status": "In progress"}, {"color": "#008844", "status": "Closed"}]}, {"name": "Scrum", "statuses": [{"color": "#87909e", "status": "Open"}, {"color": "#f8ae00", "status": "Pending"}, {"color": "#ee5e99", "status": "In progress"}, {"color": "#000000", "status": "Completed"}, {"color": "#e16b16", "status": "In review"}, {"color": "#d33d44", "status": "Accepted"}, {"color": "#b660e0", "status": "Rejected"}, {"color": "#5f55ee", "status": "Blocked"}, {"color": "#008844", "status": "Closed"}]}, {"name": "Marketing", "statuses": [{"color": "#87909e", "status": "Open"}, {"color": "#f8ae00", "status": "Concept"}, {"color": "#ee5e99", "status": "In progress"}, {"color": "#b660e0", "status": "Running"}, {"color": "#5f55ee", "status": "Review"}, {"color": "#008844", "status": "Closed"}]}, {"name": "Content", "statuses": [{"color": "#87909e", "status": "Open"}, {"color": "#f8ae00", "status": "Ready"}, {"color": "#ee5e99", "status": "Writing"}, {"color": "#d33d44", "status": "Approval"}, {"color": "#b660e0", "status": "Rejected"}, {"color": "#5f55ee", "status": "Publish"}, {"color": "#008844", "status": "Closed"}]}, {"name": "Ka<PERSON><PERSON>", "statuses": [{"color": "#87909e", "status": "Open"}, {"color": "#1090e0", "status": "In Progress"}, {"color": "#5f55ee", "status": "Review"}, {"color": "#008844", "status": "Closed"}]}], "pretty_ids": {"region_number": 0, "region_total": 1}, "priorities": {"list": [{"color": "#f50000", "id": "1", "orderindex": "1", "priority": "urgent"}, {"color": "#f8ae00", "id": "2", "orderindex": "2", "priority": "high"}, {"color": "#6fddff", "id": "3", "orderindex": "3", "priority": "normal"}, {"color": "#d8d8d8", "id": "4", "orderindex": "4", "priority": "low"}], "map": {"1": {"color": "#f50000", "id": "1", "orderindex": "1", "priority": "urgent"}, "2": {"color": "#f8ae00", "id": "2", "orderindex": "2", "priority": "high"}, "3": {"color": "#6fddff", "id": "3", "orderindex": "3", "priority": "normal"}, "4": {"color": "#d8d8d8", "id": "4", "orderindex": "4", "priority": "low"}}}, "private_api": {"jwt": {"public_key": "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEApY4PEJarm6kDY+guTLbo70hXnTg0fhPqpp2QDKZLzCg8oqigPvS+DTW8Dy25eCITdgSRIZWsNgoBbWXCr++VwT7WST26GSHGGqvVjIkE0SATCiTSSkLnKXI+KJClZ7qtRFB+gfRytG1RR4yDH9lEChVtHBBrvt/b8pwYA2tu56v1E6OHcYkTTiqNjTqzEpFIBhxDJ03Ty3hW4i77z5b5xhE6UI6i/2uKWh60Y8DIBVBjhQCJHR//YAHFq9y4n6y06Z0oPk076BNw3nIq103ykfhayfsc4qoWVKVTiK4USFnTmxnolIqTwX6CmMY3+01N1WIRN0Yfvge2qPWoJn1kU4CByHGfZ0/kUPL98m5yZBqSrbLScR41CpPNMZlsL0SQ8upQTMT1AoDsB1r/IFCp4xbUxEvKrYj9BABmgnJlIVN9CdCdoORPBqgaARtiz6xPAIOvf+rxQWTBF0GQjMzdYSVYfBDavIYBnJuEJU7FGwcEMcN3CZZv6m3HbW8qflde4IP6ue85fGl2HNfGxC6Wi/R/mgDseLrr5PRIbnwKD2ZXfJJvwSWhjEIE/nMcduGjN0VBZlfIVWj40QD3Tn+cWTNXg5leZYwTczbVc6jtZkaXqpp4Ug63D3yzkok6kp7nBgDlt+AH+h5Eo3hIeCaXqL9H1vnUaWNy0TPLEU0VAh8CAwEAAQ==", "secret_key": "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"}}, "processor": {"wait_retry_limit": 3}, "profitwell": {"combined_key": "", "key": "", "plan_interval_key": ""}, "proxy": {"deepgram": {"authHeader": "Authorization", "keys": {"api_key": "dummy-deepgram-api-key-for-e2e-test"}, "path": "proxy/deepgram", "target": "To be replaced in the test"}, "elevenlabs": {"authHeader": "xi-api-key", "keys": {"api_key": "dummy-elevenlabs-api-key-for-e2e-test"}, "path": "proxy/elevenlabs", "target": "To be replaced in the test"}, "gemini": {"authHeader": "x-goog-api-key", "keys": {"api_key": "dummy-gemini-api-key-for-e2e-test"}, "path": "proxy/gemini", "target": "To be replaced in the test"}, "openai": {"authHeader": "Authorization", "keys": {"api_key": "dummy-openai-api-key-for-e2e-test"}, "path": "proxy/openai", "target": "To be replaced in the test"}, "vertexAi": {"authHeader": "Authorization", "keys": {"api_key": "dummy-vertex-ai-api-key-for-e2e-test"}, "location": "us-central1", "path": "proxy/vertex-ai", "projectId": "clickup-ai", "target": "To be replaced in the test"}}, "public_images": {"gallery_url": "https://attachments.clickup.com", "unsplash_access_key": "", "unsplash_api_url": "https://api.unsplash.com", "unsplash_app_name": "clickup-doc-cover-images", "unsplash_url": "https://unsplash.com"}, "public_sort_value": {"task": 1, "view": 2}, "public_template_teams": ["14363", "303826"], "public_template_user": "358", "push_notifications": {"android_key": "", "enabled": true, "gcm_api_key": "", "ios_dev_url": "api.development.push.apple.com", "ios_url": "api.push.apple.com", "vapid_keys": {"private_key": "", "public_key": ""}}, "quick_start_template": {"assignee_task": "gaxvxn", "due_date_task": "gaxvx8", "id": "21079127", "subcat_id": 50480771}, "raised_rate_limit_teams": [2479620, 310433, 1226234, 3677093, 10523487, 3151301, 9014107072], "rate_limit": {"redis": {"host": "localhost", "port": 6379, "useTls": false}}, "rate_limits": {"1": 100, "10": 1000, "11": 10000, "12": 10000, "13": 100, "14": 100, "15": 100, "16": 1000, "17": 10000, "2": 100, "3": 100, "4": 10000, "5": 1000, "6": 100, "7": 100, "8": 100, "9": 100, "*********": 10000, "authz_token": {"ECODE": "AUTHZ_TOKEN_RATE_LIMIT_001", "limit": 750, "redisStorePrefix": "authz_token_limit:", "windowInSeconds": 60}, "default": 100, "genericView": {"limit": 25, "task_list_limit": 10000, "windowInSeconds": 15}, "genericViewWS": {"limit": 25, "windowInSeconds": 15}, "global": {"exclusions": ["/api", "/v1/wsMsg", "/v2/wsMsg", "/v1/wsQueue", "/v1/lbWsMsg", "/v2/lbWsMsg", "/v1/webhookQueue", "/v1/automationQueue", "/v1/es_updates", "/health", "\\/\\w+/health", "/ngsw", "/v1/orderindexMigrationQueue", "/v1/codoxApi", "[^?]*email_task", "/automation/automationSortQueue", "/v1/gcal_task_updated", "/v1/widget/[^/]+/stats", "/widget/[^/]+/stats", "/v1/import/companion/*", "/v1/test-rate-limit-[^/]+", "/v2/sd", "/attachments/v1/data/utils/authz_token"], "limit": 1000, "windowInSeconds": 60}, "import_new_job": {"limit": 60, "redisStorePrefix": "import_new_job_limit:", "windowInSeconds": 3600}, "promo_code": {"limit": 30, "redisStorePrefix": "promo_code_limit:", "windowInSeconds": 600}, "sharedAndHierarchy": {"limit": 8, "windowInSeconds": 10}}, "read_only_guest_to_guest_blacklist": ["vmware.com"], "recaptcha": {"android_secret": "", "datadog_synthetics_ips": ["*************", "*************", "*************", "**************", "*************", "**************", "***********", "**************", "************", "**************", "************", "**************", "*************", "*************", "************", "************", "*************", "**************", "**************", "*************", "*************", "**************", "*************", "*************", "*************", "**************", "************", "*************", "*************", "*************", "***********", "*********", "************", "************", "************", "************", "**********", "************", "************", "**********", "*************", "*************", "*************", "*************", "*************", "**************", "************", "***********", "************", "************", "*************", "***********", "************", "*************", "*************", "************", "************"], "datadog_synthetics_ips_modified": "2022-06-22-12-00-00", "datadog_synthetics_ips_url": "https://ip-ranges.datadoghq.com/synthetics.json", "score_threshold_v3": "0.5", "secret_v3": "", "web_secret": ""}, "recently": 30000, "recently_closed_tasks_max_per_user": 1000, "recently_created_tasks_max_per_user": 1000, "recently_done_tasks_max_per_user": 1000, "recently_table_prune_batch_size": 100, "recently_tracked_tasks_max_per_user": 1000, "recently_updated_tasks_max_per_user": 1000, "recently_updated_views_max_per_user": 1000, "recently_viewed_pages_max_per_user": 1000, "recently_viewed_views_max_per_user": 1000, "recur": {"recur_on": {"schedule": 1, "trigger": 2}, "status_types": {"closed": 1, "done": 2}, "types": {"create_new_task": 2, "restart_due_date": 1}}, "region": "local", "relationships": {"display_settings": ["off", "dialog", "accordion"], "sections": ["dependencies", "tasks", "pages", "backlinks", "backlinks:tasks", "backlinks:pages", "integrations"]}, "reliable_queue": {"processor_cleanup_interval_minutes": 5, "retry_limit": 3}, "returnEmailCode": true, "role_permissions": {"default_settings": {"1": {"add_email_account": 1, "assignee": 1, "attachments": 1, "can_convert_item": 1, "can_convert_milestone": 1, "can_edit_user_groups": 1, "can_send_login_bypass_link": 1, "can_set_approval_settings": 1, "can_use_chat": 1, "can_use_public_api_dev_key": 1, "can_view_audit_logs": 1, "can_view_team_timesheet": 1, "comment_resolved": 1, "create_private_view": 1, "create_projects": 1, "custom_roles": 1, "delete_items": 1, "dependency_of": 1, "depends_on": 1, "due_date": 1, "due_date_missed": 1, "edit_members": 1, "exporting": 1, "git": 1, "importing": 1, "invite_guests": 1, "invite_members": 1, "manage_custom_fields": 1, "manage_custom_items": 1, "manage_statuses": 1, "manage_tags": 1, "pin_fields": 1, "reaction": 1, "recur": 1, "send_email": 1, "team_integrations": 1, "team_permissions": 1, "unblocked": 1, "upload_attachments": 1, "views": 1}, "2": {"add_email_account": 1, "assignee": 1, "attachments": 1, "can_convert_item": 1, "can_convert_milestone": 1, "can_edit_user_groups": 1, "can_send_login_bypass_link": 1, "can_set_approval_settings": 1, "can_use_chat": 1, "can_use_public_api_dev_key": 1, "can_view_audit_logs": 0, "can_view_team_timesheet": 1, "comment_resolved": 1, "convert_custom_fields": 1, "create_custom_fields": 1, "create_private_view": 1, "create_projects": 1, "custom_roles": 0, "delete_items": 1, "dependency_of": 1, "depends_on": 1, "due_date": 1, "due_date_missed": 1, "edit_custom_fields": 1, "edit_members": 1, "exporting": 1, "git": 1, "importing": 1, "invite_guests": 1, "invite_members": 1, "manage_custom_fields": 1, "manage_custom_items": 1, "manage_statuses": 1, "manage_tags": 1, "merge_custom_fields": 1, "move_custom_fields": 1, "pin_fields": 1, "reaction": 1, "recur": 1, "send_email": 1, "team_integrations": 1, "team_permissions": 1, "unblocked": 1, "upload_attachments": 1, "views": 1}, "3": {"add_email_account": 1, "assignee": 1, "attachments": 1, "can_convert_item": 1, "can_convert_milestone": 1, "can_edit_user_groups": 0, "can_send_login_bypass_link": 0, "can_set_approval_settings": 0, "can_use_chat": 1, "can_use_public_api_dev_key": 1, "can_view_audit_logs": 0, "can_view_team_timesheet": 0, "comment_resolved": 1, "convert_custom_fields": 1, "create_custom_fields": 1, "create_private_view": 1, "create_projects": 1, "custom_roles": 0, "delete_items": 3, "dependency_of": 1, "depends_on": 1, "due_date": 1, "due_date_missed": 1, "edit_custom_fields": 1, "edit_members": 0, "exporting": 0, "git": 1, "importing": 1, "invite_guests": 1, "invite_members": 0, "manage_custom_fields": 1, "manage_custom_items": 1, "manage_statuses": 1, "manage_tags": 1, "merge_custom_fields": 1, "move_custom_fields": 1, "pin_fields": 1, "reaction": 1, "recur": 1, "send_email": 1, "team_integrations": 0, "team_permissions": 0, "unblocked": 1, "upload_attachments": 1, "views": 1}, "4": {"add_email_account": 0, "assignee": 1, "attachments": 1, "can_convert_item": 1, "can_convert_milestone": 1, "can_edit_user_groups": 0, "can_send_login_bypass_link": 0, "can_set_approval_settings": 0, "can_use_chat": 1, "can_use_public_api_dev_key": 1, "can_view_audit_logs": 0, "can_view_team_timesheet": 0, "comment_resolved": 1, "convert_custom_fields": 0, "create_custom_fields": 0, "create_private_view": 1, "create_projects": 0, "custom_roles": 0, "delete_items": 3, "dependency_of": 1, "depends_on": 1, "due_date": 1, "due_date_missed": 1, "edit_custom_fields": 0, "edit_members": 0, "exporting": 0, "git": 0, "importing": 0, "invite_guests": 0, "invite_members": 0, "manage_custom_fields": 0, "manage_custom_items": 0, "manage_statuses": 0, "manage_tags": 0, "merge_custom_fields": 0, "move_custom_fields": 0, "pin_fields": 1, "reaction": 1, "recur": 1, "send_email": 0, "team_integrations": 0, "team_permissions": 0, "unblocked": 1, "upload_attachments": 1, "views": 0}}, "mandatory_settings": {"1": ["invite_guests", "edit_members", "create_projects", "team_integrations", "importing", "exporting", "manage_tags", "manage_custom_fields", "create_custom_fields", "edit_custom_fields", "delete_custom_fields", "merge_custom_fields", "convert_custom_fields", "move_custom_fields", "views", "manage_statuses", "delete_items", "team_permissions", "pin_fields", "git", "custom_roles", "add_email_account", "send_email", "manage_custom_items", "can_convert_item", "can_convert_milestone", "upload_attachments", "invite_members", "can_view_team_timesheet", "can_use_chat", "can_view_audit_logs", "can_edit_user_groups"], "2": [], "3": ["team_permissions", "custom_roles", "can_view_audit_logs"], "4": ["invite_guests", "invite_members", "edit_members", "create_projects", "team_integrations", "team_permissions", "custom_roles", "add_email_account", "git", "send_email", "manage_custom_items", "can_convert_item", "can_convert_milestone", "can_view_audit_logs", "can_edit_user_groups"], "4:3": ["edit_members", "create_projects", "team_permissions", "importing", "exporting", "team_integrations", "custom_roles", "manage_tags", "manage_statuses", "workloads", "manage_custom_fields", "git", "add_email_account", "invite_members", "can_view_team_timesheet", "can_view_audit_logs", "can_edit_user_groups", "views"], "4:4": ["edit_members", "team_permissions", "importing", "exporting", "team_integrations", "custom_roles", "manage_tags", "manage_statuses", "workloads", "manage_custom_fields", "git", "add_email_account", "invite_members", "can_view_team_timesheet", "can_edit_user_groups", "can_view_audit_logs"]}, "subtype_role_default_settings": {"4": {"create_projects": 1}}, "subtype_role_disabled_settings": {"3": ["create_projects", "views"]}, "valid_roles": [2, 3, 4], "valid_settings": ["invite_guests", "edit_members", "create_projects", "team_integrations", "importing", "exporting", "manage_tags", "manage_custom_fields", "views", "manage_statuses", "delete_items", "team_permissions", "workloads", "pin_fields", "git", "custom_roles", "add_email_account", "send_email", "manage_custom_items", "can_convert_item", "can_convert_milestone", "create_custom_fields", "edit_custom_fields", "delete_custom_fields", "merge_custom_fields", "convert_custom_fields", "move_custom_fields", "upload_attachments", "invite_members", "can_view_team_timesheet", "can_use_chat", "can_view_audit_logs", "can_edit_user_groups", "can_use_public_api_dev_key", "can_send_login_bypass_link"]}, "role_subtype": {"permission_overrides": {"1": {"chat_comment": false, "chat_reply": false, "comment": false, "create_private_view": 0}, "2": {"chat_create_dm": true}, "3": {"can_be_added_to_spaces": true, "can_see_team_members": true, "chat_add_members": true, "chat_create_dm": true, "create_private_view": 0, "profile": true, "share": true}, "4": {"can_add_automation": true, "can_be_added_to_spaces": true, "can_create_folders": true, "can_create_goals": true, "can_create_lists": true, "can_create_personal_list": true, "can_create_portfolios": true, "can_create_projects": true, "can_create_spaces": true, "can_edit_privacy": 1, "can_edit_project_settings": 1, "can_edit_space_settings": 1, "can_see_team_members": true, "chat_add_members": true, "chat_create_channel": true, "chat_create_dm": true, "create_automation": true, "create_dashboards": true, "profile": true, "send_email": true, "share": true}}}, "roles": {"1": {"add_email_account": true, "billing": true, "can_add_team_guests": true, "can_add_team_members": true, "can_add_workspace_attachments": true, "can_be_added_to_spaces": true, "can_be_added_to_user_groups": true, "can_convert_item": true, "can_create_folders": true, "can_create_goals": true, "can_create_lists": true, "can_create_milestone": true, "can_create_personal_list": true, "can_create_portfolios": true, "can_create_projects": true, "can_create_spaces": true, "can_create_workload": true, "can_create_workspace_doc": true, "can_delete_comments": true, "can_delete_no_access": true, "can_edit_description": true, "can_edit_integrations": true, "can_edit_list_statuses": true, "can_edit_privacy": 2, "can_edit_project_settings": 2, "can_edit_space_settings": 2, "can_edit_team": true, "can_edit_team_members": true, "can_edit_team_owner": true, "can_edit_trial": true, "can_edit_user_groups": true, "can_edit_view_protection": true, "can_enable_sso": true, "can_export_tasks": true, "can_gdpr_export": true, "can_import": true, "can_list_inaccessible_spaces": true, "can_make_tasks_public": true, "can_pin_fields": true, "can_recover_inaccessible_spaces": true, "can_see_data_retention_settings": true, "can_see_team_members": true, "can_see_workload": true, "can_send_login_bypass_link": true, "can_set_approval_settings": true, "can_set_data_retention_settings": true, "can_use_chat": true, "can_use_git": true, "can_use_public_api_dev_key": true, "can_view_audit_logs": true, "can_view_reporting": true, "can_view_team_timesheet": true, "chat_add_members": true, "chat_create_channel": true, "chat_create_dm": true, "create_automation": true, "create_dashboards": true, "custom_roles": true, "make_views_public": true, "manage_custom_fields": true, "manage_custom_items": true, "manage_statuses": true, "manage_tags": true, "name": "owner", "oauth_apps": true, "profile": true, "public_spaces_visible": true, "send_email": true, "share": true, "team_permissions": true, "team_role": 1}, "2": {"add_email_account": true, "billing": true, "can_add_team_guests": true, "can_add_team_members": true, "can_add_workspace_attachments": true, "can_be_added_to_spaces": true, "can_be_added_to_user_groups": true, "can_convert_item": true, "can_create_folders": true, "can_create_goals": true, "can_create_lists": true, "can_create_milestone": true, "can_create_personal_list": true, "can_create_portfolios": true, "can_create_projects": true, "can_create_spaces": true, "can_create_workload": true, "can_create_workspace_doc": true, "can_delete_comments": true, "can_delete_no_access": false, "can_edit_description": true, "can_edit_integrations": true, "can_edit_list_statuses": true, "can_edit_privacy": 2, "can_edit_project_settings": 2, "can_edit_space_settings": 2, "can_edit_team": true, "can_edit_team_members": true, "can_edit_team_owner": false, "can_edit_trial": true, "can_edit_user_groups": true, "can_edit_view_protection": true, "can_enable_sso": true, "can_export_tasks": true, "can_gdpr_export": true, "can_import": true, "can_list_inaccessible_spaces": true, "can_make_tasks_public": true, "can_pin_fields": true, "can_recover_inaccessible_spaces": false, "can_see_data_retention_settings": true, "can_see_team_members": true, "can_see_workload": true, "can_send_login_bypass_link": true, "can_set_approval_settings": true, "can_set_data_retention_settings": true, "can_use_chat": true, "can_use_git": true, "can_use_public_api_dev_key": true, "can_view_audit_logs": false, "can_view_reporting": true, "can_view_team_timesheet": true, "chat_add_members": true, "chat_create_channel": true, "chat_create_dm": true, "create_automation": true, "create_dashboards": true, "custom_roles": false, "make_views_public": true, "manage_custom_fields": true, "manage_custom_items": true, "manage_statuses": true, "manage_tags": true, "name": "admin", "oauth_apps": true, "profile": true, "public_spaces_visible": true, "send_email": true, "share": true, "team_permissions": true, "team_role": 2}, "3": {"add_email_account": true, "billing": false, "can_add_team_guests": true, "can_add_team_members": false, "can_add_workspace_attachments": true, "can_be_added_to_spaces": true, "can_be_added_to_user_groups": true, "can_convert_item": true, "can_create_folders": true, "can_create_goals": true, "can_create_lists": true, "can_create_milestone": true, "can_create_personal_list": true, "can_create_portfolios": true, "can_create_projects": true, "can_create_spaces": true, "can_create_workload": true, "can_create_workspace_doc": true, "can_delete_comments": false, "can_delete_no_access": false, "can_edit_description": false, "can_edit_integrations": false, "can_edit_list_statuses": true, "can_edit_privacy": 1, "can_edit_project_settings": 1, "can_edit_space_settings": 1, "can_edit_team": false, "can_edit_team_members": false, "can_edit_team_owner": false, "can_edit_trial": false, "can_edit_user_groups": false, "can_edit_view_protection": true, "can_enable_sso": false, "can_export_tasks": true, "can_gdpr_export": false, "can_import": true, "can_list_inaccessible_spaces": false, "can_make_tasks_public": true, "can_pin_fields": true, "can_recover_inaccessible_spaces": false, "can_see_data_retention_settings": false, "can_see_team_members": true, "can_see_workload": true, "can_send_login_bypass_link": false, "can_set_approval_settings": false, "can_set_data_retention_settings": false, "can_use_chat": true, "can_use_git": true, "can_use_public_api_dev_key": true, "can_view_audit_logs": false, "can_view_reporting": true, "can_view_team_timesheet": false, "chat_add_members": true, "chat_create_channel": true, "chat_create_dm": true, "create_automation": true, "create_dashboards": true, "custom_roles": false, "make_views_public": true, "manage_custom_fields": true, "manage_custom_items": false, "manage_statuses": true, "manage_tags": true, "name": "member", "oauth_apps": false, "profile": true, "public_spaces_visible": true, "send_email": true, "share": true, "team_permissions": false, "team_role": 3}, "4": {"add_email_account": false, "billing": false, "can_add_automation": false, "can_add_team_guests": false, "can_add_team_members": false, "can_add_workspace_attachments": false, "can_be_added_to_spaces": false, "can_be_added_to_user_groups": true, "can_convert_item": true, "can_create_folders": false, "can_create_goals": false, "can_create_lists": false, "can_create_milestone": true, "can_create_personal_list": false, "can_create_portfolios": false, "can_create_projects": false, "can_create_spaces": false, "can_create_workload": true, "can_create_workspace_doc": false, "can_delete_comments": false, "can_delete_no_access": false, "can_edit_description": false, "can_edit_integrations": false, "can_edit_list_statuses": false, "can_edit_privacy": 0, "can_edit_project_settings": 0, "can_edit_space_settings": 0, "can_edit_team": false, "can_edit_team_members": false, "can_edit_team_owner": false, "can_edit_trial": false, "can_edit_user_groups": false, "can_edit_view_protection": false, "can_enable_sso": false, "can_export_tasks": false, "can_gdpr_export": false, "can_import": false, "can_list_inaccessible_spaces": false, "can_make_tasks_public": false, "can_pin_fields": true, "can_recover_inaccessible_spaces": false, "can_see_data_retention_settings": false, "can_see_team_members": false, "can_see_workload": true, "can_send_login_bypass_link": false, "can_set_approval_settings": false, "can_set_data_retention_settings": false, "can_use_chat": true, "can_use_git": false, "can_use_public_api_dev_key": true, "can_view_audit_logs": false, "can_view_reporting": true, "can_view_team_timesheet": false, "chat_add_members": false, "chat_create_channel": false, "chat_create_dm": false, "convert_custom_fields": false, "create_automation": false, "create_custom_fields": false, "create_dashboards": false, "custom_roles": false, "delete_custom_fields": false, "edit_custom_fields": false, "make_views_public": false, "manage_custom_fields": false, "manage_custom_items": false, "manage_statuses": false, "manage_tags": false, "merge_custom_fields": false, "move_custom_fields": false, "name": "guest", "oauth_apps": false, "profile": false, "public_spaces_visible": false, "send_email": false, "share": false, "team_permissions": false, "team_role": 4}}, "rollupBannerExpirationInDays": 7, "sales_types": {"hybrid": "hybrid", "sales_assisted": "sales assisted", "self_serve": "self serve"}, "salesforce": {"api_base": "", "app_client_id": "", "app_refresh_token": "", "app_secret_id": "", "client_id": "", "client_secret": "", "refresh_token": ""}, "salt": "", "salt_rounds": 14, "scheduled_triggers": {"fetch_batch_size": 500, "use_double_fetch": false}, "scheduling_server": false, "screenshotService": {"local": {"endpoint": "http://localhost:8000", "region": "us-east-1", "tableName": "ScreenshotService"}}, "search": {"testRequest": {"userID": 49, "workspaceID": 353}}, "search_processor": {"dlqTopic": "DeadLetterQueueSDP", "kafkaClientId": "be-search", "kafkaConsumerGroupId": "search-pipeline", "kafkaConsumerMaxBytes": 24000, "maxContentLength": 990000, "maxRetryCount": 10, "noOpWrite": false, "processorTimeoutMs": 60000, "reindexBlockSize": 100, "retryDelayMaxMs": 7500, "retryDelayMinMs": 500, "retryTopic": "RetryTopicSDP", "sendToTopicRetryOptions": {"initialRetryTime": 1000, "maxRetryTime": 6000, "multiplier": 1, "retries": 5}, "sinkTopicMap": {"attachment": "clickup_attachments", "comment": "clickup_comments", "page": "clickup_pages", "task": "clickup_tasks"}, "sourceTopic": "ObjectVersionVectorCDC", "useDlq": false}, "search_processor_2": {"dlq": {"enabled": true, "kafkaTopic": "DeadLetterQueueSDP"}, "elastic": {"indexMap": {"attachment": "clickup_attachments", "comment": "clickup_comments", "page": "clickup_pages", "task": "clickup_tasks"}, "noOpWrite": false}, "kafka": {"cdcConsumerGroupPrefix": "search-pipeline", "clientId": "be-search", "consumerMaxBytes": 24000, "processorTimeoutMs": 60000, "retryConsumerGroupPrefix": "search-pipeline-retry", "sendToTopicRetryOptions": {"initialRetryTime": 1000, "maxRetryTime": 6000, "multiplier": 1, "retries": 5}}, "maxContentLength": 990000, "retry": {"delayMaxMs": 7500, "delayMinMs": 500, "kafkaTopic": "RetryTopicSDP", "maxRetryCount": 10}, "sourceKafkaTopic": "ObjectVersionVectorCDC"}, "secretsManager": {"dev_account_for_secrets": "************", "environment_replica_regions": ["us-east-1", "us-west-2"], "legacy_replica_regions": ["us-east-1", "us-west-2"], "managed_secret_prefix": "dev"}, "security": {"redis": {"cluster": false, "host": "localhost", "port": 6379, "useTls": false}}, "segment": {"backend_system_account": "managedSecret/v1/env/true/json/segment_backend_system_account", "write_key": ""}, "sendEmails": false, "sendSMS": true, "sendgrid": {"key": ""}, "sentry": {"client_id": "", "client_secret": ""}, "server": {"headers_timeout_ms": 60000, "keep_alive_timeout_ms": 5000}, "service_status": {"active": 1, "archived": 6, "canceled": 2, "fraud": 3, "frozen": 5, "suspended": 4, "trial": 0}, "session_settings": {"max_bearer_token_expiration_in_ms": ********}, "sharding": {"enforce_routed_requests": false, "exportRoutes": null, "formRoutes": null, "legacy_shard_id": "g001", "routes": [], "shard_id": "g001", "wsRoutes": null}, "sharing": {"default_hist_fields": ["name", "status", "task_creation"], "hist_field_map": {"assignees": ["assignee"], "attachments": ["attachments"], "checklists": ["checklist_items_added", "checklist_item_assignee", "checklist_item_resolved"], "comments": ["comment", "comment_resolved", "comment_assigned"], "content": ["content"], "customFields": [], "due_date": ["due_date"], "followers": ["follower"], "priority": ["priority"], "subtasks": ["new_subtask"], "tags": ["tag", "tag_removed"]}, "public_fields": ["assignees", "priority", "due_date", "content", "comments", "attachments", "customFields", "subtasks", "tags", "checklists", "coverimage"], "public_permission_levels": {"1": {"can_comment": false, "name": "can read"}, "3": {"can_comment": true, "name": "can comment"}}}, "simple_cache": {"default_ttl_seconds": 300, "redis_host": "localhost", "redis_port": 6379, "useTls": false}, "skilljarMonitor": {"credentials": {"clientId": "", "clientSecret": "", "url": ""}}, "skip_guest_count_teams": [1851686, 2591381, 52305, ********, ********, 1235435, 1221863, ********, 3357294], "slapdash": {"client_id": "clickup_local", "client_namespace": "", "client_secret": "Kgc5vsvnbdmIhGisFQoWzz6t3O9Vasx2bc3avBz", "global_client_id": "clickup_global", "global_client_secret": "Kgc5vsvnbdmIhGisFQoWzz6t3O9Vasx2bc3avBz", "origin": "https://search.clickup-qa.com"}, "snowflake": {"account_identifier": "", "authenticator": "", "database": "", "password": "", "role": "", "schema": "", "username": "", "warehouse": ""}, "social_sign_in": {"providers": ["google"]}, "sort_automation_rate_limiting_config": {"action": "delay_rate_limited_messages", "action_type": {"message_expiry": 86400, "message_expiry_key": "first_delay_ts"}, "duration": 60, "max_counter": 10000, "notification": false}, "source_control": {"bitbucket": "bitbucket", "github": "github", "gitlab": "gitlab"}, "splitio": {"api_key": ""}, "sql_field_types": {"attachment": "text[]", "checkbox": "boolean", "currency": "numeric", "date": "numeric", "drop_down": "numeric", "email": "text", "emoji": "numeric", "labels": "uuid[]", "list_relationship": "text[]", "manual_progress": "numeric", "number": "numeric", "phone": "text", "short_text": "text", "tasks": "text[]", "text": "text", "url": "text", "users": "numeric[]"}, "sso": {"azure": {"certificate_name": "certificate.pem", "federation_metadata_urls": ["https://login.microsoftonline.com/a6b44062-7852-445c-aba7-c33a826ee1a9/federationmetadata/2007-06/federationmetadata.xml?appid=4415373a-6637-43d8-a276-1ac35b4ce8e3"], "idp_login_url": "https://login.microsoftonline.com/common/saml2", "idp_logout_url": "https://login.microsoftonline.com/common/wsfederation?wa=wsignout1.0", "private_key_name": "key.pem", "setup_url": "https://docs.microsoft.com/en-us/azure/active-directory/saas-apps/clickup-productivity-platform-tutorial"}, "okta": {"setup_url": "https://help.clickup.com/hc/en-us/articles/6305095991703-Enable-<PERSON><PERSON>-single-sign-on-integration"}, "provider_ids": ["google_id", "azure_id", "okta_id"], "providers": ["google_sso", "azure_sso", "okta_sso", "saml_sso"], "require_levels": {"all_users": 2, "except_guests": 1, "optional": 0}, "required_sso": {"exempt_user_types": [1]}, "saml": {"setup_url": "https://docs.clickup.com/en/articles/3952199-custom-saml-single-sign-on", "types": {"azure": "azure", "custom": "custom", "okta": "okta"}}, "saml_cert_grace_period": {"unit": "months", "value": 1}, "sp_host": "https://f758ed370964.ngrok.io"}, "staging_ba": {"password": "", "user": ""}, "start_of_reminder_notifications": 1569357390853, "static_test_email_code": 9876, "subcat_status_types": {"hidden": 2, "visible": 1}, "subtask_sort_options": ["name", "priority", "status", "due_date", "manual"], "task_notifs": {"created": 1, "follow": 2, "none": 0}, "task_state": {"threads": 4}, "tasks": {"limits": {"text_content_size": 262144}}, "taxamo": {"api_private_token": "", "api_url": "https://services.taxamo.com/api/v2/"}, "team_invite": {"expiration": "14d", "expiration_in_ms": **********}, "team_limit_fields": ["gantt_critical_path", "siml", "custom_roles"], "team_members": {"task_fanout_after_removal_batch_size": 500}, "team_roles": {"admin": 2, "guest": 4, "owner": 1, "user": 3}, "team_tags": {"types": {"doc_tags": 1}}, "teams": {"remindInterval": 3600000}, "template_permissions": {"admin": 2, "members": 0, "private": 3, "public": 1, "team_members": 4}, "template_types": {"category": 2, "checklist": 8, "filter": 7, "project": 1, "status": 6, "subcategory": 3, "task": 5, "view": 4}, "template_user": -3, "temporal": {"address": "temporal-frontend.core", "tls": false}, "todoist": {"client_id": "", "client_secret": ""}, "toggl": {"interval": 90000}, "trash": {"delete_threshold": {"docs": 180, "lists": 31, "notes": 31, "reminders": 7, "tasks": 31, "views": 31}, "page_size": 100, "paging_cache_ttl": 300, "recurrence_minutes": 30, "reminders_safeguard": 5000}, "trash_sort_value": {"category": 4, "customItem": 10, "dashboard": 12, "field": 6, "page": 7, "project": 5, "projectTag": 9, "reminder": 13, "subcategory": 3, "subtask": 11, "tag": 8, "task": 2, "timeEntry": 14, "view": 1}, "trayTabs": {"max": 100}, "trello": {"key": "", "secret": ""}, "twilio": {"MessagingServiceSid": "", "acct_sid": "", "phone_number": "", "sms_debounce_timeout_sec": 30, "token": "", "verify_sid": ""}, "twofa_methods": {"text": 1, "totp": 2}, "unstarted_status_types": ["open", "unstarted"], "usage_limit_fields": ["custom_fields", "gantt_usage", "mind_maps", "timelines", "workloads", "map_usage"], "use_express_partial_response": true, "user_data_workspace_id": 100, "user_weight_type": {"assign": 0, "assigned_comment": 1, "mention": 2}, "v-portfolio": {"cache_ttl": 300}, "v2_migrated_template_teams": [2553720], "validCookieOrigins": {"eudev": ["https://app.clickup-eudev.com", "https://share.clickup-eudev.com", "https://docs.clickup-eudev.com"], "euprod": ["https://doc.clickup-eu.com", "https://app.clickup-eu.com", "https://share.clickup-eu.com"], "prod": ["https://app.clickup.co", "https://app.clickup.com", "https://clickup.com", "https://doc.clickup.com", "https://publicdoc.clickup.com", "https://public-doc.clickup.com", "https://share-docs.clickup.com", "https://pull-requests.clickup.com", "https://share.clickup.com", "https://sharing.clickup.com", "https://id.app.clickup.com", "https://oauth.clickup.com", "https://prod-eu-west-1-g.clickup.com", "https://prod-eu-west-1-2.clickup.com", "https://prod-eu-west-1-3.clickup.com", "https://prod-ap-southeast-1-g.clickup.com", "https://prod-ap-southeast-1-1.clickup.com", "https://prod-ap-southeast-2-g.clickup.com", "https://prod-ap-southeast-2-2.clickup.com", "https://prod-us-east-2-g.clickup.com", "https://prod-us-east-2-1.clickup.com", "https://prod-us-east-2-2.clickup.com", "https://prod-us-west-2-g.clickup.com", "https://prod-us-west-2-2.clickup.com", "https://prod-us-west-2-3.clickup.com", "https://miro.clickup.com"], "stage1": ["https://pre-prod1.clickup.com", "https://s1-pull-requests.clickup.com", "https://stage1-doc.clickup.com", "https://stage1-forms.clickup.com", "https://stage1-share.clickup.com", "https://stage1.clickup.com", "https://stage1-oauth.clickup.com"], "staging": ["https://dev-doc.clickup.com", "https://dev-share.clickup.com", "https://qa.clickup-stg.com", "https://staging-pull-requests.clickup.com", "https://staging.clickup.com", "https://app.clickup-stg.com", "http://localhost:4200", "http://localhost:4201", "https://id.app.clickup-stg.com", "https://doc.clickup-stg.com", "https://share.clickup-stg.com", "https://oauth.clickup-stg.com", "https://staging-us-west-2-g.clickup.com", "https://staging-us-west-2-1.clickup.com", "https://miro.clickup-stg.com"], "usqa": ["https://app.clickup-qa.com", "https://doc.clickup-qa.com", "https://pull-requests.clickup.com", "https://share.clickup-qa.com", "https://dev-playground.clickup-qa.com", "http://localhost:4200", "http://localhost:4201", "http://localhost:4204", "https://id.app.clickup-qa.com", "https://oauth.clickup-qa.com", "https://qa-us-east-2-g.clickup.com", "https://qa-us-east-2-1.clickup.com", "https://qa-us-east-2-2.clickup.com", "https://qa-us-west-2-g.clickup.com", "https://qa-us-west-2-6.clickup.com", "https://miro.clickup-qa.com"]}, "validFormCookieOrigins": {"prod": ["https://forms.clickup.com"], "stage1": ["https://s1-pull-requests.clickup.com", "https://stage1-forms.clickup.com"], "staging": ["https://dev-forms.clickup.com", "https://forms.clickup-stg.com", "https://staging-pull-requests.clickup.com", "http://localhost:4200", "http://localhost:4201"], "usqa": ["https://forms.clickup-qa.com", "https://pull-requests.clickup.com", "http://localhost:4200", "http://localhost:4201"]}, "versions": {"mobile": {"flutter": {"csrf_token": "3.14.2", "sso_protocol": "3.12.3"}, "ionic": {}}}, "views": {"activity_filter_fields": ["fields", "users", "date_range", "assigned_comment_users"], "batching": {"content_history_limit": 75, "filter_limit": 5, "query_limit": 10}, "box_sort_fields": ["taskCount", "incomplete", "timeEstimate", "name", "scrum", "pointsEstimate"], "calculation_custom_fields": [1, 4, 7, 8, 9, 11, 14, 16, 17, 18, 21], "calculation_fields": ["timeLogged", "timeEstimate", "pointsEstimate", "timeL<PERSON><PERSON><PERSON><PERSON>", "timeEstimateRollup", "pointsEstimateRollup", "dueDate", "startDate", "dateCreated", "dateUpdated", "dateClosed", "dateDone", "status", "timeInStatus", "incompleteCommentCount", "id", "created<PERSON>y", "commentCount", "priority", "dependencies", "customId", "assignee", "linked", "pages"], "calculation_functions": ["sum", "average", "median", "range", "min", "max", "valuescount", "uniquevaluescount", "emptycount", "emptypercent", "notemptypercent", "countpergroup", "percentpergroup"], "column_fields": ["name", "status", "dateCreated", "dateUpdated", "dateClosed", "dateDone", "startDate", "dueDate", "commentCount", "incompleteCommentCount", "latestComment", "timeLogged", "timeL<PERSON><PERSON><PERSON><PERSON>", "timeEstimate", "timeEstimateRollup", "pointsEstimate", "pointsEstimateRollup", "assignee", "priority", "id", "recurring", "created<PERSON>y", "lists", "tag", "linked", "dependencies", "pullRequests", "customId", "timeInStatus", "sprints", "pages", "customItems", "duration", "duration_is_elapsed"], "custom_field_group_by_types": ["checkbox", "date", "drop_down", "emoji", "labels", "list_relationship", "tasks", "users", "votes"], "divide_fields": ["subcategory", "assignee", "priority"], "doc_types": {"meeting_notes": 3, "standard": 1, "wiki": 2}, "fast_load_mode_views": [1], "filter_fields": ["anyDate", "archived", "assignee", "blocked_by", "category", "creator", "customItems", "dateClosed", "dateCreated", "dateDone", "dateOfLastStatusChange", "dateUpdated", "delegator", "dependency", "description", "dueDate", "duration", "duration_is_elapsed", "include_active", "isClosed", "linked_docs", "linked_tasks", "location", "milestone", "name", "pointsEstimate", "pointsEstimateRollup", "priority", "project", "recurring", "recurring_v2", "show_estimate", "startDate", "status", "statusType", "subcategory", "tag", "timeEstimate", "timeEstimateRollup", "timeLogged", "timeL<PERSON><PERSON><PERSON><PERSON>", "time_billable", "time_tag", "time_user", "unresolvedComment", "waiting_on", "watcher"], "filter_operators": ["GT", "GTE", "LT", "LTE", "EQ", "NOT", "ANY", "ALL", "NOT ANY", "NOT ALL", "IS SET", "NOT SET"], "frontend_code_to_name": {"a": "Activity", "b": "Board", "c": "Calendar", "g": "<PERSON><PERSON><PERSON>", "gr": "Table", "l": "List", "m": "Map", "mm": "Mind Map", "o": "Location Overview", "tl": "Timeline", "wl": "Workload", "x": "Box"}, "grouping_fields": ["subcategory", "assignee", "group", "priority", "status", "tag", "dueDate", "customItems", "none"], "locked_permission": {"admins_only": 3, "can_unlock": 1, "cant_unlock": 2}, "max_page_recursive_depth": 100, "page_views": [6, 8, 9, 15], "parent_types": {"category": 5, "dashboard": 20, "document": 13, "project": 4, "shared": 8, "shared_tasks": 11, "subcategory": 6, "task": 1, "team": 7, "team_unparented": 12, "template": 9, "user_group": 14, "view": 15, "widget": 10}, "permission_level_constants": {"comment": 3, "edit": 5, "read": 1}, "permission_levels": {"1": {"can_add_automation": true, "can_delete_comments": false, "chat_add_followers": true, "chat_add_self_follower": true, "chat_comment": true, "chat_delete_channel": false, "chat_manage_tiles": false, "chat_remove_followers": false, "chat_remove_members": false, "chat_remove_self_follower": true, "chat_reply": true, "comment": false, "create_automation": true, "create_private_view": true, "delete_view": false, "edit_view": false}, "3": {"can_add_automation": true, "can_delete_comments": false, "chat_add_followers": true, "chat_add_self_follower": true, "chat_comment": true, "chat_delete_channel": false, "chat_manage_tiles": true, "chat_remove_followers": false, "chat_remove_members": false, "chat_remove_self_follower": true, "chat_reply": true, "comment": true, "create_automation": true, "create_private_view": true, "delete_view": false, "edit_view": false}, "4": {"can_add_automation": true, "can_delete_comments": false, "chat_add_followers": true, "chat_add_self_follower": true, "chat_comment": true, "chat_delete_channel": false, "chat_manage_tiles": true, "chat_remove_followers": true, "chat_remove_members": true, "chat_remove_self_follower": true, "chat_reply": true, "comment": true, "create_automation": true, "create_private_view": true, "delete_view": true, "edit_view": true}, "5": {"can_add_automation": true, "can_delete_comments": true, "chat_add_followers": true, "chat_add_self_follower": true, "chat_comment": true, "chat_delete_channel": true, "chat_manage_tiles": true, "chat_remove_followers": true, "chat_remove_members": true, "chat_remove_self_follower": true, "chat_reply": true, "comment": true, "create_automation": true, "create_private_view": true, "delete_view": true, "edit_view": true}}, "public_views": [1, 2, 7, 9, 12, 5, 16, 23, 20, 26, 27], "rollup_functions": ["avg", "sum", "min", "max", "range"], "rollup_types": {"direct": 1, "list": 2, "task": 3}, "show_subtasks_types": {"below_parent": 2, "none": 1, "stand_alone": 3}, "sorting_fields": ["subcategory", "assignee", "priority", "dueDate", "commentCount", "incompleteCommentCount", "startDate", "anyDate", "timeLogged", "timeL<PERSON><PERSON><PERSON><PERSON>", "timeEstimate", "timeEstimateRollup", "pointsEstimate", "pointsEstimateRollup", "dateCreated", "dateUpdated", "dateDone", "duration", "name", "status", "dateClosed", "dateDelegated", "created<PERSON>y", "timeInStatus", "linked", "dependencies", "pages", "id"], "task_views": [1, 2, 3, 4, 5, 7, 16, 17, 18, 20, 23, 26, 27], "template_visibility": {"admins": 2, "personal": 3, "public": 1, "team_members": 4, "users": 0}, "time_in_status_view": {"by_day": 2, "by_hour": 1, "since": 3}, "unassigned_sort_fields": ["status", "name", "priority"], "view_types": {"activity": 17, "activity_required": 19, "agent": 33, "agent_v1": 34, "agent_workspace": 32, "board": 2, "board_required": 11, "box": 3, "box_required": 14, "calendar": 5, "calendar_required": 13, "clickboard": 27, "conversation": 8, "conversation_dm": 30, "conversation_group_dm": 31, "dashboard": 29, "doc": 9, "embed": 6, "form": 15, "gantt": 7, "gantt_required": 12, "list": 1, "list_required": 10, "location_overview": 28, "map": 26, "mind_map": 20, "mind_map_required": 21, "table": 23, "table_required": 24, "time": 4, "timeline": 16, "timeline_required": 22, "workload": 18, "workload_required": 25}, "visibility": {"hidden": 4, "personal": 3, "private": 2, "public": 1, "system": 5}, "workload_capacity_types": {"0": "tasks", "1": "time_estimate", "2": "custom_field", "3": "points"}, "workload_fields": ["completed", "timeEstimate", "scrum", "pointsEstimate"]}, "visibility": {"admin": 2, "everyone": 3, "me": 0, "private": 1}, "webhook_server": true, "webhooks": {"active_statuses": [1, 2], "bearer": "", "events": {"folderCreated": 3, "folderDeleted": 3, "folderUpdated": 3, "goalCreated": 5, "goalDeleted": 5, "goalFolderCreated": 5, "goalFolderDeleted": 5, "goalFolderUpdated": 5, "goalUpdated": 5, "keyResultCreated": 5, "keyResultDeleted": 5, "keyResultUpdated": 5, "listCreated": 2, "listDeleted": 2, "listUpdated": 2, "spaceCreated": 4, "spaceDeleted": 4, "spaceUpdated": 4, "taskAssigneeUpdated": 1, "taskCommentPosted": 1, "taskCommentReaction": 1, "taskCommentReactionRemoved": 1, "taskCommentUpdated": 1, "taskCreated": 1, "taskDeleted": 1, "taskDueDateUpdated": 1, "taskMoved": 1, "taskPriorityUpdated": 1, "taskStatusUpdated": 1, "taskTagUpdated": 1, "taskTimeEstimateUpdated": 1, "taskTimeTrackedUpdated": 1, "taskUpdated": 1, "taskUserMentioned": 1, "teamDeleted": 7, "viewCommentPosted": 6, "viewCommentReaction": 6, "viewCommentReactionRemoved": 6, "viewCommentUpdated": 6, "viewDeleted": 6, "viewUserMentioned": 6}, "resource_types": {"folder": 3, "goal": 5, "list": 2, "space": 4, "task": 1, "view": 6, "workspace": 7}, "statuses": {"active": 1, "failing": 2, "paused": 3, "suspended": 4}, "suspend_threshold": 100}, "websocket_distributor": {"enabled": true, "redis": {"host": "localhost", "port": 6379, "useTls": false}}, "whiteboard": {"db_cluster": "", "db_password": "", "db_username": "", "secretName": "LocalDevWhiteboardDatabase", "secretRegion": "us-west-2", "tls_cert": "certs/rds-combined-ca-bundle.pem"}, "whitelabel": {"reserved_subdomains": ["app", "stage1", "staging", "doc", "docs", "export", "front", "api", "tableau", "azure", "forms", "outlook", "zendesk", "share"], "root_domain": "clickup.com"}, "widgets": {"attached_widget_delete_check_interval": 900000, "calculation": {"fields": ["task_count", "time_estimate", "time_spent", "points_estimate"], "functions": ["sum", "average", "range", "median", "min", "max", "count"]}, "parent_types": {"view": 8}, "sort_by": {"alphabetical": 1, "manual": 3, "value": 2}, "time_tracking_export_columns": [{"header": "User ID", "key": "userId"}, {"header": "Username", "key": "username"}, {"header": "Time Entry ID", "key": "timeEntryId"}, {"header": "Description", "key": "description"}, {"header": "Billable", "key": "billable"}, {"header": "Time Labels", "key": "time<PERSON><PERSON><PERSON>"}, {"header": "Start", "key": "start"}, {"header": "Start Text", "key": "startText"}, {"header": "Stop", "key": "stop"}, {"header": "Stop Text", "key": "stopText"}, {"header": "Time Tracked", "key": "timeTracked"}, {"header": "Time Tracked Text", "key": "timeTrackedText"}, {"header": "Space ID", "key": "spaceId"}, {"header": "Space Name", "key": "spaceName"}, {"header": "Folder ID", "key": "folderId"}, {"header": "Folder Name", "key": "folderName"}, {"header": "List ID", "key": "listId"}, {"header": "List Name", "key": "listName"}, {"header": "Task ID", "key": "taskId"}, {"header": "Task Name", "key": "taskName"}, {"header": "Task Status", "key": "taskStatus"}, {"header": "Due Date", "key": "dueDate"}, {"header": "Due Date Text", "key": "dueDateText"}, {"header": "Start Date", "key": "startDate"}, {"header": "Start Date Text", "key": "startDateText"}, {"header": "Task Time Estimated", "key": "taskTimeEstimated"}, {"header": "Task Time Estimated Text", "key": "taskTimeEstimatedText"}, {"header": "Task Time Spent", "key": "taskTimeSpent"}, {"header": "Task Time Spent Text", "key": "taskTimeSpentText"}, {"header": "User Total Time Estimated", "key": "userTotalTimeEstimated"}, {"header": "User Total Time Estimated Text", "key": "userTotalTimeEstimatedText"}, {"header": "User Total Time Tracked", "key": "userTotalTimeTracked"}, {"header": "User Total Time Tracked Text", "key": "userTotalTimeTrackedText"}, {"header": "Tags", "key": "tags"}, {"header": "Checklists", "key": "checkLists"}, {"header": "User Period Time Spent", "key": "userPeriodTimeSpent"}, {"header": "User Period Time Spent Text", "key": "userPeriodTimeSpentText"}, {"header": "Date Created", "key": "dateCreated"}, {"header": "Date Created Text", "key": "dateCreatedText"}, {"header": "Custom Task ID", "key": "customTaskId"}, {"header": "Parent Task ID", "key": "parentTaskId"}], "time_tracking_items_max_pages": 500, "time_tracking_items_page_size": 1000, "time_tracking_private_container_name": "Private"}, "workspaces": {"permission_levels": {"1": {"add_attachments": true, "can_read": true, "delete": true, "set_custom_field_values": true}, "3": {"add_attachments": true, "can_read": true}, "4": {"add_attachments": true, "can_read": true}, "5": {"add_attachments": true, "can_read": true}}}, "wrike": {"client_id": "", "client_secret": ""}, "ws": {"all_projects_key": -1, "categoryMsgs": ["categoryMemberRemoved", "categoryMoved", "categoryEdited", "categoriesCreated", "categoryMemberAdded", "categoryMemberEdited", "categoryOwnerEdited", "categoriesDeleted"], "checklistMsgs": ["checklistCreated", "checklistDeleted", "checklistEdited", "checklistItemDeleted", "checklistItemMoved", "checklistItemEdited"], "coeditorMsgs": ["saveContentResponse"], "commentMsgs": ["commentDeleted", "commentEdited", "commentDeleteUndo", "threadedCommentEdited", "sendReactionUpdated", "threadedCommentDeleted", "threadedCommentDeleteUndo"], "docTagsWebsocketMessages": ["documentTagAdded", "documentTagRemoved", "documentTagCreated", "documentTagDeleted", "documentTagUpdated"], "doc_home_key": -5, "docsMsgs": ["takeoverPage", "takeoverPageAck", "documentTagAdded", "documentTagRemoved", "documentTagCreated", "documentTagUpdated", "documentTagDeleted", "pageCreated", "pageDeleted", "pageLinkUpdated", "pageUpdated", "documentArchived"], "exclude_ws_uuid": ["viewingSet", "viewingUnset", "commentingSet"], "fieldMsgs": ["fieldCreated", "fieldUpdated", "fieldDeleted"], "goalMsgs": ["goalCreated", "goalDeleted", "goalUpdated", "goalFolderCreated", "goalFolderUpdated", "goalFolderDeleted", "keyResultCreated", "keyResultUpdated", "keyResultDeleted"], "goals_key": -6, "hist_only": ["taskUpdate", "taskDeleted", "tagRemoved", "tagAdded"], "inbox_key": -3, "include_hide_from_guests": ["fieldValueUpdated", "fieldCreated", "fieldUpdated", "fieldDeleted"], "include_send_only_to_user_no_permissions": ["taskAccessDeleted"], "include_userid": ["categoryMemberRemoved", "projectMemberAdded", "projectMemberEdited", "projectMemberRemoved", "projectOwnerEdited", "teamInvite", "teamInviteAccepted", "subcategoryMemberRemoved", "taskMemberRemoved", "viewMemberRemoved", "subcategoryCopyComplete"], "interactionMsgs": ["viewingViewUnset", "viewingViewSet", "commentingViewSet", "viewingSet", "viewingUnset", "commentingPageSet", "commentingSet", "viewingPageUnset", "viewingPageSet"], "msg_keys": {"category": ["categoryMemberRemoved", "categoryMoved", "categoryEdited", "categoriesCreated", "categoryMemberAdded", "categoryMemberEdited", "categoryOwnerEdited", "categoryComment", "categoryCommentEdited"], "docHomeAndView": ["documentTagAdded", "documentTagRemoved"], "goal": ["goalCreated", "goalDeleted", "goalUpdated", "goalFolderCreated", "goalFolderUpdated", "goalFolderDeleted", "keyResultCreated", "keyResultUpdated", "keyResultDeleted"], "inbox": ["reminderCreated", "reminderEdited", "reminderDeleted"], "page": ["viewingPageUnset", "viewingPageSet", "commentingPageSet", "takeoverPage", "takeoverPageAck", "pageRelationshipCreated", "pageRelationshipDeleted"], "project": ["tasksMoved", "tagUpdated", "projectMemberAdded", "projectMemberEdited", "projectMemberRemoved", "projectOwnerEdited", "subcategoryCopyComplete", "projectComment", "projectCommentEdited"], "project_access": ["projectCreated", "projectEdited", "projectDeleted"], "subcategory": ["subcategoryCreated", "subcategoryDeleted", "subcategoryEdited", "subcategoryMoved", "subcategoryMemberAdded", "subcategoryMemberEdited", "subcategoryOwnerEdited", "subcategoryMemberRemoved", "subcategoryComment", "subcategoryCommentDeleted", "subcategoryCommentEdited", "subcategoryCommentDeleteUndo", "subcategoryAttachment", "subcategoryAttachmentDeleted"], "taskNoPermissions": ["commentingSet", "taskAccessDeleted"], "taskOrPulse": ["viewingSet", "viewingUnset"], "tasks": ["taskMemberRemoved", "taskUpdate", "taskCreated", "taskDeleted", "taskMoved", "fieldValueUpdated", "commentDeleted", "commentEdited", "threadedCommentEdited", "threadedCommentDeleted", "threadedCommentDeleteUndo", "commentDeleteUndo", "tagRemoved", "tagAdded", "taskMerged", "subtaskRemoved", "attachmentDeleted", "dependencyRemoved", "taskMemberAdded", "taskMemberEdited", "taskOwnerEdited", "checklistCreated", "checklistDeleted", "checklistEdited", "checklistItemDeleted", "checklistItemMoved", "checklistItemEdited", "reactionUpdated"], "team": ["automationsThrottled", "billingResolved", "billingError", "tasksArchived", "tasksReordered", "taskPrivacyChange", "subcategoryPrivacyChange", "categoryPrivacyChange", "projectPrivacyChange", "categoriesDeleted", "teamInvite", "teamInviteAccepted", "teamRemoved", "fieldCreated", "fieldUpdated", "fieldDeleted", "userOnline", "timeTrackerCreated", "timeTrackerUpdated", "timeTrackerRemoved", "documentTagDeleted", "documentTagCreated", "documentTagUpdated", "workspaceMaintenanceStarted", "workspaceMaintenanceEnded", "adminV3Updated"], "user": ["inboxMessageToUser", "newNotif", "scratchpadNoteEdited", "scratchpadNoteCreated", "scratchpadNotesDeleted", "saveContentResponse", "exportDone", "tabEdited", "tabsAdded", "tabsRemoved", "trayTabAdded", "trayTabEdited", "trayTabRemoved", "favoriteCreated", "favoriteDeleted", "favoriteEdited", "favoriteMoved", "importComplete", "taskAccessChanged", "subcategoryAccessChanged", "categoryAccessChanged", "projectAccessChanged", "viewAccessChanged", "viewMemberRemoved", "leftProject", "teamRoleChanged", "darkThemeUpdate", "projectMoved", "userEdited", "sessionIdleTimeoutChange", "timeTrackerStarted", "timeTrackerStopped", "userStatusChanged"], "user_ws_key": ["taskCopyComplete", "categoryCopyComplete", "projectCopyComplete", "tasksCopyComplete", "viewsCopyComplete", "templateCreated", "templateUpdated", "templateCreateFailed", "templateUpdateFailed"], "view": ["pageArchived", "viewComment", "viewCommentDeleted", "viewCommentEdited", "viewingViewUnset", "viewingViewSet", "commentingViewSet", "viewCommentDeleteUndo", "pageUpdated", "pageCreated", "pageDeleted", "viewMemberEdited", "pageLinkUpdated"], "viewParent": ["viewCreated", "viewDeleted", "viewUpdated", "documentArchived"]}, "notifMsgs": ["anyTeamNotificationsAvailable", "notificationsAvailable", "newNotif", "inboxMessageToUser"], "pageRelationshipWebsocketMessages": ["pageRelationshipCreated", "pageRelationshipDeleted"], "pageWebsocketMessages": ["pageUpdated", "pageCreated", "pageDeleted"], "privacyChangedMsgs": ["taskPrivacyChange", "subcategoryPrivacyChange", "categoryPrivacyChange", "projectPrivacyChange", "taskAccessDeleted"], "projectMsgs": ["projectMemberAdded", "projectMemberEdited", "projectMemberRemoved", "projectOwnerEdited", "projectCreated", "projectEdited", "projectDeleted"], "pulse_view_key": -4, "pw": "", "queue_monitor": {"check_interval_ms": 25000, "size_limit": 10000}, "queue_processors": 8, "reminderMsgs": ["reminderCreated", "reminderEdited", "reminderDeleted"], "shared_space_key": -2, "skippableV1Messages": {"sendAttachmentDeleted": "attachmentDeleted", "sendAutomationsThrottled": "automationsThrottled", "sendBillingError": "billingError", "sendBillingResolved": "billingResolved", "sendCatComment": "categoryComment", "sendCatCopyComplete": "categoryCopyComplete", "sendCategoriesCreated": "categoriesCreated", "sendCategoriesDeleted": "categoriesDeleted", "sendCategoryCommentEdited": "categoryCommentEdited", "sendCategoryEdited": "categoryEdited", "sendCategoryMoved": "categoryMoved", "sendChecklistCreated": "checklistCreated", "sendChecklistDeleted": "checklistDeleted", "sendChecklistEdited": "checklistEdited", "sendChecklistItemDeleted": "checklistItemDeleted", "sendChecklistItemEdited": "checklistItemEdited", "sendChecklistItemMoved": "checklistItemMoved", "sendCommentDeleteUndo": "commentDeleteUndo", "sendCommentDeleted": "commentDeleted", "sendCommentEdited": "commentEdited", "sendCommentingPageSet": "commentingPageSet", "sendCommentingSet": "commentingSet", "sendCommentingViewSet": "commentingViewSet", "sendDocumentTagAdded": "documentTagAdded", "sendDocumentTagRemoved": "documentTagRemoved", "sendFieldCreated": "fieldCreated", "sendFieldDeleted": "fieldDeleted", "sendFieldUpdated": "fieldUpdated", "sendFieldValueUpdated": "fieldValueUpdated", "sendGoalCreated": "goalCreated", "sendGoalDeleted": "goalDeleted", "sendGoalUpdated": "goalUpdated", "sendKeyResultCreated": "keyResultCreated", "sendKeyResultDeleted": "keyResultDeleted", "sendKeyResultUpdated": "keyResultUpdated", "sendPageArchived": "pageArchived", "sendPageCreated": "pageCreated", "sendPageDeleted": "pageDeleted", "sendPageLinkUpdated": "pageLinkUpdated", "sendPageRelationshipCreated": "pageRelationshipCreated", "sendPageRelationshipDeleted": "pageRelationshipDeleted", "sendPageUpdated": "pageUpdated", "sendProjectComment": "projectComment", "sendProjectCommentEdited": "projectCommentEdited", "sendProjectCopyComplete": "projectCopyComplete", "sendProjectCreated": "projectCreated", "sendProjectDeleted": "projectDeleted", "sendProjectEdited": "projectEdited", "sendProjectMoved": "projectMoved", "sendReactionUpdated": "reactionUpdated", "sendReminderCreated": "reminderCreated", "sendReminderDeleted": "reminderDeleted", "sendReminderEdited": "reminderEdited", "sendRemovedFromTeam": "teamRemoved", "sendSaveContentResponse": "saveContentResponse", "sendSubcatComment": "subcategoryComment", "sendSubcatCommentDeleteUndo": "subcategoryCommentDeleteUndo", "sendSubcatCommentEdited": "subcategoryCommentEdited", "sendSubcatCopyComplete": "subcategoryCopyComplete", "sendSubcategoryAttachment": "subcategoryAttachment", "sendSubcategoryAttachmentDeleted": "subcategoryAttachmentDeleted", "sendSubcategoryCommentDeleted": "subcategoryCommentDeleted", "sendSubcategoryCreated": "subcategoryCreated", "sendSubcategoryDeleted": "subcategoryDeleted", "sendSubcategoryEdited": "subcategoryEdited", "sendSubcategoryMoved": "subcategoryMoved", "sendSubtaskAdded": "subtaskAdded", "sendSubtaskRemoved": "subtaskRemoved", "sendTagAdded": "tagAdded", "sendTagRemoved": "tagRemoved", "sendTagUpdated": "tagUpdated", "sendTaskCopyComplete": "taskCopyComplete", "sendTaskCreated": "taskCreated", "sendTaskDeleted": "taskDeleted", "sendTaskMerged": "taskMerged", "sendTaskMoved": "taskMoved", "sendTaskUpdated": "taskUpdate", "sendTasksArchived": "tasksArchived", "sendTasksMoved": "tasksMoved", "sendTasksReordered": "tasksReordered", "sendTeamInvite": "teamInvite", "sendTeamInviteAccepted": "teamInviteAccepted", "sendThreadedCommentDeleteUndo": "threadedCommentDeleteUndo", "sendThreadedCommentDeleted": "threadedCommentDeleted", "sendThreadedCommentEdited": "threadedCommentEdited", "sendTimeTrackerCreated": "timeTrackerCreated", "sendTimeTrackerRemoved": "timeTrackerRemoved", "sendTimeTrackerStarted": "timeTrackerStarted", "sendTimeTrackerStopped": "timeTrackerStopped", "sendTimeTrackerUpdated": "timeTrackerUpdated", "sendUserOnline": "userOnline", "sendViewComment": "viewComment", "sendViewCommentDeleteUndo": "viewCommentDeleteUndo", "sendViewCommentDeleted": "viewCommentDeleted", "sendViewCommentEdited": "viewCommentEdited", "sendViewCreated": "viewCreated", "sendViewDeleted": "viewDeleted", "sendViewUpdated": "viewUpdated", "sendViewingPageSet": "viewingPageSet", "sendViewingPageUnset": "viewingPageUnset", "sendViewingSetOpts": "viewingSet", "sendViewingUnsetOpts": "viewingUnset", "sendViewingViewSet": "viewingViewSet", "sendViewingViewUnset": "viewingViewUnset", "sendWorkspaceMaintenanceEnded": "workspaceMaintenanceEnded", "sendWorkspaceMaintenanceStarted": "workspaceMaintenanceStarted"}, "subcategoryContentMsgs": ["subcategoryComment", "subcategoryCommentDeleted", "subcategoryCommentEdited", "subcategoryCommentDeleteUndo", "subcategoryAttachment", "subcategoryAttachmentDeleted"], "subcategoryMsgs": ["subcategoryCreated", "subcategoryDeleted", "subcategoryEdited", "subcategoryMoved", "subcategoryMemberAdded", "subcategoryMemberEdited", "subcategoryOwnerEdited", "subcategoryMemberRemoved"], "subscription_monitor": {"activities_outdated_period_ms": 1500000, "host_cleanup_delay_ms": 60000, "host_outdated_period_ms": 240000, "send_to_host_timeout_ms": 30000, "stranded_activities_limit": 1000, "stranded_subs_limit": 50000, "stranded_subscription_ip_limit": 100}, "taskMsgs": ["tasksArchived", "tasksReordered", "tasksMoved", "taskMemberRemoved", "taskUpdate", "taskCreated", "tasksMoved", "taskDeleted", "taskMoved", "tagRemoved", "tagUpdated", "tagAdded", "taskMerged", "subtaskRemoved", "attachmentDeleted", "dependencyRemoved", "taskMemberAdded", "taskMemberEdited", "taskOwnerEdited", "fieldValueUpdated", "reactionUpdated"], "teamMsgs": ["billingResolved", "billingError", "teamInvite", "teamInviteAccepted", "userOnline"], "testing": {"messageWhitelist": ["sendTaskCreated", "sendTaskUpdated", "sendThreadedCommentDeleteUndone", "sendTagAdded", "sendTagRemoved", "sendViewingSetOpts", "sendViewingUnsetOpts"]}, "timeTrackingMsgs": ["timeTrackerCreated", "timeTrackerUpdated", "timeTrackerRemoved", "timeTrackerStarted", "timeTrackerStopped"], "trayMsgs": ["tabEdited", "tabsAdded", "tabsRemoved", "trayTabAdded", "trayTabEdited", "trayTabRemoved"], "unauditedWsMsgs": ["categoryOwnerEdited", "subcategoryOwnerEdited", "subcategoryCommentEdited", "subcategoryCommentDeleteUndo", "subtaskRemoved", "taskOwnerEdited", "projectMemberAdded", "projectMemberEdited", "projectMemberRemoved", "projectOwnerEdited"], "unsafeWSMsgs": ["tasksArchived", "tasksReordered", "categoriesDeleted", "timeTrackerCreated", "timeTrackerUpdated", "timeTrackerRemoved", "subcategoryAccessChanged", "categoryAccessChanged", "tasksMoved", "projectEdited", "categoryMemberRemoved", "categoryMoved", "categoryEdited", "categoriesCreated", "categoryMemberAdded", "subcategoryCreated", "subcategoryDeleted", "subcategoryEdited", "subcategoryMoved", "subcategoryMemberAdded", "subcategoryMemberRemoved", "tasksMoved", "subtaskRemoved", "reminderCreated", "reminderDeleted", "goalCreated", "goalUpdated", "goalFolderCreated", "goalFolderUpdated", "goalFolderDeleted", "keyResultCreated", "keyResultUpdated", "keyResultDeleted"], "userMsgs": ["darkThemeUpdate", "favoriteCreated", "favoriteDeleted", "favoriteEdited", "favoriteMoved", "taskAccessChanged", "subcategoryAccessChanged", "categoryAccessChanged", "projectAccessChanged", "viewAccessChanged", "taskAccessDeleted", "newNotif", "scratchpadNoteEdited", "scratchpadNoteCreated", "scratchpadNotesDeleted", "leftProject", "teamRemoved", "teamRoleChanged", "userEdited", "sessionIdleTimeoutChange", "userEdited", "userStatusChanged"], "viewCommentWebsocketMessages": ["viewComment", "viewCommentDeleted", "viewCommentEdited"], "viewMsgs": ["viewCreated", "viewDeleted", "viewUpdated", "viewCommentDeleteUndo", "viewComment", "viewCommentDeleted", "viewMemberRemoved", "viewMemberEdited", "viewCommentEdited"], "viewWebsocketMessages": ["viewDeleted", "viewCreated", "viewUpdated"], "vitalWebsocketMessages": ["adminV3Updated", "categoryCopyComplete", "exportDone", "importComplete", "projectCopyComplete", "sendCatCopyComplete", "sendExportDone", "sendProjectCopyComplete", "sendSubcatCopyComplete", "sendTaskCopyComplete", "sendTasksCopyComplete", "sendTemplateCreated", "sendTemplateUpdated", "sendTemplateCreateFailed", "sendTemplateUpdateFailed", "sendViewsCopyComplete", "subcategoryCopyComplete", "taskCopyComplete", "tasksCopyComplete", "templateCreated", "templateUpdated", "templateCreateFailed", "templateUpdateFailed", "viewsCopyComplete", "workspaceMaintenanceStarted", "workspaceMaintenanceEnded"]}, "wss": {"authorization_header": ""}, "xr_or_xs_url": "http://localhost:9004", "zendesk": {"auth_key": ""}, "zoom": {"client_id": "", "client_secret": "", "signing_secret": "", "verification_token": ""}}