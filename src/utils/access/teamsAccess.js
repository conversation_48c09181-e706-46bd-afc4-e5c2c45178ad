import config from 'config';
import { ObjectType } from '@time-loop/ovm-object-version';
import {
    BlockedUserException,
    BlockedWorkspaceException,
    getRoleKey,
    hasViewPermissionsEnabled,
    isExternalGuest,
    WorkspaceArchivedException,
    isLimitedMemberEnabled,
} from '@clickup/utils/authorization';
import { GlobalWorkspaceId } from '@clickup-legacy/libs/user/constants';
import { calculateGranularFieldPermissions } from './services/granularFieldPermissionsCalculator';
import { isPermissionDisabledOnWorkspaceLevel } from './constants/workspace-toggleable-permissions';
import { AccessCheckEntitlementsCache } from './services/accessEntitlements';
import { AccessErrorCodes, StatusErrorCodes } from '../errors/constants';
import { assertUserAndWorkspaceNotBlocked } from './services/authorization/teamBlockedService';
import {
    applyWorkspaceRolePermissionOverrides,
    getRoleSubtypePermissionOverrides,
    getWorkspaceRolePermissions,
    getFeatureFlagPermissionsOverrides,
    handleCustomPermissionRules,
    roleCustomPermissionsConversion,
    updateChatCommentPermission,
    maybeAddDevKeyAuthorization,
    applySubTypeOverrides,
    applyEntitlementsLimitations,
} from './helpers/accessHelper';
import * as async from '../asyncHelper';
import * as db from '../db';
import { ClickUpError } from '../errors';
import { checkAndSetWorkspace } from '../workspaceId/context';
import { notFound, insufficientAccess, exception, archivedAccess } from './helpers/authorizationFailure';
import { AccessAction, AccessApiVersion, beginAccessEvent } from '../../libs/access/services/AccessEvents';
import { recordAccessTiming } from '../../libs/access/services/accessEventsMonolithHelpers';
import { getPermissionConfigRepository, getRoleService } from './services/authorization/instances';
import {
    authzVerboseLogging,
    roleSubtype,
    workspaceRolePermissionOverrides,
} from '../../models/integrations/split/squadTreatments/accessManagementTreatments';
import { denyUndelegatedPermissions } from './helpers/delegatedPermissions';
import { checkIsWorkspaceShardMonolith } from '../workspaceId/checks';

const AccessError = ClickUpError.makeNamedError('access');
const logger = ClickUpError.getBasicLogger('teamsAccess');
const additional = { skipLogging: true };
const objectType = ObjectType.WORKSPACE;
const shardingConfig = config.get('sharding');

export function checkAccessTeam(userid, team, options, callback) {
    const event = beginAccessEvent({
        actions: [
            {
                action: AccessAction.Authorize,
                apiVersion: AccessApiVersion.v1,
                objectType,
                userid,
            },
        ],
    });

    maybeAddDevKeyAuthorization(options, userid);

    function cb(...args) {
        if (options?.skipAccessEvent !== true) {
            recordAccessTiming({
                event,
                error: args[0],
            });
        }
        callback(...args);
    }

    if (!team) {
        cb();
        return;
    }

    if (Number(team) !== GlobalWorkspaceId) {
        const routingError = checkIsWorkspaceShardMonolith(shardingConfig);
        if (routingError) {
            cb(routingError);
            return;
        }
    }

    const originator = options?.originator;
    const permissions = [...(options.permissions || [])];
    const checkJoined = options.checkJoined != null ? options.checkJoined : true;
    const check_joined_team_condition = !checkJoined ? `` : ` AND team_members.date_joined IS NOT NULL`;
    const { check_owner } = options;
    const workspaceId = Number(team);
    const objectId = String(team);
    const objectKey = {
        object_type: ObjectType.WORKSPACE,
        object_id: objectId,
        workspace_id: workspaceId,
    };
    const entitlementsCache = new AccessCheckEntitlementsCache();

    const query = `
        SELECT 
            team_members.*, 
            teams.can_add_guests, 
            teams.can_remove_guests,
            teams.charge_for_internal_guests,
            teams.service_status,
            COALESCE(free_trials.plan_id, teams.plan_id) AS plan_id,
            free_trials.plan_id AS trial_plan_id
        FROM
            task_mgmt.teams
            JOIN task_mgmt.team_members ON 
                team_members.team_id = teams.id AND 
                team_members.userid = $1 
                ${check_joined_team_condition}
            LEFT JOIN task_mgmt.free_trials
                ON free_trials.team_id = teams.id
                AND free_trials.trial_end > $3
                AND free_trials.trial_removed = false
                AND free_trials.trial_start = (
                    SELECT max(trial_start)
                    FROM 
                        task_mgmt.free_trials
                    WHERE free_trials.team_id = teams.id
                    AND free_trials.trial_removed = false
                    AND free_trials.trial_end > $3
                )
        WHERE 
            teams.id = $2`;

    const use_master = options.use_master || false;
    const dbMethod = use_master ? db.readQuery : db.tryReplicaFallbackToMaster;

    let accessEntitlements;
    let workspaceRolePermissionsResults;
    async.series(
        [
            async function checkBlockedUserAndWorkspace(series_cb) {
                try {
                    // NOTE: We allow blockedService to be passed in for the case where we are called
                    //       from Nest.js code, in that case we want to use the version that is already resolved
                    //       rather than trying to resolve a new one from NestMonolithInstance.
                    //       This is a bit of a hack until we can delete teamsAccess.js completely in favor of authz lib.
                    await assertUserAndWorkspaceNotBlocked(userid, team, options.blockedService);
                    series_cb();
                } catch (error) {
                    series_cb(error);
                }
            },
            async function entitlementsAndPermissions(series_cb) {
                try {
                    [accessEntitlements, workspaceRolePermissionsResults] = await Promise.all([
                        entitlementsCache.getAccessCheckEntitlements(team),
                        getRoleService().getRolePermissionsForWorkspace(team),
                    ]);
                    series_cb();
                } catch (err) {
                    // The entitlements check will effectively hit the ACCESS_002 error case below
                    // before we can get to it.
                    // For the sake of existing tests and callers we will throw it from here as well.
                    if (err?.ECODE === 'PAYWALL_015') {
                        series_cb(
                            new AccessError(
                                'Team not found',
                                'ACCESS_002',
                                404,
                                notFound({
                                    objectType,
                                    objectId,
                                    workspaceId,
                                    options,
                                    originator,
                                    additional: {
                                        ...additional,
                                        paywall_err: err,
                                    },
                                })
                            )
                        );
                    } else {
                        series_cb(err);
                    }
                }
            },
        ],
        series_err => {
            if (series_err) {
                if (
                    series_err instanceof BlockedUserException ||
                    series_err instanceof BlockedWorkspaceException ||
                    series_err instanceof WorkspaceArchivedException
                ) {
                    cb(series_err);
                    return;
                }

                cb(
                    new AccessError(
                        series_err,
                        AccessErrorCodes.TeamQueryFail,
                        StatusErrorCodes.InternalServer,
                        exception({ objectType, objectId, workspaceId, options, originator })
                    )
                );
                return;
            }

            dbMethod(query, [userid, team, Date.now()], { use_master }, (err, result) => {
                if (err) {
                    cb(
                        new AccessError(
                            err,
                            'ACCESS_001',
                            500,
                            exception({ objectType, objectId, workspaceId, options, originator })
                        )
                    );
                    return;
                }

                if (result.rows.length === 0) {
                    cb(
                        new AccessError(
                            'Team not found',
                            'ACCESS_002',
                            404,
                            notFound({ objectType, objectId, workspaceId, options, team, additional, originator })
                        )
                    );
                    return;
                }

                const {
                    role,
                    custom_role,
                    role_subtype,
                    invite,
                    can_create_views,
                    can_add_guests,
                    can_remove_guests,
                    plan_id,
                    service_status,
                    charge_for_internal_guests,
                } = result.rows[0];

                const permissionConfigRepository =
                    options.permissionConfigRepository ?? getPermissionConfigRepository();
                const rolePermissionMap =
                    permissionConfigRepository?.getWorkspaceRolePermissionsToCommonPermissionsMap() || {};

                let { can_see_time_spent, can_see_time_estimated, can_see_points_estimated, can_edit_tags } =
                    result.rows[0];

                if (service_status === config.service_status.archived) {
                    cb(
                        new AccessError(
                            'Team is archived',
                            AccessErrorCodes.TeamArchived,
                            404,
                            archivedAccess({ objectType, objectId, workspaceId, options, originator })
                        )
                    );
                    return;
                }

                const entitlements = accessEntitlements ?? entitlementsCache.getLegacyAccessCheckEntitlements(plan_id);

                const roleValidationConfig = {
                    roleSubTypeConfig: roleSubtype(workspaceId),
                    chargeForInternalGuests: charge_for_internal_guests ?? false,
                };

                const limitedMemberEnabled = isLimitedMemberEnabled(roleValidationConfig);

                const is_guest = isExternalGuest(role, role_subtype, roleValidationConfig);

                const is_guest_or_limited_member = Number(role) === config.team_roles.guest;

                const rolePermissions = {
                    ...getWorkspaceRolePermissions({
                        role,
                        customRole: custom_role,
                        roleSubtype: role_subtype,
                        limitedMemberEnabled,
                        hasCustomRoleEntitlement: entitlements.customRoles === true,
                        workspaceRolePermissionsResults,
                        permissionConfigRepository,
                    }),
                    ...getFeatureFlagPermissionsOverrides(workspaceId),
                };

                const {
                    role_id: found_role_id,
                    invite_guests,
                    edit_members,
                    create_projects,
                    team_integrations,
                    importing,
                    exporting,
                    manage_tags,
                    manage_custom_fields,
                    views,
                    manage_statuses,
                    delete_items,
                    team_permissions,
                    pin_fields,
                    git,
                    custom_roles,
                    add_email_account,
                    send_email,
                    manage_custom_items,
                    can_convert_item,
                    can_convert_milestone,
                    create_custom_fields,
                    edit_custom_fields,
                    delete_custom_fields,
                    merge_custom_fields,
                    convert_custom_fields,
                    move_custom_fields,
                    upload_attachments,
                    invite_members,
                    can_use_chat,
                    can_view_audit_logs,
                    can_edit_user_groups,
                    can_use_public_api_dev_key,
                    can_send_login_bypass_link,
                    create_private_view,
                } = rolePermissions;

                result.rows[0] = {
                    ...result.rows[0],
                    found_role_id,
                    invite_guests,
                    edit_members,
                    create_projects,
                    team_integrations,
                    importing,
                    exporting,
                    manage_tags,
                    manage_custom_fields,
                    views,
                    manage_statuses,
                    delete_items,
                    team_permissions,
                    pin_fields,
                    git,
                    custom_roles,
                    add_email_account,
                    send_email,
                    manage_custom_items,
                    can_convert_item,
                    can_convert_milestone,
                    create_custom_fields,
                    edit_custom_fields,
                    delete_custom_fields,
                    merge_custom_fields,
                    convert_custom_fields,
                    move_custom_fields,
                    upload_attachments,
                    invite_members,
                    can_use_chat,
                    can_view_audit_logs,
                    can_edit_user_groups,
                    can_use_public_api_dev_key,
                    can_send_login_bypass_link,
                    create_private_view,
                };

                const has_view_permissions = hasViewPermissionsEnabled({
                    role,
                    roleSubtype: role_subtype,
                    limitedMemberEnabled,
                    hasIndividualPermissions: can_create_views === true,
                    hasCustomRoleEntitlement: entitlements.customRoles === true,
                    customRolePermissionValue: views,
                });
                const custom_permissions_to_ignore = [];

                if (!is_guest_or_limited_member) {
                    can_see_time_spent = true;
                    can_see_time_estimated = true;
                    can_see_points_estimated = true;
                    can_edit_tags = true;
                }

                let permission_levels_obj = {};
                const role_obj = config.get(`roles.${role}`);
                const { permission_level } = role_obj;

                if (permission_level) {
                    permission_levels_obj = config.get(`permission_levels.${permission_level}`);
                }

                let role_permissions = {
                    ...permission_levels_obj,
                    ...role_obj,
                    ...getRoleSubtypePermissionOverrides(
                        config,
                        workspaceId,
                        role,
                        role_subtype,
                        charge_for_internal_guests
                    ),
                    ...applyWorkspaceRolePermissionOverrides(workspaceRolePermissionOverrides(workspaceId), role),
                    can_see_time_spent,
                    can_see_time_estimated,
                    can_see_points_estimated,
                    can_edit_tags,
                };

                const invalid_permissions = [];
                let permission_err;

                if (is_guest_or_limited_member) {
                    role_permissions.manage_custom_fields = false;
                    role_permissions.create_view = has_view_permissions;
                    role_permissions.edit_view = has_view_permissions;
                    custom_permissions_to_ignore.push('manage_custom_fields');
                    custom_permissions_to_ignore.push('views');
                }

                if (
                    can_add_guests === 0 &&
                    [config.service_status.active, config.service_status.trial].includes(service_status) &&
                    entitlements.customRoles
                ) {
                    role_permissions[config.permission_constants.can_add_team_guests] = false;
                    custom_permissions_to_ignore.push('invite_guests');
                } else if (
                    can_add_guests === 1 &&
                    role !== config.team_roles.admin &&
                    role !== config.team_roles.owner &&
                    [config.service_status.active, config.service_status.trial].includes(service_status) &&
                    entitlements.customRoles
                ) {
                    role_permissions[config.permission_constants.can_add_team_guests] = false;
                    custom_permissions_to_ignore.push('invite_guests');
                }

                if (
                    can_remove_guests === 1 &&
                    role !== config.team_roles.admin &&
                    role !== config.team_roles.owner &&
                    [config.service_status.active, config.service_status.trial].includes(service_status) &&
                    entitlements.customRoles
                ) {
                    // didnt add to the role permission object in order to reduce some bloat there
                    role_permissions.can_remove_team_guests = false;
                    custom_permissions_to_ignore.push('invite_guests');
                } else if (permissions.includes('can_remove_team_guests')) {
                    role_permissions.can_remove_team_guests = !is_guest;
                    // we still want to check custom permissions in this case so dont add to custom_permissions_to_ignore
                }

                if (!can_edit_tags) {
                    role_permissions[config.permission_constants.add_tags] = false;
                    role_permissions[config.permission_constants.remove_tags] = false;
                }

                /** check if current user is a workspace owner */
                if (check_owner && role !== config.team_roles.owner) {
                    cb(
                        new AccessError(
                            'Only Workspace Owner can change this setting',
                            'ACCESS_038',
                            401,
                            insufficientAccess({ objectType, objectId, workspaceId, options, originator })
                        )
                    );
                    return;
                }

                if (checkJoined && invite === true) {
                    cb(
                        new AccessError(
                            'You must accept the Workspace invitation to do this',
                            'ACCESS_008',
                            401,
                            notFound({ objectType, objectId, workspaceId, options, originator })
                        )
                    );
                    return;
                }

                if (
                    is_guest_or_limited_member &&
                    permissions.includes('create_view') &&
                    !role_permissions.create_view
                ) {
                    if (options.dontFailOnInvalid !== true) {
                        cb(
                            new AccessError(
                                `You don't have permission to do this action`,
                                'ACCESS_434',
                                401,
                                insufficientAccess({ objectType, objectId, workspaceId, options, originator })
                            )
                        );
                        return;
                    }
                }

                if (found_role_id) {
                    const role_custom_permissions = {
                        invite_guests,
                        edit_members,
                        create_projects,
                        team_integrations,
                        importing,
                        exporting,
                        manage_tags,
                        manage_custom_fields,
                        views,
                        manage_statuses,
                        delete_items,
                        team_permissions,
                        pin_fields,
                        git,
                        custom_roles,
                        add_email_account,
                        send_email,
                        manage_custom_items,
                        can_convert_item,
                        can_convert_milestone,
                        create_custom_fields,
                        edit_custom_fields,
                        delete_custom_fields,
                        merge_custom_fields,
                        convert_custom_fields,
                        move_custom_fields,
                        upload_attachments,
                        invite_members,
                        can_use_chat,
                        can_view_audit_logs,
                        can_edit_user_groups,
                        can_use_public_api_dev_key,
                        can_send_login_bypass_link,
                        create_private_view,
                    };

                    Object.keys(rolePermissionMap).forEach(role_perm => {
                        const custom_perm = rolePermissionMap[role_perm];

                        if (custom_permissions_to_ignore.includes(custom_perm)) {
                            return; // already handled this above
                        }

                        const value = role_custom_permissions[custom_perm];

                        if (value != null) {
                            if (value === 1) {
                                role_permissions[role_perm] = true;
                            } else if (value === 0 || isPermissionDisabledOnWorkspaceLevel(role_perm, value)) {
                                role_permissions[role_perm] = false;
                            }
                        }
                    });

                    if (options.team_permissions && team_permissions === 0) {
                        invalid_permissions.push('team_permission');
                    }

                    // TODO: Move the remaining rules for comments and fields below into handleCustomPermissionRules
                    handleCustomPermissionRules(
                        userid,
                        objectKey,
                        has_view_permissions,
                        permissions,
                        delete_items,
                        role_permissions,
                        invalid_permissions,
                        options
                    );

                    // comment permissions
                    const rolesAbleToDeleteOthersComments = [
                        config.get('team_roles.owner'),
                        config.get('team_roles.admin'),
                    ];

                    // delete comment
                    // cant delete other peoples comments with full delete permission
                    if (
                        permissions.includes(config.permission_constants.can_delete_comments) &&
                        rolesAbleToDeleteOthersComments.includes(Number(role)) === false &&
                        Number(options?.commenter) !== Number(userid) &&
                        delete_items === 1
                    ) {
                        role_permissions[config.permission_constants.can_delete_comments] = false;
                    }

                    // check if owner of custom field
                    if (
                        permissions.indexOf(config.permission_constants.delete_custom_fields) > -1 &&
                        permissions.indexOf(config.permission_constants.delete) > -1 &&
                        Number(userid) === Number(options.field_owner) &&
                        delete_items === 3
                    ) {
                        role_permissions[config.permission_constants.delete] = true;
                    }
                } else if (found_role_id && (invite_guests != null || invite_members != null)) {
                    roleCustomPermissionsConversion(role_permissions, {
                        invite_guests,
                        invite_members,
                    });
                }

                Object.assign(
                    role_permissions,
                    calculateGranularFieldPermissions(
                        team,
                        entitlements.granularFieldPermissions,
                        role_permissions.manage_custom_fields,
                        role_permissions
                    )
                );

                const subTypePermissionOverrides = getRoleSubtypePermissionOverrides(
                    config,
                    workspaceId,
                    role,
                    role_subtype,
                    charge_for_internal_guests
                );

                applySubTypeOverrides(team, role_permissions, subTypePermissionOverrides);

                updateChatCommentPermission({
                    viewType: options.view_type,
                    workspaceId,
                    permissions: role_permissions,
                    config,
                });

                applyEntitlementsLimitations(role_permissions, entitlements);
                role_permissions = denyUndelegatedPermissions(role_permissions);
                if (options.dontFailOnInvalid !== true) {
                    permissions.forEach(permission => {
                        //
                        // NOTE: Checking strict equals false means we don't fail
                        //       if `role_permissions[permission]` is undefined.
                        //       This means we allow checking for hierarchy permissions like `can_read`.
                        //       If we change this behavior we need to update the authz lib wrapper
                        //       in teamsAccessAuthz.ts as well.
                        if (role_permissions[permission] === false) {
                            invalid_permissions.push(permission);
                        } else {
                            // integer role permission, check owner, doesn't apply in checking access to team
                        }
                    });

                    if (invalid_permissions.length) {
                        permission_err = new AccessError(
                            'You do not have permission to do this action',
                            'ACCESS_009',
                            401,
                            insufficientAccess({
                                objectType,
                                objectId,
                                workspaceId,
                                options,
                                originator,
                                additional: { invalid_permissions },
                            })
                        );
                    }
                }

                if (invalid_permissions.length && options.dontFailOnInvalid === true) {
                    for (const invalid_permission of invalid_permissions) {
                        role_permissions[invalid_permission] =
                            typeof role_permissions[invalid_permission] === 'number' ? 0 : false;
                    }
                }

                result.rows[0].permissions = role_permissions;
                result.rows[0].role = role;
                result.rows[0].role_subtype = role_subtype;
                result.rows[0].role_key = getRoleKey(role, role_subtype);

                if (permission_err && options.dontFailOnInvalid === true) {
                    result.rows[0].permission_err = permission_err;
                }

                if (authzVerboseLogging(userid)) {
                    logger.info({
                        userid,
                        log_tags: ['access', 'workspace'],
                        options,
                        permissions: result.rows[0],
                        permission_err,
                        object_ids: [team],
                        workspace_id: team,
                    });
                }

                checkAndSetWorkspace(team, 'access.teamsAccess')
                    .then(() => cb(options.dontFail ? null : permission_err, result.rows[0]))
                    .catch(error => cb(error));
            });
        }
    );
}

export function promiseAccessTeam(userid, team, options = {}) {
    return new Promise((res, rej) => {
        checkAccessTeam(userid, team, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}

export function checkAccessTeams(userid, teams, options, cb) {
    async.map(
        teams,
        (team_id, map_cb) => checkAccessTeam(userid, team_id, options, map_cb),
        (err, result) => cb(err, result)
    );
}

export function promiseAccessTeams(userid, teams, options = {}) {
    return new Promise((res, rej) => {
        checkAccessTeams(userid, teams, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}
