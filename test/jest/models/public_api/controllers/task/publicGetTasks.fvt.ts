import { RestClient } from '@clickup/rest-client';
import ClickUpContext from '../../../../../../src/jest-context/ClickUpContext';
import { getPublicTeamTasks, publicApiGetTask } from '../../../../../sdk/task';
import { buildTestScenario } from '../../../../../../src/models/field/factories/__tests__/field-parent-filter-test-utils';
import { changeSplitTestTreatments, restoreSplitTest } from '../../../../../sdk/split';
import { FeatureFlag } from '../../../../../../src/models/integrations/split/feature-flag.enum';

/**
 * @group functional/models/public_api
 */

let context: ClickUpContext;
let client: RestClient;

describe('public api tasks functional tests', () => {
    describe.each([true, false])('when database query update is %s', (useOptimizedQuery: boolean) => {
        jest.setTimeout(60_000);

        let teamId: string;
        let listId: string;
        let targetListId: string;
        let timlListId: string;
        let userId: number;
        let featureFlags: { featureName: string; value: 'on' | 'off'; config?: Record<string, any> }[];

        beforeAll(async () => {
            context = new ClickUpContext();
            teamId = (
                await context.provideTeam({ name: `publicGetTasks.fvt.${useOptimizedQuery ? 'optimized' : 'legacy'}` })
            ).id;
            const user = await context.provideUser({ prefix: 'some-api-user' });
            userId = user.id;
            client = user.client;
            await context.teamAddUser(teamId, userId, 2);

            const { id: projectId } = await context.provideProject({ name: 'Project A', teamId }, { client });
            const { id: categoryId } = await context.provideCategory({ name: 'Category A', projectId }, { client });

            [listId, targetListId, timlListId] = (
                await Promise.all([
                    context.provideSubcategory({ categoryId, name: 'List 1' }, { client }),
                    context.provideSubcategory({ categoryId, name: 'List for relationship' }, { client }),
                    context.provideSubcategory({ categoryId, name: 'List for TIML' }, { client }),
                ])
            ).map(resp => resp.id);

            featureFlags = [
                {
                    featureName: FeatureFlag.CustomFieldFiltersCteUpdate,
                    value: useOptimizedQuery ? 'on' : 'off',
                    config: { enabled: useOptimizedQuery },
                },
                {
                    featureName: FeatureFlag.RefactorTaskEgressQueries,
                    value: useOptimizedQuery ? 'on' : 'off',
                    config: { get_item_ids: useOptimizedQuery },
                },
            ];

            await changeSplitTestTreatments(featureFlags);
        });

        afterAll(() => restoreSplitTest());

        describe('getTasks', () => {
            beforeAll(async () => {
                await changeSplitTestTreatments([
                    ...featureFlags,
                    {
                        featureName: 'list-relationships-filtering',
                        value: 'on',
                    },
                ]);
            });

            describe('parameter validation', () => {
                const TEAM_ID = '777';
                const USER_777777 = 777777;

                const invalidParams = [
                    {
                        field: 'space_ids',
                        invalidValue: '123',
                    },
                    {
                        field: 'list_ids',
                        invalidValue: '458121',
                    },
                    {
                        field: 'statuses',
                        invalidValue: 'to do',
                    },
                    {
                        field: 'assignees',
                        invalidValue: '5678',
                    },
                    {
                        field: 'tags',
                        invalidValue: 'this tag',
                    },
                    {
                        field: 'custom_items',
                        invalidValue: 0,
                    },
                    {
                        field: 'category_ids',
                        invalidValue: 123,
                    },
                ];

                test.each(invalidParams)(
                    'should only accept a request if the $field parameter is an array',
                    async ({ field, invalidValue }) => {
                        const resp1 = await getPublicTeamTasks(USER_777777, TEAM_ID, { [field]: [invalidValue] });
                        expect(resp1.status).toEqual(200);
                        await expect(
                            getPublicTeamTasks(USER_777777, TEAM_ID, { [field]: invalidValue })
                        ).rejects.toThrow(/Request failed with status code 400/);
                    }
                );

                test.each([
                    {
                        description: 'validates list_ids',
                        params: { list_ids: ['123', '456foo'] },
                    },
                    {
                        description: 'validates category_ids',
                        params: { list_ids: ['123', '456foo'] },
                    },
                    {
                        description: 'validates category_ids with null',
                        params: { list_ids: [null] },
                    },
                    {
                        description: 'validates category_ids with undefined',
                        params: { list_ids: [null] },
                    },
                    {
                        description: 'validates project_ids with undefined',
                        params: { list_ids: ['123', 'undefined456'] },
                    },
                    {
                        description: 'validates custom field with null',
                        params: { custom_fields: [{ field_id: 'xxx', operator: '=', value: null }] },
                    },
                    {
                        description: 'validates custom field with undefined',
                        params: { custom_fields: [{ field_id: 'xxx', operator: '!=', value: undefined }] },
                    },
                    {
                        description: 'validates date_updated_gt',
                        params: { date_updated_gt: '2025-04-09' },
                    },
                ])('$description', async ({ params }) => {
                    await expect(getPublicTeamTasks(USER_777777, TEAM_ID, params)).rejects.toThrow(
                        /Request failed with status code 400/
                    );
                });

                test.each([
                    {
                        description: 'custom field with RANGE filter should fail if no 2 values are provided',
                        params: { custom_fields: [{ field_id: 'xxx', operator: 'RANGE', value: 1 }] },
                    },
                    {
                        description: 'custom field with RANGE filter should fail if no number is provided',
                        params: { custom_fields: [{ field_id: 'xxx', operator: 'RANGE', value: ['abc', 2] }] },
                    },
                    {
                        description: 'custom field with RANGE filter should fail if value is undefined',
                        params: { custom_fields: [{ field_id: 'xxx', operator: 'RANGE', value: undefined }] },
                    },
                    {
                        description: 'custom field with RANGE filter should fail if value is null',
                        params: { custom_fields: [{ field_id: 'xxx', operator: 'RANGE', value: null }] },
                    },
                ])('$description', async ({ params }) => {
                    await expect(getPublicTeamTasks(USER_777777, TEAM_ID, params)).rejects.toThrow(
                        /Request failed with status code 400/
                    );
                });
            });

            test('should get tasks with last_page boolean', async () => {
                const listToCreateATaskIn = await context.provideSubcategory(
                    {
                        name: 'List to create a task in',
                    },
                    { client }
                );

                let resp = await context.publicApiGetTasks(
                    {
                        listId: listToCreateATaskIn.id,
                    },
                    { client }
                );
                expect(resp).toBeDefined();
                expect(resp.last_page).toBeTruthy();
                expect(resp.tasks).toHaveLength(0);

                await context.provideTask(
                    {
                        name: 'Created task',
                        subcategoryId: listToCreateATaskIn.id,
                    },
                    { client }
                );

                resp = await context.publicApiGetTasks(
                    {
                        listId: listToCreateATaskIn.id,
                    },
                    { client }
                );
                expect(resp.last_page).toBeTruthy();
                expect(resp.tasks).toHaveLength(1);
                expect(resp.tasks[0].watchers).toHaveLength(1);
                expect(resp.tasks[0].attachments).toBeUndefined();
            });

            test('should get timl tasks', async () => {
                const baselistId = (await context.provideSubcategory({ name: 'List 3' }, { client })).id;
                const { id: taskId } = await context.provideTask({ subcategoryId: baselistId });
                await context.assignTIML({ task_ids: [taskId], subcategoryId: timlListId });
                let resp;
                resp = await context.publicApiGetTasks({
                    listId: timlListId,
                    params: { include_timl: 'false' },
                });

                expect(resp.tasks).toHaveLength(0);

                resp = await context.publicApiGetTasks({
                    listId: timlListId,
                    params: { include_timl: 'true' },
                });

                expect(resp.tasks).toHaveLength(1);
            });

            describe('given tasks with custom item types', () => {
                let bugTypeId: number;
                beforeAll(async () => {
                    bugTypeId = (await context.provideCustomItem({ team_id: teamId, name: 'bug' })).id;
                });

                test('filtering by invalid custom items should fail', async () => {
                    try {
                        await context.publicApiGetTasks(
                            { listId, params: { 'custom_items[]': ['not-a-number'] } },
                            { client }
                        );
                    } catch (e: any) {
                        expect(e.name).toContain('RestResponseError');
                    }
                });

                test('filtering by custom item type', async () => {
                    const task = await context.provideTask(
                        {
                            name: 'Task',
                            subcategoryId: listId,
                        },
                        { client }
                    );
                    const milestone = await context.provideTask(
                        {
                            name: 'Milestone',
                            subcategoryId: listId,
                            custom_type: 1,
                        },
                        { client }
                    );
                    const bug = await context.provideTask(
                        {
                            name: 'Bug',
                            subcategoryId: listId,
                            custom_type: bugTypeId,
                        },
                        { client }
                    );

                    let resp: any;
                    resp = await context.publicApiGetTasks({ listId, params: { 'custom_items[]': [0] } }, { client });
                    expect(resp).toBeDefined();
                    expect(resp.tasks).toHaveLength(1);

                    resp = await context.publicApiGetTasks({ listId, params: { 'custom_items[]': [1] } }, { client });
                    expect(resp).toBeDefined();
                    expect(resp.tasks).toHaveLength(1);

                    resp = await context.publicApiGetTasks(
                        { listId, params: { 'custom_items[]': [bugTypeId] } },
                        { client }
                    );
                    expect(resp).toBeDefined();
                    expect(resp.tasks).toHaveLength(1);

                    resp = await context.publicApiGetTasks(
                        { listId, params: { 'custom_items[]': [0, bugTypeId] } },
                        { client }
                    );
                    expect(resp).toBeDefined();
                    expect(resp.tasks).toHaveLength(2);

                    resp = await context.publicApiGetTasks(
                        { listId, params: { 'custom_items[]': [1, bugTypeId] } },
                        { client }
                    );
                    expect(resp).toBeDefined();
                    expect(resp.tasks).toHaveLength(2);

                    // cleanup
                    await Promise.all([
                        context.taskDelete({ taskId: task.id }),
                        context.taskDelete({ taskId: milestone.id }),
                        context.taskDelete({ taskId: bug.id }),
                    ]);
                });
            });

            describe('should respect subtasks param', () => {
                let taskWithSubtasks;
                let subtasks;
                let subcatId: string;
                beforeAll(async () => {
                    const subcat = await context.provideSubcategory({ name: 'Subtasks test' });
                    subcatId = subcat.id;
                    taskWithSubtasks = await context.provideTask({
                        name: 'Task with subtasks',
                        subcategoryId: subcatId,
                    });
                    subtasks = await Promise.all([
                        context.provideTask({
                            name: 'Subtask 1',
                            parent: taskWithSubtasks.id,
                            subcategoryId: subcatId,
                        }),
                        context.provideTask({
                            name: 'Subtask 2',
                            parent: taskWithSubtasks.id,
                            subcategoryId: subcatId,
                        }),
                    ]);
                });
                it('should return subtasks when subtasks param is true', async () => {
                    const resp = await context.publicApiGetTasks(
                        { listId: subcatId, params: { subtasks: true } },
                        { client }
                    );
                    expect(resp.tasks).toHaveLength(3);
                    expect(resp.tasks.map(t => t.id)).toEqual(
                        expect.arrayContaining([taskWithSubtasks.id, ...subtasks.map(s => s.id)])
                    );
                });
                it('should not return subtasks when subtasks param is not provided', async () => {
                    const resp = await context.publicApiGetTasks({ listId: subcatId }, { client });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(taskWithSubtasks.id);
                });
                it('should not return subtasks when subtasks param is false', async () => {
                    const resp = await context.publicApiGetTasks(
                        { listId: subcatId, params: { subtasks: false } },
                        { client }
                    );
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(taskWithSubtasks.id);
                });
                it('should not return subtasks when subtasks param is not a boolean', async () => {
                    const resp = await context.publicApiGetTasks(
                        { listId: subcatId, params: { subtasks: 'whynot' } },
                        { client }
                    );
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(taskWithSubtasks.id);
                });
            });
        });

        describe('given tasks with different custom fields', () => {
            let shortText: any;
            let numberField: any;
            let checkbox: any;
            let label: any;
            let dropdown: any;
            let newDropdown: any;
            let taskField: any;
            let listRelationshipField: any;
            let userField: any;
            let tasks: any[] = [];
            let targetTasks: any[] = [];
            beforeAll(async () => {
                shortText = await context.provideCustomField({
                    type: 'short_text',
                    subcategory_id: listId,
                });
                numberField = await context.provideCustomField({
                    type: 'number',
                    subcategory_id: listId,
                });
                checkbox = await context.provideCustomField({
                    type: 'checkbox',
                    subcategory_id: listId,
                });
                label = await context.provideCustomField({
                    name: 'Label',
                    type: 'labels',
                    type_config: {
                        options: [
                            { label: '1', color: null },
                            { label: '2', color: null },
                        ],
                    },
                    subcategory_id: listId,
                });
                dropdown = await context.provideCustomField({
                    name: 'Dropdown',
                    type: 'drop_down',
                    subcategory_id: listId,
                    type_config: {
                        options: [
                            { name: 'Option 1', color: null },
                            { name: 'Option 2', color: null },
                        ],
                    },
                });
                newDropdown = await context.provideCustomField({
                    name: 'New Dropdown',
                    type: 'drop_down',
                    subcategory_id: listId,
                    type_config: {
                        new_drop_down: true,
                        options: [
                            { name: 'Option 1', color: null },
                            { name: 'Option 2', color: null },
                        ],
                    },
                });

                taskField = await context.provideCustomField({
                    type: 'tasks',
                    subcategory_id: listId,
                });

                userField = await context.provideCustomField({
                    type: 'users',
                    subcategory_id: listId,
                    type_config: {
                        single_user: false,
                        include_groups: true,
                    },
                });

                listRelationshipField = await context.provideCustomField({
                    type: 'list_relationship',
                    subcategory_id: listId,
                    type_config: {
                        subcategory_id: targetListId,
                    },
                });

                tasks = await Promise.all([
                    context.provideTask({ name: 'Task A', subcategoryId: listId }),
                    context.provideTask({ name: 'Task B', subcategoryId: listId }),
                    context.provideTask({ name: 'Task C', subcategoryId: listId }),
                    context.provideTask({ name: 'Task D', subcategoryId: listId }),
                    context.provideTask({ name: 'Task E', subcategoryId: listId }), // idx: 4
                    context.provideTask({ name: 'Task F', subcategoryId: listId }),
                    context.provideTask({ name: 'Task G', subcategoryId: listId }),
                    context.provideTask({ name: 'Task H', subcategoryId: listId }),
                    context.provideTask({ name: 'Task I', subcategoryId: listId }), // idx: 8
                    context.provideTask({ name: 'Task J', subcategoryId: listId }),
                ]);

                targetTasks = await Promise.all([
                    context.provideTask({ name: 'Task T1', subcategoryId: targetListId }),
                    context.provideTask({ name: 'Task T2', subcategoryId: targetListId }),
                    context.provideTask({ name: 'Task T3', subcategoryId: targetListId }),
                ]);

                await Promise.all([
                    context.provideTaskCustomFields({
                        task_id: tasks[0].id,
                        cf_id: shortText.id,
                        value: 'example test text',
                    }),
                    context.provideTaskCustomFields({
                        task_id: tasks[1].id,
                        cf_id: numberField.id,
                        value: 10,
                    }),
                    context.provideTaskCustomFields({
                        task_id: tasks[2].id,
                        cf_id: label.id,
                        value: [label.type_config.options[0].id],
                    }),
                    context.provideTaskCustomFields({
                        task_id: tasks[3].id,
                        cf_id: dropdown.id,
                        value: dropdown.type_config.options[0].id,
                    }),
                    context.provideTaskCustomFields({
                        task_id: tasks[4].id,
                        cf_id: newDropdown.id,
                        value: newDropdown.type_config.options[0].id,
                    }),
                    context.provideTaskCustomFields({
                        task_id: tasks[5].id,
                        cf_id: checkbox.id,
                        value: true,
                    }),
                    context.provideTaskCustomFields({
                        task_id: tasks[6].id,
                        cf_id: taskField.id,
                        value: { add: [tasks[0].id] },
                    }),
                    context.provideTaskCustomFields({
                        task_id: tasks[7].id,
                        cf_id: userField.id,
                        value: { add: [userId] },
                    }),
                    context.provideTaskCustomFields({
                        task_id: tasks[8].id,
                        cf_id: listRelationshipField.id,
                        value: { add: [targetTasks[0].id] },
                    }),
                    context.provideTaskCustomFields({
                        task_id: tasks[9].id,
                        cf_id: listRelationshipField.id,
                        value: { add: [targetTasks[0].id, targetTasks[1].id] },
                    }),
                ]);

                await changeSplitTestTreatments([
                    ...featureFlags,
                    {
                        featureName: 'list-relationships-filtering',
                        value: 'on',
                    },
                ]);
            });

            describe('when filtering is more complex', () => {
                test('returns all tasks when no custom field filter is present', async () => {
                    const resp = await context.publicApiGetTasks({ listId });
                    expect(resp.tasks).toHaveLength(tasks.length);
                });

                test('filters by short text', async () => {
                    let resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: shortText.id,
                                operator: '=',
                                value: 'test',
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[0].id);

                    // comparison uses LIKE
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: shortText.id,
                                operator: '=',
                                value: 'es',
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[0].id);

                    // using ANY
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: shortText.id,
                                operator: 'ANY',
                                value: ['quiz', 'test'],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[0].id);

                    // using NOT ANY with match
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: shortText.id,
                                operator: 'NOT ANY',
                                value: ['quiz', 'test'],
                            })}]`,
                        },
                    });
                    expect(resp.tasks.map(t => t.id)).not.toContainEqual(tasks[0].id);

                    // using ALL with match
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: shortText.id,
                                operator: 'ALL',
                                value: ['text', 'test'],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[0].id);

                    // using ALL with no match
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: shortText.id,
                                operator: 'ALL',
                                value: ['quiz', 'text', 'test'],
                            })}]`,
                        },
                    });
                    expect(resp.tasks.map(t => t.id)).not.toContainEqual(tasks[0].id);

                    // using NOT ALL with no match
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: shortText.id,
                                operator: 'NOT ALL',
                                value: ['quiz', 'text', 'test'],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[0].id);

                    // using NOT ALL with match
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: shortText.id,
                                operator: 'NOT ALL',
                                value: ['text', 'test'],
                            })}]`,
                        },
                    });
                    expect(resp.tasks.map(t => t.id)).not.toContainEqual(tasks[0].id);

                    // not matching task
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: shortText.id,
                                operator: '=',
                                value: 'hello',
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(0);

                    // returns everything without a short text
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: shortText.id, operator: 'IS NULL' })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(tasks.length - 1);

                    // returns only tasks with a short text
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: shortText.id, operator: 'IS NOT NULL' })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[0].id);
                });

                test('filters by number', async () => {
                    let resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: numberField.id,
                                operator: '=',
                                value: 10,
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[1].id);

                    // not matching task
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: numberField.id,
                                operator: '!=',
                                value: 10,
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(0);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: numberField.id,
                                operator: '<',
                                value: 10,
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(0);

                    // everything without a number
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: numberField.id, operator: 'IS NULL' })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(tasks.length - 1);

                    // everything with a number
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: numberField.id, operator: 'IS NOT NULL' })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[1].id);
                });

                test('filters by checkbox', async () => {
                    let resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: checkbox.id, operator: '=', value: true })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[5].id);

                    // comparison uses LIKE
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: checkbox.id,
                                operator: '=',
                                value: false,
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(0);

                    // returns everything without a short text
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: checkbox.id, operator: 'IS NULL' })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(tasks.length - 1);

                    // returns only tasks with a short text
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: checkbox.id, operator: 'IS NOT NULL' })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[5].id);
                });

                test('filters by label', async () => {
                    let resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: label.id,
                                operator: 'ALL',
                                value: [label.type_config.options[0].id],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[2].id);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: label.id,
                                operator: 'ALL',
                                value: [label.type_config.options[0].id, label.type_config.options[1].id],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(0);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: label.id,
                                operator: 'NOT ALL',
                                value: [label.type_config.options[0].id],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(tasks.length - 1);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: label.id,
                                operator: 'ANY',
                                value: [label.type_config.options[0].id, label.type_config.options[1].id],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[2].id);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: label.id,
                                operator: 'NOT ANY',
                                value: [label.type_config.options[0].id, label.type_config.options[1].id],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(tasks.length - 1);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: { custom_fields: `[${JSON.stringify({ field_id: label.id, operator: 'IS NULL' })}]` },
                    });
                    expect(resp.tasks).toHaveLength(tasks.length - 1);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: label.id, operator: 'IS NOT NULL' })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[2].id);
                });

                test('filters by dropdown', async () => {
                    let resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: dropdown.id,
                                operator: '=',
                                value: dropdown.type_config.options[0].id,
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[3].id);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: dropdown.id,
                                operator: '!=',
                                value: dropdown.type_config.options[0].id,
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(0);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: dropdown.id, operator: 'IS NULL' })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(tasks.length - 1);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: dropdown.id, operator: 'IS NOT NULL' })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[3].id);
                });

                test('filters by new dropdown', async () => {
                    let resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: newDropdown.id,
                                operator: 'ALL',
                                value: [newDropdown.type_config.options[0].id],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[4].id);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: newDropdown.id,
                                operator: 'ALL',
                                value: [newDropdown.type_config.options[0].id, newDropdown.type_config.options[1].id],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(0);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: newDropdown.id,
                                operator: 'ANY',
                                value: [newDropdown.type_config.options[0].id, newDropdown.type_config.options[1].id],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[4].id);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: newDropdown.id,
                                operator: 'NOT ANY',
                                value: [newDropdown.type_config.options[0].id, newDropdown.type_config.options[1].id],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(tasks.length - 1);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: newDropdown.id, operator: 'IS NULL' })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(tasks.length - 1);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: newDropdown.id, operator: 'IS NOT NULL' })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[4].id);
                });

                describe('filters by task field', () => {
                    const testCases = [
                        // operator, taskIndex, expectedLength, expectedTaskIndex
                        ['ANY', 0, 1, 6],
                        ['ANY', 1, 0, null],
                        ['ANY', '0,1', 1, 6],
                        ['NOT ANY', 0, 'tasks.length - 1', null],
                        ['ALL', 0, 1, 6],
                        ['ALL', '0,1', 0, null],
                        ['IS NULL', null, 'tasks.length - 1', null],
                        ['IS NOT NULL', null, 1, 6],
                    ];

                    test.each(testCases)(
                        '%s with task %s',
                        async (operator, taskIndex, expectedLength, expectedTaskIndex) => {
                            let value;
                            if (taskIndex === '0,1') {
                                value = [tasks[0].id, tasks[1].id];
                            } else if (taskIndex !== null) {
                                value = [tasks[taskIndex].id];
                            }

                            const customFields = JSON.stringify({
                                field_id: taskField.id,
                                operator,
                                ...(value !== null && { value }),
                            });

                            const resp = await context.publicApiGetTasks({
                                listId,
                                params: { custom_fields: `[${customFields}]` },
                            });

                            const actualLength = typeof expectedLength === 'string' ? tasks.length - 1 : expectedLength;

                            expect(resp.tasks).toHaveLength(actualLength);

                            if (expectedTaskIndex !== null) {
                                expect(resp.tasks[0].id).toEqual(tasks[expectedTaskIndex].id);
                            }
                        }
                    );
                });

                test('filters by user field', async () => {
                    let resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: userField.id,
                                operator: 'ANY',
                                value: [userId],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[7].id);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: userField.id,
                                operator: 'ANY',
                                value: [123],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(0);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: userField.id,
                                operator: 'ANY',
                                value: [userId, 123],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[7].id);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: userField.id,
                                operator: 'NOT ANY',
                                value: [userId],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(tasks.length - 1);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: userField.id,
                                operator: 'ALL',
                                value: [userId],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[7].id);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({
                                field_id: userField.id,
                                operator: 'ALL',
                                value: [userId, 123],
                            })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(0);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: userField.id, operator: 'IS NULL' })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(tasks.length - 1);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: `[${JSON.stringify({ field_id: userField.id, operator: 'IS NOT NULL' })}]`,
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[7].id);
                });

                describe('filters by list relationship field', () => {
                    const testCases: [string, number[], number, number[]][] = [
                        // operator, targetTaskIndices, expectedLength, taskIndices
                        ['ANY', [0], 2, [8, 9]],
                        ['ANY', [1], 1, [9]],
                        ['ANY', [0, 1], 2, [8, 9]],
                        ['NOT ANY', [0], null, null],
                        ['ALL', [0], 2, [8, 9]],
                        ['ALL', [0, 1], 1, [9]],
                        ['IS NULL', null, null, null],
                        ['IS NOT NULL', null, 2, [8, 9]],
                    ];

                    test.each(testCases)(
                        '%s with target %s',
                        async (operator, targetIndices, expectedLength, taskIndices) => {
                            let value: string[];
                            if (targetIndices !== null) {
                                value = targetIndices.map(i => targetTasks[i].id);
                            }

                            const customFields = JSON.stringify({
                                field_id: listRelationshipField.id,
                                operator,
                                ...(value !== null && { value }),
                            });

                            const resp = await context.publicApiGetTasks({
                                listId,
                                params: { custom_fields: `[${customFields}]` },
                            });

                            const actualLength =
                                operator === 'NOT ANY' || operator === 'IS NULL' ? tasks.length - 2 : expectedLength;

                            expect(resp.tasks).toHaveLength(actualLength);
                            if (taskIndices) {
                                const expectedIds = taskIndices.map(i => tasks[i].id);
                                const taskIds = resp.tasks.map(task => task.id);
                                expect(taskIds).toEqual(expect.arrayContaining(expectedIds));
                                expect(taskIds).toHaveLength(expectedIds.length);
                            }
                        }
                    );
                });
            });

            describe('when filtering by possible sqli queries', () => {
                // this is in response to inc-940
                test('range filter works', async () => {
                    let resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: JSON.stringify([
                                { field_id: numberField.id, operator: 'RANGE', value: [5, 15] },
                            ]),
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[1].id);

                    // Ensure backwards compatibility with numbers as string
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: JSON.stringify([
                                { field_id: numberField.id, operator: 'RANGE', value: ['5', '15'] },
                            ]),
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[1].id);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: JSON.stringify([
                                {
                                    field_id: numberField.id,
                                    operator: 'RANGE',
                                    value: { low_value: '5', high_value: '15' },
                                },
                            ]),
                        },
                    });
                    expect(resp.tasks).toHaveLength(1);
                    expect(resp.tasks[0].id).toEqual(tasks[1].id);

                    // No results for out of range search
                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: JSON.stringify([
                                { field_id: numberField.id, operator: 'RANGE', value: [5, 9] },
                            ]),
                        },
                    });
                    expect(resp.tasks).toHaveLength(0);

                    resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: JSON.stringify([
                                { field_id: numberField.id, operator: 'RANGE', value: [11, 15] },
                            ]),
                        },
                    });
                    expect(resp.tasks).toHaveLength(0);
                });

                test('handles filtering short text with a range operator with string values', async () => {
                    await expect(
                        context.publicApiGetTasks({
                            listId,
                            params: {
                                custom_fields: JSON.stringify([
                                    { field_id: shortText.id, operator: 'RANGE', value: ['rmrabc', 'RMRACE'] },
                                ]),
                            },
                        })
                    ).rejects.toThrow(/RANGE filter values must be numbers/);

                    const resp = await context.publicApiGetTasks({
                        listId,
                        params: {
                            custom_fields: JSON.stringify([
                                { field_id: shortText.id, operator: 'RANGE', value: [1, 2] },
                            ]),
                        },
                    });
                    expect(resp.tasks).toHaveLength(0);
                });
            });
        });

        describe('getTask', () => {
            test('should get task with assignees and group_assignees', async () => {
                const userId1 = 777777;
                await context.teamAddUser(teamId, userId1, 3);

                const groupId = await context.provideGroup({ teamId, name: 'Test Group' }, { client });
                const task = await context.provideTask({ name: 'Task Name' }, { client });

                let publicTask = (await publicApiGetTask(userId1, task.id)).data;
                expect(publicTask.assignees).toEqual([]);
                expect(publicTask.group_assignees).toEqual([]);

                await context.provideTaskAssignee({ taskId: task.id, assignees: [userId, userId1] });
                await context.provideTaskAssignee({ taskId: task.id, assignees: [groupId] }, true);

                publicTask = (await publicApiGetTask(userId1, task.id)).data;
                expect(publicTask.assignees).toHaveLength(2);
                expect(publicTask.assignees).toContainEqual(
                    expect.objectContaining({
                        id: userId,
                        email: expect.any(String),
                    })
                );
                expect(publicTask.assignees).toContainEqual(
                    expect.objectContaining({
                        id: userId1,
                        email: expect.any(String),
                        username: expect.any(String),
                    })
                );
                expect(publicTask.group_assignees).toHaveLength(1);
                expect(publicTask.group_assignees[0]).toMatchObject({
                    id: groupId,
                    name: 'Test Group',
                });
            });
        });

        describe('custom fields by custom task type', () => {
            let subcategoryId;
            const userId2 = 777777;
            let taskType1;
            let taskType2;
            let taskType1CF;
            let taskType1And2CF;
            let globalCF;
            let valuesForAllCFs;

            let task1Type1;
            let task2Type2;
            let task3NoType;

            beforeAll(async () => {
                context = new ClickUpContext();
                const { id: team_id } = await context.provideTeam({ name: 'publicGetTasks.fvt-cfs-by-task-type' });
                const user = await context.provideUser({ prefix: 'some-new-api-user' });
                userId = user.id;
                client = user.client;
                await context.teamAddUser(team_id, userId, 2);
                await context.teamAddUser(team_id, userId2, 3);
                subcategoryId = (await context.provideSubcategory({ name: 'cf subcat' })).id;

                const scenario = await buildTestScenario(context, team_id, client);
                ({ taskType1, taskType2, taskType1CF, globalCF, taskType1And2CF } = scenario);
                valuesForAllCFs = [
                    { id: globalCF.id, value: '1' },
                    { id: taskType1CF.id, value: '2' },
                    { id: taskType1And2CF.id, value: '3' },
                ];
                [task1Type1, task2Type2, task3NoType] = await Promise.all([
                    context.provideTask({
                        subcategoryId,
                        customFields: valuesForAllCFs,
                        custom_type: taskType1.id,
                    }),
                    context.provideTask({
                        subcategoryId,
                        customFields: valuesForAllCFs,
                        custom_type: taskType2.id,
                    }),
                    context.provideTask({
                        subcategoryId,
                        customFields: valuesForAllCFs,
                    }),
                ]);
            });

            describe.each([false, true])('with custom fields by custom task type flag %s', flagVal => {
                beforeAll(async () => {
                    await changeSplitTestTreatments([
                        ...featureFlags,
                        {
                            featureName: FeatureFlag.CustomFieldsByCustomTaskType,
                            value: flagVal ? 'on' : 'off',
                            config: { default: flagVal },
                        },
                    ]);
                });
                describe.each(['getTasks', 'getTask'])('when using %s', method => {
                    async function fetchTasks() {
                        if (method === 'getTasks') {
                            const { tasks } = await context.publicApiGetTasks({
                                listId: subcategoryId,
                            });
                            // ensure order matches what we expect for testing
                            return [
                                tasks.find(({ id }) => id === task1Type1.id),
                                tasks.find(({ id }) => id === task2Type2.id),
                                tasks.find(({ id }) => id === task3NoType.id),
                            ];
                        }
                        return Promise.all([
                            publicApiGetTask(userId2, task1Type1.id),
                            publicApiGetTask(userId2, task2Type2.id),
                            publicApiGetTask(userId2, task3NoType.id),
                        ]).then(tasks => tasks.map(({ data }) => data));
                    }
                    function buildMatchers(tasks: [string, typeof valuesForAllCFs][]) {
                        return tasks.map(([taskId, fields]) =>
                            expect.objectContaining({
                                id: taskId,
                                custom_fields: expect.arrayContaining(
                                    fields.map(({ id, value }) => expect.objectContaining({ id, value }))
                                ),
                            })
                        );
                    }
                    if (!flagVal) {
                        it('should return all custom field values and definitions', async () => {
                            const tasks = await fetchTasks();
                            expect(tasks).toHaveLength(3);
                            expect(tasks).toEqual(
                                buildMatchers([
                                    [task1Type1.id, valuesForAllCFs],
                                    [task2Type2.id, valuesForAllCFs],
                                    [task3NoType.id, valuesForAllCFs],
                                ])
                            );
                        });
                    } else if (flagVal) {
                        it('should return only the custom field values and defintions that apply for the custom type', async () => {
                            const tasks = await fetchTasks();
                            expect(tasks).toHaveLength(3);
                            expect(tasks).toEqual(
                                buildMatchers([
                                    [task1Type1.id, valuesForAllCFs],
                                    [task2Type2.id, [valuesForAllCFs[0], valuesForAllCFs[2]]],
                                    [task3NoType.id, [valuesForAllCFs[0]]],
                                ])
                            );
                            expect(tasks).not.toEqual(
                                buildMatchers([
                                    [task1Type1.id, []],
                                    [task2Type2.id, [valuesForAllCFs[1]]],
                                    [task3NoType.id, [valuesForAllCFs[1], valuesForAllCFs[2]]],
                                ])
                            );
                        });
                    }
                });
            });
        });
    });
});
