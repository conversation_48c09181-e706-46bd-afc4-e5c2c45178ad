/**
 * Plan Service
 *
 * @group functional/models/billing/services/addOnPromoCode
 */

import { RestClient } from '@clickup/rest-client';

import { TeamId } from '@clickup-legacy/libs/common/types';
import { planService } from '@clickup-legacy/models/billing/plan/planService';
import { PromoCode } from '@clickup-legacy/libs/billing/promoCode/interface';
import { AddonType, PlanIds, SpecificAddonType, WorkspacePaymentsCycle } from '@clickup/billing/types';
import { promiseWriteQuery, globalReadQueryAsync } from '@clickup/utils/db-monolith';
import ClickUpContext from '../../../../../src/jest-context/ClickUpContext';

import { PlanPromoProductType } from '../../../../../src/libs/billing/promoCode/teamPromoCode/types';
import { GlobalTableMigrationBatchId } from '../../../../../src/utils/pg/interfaces/GlobalDatabaseByBatchConfig';

const DAY_IN_MS = 1000 * 60 * 60 * 24;

describe('AddOn Promo Codes', () => {
    jest.setTimeout(60_000);

    let context: ClickUpContext;
    let teamId: TeamId;
    let client: RestClient;

    const calculateExpectedAddonPrice = async () => {
        const {
            rows: [txnHistoryRecord],
        } = await globalReadQueryAsync(
            `SELECT * FROM task_mgmt.txn_history WHERE team_id = $1 AND event_type LIKE 'addon%'`,
            [teamId],
            { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_20 }
        );

        return getExpectedAddonPrice(txnHistoryRecord);
    };

    const getExpectedAddonPrice = (addonRecord: any) =>
        (
            Math.ceil(+addonRecord.transaction_details.prorate * addonRecord.qty * addonRecord.discounted_ppu * 100) /
            100
        ).toFixed(2);

    beforeAll(() => {
        context = new ClickUpContext(); // needed for initialization of nested beforeAll()s
    });

    beforeEach(async () => {
        context = new ClickUpContext(); // need a fresh workspace for each test
        ({ client } = await context.provideUser());
        ({ id: teamId } = await context.provideTeam({}, { client }));
        await context.addPaymentMethod({ teamId }, { client });
    });

    /**
     * Unified logic charges for billed users this cycle in case of add-on per-seat transactions. It's a expected difference since
     * legacy logic used to recompute the seats, which led to discrepancies. Due to such difference we need to manually override
     * the billed users this cycle in below test cases.
     */
    it.each([
        {
            name: 'off both',
            addon: 'ai_addon',
            testPromo: {
                business_percentage_off_per_user_yearly: 13,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 13,
                    },
                ],
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Yearly,
            additionalMembers: [],
            expectedAmount: '177.48',
            expectedAddonFullPrice: 52.2,
            expectedAddonMonthlyPrice: 4.35,
            expectedAddonMonthlyPricePerUser: 4.35,
            expectedDiscount: '26.52', // 26.52 + 177.48 = 204 (144 + 60)
            expectedDiscountType: 'promo_code',
            expectedAddonPaidUsers: 1,
            expectedFullPrice: 60,
            expectedMonthlyPrice: 5,
            expectedPricePerUser: 60,
        },
        {
            name: 'base plan null promo',
            addon: 'ai_addon',
            testPromo: {
                valid_yearly: true,
                business_percentage_off_per_user_yearly: 13,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                    },
                ],
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Yearly,
            additionalMembers: [],
            expectedAmount: '185.28',
            expectedAddonFullPrice: 60,
            expectedAddonMonthlyPrice: 5,
            expectedAddonMonthlyPricePerUser: 5,
            expectedDiscount: '18.72', // 18.72 + 185.28 = 204 (144 + 60)
            expectedDiscountType: 'promo_code',
            expectedAddonPaidUsers: 1,
            expectedFullPrice: 60,
            expectedMonthlyPrice: 5,
            expectedPricePerUser: 60,
        },
        {
            name: 'base plan only',
            addon: 'ai_addon',
            testPromo: {
                business_percentage_off_per_user_yearly: 13,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Yearly,
            additionalMembers: [],
            expectedAmount: '185.28',
            expectedAddonFullPrice: 60,
            expectedAddonMonthlyPrice: 5,
            expectedAddonMonthlyPricePerUser: 5,
            expectedDiscount: '18.72', // 18.72 + 185.28 = 204 (144 + 60)
            expectedDiscountType: null,
            expectedAddonPaidUsers: 1,
            expectedFullPrice: 60,
            expectedMonthlyPrice: 5,
            expectedPricePerUser: 60,
        },
        {
            name: 'AI only',
            addon: 'ai_addon',
            testPromo: {
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 13,
                    },
                ],
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            additionalMembers: [],
            cycles: WorkspacePaymentsCycle.Yearly,
            expectedAmount: '196.20',
            expectedAddonFullPrice: 52.2,
            expectedAddonMonthlyPrice: 4.35,
            expectedAddonMonthlyPricePerUser: 4.35,
            expectedDiscount: '7.80', // 7.8 + 196.2 = 204 (144 + 60)
            expectedDiscountType: 'promo_code',
            expectedAddonPaidUsers: 1,
            expectedFullPrice: 60,
            expectedMonthlyPrice: 5,
            expectedPricePerUser: 60,
        },
        {
            name: 'AI 100% off',
            addon: 'ai_addon',
            testPromo: {
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 100,
                    },
                ],
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Yearly,
            additionalMembers: [],
            expectedAmount: '144.00',
            expectedAddonFullPrice: 0,
            expectedAddonMonthlyPrice: 0,
            expectedAddonMonthlyPricePerUser: 0,
            expectedDiscount: '60.00',
            expectedDiscountType: 'promo_code',
            expectedAddonPaidUsers: 1,
            expectedFullPrice: 60,
            expectedMonthlyPrice: 5,
            expectedPricePerUser: 60,
        },
        {
            name: 'off both',
            addon: 'ai_addon',
            testPromo: {
                business_percentage_off_per_user_monthly: 13,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_monthly: 13,
                    },
                ],
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [],
            expectedAmount: '20.88',
            expectedAddonFullPrice: 4.35,
            expectedAddonMonthlyPrice: 4.35,
            expectedAddonMonthlyPricePerUser: 4.35,
            expectedDiscount: '3.12', // 3.12 + 20.88 = 24 (19 + 5)
            expectedDiscountType: 'promo_code',
            expectedAddonPaidUsers: 1,
            expectedFullPrice: 60,
            expectedMonthlyPrice: 5,
            expectedPricePerUser: 60,
        },
        {
            name: 'base plan and null AI promo',
            addon: 'ai_addon',
            testPromo: {
                business_percentage_off_per_user_monthly: 13,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                    },
                ],
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [],
            expectedAmount: '21.53',
            expectedAddonFullPrice: 5,
            expectedAddonMonthlyPrice: 5,
            expectedAddonMonthlyPricePerUser: 5,
            expectedDiscount: '2.47', // 2.47 + 21.53 = 24 (19 + 5)
            expectedDiscountType: 'promo_code',
            expectedAddonPaidUsers: 1,
            expectedFullPrice: 60,
            expectedMonthlyPrice: 5,
            expectedPricePerUser: 60,
        },
        {
            name: 'base plan only',
            addon: 'ai_addon',
            testPromo: {
                business_percentage_off_per_user_monthly: 13,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [],
            expectedAmount: '21.53',
            expectedAddonFullPrice: 5,
            expectedAddonMonthlyPrice: 5,
            expectedAddonMonthlyPricePerUser: 5,
            expectedDiscount: '2.47', // 2.47 + 21.53 = 24 (19 + 5)
            expectedDiscountType: null,
            expectedAddonPaidUsers: 1,
            expectedFullPrice: 60,
            expectedMonthlyPrice: 5,
            expectedPricePerUser: 60,
        },
        {
            name: 'AI only',
            addon: 'ai_addon',
            testPromo: {
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_monthly: 13,
                    },
                ],
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [],
            expectedAmount: '23.35',
            expectedAddonFullPrice: 4.35,
            expectedAddonMonthlyPrice: 4.35,
            expectedAddonMonthlyPricePerUser: 4.35,
            expectedDiscount: '0.65', // 0.65 + 23.35 = 24 (19 + 5)
            expectedDiscountType: 'promo_code',
            expectedAddonPaidUsers: 1,
            expectedFullPrice: 60,
            expectedMonthlyPrice: 5,
            expectedPricePerUser: 60,
        },
        {
            name: 'unmatched addon',
            addon: 'automations_unlimited',
            testPromo: {
                business_percentage_off_per_user_monthly: 13,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_monthly: 13,
                    },
                ],
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [],
            expectedAmount: '45.53',
            expectedAddonFullPrice: 4.35,
            expectedAddonMonthlyPrice: 4.35,
            expectedAddonMonthlyPricePerUser: 4.35,
            expectedDiscount: '2.47', // no promo discount for this type of addon, only plan is discounted
            expectedDiscountType: null,
            expectedAddonPaidUsers: 1,
            expectedFullPrice: 60,
            expectedMonthlyPrice: 5,
            expectedPricePerUser: 60,
        },
        {
            name: 'AI only but with minimum seats',
            addon: 'ai_addon',
            testPromo: {
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_monthly: 13,
                    },
                ],
            },
            billedUsersThisCycle: 4,
            minimumSeats: 4,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [],
            expectedAmount: '93.40', // 23.35 * 4
            expectedAddonFullPrice: 17.4, // 4.35 * 4,
            expectedAddonMonthlyPrice: 17.4, // 4.35 * 4,
            expectedAddonMonthlyPricePerUser: 4.35,
            expectedDiscount: '2.60', // 0.65 * 4,
            expectedDiscountType: 'promo_code',
            expectedAddonPaidUsers: 4,
            expectedFullPrice: 240,
            expectedMonthlyPrice: 20,
            expectedPricePerUser: 60,
        },
        {
            name: 'Base Plan Only but with minimum seats',
            addon: 'ai_addon',
            testPromo: {
                business_percentage_off_per_user_monthly: 13,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
            },
            billedUsersThisCycle: 4,
            minimumSeats: 4,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [],
            expectedAmount: '86.12', // 21.53 * 4
            expectedAddonFullPrice: 20, // 5 * 4,
            expectedAddonMonthlyPrice: 20, // 5 * 4,
            expectedAddonMonthlyPricePerUser: 5,
            expectedDiscount: '9.88', // 2.47 * 4,
            expectedDiscountType: null,
            expectedAddonPaidUsers: 4,
            expectedFullPrice: 240,
            expectedMonthlyPrice: 20,
            expectedPricePerUser: 60,
        },
        {
            name: 'Both off but with minimum seats',
            addon: 'ai_addon',
            testPromo: {
                business_percentage_off_per_user_monthly: 13,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_monthly: 13,
                    },
                ],
            },
            billedUsersThisCycle: 4,
            minimumSeats: 4,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [],
            expectedAmount: '83.52', // 20.88 * 4
            expectedAddonFullPrice: 17.4, // 4.35 * 4,
            expectedAddonMonthlyPrice: 17.4, // 4.35 * 4,
            expectedAddonMonthlyPricePerUser: 4.35,
            expectedDiscount: '12.48', // 3.12 * 4,
            expectedDiscountType: null,
            expectedAddonPaidUsers: 4,
            expectedFullPrice: 240,
            expectedMonthlyPrice: 20,
            expectedPricePerUser: 60,
        },
        {
            name: 'Both off but with additional member',
            addon: 'ai_addon',
            testPromo: {
                business_percentage_off_per_user_monthly: 13,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_monthly: 13,
                    },
                ],
            },
            billedUsersThisCycle: 2,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [{ role: 2, userid: 4372492 }],
            expectedAmount: '41.76', // 20.88 * 2
            expectedAddonFullPrice: 8.7, // 4.35 * 2,
            expectedAddonMonthlyPrice: 8.7, // 4.35 * 2,
            expectedAddonMonthlyPricePerUser: 4.35,
            expectedDiscount: '6.24', // 3.12 * 2,
            expectedDiscountType: null,
            expectedAddonPaidUsers: 2,
            expectedFullPrice: 120,
            expectedMonthlyPrice: 10,
            expectedPricePerUser: 60,
        },
        {
            name: 'AI Only but with free seats',
            addon: 'ai_addon',
            testPromo: {
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_monthly: 13,
                    },
                ],
            },
            billedUsersThisCycle: 2,
            minimumSeats: null,
            // 4 team members with 2 free seats, gives us 2 paid seats
            freeSeats: 2,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [
                { role: 2, userid: 4372492 },
                { role: 2, userid: 4372478 },
                { role: 2, userid: 3481112 },
            ],
            expectedAmount: '46.70', // 23.35 * 2
            expectedAddonFullPrice: 8.7, // 4.35 * 2,
            expectedAddonMonthlyPrice: 8.7, // 4.35 * 2,
            expectedAddonMonthlyPricePerUser: 4.35,
            expectedDiscount: '1.30', // 0.65 * 2,
            expectedDiscountType: null,
            expectedAddonPaidUsers: 2,
            expectedFullPrice: 120,
            expectedMonthlyPrice: 10,
            expectedPricePerUser: 60,
        },
        {
            name: 'Base plan only but with free seats',
            addon: 'ai_addon',
            testPromo: {
                business_percentage_off_per_user_monthly: 13,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
            },
            billedUsersThisCycle: 2,
            minimumSeats: null,
            // 4 team members with 2 free seats, gives us 2 paid seats
            freeSeats: 2,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [
                { role: 2, userid: 4372492 },
                { role: 2, userid: 4372478 },
                { role: 2, userid: 3481112 },
            ],
            expectedAmount: '43.06', // 21.53 * 2
            expectedAddonFullPrice: 10, // 5 * 2,
            expectedAddonMonthlyPrice: 10, // 5 * 2,
            expectedAddonMonthlyPricePerUser: 5,
            expectedDiscount: '4.94', // 2.47 * 2,
            expectedDiscountType: null,
            expectedAddonPaidUsers: 2,
            expectedFullPrice: 120,
            expectedMonthlyPrice: 10,
            expectedPricePerUser: 60,
        },
        {
            name: 'Both off but with free seats',
            addon: 'ai_addon',
            testPromo: {
                business_percentage_off_per_user_monthly: 13,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_monthly: 13,
                    },
                ],
            },
            billedUsersThisCycle: 2,
            minimumSeats: null,
            // 4 team members with 2 free seats, gives us 2 paid seats
            freeSeats: 2,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [
                { role: 2, userid: 4372492 },
                { role: 2, userid: 4372478 },
                { role: 2, userid: 3481112 },
            ],
            expectedAmount: '41.76', // 20.88 * 2
            expectedAddonFullPrice: 8.7, // 4.35 * 2,
            expectedAddonMonthlyPrice: 8.7, // 4.35 * 2,
            expectedAddonMonthlyPricePerUser: 4.35,
            expectedDiscount: '6.24', // 3.12 * 2,
            expectedDiscountType: null,
            expectedAddonPaidUsers: 2,
            expectedFullPrice: 120,
            expectedMonthlyPrice: 10,
            expectedPricePerUser: 60,
        },
        {
            name: 'off both but on yearly cycles',
            addon: 'ai_addon',
            testPromo: {
                business_percentage_off_per_user_yearly: 13,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 13,
                    },
                ],
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Yearly,
            additionalMembers: [],
            expectedAmount: '177.48', // 0.87 * 204 (144 + 60)
            expectedAddonFullPrice: 52.2,
            expectedAddonMonthlyPrice: 4.35,
            expectedAddonMonthlyPricePerUser: 4.35,
            expectedDiscount: '26.52',
            expectedDiscountType: 'promo_code',
            expectedAddonPaidUsers: 1,
            expectedFullPrice: 60,
            expectedMonthlyPrice: 5,
            expectedPricePerUser: 60,
        },
        {
            name: 'even though multiple addons bought, only ai should be discounted',
            addon: 'ai_addon',
            additionalAddons: [{ id: SpecificAddonType.ProductivityStarterPack, amount: 1 }],
            testPromo: {
                business_percentage_off_per_user_yearly: 13,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 13,
                    },
                ],
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Yearly,
            additionalMembers: [],
            expectedAmount: '1365.48', // 0.87 * 204 (144 + 60) + 1188
            expectedAddonFullPrice: 52.2,
            expectedAddonMonthlyPrice: 4.35,
            expectedAddonMonthlyPricePerUser: 4.35,
            expectedDiscount: '26.52',
            expectedDiscountType: 'promo_code',
            expectedAddonPaidUsers: 1,
            expectedFullPrice: 60,
            expectedMonthlyPrice: 5,
            expectedPricePerUser: 60,
        },
        {
            name: 'Both off but with one member and more free seats',
            addon: 'ai_addon',
            testPromo: {
                business_percentage_off_per_user_monthly: 13,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_monthly: 13,
                    },
                ],
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: 3,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [],
            /**
             * Note: It bills only for 1 user
             */
            expectedAmount: '20.88', // 0.87 * 24
            expectedAddonFullPrice: 4.35,
            expectedAddonMonthlyPrice: 4.35,
            expectedAddonMonthlyPricePerUser: 4.35,
            expectedDiscount: '3.12', // 2.47 + 0.65
            expectedDiscountType: 'promo_code',
            // we always should bill at least 1 user for AI addon
            expectedAddonPaidUsers: 1,
            expectedFullPrice: 60,
            expectedMonthlyPrice: 5,
            expectedPricePerUser: 60,
        },
        {
            name: 'live training discount only',
            addon: 'ai_addon',
            additionalAddons: [{ id: SpecificAddonType.ProductivityPowerPack, amount: 1 }],
            validatedAddon: 'productivity_power_pack',
            testPromo: {
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'productivity_starter_pack',
                        percentage_off_monthly: 13,
                    },
                    {
                        addon_id: 'productivity_power_pack',
                        percentage_off_monthly: 26,
                    },
                ],
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            additionalMembers: [],
            expectedAmount: '208.26',
            expectedAddonFullPrice: 184.26,
            expectedAddonMonthlyPrice: 184.26,
            expectedAddonMonthlyPricePerUser: undefined,
            expectedDiscount: '64.74', // 249 - 26% off = 184.26; 249 - 184.26 = 64.74
            expectedDiscountType: 'promo_code',
            expectedAddonPaidUsers: undefined,
            expectedFullPrice: 2988,
            expectedMonthlyPrice: 249,
            expectedPricePerUser: undefined,
        },
        {
            name: 'live training discount only',
            addon: 'ai_addon',
            additionalAddons: [{ id: SpecificAddonType.ProductivityStarterPack, amount: 1 }],
            validatedAddon: 'productivity_starter_pack',
            testPromo: {
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'productivity_starter_pack',
                        percentage_off_yearly: 13,
                    },
                    {
                        addon_id: 'productivity_power_pack',
                        percentage_off_yearly: 26,
                    },
                ],
            },
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            cycles: WorkspacePaymentsCycle.Yearly,
            additionalMembers: [],
            expectedAmount: '1237.56', // 1392 = 204 (144 plan + 60 AI) + productivity pack - 13% off 1033.56
            expectedAddonFullPrice: 1033.56,
            expectedAddonMonthlyPrice: 86.13,
            expectedAddonMonthlyPricePerUser: undefined,
            expectedDiscount: '154.44', // 1188 - 13% off = 1033.56; 1188 - 1033.56 = 154.44
            expectedDiscountType: 'promo_code',
            expectedAddonPaidUsers: undefined,
            expectedFullPrice: 1188,
            expectedMonthlyPrice: 99,
            expectedPricePerUser: undefined,
        },
    ])(
        'should charge $expectedAmount for $addon with $testPromo.usage_type $name promo code $cycles',
        async ({
            testPromo,
            addon,
            validatedAddon,
            additionalAddons,
            expectedAmount,
            cycles,
            additionalMembers,
            billedUsersThisCycle,
            minimumSeats,
            freeSeats,
            expectedAddonFullPrice,
            expectedAddonMonthlyPrice,
            expectedAddonMonthlyPricePerUser,
            expectedDiscount,
            expectedAddonPaidUsers,
            expectedFullPrice,
            expectedMonthlyPrice,
            expectedPricePerUser,
        }) => {
            // there used to only be one addon which could be discounted; now we have multiple, but preserve the default
            validatedAddon = validatedAddon ?? 'ai_addon';

            /**
             * NOTE: If minimumSeats or freeSeats are null, it means that workspace is updated with default values.
             */
            await Promise.all([
                context.updateTeamForBillingPurposes(teamId, {
                    minimum_seats: minimumSeats,
                    free_seats: freeSeats,
                    billed_users_this_cycle: billedUsersThisCycle,
                }),
                context.updateTeamBillingInfoForBillingPurposes(teamId, {
                    billed_users_this_cycle: billedUsersThisCycle,
                }),
            ]);

            /**
             * If there's any additional member, we should add him to the workspace
             */
            await context.addUsersToWorkspace(teamId, null, additionalMembers);

            const promoCode = await context.providePromoCode(
                testPromo as Partial<PromoCode & { usage_type: 'PERCENTAGE_OFF' | 'FIXED_AMOUNT_OFF' | 'LOCKED_PRICE' }>
            );

            // 1. validate if prices are good pre-subscription
            const teamDetailsPre = await context.getTeam({ team_id: teamId }, { client });
            expect(teamDetailsPre.addon_prices.find(e => e.id === validatedAddon)).toMatchObject({
                /**
                 * Pre subcription means, that there is no promo code applied, and we should
                 * follow the default values for the addon. Pre subscription prices are per year, as there's no
                 * cycles changed yet.
                 */
                full_price: expectedFullPrice,
                price_per_month: expectedMonthlyPrice,
                price_per_user: expectedPricePerUser,
                addon_paid_users: expectedAddonPaidUsers,
            });

            // 1a. check if quotes are ok
            await expect(
                context
                    .getBillingPlanDetails({ teamId, promoCode }, { client })
                    .then(res => res.addonPrices[cycles].find(e => e.id === validatedAddon))
            ).resolves.toMatchObject({
                ...(expectedAddonPaidUsers && {
                    price_per_user_per_month: expectedAddonMonthlyPricePerUser,
                    addon_paid_users: expectedAddonPaidUsers,
                }),
                price_per_month: expectedAddonMonthlyPrice,
                full_price: expectedAddonFullPrice,
                usedPromoCode: testPromo?.addons?.find(a => a.addon_id === validatedAddon) ? promoCode : null, // if defined for the addon, it should be reported
            });

            // 2. subscribe
            await context.choosePlan(
                {
                    teamId,
                    planId: PlanIds.Business,
                    cycles,
                    addons: [{ id: addon }, ...(additionalAddons || [])],
                    promoCode,
                },
                { client }
            );

            // 3. validate if prices are good after subscription
            const planDetails = await planService.getPlanDetails(teamId);
            expect(planDetails).toMatchObject({
                billedPlanId: PlanIds.Business,
                cycles,
                nextBillDate: expect.any(Number),
                planId: PlanIds.Business,
                planTier: 'Business',
            });

            const teamDetailsPost = await context.getTeam({ team_id: teamId }, { client });
            expect(teamDetailsPost).toMatchObject({
                addons: expect.arrayContaining([
                    expect.objectContaining({
                        addon_id: addon,
                        date_added: expect.any(String),
                        /** For AI addon the total is always 1, even if there's multiple users in the workspace */
                        total: 1,
                    }),
                    ...(additionalAddons
                        ? additionalAddons.map(additionalAddon =>
                              expect.objectContaining({
                                  addon_id: additionalAddon.id,
                                  date_added: expect.any(String),
                                  total: additionalAddon.amount,
                              })
                          )
                        : []),
                ]),
                billing_info: {
                    promo_codes: expect.arrayContaining([
                        expect.objectContaining({
                            promo_code: promoCode,
                        }),
                    ]),
                },
            });

            const result = await promiseWriteQuery(
                `SELECT body->>'amount' amount, data->>'discount' discount FROM task_mgmt.transactions WHERE team_id = $1 ORDER BY 1`,
                [teamId]
            );
            expect(result.rows).toStrictEqual([{ amount: expectedAmount, discount: expectedDiscount }]);

            await expect(context.getBillingPlanDetails({ teamId, promoCode }, { client })).resolves.toMatchObject({
                currentPrice: {
                    total_with_addons: expectedAmount,
                },
            });
        }
    );

    it('should not throw an error if promo code to apply during addon purchase is not expired', async () => {
        // create promo code which expires in 2 days
        const promoCode = await context.providePromoCode({
            usage_type: 'PERCENTAGE_OFF',
            expiration: Date.now() + 2 * DAY_IN_MS,
            business_percentage_off_per_user_monthly: 13,
            valid_monthly: true,
            addons: [
                {
                    addon_id: 'ai_addon',
                    percentage_off_monthly: 13,
                },
            ],
        } as unknown as Partial<PromoCode & { usage_type: 'PERCENTAGE_OFF' | 'FIXED_AMOUNT_OFF' | 'LOCKED_PRICE' }>);

        // upgrade workspace to paid plan
        await context.choosePlan(
            {
                teamId,
                planId: PlanIds.Business,
                cycles: WorkspacePaymentsCycle.Monthly,
            },
            { client }
        );

        // we do pass promo code via options
        const result = await context.purchaseAddon({ teamId, addonId: 'ai_addon', promoCode }, { client });

        const expectedAddonPrice = await calculateExpectedAddonPrice();
        // we should still charge workspace with discount
        await expect(
            promiseWriteQuery(
                `SELECT body->>'amount' amount, data->>'discount' discount FROM task_mgmt.transactions WHERE team_id = $1 AND data->>'reason' = 'addon added'`,
                [teamId]
            ).then(res => res.rows.map(({ amount, discount }) => ({ amount, discount })))
        ).resolves.toMatchObject([{ amount: expectedAddonPrice }]);

        // transaction is defined
        expect(result.trx_id).toBeDefined();
    });

    it('should throw an error if promo code to apply during addon purchase is expired', async () => {
        // create promo code which expired 2 days ago
        const promoCode = await context.providePromoCode({
            usage_type: 'PERCENTAGE_OFF',
            expiration: Date.now() - 2 * DAY_IN_MS,
            business_percentage_off_per_user_monthly: 13,
            valid_monthly: true,
            addons: [
                {
                    addon_id: 'ai_addon',
                    percentage_off_monthly: 13,
                },
            ],
        } as unknown as Partial<PromoCode & { usage_type: 'PERCENTAGE_OFF' | 'FIXED_AMOUNT_OFF' | 'LOCKED_PRICE' }>);

        // upgrade workspace to paid plan
        await context.choosePlan(
            {
                teamId,
                planId: PlanIds.Business,
                cycles: WorkspacePaymentsCycle.Monthly,
            },
            { client }
        );

        expect.assertions(1);

        try {
            // we do pass promo code via options
            await context.purchaseAddon({ teamId, addonId: 'ai_addon', promoCode }, { client });
        } catch (err: unknown) {
            // should throw an error if promo code is expired
            expect(err).toBeDefined();
        }
    });

    it('should not throw an error if already applied promo code is expired', async () => {
        // create promo code which expired 2 days ago
        const promoCode = await context.providePromoCode({
            usage_type: 'PERCENTAGE_OFF',
            expiration: Date.now() - 2 * DAY_IN_MS,
            business_percentage_off_per_user_monthly: 13,
            valid_monthly: true,
            addons: [
                {
                    addon_id: 'ai_addon',
                    percentage_off_monthly: 13,
                },
            ],
        } as unknown as Partial<PromoCode & { usage_type: 'PERCENTAGE_OFF' | 'FIXED_AMOUNT_OFF' | 'LOCKED_PRICE' }>);

        // upgrade workspace to paid plan
        await context.choosePlan(
            {
                teamId,
                planId: PlanIds.Business,
                cycles: WorkspacePaymentsCycle.Monthly,
            },
            { client }
        );

        // assign expired promo code to workspace
        await context.updateTeamForBillingPurposes(teamId, {}, [
            {
                promo_code: promoCode,
                cycles_applied: 0,
                product_type: AddonType.Ai,
            },
        ]);

        // we do not pass promo code via options, as it's already applied to the workspace
        const result = await context.purchaseAddon({ teamId, addonId: 'ai_addon', promoCode: null }, { client });

        const expectedAddonPrice = await calculateExpectedAddonPrice();
        // we should still charge workspace with discount
        await expect(
            promiseWriteQuery(
                `SELECT body->>'amount' amount, data->>'discount' discount FROM task_mgmt.transactions WHERE team_id = $1 AND data->>'reason' = 'addon added'`,
                [teamId]
            ).then(res => res.rows.map(({ amount, discount }) => ({ amount, discount })))
        ).resolves.toMatchObject([{ amount: expectedAddonPrice }]);

        // transaction is defined
        expect(result.trx_id).toBeDefined();
    });

    it('should successfuly process purchasing addon if already applied promo code is not expired', async () => {
        // create promo code which expires in 2 days
        const promoCode = await context.providePromoCode({
            usage_type: 'PERCENTAGE_OFF',
            expiration: Date.now() + 2 * DAY_IN_MS,
            business_percentage_off_per_user_monthly: 13,
            valid_monthly: true,
            addons: [
                {
                    addon_id: 'ai_addon',
                    percentage_off_monthly: 13,
                },
            ],
        } as unknown as Partial<PromoCode & { usage_type: 'PERCENTAGE_OFF' | 'FIXED_AMOUNT_OFF' | 'LOCKED_PRICE' }>);

        // upgrade workspace to paid plan
        await context.choosePlan(
            {
                teamId,
                planId: PlanIds.Business,
                cycles: WorkspacePaymentsCycle.Monthly,
            },
            { client }
        );

        // assign expired promo code to workspace
        await context.updateTeamForBillingPurposes(teamId, {}, [
            {
                promo_code: promoCode,
                cycles_applied: 0,
                product_type: AddonType.Ai,
            },
        ]);

        // we do not pass promo code via options, as it's already applied to the workspace
        const result = await context.purchaseAddon({ teamId, addonId: 'ai_addon', promoCode: null }, { client });

        const expectedAddonPrice = await calculateExpectedAddonPrice();
        // we should still charge workspace with discount
        await expect(
            promiseWriteQuery(
                `SELECT body->>'amount' amount, data->>'discount' discount FROM task_mgmt.transactions WHERE team_id = $1 AND data->>'reason' = 'addon added'`,
                [teamId]
            ).then(res => res.rows.map(({ amount, discount }) => ({ amount, discount })))
        ).resolves.toMatchObject([{ amount: expectedAddonPrice }]);

        // transaction is defined
        expect(result.trx_id).toBeDefined();
    });

    it.each([
        {
            /** Addon prices should be recalculated */
            name: 'off both to better',
            addon: 'ai_addon',
            firstPromo: {
                business_percentage_off_per_user_yearly: 13,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 13,
                    },
                ],
            },
            secondPromo: {
                business_plus_percentage_off_per_user_yearly: 29,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 29,
                    },
                ],
            },
            firstPlan: PlanIds.Business,
            secondPlan: PlanIds.BusinessPlus,
            cycles: WorkspacePaymentsCycle.Yearly,
            expectedAmounts: ['177.48', '36.60'], // (1 - 0.29) * (228 + 60) - (1 - 0.13) * (144 + 60) = 27
            aiPpu: 60,
            aiDiscountedPpu: 42.6,
            aiDiscountType: 'promo_code',
            expectedDiscounts: ['26.52', '73.92'], // 0.13 * (60 + 144) | 0.29 * (60 + 228) = 83.52
            shouldAddonPriceBeRecalculated: true,
        },
        {
            /** Addon prices should be recalculated */
            name: 'off both to worse',
            addon: 'ai_addon',
            firstPromo: {
                business_percentage_off_per_user_yearly: 29,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 29,
                    },
                ],
            },
            secondPromo: {
                business_plus_percentage_off_per_user_yearly: 13,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 13,
                    },
                ],
            },
            firstPlan: PlanIds.Business,
            secondPlan: PlanIds.BusinessPlus,
            cycles: WorkspacePaymentsCycle.Yearly,
            expectedAmounts: ['144.84', '96.12'], // (1-0.13)*(228+60)-(1-0.29)*(144+60) = 105.72
            aiPpu: 60,
            aiDiscountedPpu: 52.2,
            aiDiscountType: 'promo_code',
            expectedDiscounts: ['59.16', '47.04'], // 0.29 * (60 + 144) = 59.16  | 0.13 * (60 + 228) = 37.44
            shouldAddonPriceBeRecalculated: true,
        },
        {
            /** Addon prices should be recalculated */
            name: 'off both to zero',
            addon: 'ai_addon',
            firstPromo: {
                business_percentage_off_per_user_yearly: 13,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 13,
                    },
                ],
            },
            secondPromo: {
                business_plus_percentage_off_per_user_yearly: 29,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 0,
                    },
                ],
            },
            firstPlan: PlanIds.Business,
            secondPlan: PlanIds.BusinessPlus,
            cycles: WorkspacePaymentsCycle.Yearly,
            expectedAmounts: ['177.48', '36.60'], // (1-0.29)*228+60-(1-0.13)*(144+60) = 44.40
            aiPpu: 60,
            aiDiscountedPpu: 60,
            aiDiscountType: 'promo_code',
            expectedDiscounts: ['26.52', '73.92'], // 0.13 * (60 + 144) = 26.52  | 0.29 * 228 = 66.12
            shouldAddonPriceBeRecalculated: true,
        },
        {
            /** Addon prices should be recalculated */
            name: 'first off both to base only',
            addon: 'ai_addon',
            firstPromo: {
                business_percentage_off_per_user_yearly: 13,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 13,
                    },
                ],
            },
            secondPromo: {
                business_plus_percentage_off_per_user_yearly: 29,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [],
            },
            firstPlan: PlanIds.Business,
            secondPlan: PlanIds.BusinessPlus,
            cycles: WorkspacePaymentsCycle.Yearly,
            expectedAmounts: ['177.48', '36.60'], // $177.48 = 0.87 * ($144 + 60) | $67.92 = 0.71 * $228 + $60 - 177.48
            aiPpu: 60,
            aiDiscountedPpu: 60,
            aiDiscountType: null,
            expectedDiscounts: ['26.52', '73.92'],
            shouldAddonPriceBeRecalculated: true,
        },
        // {
        //     /** Produces invalid entry to subscription history for ai although promo wasn't applied - need to be fixed
        //     /** Addon prices should be recalculated */
        //     name: 'first base only to base better',
        //     addon: 'ai_addon',
        //     firstPromo: {
        //         business_percentage_off_per_user_yearly: 13,
        //         valid_yearly: true,
        //         usage_type: 'PERCENTAGE_OFF',
        //         addons: [],
        //     },
        //     secondPromo: {
        //         business_plus_percentage_off_per_user_yearly: 29,
        //         valid_yearly: true,
        //         usage_type: 'PERCENTAGE_OFF',
        //         addons: [
        //             {
        //                 addon_id: 'ai_addon',
        //                 percentage_off_yearly: 29,
        //             },
        //         ],
        //     },
        //     firstPlan: PlanIds.Business,
        //     secondPlan: PlanIds.BusinessPlus,
        //     cycles: WorkspacePaymentsCycle.Yearly,
        //     expectedAmounts: ['185.28', '36.60'], // ((1 - 0.29) * (228 + 60)) - ((1 - 0.13) * 144 + 60) = 50.52
        //     aiPpu: 60,
        //     aiDiscountedPpu: 42.6,
        //     aiDiscountType: 'promo_code',
        //     expectedDiscounts: ['18.72', '66.12'], // 0.29 * (228 + 60) = 83.52
        //     shouldAddonPriceBeRecalculated: true,
        // },
        {
            /** Addon prices SHOULDN'T be recalculated */
            name: 'first base only to better',
            addon: 'ai_addon',
            firstPromo: {
                business_percentage_off_per_user_yearly: 13,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [],
            },
            secondPromo: {
                business_plus_percentage_off_per_user_yearly: 29,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [],
            },
            firstPlan: PlanIds.Business,
            secondPlan: PlanIds.BusinessPlus,
            cycles: WorkspacePaymentsCycle.Yearly,
            expectedAmounts: ['185.28', '36.60'], // ((1 - 0.29) * 228 + 60) - ((1 - 0.13) * 144 + 60) = 67.92
            aiPpu: 60,
            aiDiscountedPpu: 60,
            aiDiscountType: null,
            expectedDiscounts: ['18.72', '66.12'],
            shouldAddonPriceBeRecalculated: false,
        },
        {
            /** Addon prices SHOULDN'T be recalculated */
            name: 'first base only to worse',
            addon: 'ai_addon',
            firstPromo: {
                business_percentage_off_per_user_yearly: 29,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [],
            },
            secondPromo: {
                business_plus_percentage_off_per_user_yearly: 13,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [],
            },
            firstPlan: PlanIds.Business,
            secondPlan: PlanIds.BusinessPlus,
            cycles: WorkspacePaymentsCycle.Yearly,
            expectedAmounts: ['162.24', '96.12'], // ((1 - 0.13) * 228 + 60) - ((1 - 0.29) * 144 + 60) = 96.12
            aiPpu: 60,
            aiDiscountedPpu: 60,
            aiDiscountType: null,
            expectedDiscounts: ['41.76', '29.64'],
            shouldAddonPriceBeRecalculated: false,
        },
        {
            /** Addon prices should be recalculated */
            name: 'first ai only to both off',
            addon: 'ai_addon',
            firstPromo: {
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 13,
                    },
                ],
            },
            secondPromo: {
                business_plus_percentage_off_per_user_yearly: 29,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 29,
                    },
                ],
            },
            firstPlan: PlanIds.Business,
            secondPlan: PlanIds.BusinessPlus,
            cycles: WorkspacePaymentsCycle.Yearly,
            expectedAmounts: ['196.20', '17.88'],
            aiPpu: 60,
            aiDiscountedPpu: 42.6,
            aiDiscountType: 'promo_code',
            expectedDiscounts: ['7.80', '73.92'], // 0.13 * 60 = 7.80 | 0.29 * (228 + 60) = 83.52
            shouldAddonPriceBeRecalculated: true,
        },
        {
            /** Addon prices should be recalculated */
            name: 'first ai only to base only',
            addon: 'ai_addon',
            firstPromo: {
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 13,
                    },
                ],
            },
            secondPromo: {
                business_plus_percentage_off_per_user_yearly: 29,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [],
            },
            firstPlan: PlanIds.Business,
            secondPlan: PlanIds.BusinessPlus,
            cycles: WorkspacePaymentsCycle.Yearly,
            expectedAmounts: ['196.20', '17.88'], // ((1 - 0.29) * 228 + 60) - ((1 - 0.13) * 60 + 144) = 25.68
            aiPpu: 60,
            aiDiscountedPpu: 60,
            aiDiscountType: null,
            expectedDiscounts: ['7.80', '73.92'], // 7.8 + 160.2 = 168 (108 + 60) | 66.12 + 161.88 = 228
            shouldAddonPriceBeRecalculated: true,
        },
    ])(
        'should charge $expectedAmounts for $cycles $addon when switching promo code $name',
        async ({
            firstPromo,
            secondPromo,
            firstPlan,
            secondPlan,
            addon,
            expectedAmounts,
            cycles,
            aiPpu,
            aiDiscountedPpu,
            aiDiscountType,
            expectedDiscounts,
            shouldAddonPriceBeRecalculated,
        }) => {
            const firstPromoCode = await context.providePromoCode(
                firstPromo as Partial<
                    PromoCode & { usage_type: 'PERCENTAGE_OFF' | 'FIXED_AMOUNT_OFF' | 'LOCKED_PRICE' }
                >
            );

            await context.choosePlan(
                {
                    teamId,
                    planId: firstPlan,
                    cycles,
                    addons: [{ id: addon }],
                    promoCode: firstPromoCode,
                },
                { client }
            );
            // wait for unawaited promises touching subscriptions etc.
            await new Promise(resolve => setTimeout(resolve, 200));

            const secondPromoCode = await context.providePromoCode(
                secondPromo as Partial<
                    PromoCode & { usage_type: 'PERCENTAGE_OFF' | 'FIXED_AMOUNT_OFF' | 'LOCKED_PRICE' }
                >
            );

            await context.choosePlan(
                {
                    teamId,
                    planId: secondPlan,
                    cycles,
                    // can't subscribe twice to the same addon
                    promoCode: secondPromoCode,
                },
                { client }
            );

            // ...again
            await new Promise(resolve => setTimeout(resolve, 200));

            const teamDetails = await context.getTeam({ team_id: teamId }, { client });

            expect(teamDetails).toMatchObject({
                addons: [
                    {
                        addon_id: addon,
                        date_added: expect.any(String),
                        total: 1,
                    },
                ],
            });

            const result = await promiseWriteQuery(
                `SELECT body->>'amount' amount, data->>'discount' discount FROM task_mgmt.transactions WHERE team_id = $1 ORDER BY created ASC`,
                [teamId]
            );

            expect(result.rows.map(({ amount }) => amount)).toStrictEqual(expectedAmounts);
            expect(result.rows.map(({ discount }) => discount)).toStrictEqual(expectedDiscounts);

            await expect(
                context.getTeam({ team_id: teamId }, { client }).then(t => t?.billing_info?.promo_codes)
            ).resolves.toHaveLength(shouldAddonPriceBeRecalculated ? 2 : 1);
        }
    );

    /**
     * Unified logic charges for billed users this cycle in case of add-on only transactions. It's a expected difference since
     * legacy logic used to recompute the seats, which led to discrepancies. Due to such difference we need to manually override
     * the billed users this cycle in below test cases.
     */
    it.each([
        {
            name: 'null',
            promo: null,
            expectedMonthlyPricePerUser: 5,
            expectedPromo: null,
            cycles: WorkspacePaymentsCycle.Yearly,
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            additionalMembers: [],
            expectedPaidUsers: 1,
            expectedTotalPrice: 60,
        },
        {
            name: 'zero',
            promo: {
                usage_type: 'PERCENTAGE_OFF',
            },
            expectedMonthlyPricePerUser: 5,
            expectedPromo: null,
            cycles: WorkspacePaymentsCycle.Yearly,
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            additionalMembers: [],
            expectedPaidUsers: 1,
            expectedTotalPrice: 60,
        },
        {
            name: 'base',
            promo: {
                business_percentage_off_per_user_yearly: 13,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [],
            },
            expectedMonthlyPricePerUser: 5,
            expectedPromo: null,
            cycles: WorkspacePaymentsCycle.Yearly,
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            additionalMembers: [],
            expectedPaidUsers: 1,
            expectedTotalPrice: 60,
        },
        {
            name: 'base with addon',
            promo: {
                business_percentage_off_per_user_yearly: 13,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 13,
                    },
                ],
            },
            expectedMonthlyPricePerUser: 4.35,
            expectedPromo: expect.any(String),
            cycles: WorkspacePaymentsCycle.Yearly,
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            additionalMembers: [],
            expectedPaidUsers: 1,
            expectedTotalPrice: 52.2,
        },
        {
            name: 'null',
            promo: null,
            expectedMonthlyPricePerUser: 5,
            expectedPromo: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            additionalMembers: [],
            expectedPaidUsers: 1,
            expectedTotalPrice: 5,
        },
        {
            name: 'zero',
            promo: {
                usage_type: 'PERCENTAGE_OFF',
            },
            expectedMonthlyPricePerUser: 5,
            expectedPromo: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            additionalMembers: [],
            expectedPaidUsers: 1,
            expectedTotalPrice: 5,
        },
        {
            name: 'base',
            promo: {
                business_percentage_off_per_user_monthly: 13,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [],
            },
            expectedMonthlyPricePerUser: 5,
            expectedPromo: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            additionalMembers: [],
            expectedPaidUsers: 1,
            expectedTotalPrice: 5,
        },
        {
            name: 'base with addon',
            promo: {
                business_percentage_off_per_user_monthly: 13,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_monthly: 13,
                    },
                ],
            },
            expectedMonthlyPricePerUser: 4.35,
            expectedPromo: expect.any(String),
            cycles: WorkspacePaymentsCycle.Monthly,
            billedUsersThisCycle: 1,
            minimumSeats: null,
            freeSeats: null,
            additionalMembers: [],
            expectedPaidUsers: 1,
            expectedTotalPrice: 4.35,
        },
        {
            name: 'with minimum seats without promo code',
            promo: null,
            expectedMonthlyPricePerUser: 5,
            expectedPromo: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            billedUsersThisCycle: 5,
            minimumSeats: 5,
            freeSeats: null,
            additionalMembers: [],
            expectedPaidUsers: 5,
            expectedTotalPrice: 25,
        },
        {
            name: 'with free seats without promo code',
            promo: null,
            expectedMonthlyPricePerUser: 5,
            expectedPromo: null,
            cycles: WorkspacePaymentsCycle.Monthly,
            billedUsersThisCycle: 2,
            minimumSeats: null,
            freeSeats: 2,
            additionalMembers: [
                { role: 2, userid: 4372492 },
                { role: 2, userid: 4372478 },
                { role: 2, userid: 3481112 },
            ],
            expectedPaidUsers: 2,
            expectedTotalPrice: 10,
        },
    ])(
        'should compute addon price with promo code $name to be $expectedPrice per month $cycles',
        async ({
            promo,
            expectedMonthlyPricePerUser,
            expectedPromo,
            expectedPaidUsers,
            cycles,
            billedUsersThisCycle,
            minimumSeats,
            freeSeats,
            additionalMembers,
            expectedTotalPrice,
        }) => {
            await Promise.all([
                context.updateTeamForBillingPurposes(teamId, {
                    minimum_seats: minimumSeats,
                    free_seats: freeSeats,
                    billed_users_this_cycle: billedUsersThisCycle,
                }),
                context.updateTeamBillingInfoForBillingPurposes(teamId, {
                    billed_users_this_cycle: billedUsersThisCycle,
                }),
            ]);
            await context.addUsersToWorkspace(teamId, null, additionalMembers);

            const promoCode = promo
                ? await context.providePromoCode(
                      promo as Partial<
                          PromoCode & { usage_type: 'PERCENTAGE_OFF' | 'FIXED_AMOUNT_OFF' | 'LOCKED_PRICE' }
                      >
                  )
                : null;
            await expect(
                context.getBillingPlanDetails({ teamId, promoCode }, { client }).then(res => {
                    expect(res.addonPrices[cycles]).toBeDefined();
                    return res.addonPrices[cycles].find(e => e.id === 'ai_addon');
                })
            ).resolves.toMatchObject({
                price_per_user_per_month: expectedMonthlyPricePerUser,
                usedPromoCode: expectedPromo,
                addon_paid_users: expectedPaidUsers,
                full_price: expectedTotalPrice,
            });
        }
    );

    describe('addon purchases', () => {
        let pcBusinessMonthlyCombo13: string;
        let pcBusinessMonthlyCombo29: string;

        beforeAll(async () => {
            pcBusinessMonthlyCombo13 = await context.providePromoCode({
                business_percentage_off_per_user_monthly: 13,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_monthly: 13,
                    },
                ],
            } as Partial<PromoCode & { usage_type: 'PERCENTAGE_OFF' | 'FIXED_AMOUNT_OFF' | 'LOCKED_PRICE' }>);

            pcBusinessMonthlyCombo29 = await context.providePromoCode({
                business_percentage_off_per_user_monthly: 29,
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_monthly: 13,
                    },
                ],
            } as Partial<PromoCode & { usage_type: 'PERCENTAGE_OFF' | 'FIXED_AMOUNT_OFF' | 'LOCKED_PRICE' }>);
        });

        beforeEach(async () => {
            // let the previous test finish its unawaited promises
            await new Promise(resolve => setTimeout(resolve, 200));
            // clear the previous test's data only now since the unawaited promises touch subscriptions
            await Promise.all([
                promiseWriteQuery(`DELETE FROM task_mgmt.promo_code_history WHERE team_id = $1`, [teamId]),
            ]);
        });

        it('should charge for minimum seats when purchasing an addon', async () => {
            await context.updateTeamForBillingPurposes(teamId, { minimum_seats: 3 });

            /**
             * For plan, it should also charge us for the minimum seats, so it should be 30, as 3 * 10 = 30
             */
            await context.choosePlan(
                {
                    teamId,
                    planId: PlanIds.Unlimited2305,
                    cycles: WorkspacePaymentsCycle.Monthly,
                },
                { client }
            );

            const nextBillDate = new Date('2030-09-01T00:00:00Z');

            await context.updateTeamForBillingPurposes(teamId, {
                next_bill_date: +nextBillDate,
            });
            await context.updateTeamBillingInfoForBillingPurposes(teamId, {
                next_bill_date: +nextBillDate,
            });

            /** It should charge us for 3 AI addon seats, so 15 as, 3 * 5 = 15 */
            await expect(
                context.purchaseAddon({ teamId, addonId: 'ai_addon', promoCode: null }, { client })
            ).resolves.toMatchObject({ trx_id: expect.any(String) });

            const result = await promiseWriteQuery(
                `SELECT body->>'amount' amount, data->>'discount' discount FROM task_mgmt.transactions WHERE team_id = $1 ORDER BY created ASC`,
                [teamId]
            );

            const expectedAddonPrice = await calculateExpectedAddonPrice();

            expect(result.rows.map(({ amount }) => amount)).toStrictEqual(['30.00', expectedAddonPrice]);
            expect(result.rows.map(({ discount }) => discount)).toStrictEqual(['0.00', '0.00']);
        });

        it('should not charge for free seats when purchasing an addon', async () => {
            await context.updateTeamForBillingPurposes(teamId, { free_seats: 1 });
            await context.addUsersToWorkspace(teamId, null, [{ role: 2, userid: 4372492 }]);

            /**
             * For plan, it shouldn't also charge us for the free seats, so it should be 10, as only
             * one seat should be chargable (one seat is free)
             */
            await context.choosePlan(
                {
                    teamId,
                    planId: PlanIds.Unlimited2305,
                    cycles: WorkspacePaymentsCycle.Monthly,
                },
                { client }
            );

            /** It should charge us for 1 AI addon seat */
            await expect(
                context.purchaseAddon({ teamId, addonId: 'ai_addon', promoCode: null }, { client })
            ).resolves.toMatchObject({ trx_id: expect.any(String) });

            const result = await promiseWriteQuery(
                `SELECT body->>'amount' amount, data->>'discount' discount FROM task_mgmt.transactions WHERE team_id = $1 ORDER BY created ASC`,
                [teamId]
            );

            const expectedAddonPrice = await calculateExpectedAddonPrice();

            expect(result.rows.map(({ amount }) => amount)).toStrictEqual(['10.00', expectedAddonPrice]);
            expect(result.rows.map(({ discount }) => discount)).toStrictEqual(['0.00', '0.00']);
        });

        it('should charge us for at least one AI addon seat', async () => {
            /**
             * 5 free seats can determine that we shouldn't be charged, however we should always
             * bill at least 1 seat.
             */
            await context.updateTeamForBillingPurposes(teamId, { free_seats: 5 });
            await context.addUsersToWorkspace(teamId, null, [{ role: 2, userid: 4372492 }]);

            /**
             * It should charge us for at lesat one seat
             */
            await context.choosePlan(
                {
                    teamId,
                    planId: PlanIds.Unlimited2305,
                    cycles: WorkspacePaymentsCycle.Monthly,
                },
                { client }
            );

            /** It should charge us for single AI seat */
            await expect(
                context.purchaseAddon({ teamId, addonId: 'ai_addon', promoCode: null }, { client })
            ).resolves.toMatchObject({ trx_id: expect.any(String) });

            const result = await promiseWriteQuery(
                `SELECT body->>'amount' amount, data->>'discount' discount FROM task_mgmt.transactions WHERE team_id = $1 ORDER BY created ASC`,
                [teamId]
            );

            const expectedAddonPrice = await calculateExpectedAddonPrice();

            expect(result.rows.map(({ amount }) => amount)).toStrictEqual(['10.00', expectedAddonPrice]);
            expect(result.rows.map(({ discount }) => discount)).toStrictEqual(['0.00', '0.00']);
        });

        it('should allow to set promo code when purchasing an addon', async () => {
            await context.choosePlan(
                {
                    teamId,
                    planId: PlanIds.Unlimited2305,
                    cycles: WorkspacePaymentsCycle.Monthly,
                },
                { client }
            );
            await expect(
                context.purchaseAddon({ teamId, addonId: 'ai_addon', promoCode: pcBusinessMonthlyCombo13 }, { client })
            ).resolves.toMatchObject({ trx_id: expect.any(String) });

            const result = await promiseWriteQuery(
                `SELECT body->>'amount' amount, data->>'discount' discount FROM task_mgmt.transactions WHERE team_id = $1 ORDER BY 1`,
                [teamId]
            );

            const expectedAddonPrice = await calculateExpectedAddonPrice();

            expect(result.rows.map(({ amount }) => amount)).toStrictEqual(['10.00', expectedAddonPrice]);

            await expect(
                promiseWriteQuery(`SELECT promo_code FROM task_mgmt.teams WHERE id = $1`, [teamId]).then(
                    res => res.rows[0].promo_code
                )
            ).resolves.toBeNull();

            await expect(
                promiseWriteQuery(`SELECT * FROM task_mgmt.team_promo_codes WHERE workspace_id = $1`, [teamId]).then(
                    res => res.rows
                )
            ).resolves.toHaveLength(1);

            await expect(
                promiseWriteQuery(
                    `SELECT sum(revenue) revenue FROM task_mgmt.promo_code_history WHERE team_id = $1 AND promo_code = $2`,
                    [teamId, pcBusinessMonthlyCombo13]
                ).then(res => res.rows.map(({ revenue }) => revenue))
            ).resolves.toEqual([Number(expectedAddonPrice)]);

            await expect(
                promiseWriteQuery(
                    `SELECT * FROM task_mgmt.team_audit WHERE team_id = $1 AND action = 'addon_added' ORDER BY date DESC`,
                    [teamId]
                ).then(res => res.rows)
            ).resolves.toMatchObject([
                {
                    data: {
                        newAddon: {
                            addonId: 'ai_addon',
                            total: 1,
                        },
                        reason: 'addon_added',
                        pricing: { promoCode: pcBusinessMonthlyCombo13 },
                    },
                },
            ]);

            await expect(
                context.getTeam({ team_id: teamId }, { client }).then(t => t?.billing_info?.promo_codes)
            ).resolves.toHaveLength(1);
        });

        it('should apply a promo from existing code when purchasing an addon', async () => {
            await context.choosePlan(
                {
                    teamId,
                    planId: PlanIds.Unlimited2305,
                    cycles: WorkspacePaymentsCycle.Monthly,
                },
                { client }
            );

            await context.updateTeamForBillingPurposes(teamId, {}, [
                {
                    promo_code: pcBusinessMonthlyCombo13,
                    product_type: PlanPromoProductType,
                    cycles_applied: 0,
                },
                {
                    promo_code: pcBusinessMonthlyCombo13,
                    product_type: AddonType.Ai,
                    cycles_applied: 0,
                },
            ]);

            await expect(context.purchaseAddon({ teamId, addonId: 'ai_addon' }, { client })).resolves.toMatchObject({
                trx_id: expect.any(String),
            });

            const result = await promiseWriteQuery(
                `SELECT body->>'amount' amount, data->>'discount' discount FROM task_mgmt.transactions WHERE team_id = $1 ORDER BY 1`,
                [teamId]
            );

            const expectedAddonPrice = await calculateExpectedAddonPrice();

            expect(result.rows.map(({ amount }) => amount)).toStrictEqual(['10.00', expectedAddonPrice]);

            await expect(
                promiseWriteQuery(
                    `SELECT sum(revenue) revenue FROM task_mgmt.promo_code_history WHERE team_id = $1 AND promo_code = $2`,
                    [teamId, pcBusinessMonthlyCombo13]
                ).then(res => res.rows.map(({ revenue }) => revenue))
            ).resolves.toEqual([Number(expectedAddonPrice)]);

            await expect(
                promiseWriteQuery(
                    `SELECT * FROM task_mgmt.team_audit WHERE team_id = $1 AND action = 'addon_added' ORDER BY date DESC`,
                    [teamId]
                ).then(res => res.rows)
            ).resolves.toMatchObject([
                {
                    data: {
                        newAddon: {
                            addonId: 'ai_addon',
                            total: 1,
                        },
                        reason: 'addon_added',
                        pricing: { promoCode: pcBusinessMonthlyCombo13 },
                    },
                },
            ]);
        });

        it('should not allow to set yearly promo code when purchasing an addon on a monthly plan', async () => {
            const pcBusinessYearlyCombo13 = await context.providePromoCode({
                business_percentage_off_per_user_yearly: 13,
                valid_yearly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_yearly: 13,
                    },
                ],
            } as Partial<PromoCode & { usage_type: 'PERCENTAGE_OFF' | 'FIXED_AMOUNT_OFF' | 'LOCKED_PRICE' }>);

            await context.choosePlan(
                {
                    teamId,
                    planId: PlanIds.Unlimited2305,
                    cycles: WorkspacePaymentsCycle.Monthly,
                    addons: [],
                },
                { client }
            );
            await expect(
                context.purchaseAddon({ teamId, addonId: 'ai_addon', promoCode: pcBusinessYearlyCombo13 }, { client })
            ).rejects.toMatchObject({
                res: { json: { ECODE: 'PROMO_010', err: 'Promotion not valid for monthly plan' } },
            });

            const result = await promiseWriteQuery(
                `SELECT body->>'amount' amount FROM task_mgmt.transactions WHERE team_id = $1 ORDER BY 1`,
                [teamId]
            );
            expect(result.rows.map(({ amount }) => amount)).toStrictEqual(['10.00']);

            await expect(
                promiseWriteQuery(`SELECT promo_code FROM task_mgmt.teams WHERE id = $1`, [teamId]).then(
                    res => res.rows[0].promo_code
                )
            ).resolves.toBeNull();

            await expect(
                promiseWriteQuery(`SELECT * FROM task_mgmt.team_promo_codes WHERE workspace_id = $1`, [teamId]).then(
                    res => res.rows
                )
            ).resolves.toHaveLength(0);
        });

        it('should not block to change promo codes when purchasing addons', async () => {
            const firstPromoCode = pcBusinessMonthlyCombo13;
            const secondPromoCode = pcBusinessMonthlyCombo29;

            await context.choosePlan(
                {
                    teamId,
                    planId: PlanIds.BusinessPlus,
                    cycles: WorkspacePaymentsCycle.Monthly,
                    addons: [],
                },
                { client }
            );

            await context.updateTeamForBillingPurposes(teamId, {}, [
                {
                    promo_code: firstPromoCode,
                    product_type: PlanPromoProductType,
                    cycles_applied: 0,
                },
            ]);

            await context.purchaseAddon({ teamId, addonId: 'ai_addon', promoCode: secondPromoCode }, { client });

            await expect(
                promiseWriteQuery(`SELECT promo_code FROM task_mgmt.teams WHERE id = $1`, [teamId]).then(
                    res => res.rows[0].promo_code
                )
            ).resolves.toBeNull();

            await expect(
                promiseWriteQuery(`SELECT * FROM task_mgmt.team_promo_codes WHERE workspace_id = $1`, [teamId]).then(
                    res => res.rows
                )
            ).resolves.toHaveLength(2);
        });

        it('should not discount plan value gained from price of addon purchase when adding promo to addon purchase', async () => {
            await context.choosePlan(
                {
                    teamId,
                    planId: PlanIds.Business,
                    cycles: WorkspacePaymentsCycle.Monthly,
                },
                { client }
            );

            await expect(
                context.purchaseAddon({ teamId, addonId: 'ai_addon', promoCode: pcBusinessMonthlyCombo13 }, { client })
            ).resolves.toMatchObject({
                trx_id: expect.any(String),
            });

            const expectedAddonCost = await calculateExpectedAddonPrice();

            // first transaction: $19 - full price, no promo
            // nb. second transaction could be $1.88:
            //     13% plan+addon promo added, so charging 87% of addon (4.35) and discounting 13% of plan (2.47): 4.35 - 2.47 = 1.88
            // this test is *normative*: we don't want to do that
            await expect(
                promiseWriteQuery(
                    `SELECT body->>'amount' amount, data->>'discount' discount FROM task_mgmt.transactions WHERE team_id = $1 ORDER BY 1`,
                    [teamId]
                ).then(res => res.rows.map(({ amount, discount }) => ({ amount, discount })))
            ).resolves.toMatchObject([{ amount: '19.00' }, { amount: expectedAddonCost }]); // NORMATIVE: just the addon gets discounted, not the plan

            await expect(
                promiseWriteQuery(
                    `SELECT * FROM task_mgmt.team_audit WHERE team_id = $1 AND action = 'addon_added' ORDER BY date DESC`,
                    [teamId]
                ).then(res => res.rows)
            ).resolves.toMatchObject([
                {
                    data: {
                        newAddon: {
                            addonId: 'ai_addon',
                            total: 1,
                        },
                        reason: 'addon_added',
                        pricing: { promoCode: pcBusinessMonthlyCombo13 },
                    },
                },
            ]);

            await expect(
                context.getTeam({ team_id: teamId }, { client }).then(t => t?.billing_info?.promo_codes)
            ).resolves.toMatchObject([
                {
                    promo_code: pcBusinessMonthlyCombo13,
                },
            ]);
        });

        it('should have correct discount recorded in subscriptions when 100% off', async () => {
            const promoCode = await context.providePromoCode({
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'ai_addon',
                        percentage_off_monthly: 100,
                    },
                ],
            } as Partial<PromoCode & { usage_type: 'PERCENTAGE_OFF' | 'FIXED_AMOUNT_OFF' | 'LOCKED_PRICE' }>);

            await context.choosePlan(
                {
                    teamId,
                    planId: PlanIds.Business,
                    cycles: WorkspacePaymentsCycle.Monthly,
                },
                { client }
            );

            // addon purchased for free: no transaction recorded
            // frontend does not utilise trx_id information anyway
            await expect(
                context.purchaseAddon({ teamId, addonId: SpecificAddonType.AiAddon, promoCode }, { client })
            ).resolves.toStrictEqual({
                trx_id: null,
                skuInfo: { sourceSku: null, targetSku: SpecificAddonType.AiAddon },
            });
        });

        it('should have correct discount recorded in subscriptions when purchasing non-per-seat addon', async () => {
            const promoCode = await context.providePromoCode({
                valid_monthly: true,
                usage_type: 'PERCENTAGE_OFF',
                addons: [
                    {
                        addon_id: 'productivity_starter_pack',
                        percentage_off_monthly: 10,
                    },
                    {
                        addon_id: 'productivity_power_pack',
                        percentage_off_monthly: 20,
                    },
                ],
            } as Partial<PromoCode & { usage_type: 'PERCENTAGE_OFF' | 'FIXED_AMOUNT_OFF' | 'LOCKED_PRICE' }>);

            await context.choosePlan(
                {
                    teamId,
                    planId: PlanIds.Business,
                    cycles: WorkspacePaymentsCycle.Monthly,
                },
                { client }
            );

            const nextBillDate = new Date('2030-09-01T00:00:00Z');

            await context.updateTeamForBillingPurposes(teamId, {
                next_bill_date: +nextBillDate,
            });
            await context.updateTeamBillingInfoForBillingPurposes(teamId, {
                next_bill_date: +nextBillDate,
            });

            await expect(
                context.purchaseAddon(
                    { teamId, addonId: 'productivity_starter_pack', promoCode, amount: 1 },
                    { client }
                )
            ).resolves.toMatchObject({
                trx_id: expect.any(String),
            });
        });
    });
});
