/**
 * @group functional/models/views
 */

import ClickUpContext from '../../../../src/jest-context/ClickUpContext';
import { VIEW_PARENT_TYPES } from '../../../../src/jest-context/helpers/view';

describe('/default_views endpoint', () => {
    const context = new ClickUpContext();

    it('should fail for invalid parents', async () => {
        let raisedErr = null;

        try {
            await context.getDefaultViews({ parentType: VIEW_PARENT_TYPES.task, parentId: 'some-task-id' });
        } catch (err) {
            raisedErr = err;
        }

        expect(raisedErr.res.status).toEqual(400);

        const responseBody = JSON.parse(raisedErr.res.text);
        expect(responseBody.err).toEqual('parent_type is invalid');
        expect(responseBody.ECODE).toEqual('DEFAULT_VIEWS_002');
    });

    describe('initial views state', () => {
        it('should return the default data (nulls mostly)', async () => {
            const project = await context.provideProject({ name: 'Some space' });
            const category = await context.provideCategory({ name: 'Some folder', projectId: project.id });
            const subcategory = await context.provideSubcategory({ name: 'Some list', categoryId: category.id });
            const response = await context.getDefaultViews({
                parentType: VIEW_PARENT_TYPES.subcategory,
                parentId: subcategory.id,
            });

            expect(response).toEqual(
                expect.objectContaining({
                    views: [],
                    standard_views: {
                        board: null,
                        list: null,
                        calendar: null,
                        gantt_type: null,
                        box: null,
                        activity: null,
                        timeline: null,
                        workload: null,
                        mind_map: null,
                        table: null,
                        map: null,
                        dashboard: null,
                    },
                    default_view: null,
                    groups: [],
                    last_page: true,
                    paging: {},
                    required_templates: {},
                    project_id: project.id,
                    permissions: expect.any(Object),
                })
            );
        });
    });

    describe('updated views state', () => {
        let teamId: string;
        let projectId: string;
        let categoryId: string;
        let subcategoryId: string;

        beforeAll(async () => {
            teamId = (await context.provideTeam({ name: 'Some team' })).id;
            projectId = (await context.provideProject({ name: 'Some other space', teamId })).id;
            categoryId = (await context.provideCategory({ name: 'Some folder', projectId })).id;
            subcategoryId = (await context.provideSubcategory({ name: 'Some list', categoryId })).id;

            const sourceBoardView = await context.provideView({ name: 'Board view template source', viewType: 2 });
            const boardViewTemplate = await context.provideViewTemplate(
                { sourceViewId: sourceBoardView.id },
                { name: 'Board view template' }
            );

            await context.projectUpdate({ projectId, board_view_template: boardViewTemplate.id });
        });

        it('should return correct data for subcategory', async () => {
            await context.provideView({
                name: 'Standard list view',
                parentSubcategoryId: subcategoryId,
                standardView: true,
                viewType: 1,
            });

            await context.provideView({
                name: 'Default view',
                parentSubcategoryId: subcategoryId,
                viewType: 1,
                additionalSettings: { default: true },
            });

            await context.provideView({
                name: 'Custom list view',
                parentSubcategoryId: subcategoryId,
                viewType: 1,
            });

            const response = await context.getDefaultViews({
                parentType: VIEW_PARENT_TYPES.subcategory,
                parentId: subcategoryId,
            });

            expect(response).toEqual(
                expect.objectContaining({
                    views: [
                        expect.objectContaining({
                            name: 'Default view',
                        }),
                    ],
                    standard_views: {
                        board: null,
                        list: expect.objectContaining({
                            name: 'Standard list view',
                        }),
                        calendar: null,
                        gantt_type: null,
                        box: null,
                        activity: null,
                        timeline: null,
                        workload: null,
                        mind_map: null,
                        table: null,
                        map: null,
                        dashboard: null,
                    },
                    default_view: expect.objectContaining({
                        name: 'Default view',
                    }),
                    groups: [],
                    last_page: true,
                    paging: {},
                    required_templates: {
                        board_view_template: expect.objectContaining({
                            name: 'Board view template',
                        }),
                    },
                    project_id: projectId,
                    permissions: expect.any(Object),
                })
            );
        });

        it('should return correct data for category', async () => {
            await context.provideView({
                name: 'Standard list view',
                parentCategoryId: categoryId,
                standardView: true,
                viewType: 1,
            });

            await context.provideView({
                name: 'Default view',
                parentCategoryId: categoryId,
                viewType: 1,
                additionalSettings: { default: true },
            });

            await context.provideView({
                name: 'Custom list view',
                parentCategoryId: categoryId,
                viewType: 1,
            });

            const response = await context.getDefaultViews({
                parentType: VIEW_PARENT_TYPES.category,
                parentId: categoryId,
            });

            expect(response).toEqual(
                expect.objectContaining({
                    views: [
                        expect.objectContaining({
                            name: 'Default view',
                        }),
                    ],
                    standard_views: {
                        board: null,
                        list: expect.objectContaining({
                            name: 'Standard list view',
                        }),
                        calendar: null,
                        gantt_type: null,
                        box: null,
                        activity: null,
                        timeline: null,
                        workload: null,
                        mind_map: null,
                        table: null,
                        map: null,
                        dashboard: null,
                    },
                    default_view: expect.objectContaining({
                        name: 'Default view',
                    }),
                    groups: [],
                    last_page: true,
                    paging: {},
                    required_templates: {
                        board_view_template: expect.objectContaining({
                            name: 'Board view template',
                        }),
                    },
                    project_id: projectId,
                    permissions: expect.any(Object),
                })
            );
        });

        it('should return correct data for project', async () => {
            await context.provideView({
                name: 'Standard list view',
                parentProjectId: projectId,
                standardView: true,
                viewType: 1,
            });

            await context.provideView({
                name: 'Default view',
                parentProjectId: projectId,
                viewType: 1,
                additionalSettings: { default: true },
            });

            await context.provideView({
                name: 'Custom list view',
                parentProjectId: projectId,
                viewType: 1,
            });

            const response = await context.getDefaultViews({
                parentType: VIEW_PARENT_TYPES.project,
                parentId: projectId,
            });

            expect(response).toEqual(
                expect.objectContaining({
                    views: [
                        expect.objectContaining({
                            name: 'Overview',
                        }),
                        expect.objectContaining({
                            name: 'Default view',
                        }),
                    ],
                    standard_views: {
                        board: null,
                        list: expect.objectContaining({
                            name: 'Standard list view',
                        }),
                        calendar: null,
                        gantt_type: null,
                        box: null,
                        activity: null,
                        timeline: null,
                        workload: null,
                        mind_map: null,
                        table: null,
                        map: null,
                        dashboard: null,
                    },
                    default_view: expect.objectContaining({
                        name: 'Default view',
                    }),
                    groups: [],
                    last_page: true,
                    paging: {},
                    required_templates: {
                        board_view_template: expect.objectContaining({
                            name: 'Board view template',
                        }),
                    },
                    project_id: projectId,
                    permissions: expect.any(Object),
                })
            );
        });

        it('should return correct data for team', async () => {
            await context.provideView({
                name: 'Standard list view',
                parentTeamId: teamId,
                standardView: true,
                viewType: 1,
            });

            await context.provideView({
                name: 'Default view',
                parentTeamId: teamId,
                viewType: 1,
                additionalSettings: { default: true },
            });

            await context.provideView({
                name: 'Custom list view',
                parentTeamId: teamId,
                viewType: 1,
            });

            const response = await context.getDefaultViews({
                parentType: VIEW_PARENT_TYPES.team,
                parentId: teamId,
            });

            expect(response).toEqual(
                expect.objectContaining({
                    views: [
                        expect.objectContaining({
                            name: 'Default view',
                        }),
                    ],
                    standard_views: {
                        board: null,
                        list: expect.objectContaining({
                            name: 'Standard list view',
                        }),
                        calendar: null,
                        gantt_type: null,
                        box: null,
                        activity: null,
                        timeline: null,
                        workload: null,
                        mind_map: null,
                        table: null,
                        map: null,
                        dashboard: null,
                    },
                    default_view: expect.objectContaining({
                        name: 'Default view',
                    }),
                    groups: [],
                    last_page: true,
                    paging: {},
                    permissions: expect.any(Object),
                })
            );
        });
    });
});
