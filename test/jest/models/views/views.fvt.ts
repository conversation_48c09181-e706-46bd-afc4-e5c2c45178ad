import config from 'config';
import { omit } from 'lodash';

import { randomUUID } from 'crypto';
import { RestClient } from '@clickup/rest-client';
import { DbPool } from '@clickup/data-platform/pg-pools';
import { PlanIds } from '@clickup/billing/types';
import ClickUpContext from '../../../../src/jest-context/ClickUpContext';
import { addMembers, deleteMembers } from '../../../../src/models/views/members/services/viewMemberService';
import { embedPaywallService } from '../../../../src/models/views/paywalls/services/embedPaywallService';
import { defaultViewPaywallService } from '../../../../src/models/views/paywalls/services/defaultViewPaywallService';
import { mapPaywallService } from '../../../../src/models/views/paywalls/services/mapPaywallService';
import {
    _getEditQueriesAsync,
    _promiseGetViews,
    createViewAsync,
    editViewAsync,
} from '../../../../src/models/views/views';
import { NON_DETERMINISTIC_VIEW_KEYS } from './constants';
import { ProvidedUser } from '../../../../src/jest-context/providers/users/provideUser';
import { generateAppliedObjectsJSON } from '../../../../src/models/field/factories/__tests__/field-parent-filter-test-utils';

async function bootstrapDashboardWithDrilldownView(
    context: ClickUpContext,
    args: {
        teamId: string;
        ownerClient: RestClient;
        dashboardMember?: {
            permission_level: number;
            id: number;
        };
    }
) {
    const { teamId, ownerClient, dashboardMember } = args;

    const dashboard = await context.provideDashboard({ teamId }, { client: ownerClient });

    if (dashboardMember) {
        await context.addDashboardMembers(
            {
                dashboard_id: dashboard.id,
                permission_level: dashboardMember.permission_level,
                members: [dashboardMember.id],
            },
            { client: ownerClient }
        );
    }

    const widget = await context.dashboardWidgetV1Create(ownerClient, {
        dashboardId: dashboard.id,
        location: [],
    });
    const drilldownView = await context.widgetDrilldownViewCreate(ownerClient, {
        widgetId: widget.id,
        view: { name: 'cool name', parentTeamId: teamId, columns: { fields: [] } },
    });

    return {
        dashboard,
        widget,
        drilldownView,
    };
}

/**
 * @group functional/models/views
 * @group views
 */
describe('_promiseGetViews', () => {
    jest.setTimeout(60_000);

    const context = new ClickUpContext();
    let user: { id: number };
    let team: { id: string };
    let project: { id: string };
    let category: { id: string };
    let subcategory: { id: string };

    beforeAll(async () => {
        user = await context.provideUser();
        team = await context.provideTeam({
            plan: PlanIds.FreeForever,
        });
        project = await context.provideProject({
            teamId: team.id,
        });
        category = await context.provideCategory({ projectId: project.id });
        subcategory = await context.provideSubcategory({ categoryId: category.id });
    });

    it('Single view matches snapshot', async () => {
        const view = await context.provideView({
            parentSubcategoryId: subcategory.id,
            viewType: config.get('views.view_types.list'),
            name: 'List',
            grouping: {
                collapsed: [],
                dir: 1,
                field: 'none',
                ignore: false,
            },
        });

        const { views } = await _promiseGetViews(user.id, { view_id: view.id });
        const deterministicViews = views.map((value: any) => omit(value, NON_DETERMINISTIC_VIEW_KEYS));
        expect(deterministicViews).toMatchSnapshot();
    });

    it('Multiple views match snapshot', async () => {
        const view1 = await context.provideView({
            parentSubcategoryId: subcategory.id,
            viewType: config.get('views.view_types.list'),
            name: 'List 1',
            grouping: {
                collapsed: [],
                dir: 1,
                field: 'none',
                ignore: false,
            },
        });
        const view2 = await context.provideView({
            parentSubcategoryId: subcategory.id,
            viewType: config.get('views.view_types.list'),
            name: 'List 2',
            grouping: {
                collapsed: [],
                dir: 1,
                field: 'none',
                ignore: false,
            },
        });

        const { views } = await _promiseGetViews(user.id, {
            parent_id: subcategory.id,
            parent_type: config.get('views.parent_types.subcategory'),
            in_view_ids: [view1.id, view2.id],
        });
        const deterministicViews = views.map((value: any) => omit(value, NON_DETERMINISTIC_VIEW_KEYS));
        expect(deterministicViews).toMatchSnapshot();
    });

    it('Views by parent match snapshot', async () => {
        const view = await context.provideView({
            parentSubcategoryId: subcategory.id,
            viewType: config.get('views.view_types.list'),
            name: 'List',
            grouping: {
                collapsed: [],
                dir: 1,
                field: 'none',
                ignore: false,
            },
        });

        const { views } = await _promiseGetViews(user.id, {
            parent_id: subcategory.id,
            parent_type: config.get('views.parent_types.subcategory'),
        });
        const deterministicViews = views.map((value: any) => omit(value, NON_DETERMINISTIC_VIEW_KEYS));
        expect(deterministicViews).toMatchSnapshot();
    });

    it('board view settings', async () => {
        const view = await context.provideView({
            parentSubcategoryId: subcategory.id,
            viewType: config.get('views.view_types.board'),
            name: 'Board',
            grouping: {
                collapsed: [],
                dir: 1,
                field: 'none',
                ignore: false,
            },
        });

        const { views } = await _promiseGetViews(user.id, { view_id: view.id });
        const expectedBoardSettings = {
            show_task_locations: false,
            show_subtasks: 1,
            show_subtask_parent_names: false,
            show_closed_subtasks: false,
            show_assignees: true,
            show_images: true,
            show_timer: false,
            fast_load_mode: false,
            collapse_empty_columns: null,
            me_comments: true,
            me_subtasks: true,
            override_parent_hierarchy_filter: false,
            me_checklists: true,
            show_empty_statuses: false,
            auto_wrap: false,
            time_in_status_view: 1,
            is_description_pinned: false,
            show_task_ids: false,
            task_cover: 1,
            field_rendering: 1,
            colored_columns: false,
            card_size: 2,
            show_empty_fields: true,
            show_task_properties: true,
        };

        expect(views[0].settings).toEqual(expectedBoardSettings);
    });

    describe('Paywalls V2', () => {
        describe('Map view', () => {
            let view: { id: string };

            beforeAll(async () => {
                view = await context.provideView({
                    viewType: config.get('views.view_types.map'),
                    grouping: {},
                });
            });

            it('Increments map usage', async () => {
                const limit = Number(config.get(`paywalls.map_view.limits.${PlanIds.FreeForever}`));
                await mapPaywallService.incrementLimit({ teamId: team.id }, limit - 2); // 1 counts for provide view
                const params = {
                    view_id: view.id,
                    from_request: true,
                };
                await _promiseGetViews(user.id, params);
            });

            it('Throws error when the map view usage limit is reached', async () => {
                const limit = Number(config.get(`paywalls.map_view.limits.${PlanIds.FreeForever}`));
                await expect(
                    _promiseGetViews(user.id, {
                        view_id: view.id,
                        from_request: true,
                    })
                ).rejects.toThrow(
                    expect.objectContaining({
                        err: `Your plan is limited to ${limit} usages of Map View, ${limit} usage.`,
                    })
                );
            });
        });

        it('Fails when view parent types do not exist', async () => {
            await expect(
                _promiseGetViews(user.id, {
                    parent_id: -1,
                    parent_type: config.get('views.parent_types.subcategory'),
                    skipAccess: true,
                })
            ).rejects.toThrow(
                expect.objectContaining({
                    msg: 'List not found',
                })
            );
        });

        // skip until plans actually change
        it.skip('Shows an Embed paywall when over the usage limit', async () => {
            const view = await context.provideView({
                parentSubcategoryId: subcategory.id,
                viewType: config.get('views.view_types.embed'),
                name: 'Embed View',
                embed_settings: {
                    url: 'https://www.youtube.com/watch?v=4iQmPv_dTI0',
                },
                grouping: {
                    collapsed: [],
                    dir: 1,
                    field: 'none',
                    ignore: false,
                },
            });
            await embedPaywallService.incrementLimit({ teamId: team.id }, 101);

            await expect(async () => {
                await _promiseGetViews(user.id, { view_id: view.id });
            }).rejects.toThrowError();
        });
    });
});

describe('_getEditQueriesAsync', () => {
    jest.setTimeout(60_000);

    const context = new ClickUpContext();
    let user: { id: number };

    let enterpriseTeam: { id: string };
    let enterpriseProject: { id: string };
    let enterpriseFolder: { id: string };
    let enterpriseList: { id: string };

    let freeTeam: { id: string };
    let freeProject: { id: string };
    let freeFolder: { id: string };
    let freeList: { id: string };

    beforeAll(async () => {
        user = await context.provideUser();

        enterpriseTeam = await context.provideTeam({
            plan: PlanIds.Enterprise,
        });
        enterpriseProject = await context.provideProject({
            teamId: enterpriseTeam.id,
        });
        enterpriseFolder = await context.provideCategory({ projectId: enterpriseProject.id });
        enterpriseList = await context.provideSubcategory({ categoryId: enterpriseFolder.id });

        freeTeam = await context.provideTeam({
            plan: PlanIds.FreeForever,
        });
        freeProject = await context.provideProject({
            teamId: freeTeam.id,
        });
        freeFolder = await context.provideCategory({ projectId: freeProject.id });
        freeList = await context.provideSubcategory({ categoryId: freeFolder.id });
    });

    it('Fetches bulk queries with permissions', async () => {
        const view = await context.provideView({
            parentSubcategoryId: enterpriseList.id,
            viewType: config.get('views.view_types.list'),
            grouping: {},
            additionalSettings: {
                public_share_expires_on: 1,
                public: true,
            },
        });

        await expect(
            _getEditQueriesAsync(user.id, view.id, {
                parent: {
                    id: enterpriseList.id,
                    type: config.get('views.parent_types.subcategory'),
                },
                disable_never_expire_pub_links: true,
                plan_id: PlanIds.Enterprise,
                public: true,
            })
        ).resolves.not.toThrowError();
    });

    it('filters out private fields', async () => {
        const context2 = new ClickUpContext();
        const user2 = await context2.provideUser();
        const field = await context.provideCustomField({
            private: true,
            subcategory_id: enterpriseList.id,
        });
        const view = await context.provideView({
            parentSubcategoryId: enterpriseList.id,
            viewType: config.get('views.view_types.list'),
            additionalSettings: {
                public: true,
            },
        });

        await context.teamAddUser(enterpriseTeam.id, user2.id, 5);
        await context.subcategoryAddMember({
            subcategory_id: enterpriseList.id,
            member: {
                userid: user2.id,
                permission_level: 5,
            },
        });

        const queries = await _getEditQueriesAsync(user2.id, view.id, {
            parent: {
                id: enterpriseList.id,
                type: config.get('views.parent_types.subcategory'),
            },
            columns: {
                fields: [
                    {
                        field: `cf_${field.id}`,
                        width: 123,
                        hidden: false,
                    },
                ],
            },
        });

        expect(queries.fields_added).toBe(false);
    });

    describe('Paywalls V2', () => {
        it('Checks entitlements without an error', async () => {
            const view = await context.provideView({
                parentSubcategoryId: enterpriseList.id,
                viewType: config.get('views.view_types.list'),
                grouping: {},
                additionalSettings: {
                    public: true,
                },
            });

            await expect(
                _getEditQueriesAsync(user.id, view.id, {
                    parent: {
                        id: enterpriseList.id,
                        type: config.get('views.parent_types.subcategory'),
                    },
                    plan_id: PlanIds.Enterprise,
                    public: true,
                })
            ).resolves.not.toThrowError();
        });
    });
});

describe('createViewAsync', () => {
    jest.setTimeout(60_000);

    const context = new ClickUpContext();
    let user: { id: number };

    let enterpriseTeam: { id: string };
    let enterpriseProject: { id: string };
    let enterpriseFolder: { id: string };
    let enterpriseList: { id: string };

    let unlimitedTeam: { id: string };
    let unlimitedProject: { id: string };
    let unlimitedFolder: { id: string };
    let unlimitedList: { id: string };

    let businessTeam: { id: string };
    let businessProject: { id: string };
    let businessFolder: { id: string };
    let businessList: { id: string };

    let freeTeam: { id: string };
    let freeProject: { id: string };
    let freeFolder: { id: string };
    let freeList: { id: string };

    let Business2503Team: { id: string };
    let Business2503Project: { id: string };
    let Business2503Folder: { id: string };

    const baseView: any = {
        name: 'View Name',
        visibility: 1,
        me_view: false,
        id: '',
        grouping: {},
        embed_settings: {},
        divide: {},
        settings: {
            show_task_locations: false,
            show_timer: false,
            show_subtasks: 1,
            show_subtask_parent_names: false,
            me_comments: true,
            me_subtasks: true,
            me_checklists: true,
            show_closed_subtasks: false,
            show_task_ids: false,
            show_empty_statuses: false,
            time_in_status_view: 1,
            auto_wrap: false,
        },
        members: [],
        group_members: [],
        sorting: { fields: [] },
        filters: {
            show_closed: false,
            search_custom_fields: false,
            search_description: false,
            fields: [],
        },
        columns: { fields: [] },
        permissions: {
            edit_view: true,
            delete_view: true,
            can_unprotect: true,
            comment: true,
        },
        auto_save: false,
        board_settings: {},
        type: 1,
        standard_view: false,
        sidebar_view: false,
        create_page: true,
        user_filter_settings: true,
        form_settings: { display: {}, fields: [] },
        public_share_expires_on: 1,
        public: true,
        skip_access: false,
        skipAccess: false,
        default_skip_access: false,
        dontFail: false,
        skip_privacy_check: false,
        ws_key: 5152164112,
        from_request: true,
    };

    beforeAll(async () => {
        user = await context.provideUser();

        enterpriseTeam = await context.provideTeam({
            plan: PlanIds.Enterprise,
        });
        enterpriseProject = await context.provideProject({
            teamId: enterpriseTeam.id,
        });
        enterpriseFolder = await context.provideCategory({ projectId: enterpriseProject.id });
        enterpriseList = await context.provideSubcategory({ categoryId: enterpriseFolder.id });

        businessTeam = await context.provideTeam({
            plan: PlanIds.Business,
        });
        businessProject = await context.provideProject({
            teamId: businessTeam.id,
        });
        businessFolder = await context.provideCategory({ projectId: businessProject.id });
        businessList = await context.provideSubcategory({ categoryId: businessFolder.id });

        unlimitedTeam = await context.provideTeam({
            plan: PlanIds.Unlimited,
        });
        unlimitedProject = await context.provideProject({
            teamId: unlimitedTeam.id,
        });
        unlimitedFolder = await context.provideCategory({ projectId: unlimitedProject.id });
        unlimitedList = await context.provideSubcategory({ categoryId: unlimitedFolder.id });

        freeTeam = await context.provideTeam({
            plan: PlanIds.FreeForever2306,
        });
        freeProject = await context.provideProject({
            teamId: freeTeam.id,
        });
        freeFolder = await context.provideCategory({ projectId: freeProject.id });
        freeList = await context.provideSubcategory({ categoryId: freeFolder.id });

        Business2503Team = await context.provideTeam({ plan: PlanIds.Business2503 });
        Business2503Project = await context.provideProject({ teamId: Business2503Team.id });
        Business2503Folder = await context.provideCategory({ projectId: Business2503Project.id });
    });

    it('Created view matches snapshot', async () => {
        const { view } = await createViewAsync(user.id, {
            ...baseView,
            parent: {
                type: config.get('views.parent_types.subcategory'),
                id: enterpriseList.id,
            },
        });
        expect(omit(view, NON_DETERMINISTIC_VIEW_KEYS)).toMatchSnapshot();
    });

    it('Created views set the correct user, team, and parent', async () => {
        const { view } = await createViewAsync(user.id, {
            ...baseView,
            parent: {
                type: config.get('views.parent_types.subcategory'),
                id: freeList.id,
            },
        });

        expect(view.creator).toBe(user.id);
        expect(view.team_id).toBe(freeTeam.id);
        expect(view.parent.id).toBe(freeList.id);
    });

    it('Calendar view should be created with showRecurringTasks false', async () => {
        const { view } = await createViewAsync(user.id, {
            ...baseView,
            parent: {
                type: config.get('views.parent_types.subcategory'),
                id: freeList.id,
            },
            type: config.get('views.view_types.calendar'),
        });

        expect(view.calendar_settings.showRecurringTasks).toBe(false);
    });

    describe('Paywalls', () => {
        describe('Map view', () => {
            it('Does not increment map usage when map view is created', async () => {
                const limit = Number(config.get(`paywalls.map_view.limits.${PlanIds.FreeForever}`));
                await mapPaywallService.incrementLimit({ teamId: freeTeam.id }, limit - 1);
                const params = {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.team'),
                        id: freeTeam.id,
                    },
                    public: false,
                    type: config.get('views.view_types.map'),
                };
                await createViewAsync(user.id, params);
                await createViewAsync(user.id, params);
            });

            it('Throws error when the map view usage limit is reached', async () => {
                const limit = Number(config.get(`paywalls.map_view.limits.${PlanIds.FreeForever}`));
                await mapPaywallService.incrementLimit({ teamId: freeTeam.id }, 1);

                const expectedErr = `[PaywallClickUpError: Your plan is limited to ${limit} usages of Map View, ${limit} usage.]`;
                await expect(
                    createViewAsync(user.id, {
                        ...baseView,
                        parent: {
                            type: config.get('views.parent_types.team'),
                            id: freeTeam.id,
                        },
                        public: false,
                        type: config.get('views.view_types.map'),
                    })
                ).rejects.toMatchInlineSnapshot(expectedErr);
            });
        });

        it('Throws an error when trying to create a private doc on the free plan', async () => {
            await expect(async () => {
                await createViewAsync(user.id, {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.team'),
                        id: freeTeam.id,
                    },

                    type: config.get('views.view_types.doc'),
                    visibility: config.get('views.visibility.private'),
                    public: false,
                });
            }).rejects.toMatchInlineSnapshot(`[ViewsClickUpError: Upgrade your team to make views private]`);
        });

        it('Throws an error when trying to create a private view on the unlimited plan', async () => {
            await expect(async () => {
                await createViewAsync(user.id, {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.team'),
                        id: unlimitedTeam.id,
                    },

                    type: config.get('views.view_types.list'),
                    visibility: config.get('views.visibility.private'),
                    public: false,
                });
            }).rejects.toMatchInlineSnapshot(`[ViewsClickUpError: Upgrade your team to make views private]`);
        });

        it('Allows creating a private view on the business plan', async () => {
            await expect(
                createViewAsync(user.id, {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.subcategory'),
                        id: businessList.id,
                    },
                    type: config.get('views.view_types.list'),
                    visibility: config.get('views.visibility.private'),
                    public: false,
                })
            ).resolves.not.toThrowError();
        });

        it('Throws an error when trying to create team view on free plan', async () => {
            await expect(async () => {
                await createViewAsync(user.id, {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.team'),
                        id: freeTeam.id,
                    },
                    type: config.get('views.view_types.box'),
                });
            }).rejects.toMatchInlineSnapshot(`[ViewsClickUpError: Upgrade your team to use box views]`);
        });

        it('Allows enterprise plan to create a team view', async () => {
            await expect(
                createViewAsync(user.id, {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.team'),
                        id: enterpriseTeam.id,
                    },
                    type: config.get('views.view_types.box'),
                })
            ).resolves.not.toThrowError();
        });

        it('Throws an error when trying to create shared everything view on free plan', async () => {
            await expect(async () => {
                await createViewAsync(user.id, {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.team'),
                        id: freeTeam.id,
                    },

                    public: true,
                });
            }).rejects.toMatchInlineSnapshot(
                `[ViewsClickUpError: Only business plans or higher can share everything views]`
            );
        });

        it('Allows enterprise plan to create a shared everything view', async () => {
            const view = await context.provideView({
                parentTeamId: enterpriseTeam.id,
                viewType: config.get('views.view_types.list'),
                grouping: {},
            });

            await expect(
                createViewAsync(user.id, {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.team'),
                        id: enterpriseTeam.id,
                    },
                    public: true,
                })
            ).resolves.not.toThrowError();
        });

        it('Throws an error when trying to make a view as default over the usage limit on Business2503 plan', async () => {
            await defaultViewPaywallService.incrementLimit({ teamId: Business2503Team.id }, 5);
            await expect(async () => {
                await createViewAsync(user.id, {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.team'),
                        id: Business2503Team.id,
                    },
                    public: false,
                    default: true,
                });
            }).rejects.toMatchInlineSnapshot(
                `[PaywallClickUpError: Your plan is limited to {{limit}} usages of Default View, {{usage}} usage.]`
            );
        });

        it('Allow FreeForever plan to create location_overview default view', async () => {
            await expect(
                createViewAsync(user.id, {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.team'),
                        id: freeTeam.id,
                    },
                    type: config.get('views.view_types.location_overview'),
                    default: true,
                })
            ).resolves.not.toThrowError();
        });
    });

    describe('Paywalls V1', () => {
        it('Throws an error if a free plan is trying to lock a view', async () => {
            await expect(async () => {
                await createViewAsync(user.id, {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.subcategory'),
                        id: freeList.id,
                    },
                    locked: true,
                });
            }).rejects.toMatchInlineSnapshot(
                `[PaywallClickUpError: Your plan is limited to {{limit}} protected views, {{usage}} usage.]`
            );
        });

        it('Allows enterprise plan to lock a view', async () => {
            await expect(
                createViewAsync(user.id, {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.subcategory'),
                        id: enterpriseList.id,
                    },

                    locked: true,
                })
            ).resolves.not.toThrowError();
        });
    });

    describe('Paywalls V2', () => {
        it('Throws an error if a free plan is trying to lock a view', async () => {
            await expect(async () => {
                await createViewAsync(user.id, {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.subcategory'),
                        id: freeList.id,
                    },

                    locked: true,
                });
            }).rejects.toMatchInlineSnapshot(
                `[PaywallClickUpError: Your plan is limited to {{limit}} protected views, {{usage}} usage.]`
            );
        });

        it('Allows enterprise plan to lock a view', async () => {
            await expect(
                createViewAsync(user.id, {
                    ...baseView,
                    parent: {
                        type: config.get('views.parent_types.subcategory'),
                        id: enterpriseList.id,
                    },

                    locked: true,
                })
            ).resolves.not.toThrowError();
        });
    });
});

describe('editViewAsync', () => {
    jest.setTimeout(60_000);

    const context = new ClickUpContext();
    let user: ProvidedUser;

    let enterpriseTeam: { id: string };
    let enterpriseProject: { id: string };
    let enterpriseFolder: { id: string };
    let enterpriseList: { id: string };

    let freeTeam: { id: string };
    let freeProject: { id: string };
    let freeFolder: { id: string };
    let freeList: { id: string };

    let Business2503Team: { id: string };
    let Business2503Project: { id: string };
    let Business2503Folder: { id: string };
    let Business2503List: { id: string };

    beforeAll(async () => {
        user = await context.provideUser();

        enterpriseTeam = await context.provideTeam({ plan: PlanIds.Enterprise });
        enterpriseProject = await context.provideProject({ teamId: enterpriseTeam.id });
        enterpriseFolder = await context.provideCategory({ projectId: enterpriseProject.id });
        enterpriseList = await context.provideSubcategory({ categoryId: enterpriseFolder.id });

        freeTeam = await context.provideTeam({
            plan: PlanIds.FreeForever,
        });
        freeProject = await context.provideProject({
            teamId: freeTeam.id,
        });
        freeFolder = await context.provideCategory({ projectId: freeProject.id });
        freeList = await context.provideSubcategory({ categoryId: freeFolder.id });

        Business2503Team = await context.provideTeam({ plan: PlanIds.Business2503 });
        Business2503Project = await context.provideProject({ teamId: Business2503Team.id });
        Business2503Folder = await context.provideCategory({ projectId: Business2503Project.id });
        Business2503List = await context.provideSubcategory({ categoryId: Business2503Folder.id });
    });

    it('Edit view matches snapshot', async () => {
        const view = await context.provideView({
            parentSubcategoryId: enterpriseList.id,
            viewType: config.get('views.view_types.list'),
            grouping: {},
        });
        const { view: updatedView } = await editViewAsync(user.id, view.id, {
            ...view,
        });

        expect(omit(updatedView, NON_DETERMINISTIC_VIEW_KEYS)).toMatchSnapshot();
    });

    it('should be able to edit showRecurringTasks for calendar view and enterprise plan', async () => {
        const view = await context.provideView({
            parentSubcategoryId: enterpriseList.id,
            viewType: config.get('views.view_types.calendar'),
            grouping: {},
        });
        const { view: updatedView } = await editViewAsync(user.id, view.id, {
            ...view,
            calendar_settings: {
                showRecurringTasks: true,
            },
        });

        expect(updatedView.calendar_settings.showRecurringTasks).toBe(true);
    });

    it('should not be able to edit showRecurringTasks for calendar view on less than unlimited plan', async () => {
        const view = await context.provideView({
            parentSubcategoryId: freeList.id,
            viewType: config.get('views.view_types.calendar'),
            grouping: {},
        });
        const { view: updatedView } = await editViewAsync(user.id, view.id, {
            ...view,
            calendar_settings: {
                showRecurringTasks: true,
            },
        });

        expect(updatedView.calendar_settings.showRecurringTasks).toBe(false);
    });

    it('should set showRecurringTasks to false for calendar view and the free forever plan', async () => {
        const view = await context.provideView({
            parentSubcategoryId: freeList.id,
            viewType: config.get('views.view_types.calendar'),
            grouping: {},
        });
        const { view: updatedView } = await editViewAsync(user.id, view.id, {
            ...view,
            calendar_settings: {
                showRecurringTasks: true,
            },
        });

        expect(updatedView.calendar_settings.showRecurringTasks).toBe(false);
    });

    describe('Paywalls', () => {
        it('Does not check for map paywall when editing a map view', async () => {
            const view = await context.provideView({
                parentTeamId: freeTeam.id,
                viewType: config.get('views.view_types.map'),
                grouping: {},
            });

            const limit = Number(config.get(`paywalls.map_view.limits.${PlanIds.FreeForever}`));
            await mapPaywallService.incrementLimit({ teamId: freeTeam.id }, limit);

            await editViewAsync(user.id, view.id, {
                ...view,
                name: 'test',
            });
        });

        it('Silently make not public when trying to share everything view on free plan', async () => {
            const view = await context.provideView({
                parentTeamId: freeTeam.id,
                viewType: config.get('views.view_types.list'),
                grouping: {},
            });

            const result = await editViewAsync(user.id, view.id, {
                ...view,
                public: true,
            });
            expect(result.view.public).toEqual(false);
        });

        it('Throws an error when trying to protect view on free plan', async () => {
            const view = await context.provideView({
                parentTeamId: freeTeam.id,
                viewType: config.get('views.view_types.list'),
                grouping: {},
            });

            await expect(async () => {
                await editViewAsync(user.id, view.id, {
                    ...view,
                    locked: true,
                    from_request: true,
                });
            }).rejects.toMatchInlineSnapshot(
                `[PaywallClickUpError: Your plan is limited to {{limit}} protected views, {{usage}} usage.]`
            );
        });

        it('Throws an error when trying to protect doc on free plan', async () => {
            const doc = await context.provideDoc({
                parentTeamId: freeTeam.id,
            });

            await expect(async () => {
                await editViewAsync(user.id, doc.id, {
                    ...doc,
                    locked: true,
                });
            }).rejects.toMatchInlineSnapshot(
                `[ViewsClickUpError: Protected Doc available on Business+ and Enterprise plans only]`
            );
        });

        it('Throws an error when trying to make a view as default over the usage limit on Business2503 plan', async () => {
            const view = await context.provideView({
                parentTeamId: Business2503Team.id,
                viewType: config.get('views.view_types.list'),
                grouping: {},
            });
            await defaultViewPaywallService.incrementLimit({ teamId: Business2503Team.id }, 5);

            await expect(async () => {
                await editViewAsync(user.id, view.id, {
                    ...view,
                    default: true,
                });
            }).rejects.toMatchInlineSnapshot(
                `[PaywallClickUpError: Your plan is limited to {{limit}} usages of Default View, {{usage}} usage.]`
            );
        });

        it('Allows enterprise plan to protect a view', async () => {
            const view = await context.provideView({
                parentTeamId: enterpriseTeam.id,
                viewType: config.get('views.view_types.list'),
                grouping: {},
            });

            await expect(
                editViewAsync(user.id, view.id, {
                    ...view,
                    locked: true,
                    from_request: true,
                })
            ).resolves.not.toThrowError();
        });

        it('Allows enterprise plan to share an everything view', async () => {
            const view = await context.provideView({
                parentTeamId: enterpriseTeam.id,
                viewType: config.get('views.view_types.list'),
                grouping: {},
            });

            await expect(
                editViewAsync(user.id, view.id, {
                    ...view,
                    public: true,
                })
            ).resolves.not.toThrowError();
        });
    });

    describe('Paywalls V1', () => {
        it('Throws an error when trying to lock on free plan', async () => {
            const view = await context.provideView({
                parentTeamId: freeTeam.id,
                viewType: config.get('views.view_types.list'),
                grouping: {},
            });

            await expect(async () => {
                await editViewAsync(user.id, view.id, {
                    ...view,
                    locked: true,
                    from_request: true,
                });
            }).rejects.toMatchInlineSnapshot(
                `[PaywallClickUpError: Your plan is limited to {{limit}} protected views, {{usage}} usage.]`
            );
        });

        it('Allows enterprise plan to lock a view', async () => {
            const view = await context.provideView({
                parentTeamId: enterpriseTeam.id,
                viewType: config.get('views.view_types.list'),
                grouping: {},
            });

            await expect(
                editViewAsync(user.id, view.id, {
                    ...view,
                    locked: true,
                    from_request: true,
                })
            ).resolves.not.toThrowError();
        });
    });

    describe('Paywalls V2', () => {
        it('Throws an error when trying to lock on free plan', async () => {
            const view = await context.provideView({
                parentTeamId: freeTeam.id,
                viewType: config.get('views.view_types.list'),
                grouping: {},
            });

            await expect(async () => {
                await editViewAsync(user.id, view.id, {
                    ...view,
                    locked: true,
                    from_request: true,
                });
            }).rejects.toMatchInlineSnapshot(
                `[PaywallClickUpError: Your plan is limited to {{limit}} protected views, {{usage}} usage.]`
            );
        });

        it('Allows enterprise plan to lock a view', async () => {
            const view = await context.provideView({
                parentTeamId: enterpriseTeam.id,
                viewType: config.get('views.view_types.list'),
                grouping: {},
            });

            await expect(
                editViewAsync(user.id, view.id, {
                    ...view,
                    locked: true,
                    from_request: true,
                })
            ).resolves.not.toThrowError();
        });
    });
});

describe('editViewAsync when it is a Drilldown View', () => {
    jest.setTimeout(60_000);

    const context = new ClickUpContext();
    let admin: ProvidedUser;
    let member: ProvidedUser;
    let guest: ProvidedUser;

    let team: { id: string };

    beforeAll(async () => {
        admin = await context.provideUser({ username: randomUUID(), email: `admin-${randomUUID()}@clickup.com` });
        member = await context.provideUser({ username: randomUUID(), email: `member-${randomUUID()}@clickup.com` });
        guest = await context.provideUser({ username: randomUUID(), email: `guest-${randomUUID()}@clickup.com` });

        team = await context.provideTeam({ plan: PlanIds.Enterprise }, { client: admin.client });
        await context.teamAddUser(team.id, member.id, config.team_roles.user);
        await context.teamAddUser(team.id, guest.id, config.team_roles.guest);
    });

    describe('standalone dashboard', () => {
        it('as owner I should be able to edit drilldown view', async () => {
            const { drilldownView } = await bootstrapDashboardWithDrilldownView(context, {
                teamId: team.id,
                ownerClient: admin.client,
            });

            const newName = 'new name';
            await context.updateView(
                {
                    id: drilldownView.id,
                    viewType: drilldownView.type,
                    parentTeamId: team.id,
                    isWidgetView: true,
                    name: newName,
                },
                {},
                { client: admin.client }
            );
            const updatedDrilldownView = await context.viewGet(drilldownView.id, { client: admin.client });

            expect(updatedDrilldownView.name).toBe(newName);
        });

        it('as member with edit permissions I should be able to edit drilldown view', async () => {
            const { drilldownView } = await bootstrapDashboardWithDrilldownView(context, {
                teamId: team.id,
                ownerClient: admin.client,
                dashboardMember: {
                    id: member.id,
                    permission_level: 5,
                },
            });

            const newName = 'new name';
            await context.updateView(
                {
                    id: drilldownView.id,
                    viewType: drilldownView.type,
                    parentTeamId: team.id,
                    isWidgetView: true,
                    name: newName,
                },
                {},
                { client: member.client }
            );
            const updatedDrilldownView = await context.viewGet(drilldownView.id, { client: member.client });

            expect(updatedDrilldownView.name).toBe(newName);
        });

        it('as member with readonly permissions I should NOT be able to edit drilldown view', async () => {
            const { drilldownView } = await bootstrapDashboardWithDrilldownView(context, {
                teamId: team.id,
                ownerClient: admin.client,
                dashboardMember: {
                    id: member.id,
                    permission_level: 1,
                },
            });

            await expect(() =>
                context.updateView(
                    {
                        id: drilldownView.id,
                        viewType: drilldownView.type,
                        parentTeamId: team.id,
                        isWidgetView: true,
                        name: 'new name',
                    },
                    {},
                    { client: member.client }
                )
            ).rejects.toThrow();
        });

        it('as guest with readonly permissions I should NOT be able to edit drilldown view', async () => {
            const { drilldownView } = await bootstrapDashboardWithDrilldownView(context, {
                teamId: team.id,
                ownerClient: admin.client,
                dashboardMember: {
                    id: guest.id,
                    permission_level: 1,
                },
            });

            await expect(() =>
                context.updateView(
                    {
                        id: drilldownView.id,
                        viewType: drilldownView.type,
                        parentTeamId: team.id,
                        isWidgetView: true,
                        name: 'new name',
                    },
                    {},
                    { client: guest.client }
                )
            ).rejects.toThrow();
        });
    });
});

describe('Private view sharing', () => {
    const context = new ClickUpContext();
    let user1: { id: number };
    let user2: { id: number };
    let view1: any;
    let team: { id: string };
    let project: { id: string };
    let category: { id: string };
    let subcategory: { id: string };

    const baseView: any = {
        name: 'View Name',
        visibility: 2,
        me_view: false,
        id: '',
        grouping: {},
        embed_settings: {},
        divide: {},
        settings: {
            show_task_locations: false,
            show_timer: false,
            show_subtasks: 1,
            show_subtask_parent_names: false,
            me_comments: true,
            me_subtasks: true,
            me_checklists: true,
            show_closed_subtasks: false,
            show_task_ids: false,
            show_empty_statuses: false,
            time_in_status_view: 1,
            auto_wrap: false,
        },
        members: [],
        group_members: [],
        sorting: { fields: [] },
        filters: {
            show_closed: false,
            search_custom_fields: false,
            search_description: false,
            fields: [],
        },
        columns: { fields: [] },
        permissions: {
            edit_view: true,
            delete_view: true,
            can_unprotect: true,
            comment: true,
        },
        auto_save: false,
        board_settings: {},
        type: 1,
        standard_view: false,
        sidebar_view: false,
        create_page: true,
        user_filter_settings: true,
        form_settings: { display: {}, fields: [] },
        public_share_expires_on: 1,
        public: true,
        skip_access: false,
        skipAccess: false,
        default_skip_access: false,
        dontFail: false,
        skip_privacy_check: false,
        ws_key: 5152164112,
        from_request: true,
    };

    beforeAll(async () => {
        user1 = await context.provideUser({ username: 'user 1' });
        user2 = await context.provideUser({ username: 'user 2' });
        team = await context.provideTeam({
            plan: '3',
        });

        await context.teamAddUser(team.id, user1.id, 3);
        await context.teamAddUser(team.id, user2.id, 3);

        project = await context.provideProject({
            teamId: team.id,
        });
        category = await context.provideCategory({ projectId: project.id });
        subcategory = await context.provideSubcategory({ categoryId: category.id });
        view1 = (
            await createViewAsync(user1.id, {
                ...baseView,
                parent: {
                    type: config.get('views.parent_types.subcategory'),
                    id: subcategory.id,
                },
            })
        ).view;
    });

    it('Private view should return for creator', async () => {
        const { views } = await _promiseGetViews(user1.id, {
            parent_id: subcategory.id,
            parent_type: config.get('views.parent_types.subcategory'),
        });

        expect(views.length).toEqual(1);
    });

    it('Private view should not return for other user', async () => {
        const { views: user2Views } = await _promiseGetViews(user2.id, {
            parent_id: subcategory.id,
            parent_type: config.get('views.parent_types.subcategory'),
        });

        expect(user2Views.length).toEqual(0);
    });

    it('Private view should return for other user if added to members', async () => {
        await addMembers(user1.id, view1.id, [{ id: user2.id, permission_level: 1 }]);
        const { views: user2Views } = await _promiseGetViews(user2.id, {
            parent_id: subcategory.id,
            parent_type: config.get('views.parent_types.subcategory'),
        });

        expect(user2Views.length).toEqual(1);
    });

    it('Private view should not be editable by read only user', async () => {
        await expect(async () => {
            await editViewAsync(user2.id, view1.id, baseView);
        }).rejects.toMatchInlineSnapshot(`[AccessClickUpError: You dont have permission to edit this view]`);
    });

    it('Private view should be editable by the original user', async () => {
        await editViewAsync(user1.id, view1.id, baseView);
    });

    it('Private view should not return for other user if removed from members', async () => {
        await deleteMembers(user1.id, view1.id, [user2.id]);
        const { views: user2Views } = await _promiseGetViews(user2.id, {
            parent_id: subcategory.id,
            parent_type: config.get('views.parent_types.subcategory'),
        });

        expect(user2Views.length).toEqual(0);
    });
});

describe('CRU group view', () => {
    jest.setTimeout(60_000);

    const context = new ClickUpContext();
    let user: { id: number };

    let enterpriseTeam: { id: string };

    const baseView: any = {
        name: 'View Name',
        visibility: 1,
        me_view: false,
        id: '',
        grouping: {},
        embed_settings: {},
        divide: {},
        settings: {
            show_task_locations: false,
            show_timer: false,
            show_subtasks: 1,
            show_subtask_parent_names: false,
            me_comments: true,
            me_subtasks: true,
            me_checklists: true,
            show_closed_subtasks: false,
            show_task_ids: false,
            show_empty_statuses: false,
            time_in_status_view: 1,
            auto_wrap: false,
        },
        members: [],
        group_members: [],
        sorting: { fields: [] },
        filters: {
            show_closed: false,
            search_custom_fields: false,
            search_description: false,
            fields: [],
        },
        columns: { fields: [] },
        permissions: {
            edit_view: true,
            delete_view: true,
            can_unprotect: true,
            comment: true,
        },
        auto_save: false,
        board_settings: {},
        type: 1,
        standard_view: false,
        sidebar_view: false,
        create_page: true,
        user_filter_settings: true,
        form_settings: { display: {}, fields: [] },
        public_share_expires_on: 1,
        public: true,
        skip_access: false,
        skipAccess: false,
        default_skip_access: false,
        dontFail: false,
        skip_privacy_check: false,
        ws_key: 5152164112,
        from_request: true,
    };
    let viewId: string;
    let groupId: string;

    beforeAll(async () => {
        user = await context.provideUser();

        enterpriseTeam = await context.provideTeam({
            plan: PlanIds.Enterprise,
        });

        groupId = await context.provideGroup();
    });

    it('Created user group view', async () => {
        const { view } = await createViewAsync(user.id, {
            ...baseView,
            parent: {
                type: config.get('views.parent_types.user_group'),
                id: groupId,
            },
        });
        viewId = view.id;
        expect(viewId).toBeDefined();
    });

    it('Single view matches snapshot', async () => {
        const { views } = await _promiseGetViews(user.id, {
            parent_id: groupId,
            parent_type: config.get('views.parent_types.user_group'),
        });

        expect(views.length).toEqual(1);
    });

    it('Edit view matches snapshot', async () => {
        const { view } = await editViewAsync(user.id, viewId, {
            ...baseView,
        });
        expect(viewId).toEqual(view.id);
    });
});

describe('view position updating in scope of group (when grouped by view type)', () => {
    jest.setTimeout(60_000);
    const context = new ClickUpContext();
    const views = [];
    let user: ProvidedUser;

    beforeAll(async () => {
        user = await context.provideUser();
        const team = await context.provideTeam();
        context.teamAddUser(team.id, user.id, config.team_roles.user);
        const subcategory = await context.provideSubcategory({}, { client: user.client });
        for (let i = 0; i < 5; i++) {
            const view = await context.provideView(
                {
                    parentSubcategoryId: subcategory.id,
                    viewType: config.get('views.view_types.list'),
                    grouping: {},
                },
                { client: user.client }
            );
            views.push(view);
        }
    });

    it('should update position of a view in view group and set orderidexes to items before current', async () => {
        await context.updateViewPosition(
            {
                viewId: views[1].id,
                grouped_views: true,
                next: views[3].id,
            },
            { client: user.client }
        );

        const [v1, v2, v3, v4, v5] = await Promise.all(views.map(v => context.viewGet(v.id, { client: user.client })));
        expect([
            v1.grouped_orderindex,
            v2.grouped_orderindex,
            v3.grouped_orderindex,
            v4.grouped_orderindex,
            v5.grouped_orderindex,
        ]).toEqual([1, 3, 2, null, null]);
    });

    it('should update positions properly for already existing orderindexes', async () => {
        await context.updateViewPosition(
            {
                viewId: views[4].id,
                grouped_views: true,
                next: views[2].id,
            },
            { client: user.client }
        );

        const [v1, v2, v3, v4, v5] = await Promise.all(views.map(v => context.viewGet(v.id, { client: user.client })));

        expect([
            v1.grouped_orderindex,
            v2.grouped_orderindex,
            v3.grouped_orderindex,
            v4.grouped_orderindex,
            v5.grouped_orderindex,
        ]).toEqual([1, 4, 3, null, 2]);
    });
});

describe('View permissions', () => {
    const context = new ClickUpContext();
    let teamId: string;
    let subcategoryId: string;
    let viewId: string;
    let admin: ProvidedUser;
    let member: ProvidedUser;
    let limitedMember: ProvidedUser;
    let limitedMemberGuestPerms: ProvidedUser;
    let limitedMemberGuestPermsCreator: ProvidedUser;
    let customPermissionsOn: ProvidedUser;
    let customPermissionsOff: ProvidedUser;
    let owner: ProvidedUser;
    let users: Map<string, ProvidedUser>;

    const expectedChatPermissions = {
        chat_add_followers: true,
        chat_add_self_follower: true,
        chat_comment: true,
        chat_delete_channel: true,
        chat_manage_tiles: true,
        chat_remove_followers: true,
        chat_remove_members: true,
        chat_remove_self_follower: true,
        chat_reply: true,
    };
    beforeAll(async () => {
        // setup users
        admin = await context.provideUser({ username: 'admin' });
        member = await context.provideUser({ username: 'member' });
        limitedMember = await context.provideUser({ username: 'limited-member' });
        limitedMemberGuestPerms = await context.provideUser({ username: 'limited-member-guest-perms' });
        limitedMemberGuestPermsCreator = await context.provideUser({ username: 'limited-member-guest-perms-creator' });
        customPermissionsOn = await context.provideUser({ username: 'custom-permissions-on' });
        customPermissionsOff = await context.provideUser({ username: 'custom-permissions-off' });
        owner = await context.provideUser({ username: 'owner' });
        users = new Map([
            ['owner', owner],
            ['admin', admin],
            ['member', member],
            ['limited-member', limitedMember],
            ['limited-member-guest-perms', limitedMemberGuestPerms],
            ['limited-member-guest-perms-creator', limitedMemberGuestPermsCreator],
            ['custom-permissions-on', customPermissionsOn],
            ['custom-permissions-off', customPermissionsOff],
        ]);

        // setup workspace
        const team = await context.provideTeam({ plan: '4' });
        teamId = team.id;

        await context.teamAddUser(team.id, admin.id, 2);
        await context.teamAddUser(team.id, member.id, 3);
        await context.teamAddUser(team.id, limitedMember.id, 4, 4);
        await context.teamAddUser(team.id, limitedMemberGuestPerms.id, 4, 4, DbPool.Write, undefined, {
            canCreateViews: true,
        });
        await context.teamAddUser(team.id, limitedMemberGuestPermsCreator.id, 4, 4, DbPool.Write, undefined, {
            canCreateViews: true,
        });
        await context.teamAddUser(team.id, customPermissionsOn.id, 3);
        await context.teamAddUser(team.id, customPermissionsOff.id, 3);

        // setup custom roles
        const [customRoleOff, customRoleOn] = await Promise.all([
            context.provideCustomRole({
                teamId: team.id,
                name: `custom-role-off`,
                inherited_role: 3,
            }),
            context.provideCustomRole({
                teamId: team.id,
                name: `custom-role-on`,
                inherited_role: 3,
            }),
        ]);

        await Promise.all([
            context.provideRolePermissions({
                team_id: team.id,
                role_id: customRoleOff.id,
                permissions: {
                    delete_items: 0,
                    views: 0,
                },
            }),
            context.provideRolePermissions({
                team_id: team.id,
                role_id: customRoleOn.id,
                permissions: {
                    delete_items: 1,
                    views: 1,
                },
            }),
        ]);

        await Promise.all([
            context.teamUpdateCustomRoleUser(team.id, customPermissionsOff.id, customRoleOff.id),
            context.teamUpdateCustomRoleUser(team.id, customPermissionsOn.id, customRoleOn.id),
        ]);

        // setup hierarchy and view
        const project = await context.provideProject({
            teamId: team.id,
            add: [
                { id: limitedMember.id },
                { id: limitedMemberGuestPerms.id },
                { id: limitedMemberGuestPermsCreator.id },
            ],
        });
        const category = await context.provideCategory({ projectId: project.id });
        const subcategory = await context.provideSubcategory({ categoryId: category.id });
        const createdView = await context.provideView(
            { parentSubcategoryId: subcategory.id },
            // sending request as limited member guest perms creator to set proper owner
            { client: limitedMemberGuestPermsCreator.client }
        );
        await context.updateViewMembers({
            id: createdView.id,
            permission_level: 5,
            members: [limitedMember.id, limitedMemberGuestPerms.id],
        });
        viewId = createdView.id;
    });

    it.each([
        [
            'owner',
            {
                can_unprotect: true,
                comment: true,
                can_edit_privacy: 2,
                can_delete_comments: true,
                team_role: 1,
                delete_view: true,
                edit_view: true,
                permission_level: 5,
                create_automation: true,
                create_private_view: true,
                can_add_automation: true,
                ...expectedChatPermissions,
            },
        ],
        [
            'admin',
            {
                can_unprotect: true,
                comment: true,
                can_edit_privacy: 2,
                can_delete_comments: true,
                team_role: 2,
                delete_view: true,
                edit_view: true,
                permission_level: 5,
                create_automation: true,
                create_private_view: true,
                can_add_automation: true,
                ...expectedChatPermissions,
            },
        ],
        [
            'member',
            {
                can_unprotect: true,
                comment: true,
                can_delete_comments: true,
                team_role: 3,
                can_edit_privacy: 2,
                delete_view: true,
                edit_view: true,
                permission_level: 5,
                create_automation: true,
                create_private_view: true,
                can_add_automation: true,
                ...expectedChatPermissions,
            },
        ],
        [
            'limited-member',
            {
                can_unprotect: true,
                comment: true,
                can_delete_comments: true,
                team_role: 4,
                create_view: false,
                delete_view: false,
                edit_view: false,
                permission_level: 5,
                create_automation: true,
                create_private_view: true,
                can_add_automation: true,
                ...expectedChatPermissions,
            },
        ],
        [
            'limited-member-guest-perms',
            {
                can_unprotect: true,
                comment: true,
                can_edit_privacy: 2,
                // create_view: true,
                can_delete_comments: true,
                team_role: 4,
                delete_view: false,
                edit_view: true,
                permission_level: 5,
                create_automation: true,
                create_private_view: true,
                can_add_automation: true,
                ...expectedChatPermissions,
            },
        ],
        [
            'limited-member-guest-perms-creator',
            {
                can_unprotect: true,
                comment: true,
                can_edit_privacy: 2,
                // create_view: true,
                can_delete_comments: true,
                team_role: 4,
                delete_view: true,
                edit_view: true,
                permission_level: 5,
                create_automation: true,
                create_private_view: true,
                can_add_automation: true,
                ...expectedChatPermissions,
            },
        ],
        [
            'custom-permissions-on',
            {
                can_unprotect: true,
                comment: true,
                can_edit_privacy: 2,
                can_delete_comments: true,
                team_role: 3,
                delete_view: true,
                edit_view: true,
                permission_level: 5,
                create_automation: true,
                create_private_view: true,
                can_add_automation: true,
                ...expectedChatPermissions,
            },
        ],
        [
            'custom-permissions-off',
            {
                can_unprotect: true,
                comment: true,
                can_delete_comments: true,
                team_role: 3,
                create_view: false,
                delete_view: false,
                edit_view: false,
                permission_level: 5,
                create_automation: true,
                create_private_view: true,
                can_add_automation: true,
                ...expectedChatPermissions,
            },
        ],
    ])('%s has correct view permissions for owner created view', async (user, expectedPermissions) => {
        const view: any = await context.viewGet(viewId, { client: users.get(user).client });
        expect(view?.permissions).toEqual(expectedPermissions);
    });

    it('can delete own view with custom permission', async () => {
        // Permission off, cannot delete own view
        await context.provideRolePermissions({
            team_id: teamId,
            role_id: 3,
            permissions: {
                delete_items: 0,
            },
        });

        let view: any = await context.provideView(
            { parentSubcategoryId: subcategoryId },
            {
                client: member.client,
            }
        );

        expect(view?.permissions).toMatchObject({
            delete_view: false,
        });

        // Permission to self, can delete own view
        await context.provideRolePermission({
            team_id: teamId,
            role_id: 3,
            permission: 'delete_items',
            value: 3,
        });

        view = await context.viewGet(view.id, { client: member.client });

        expect(view?.permissions).toMatchObject({
            delete_view: true,
        });
    });
});

describe('Update view with CFs by task type', () => {
    let context: ClickUpContext;
    let customFieldForTaskType;
    let customField;
    let view;
    let teamId;
    let subcategory;
    let subcategory2;

    beforeAll(async () => {
        context = new ClickUpContext();
        teamId = (await context.provideTeam()).id;
        [subcategory, subcategory2] = await Promise.all([
            context.provideSubcategory({ name: 'CF by TT list' }),
            context.provideSubcategory({ name: 'List with CF2' }),
        ]);
        const customTaskType = await context.provideCustomItem({
            name: 'Custom Task Type',
            team_id: teamId,
        });
        // Here we are assuming that the custom-fields-by-custom-task-type flag is on
        [customFieldForTaskType, customField, view] = await Promise.all([
            context.provideCustomField({
                name: 'CF by TT',
                type: 'number',
                team_id: teamId,
                applied_objects: generateAppliedObjectsJSON([customTaskType.id]),
            }),
            context.provideCustomField({
                name: 'CF',
                type: 'number',
                subcategory_id: subcategory2.id,
            }),
            context.provideView({
                parentSubcategoryId: subcategory.id,
                viewType: config.get('views.view_types.list'),
            }),
        ]);
    });
    it('should not change location for CF by TT but should add CF2 to view list', async () => {
        await context.updateView({
            id: view.id,
            columns: {
                fields: [
                    {
                        field: `cf_${customFieldForTaskType.id}`,
                    },
                    {
                        field: `cf_${customField.id}`,
                    },
                ],
            },
        });

        const allFields = (await context.getTeamCustomFields({ team_id: teamId })).fields;
        expect(allFields.length).toBe(2);

        const cfByTT = allFields.find(f => f.id === customFieldForTaskType.id);
        expect(cfByTT.subcategories).not.toBeDefined();

        const cf = allFields.find(f => f.id === customField.id);
        expect(cf.subcategories).toHaveLength(2);
        expect(cf.subcategories.map(s => s.id)).toEqual(expect.arrayContaining([subcategory.id, subcategory2.id]));
        expect(allFields).toHaveLength(2);
        expect(allFields.map(f => f.id)).toEqual(expect.arrayContaining([customFieldForTaskType.id, customField.id]));
    });
});
