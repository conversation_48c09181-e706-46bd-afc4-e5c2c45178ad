/**
 * Functional copy-task tests, it tests new logic behind templates-copy-task-options feature flag
 * Require server running
 * @group functional/models/templates
 * @group functional/changeSplitTreatments
 */

import ClickUpContext from '@clickup-legacy/jest-context/ClickUpContext';
import { FeatureFlag } from '@clickup-legacy/models/integrations/split/feature-flag.enum';
import { readAsync, writeAsync } from '@clickup-legacy/utils/db2';
import _ from 'lodash';

import { v4 as uuidv4 } from 'uuid';
import { changeSplitTestTreatment, changeSplitTestTreatments, restoreSplitTest } from '../../../../sdk/split';
import { COPY_TASK_OPTIONS_ALL_ON_CONFIG } from './common';

describe('new copyTask lib implementation functional tests', () => {
    describe.each<['on' | 'off']>([['on'], ['off']])('copy task refactor %s', treatment => {
        beforeEach(async () => {
            await changeSplitTestTreatment(
                FeatureFlag.TemplatesCopyTaskOptions,
                treatment,
                COPY_TASK_OPTIONS_ALL_ON_CONFIG
            );
        });

        afterAll(async () => {
            await restoreSplitTest();
        });

        describe('with should-prevent-copy-task-by-subtask-limit flag on', () => {
            let context;
            let user1;
            let team;

            beforeEach(async () => {
                await changeSplitTestTreatments([
                    {
                        featureName: FeatureFlag.TemplatesCopyTaskOptions,
                        value: treatment,
                        config: COPY_TASK_OPTIONS_ALL_ON_CONFIG,
                    },
                    {
                        featureName: FeatureFlag.ShouldPreventCopyTaskBySubtaskLimit,
                        value: 'on',
                        config: {
                            limit: 1,
                        },
                    },
                ]);
                context = new ClickUpContext();
                user1 = await context.provideUser({ username: 'user1' });
                team = await context.provideTeam({}, { client: user1.client });
            });

            afterAll(async () => {
                await restoreSplitTest();
            });

            it('should not allow to copy task with exceeded number of subtasks', async () => {
                const task = await context.provideTask({ name: 'Task template' }, { client: user1.client });
                await context.provideTask({ name: 'Subtask 1', parent: task.id }, { client: user1.client });
                await context.provideTask({ name: 'Subtask 2', parent: task.id }, { client: user1.client });

                await context.selectPnpQaTestPlan(team.id, { client: user1.client });

                const createTaskTemplateWith2Subtasks = () =>
                    context.provideTaskTemplate({ teamId: team.id, taskId: task.id }, { client: user1.client });

                await expect(createTaskTemplateWith2Subtasks()).rejects.toThrowError('HTTP 400');
            });
        });

        describe('task with assignees', () => {
            let context;
            let user;
            let team;

            beforeEach(async () => {
                context = new ClickUpContext();
                user = await context.provideUser({ username: 'user1' });
                team = await context.provideTeam({}, { client: user.client });
            });

            it('should copy task with assignees correctly', async () => {
                const user2 = await context.provideUser({ username: 'user2' });
                await context.teamAddUser(team.id, user2.id);
                const task = await context.provideTask(
                    { name: 'task', assignees: [user2.id] },
                    { client: user.client }
                );

                const template = await context.provideTaskTemplate({ taskId: task.id });

                const taskFromTemplate = await context.createFromTaskTemplate({
                    templateId: template.id,
                    name: 'New Task from template with assignees',
                    task_relationships: true,
                });

                expect(taskFromTemplate.assignees).toHaveLength(1);
                const assignee = taskFromTemplate.assignees.pop();
                expect(assignee.id).toEqual(user2.id);
            });
        });

        describe('task with checklist items', () => {
            it('should copy task with links', async () => {
                const task = await context.provideTask({ name: 'Task', subcategoryId: subcategory.id }, { client });
                const checklistId = (await context.provideChecklist({ taskId: task.id })).id;

                for (let i = 0; i < 3; i++) {
                    checklistItemId = (await context.provideChecklistItem({ checklistId })).id;
                }


                const taskWithLink = await context.provideTask(
                    { name: 'Task with link', linkedTasks: [linkedTask.id] },
                    { client: user.client }
                );

                const template = await context.provideTaskTemplate({ taskId: taskWithLink.id });

                const taskFromTemplate = await context.createFromTaskTemplate({
                    templateId: template.id,
                    name: 'New Task from template with link',
                    task_relationships: true,
                });

                expect(taskFromTemplate.linkedTasks).toHaveLength(1);
                const link = taskFromTemplate.linkedTasks.pop();
                expect(link.task_id).toEqual(linkedTask.id);
                expect(link.link_id).toEqual(taskFromTemplate.id);
            });
        });

        describe('with PnP QA test plan', () => {
            let context;
            let user;
            let team;
            let project;
            let category;
            let subcategory;

            beforeAll(async () => {
                context = new ClickUpContext();
                user = await context.provideUser({ username: 'user1' });
                team = await context.provideTeam({}, { client: user.client });
                project = await context.provideProject({ teamId: team.id }, { client: user.client });
                category = await context.provideCategory({ projectId: project.id }, { client: user.client });
                subcategory = await context.provideSubcategory({ categoryId: category.id }, { client: user.client });

                await context.selectPnpQaTestPlan(team.id, { client: user.client });
            });

            it('should not allow to copy task with exceeded subtasks entitlement limit', async () => {
                const team2 = await context.provideTeam({}, { client: user.client });
                const project2 = await context.provideProject({ teamId: team2.id }, { client: user.client });
                const category2 = await context.provideCategory({ projectId: project2.id }, { client: user.client });
                const subcategory2 = await context.provideSubcategory(
                    { categoryId: category2.id },
                    { client: user.client }
                );

                const task = await context.provideTask(
                    { subcategoryId: subcategory2.id, name: 'Task' },
                    { client: user.client }
                );
                await context.provideTask({ name: 'Subtask 1', parent: task.id }, { client: user.client });
                await context.provideTask({ name: 'Subtask 2', parent: task.id }, { client: user.client });
                await context.provideTask({ name: 'Subtask 3', parent: task.id }, { client: user.client });
                await context.provideTask({ name: 'Subtask 4', parent: task.id }, { client: user.client });

                const taskTemplateWith4Subtasks = await context.provideTaskTemplate(
                    { teamId: team2.id, taskId: task.id },
                    { client: user.client }
                );

                const createTaskFromTemplate = () =>
                    context.createFromTaskTemplate(
                        {
                            templateId: taskTemplateWith4Subtasks.id,
                            subcategoryId: subcategory2.id,
                            name: 'Task from template',
                        },
                        { client: user.client }
                    );

                await context.selectPnpQaTestPlan(team2.id, { client: user.client });

                await expect(createTaskFromTemplate()).rejects.toThrowError(
                    'Maximum number of subtasks in tree exceeded'
                );
            });

            it('should not allow to copy task with exceeded custom type limit', async () => {
                const customType = await context.provideCustomItem(
                    {
                        name: 'task type 1',
                        team_id: team.id,
                    },
                    { client: user.client }
                );

                const [task1] = await Promise.all([
                    context.provideTask(
                        { subcategoryId: subcategory.id, name: 'Task1', custom_type: customType.id },
                        { client: user.client }
                    ),
                    context.provideTask(
                        { subcategoryId: subcategory.id, name: 'Task2', custom_type: customType.id },
                        { client: user.client }
                    ),
                ]);

                const copyTaskWithAlreadyExisting2TasksWithCustomType = () =>
                    context.copyTasks(
                        [{ name: 'Copied task 1', sourceTaskId: task1.id, subcategoryId: subcategory.id }],
                        {
                            client: user.client,
                        }
                    );

                await expect(copyTaskWithAlreadyExisting2TasksWithCustomType()).rejects.toThrowError('400');
            });

            describe('with should-prevent-copy-task-by-subtask-limit flag on', () => {
                beforeEach(async () => {
                    await changeSplitTestTreatments([
                        {
                            featureName: FeatureFlag.TemplatesCopyTaskOptions,
                            value: treatment,
                            config: COPY_TASK_OPTIONS_ALL_ON_CONFIG,
                        },
                        {
                            featureName: 'task_should_limit_tasks_per_list',
                            value: 'on',
                        },
                    ]);
                });

                afterAll(async () => {
                    await restoreSplitTest();
                });

                it('should not allow to copy task with exceeded list size limit', async () => {
                    const subcategory2 = await context.provideSubcategory(
                        { name: 'List 2', categoryId: category.id },
                        { client: user.client }
                    );
                    const [task1] = await Promise.all(
                        _.times(10, i =>
                            context.provideTask(
                                { subcategoryId: subcategory2.id, name: `Task ${i}` },
                                { client: user.client }
                            )
                        )
                    );

                    const copyTaskWithAlreadyExisting2TasksWithCustomType = () =>
                        context.copyTasks(
                            [{ name: 'Copied task 1', sourceTaskId: task1.id, subcategoryId: subcategory2.id }],
                            {
                                client: user.client,
                            }
                        );

                    await expect(copyTaskWithAlreadyExisting2TasksWithCustomType()).rejects.toThrowError(
                        'Maximum number of tasks on a list exceeded'
                    );
                });
            });
        });

        describe('with enterprise plan and click apps enabled', () => {
            let context;
            let user;
            let team;
            let project;
            let category;
            let subcategory;

            beforeAll(async () => {
                context = new ClickUpContext();
                user = await context.provideUser({ username: 'user1' });
                team = await context.provideTeam({}, { client: user.client });
                project = await context.provideProject(
                    {
                        teamId: team.id,
                        features: {
                            points: { enabled: true },
                        },
                    },
                    { client: user.client }
                );
                category = await context.provideCategory({ projectId: project.id }, { client: user.client });
                subcategory = await context.provideSubcategory({ categoryId: category.id }, { client: user.client });

                await context.provideTeamFeature({
                    teamId: team.id,
                    features: {
                        points: true,
                        points_per_assignee: true,
                        estimates_per_assignee: true,
                        nested_subtasks: true,
                    },
                });

                await context.teamUpdatePlan(team.id, 4);
            });

            it('should copy task with custom click apps', async () => {
                const task = await context.provideTask(
                    { name: 'task', subcategoryId: subcategory.id },
                    { client: user.client }
                );

                await context.provideTaskAssignee({ taskId: task.id, assignees: [user.id] }, false, {
                    client: user.client,
                });
                await context.provideTaskPointsPerAssignee({ taskId: task.id, assignees: [user.id], points: 2 });
                await context.provideTaskTimePerAssignee({ taskId: task.id, assignee: user.id, time: 60 });

                const template = await context.provideTaskTemplate({ taskId: task.id });

                const taskFromTemplate = await context.createFromTaskTemplate({
                    templateId: template.id,
                    name: 'New Task from template with assignee and apps',
                    task_relationships: true,
                });

                expect(taskFromTemplate.points).toEqual(2);
                expect(taskFromTemplate.time_estimate).toEqual(60);
            });
        });

        describe('with regular plan', () => {
            let context;
            let user;
            let team;
            let project;
            let category;
            let subcategory;

            beforeAll(async () => {
                context = new ClickUpContext();
                user = await context.provideUser({ username: 'user1' });
                team = await context.provideTeam({}, { client: user.client });
                project = await context.provideProject({ teamId: team.id }, { client: user.client });
                category = await context.provideCategory({ projectId: project.id }, { client: user.client });
                subcategory = await context.provideSubcategory({ categoryId: category.id }, { client: user.client });
            });

            it('should correctly copy task with nested subtasks', async () => {
                await context.provideTeamFeature({
                    teamId: team.id,
                    features: {
                        nested_subtasks: true,
                        nested_subtasks_level: 5,
                    },
                });
                const task = await context.provideTask(
                    { subcategoryId: subcategory.id, name: 'Task' },
                    { client: user.client }
                );
                const subtask = await context.provideTask(
                    { name: 'Subtask 1', parent: task.id },
                    { client: user.client }
                );
                await context.provideTask({ name: 'Nested Subtask 1', parent: subtask.id }, { client: user.client });

                const taskTemplate = await context.provideTaskTemplate(
                    { teamId: team.id, taskId: task.id },
                    { client: user.client }
                );

                const taskFromTemplate = await context.createFromTaskTemplate(
                    { templateId: taskTemplate.id, subcategoryId: subcategory.id, name: 'Task from template' },
                    { client: user.client }
                );

                expect(taskFromTemplate.subtasks).toHaveLength(1);
                const [subtaskFromTemplate] = taskFromTemplate.subtasks;
                const nestedSubtaskFromTemplate = await context.getTask({ taskId: subtaskFromTemplate.id });
                expect(nestedSubtaskFromTemplate.subtasks).toHaveLength(1);
            });

            it('should copy public task', async () => {
                const context2 = new ClickUpContext();
                const user2 = await context2.provideUser({ username: 'user2' });

                const task = await context2.provideTask({ name: 'Task' }, { client: user2.client });
                await context2.provideTask({ name: 'Subtask 1', parent: task.id }, { client: user2.client });

                const taskTemplate = await context2.provideTaskTemplate(
                    { taskId: task.id, public: true },
                    { client: user2.client }
                );

                const createTaskFromTemplate = () =>
                    context.copyCommunityTaskTemplate(
                        {
                            templateId: taskTemplate.permanent_template_id,
                            publicKey: taskTemplate.public_key,
                            teamId: team.id,
                        },
                        { client: user.client }
                    );
                await expect(createTaskFromTemplate()).resolves.not.toThrow();
            });

            it('should copy task with custom type', async () => {
                const taskToBuildTemplate = await context.provideTask({
                    custom_type: 1,
                    subcategoryId: subcategory.id,
                });
                const template = await context.provideTaskTemplate({ taskId: taskToBuildTemplate.id, teamId: team.id });
                expect(template.custom_type).toBe(1);

                const newTask = await context.createFromTaskTemplate({
                    templateId: template.id,
                    name: 'New Task from custom type template',
                    subcategoryId: subcategory.id,
                });
                expect(newTask.custom_type).toBe(1);
            });

            it('should copy task with dependencies', async () => {
                const taskWithDependency = await context.provideTask(
                    { name: 'Task with dependency' },
                    { client: user.client }
                );
                const otherTask = await context.provideTask({ name: 'other task' }, { client: user.client });

                await context.addTaskDependency({ task_id: otherTask.id, dependency_of: taskWithDependency.id });

                const template = await context.provideTaskTemplate({ taskId: taskWithDependency.id });

                const taskFromTemplate = await context.createFromTaskTemplate({
                    templateId: template.id,
                    name: 'New Task from template with dependency',
                });

                expect(taskFromTemplate.dependencies).toHaveLength(1);
                const dependency = taskFromTemplate.dependencies.pop();
                expect(dependency.depends_on).toEqual(otherTask.id);
            });

            it('should copy task with links', async () => {
                const linkedTask = await context.provideTask({ name: 'linked task' }, { client: user.client });
                const taskWithLink = await context.provideTask(
                    { name: 'Task with link', linkedTasks: [linkedTask.id] },
                    { client: user.client }
                );

                const template = await context.provideTaskTemplate({ taskId: taskWithLink.id });

                const taskFromTemplate = await context.createFromTaskTemplate({
                    templateId: template.id,
                    name: 'New Task from template with link',
                    task_relationships: true,
                });

                expect(taskFromTemplate.linkedTasks).toHaveLength(1);
                const link = taskFromTemplate.linkedTasks.pop();
                expect(link.task_id).toEqual(linkedTask.id);
                expect(link.link_id).toEqual(taskFromTemplate.id);
            });

            it('should copy task with links without modifying the links', async () => {
                const linkedTask = await context.provideTask({ name: 'linked task', subcategoryId: subcategory.id });
                const taskWithLink = await context.provideTask({
                    name: 'Task with link',
                    subcategoryId: subcategory.id,
                    linkedTasks: [linkedTask.id],
                });

                const template = await context.provideTaskTemplate({ taskId: taskWithLink.id });
                const taskFromTemplate1 = await context.createFromTaskTemplate({
                    templateId: template.id,
                    name: 'New Task from template with link',
                    task_relationships: true,
                    subcategoryId: subcategory.id,
                });

                // This is here because of CLK-702420 bug.
                // in a nutshell: when copying a list with template source task, template was affected.
                // we want to make sure that this doesn't happen.
                const copiedSubcategory = await context.subcategoryCopy(
                    {
                        subcategory_id: subcategory.id,
                        category_id: category.id,
                        name: 'Copied Subcategory',
                        old_links: true,
                        relationships: true,
                    },
                    { client: user.client }
                );

                const taskFromTemplate2 = await context.createFromTaskTemplate({
                    templateId: template.id,
                    name: 'New Task from template with link',
                    task_relationships: true,
                    subcategoryId: subcategory.id,
                });

                expect(taskFromTemplate1.linkedTasks).toHaveLength(1);
                expect(taskFromTemplate2.linkedTasks).toHaveLength(1);
                expect(taskFromTemplate1.linkedTasks[0].task_id).toEqual(taskFromTemplate2.linkedTasks[0].task_id);
            });

            it('should copy task with group assignees', async () => {
                const user2 = await context.provideUser({ username: 'user2' });
                await context.teamAddUser(team.id, user2.id);

                const groupId = await context.provideGroup(
                    { name: 'group 1', teamId: team.id },
                    { client: user.client }
                );
                await context.groupAddUser({ groupId, add: [user2.id] }, { client: user.client });

                const task = await context.provideTask(
                    { name: 'task', group_assignees: [groupId] },
                    { client: user.client }
                );

                const template = await context.provideTaskTemplate({ taskId: task.id });

                const taskFromTemplate = await context.createFromTaskTemplate({
                    templateId: template.id,
                    name: 'New Task from template with group assignees',
                    task_relationships: true,
                });

                expect(taskFromTemplate.group_assignees).toHaveLength(1);
                const groupAssignee = taskFromTemplate.group_assignees.pop();
                expect(groupAssignee.id).toEqual(groupId);
            });

            it('should copy task with group followers', async () => {
                const user2 = await context.provideUser({ username: 'user2' });
                await context.teamAddUser(team.id, user2.id);

                const groupId = await context.provideGroup(
                    { name: 'group 2', teamId: team.id },
                    { client: user.client }
                );
                await context.groupAddUser({ groupId, add: [user2.id] }, { client: user.client });

                const task = await context.provideTask(
                    { name: 'task', group_followers: [groupId] },
                    { client: user.client }
                );

                const template = await context.provideTaskTemplate({ taskId: task.id });

                const taskFromTemplate = await context.createFromTaskTemplate({
                    templateId: template.id,
                    name: 'New Task from template with group followers',
                    task_relationships: true,
                });

                expect(taskFromTemplate.group_followers).toHaveLength(1);
                const groupFollowers = taskFromTemplate.group_followers.pop();
                expect(groupFollowers.id).toEqual(groupId);
            });

            it('should copy inbox order indexes', async () => {
                const user2 = await context.provideUser({ username: 'user2' });
                await context.teamAddUser(team.id, user2.id);
                const task = await context.provideTask(
                    { name: 'task', assignees: [user2.id] },
                    { client: user.client }
                );

                const template = await context.provideTaskTemplate({ taskId: task.id });

                const taskFromTemplate = await context.createFromTaskTemplate({
                    templateId: template.id,
                    name: 'New Task from template with assignees order indexes',
                    task_relationships: true,
                });

                const { rows } = await readAsync<{ total: string }>(
                    `SELECT count(*) as total FROM task_mgmt.orderindexes WHERE task_id=ANY($1) AND type=$2`,
                    [[task.id, template.original_id, taskFromTemplate.id], `inbox:${user2.id}`]
                );

                expect(rows.pop().total).toEqual('3');
            });

            it('should copy sync blocks', async () => {
                const task = await context.provideTask({}, { client: user.client });
                // it's a bit hacky, but it would require some additional services to make it through API,
                // so for simplicity, just add a record and check whether it's carried over.
                await writeAsync(
                    `INSERT INTO 
                    task_mgmt.sync_blocks 
                    (id, workspace_id, parent_id, content, ydoc, parent_type, creator) 
                    VALUES ($1, $2, $3, $4, $5, $6, $7)`,
                    [uuidv4(), team.id, task.id, '{}', null, 'task', user.id]
                );

                const template = await context.provideTaskTemplate({ taskId: task.id });
                const taskFromTemplate = await context.createFromTaskTemplate({
                    templateId: template.id,
                    name: 'New Task from template',
                });

                const { rows } = await readAsync<{ total: string }>(
                    `SELECT count(*) as total FROM task_mgmt.sync_blocks WHERE parent_id=ANY($1)`,
                    [[template.original_id, taskFromTemplate.id]]
                );
                expect(rows.pop().total).toEqual('2');
            });

            it('should copy task with custom status', async () => {
                const statuses = await context.getProjectStatuses({ projectId: project.id });
                const status = {
                    color: '#f8ae00',
                    orderindex: 2,
                    status: 'CustomStatus',
                    type: 'custom',
                };
                statuses.push(status);
                await context.editProjectStatuses({ project_id: project.id, statuses }, { client: user.client });

                const task = await context.provideTask({ status: status.status, subcategoryId: subcategory.id });
                const template = await context.provideTaskTemplate({ taskId: task.id });
                const taskFromTemplate = await context.createFromTaskTemplate({
                    templateId: template.id,
                });

                expect(taskFromTemplate.status.status).toEqual(status.status.toLowerCase());
            });

            it('should copy order indexes', async () => {
                const task = await context.provideTask({ subcategoryId: subcategory.id, assignees: [user.id] });
                const template = await context.provideTaskTemplate({ taskId: task.id });
                const taskFromTemplate = await context.createFromTaskTemplate({
                    templateId: template.id,
                });

                // check assignee order indexes
                const { rows } = await readAsync<{
                    total_assignee_orderindexes: string;
                    total_status_orderindexes: string;
                    total_field_orderindexes: string;
                    total_subtask_orderindexes: string;
                    total_none_orderindexes: string;
                    total_custom_items_orderindexes: string;
                    total_orderindexes: string;
                }>(
                    `
                SELECT 
                (SELECT count(*) FROM task_mgmt.assignee_orderindexes WHERE task_id=ANY($1)) as total_assignee_orderindexes,
                (SELECT count(*) FROM task_mgmt.status_orderindexes WHERE task_id=ANY($1)) as total_status_orderindexes,
                (SELECT count(*) FROM task_mgmt.field_orderindexes WHERE task_id=ANY($1)) as total_field_orderindexes,
                (SELECT count(*) FROM task_mgmt.subtask_orderindexes WHERE task_id=ANY($1)) as total_subtask_orderindexes,
                (SELECT count(*) FROM task_mgmt.none_orderindexes WHERE task_id=ANY($1)) as total_none_orderindexes,
                (SELECT count(*) FROM task_mgmt.custom_items_orderindexes WHERE task_id=ANY($1)) as total_custom_items_orderindexes,
                (SELECT count(*) FROM task_mgmt.orderindexes WHERE task_id=ANY($1)) as total_orderindexes
                `,
                    [[template.original_id, taskFromTemplate.id]]
                );

                const row = rows.pop();

                expect(row.total_status_orderindexes).toEqual('2');
                expect(row.total_assignee_orderindexes).toEqual('2');
                expect(row.total_field_orderindexes).toEqual('2');
                expect(row.total_subtask_orderindexes).toEqual('2');
                expect(row.total_none_orderindexes).toEqual('2');
                expect(row.total_custom_items_orderindexes).toEqual('2');
                expect(row.total_orderindexes).toEqual('2');
            });
        });

        describe('with custom status when copying with or without old statuses', () => {
            let context;
            let user;
            let team;
            let listWithCustomStatus;

            beforeAll(async () => {
                context = new ClickUpContext();
                user = await context.provideUser({ username: 'user1' });
                team = await context.provideTeam({}, { client: user.client });

                await context.provideTeamFeature({
                    teamId: team.id,
                    features: {
                        nested_subtasks: true,
                        nested_subtasks_level: 5,
                    },
                });
                const statuses = [
                    {
                        status: 'TODO',
                        orderindex: 0,
                        type: 'open',
                        color: '#ff0000',
                    },
                    {
                        status: 'DONE',
                        orderindex: 1,
                        type: 'done',
                        color: '#000000',
                    },
                    {
                        status: 'CLOSED',
                        orderindex: 2,
                        type: 'closed',
                        color: '#ffffff',
                    },
                ];
                const spaceWithCustomStatus = await context.provideProject({ name: 'Project with statuses', statuses });
                const folderWithCustomStatus = await context.provideCategory({
                    projectId: spaceWithCustomStatus.id,
                    name: 'Folder with statuses',
                    override_statuses: true,
                    statuses,
                });
                listWithCustomStatus = await context.provideSubcategory({
                    categoryId: folderWithCustomStatus.id,
                    name: 'List with statuses',
                    override_statuses: true,
                    statuses,
                });
            });

            it('should correctly set date done and closed when copying task with nested subtasks', async () => {
                const task = await context.provideTask(
                    { subcategoryId: listWithCustomStatus.id, name: 'Task', status: 'TODO' },
                    { client: user.client }
                );
                const subtasks = await Promise.all([
                    context.provideTask(
                        { name: 'Subtask 1', parent: task.id, status: 'TODO' },
                        { client: user.client }
                    ),
                    context.provideTask(
                        { name: 'Subtask 2', parent: task.id, status: 'TODO' },
                        { client: user.client }
                    ),
                ]);
                const subtask1 = subtasks.find(t => t.name === 'Subtask 1');
                const subtask2 = subtasks.find(t => t.name === 'Subtask 2');
                const nestedTask = await context.provideTask(
                    { name: 'Nested Subtask 1', parent: subtask2.id, status: 'TODO' },
                    { client: user.client }
                );

                // Update all subtasks to complete
                await Promise.all([
                    context.editTasks(
                        {
                            item_ids: [nestedTask.id],
                            status: 'CLOSED',
                        },
                        { client: user.client }
                    ),
                    context.editTasks(
                        {
                            item_ids: [subtask1.id, task.id],
                            status: 'DONE',
                        },
                        { client: user.client }
                    ),
                ]);

                const getTasksFromStorage = async (ids: string[]) => {
                    const { rows } = await readAsync(
                        `SELECT id, name, parent, subtask_parent, date_closed, date_done
                         FROM task_mgmt.tasks
                         WHERE id = ANY($1) OR parent = ANY($1)`,
                        [ids]
                    );
                    return rows;
                };
                const assertDates = (rows: any[], expectNullDates: boolean) => {
                    const verify = expectNullDates
                        ? (v: any) => expect(v).toBeNull()
                        : (v: any) => expect(v).not.toBeNull();
                    for (const row of rows) {
                        if (row.subtask_parent) {
                            // Nested subtask
                            verify(row.date_closed);
                            verify(row.date_done);
                        } else if (row.parent) {
                            // Subtasks

                            expect(row.date_closed).toBeNull();
                            // Setting to DONE will only update date_done
                            if (row.name === 'Subtask 1') {
                                verify(row.date_done);
                            } else {
                                expect(row.date_done).toBeNull();
                            }
                        } else {
                            // Parent task

                            expect(row.date_closed).toBeNull();
                            // Setting to DONE will only update date_done
                            verify(row.date_done);
                        }
                    }
                };

                // Copying task by retaining status should keep the dates
                const taskIdsWithDate = await context.copyTasks(
                    [
                        {
                            name: 'Copied task with dates persisted',
                            sourceTaskId: task.id,
                            subcategoryId: listWithCustomStatus.id,
                        },
                    ],
                    {
                        client: user.client,
                    }
                );
                let tasks = await getTasksFromStorage(taskIdsWithDate);
                assertDates(tasks, false);
                expect(tasks).toHaveLength(4);

                // Copying task without retaining status should clear the dates
                const taskIdsWithoutDate = await context.copyTasks(
                    [
                        {
                            name: 'Copied task without status',
                            sourceTaskId: task.id,
                            subcategoryId: listWithCustomStatus.id,
                            old_status: false,
                        },
                    ],
                    {
                        client: user.client,
                    }
                );
                tasks = await getTasksFromStorage(taskIdsWithoutDate);
                assertDates(tasks, true);
                expect(tasks).toHaveLength(4);
            });
        });
    });
});
