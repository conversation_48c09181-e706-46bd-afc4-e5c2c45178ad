# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [0.82.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.82.0) (2025-06-03)

## [0.81.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.81.0) (2025-06-02)

## [0.80.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.80.0) (2025-05-29)

## [0.79.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.79.0) (2025-05-01)

## [0.78.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.78.0) (2025-04-29)

## [0.77.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.77.0) (2025-04-28)


### Features

* **core-services:** add array decorators for generic ovm versioned data responses [CORE-1302] ([#57795](https://github.com/time-loop/clickup/issues/57795)) ([05e7497](https://github.com/time-loop/clickup/commit/05e7497240ba7a719325c38f448f94bd2d0d1ad2))

## [0.76.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.76.0) (2025-04-25)


### Features

* **core-services:** add dto export for versioned-object-data [CORE-1302] ([#57698](https://github.com/time-loop/clickup/issues/57698)) ([dbcd6f8](https://github.com/time-loop/clickup/commit/dbcd6f8bd39a3e881791a1f52c56ca08b8726991))

## [0.75.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.75.0) (2025-04-25)


### Features

* **core-services:** add api versioned object response decorators to document ovm responses [CORE-1302] ([#57671](https://github.com/time-loop/clickup/issues/57671)) ([6e32ce5](https://github.com/time-loop/clickup/commit/6e32ce55735e480a3c3c8ddafcdaae3dd2b6768e))

## [0.74.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.74.0) (2025-04-25)


### Features

* **core-services:** Add OVM DTOs for gateway clients in the monolith [CORE-1302] ([#57567](https://github.com/time-loop/clickup/issues/57567)) ([fa58781](https://github.com/time-loop/clickup/commit/fa5878116dbc61e24544a6c2792f735b32c36d73))

## [0.73.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.73.0) (2025-04-24)

## [0.72.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.72.0) (2025-04-18)


### Features

* **access-management:** add cpl object type [CLK-665438] ([#57115](https://github.com/time-loop/clickup/issues/57115)) ([3d63a0e](https://github.com/time-loop/clickup/commit/3d63a0eac732b44e430805630f22227d8028dc96))

## [0.71.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.71.0) (2025-04-03)

## [0.70.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.70.0) (2025-03-27)

## [0.69.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.69.0) (2025-03-25)

## [0.68.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.68.0) (2025-03-20)

## [0.67.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.67.0) (2025-03-20)


### Features

* **core-services:** add UserPresence object type [CORE-1309] ([#54776](https://github.com/time-loop/clickup/issues/54776)) ([9ff63df](https://github.com/time-loop/clickup/commit/9ff63df9669f3e1376ba007417e719fe4a0a7c23))

## [0.66.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.66.0) (2025-03-19)

## [0.65.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.65.0) (2025-03-18)


### Features

* **ai:** Add Task Property object type [AI-1178] ([4e6b238](https://github.com/time-loop/clickup/commit/4e6b23897bc65e41f95e66d0aa78828855b6b24e))

## [0.64.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.64.0) (2025-03-15)


### Features

* **chat:** Add new object relationship for comment scheduled [CHAT-9278] ([#54412](https://github.com/time-loop/clickup/issues/54412)) ([8ff8adf](https://github.com/time-loop/clickup/commit/8ff8adfdd9acb552cd65dea614eef5eb2e7a06ba))

## [0.63.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.63.0) (2025-03-14)


### Features

* **fields:** add user id to version event context [CLK-635410] ([#54155](https://github.com/time-loop/clickup/issues/54155)) ([54bdad6](https://github.com/time-loop/clickup/commit/54bdad646e0a27a4d7700f3e152065d31aec1cb5))

## [0.62.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.62.0) (2025-03-06)

## [0.61.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.61.0) (2025-03-04)

## [0.60.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.60.0) (2025-03-03)


### Features

* **user-platform:** add user_shard_workspace ovm relationship type [CLK-628683] ([#53227](https://github.com/time-loop/clickup/issues/53227)) ([e915698](https://github.com/time-loop/clickup/commit/e915698e0667dc1fc1cfbf2d144060664c697920))

## [0.59.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.59.0) (2025-02-19)

## [0.58.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.58.0) (2025-02-13)


### Features

* **chat:** Create a new object type ScheduledComment [CHAT-7107] ([#52046](https://github.com/time-loop/clickup/issues/52046)) ([e3ac18c](https://github.com/time-loop/clickup/commit/e3ac18c44d20ea1f58b5ce4f18361f8917bb58ce))

## [0.57.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.57.0) (2025-02-13)

## [0.56.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.56.0) (2025-02-05)


### Features

* **fields:** Add task parents to field value update events [CLK-616028] ([#51527](https://github.com/time-loop/clickup/issues/51527)) ([aa0909c](https://github.com/time-loop/clickup/commit/aa0909cb0f92948ec8f2cddf09a593422d2a958e))

## [0.55.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.55.0) (2025-02-03)

## [0.54.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.54.0) (2025-01-30)

## [0.53.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.53.0) (2025-01-28)


### Features

* **hierarchy:** Add object types to ovm library for new cached calculated list values [CLK-597772] ([#51093](https://github.com/time-loop/clickup/issues/51093)) ([2506104](https://github.com/time-loop/clickup/commit/2506104896c459acd23f2f20903a244a38d13bd7))

## [0.52.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.52.0) (2025-01-22)

## [0.51.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.51.0) (2025-01-09)

### Features

-   **core-services:** POST api v3 chat [CORE-141] ([#49841](https://github.com/time-loop/clickup/issues/49841)) ([1c5532e](https://github.com/time-loop/clickup/commit/1c5532e631a71163e4e36ebbaff48ea92438e63f))

## [0.50.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.50.0) (2025-01-09)

### Features

-   **core-services:** add public api exception handling [CORE-549] ([#49108](https://github.com/time-loop/clickup/issues/49108)) ([7156598](https://github.com/time-loop/clickup/commit/71565986b6c7b637b27c42393f433bbfbe2bad3b))

## [0.49.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.49.0) (2025-01-06)

## [0.48.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.48.0) (2024-12-31)

### Features

-   **core-services:** introduce a new version status value to distinguish FAILED from INVALID [CORE-622] ([#49591](https://github.com/time-loop/clickup/issues/49591)) ([850463b](https://github.com/time-loop/clickup/commit/850463bddfac99a66d95162fb194f8b2eee2949d))

## [0.47.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.47.0) (2024-12-27)

### Features

-   **core-services:** post chat messages using impersonation [CORE-606] ([#49485](https://github.com/time-loop/clickup/issues/49485)) ([f722264](https://github.com/time-loop/clickup/commit/f72226403ca490464e8f28ca03fc76c8908d9084))

## [0.46.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.46.0) (2024-12-26)

## [0.45.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.45.0) (2024-12-20)

### Features

-   **core-services:** Chat Message DELETE route implemented [CORE-147] ([#49275](https://github.com/time-loop/clickup/issues/49275)) ([4979b93](https://github.com/time-loop/clickup/commit/4979b93616dcf8526326a695a6df837fd035994e))

## [0.44.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.44.0) (2024-12-20)

## [0.43.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.43.0) (2024-12-19)

### Features

-   **data-platform:** New OVM event type for workspace banning [CLK-580550] ([#49340](https://github.com/time-loop/clickup/issues/49340)) ([f8272af](https://github.com/time-loop/clickup/commit/f8272af2321d16ad32544f980165e8f8a464f756))

## [0.42.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.42.0) (2024-12-10)

## [0.41.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.41.0) (2024-12-06)

## [0.40.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.40.0) (2024-12-03)

## [0.39.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.39.0) (2024-12-03)

## [0.38.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.38.0) (2024-11-28)

## [0.37.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.37.0) (2024-11-22)

## [0.36.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.36.0) (2024-11-14)

## [0.35.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.35.0) (2024-11-14)

### Features

-   **core-services:** add PAGE_DOC to ObjectRelationshipType in OVM lib [CORE-400] ([#47093](https://github.com/time-loop/clickup/issues/47093)) ([29b08e0](https://github.com/time-loop/clickup/commit/29b08e0c8bd5c9dc0fd3f54b96a350199e62ea2f))

## [0.34.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.34.0) (2024-11-12)

## [0.33.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.33.0) (2024-10-16)

## [0.32.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.32.0) (2024-10-10)

## [0.31.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.31.0) (2024-10-09)

### Features

-   **automations:** webhook chat action [CLK-539354] ([#44973](https://github.com/time-loop/clickup/issues/44973)) ([2047af9](https://github.com/time-loop/clickup/commit/2047af919d3ca5c4267853d68e04f708d2ecde7a))

## [0.30.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.30.0) (2024-10-08)

### Features

-   **project-mgmt:** adds approval reminder notifications [CLK-539908] ([#44832](https://github.com/time-loop/clickup/issues/44832)) ([68ba33a](https://github.com/time-loop/clickup/commit/68ba33afad9002650d974d8fa9b89150e7bd93f2))

## [0.29.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.29.0) (2024-10-03)

## [0.28.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.28.0) (2024-10-02)

### Features

-   **chat:** add mention_count logic to chat counts [CHAT-3340] ([#44263](https://github.com/time-loop/clickup/issues/44263)) ([8fe8eb2](https://github.com/time-loop/clickup/commit/8fe8eb212431e4b850d6220714f7e8876dc720c7))

## [0.27.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.27.0) (2024-10-02)

## [0.26.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.26.0) (2024-09-26)

## [0.25.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.25.0) (2024-09-25)

### Features

-   **project-mgmt:** Adds APPROVAL ObjectType [CLK-548281] ([#44292](https://github.com/time-loop/clickup/issues/44292)) ([ed76ac1](https://github.com/time-loop/clickup/commit/ed76ac179b834dc7009107851d5c495809ee10b0))

## [0.24.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.24.0) (2024-09-24)

## [0.23.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.23.0) (2024-09-24)

## [0.22.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.22.0) (2024-09-23)

### Features

-   **core-services:** default access to attachment when checking public [CLK-543530] ([#43411](https://github.com/time-loop/clickup/issues/43411)) ([b5f259e](https://github.com/time-loop/clickup/commit/b5f259e254a0c59ffbb3a5920079ad55d241181d))

## [0.21.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.21.0) (2024-09-10)

## [0.20.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.20.0) (2024-09-04)

## [0.19.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.19.0) (2024-08-22)

### Features

-   **chat:** Add viewUtils lib [CHAT-2828] ([#42253](https://github.com/time-loop/clickup/issues/42253)) ([4092bfb](https://github.com/time-loop/clickup/commit/4092bfb9fa9ec2aee7de3ad1f7a180eebe309b65))

## [0.18.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.18.0) (2024-08-13)

## [0.17.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.17.0) (2024-08-13)

## [0.16.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.16.0) (2024-08-13)

### Features

-   **chat:** Add root_parent_type to ovm data context with release 0.17 [CHAT-2723] ([#41907](https://github.com/time-loop/clickup/issues/41907)) ([735db20](https://github.com/time-loop/clickup/commit/735db20e3ba06fd1a1c79d9aa7e93850f4a40f7b))

## [0.16.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.16.0) (2024-08-08)

## [0.16.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.16.0) (2024-08-07)

## [0.15.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.15.0) (2024-07-31)

## [0.14.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.14.0) (2024-07-30)

## [0.13.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.13.0) (2024-07-24)

## [0.12.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.12.0) (2024-07-10)

## [0.11.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.11.0) (2024-07-02)

## [0.10.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.10.0) (2024-07-01)

### Features

-   **user-platform:** add form_submission for ovm audit log context [CLK-517028] ([#40178](https://github.com/time-loop/clickup/issues/40178)) ([0b924e6](https://github.com/time-loop/clickup/commit/0b924e665eadefc19e4382864e3d5739deba7df8))

## [0.9.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.9.0) (2024-06-22)

## [0.8.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.8.0) (2024-06-17)

## [0.7.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.7.0) (2024-06-13)

## [0.6.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.6.0) (2024-06-13)

## [0.5.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.5.0) (2024-06-13)

## [0.4.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.4.0) (2024-06-13)

## [0.3.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.3.0) (2024-06-12)

## [0.2.3](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.2.3) (2024-05-17)

## [0.2.2](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.2.2) (2024-05-17)

## [0.2.1](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.2.1) (2024-05-17)

### Reverts

-   Revert "chore(chat): swapping releaseAs to minor [CLK-493305]" (#38578) ([2be9a26](https://github.com/time-loop/clickup/commit/2be9a262a68eb375f36ecae26b705e8f2bb9c65e)), closes [#38578](https://github.com/time-loop/clickup/issues/38578) [#38400](https://github.com/time-loop/clickup/issues/38400)

## [0.2.0](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.2.0) (2024-05-13)

## [0.1.12](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.12) (2024-05-13)

## [0.1.12](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.12) (2024-05-13)

## [0.1.12](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.12) (2024-05-02)

## [0.1.12](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.12) (2024-04-29)

## [0.1.12](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.12) (2024-04-28)

## [0.1.11](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.11) (2024-04-16)

## [0.1.10](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.10) (2024-04-15)

## [0.1.9](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.9) (2024-04-12)

## [0.1.8](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.8) (2024-04-09)

## [0.1.7](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.7) (2024-04-05)

### Features

-   **chat:** Add REACTION_USER ovm relationship type [CLK-484699] ([#36856](https://github.com/time-loop/clickup/issues/36856)) ([b8214c8](https://github.com/time-loop/clickup/commit/b8214c8c1e4dcd9c7ac4b4b57588ade5ae1a788c))

## [0.1.6](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.6) (2024-04-03)

### Features

-   **chat:** Add comment author OVM relationship [CLK-484699] ([#36785](https://github.com/time-loop/clickup/issues/36785)) ([e5bc3c0](https://github.com/time-loop/clickup/commit/e5bc3c0c4398e130a6271ebe8a36a2a3f818cf78))
-   **core-services:** fan out count for individual fan outs [CLK-475706] ([#36611](https://github.com/time-loop/clickup/issues/36611)) ([d16a019](https://github.com/time-loop/clickup/commit/d16a019359e05ae865815e8bfacfa153c5c738fc))

## [0.1.5](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.5) (2024-03-29)

### Features

-   **chat:** Root parent comment backfill scripts [CLK-481036] ([#36510](https://github.com/time-loop/clickup/issues/36510)) ([44b5235](https://github.com/time-loop/clickup/commit/44b523581f167e25182a1f932222903e4b5fe9db))

## [0.1.4](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.4) (2024-03-29)

### Features

-   **core-services:** Fix ovm-object-version package publishing [CLK-475706] ([#36517](https://github.com/time-loop/clickup/issues/36517)) ([0b7d8bf](https://github.com/time-loop/clickup/commit/0b7d8bfcd88f9ac91b24d573df37c17514063b5b))

## [0.1.3](https://github.com/time-loop/clickup/compare/<EMAIL>-object-version@0.1.3) (2024-03-28)

### Features

-   **core-services:** fan out unique id for individual fan outs [CLK-475706] ([#36507](https://github.com/time-loop/clickup/issues/36507)) ([9e49b74](https://github.com/time-loop/clickup/commit/9e49b7436f932b7b9256c5908cedf3c9151274e6))
-   **core-services:** Fan out unique id for individual fan outs added to OVM context data [CLK-475706] ([#36486](https://github.com/time-loop/clickup/issues/36486)) ([b74b4bf](https://github.com/time-loop/clickup/commit/b74b4bf5e89c88836809d74cc07ece1c9382c9ab))

### Bug Fixes

-   **user-platform:** allow before/after fields values to be nullable in OVM changeData [CLK-480144] ([#36449](https://github.com/time-loop/clickup/issues/36449)) ([57b052b](https://github.com/time-loop/clickup/commit/57b052b61f67425aa7f7e45559a2b8f3713936be))
