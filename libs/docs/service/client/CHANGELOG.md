# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [0.0.137](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.137) (2025-06-03)

## [0.0.136](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.136) (2025-06-02)

## [0.0.135](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.135) (2025-05-29)

## [0.0.134](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.134) (2025-05-01)

## [0.0.133](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.133) (2025-04-29)


### Features

* **access-management:** add custom permission level to prefetched data in authz [CU-8xdfmcvvx] ([#57250](https://github.com/time-loop/clickup/issues/57250)) ([1c622df](https://github.com/time-loop/clickup/commit/1c622df58c53980d2597be3bf43e7bb010e2ed04))

## [0.0.132](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.132) (2025-04-24)

## [0.0.131](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.131) (2025-04-23)


### Features

* **docs:** Support share_with_team while moving docs in v3 API [CLK-615862] ([#55237](https://github.com/time-loop/clickup/issues/55237)) ([986ed78](https://github.com/time-loop/clickup/commit/986ed78d6043a477ef8045f4c4a40e7f633fe4a4))

## [0.0.130](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.130) (2025-04-21)


### Features

* **docs:** accept versions in V3 api requests [CLK-667665] ([#57204](https://github.com/time-loop/clickup/issues/57204)) ([9628cae](https://github.com/time-loop/clickup/commit/9628caef21981287882d093773b972447bbf7a9d))

## [0.0.129](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.129) (2025-04-08)


### Features

* **docs:** Add version vector field to get page v3 [CLK-647651] ([#55510](https://github.com/time-loop/clickup/issues/55510)) ([de3bfe7](https://github.com/time-loop/clickup/commit/de3bfe7ca4488da6c23764cd4ed96ef4a2387f87))

## [0.0.128](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.128) (2025-04-03)

## [0.0.127](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.127) (2025-03-27)

## [0.0.126](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.126) (2025-03-25)

## [0.0.125](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.125) (2025-03-25)


### Features

* **docs:** add swagger docs for experience api [CLK-643337] ([#55074](https://github.com/time-loop/clickup/issues/55074)) ([e391bfb](https://github.com/time-loop/clickup/commit/e391bfb768a86c6c610d593ec2464619f9159671))

## [0.0.124](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.124) (2025-03-20)


### Features

* **core-services:** change contract to match the newly discussed contract [CORE-1309] ([#54778](https://github.com/time-loop/clickup/issues/54778)) ([6dfadbd](https://github.com/time-loop/clickup/commit/6dfadbdab9a392a5b6bb49dc1e8ada495febb7cc))

## [0.0.123](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.123) (2025-03-19)

## [0.0.122](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.122) (2025-03-18)

## [0.0.121](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.121) (2025-03-14)

## [0.0.120](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.120) (2025-03-06)


### Features

* **project-mgmt:** implementation of work schedules [CLK-602101] ([#53342](https://github.com/time-loop/clickup/issues/53342)) ([04b2987](https://github.com/time-loop/clickup/commit/04b2987c5a2023269b390a6fee549c763a5698d7))

## [0.0.119](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.119) (2025-03-04)


### Features

* **docs:** add meeting notes to doc type enum [CLK-631350] ([#53599](https://github.com/time-loop/clickup/issues/53599)) ([c4cf431](https://github.com/time-loop/clickup/commit/c4cf431141ca53a27d21332de128320509cba1a0))

## [0.0.118](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.118) (2025-02-19)


### Features

* **chat:** Add scheduledComments operator for the OVM events [CHAT-7236] ([#52576](https://github.com/time-loop/clickup/issues/52576)) ([ef0da4a](https://github.com/time-loop/clickup/commit/ef0da4a9c283216dd714c038b390d0a5ec0b9f40))

## [0.0.117](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.117) (2025-02-13)

## [0.0.116](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.116) (2025-02-05)

## [0.0.115](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.115) (2025-02-04)


### Features

* **docs:** Adds keep_creator and sidebar_view in edit doc v3 API [CLK-609084] ([#51449](https://github.com/time-loop/clickup/issues/51449)) ([290f2a8](https://github.com/time-loop/clickup/commit/290f2a8d418f375cf45c5f70462b61967e4e71cf))

## [0.0.114](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.114) (2025-02-03)

## [0.0.113](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.113) (2025-01-30)


### Features

* **hierarchy:** Update hierarchy-worker for generating new list points and time estimates versions [CLK-597772] ([#51182](https://github.com/time-loop/clickup/issues/51182)) ([7db84bf](https://github.com/time-loop/clickup/commit/7db84bff1970ac8c7a925d07177eadcee59e07c0))

## [0.0.112](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.112) (2025-01-22)

## [0.0.111](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.111) (2025-01-21)


### Features

* **docs:** Support moving doc and updating visibility in Docs V3 API [CLK-595978] ([#50117](https://github.com/time-loop/clickup/issues/50117)) ([03f5188](https://github.com/time-loop/clickup/commit/03f5188209195636c859abb44cf44676d1cb6233))

## [0.0.110](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.110) (2025-01-06)

## [0.0.109](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.109) (2024-12-26)

## [0.0.108](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.108) (2024-12-20)


### Features

* **data-platform:** Update OVM library [CLK-578572] ([#49395](https://github.com/time-loop/clickup/issues/49395)) ([23bfcb1](https://github.com/time-loop/clickup/commit/23bfcb1c3ee1d24614e5840cdb6fde26dc8aca06))

## [0.0.107](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.107) (2024-12-10)

## [0.0.106](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.106) (2024-12-06)

## [0.0.105](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.105) (2024-12-03)

## [0.0.104](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.104) (2024-12-03)

## [0.0.103](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.103) (2024-11-28)

## [0.0.102](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.102) (2024-11-22)

## [0.0.101](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.101) (2024-11-14)


### Bug Fixes

* **project-mgmt:** Add approval parent lookup in authz [CLK-548852] ([#44485](https://github.com/time-loop/clickup/issues/44485)) ([4185baf](https://github.com/time-loop/clickup/commit/4185bafc4eeb313d7af5231cf62cd8b47aa1934b))

## [0.0.100](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.100) (2024-11-14)

## [0.0.99](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.99) (2024-11-12)

## [0.0.98](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.98) (2024-10-16)

## [0.0.97](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.97) (2024-10-10)

## [0.0.96](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.96) (2024-10-08)

## [0.0.95](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.95) (2024-10-03)


### Features

* **core-services:** Initial Public API V3 passing base oas3 spectral ruleset [CORE-76] ([#44302](https://github.com/time-loop/clickup/issues/44302)) ([0f8b3f5](https://github.com/time-loop/clickup/commit/0f8b3f50163e52c15e24c7366075ff2637fc8b2a))

## [0.0.94](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.94) (2024-10-02)

## [0.0.93](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.93) (2024-10-02)

## [0.0.92](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.92) (2024-09-26)

## [0.0.91](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.91) (2024-09-24)

## [0.0.90](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.90) (2024-09-24)

## [0.0.89](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.89) (2024-09-23)

## [0.0.88](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.88) (2024-09-10)

## [0.0.87](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.87) (2024-09-04)

## [0.0.87](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.87) (2024-08-08)

## [0.0.87](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.87) (2024-08-07)

## [0.0.86](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.86) (2024-07-31)

## [0.0.85](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.85) (2024-07-30)

## [0.0.84](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.84) (2024-07-24)

## [0.0.83](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.83) (2024-07-10)

## [0.0.82](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.82) (2024-07-02)

## [0.0.81](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.81) (2024-06-22)

## [0.0.80](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.80) (2024-06-21)


### Features

* **core-services:** Bring response schemas into gateway OpenAPI specs [CLK-515791] ([#39844](https://github.com/time-loop/clickup/issues/39844)) ([674f637](https://github.com/time-loop/clickup/commit/674f6377884afd65e1b8ebfefb6be0fe5242f68f))

## [0.0.79](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.79) (2024-06-17)

## [0.0.78](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.78) (2024-06-14)

## [0.0.77](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.77) (2024-06-13)

## [0.0.76](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.76) (2024-06-13)

## [0.0.75](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.75) (2024-06-13)

## [0.0.74](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.74) (2024-06-13)

## [0.0.73](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.73) (2024-06-12)

## [0.0.72](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.72) (2024-05-27)


### Features

* **docs:** allow requesting sync block information for references [CLK-497619] ([#38306](https://github.com/time-loop/clickup/issues/38306)) ([6fc62ae](https://github.com/time-loop/clickup/commit/6fc62ae805d31830f782ca2b7037a94453339eb1))

## [0.0.71](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.71) (2024-05-17)

## [0.0.70](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.70) (2024-05-17)

## [0.0.68](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.68) (2024-05-13)


### Features

* **core-services:** migrate docs-service to esbuild [CLK-489341] ([#37330](https://github.com/time-loop/clickup/issues/37330)) ([7b333a0](https://github.com/time-loop/clickup/commit/7b333a016f5929ae3f474f4c0eae1a40534f4dda))


### Bug Fixes

* **automations:** Fix paywall error that starts at the start of the month [CLK-493970] ([#37958](https://github.com/time-loop/clickup/issues/37958)) ([30ed8cd](https://github.com/time-loop/clickup/commit/30ed8cd7d3f3e190544f9f03546e6bf16606df7f))


### Performance Improvements

* **data-platform:** reduce the db lag cache update frequency [CLK-486738] ([#37526](https://github.com/time-loop/clickup/issues/37526)) ([d3c54cd](https://github.com/time-loop/clickup/commit/d3c54cdbe6931b1647f981a12411db7b22878bc6))

## [0.0.68](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.68) (2024-05-02)


### Features

* **core-services:** migrate docs-service to esbuild [CLK-489341] ([#37330](https://github.com/time-loop/clickup/issues/37330)) ([7b333a0](https://github.com/time-loop/clickup/commit/7b333a016f5929ae3f474f4c0eae1a40534f4dda))


### Bug Fixes

* **automations:** Fix paywall error that starts at the start of the month [CLK-493970] ([#37958](https://github.com/time-loop/clickup/issues/37958)) ([30ed8cd](https://github.com/time-loop/clickup/commit/30ed8cd7d3f3e190544f9f03546e6bf16606df7f))


### Performance Improvements

* **data-platform:** reduce the db lag cache update frequency [CLK-486738] ([#37526](https://github.com/time-loop/clickup/issues/37526)) ([d3c54cd](https://github.com/time-loop/clickup/commit/d3c54cdbe6931b1647f981a12411db7b22878bc6))

## [0.0.68](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.68) (2024-04-29)


### Features

* **core-services:** migrate docs-service to esbuild [CLK-489341] ([#37330](https://github.com/time-loop/clickup/issues/37330)) ([7b333a0](https://github.com/time-loop/clickup/commit/7b333a016f5929ae3f474f4c0eae1a40534f4dda))


### Performance Improvements

* **data-platform:** reduce the db lag cache update frequency [CLK-486738] ([#37526](https://github.com/time-loop/clickup/issues/37526)) ([d3c54cd](https://github.com/time-loop/clickup/commit/d3c54cdbe6931b1647f981a12411db7b22878bc6))

## [0.0.68](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.68) (2024-04-28)


### Features

* **core-services:** migrate docs-service to esbuild [CLK-489341] ([#37330](https://github.com/time-loop/clickup/issues/37330)) ([7b333a0](https://github.com/time-loop/clickup/commit/7b333a016f5929ae3f474f4c0eae1a40534f4dda))


### Performance Improvements

* **data-platform:** reduce the db lag cache update frequency [CLK-486738] ([#37526](https://github.com/time-loop/clickup/issues/37526)) ([d3c54cd](https://github.com/time-loop/clickup/commit/d3c54cdbe6931b1647f981a12411db7b22878bc6))

## [0.0.67](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.67) (2024-04-16)

## [0.0.66](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.66) (2024-04-15)


### Features

* **docs:** Set version for swagger using runtime config [CLK-488673] ([#37183](https://github.com/time-loop/clickup/issues/37183)) ([7d30120](https://github.com/time-loop/clickup/commit/7d3012082e5f476d3516da34273daf686af1e6a3))

## [0.0.65](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.65) (2024-04-15)

## [0.0.64](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.64) (2024-04-12)


### Features

* **docs:** Add support for parent_id and parent_type [CLK-487414] ([#37043](https://github.com/time-loop/clickup/issues/37043)) ([7b97cd0](https://github.com/time-loop/clickup/commit/7b97cd02f19ab689cf1d1513a06f283d96d03b11))

## [0.0.63](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.63) (2024-04-09)

## [0.0.62](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.62) (2024-03-29)

## [0.0.61](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.61) (2024-03-29)

## [0.0.60](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.60) (2024-03-28)
