# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [0.1.51](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.51) (2025-06-03)

## [0.1.50](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.50) (2025-06-02)

## [0.1.49](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.49) (2025-05-29)

## [0.1.48](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.48) (2025-05-13)


### Features

* **docs:** Return urlsFixedCount in migration API [CLK-656467] ([#58930](https://github.com/time-loop/clickup/issues/58930)) ([2749b10](https://github.com/time-loop/clickup/commit/2749b1030e88458c9f547bc344805d901b68c108))

## [0.1.47](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.47) (2025-05-09)


### Features

* **whiteboards:** Re-added endpoint to migrate whiteboard private URLs [CLK-656467] ([#58646](https://github.com/time-loop/clickup/issues/58646)) ([4e2396f](https://github.com/time-loop/clickup/commit/4e2396fed30e472fe0f2905296ad4abdea6d64ec))

## [0.1.46](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.46) (2025-05-08)


### Reverts

* **whiteboards:** removed url migration code [CLK-656467] ([#58619](https://github.com/time-loop/clickup/issues/58619)) ([573786d](https://github.com/time-loop/clickup/commit/573786d053972d31833341656953954b39ab7bbe))

## [0.1.45](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.45) (2025-05-07)


### Features

* **whiteboards:** Endpoint to migrate whiteboard private URLs [CLK-618019] ([#58263](https://github.com/time-loop/clickup/issues/58263)) ([86f95db](https://github.com/time-loop/clickup/commit/86f95dba0559674158d28c6fd1fd2cc7374be2b6))

## [0.1.44](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.44) (2025-05-01)

## [0.1.43](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.43) (2025-04-29)

## [0.1.42](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.42) (2025-04-24)

## [0.1.41](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.41) (2025-04-03)

## [0.1.40](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.40) (2025-03-27)

## [0.1.39](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.39) (2025-03-25)

## [0.1.38](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.38) (2025-03-20)

## [0.1.37](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.37) (2025-03-19)

## [0.1.36](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.36) (2025-03-18)

## [0.1.35](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.35) (2025-03-14)


### Bug Fixes

* **fields:** fix openapi_generator issues [AI-1258] ([#53853](https://github.com/time-loop/clickup/issues/53853)) ([3e2f9af](https://github.com/time-loop/clickup/commit/3e2f9afb50029177a78c7e47d78ef706107f02bd))

## [0.1.34](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.34) (2025-03-06)

## [0.1.33](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.33) (2025-02-19)

## [0.1.32](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.32) (2025-02-13)

## [0.1.31](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.31) (2025-02-05)

## [0.1.30](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.30) (2025-02-03)

## [0.1.29](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.29) (2025-01-30)

## [0.1.28](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.28) (2025-01-22)

## [0.1.27](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.27) (2025-01-14)


### Bug Fixes

* **whiteboards:** Setup update whiteboard endpoint for template update bug [CLK-599699] ([#49734](https://github.com/time-loop/clickup/issues/49734)) ([596b45c](https://github.com/time-loop/clickup/commit/596b45c30426666adc6204877fd2812e454a5ec1))

## [0.1.26](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.26) (2025-01-06)

## [0.1.25](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.25) (2024-12-27)

## [0.1.24](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.24) (2024-12-26)

## [0.1.23](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.23) (2024-12-20)

## [0.1.22](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.22) (2024-12-10)

## [0.1.21](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.21) (2024-12-06)

## [0.1.20](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.20) (2024-12-03)

## [0.1.19](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.19) (2024-12-03)

## [0.1.18](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.18) (2024-11-28)

## [0.1.17](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.17) (2024-11-22)

## [0.1.16](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.16) (2024-11-14)

## [0.1.15](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.15) (2024-11-14)

## [0.1.14](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.14) (2024-11-12)

## [0.1.13](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.13) (2024-10-16)

## [0.1.12](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.12) (2024-10-10)

## [0.1.11](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.11) (2024-10-08)

## [0.1.10](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.10) (2024-10-03)

## [0.1.9](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.9) (2024-10-02)

## [0.1.8](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.8) (2024-10-02)

## [0.1.7](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.7) (2024-09-26)

## [0.1.6](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.6) (2024-09-24)

## [0.1.5](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.5) (2024-09-24)

## [0.1.4](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.4) (2024-09-23)

## [0.1.3](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.3) (2024-09-10)

## [0.1.2](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.2) (2024-09-04)

## [0.1.1](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.1) (2024-08-09)

## [0.1.0](https://github.com/time-loop/clickup/compare/<EMAIL>-v3-service-client@0.1.0) (2024-08-09)

## 0.0.1 (2024-08-09)


### Features

* **whiteboards:** Add whiteboards v3 openapi client [CLK-480693] ([#41508](https://github.com/time-loop/clickup/issues/41508)) ([8498df7](https://github.com/time-loop/clickup/commit/8498df7ecc24e970c9729dcc92d4bcdfd8c1d57b))


### Bug Fixes

* **whiteboards:** Whiteboard v3 replication clean up [CLK-530927] ([#41695](https://github.com/time-loop/clickup/issues/41695)) ([dd4b9ec](https://github.com/time-loop/clickup/commit/dd4b9ec9635e6061cf190ba4783b00d02e37eb5b))

## 0.0.1 (2024-08-08)


### Features

* **whiteboards:** Add whiteboards v3 openapi client [CLK-480693] ([#41508](https://github.com/time-loop/clickup/issues/41508)) ([8498df7](https://github.com/time-loop/clickup/commit/8498df7ecc24e970c9729dcc92d4bcdfd8c1d57b))

## 0.0.1 (2024-08-07)


### Features

* **whiteboards:** Add whiteboards v3 openapi client [CLK-480693] ([#41508](https://github.com/time-loop/clickup/issues/41508)) ([8498df7](https://github.com/time-loop/clickup/commit/8498df7ecc24e970c9729dcc92d4bcdfd8c1d57b))
