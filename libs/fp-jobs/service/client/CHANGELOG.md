# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [0.0.100](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.100) (2025-06-03)

## [0.0.99](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.99) (2025-06-02)

## [0.0.98](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.98) (2025-05-29)


### Features

* **core-services:** onboard foundational-job-service to gateway-service [CORE-1468] ([#60287](https://github.com/time-loop/clickup/issues/60287)) ([fe65926](https://github.com/time-loop/clickup/commit/fe65926b05d4908ca87eb9dcaf8b9a51904fa7ed))

## [0.0.97](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.97) (2025-05-29)

## [0.0.96](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.96) (2025-05-01)

## [0.0.95](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.95) (2025-04-30)


### Features

* **fields:** Use custom partition routing for AI jobs, run multiple consumers per pod [CLK-669783] ([#57766](https://github.com/time-loop/clickup/issues/57766)) ([6fbd1b1](https://github.com/time-loop/clickup/commit/6fbd1b1f3230db78c53c1a2d8f77116f77dd389b))

## [0.0.94](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.94) (2025-04-29)

## [0.0.93](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.93) (2025-04-24)

## [0.0.92](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.92) (2025-04-03)

## [0.0.91](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.91) (2025-03-27)

## [0.0.90](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.90) (2025-03-25)

## [0.0.89](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.89) (2025-03-20)

## [0.0.88](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.88) (2025-03-19)

## [0.0.87](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.87) (2025-03-18)

## [0.0.86](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.86) (2025-03-14)

## [0.0.85](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.85) (2025-03-06)

## [0.0.84](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.84) (2025-02-24)


### Features

* **docs:** add doc jobs to foundational kafka topics [CLK-625178] ([#52889](https://github.com/time-loop/clickup/issues/52889)) ([34f9406](https://github.com/time-loop/clickup/commit/34f9406ca4e0c842f9b8a700547de7aac77f52a8))

## [0.0.83](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.83) (2025-02-19)

## [0.0.82](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.82) (2025-02-18)

## [0.0.81](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.81) (2025-02-13)

## [0.0.80](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.80) (2025-02-05)

## [0.0.79](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.79) (2025-02-03)

## [0.0.78](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.78) (2025-01-30)

## [0.0.77](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.77) (2025-01-22)

## [0.0.76](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.76) (2025-01-14)


### Features

* **fields:** AI field jobs fetch bulk api [CLK-601566] ([#49976](https://github.com/time-loop/clickup/issues/49976)) ([fe8f9c4](https://github.com/time-loop/clickup/commit/fe8f9c4a15cc079425695e97d16d34dde23a9922))

## [0.0.75](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.75) (2025-01-06)

## [0.0.74](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.74) (2025-01-02)


### Features

* **fields:** AI Fields queueing module, domain, mocks [CLK-581377] ([#49519](https://github.com/time-loop/clickup/issues/49519)) ([3c4b5a8](https://github.com/time-loop/clickup/commit/3c4b5a87fd292dbd30bcc7a33183f92de74681f0))

## [0.0.73](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.73) (2024-12-26)

## [0.0.72](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.72) (2024-12-20)

## [0.0.71](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.71) (2024-12-10)

## [0.0.70](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.70) (2024-12-06)

## [0.0.69](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.69) (2024-12-03)

## [0.0.68](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.68) (2024-12-03)

## [0.0.67](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.67) (2024-11-28)

## [0.0.66](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.66) (2024-11-22)

## [0.0.65](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.65) (2024-11-14)

## [0.0.64](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.64) (2024-11-14)

## [0.0.63](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.63) (2024-11-12)

## [0.0.62](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.62) (2024-10-16)

## [0.0.61](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.61) (2024-10-10)

## [0.0.60](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.60) (2024-10-08)

## [0.0.59](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.59) (2024-10-03)

## [0.0.58](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.58) (2024-10-02)

## [0.0.57](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.57) (2024-10-02)

## [0.0.56](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.56) (2024-09-26)

## [0.0.55](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.55) (2024-09-24)

## [0.0.54](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.54) (2024-09-24)

## [0.0.53](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.53) (2024-09-23)

## [0.0.52](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.52) (2024-09-10)

## [0.0.51](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.51) (2024-09-04)

## [0.0.51](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.51) (2024-08-08)

## [0.0.51](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.51) (2024-08-07)

## [0.0.50](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.50) (2024-07-31)

## [0.0.49](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.49) (2024-07-30)

## [0.0.48](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.48) (2024-07-24)

## [0.0.47](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.47) (2024-07-10)

## [0.0.46](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.46) (2024-07-02)

## [0.0.45](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.45) (2024-06-22)

## [0.0.44](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.44) (2024-06-17)

## [0.0.43](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.43) (2024-06-13)

## [0.0.42](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.42) (2024-06-13)

## [0.0.41](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.41) (2024-06-13)

## [0.0.40](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.40) (2024-06-13)

## [0.0.39](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.39) (2024-06-12)

## [0.0.38](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.38) (2024-05-17)

## [0.0.37](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.37) (2024-05-17)

## [0.0.35](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.35) (2024-05-13)

## [0.0.35](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.35) (2024-05-02)

## [0.0.35](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.35) (2024-04-29)

## [0.0.35](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.35) (2024-04-28)

## [0.0.34](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.34) (2024-04-16)

## [0.0.33](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.33) (2024-04-15)

## [0.0.32](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.32) (2024-04-12)

## [0.0.31](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.31) (2024-04-09)

## [0.0.30](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.30) (2024-03-29)

## [0.0.29](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.29) (2024-03-29)

## [0.0.28](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.28) (2024-03-28)

## [0.0.27](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.27) (2024-03-28)


### Features

* **tasks:** add demo demo interval job creation to GV cache scheduler [CLK-455171] ([#33659](https://github.com/time-loop/clickup/issues/33659)) ([e163c8b](https://github.com/time-loop/clickup/commit/e163c8b8ab4793c48e70dccb8b6adbcaf4b420ec)), closes [#33661](https://github.com/time-loop/clickup/issues/33661)

## [0.0.27](https://github.com/time-loop/clickup/compare/<EMAIL>-job-service-client@0.0.27) (2024-03-28)


### Features

* **tasks:** add demo demo interval job creation to GV cache scheduler [CLK-455171] ([#33659](https://github.com/time-loop/clickup/issues/33659)) ([e163c8b](https://github.com/time-loop/clickup/commit/e163c8b8ab4793c48e70dccb8b6adbcaf4b420ec)), closes [#33661](https://github.com/time-loop/clickup/issues/33661)
