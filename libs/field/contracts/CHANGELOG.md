# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [1.3.39](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.39) (2025-06-03)

## [1.3.38](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.38) (2025-06-02)

## [1.3.37](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.37) (2025-05-29)

## [1.3.36](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.36) (2025-05-01)

## [1.3.35](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.35) (2025-04-29)

## [1.3.34](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.34) (2025-04-24)

## [1.3.33](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.33) (2025-04-03)

## [1.3.32](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.32) (2025-03-27)

## [1.3.31](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.31) (2025-03-25)

## [1.3.30](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.30) (2025-03-20)

## [1.3.29](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.29) (2025-03-19)

## [1.3.28](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.28) (2025-03-18)

## [1.3.27](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.27) (2025-03-14)

## [1.3.26](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.26) (2025-03-06)

## [1.3.25](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.25) (2025-02-19)

## [1.3.24](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.24) (2025-02-13)

## [1.3.23](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.23) (2025-02-05)

## [1.3.22](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.22) (2025-02-03)

## [1.3.21](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.21) (2025-01-30)

## [1.3.20](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.20) (2025-01-23)


### Features

* **chat:** Use separate redis keys for room creation [CHAT-5623] ([#50775](https://github.com/time-loop/clickup/issues/50775)) ([e6d7c16](https://github.com/time-loop/clickup/commit/e6d7c163b91075784214d4b374c6cdf1546ce447))

## [1.3.19](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.19) (2025-01-22)

### Features

-   **tasks:** Account for CFs by task type in task creation [CLK-600213] ([#50093](https://github.com/time-loop/clickup/issues/50093)) ([f6aa567](https://github.com/time-loop/clickup/commit/f6aa56796c0c0fd8a3edcd26d3dc20e4b710e932))

## [1.3.18](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.18) (2025-01-22)

### Features

-   **core-services:** use lint action to fix markdown [CORE-730] ([#50761](https://github.com/time-loop/clickup/issues/50761)) ([291e752](https://github.com/time-loop/clickup/commit/291e752aabef712a938889747d43a78ffd2a2d4b))

## [1.3.17](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.17) (2025-01-06)

## [1.3.16](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.16) (2024-12-27)

### Features

-   **core-services:** post chat messages using impersonation [CORE-606] ([#49485](https://github.com/time-loop/clickup/issues/49485)) ([f722264](https://github.com/time-loop/clickup/commit/f72226403ca490464e8f28ca03fc76c8908d9084))

## [1.3.15](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.15) (2024-12-26)

## [1.3.14](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.14) (2024-12-20)

## [1.3.13](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.13) (2024-12-20)

### Features

-   **fields:** add field_type, name, and deleted properties to the response of field-v3 bulk API [CLK-595747] ([#49319](https://github.com/time-loop/clickup/issues/49319)) ([f508d9b](https://github.com/time-loop/clickup/commit/f508d9bbc1d23ad136e1bb65092e581c40a6cc32))

## [1.3.12](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.12) (2024-12-10)

## [1.3.11](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.11) (2024-12-06)

## [1.3.10](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.10) (2024-12-05)

## [1.3.9](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.9) (2024-12-05)

### Features

-   **fields:** Bootstrap logic and types for suggested fields [CLK-562398] ([#48255](https://github.com/time-loop/clickup/issues/48255)) ([8a92699](https://github.com/time-loop/clickup/commit/8a9269960bb0a68b92f89ef081cc17946aa6931a))

## [1.3.8](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.8) (2024-12-03)

## [1.3.7](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.7) (2024-12-03)

### Features

-   **fields:** Add suggestions controller [CLK-562398] ([#48147](https://github.com/time-loop/clickup/issues/48147)) ([58deecf](https://github.com/time-loop/clickup/commit/58deecfbe091dbddb0c572a15ea4b515a1a104d4))

## [1.3.6](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.6) (2024-12-03)

## [1.3.5](https://github.com/time-loop/clickup/compare/<EMAIL>-contracts@1.3.5) (2024-11-28)

## 0.0.1 (2024-11-28)

### Features

-   **fields:** ability to limit the number of field options [CLK-207851] ([#20844](https://github.com/time-loop/clickup/issues/20844)) ([d1d11bc](https://github.com/time-loop/clickup/commit/d1d11bc47ff67b64fc29ce54d6572f23c11af77f))
-   **fields:** add interface for getting fields to fields… [CLK-135362] ([#16478](https://github.com/time-loop/clickup/issues/16478)) ([9a1b0bc](https://github.com/time-loop/clickup/commit/9a1b0bce3d697b2412987fdd80f6ace9c55bcae1))
-   **fields:** add location archived status to the bulk response [CLK475624] [CLK-475624] ([#36579](https://github.com/time-loop/clickup/issues/36579)) ([58d2ecf](https://github.com/time-loop/clickup/commit/58d2ecf5c65ed8375a70226ca696059f2b30c01a))
-   **fields:** Add reference from field-contracts to utils-constants [CLK-138274] ([#16426](https://github.com/time-loop/clickup/issues/16426)) ([31c5401](https://github.com/time-loop/clickup/commit/31c54017158ed06251a31169a605e43f3c2a33af))
-   **fields:** Add several conversions [CLK-135278] ([#16857](https://github.com/time-loop/clickup/issues/16857)) ([d93d84a](https://github.com/time-loop/clickup/commit/d93d84afb16e624a3bee90ee0b606936d16b3f8c))
-   **fields:** add type to the bulk field get experience endpoint [RDMP-12406] ([#45550](https://github.com/time-loop/clickup/issues/45550)) ([7e988ec](https://github.com/time-loop/clickup/commit/7e988ec5bdbce2ec521c1463a875f47f6bfa3356))
-   **fields:** added search jobs API for fields service [CLK-274677] ([#29199](https://github.com/time-loop/clickup/issues/29199)) ([20c69e9](https://github.com/time-loop/clickup/commit/20c69e90effb6e50b16e7d7cb22c544f5efcd0c9))
-   **fields:** Apply field reference limits to the new operations [CLK-173393] ([#18562](https://github.com/time-loop/clickup/issues/18562)) ([e8ab6c3](https://github.com/time-loop/clickup/commit/e8ab6c3e95765adc97a6393d31c180a052a13ab1))
-   **fields:** Contract lib and update location configuration endpoint [CLK-136269] ([#16298](https://github.com/time-loop/clickup/issues/16298)) ([6b5b10f](https://github.com/time-loop/clickup/commit/6b5b10fbc0dc9aa8183784a91155d2e665b54a06))
-   **fields:** contracts and endpoints for field members and permissions [CLK-217203] ([#21596](https://github.com/time-loop/clickup/issues/21596)) ([c8236bb](https://github.com/time-loop/clickup/commit/c8236bb6b4c8d9328634943ec207c22ebf9f5fdc))
-   **fields:** Duplicate Field endpoint stub [CLK-136752] ([#16427](https://github.com/time-loop/clickup/issues/16427)) ([66005c0](https://github.com/time-loop/clickup/commit/66005c0f411d2fa72aa3d02ab35f4b47d431f1d0))
-   **fields:** Field Service - OVM events [CLK-184642] ([#18220](https://github.com/time-loop/clickup/issues/18220)) ([f18d435](https://github.com/time-loop/clickup/commit/f18d435dc1ac85187f60c842841267daa57c3309))
-   **fields:** field-service e2e tests for field members [CLK-464333] ([#35597](https://github.com/time-loop/clickup/issues/35597)) ([dda8a2c](https://github.com/time-loop/clickup/commit/dda8a2c482063643a34084a2e548f666ed213043))
-   **fields:** Fields bulk API - locations [CLK-261253] ([#26454](https://github.com/time-loop/clickup/issues/26454)) ([8410f66](https://github.com/time-loop/clickup/commit/8410f669cec253ca2af54d36b29cbbc51e4ce48f)), closes [#26492](https://github.com/time-loop/clickup/issues/26492)
-   **fields:** get bulk field locations API [CLK-248612] ([#25558](https://github.com/time-loop/clickup/issues/25558)) ([6c8ddbc](https://github.com/time-loop/clickup/commit/6c8ddbc21c1800ae1d5e6bee27c1a655e2d597d8)), closes [#25589](https://github.com/time-loop/clickup/issues/25589) [#25628](https://github.com/time-loop/clickup/issues/25628)
-   **fields:** Get field usages endpoint, implement other usage types, increase coverage [CLK-136751] ([#17929](https://github.com/time-loop/clickup/issues/17929)) ([e5e7232](https://github.com/time-loop/clickup/commit/e5e7232d582476d8530489fc768c968a9c75fb02))
-   **fields:** http post api for field level permissions [CLK-509751] ([#39046](https://github.com/time-loop/clickup/issues/39046)) ([cb2f7f5](https://github.com/time-loop/clickup/commit/cb2f7f51e8ad7f1ab2d00988d85c8576b98b5e78))
-   **fields:** Merge fields endpoint stub [CLK-136754] ([#16508](https://github.com/time-loop/clickup/issues/16508)) ([bbfa508](https://github.com/time-loop/clickup/commit/bbfa5082a458f8dc4c5d61e4f76b618f6c5664e4))
-   **fields:** move field endpoint contracts and guard [CLK-136751] ([#17611](https://github.com/time-loop/clickup/issues/17611)) ([0c2256c](https://github.com/time-loop/clickup/commit/0c2256c870c2dec6ff883840d105ef2729c1010b))
-   **fields:** Shell of conversion endpoint [CLK-135278] ([#16760](https://github.com/time-loop/clickup/issues/16760)) ([f916b2c](https://github.com/time-loop/clickup/commit/f916b2c0237d4ebd65b02f1d9a65735ea2af01d1))
-   **fields:** Use user access library for field member APIs [CLK-464333] ([#35707](https://github.com/time-loop/clickup/issues/35707)) ([c7f6a48](https://github.com/time-loop/clickup/commit/c7f6a488dd7d5340ee6f57836294939923766f48))
-   **hierarchy:** Workspace bulk API [CLK-14114] ([#17358](https://github.com/time-loop/clickup/issues/17358)) ([fa18174](https://github.com/time-loop/clickup/commit/fa18174ff5258fee40c1c372e36c66e4e8e43c85))

### Bug Fixes

-   **core-services:** run field-contract through actual nx executor to build [CLK-487093] ([e5636bf](https://github.com/time-loop/clickup/commit/e5636bf7986fc3f5cf36517a164fadf523b822ab))
-   **fields:** remove a dependency from contracts lib [CLK-135382] ([#17785](https://github.com/time-loop/clickup/issues/17785)) ([afab535](https://github.com/time-loop/clickup/commit/afab5356d9fe7eb92a579f9a68a5820771939074))
