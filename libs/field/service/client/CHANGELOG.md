# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [0.0.67](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.67) (2025-06-03)

## [0.0.66](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.66) (2025-06-02)

## [0.0.65](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.65) (2025-05-29)

## [0.0.64](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.64) (2025-05-27)


### Features

* **fields:**  Make prompt for custom dropdown field optional - [AILAUNCH-1117] ([#60116](https://github.com/time-loop/clickup/issues/60116)) ([252eca7](https://github.com/time-loop/clickup/commit/252eca74f08d0b1056c76b28af750328fa1ced12))

## [0.0.63](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.63) (2025-05-01)

## [0.0.62](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.62) (2025-04-29)


### Features

* **access-management:** add custom permission level to prefetched data in authz [CU-8xdfmcvvx] ([#57250](https://github.com/time-loop/clickup/issues/57250)) ([1c622df](https://github.com/time-loop/clickup/commit/1c622df58c53980d2597be3bf43e7bb010e2ed04))

## [0.0.61](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.61) (2025-04-28)


### Features

* **fields:** Add new fields API for getting all fields across multiple locations [CLK-669769] ([#57693](https://github.com/time-loop/clickup/issues/57693)) ([044e31a](https://github.com/time-loop/clickup/commit/044e31acf261727cbeb5b986206e8dd4a6d94675))

## [0.0.60](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.60) (2025-04-24)


### Features

* **fields:** command and handler for custom dropdown AICF [CU-8xdfm9jr1] ([88b5a91](https://github.com/time-loop/clickup/commit/88b5a9124c7417b9f1dff9a7e77a043fc7b7eff9))


### Bug Fixes

* **fields:** add custom dropdown to `supportedConfigDtos` [AILAUNCH-295] ([#56961](https://github.com/time-loop/clickup/issues/56961)) ([5ad48fe](https://github.com/time-loop/clickup/commit/5ad48fe8f4aba6b7cba0908c3a2d387b97e0b689))

## [0.0.59](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.59) (2025-04-10)


### Features

* **ai:** allowed related task types in summary [CLK-647854] ([#56315](https://github.com/time-loop/clickup/issues/56315)) ([3695159](https://github.com/time-loop/clickup/commit/36951597a659fc2898e812c3eb38cfe1a3f96879)), closes [#56349](https://github.com/time-loop/clickup/issues/56349)

## [0.0.58](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.58) (2025-04-08)

## [0.0.57](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.57) (2025-04-03)

## [0.0.56](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.56) (2025-03-27)

## [0.0.55](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.55) (2025-03-25)

## [0.0.54](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.54) (2025-03-20)

## [0.0.53](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.53) (2025-03-20)


### Features

* **fields:** Update field service client [CLK-630987] ([#54684](https://github.com/time-loop/clickup/issues/54684)) ([48ef9a6](https://github.com/time-loop/clickup/commit/48ef9a6485ad85b2b0c843358176454c425677cc))

## [0.0.52](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.52) (2025-03-19)

## [0.0.51](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.51) (2025-03-18)

## [0.0.50](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.50) (2025-03-14)


### Bug Fixes

* **fields:** fix openapi_generator issues [AI-1258] ([#53853](https://github.com/time-loop/clickup/issues/53853)) ([3e2f9af](https://github.com/time-loop/clickup/commit/3e2f9afb50029177a78c7e47d78ef706107f02bd))

## [0.0.49](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.49) (2025-03-06)

## [0.0.48](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.48) (2025-02-19)

## [0.0.47](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.47) (2025-02-13)

## [0.0.46](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.46) (2025-02-05)

## [0.0.45](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.45) (2025-02-03)

## [0.0.44](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.44) (2025-01-30)

## [0.0.43](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.43) (2025-01-24)

## [0.0.42](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.42) (2025-01-22)

## [0.0.41](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.41) (2025-01-06)

## [0.0.40](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.40) (2024-12-26)

## [0.0.39](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.39) (2024-12-20)

## [0.0.38](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.38) (2024-12-20)

## [0.0.37](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.37) (2024-12-10)

## [0.0.36](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.36) (2024-12-06)

## [0.0.35](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.35) (2024-12-03)

## [0.0.34](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.34) (2024-12-03)

## [0.0.33](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.33) (2024-12-03)

## [0.0.32](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.32) (2024-11-28)

## [0.0.31](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.31) (2024-11-22)

## [0.0.30](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.30) (2024-11-14)

## [0.0.29](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.29) (2024-11-14)

## [0.0.28](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.28) (2024-11-12)

## [0.0.27](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.27) (2024-10-16)

## [0.0.26](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.26) (2024-10-10)

## [0.0.25](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.25) (2024-10-08)

## [0.0.24](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.24) (2024-10-03)

## [0.0.23](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.23) (2024-10-02)

## [0.0.22](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.22) (2024-10-02)

## [0.0.21](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.21) (2024-09-26)

## [0.0.20](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.20) (2024-09-24)

## [0.0.19](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.19) (2024-09-24)

## [0.0.18](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.18) (2024-09-23)

## [0.0.17](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.17) (2024-09-11)


### Features

* **fields:** move acl perm logic to the separate service [CLK-537015] ([#43449](https://github.com/time-loop/clickup/issues/43449)) ([4b9a9d2](https://github.com/time-loop/clickup/commit/4b9a9d228c2af8e3ea57e09fe42c9c4179d7cdaa))

## [0.0.16](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.16) (2024-09-10)


### Features

* **slapdash:** Adds `App Object` and `App Object Property` Custom Fields types [CLK-538658] ([#43044](https://github.com/time-loop/clickup/issues/43044)) ([e8f9240](https://github.com/time-loop/clickup/commit/e8f9240964c0048cbbd04435083b2f519d06b7f8))

## [0.0.15](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.15) (2024-09-04)


### Features

* **fields:** add new custom field type - button [RDMP-11462] ([#42966](https://github.com/time-loop/clickup/issues/42966)) ([41a30a7](https://github.com/time-loop/clickup/commit/41a30a73977bbbffa96841ab87b6320458a2d477))

## [0.0.15](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.15) (2024-08-08)

## [0.0.15](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.15) (2024-08-07)

## [0.0.14](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.14) (2024-07-31)

## [0.0.13](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.13) (2024-07-30)

## [0.0.12](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.12) (2024-07-24)

## [0.0.11](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.11) (2024-07-10)

## [0.0.10](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.10) (2024-07-02)

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-06-22)

## [0.0.8](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.8) (2024-06-20)


### Bug Fixes

* **core-services:** Fix OpenAPI specs of health checks [CLK-515791] ([#39792](https://github.com/time-loop/clickup/issues/39792)) ([9bac3a8](https://github.com/time-loop/clickup/commit/9bac3a88aa9a7d601a329e93d356ebb48c2b1128))

## [0.0.7](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.7) (2024-06-17)

## [0.0.6](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.6) (2024-06-13)

## [0.0.5](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.5) (2024-06-13)

## [0.0.4](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.4) (2024-06-13)

## [0.0.3](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.3) (2024-06-13)


### Features

* **fields:** add package.json to the field-service-client [CLK-514961] ([#39578](https://github.com/time-loop/clickup/issues/39578)) ([ac6644f](https://github.com/time-loop/clickup/commit/ac6644f4dbd1819d24574d8d2220f477c36a82c2))

## [0.0.2](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.2) (2024-06-13)


### Features

* **fields:** fix the name of field service swagger file [CLK-514961] ([#39577](https://github.com/time-loop/clickup/issues/39577)) ([eb17f16](https://github.com/time-loop/clickup/commit/eb17f1655598b082f06a00ca43a59f790fbb58cf))

## 0.0.1 (2024-06-13)


### Features

* **fields:** create field v3 api client [CLK-514961] ([#39533](https://github.com/time-loop/clickup/issues/39533)) ([72d2e93](https://github.com/time-loop/clickup/commit/72d2e933787d8c006fd2028650915c96a31e0c47))
