# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [0.0.86](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.86) (2025-06-03)

## [0.0.85](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.85) (2025-06-02)

## [0.0.84](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.84) (2025-05-29)

## [0.0.83](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.83) (2025-05-01)

## [0.0.82](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.82) (2025-04-30)


### Features

* **access-management:** Return ACL data with workspace-v3 [CLK-604475] ([#57877](https://github.com/time-loop/clickup/issues/57877)) ([7669c60](https://github.com/time-loop/clickup/commit/7669c606a7248ff32a58cd52bb520c36bdc0c554))

## [0.0.81](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.81) (2025-04-29)


### Features

* **access-management:** add custom permission level to prefetched data in authz [CU-8xdfmcvvx] ([#57250](https://github.com/time-loop/clickup/issues/57250)) ([1c622df](https://github.com/time-loop/clickup/commit/1c622df58c53980d2597be3bf43e7bb010e2ed04))

## [0.0.80](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.80) (2025-04-24)

## [0.0.79](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.79) (2025-04-03)

## [0.0.78](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.78) (2025-03-27)

## [0.0.77](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.77) (2025-03-27)


### Features

* **access-management:** Converting LMs/IG to Guest for selected guest domains list [CLK-632032] ([#55090](https://github.com/time-loop/clickup/issues/55090)) ([5ffa7ab](https://github.com/time-loop/clickup/commit/5ffa7abe3c474b785d9fead17999657a680884ec))

## [0.0.76](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.76) (2025-03-25)

## [0.0.75](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.75) (2025-03-21)


### Features

* **data-platform:** Data retention service[INFRA-35734] ([#54634](https://github.com/time-loop/clickup/issues/54634)) ([0224b8e](https://github.com/time-loop/clickup/commit/0224b8e707f12e23cb748b1121b5f53ddbcfc07f))

## [0.0.74](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.74) (2025-03-20)

## [0.0.73](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.73) (2025-03-19)

## [0.0.72](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.72) (2025-03-19)


### Features

* **user-platform:** implement layout_setting as workspaceSettings property [CLK-638029] ([#54371](https://github.com/time-loop/clickup/issues/54371)) ([5c5e9be](https://github.com/time-loop/clickup/commit/5c5e9be75e431858d6882b68e1ea6d9e75c88da3))

## [0.0.71](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.71) (2025-03-18)

## [0.0.70](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.70) (2025-03-17)


### Bug Fixes

* **access-management:** fix type on request/response models and add error logging/exception for create setting  [CHAT-6916] ([#54503](https://github.com/time-loop/clickup/issues/54503)) ([1b48562](https://github.com/time-loop/clickup/commit/1b48562aab34faa53c2254ee7c7ce50ba1b59ca2))

## [0.0.69](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.69) (2025-03-14)


### Features

* **access-management:** workspace data retention settings service[CHAT-6916] ([#54231](https://github.com/time-loop/clickup/issues/54231)) ([6bf4bd6](https://github.com/time-loop/clickup/commit/6bf4bd6808a4be1f5c9fa64c890aff158afda002))

## [0.0.68](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.68) (2025-03-06)

## [0.0.67](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.67) (2025-02-19)

## [0.0.66](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.66) (2025-02-13)

## [0.0.65](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.65) (2025-02-05)

## [0.0.64](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.64) (2025-02-03)

## [0.0.63](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.63) (2025-01-30)

## [0.0.62](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.62) (2025-01-22)

## [0.0.61](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.61) (2025-01-06)

## [0.0.60](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.60) (2024-12-26)

## [0.0.59](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.59) (2024-12-20)

## [0.0.58](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.58) (2024-12-10)

## [0.0.57](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.57) (2024-12-06)

## [0.0.56](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.56) (2024-12-03)

## [0.0.55](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.55) (2024-12-03)

## [0.0.54](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.54) (2024-11-28)

## [0.0.53](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.53) (2024-11-22)

## [0.0.52](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.52) (2024-11-14)

## [0.0.51](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.51) (2024-11-14)

## [0.0.50](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.50) (2024-11-12)

## [0.0.49](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.49) (2024-10-16)

## [0.0.48](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.48) (2024-10-10)

## [0.0.47](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.47) (2024-10-08)

## [0.0.46](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.46) (2024-10-03)

## [0.0.45](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.45) (2024-10-02)

## [0.0.44](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.44) (2024-10-02)

## [0.0.43](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.43) (2024-09-26)

## [0.0.42](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.42) (2024-09-24)

## [0.0.41](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.41) (2024-09-24)

## [0.0.40](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.40) (2024-09-23)

## [0.0.39](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.39) (2024-09-10)


### Features

* **slapdash:** Adds `App Object` and `App Object Property` Custom Fields types [CLK-538658] ([#43044](https://github.com/time-loop/clickup/issues/43044)) ([e8f9240](https://github.com/time-loop/clickup/commit/e8f9240964c0048cbbd04435083b2f519d06b7f8))

## [0.0.38](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.38) (2024-09-04)

## [0.0.37](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.37) (2024-09-03)

## [0.0.37](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.37) (2024-08-08)

## [0.0.37](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.37) (2024-08-07)

## [0.0.36](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.36) (2024-07-31)


### Features

* **access-management:** Workspace role metadata update API [CLK-518180] ([#41309](https://github.com/time-loop/clickup/issues/41309)) ([3dcb72e](https://github.com/time-loop/clickup/commit/3dcb72e4aeff916fad5bc43220e5a666e7cd1540))


### Bug Fixes

* **access-management:** Fix workspace service client publish [CLK-518180] ([#41383](https://github.com/time-loop/clickup/issues/41383)) ([f313786](https://github.com/time-loop/clickup/commit/f313786c21ca5d2ff46dfa0cbcf78c2cc18c1865))

## [0.0.35](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.35) (2024-07-30)

## [0.0.34](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.34) (2024-07-24)

## [0.0.33](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.33) (2024-07-10)


### Features

* **access-management:** Add allowUsersOutsideWorkspaceRequestAccess workspace_settings [CLK-520444] ([#40217](https://github.com/time-loop/clickup/issues/40217)) ([bfb232c](https://github.com/time-loop/clickup/commit/bfb232c2914efdbb0b42f1c67275665bc486b7ee))

## [0.0.32](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.32) (2024-07-02)

## [0.0.31](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.31) (2024-06-22)


### Features

* **access-management:** add workspace role permissions to get workspace v3 api [CLK-515861] ([#39691](https://github.com/time-loop/clickup/issues/39691)) ([10c283e](https://github.com/time-loop/clickup/commit/10c283e2c716111dfb88c49f87da0bd7464d482d))

## [0.0.30](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.30) (2024-06-17)

## [0.0.29](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.29) (2024-06-13)

## [0.0.28](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.28) (2024-06-13)

## [0.0.27](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.27) (2024-06-13)


### Features

* **fields:** uncomment api client generators [CLK-515028] ([#39542](https://github.com/time-loop/clickup/issues/39542)) ([352f540](https://github.com/time-loop/clickup/commit/352f540f366e824e384ef5bf706e83a41772aa7c))

## [0.0.26](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.26) (2024-06-13)

## [0.0.25](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.25) (2024-06-13)

## [0.0.24](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.24) (2024-06-12)

## [0.0.23](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.23) (2024-05-17)

## [0.0.22](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.22) (2024-05-17)

## [0.0.20](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.20) (2024-05-13)

## [0.0.20](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.20) (2024-05-02)

## [0.0.20](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.20) (2024-04-29)

## [0.0.20](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.20) (2024-04-28)

## [0.0.19](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.19) (2024-04-16)

## [0.0.18](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.18) (2024-04-15)

## [0.0.17](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.17) (2024-04-12)

## [0.0.16](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.16) (2024-04-09)

## [0.0.15](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.15) (2024-03-29)


### Bug Fixes

* **data-platform:** Access check to honor service status[CLK-483097] ([#36568](https://github.com/time-loop/clickup/issues/36568)) ([75b6129](https://github.com/time-loop/clickup/commit/75b61294b2404a27d5af732430c9a9ddd640b82a))

## [0.0.14](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.14) (2024-03-29)

## [0.0.13](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.13) (2024-03-28)
