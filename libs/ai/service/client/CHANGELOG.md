# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [0.0.88](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.88) (2025-06-03)

## [0.0.87](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.87) (2025-06-02)

## [0.0.86](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.86) (2025-05-29)

## [0.0.85](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.85) (2025-05-27)


### Features

* **fields:**  Make prompt for custom dropdown field optional - [AILAUNCH-1117] ([#60116](https://github.com/time-loop/clickup/issues/60116)) ([252eca7](https://github.com/time-loop/clickup/commit/252eca74f08d0b1056c76b28af750328fa1ced12))

## [0.0.84](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.84) (2025-05-22)


### Features

* **fields:** Extend properties endpoint with type and pass list id as a param - [CLK-559996] ([#59732](https://github.com/time-loop/clickup/issues/59732)) ([d79f44a](https://github.com/time-loop/clickup/commit/d79f44ad2485c174b3cdb564e1b624915f5a8f60))

## [0.0.83](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.83) (2025-05-16)


### Features

* **fields:** Add support for inferring task type with AI - [CLK-559996] ([#58497](https://github.com/time-loop/clickup/issues/58497)) ([166b1f3](https://github.com/time-loop/clickup/commit/166b1f373a65da49646b937ca0c89de75dd2fd6f))

## [0.0.82](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.82) (2025-05-14)

## [0.0.81](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.81) (2025-05-07)

## [0.0.80](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.80) (2025-05-01)

## [0.0.79](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.79) (2025-04-29)

## [0.0.78](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.78) (2025-04-24)

## [0.0.77](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.77) (2025-04-23)


### Features

* **fields:** command and handler for custom dropdown AICF [CU-8xdfm9jr1] ([88b5a91](https://github.com/time-loop/clickup/commit/88b5a9124c7417b9f1dff9a7e77a043fc7b7eff9))


### Bug Fixes

* **ai:** Add logs to backfill status [AI-788] ([d8517a3](https://github.com/time-loop/clickup/commit/d8517a3fdf115593c35f35cd9e3a8c49bad300e6))
* **ai:** Remove deprecated AI service endpoint [CLK-623983] ([#57330](https://github.com/time-loop/clickup/issues/57330)) ([a743241](https://github.com/time-loop/clickup/commit/a74324174326cd45746734a49e0d23bcf577f68e))
* **fields:** add custom dropdown to `supportedConfigDtos` [AILAUNCH-295] ([#56961](https://github.com/time-loop/clickup/issues/56961)) ([5ad48fe](https://github.com/time-loop/clickup/commit/5ad48fe8f4aba6b7cba0908c3a2d387b97e0b689))

## [0.0.76](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.76) (2025-04-15)


### Features

* **fields:** AI task Property automation action [AILAUNCH-4] ([#56699](https://github.com/time-loop/clickup/issues/56699)) ([327525f](https://github.com/time-loop/clickup/commit/327525f63a2fb33c7050384aa24fe883c4bd2210))

## [0.0.75](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.75) (2025-04-14)


### Bug Fixes

* **ai:** Backfill answers agent [AI-788] ([#56447](https://github.com/time-loop/clickup/issues/56447)) ([eb47b62](https://github.com/time-loop/clickup/commit/eb47b62551fb45e42a52479e58d760e62ffb6d70))

## [0.0.74](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.74) (2025-04-10)


### Features

* **ai:** allowed related task types in summary [CLK-647854] ([#56315](https://github.com/time-loop/clickup/issues/56315)) ([3695159](https://github.com/time-loop/clickup/commit/36951597a659fc2898e812c3eb38cfe1a3f96879)), closes [#56349](https://github.com/time-loop/clickup/issues/56349)

## [0.0.73](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.73) (2025-04-08)

## [0.0.72](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.72) (2025-04-07)

## [0.0.71](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.71) (2025-04-03)

## [0.0.70](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.70) (2025-03-27)

## [0.0.69](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.69) (2025-03-27)


### Bug Fixes

* **ai:** Skip invalid video attachments [CLK-643945] ([#55292](https://github.com/time-loop/clickup/issues/55292)) ([82ec9b3](https://github.com/time-loop/clickup/commit/82ec9b368f9652d28f9e970d9e8a7d78980f2d8c))

## [0.0.68](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.68) (2025-03-25)

## [0.0.67](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.67) (2025-03-20)

## [0.0.66](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.66) (2025-03-19)

## [0.0.65](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.65) (2025-03-18)

## [0.0.64](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.64) (2025-03-14)


### Bug Fixes

* **fields:** fix openapi_generator issues [AI-1258] ([#53853](https://github.com/time-loop/clickup/issues/53853)) ([3e2f9af](https://github.com/time-loop/clickup/commit/3e2f9afb50029177a78c7e47d78ef706107f02bd))

## [0.0.63](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.63) (2025-03-06)

## [0.0.62](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.62) (2025-02-19)

## [0.0.61](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.61) (2025-02-13)

## [0.0.60](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.60) (2025-02-05)

## [0.0.59](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.59) (2025-02-03)

## [0.0.58](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.58) (2025-01-30)

## [0.0.57](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.57) (2025-01-22)

## [0.0.56](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.56) (2025-01-06)

## [0.0.55](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.55) (2024-12-26)

## [0.0.54](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.54) (2024-12-20)

## [0.0.53](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.53) (2024-12-10)

## [0.0.52](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.52) (2024-12-06)

## [0.0.51](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.51) (2024-12-03)

## [0.0.50](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.50) (2024-12-03)

## [0.0.49](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.49) (2024-11-28)

## [0.0.48](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.48) (2024-11-22)

## [0.0.47](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.47) (2024-11-14)

## [0.0.46](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.46) (2024-11-14)

## [0.0.45](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.45) (2024-11-12)

## [0.0.44](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.44) (2024-10-16)

## [0.0.43](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.43) (2024-10-10)

## [0.0.42](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.42) (2024-10-08)

## [0.0.41](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.41) (2024-10-03)

## [0.0.40](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.40) (2024-10-02)

## [0.0.39](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.39) (2024-10-02)

## [0.0.38](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.38) (2024-09-26)

## [0.0.37](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.37) (2024-09-24)

## [0.0.36](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.36) (2024-09-24)

## [0.0.35](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.35) (2024-09-23)

## [0.0.34](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.34) (2024-09-23)


### Features

* **fields:** add t-shirt size field ai command handlers [CLK-546973] ([#44132](https://github.com/time-loop/clickup/issues/44132)) ([f9f380c](https://github.com/time-loop/clickup/commit/f9f380c31285a2cce7bb348a6c61f03808c0b905))

## [0.0.33](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.33) (2024-09-23)


### Features

* **fields:** implement task action items command handler for ai fields [CLK-546973] ([#44119](https://github.com/time-loop/clickup/issues/44119)) ([fd75412](https://github.com/time-loop/clickup/commit/fd75412131fb0178cdd8012079c977d855eab9a0))

## [0.0.32](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.32) (2024-09-23)

## [0.0.31](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.31) (2024-09-10)

## [0.0.30](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.30) (2024-09-04)

## [0.0.30](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.30) (2024-08-08)

## [0.0.30](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.30) (2024-08-07)

## [0.0.29](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.29) (2024-08-02)


### Features

* **fields:** improve the quality of the custom field value ai response <!-- [CLK-522656] ([#41409](https://github.com/time-loop/clickup/issues/41409)) ([d893d1c](https://github.com/time-loop/clickup/commit/d893d1c5656b6f46e7fde902f0478c4c0cace5d8))

## [0.0.28](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.28) (2024-07-31)

## [0.0.27](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.27) (2024-07-30)

## [0.0.26](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.26) (2024-07-24)

## [0.0.25](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.25) (2024-07-10)

## [0.0.24](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.24) (2024-07-02)

## [0.0.23](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.23) (2024-06-22)

## [0.0.22](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.22) (2024-06-20)


### Bug Fixes

* **core-services:** Fix OpenAPI specs of health checks [CLK-515791] ([#39792](https://github.com/time-loop/clickup/issues/39792)) ([9bac3a8](https://github.com/time-loop/clickup/commit/9bac3a88aa9a7d601a329e93d356ebb48c2b1128))

## [0.0.21](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.21) (2024-06-18)


### Features

* **fields:** update error handling [CLK-501555] ([#39620](https://github.com/time-loop/clickup/issues/39620)) ([e9e12f2](https://github.com/time-loop/clickup/commit/e9e12f2b11742eef655a71e1d4d6e30ee9f7db61))

## [0.0.20](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.20) (2024-06-17)

## [0.0.19](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.19) (2024-06-13)

## [0.0.18](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.18) (2024-06-13)

## [0.0.17](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.17) (2024-06-13)


### Features

* **fields:** uncomment api client generators [CLK-515028] ([#39542](https://github.com/time-loop/clickup/issues/39542)) ([352f540](https://github.com/time-loop/clickup/commit/352f540f366e824e384ef5bf706e83a41772aa7c))

## [0.0.16](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.16) (2024-06-13)

## [0.0.15](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.15) (2024-06-13)

## [0.0.14](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.14) (2024-06-12)

## [0.0.13](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.13) (2024-05-17)

## [0.0.12](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.12) (2024-05-17)

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-05-13)


### Features

* **ai:** add transcriptions to clips in search [RDMP-9209] ([#35185](https://github.com/time-loop/clickup/issues/35185)) ([328a63a](https://github.com/time-loop/clickup/commit/328a63a4657ac69870eeeeeefeb7643aa857547b))
* **ai:** endpoint to delete workspace data [CLK-467897] ([#34726](https://github.com/time-loop/clickup/issues/34726)) ([fbcc584](https://github.com/time-loop/clickup/commit/fbcc584d7f8bb9f6ed91b61b9890bf21213f8a82))
* **ai:** endpoint to load multiple transcriptions [RDMP-9891] ([#35124](https://github.com/time-loop/clickup/issues/35124)) ([24662a5](https://github.com/time-loop/clickup/commit/24662a5e8405418ba4eac51e741a260409c0d3b9))
* **ai:** refactor consumer setup using kafka util module [RDMP-9947] ([#35767](https://github.com/time-loop/clickup/issues/35767)) ([eaab78c](https://github.com/time-loop/clickup/commit/eaab78cf1c715b3a728b21622b2e4fe62bd9f859))

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-05-02)


### Features

* **ai:** add transcriptions to clips in search [RDMP-9209] ([#35185](https://github.com/time-loop/clickup/issues/35185)) ([328a63a](https://github.com/time-loop/clickup/commit/328a63a4657ac69870eeeeeefeb7643aa857547b))
* **ai:** endpoint to delete workspace data [CLK-467897] ([#34726](https://github.com/time-loop/clickup/issues/34726)) ([fbcc584](https://github.com/time-loop/clickup/commit/fbcc584d7f8bb9f6ed91b61b9890bf21213f8a82))
* **ai:** endpoint to load multiple transcriptions [RDMP-9891] ([#35124](https://github.com/time-loop/clickup/issues/35124)) ([24662a5](https://github.com/time-loop/clickup/commit/24662a5e8405418ba4eac51e741a260409c0d3b9))
* **ai:** refactor consumer setup using kafka util module [RDMP-9947] ([#35767](https://github.com/time-loop/clickup/issues/35767)) ([eaab78c](https://github.com/time-loop/clickup/commit/eaab78cf1c715b3a728b21622b2e4fe62bd9f859))

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-04-29)


### Features

* **ai:** add transcriptions to clips in search [RDMP-9209] ([#35185](https://github.com/time-loop/clickup/issues/35185)) ([328a63a](https://github.com/time-loop/clickup/commit/328a63a4657ac69870eeeeeefeb7643aa857547b))
* **ai:** endpoint to delete workspace data [CLK-467897] ([#34726](https://github.com/time-loop/clickup/issues/34726)) ([fbcc584](https://github.com/time-loop/clickup/commit/fbcc584d7f8bb9f6ed91b61b9890bf21213f8a82))
* **ai:** endpoint to load multiple transcriptions [RDMP-9891] ([#35124](https://github.com/time-loop/clickup/issues/35124)) ([24662a5](https://github.com/time-loop/clickup/commit/24662a5e8405418ba4eac51e741a260409c0d3b9))
* **ai:** refactor consumer setup using kafka util module [RDMP-9947] ([#35767](https://github.com/time-loop/clickup/issues/35767)) ([eaab78c](https://github.com/time-loop/clickup/commit/eaab78cf1c715b3a728b21622b2e4fe62bd9f859))

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-04-28)


### Features

* **ai:** add transcriptions to clips in search [RDMP-9209] ([#35185](https://github.com/time-loop/clickup/issues/35185)) ([328a63a](https://github.com/time-loop/clickup/commit/328a63a4657ac69870eeeeeefeb7643aa857547b))
* **ai:** endpoint to delete workspace data [CLK-467897] ([#34726](https://github.com/time-loop/clickup/issues/34726)) ([fbcc584](https://github.com/time-loop/clickup/commit/fbcc584d7f8bb9f6ed91b61b9890bf21213f8a82))
* **ai:** endpoint to load multiple transcriptions [RDMP-9891] ([#35124](https://github.com/time-loop/clickup/issues/35124)) ([24662a5](https://github.com/time-loop/clickup/commit/24662a5e8405418ba4eac51e741a260409c0d3b9))
* **ai:** refactor consumer setup using kafka util module [RDMP-9947] ([#35767](https://github.com/time-loop/clickup/issues/35767)) ([eaab78c](https://github.com/time-loop/clickup/commit/eaab78cf1c715b3a728b21622b2e4fe62bd9f859))

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-04-16)


### Features

* **ai:** add transcriptions to clips in search [RDMP-9209] ([#35185](https://github.com/time-loop/clickup/issues/35185)) ([328a63a](https://github.com/time-loop/clickup/commit/328a63a4657ac69870eeeeeefeb7643aa857547b))
* **ai:** endpoint to delete workspace data [CLK-467897] ([#34726](https://github.com/time-loop/clickup/issues/34726)) ([fbcc584](https://github.com/time-loop/clickup/commit/fbcc584d7f8bb9f6ed91b61b9890bf21213f8a82))
* **ai:** endpoint to load multiple transcriptions [RDMP-9891] ([#35124](https://github.com/time-loop/clickup/issues/35124)) ([24662a5](https://github.com/time-loop/clickup/commit/24662a5e8405418ba4eac51e741a260409c0d3b9))
* **ai:** refactor consumer setup using kafka util module [RDMP-9947] ([#35767](https://github.com/time-loop/clickup/issues/35767)) ([eaab78c](https://github.com/time-loop/clickup/commit/eaab78cf1c715b3a728b21622b2e4fe62bd9f859))

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-04-15)


### Features

* **ai:** add transcriptions to clips in search [RDMP-9209] ([#35185](https://github.com/time-loop/clickup/issues/35185)) ([328a63a](https://github.com/time-loop/clickup/commit/328a63a4657ac69870eeeeeefeb7643aa857547b))
* **ai:** endpoint to delete workspace data [CLK-467897] ([#34726](https://github.com/time-loop/clickup/issues/34726)) ([fbcc584](https://github.com/time-loop/clickup/commit/fbcc584d7f8bb9f6ed91b61b9890bf21213f8a82))
* **ai:** endpoint to load multiple transcriptions [RDMP-9891] ([#35124](https://github.com/time-loop/clickup/issues/35124)) ([24662a5](https://github.com/time-loop/clickup/commit/24662a5e8405418ba4eac51e741a260409c0d3b9))
* **ai:** refactor consumer setup using kafka util module [RDMP-9947] ([#35767](https://github.com/time-loop/clickup/issues/35767)) ([eaab78c](https://github.com/time-loop/clickup/commit/eaab78cf1c715b3a728b21622b2e4fe62bd9f859))

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-04-12)


### Features

* **ai:** add transcriptions to clips in search [RDMP-9209] ([#35185](https://github.com/time-loop/clickup/issues/35185)) ([328a63a](https://github.com/time-loop/clickup/commit/328a63a4657ac69870eeeeeefeb7643aa857547b))
* **ai:** endpoint to delete workspace data [CLK-467897] ([#34726](https://github.com/time-loop/clickup/issues/34726)) ([fbcc584](https://github.com/time-loop/clickup/commit/fbcc584d7f8bb9f6ed91b61b9890bf21213f8a82))
* **ai:** endpoint to load multiple transcriptions [RDMP-9891] ([#35124](https://github.com/time-loop/clickup/issues/35124)) ([24662a5](https://github.com/time-loop/clickup/commit/24662a5e8405418ba4eac51e741a260409c0d3b9))
* **ai:** refactor consumer setup using kafka util module [RDMP-9947] ([#35767](https://github.com/time-loop/clickup/issues/35767)) ([eaab78c](https://github.com/time-loop/clickup/commit/eaab78cf1c715b3a728b21622b2e4fe62bd9f859))

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-04-09)


### Features

* **ai:** add transcriptions to clips in search [RDMP-9209] ([#35185](https://github.com/time-loop/clickup/issues/35185)) ([328a63a](https://github.com/time-loop/clickup/commit/328a63a4657ac69870eeeeeefeb7643aa857547b))
* **ai:** endpoint to delete workspace data [CLK-467897] ([#34726](https://github.com/time-loop/clickup/issues/34726)) ([fbcc584](https://github.com/time-loop/clickup/commit/fbcc584d7f8bb9f6ed91b61b9890bf21213f8a82))
* **ai:** endpoint to load multiple transcriptions [RDMP-9891] ([#35124](https://github.com/time-loop/clickup/issues/35124)) ([24662a5](https://github.com/time-loop/clickup/commit/24662a5e8405418ba4eac51e741a260409c0d3b9))
* **ai:** refactor consumer setup using kafka util module [RDMP-9947] ([#35767](https://github.com/time-loop/clickup/issues/35767)) ([eaab78c](https://github.com/time-loop/clickup/commit/eaab78cf1c715b3a728b21622b2e4fe62bd9f859))

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-03-29)


### Features

* **ai:** add transcriptions to clips in search [RDMP-9209] ([#35185](https://github.com/time-loop/clickup/issues/35185)) ([328a63a](https://github.com/time-loop/clickup/commit/328a63a4657ac69870eeeeeefeb7643aa857547b))
* **ai:** endpoint to delete workspace data [CLK-467897] ([#34726](https://github.com/time-loop/clickup/issues/34726)) ([fbcc584](https://github.com/time-loop/clickup/commit/fbcc584d7f8bb9f6ed91b61b9890bf21213f8a82))
* **ai:** endpoint to load multiple transcriptions [RDMP-9891] ([#35124](https://github.com/time-loop/clickup/issues/35124)) ([24662a5](https://github.com/time-loop/clickup/commit/24662a5e8405418ba4eac51e741a260409c0d3b9))
* **ai:** refactor consumer setup using kafka util module [RDMP-9947] ([#35767](https://github.com/time-loop/clickup/issues/35767)) ([eaab78c](https://github.com/time-loop/clickup/commit/eaab78cf1c715b3a728b21622b2e4fe62bd9f859))

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-03-29)


### Features

* **ai:** add transcriptions to clips in search [RDMP-9209] ([#35185](https://github.com/time-loop/clickup/issues/35185)) ([328a63a](https://github.com/time-loop/clickup/commit/328a63a4657ac69870eeeeeefeb7643aa857547b))
* **ai:** endpoint to delete workspace data [CLK-467897] ([#34726](https://github.com/time-loop/clickup/issues/34726)) ([fbcc584](https://github.com/time-loop/clickup/commit/fbcc584d7f8bb9f6ed91b61b9890bf21213f8a82))
* **ai:** endpoint to load multiple transcriptions [RDMP-9891] ([#35124](https://github.com/time-loop/clickup/issues/35124)) ([24662a5](https://github.com/time-loop/clickup/commit/24662a5e8405418ba4eac51e741a260409c0d3b9))
* **ai:** refactor consumer setup using kafka util module [RDMP-9947] ([#35767](https://github.com/time-loop/clickup/issues/35767)) ([eaab78c](https://github.com/time-loop/clickup/commit/eaab78cf1c715b3a728b21622b2e4fe62bd9f859))

## [0.0.10](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.10) (2024-03-28)

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-03-28)


### Features

* **ai:** add transcriptions to clips in search [RDMP-9209] ([#35185](https://github.com/time-loop/clickup/issues/35185)) ([328a63a](https://github.com/time-loop/clickup/commit/328a63a4657ac69870eeeeeefeb7643aa857547b))
* **ai:** endpoint to delete workspace data [CLK-467897] ([#34726](https://github.com/time-loop/clickup/issues/34726)) ([fbcc584](https://github.com/time-loop/clickup/commit/fbcc584d7f8bb9f6ed91b61b9890bf21213f8a82))
* **ai:** endpoint to load multiple transcriptions [RDMP-9891] ([#35124](https://github.com/time-loop/clickup/issues/35124)) ([24662a5](https://github.com/time-loop/clickup/commit/24662a5e8405418ba4eac51e741a260409c0d3b9))
* **ai:** refactor consumer setup using kafka util module [RDMP-9947] ([#35767](https://github.com/time-loop/clickup/issues/35767)) ([eaab78c](https://github.com/time-loop/clickup/commit/eaab78cf1c715b3a728b21622b2e4fe62bd9f859))

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-03-28)


### Features

* **ai:** add transcriptions to clips in search [RDMP-9209] ([#35185](https://github.com/time-loop/clickup/issues/35185)) ([328a63a](https://github.com/time-loop/clickup/commit/328a63a4657ac69870eeeeeefeb7643aa857547b))
* **ai:** endpoint to delete workspace data [CLK-467897] ([#34726](https://github.com/time-loop/clickup/issues/34726)) ([fbcc584](https://github.com/time-loop/clickup/commit/fbcc584d7f8bb9f6ed91b61b9890bf21213f8a82))
* **ai:** endpoint to load multiple transcriptions [RDMP-9891] ([#35124](https://github.com/time-loop/clickup/issues/35124)) ([24662a5](https://github.com/time-loop/clickup/commit/24662a5e8405418ba4eac51e741a260409c0d3b9))
* **ai:** refactor consumer setup using kafka util module [RDMP-9947] ([#35767](https://github.com/time-loop/clickup/issues/35767)) ([eaab78c](https://github.com/time-loop/clickup/commit/eaab78cf1c715b3a728b21622b2e4fe62bd9f859))
