# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [0.54.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.54.0) (2025-06-03)

## [0.53.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.53.0) (2025-06-02)

## [0.52.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.52.0) (2025-05-29)

## [0.51.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.51.0) (2025-05-22)

## [0.50.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.50.0) (2025-05-01)

## [0.49.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.49.0) (2025-04-29)

## [0.48.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.48.0) (2025-04-24)

## [0.47.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.47.0) (2025-04-09)


### Features

* **core-services:** create attachments on entity api [CLK-615220] ([#55720](https://github.com/time-loop/clickup/issues/55720)) ([dee7f1b](https://github.com/time-loop/clickup/commit/dee7f1b52aee318014398156bb89db903b3b29b4))

## [0.46.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.46.0) (2025-04-07)


### Features

* **imports:** Add secure attachments authz endpoints for workspace exports [CLK-622621] ([#52570](https://github.com/time-loop/clickup/issues/52570)) ([7431791](https://github.com/time-loop/clickup/commit/7431791984a0be92c2c82ab75b160d4ebd2d5cc8))

## [0.45.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.45.0) (2025-04-03)

## [0.44.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.44.0) (2025-03-27)

## [0.43.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.43.0) (2025-03-25)

## [0.42.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.42.0) (2025-03-20)

## [0.41.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.41.0) (2025-03-19)

## [0.40.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.40.0) (2025-03-18)

## [0.39.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.39.0) (2025-03-14)

## [0.38.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.38.0) (2025-03-06)

## [0.37.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.37.0) (2025-02-19)


### Features

* **docs:** allow workspace id to be passed in body [CLK-620950] ([#52428](https://github.com/time-loop/clickup/issues/52428)) ([a4eb8f6](https://github.com/time-loop/clickup/commit/a4eb8f6d637a8f20614bdede2097f5438d484b9a))

## [0.36.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.36.0) (2025-02-19)

## [0.35.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.35.0) (2025-02-13)

## [0.34.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.34.0) (2025-02-05)


### Features

* **docs:** Use temporal to experiment with async bulk attachments duplication [CLK-569536] ([#49702](https://github.com/time-loop/clickup/issues/49702)) ([c8243aa](https://github.com/time-loop/clickup/commit/c8243aab4d18a839c69e711d75d7edc3ebaa290c))

## [0.33.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.33.0) (2025-02-03)

## [0.32.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.32.0) (2025-01-30)

## [0.31.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.31.0) (2025-01-27)


### Features

* **core-services:** Public API GET Attachments by Parent Entity [CORE-461] ([#50682](https://github.com/time-loop/clickup/issues/50682)) ([b88b621](https://github.com/time-loop/clickup/commit/b88b6212fa6fda7f83335667ebb7ce60259b76de))

## [0.30.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.30.0) (2025-01-22)

## [0.29.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.29.0) (2025-01-06)

## [0.28.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.28.0) (2024-12-26)

## [0.27.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.27.0) (2024-12-20)

## [0.26.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.26.0) (2024-12-10)

## [0.25.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.25.0) (2024-12-06)

## [0.24.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.24.0) (2024-12-03)

## [0.23.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.23.0) (2024-12-03)

## [0.22.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.22.0) (2024-11-28)

## [0.21.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.21.0) (2024-11-22)

## [0.20.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.20.0) (2024-11-22)


### Features

* **docs:** Add basic search API with parent search and pagination support [CLK-552478] ([#47411](https://github.com/time-loop/clickup/issues/47411)) ([fd16007](https://github.com/time-loop/clickup/commit/fd16007fb1e9d8dcc557898ec9bc88a46955bb64))

## [0.19.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.19.0) (2024-11-20)


### Features

* **docs:** Map mimetype properly so it can be used in new attachments processor [CLK-562867] ([#47530](https://github.com/time-loop/clickup/issues/47530)) ([b763bf9](https://github.com/time-loop/clickup/commit/b763bf90adf42e9a98571a3ce5e328409b7fa4f2))

## [0.18.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.18.0) (2024-11-14)

## [0.17.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.17.0) (2024-11-14)

## [0.16.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.16.0) (2024-11-12)

## [0.15.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.15.0) (2024-10-25)


### Features

* **cards:** Added secure attachments authz endpoints for page exports [CLK-526433] ([#46121](https://github.com/time-loop/clickup/issues/46121)) ([a5913b6](https://github.com/time-loop/clickup/commit/a5913b6c0be105b4e53ee44dab5aab485737ae05))

## [0.14.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.14.0) (2024-10-20)


### Features

* **docs:** Support making attachment permanent [CLK-556345] ([#45761](https://github.com/time-loop/clickup/issues/45761)) ([e36f725](https://github.com/time-loop/clickup/commit/e36f725f911a7bdc390057edf6b2d7cbe16d627c))

## [0.13.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.13.0) (2024-10-17)


### Features

* **docs:** Support remaining workspace attachment types [CLK-551296] ([#45522](https://github.com/time-loop/clickup/issues/45522)) ([adb00fe](https://github.com/time-loop/clickup/commit/adb00fed538f58f132d7c97b8b41f830b6a9c9c9))

## [0.12.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.12.0) (2024-10-16)


### Features

* **docs:** attachment PUT api [CLK-544600] ([#45393](https://github.com/time-loop/clickup/issues/45393)) ([d478928](https://github.com/time-loop/clickup/commit/d4789281f0894bd35aca53cd2796128d6efc691c))

## [0.11.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.11.0) (2024-10-16)

## [0.10.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.10.0) (2024-10-10)

## [0.9.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.9.0) (2024-10-08)


### Features

* **docs:** initial implementation of delete attachment api [CLK-544601] ([#44971](https://github.com/time-loop/clickup/issues/44971)) ([24dc382](https://github.com/time-loop/clickup/commit/24dc382f758d447b8fe76b882d8ef5c713ea5f3e))

## [0.8.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.8.0) (2024-10-08)

## [0.7.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.7.0) (2024-10-04)


### Features

* **docs:** Duplicate Attachment API [CLK-528089] ([#44516](https://github.com/time-loop/clickup/issues/44516)) ([7b31fda](https://github.com/time-loop/clickup/commit/7b31fda9ae12a8a298ccd2b76b42a42478537f2c))

## [0.6.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.6.0) (2024-10-03)

## [0.5.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.5.0) (2024-10-02)

## [0.4.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.4.0) (2024-10-02)

## [0.3.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.0) (2024-10-01)


### Bug Fixes

* **docs:** Add missing package.json to publish client to right location [CLK-549775] ([#44684](https://github.com/time-loop/clickup/issues/44684)) ([33c8100](https://github.com/time-loop/clickup/commit/33c8100a57372a0c64496574de39d6c5ce9055b8))

## [0.2.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.2.0) (2024-10-01)


### Bug Fixes

* **docs:** Add build target, put tags and namedInputs in right location [CLK-549775] ([#44664](https://github.com/time-loop/clickup/issues/44664)) ([baef5bd](https://github.com/time-loop/clickup/commit/baef5bdf82e46787891b74a6dac762fc1015afeb))

## 0.1.0 (2024-10-01)


### Features

* **docs:** Generate project files for attachments service client lib [CLK-549775] ([#44660](https://github.com/time-loop/clickup/issues/44660)) ([6fb4aad](https://github.com/time-loop/clickup/commit/6fb4aad1ce7de4436eedb65aa822fd18dd45dabe))
* **docs:** Support generating attachments service axios client [CLK-545921] ([#43797](https://github.com/time-loop/clickup/issues/43797)) ([d1c853f](https://github.com/time-loop/clickup/commit/d1c853f612ce42df4d71ae72e2874fb660766f4e))
