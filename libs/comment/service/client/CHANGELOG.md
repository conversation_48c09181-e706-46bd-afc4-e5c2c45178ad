# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [0.80.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.80.0) (2025-06-03)

## [0.79.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.79.0) (2025-06-02)

## [0.78.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.78.0) (2025-05-29)

## [0.77.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.77.0) (2025-05-09)


### Bug Fixes

* **chat:** Remove cache implementation [CHAT-11002] ([#58735](https://github.com/time-loop/clickup/issues/58735)) ([9e3dbda](https://github.com/time-loop/clickup/commit/9e3dbda8c4e174feae130edac84f433fbe679f87))

## [0.76.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.76.0) (2025-05-09)

## [0.75.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.75.0) (2025-05-01)


### Bug Fixes

* **chat:** Eliminate common errors causing on-call paging [CHAT-10871] ([#57961](https://github.com/time-loop/clickup/issues/57961)) ([f7849cb](https://github.com/time-loop/clickup/commit/f7849cbe2504215ab731b4fb6686a2ce7c745929))

## [0.74.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.74.0) (2025-04-29)


### Features

* **access-management:** add custom permission level to prefetched data in authz [CU-8xdfmcvvx] ([#57250](https://github.com/time-loop/clickup/issues/57250)) ([1c622df](https://github.com/time-loop/clickup/commit/1c622df58c53980d2597be3bf43e7bb010e2ed04))

## [0.73.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.73.0) (2025-04-24)

## [0.72.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.72.0) (2025-04-23)


### Features

* **chat:** add clra delta logic [CHAT-9315] ([#57001](https://github.com/time-loop/clickup/issues/57001)) ([14376c2](https://github.com/time-loop/clickup/commit/14376c266ae123349c99cf0e6c7beecfe0112bc3))


### Bug Fixes

* **chat:** Cancel the workflow if the user click send now [CHAT-10699] ([#57168](https://github.com/time-loop/clickup/issues/57168)) ([cd575e8](https://github.com/time-loop/clickup/commit/cd575e8b7c94b4e3c7ede0c87322450e784bc6fd))

## [0.71.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.71.0) (2025-04-22)


### Features

* **chat:** Comment search now works across databases [INNOVATION-4483] ([#57261](https://github.com/time-loop/clickup/issues/57261)) ([4dcf355](https://github.com/time-loop/clickup/commit/4dcf3556e13a29866ff86ca33c9b1a5b3006378a))

## [0.70.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.70.0) (2025-04-17)


### Features

* **chat:** add last read at api to comments gateway client [CHAT-10451] ([#56885](https://github.com/time-loop/clickup/issues/56885)) ([a229f75](https://github.com/time-loop/clickup/commit/a229f750b6b71b8cf0672062037ac05a16e67b10))

## [0.69.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.69.0) (2025-04-03)

## [0.68.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.68.0) (2025-03-27)

## [0.67.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.67.0) (2025-03-25)

## [0.66.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.66.0) (2025-03-24)


### Features

* **chat:** Add source comment parent id [CHAT-9432] ([#54908](https://github.com/time-loop/clickup/issues/54908)) ([87fe09d](https://github.com/time-loop/clickup/commit/87fe09d82e74e0304db04b99d2ab7637227cfeef))

## [0.65.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.65.0) (2025-03-20)


### Features

* **chat:** Add stub comment id on response [CHAT-9418] ([#54846](https://github.com/time-loop/clickup/issues/54846)) ([72d6eed](https://github.com/time-loop/clickup/commit/72d6eedcf9fc9963a2277f0684771db7ad50b0ec))

## [0.64.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.64.0) (2025-03-20)

## [0.63.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.63.0) (2025-03-20)


### Features

* **chat:** Move the send_reply_to_channel config to top level [CHAT-442] ([#54755](https://github.com/time-loop/clickup/issues/54755)) ([483cbb2](https://github.com/time-loop/clickup/commit/483cbb251f938653ef9a025b3dcfef854d04490a))

## [0.62.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.62.0) (2025-03-19)

## [0.61.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.61.0) (2025-03-19)


### Features

* **chat:** Add stub comment CRUD for send-to-channel reply [CHAT-381] ([#52047](https://github.com/time-loop/clickup/issues/52047)) ([6466608](https://github.com/time-loop/clickup/commit/6466608537534113d5cf596d535ea71608559135))

## [0.60.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.60.0) (2025-03-18)

## [0.59.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.59.0) (2025-03-14)


### Features

* **chat:** Send WS after schedule temporal workflow success [CHAT-9097] ([#54120](https://github.com/time-loop/clickup/issues/54120)) ([6172871](https://github.com/time-loop/clickup/commit/6172871381056ca8365c7770b3ae07423260bd5d))

## [0.58.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.58.0) (2025-03-06)


### Bug Fixes

* **core-services:** Fix Chat API OpenAPI spec [CORE-1124] ([#53722](https://github.com/time-loop/clickup/issues/53722)) ([1937fe6](https://github.com/time-loop/clickup/commit/1937fe6a4b0c79ed96aeae6cefabd2ed8ac7fe8f))

## [0.57.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.57.0) (2025-03-06)


### Features

* **chat:** Add thread_unfollower_ids to get threads call [CHAT-6144] ([#53774](https://github.com/time-loop/clickup/issues/53774)) ([2537ad8](https://github.com/time-loop/clickup/commit/2537ad8299a749c21961932242144b7dc13798e0))

## [0.56.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.56.0) (2025-03-06)

## [0.55.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.55.0) (2025-03-06)


### Features

* **chat:** Support user provided dates for comments [CHAT-8866] ([#53525](https://github.com/time-loop/clickup/issues/53525)) ([eff2110](https://github.com/time-loop/clickup/commit/eff2110c99a8c0b13abcd1a2495f7a48fc91f066))

## [0.54.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.54.0) (2025-03-05)


### Features

* **chat:** Expose threadGroupFollowerMembers in comments API [CHAT-6144] ([#53323](https://github.com/time-loop/clickup/issues/53323)) ([5eeed19](https://github.com/time-loop/clickup/commit/5eeed197cee72f38cf98dcc414ff4e4306a73eb7))
* **chat:** Use onBehalfUserId for update schedule comment api [CHAT-7274] ([#53135](https://github.com/time-loop/clickup/issues/53135)) ([e78e5c0](https://github.com/time-loop/clickup/commit/e78e5c01e4899969c3f03618f0a368d508fb6451))

## [0.53.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.53.0) (2025-02-26)


### Bug Fixes

* **chat:** Enable CommentsScheduledAccessGuard [CHAT-8244] ([#53084](https://github.com/time-loop/clickup/issues/53084)) ([9e69039](https://github.com/time-loop/clickup/commit/9e69039bb51c0d63e3dc9cec84640747be321350))

## [0.52.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.52.0) (2025-02-19)

## [0.51.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.51.0) (2025-02-13)

## [0.50.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.50.0) (2025-02-11)


### Bug Fixes

* **chat:** Clean cache for update scheduled comment from clickbot [CHAT-7106] ([#52052](https://github.com/time-loop/clickup/issues/52052)) ([3f0a550](https://github.com/time-loop/clickup/commit/3f0a550f7eb3bc5e5094258f634c536fae199097))

## [0.49.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.49.0) (2025-02-07)


### Bug Fixes

* **core-services:** Rename chats into channels [CORE-774] ([#51578](https://github.com/time-loop/clickup/issues/51578)) ([4659c95](https://github.com/time-loop/clickup/commit/4659c95d37bc78d46684389a963a5e23771e38d3))

## [0.48.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.48.0) (2025-02-05)

## [0.47.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.47.0) (2025-02-03)

## [0.46.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.46.0) (2025-01-30)

## [0.45.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.45.0) (2025-01-24)


### Features

* **core-services:** delete api v3 chat message reactions [CORE-655] ([#50719](https://github.com/time-loop/clickup/issues/50719)) ([c72651a](https://github.com/time-loop/clickup/commit/c72651aeccdce9965efc5f70d2ceb2a75a533cf6)), closes [#50387](https://github.com/time-loop/clickup/issues/50387) [#50586](https://github.com/time-loop/clickup/issues/50586) [#50550](https://github.com/time-loop/clickup/issues/50550) [#50524](https://github.com/time-loop/clickup/issues/50524) [#50538](https://github.com/time-loop/clickup/issues/50538) [#50347](https://github.com/time-loop/clickup/issues/50347) [#50598](https://github.com/time-loop/clickup/issues/50598) [#50431](https://github.com/time-loop/clickup/issues/50431) [#50423](https://github.com/time-loop/clickup/issues/50423) [#50604](https://github.com/time-loop/clickup/issues/50604) [#50603](https://github.com/time-loop/clickup/issues/50603) [#49829](https://github.com/time-loop/clickup/issues/49829) [#50602](https://github.com/time-loop/clickup/issues/50602) [#50612](https://github.com/time-loop/clickup/issues/50612)
* **core-services:** update tagged_users route to return simple user objects [CORE-693] ([#50903](https://github.com/time-loop/clickup/issues/50903)) ([5c8d841](https://github.com/time-loop/clickup/commit/5c8d841d00b9e0bcf1ab53d09dd248696c7a6f2c))

## [0.44.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.44.0) (2025-01-22)

## [0.43.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.43.0) (2025-01-22)


### Features

* **core-services:** POST api v3 chat message reactions [CORE-654] ([#50687](https://github.com/time-loop/clickup/issues/50687)) ([36e9d08](https://github.com/time-loop/clickup/commit/36e9d0863db7ca825e358685f63179ade0cb5fae)), closes [#50387](https://github.com/time-loop/clickup/issues/50387) [#50586](https://github.com/time-loop/clickup/issues/50586) [#50550](https://github.com/time-loop/clickup/issues/50550) [#50524](https://github.com/time-loop/clickup/issues/50524) [#50538](https://github.com/time-loop/clickup/issues/50538) [#50347](https://github.com/time-loop/clickup/issues/50347) [#50598](https://github.com/time-loop/clickup/issues/50598) [#50431](https://github.com/time-loop/clickup/issues/50431) [#50423](https://github.com/time-loop/clickup/issues/50423) [#50604](https://github.com/time-loop/clickup/issues/50604) [#50603](https://github.com/time-loop/clickup/issues/50603) [#49829](https://github.com/time-loop/clickup/issues/49829) [#50602](https://github.com/time-loop/clickup/issues/50602) [#50612](https://github.com/time-loop/clickup/issues/50612)
* **user-platform:** Add Logic of exposing User Profile Status in same way as monolith does [CLK-606984] ([#50645](https://github.com/time-loop/clickup/issues/50645)) ([01d034f](https://github.com/time-loop/clickup/commit/01d034f68da4c9820b67d4238ef4be7066e42113))

## [0.42.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.42.0) (2025-01-16)


### Features

* **user-platform:** Add User profile status details for experience api [CLK-602367] ([#50134](https://github.com/time-loop/clickup/issues/50134)) ([778c839](https://github.com/time-loop/clickup/commit/778c8396a2cf01a438630cd29cc59f4e633bde8c))


### Bug Fixes

* **chat:** async authz rooms when fetching chat aggregates [CHAT-6607] ([#50374](https://github.com/time-loop/clickup/issues/50374)) ([6d20e47](https://github.com/time-loop/clickup/commit/6d20e47b421e15a71e6a5f6c9cf63ddea9d15440))

## [0.41.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.41.0) (2025-01-14)


### Features

* **core-services:** PATCH Chat Message Public API v3 Endpoint [CORE-146] ([#50150](https://github.com/time-loop/clickup/issues/50150)) ([b7c78d2](https://github.com/time-loop/clickup/commit/b7c78d28605c9be392f7eddfdd3f0b1f896b5c1e))

## [0.40.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.40.0) (2025-01-13)


### Features

* **chat:** Return entity id for search endpoint [CHAT-6519] ([#50074](https://github.com/time-loop/clickup/issues/50074)) ([62f3880](https://github.com/time-loop/clickup/commit/62f3880308b38bce7168591f3ea21007506d0de2))

## [0.39.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.39.0) (2025-01-09)


### Features

* **core-services:** add public api exception handling [CORE-549] ([#49108](https://github.com/time-loop/clickup/issues/49108)) ([7156598](https://github.com/time-loop/clickup/commit/71565986b6c7b637b27c42393f433bbfbe2bad3b))

## [0.38.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.38.0) (2025-01-07)


### Features

* **chat:** Add comment scheduled response type [CHAT-6397] ([#49678](https://github.com/time-loop/clickup/issues/49678)) ([873b929](https://github.com/time-loop/clickup/commit/873b929d619d59d18c70346e1e91f45d81b5a3f5))

## [0.37.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.37.0) (2025-01-06)

## [0.36.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.36.0) (2025-01-02)

## [0.35.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.35.0) (2024-12-27)


### Features

* **core-services:** post chat messages using impersonation [CORE-606] ([#49485](https://github.com/time-loop/clickup/issues/49485)) ([f722264](https://github.com/time-loop/clickup/commit/f72226403ca490464e8f28ca03fc76c8908d9084))

## [0.34.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.34.0) (2024-12-26)


### Features

* **chat:** Add syncup message type exclusion [CHAT-5371] ([#49464](https://github.com/time-loop/clickup/issues/49464)) ([7fb74bc](https://github.com/time-loop/clickup/commit/7fb74bc02a9a04c22c0186cf0268cb08f8935b7c))

## [0.33.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.33.0) (2024-12-20)


### Features

* **core-services:** Chat Message DELETE route implemented [CORE-147] ([#49275](https://github.com/time-loop/clickup/issues/49275)) ([4979b93](https://github.com/time-loop/clickup/commit/4979b93616dcf8526326a695a6df837fd035994e))

## [0.32.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.32.0) (2024-12-20)

## [0.31.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.31.0) (2024-12-19)


### Features

* **core-services:** post api v3 chat messages [CORE-145] ([#49025](https://github.com/time-loop/clickup/issues/49025)) ([16a6704](https://github.com/time-loop/clickup/commit/16a6704f7e6c54f2a2387f5b1941e0462ba92afe))

## [0.30.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.30.0) (2024-12-16)


### Features

* **chat:** Allow clickbot to call update schedule endpoint [CHAT-5990] ([#49051](https://github.com/time-loop/clickup/issues/49051)) ([feda092](https://github.com/time-loop/clickup/commit/feda092efba98e7d914729c57177b087ddbbf822))

## [0.29.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.29.0) (2024-12-12)


### Features

* **chat:** Add desc/asc for search endpoint and search endpoint unit tests[CHAT-5994] ([#48711](https://github.com/time-loop/clickup/issues/48711)) ([f900288](https://github.com/time-loop/clickup/commit/f90028893cd2b358db549f3ee7b1f8c5593bc002))

## [0.28.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.28.0) (2024-12-10)

## [0.27.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.27.0) (2024-12-10)


### Features

* **core-services:** Final PR for lib/public-api/domain with dtos + builders [CORE-544] ([#48682](https://github.com/time-loop/clickup/issues/48682)) ([05814b0](https://github.com/time-loop/clickup/commit/05814b08cc2a6dd06d8b195e446f502360c3444f))

## [0.26.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.26.0) (2024-12-09)


### Bug Fixes

* **core-services:** update gateway service config to serve correct public api chat routes [CORE-82] ([#48626](https://github.com/time-loop/clickup/issues/48626)) ([5204ca0](https://github.com/time-loop/clickup/commit/5204ca03f5247116c2397ccea8ab09d133071ccd))

## [0.25.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.25.0) (2024-12-09)


### Features

* **ai:** Store AI result ID and conversation ID on AI comments [CLK-565547] ([#48451](https://github.com/time-loop/clickup/issues/48451)) ([df489b6](https://github.com/time-loop/clickup/commit/df489b60919f17dc4f2555cf9670a23f2ea11c17))

## [0.24.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.24.0) (2024-12-06)


### Features

* **core-services:** GET route for Public API v3 Chat Message Reactions [CORE-513] ([#48486](https://github.com/time-loop/clickup/issues/48486)) ([04dd5c9](https://github.com/time-loop/clickup/commit/04dd5c97b432b2565ceb48e06e48bdff69e19fcf))

## [0.23.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.23.0) (2024-12-06)

## [0.22.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.22.0) (2024-12-06)


### Features

* **core-services:** GET route for Public API v3 Chat Message Tagged Users [CORE-514] ([#48462](https://github.com/time-loop/clickup/issues/48462)) ([2dcf0a1](https://github.com/time-loop/clickup/commit/2dcf0a127a7655aa465c215b053fbdf909bbdb05))

## [0.21.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.21.0) (2024-12-06)


### Bug Fixes

* **automations:** add timezone when creating task [CLK-551820] ([#48455](https://github.com/time-loop/clickup/issues/48455)) ([a8eba82](https://github.com/time-loop/clickup/commit/a8eba826b9f4712aedee8f8001c6531363127aa0))

## [0.20.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.20.0) (2024-12-05)


### Features

* **core-services:** GET route for Public API v3 Chat Message Replies [CORE-512] ([#48256](https://github.com/time-loop/clickup/issues/48256)) ([93d573f](https://github.com/time-loop/clickup/commit/93d573f7b391acaec511504c01b016cd4ac4d18a))

## [0.19.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.19.0) (2024-12-03)

## [0.18.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.18.0) (2024-12-03)

## [0.17.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.17.0) (2024-12-02)


### Features

* **core-services:** Public API v3 route for getting chat messages (paginated) [CORE-327] ([#47676](https://github.com/time-loop/clickup/issues/47676)) ([fe97958](https://github.com/time-loop/clickup/commit/fe979586f55c92102cd520c9a4a6537d8b939e89))

## [0.16.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.16.0) (2024-11-28)

## [0.15.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.15.0) (2024-11-27)


### Features

* **chat:** Add update endpoint for schedule sent [CHAT-5729] ([#47949](https://github.com/time-loop/clickup/issues/47949)) ([8f3953c](https://github.com/time-loop/clickup/commit/8f3953c16bacd79fcae6634538fee699063c8f62))

## [0.14.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.14.0) (2024-11-25)


### Features

* **chat:** Read api for schedule sent [CHAT-4895] ([#47629](https://github.com/time-loop/clickup/issues/47629)) ([a872988](https://github.com/time-loop/clickup/commit/a8729889b7f0729cc2d792f187aebea9e204344f))

## [0.13.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.13.0) (2024-11-22)


### Features

* **chat:** Enable message history paywall on comments service [CHAT-5583] ([#47441](https://github.com/time-loop/clickup/issues/47441)) ([c891270](https://github.com/time-loop/clickup/commit/c891270d53870693aefbc953da358eb1d5658404))

## [0.12.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.12.0) (2024-11-22)

## [0.11.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.11.0) (2024-11-21)


### Features

* **chat:** Store schedule comment in table [CHAT-5538] ([#47451](https://github.com/time-loop/clickup/issues/47451)) ([4e5e847](https://github.com/time-loop/clickup/commit/4e5e847f075eba39a90f7b591a830654bd38d8e6))

## [0.10.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.10.0) (2024-11-14)

## [0.9.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.9.0) (2024-11-14)

## [0.8.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.8.0) (2024-11-12)

## [0.7.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.7.0) (2024-11-08)


### Features

* **chat:** Skeleton for scheduled comments CRUD [CHAT-4835] ([#46442](https://github.com/time-loop/clickup/issues/46442)) ([d715b98](https://github.com/time-loop/clickup/commit/d715b988df2281f1e9bfe92c0470f21904693f43))
* **slapdash:** Return comment_type for ai comments [CLK-555516] ([6811198](https://github.com/time-loop/clickup/commit/6811198f1ea8ee79e05ef2652cd2c65506dd0b29))

## [0.6.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.6.0) (2024-11-05)


### Features

* **chat:** Adds a new optional field workspace_users_map to comment API [CHAT-5204] ([#46572](https://github.com/time-loop/clickup/issues/46572)) ([698a37a](https://github.com/time-loop/clickup/commit/698a37a57931b5bbdfc55cbb34dff478f0daf9af))

## [0.5.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.5.0) (2024-10-17)


### Features

* **cce:** V2 Threads UI updates [CLK-551803] ([#45536](https://github.com/time-loop/clickup/issues/45536)) ([10b7f21](https://github.com/time-loop/clickup/commit/10b7f2138475756555fac770dd5bb23cc612b019))

## [0.4.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.4.0) (2024-10-16)

## [0.3.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.0) (2024-10-10)

## [0.2.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.2.0) (2024-10-10)


### Features

* **core-services:** Bring operationId into gateway specs [CORE-273] ([#45218](https://github.com/time-loop/clickup/issues/45218)) ([4d8b7a6](https://github.com/time-loop/clickup/commit/4d8b7a62243ccb7cee0f5f760428767411658c3d))

## 0.1.0 (2024-10-09)


### Features

* **automations:** add client for comment service [CLK-546956] ([#44017](https://github.com/time-loop/clickup/issues/44017)) ([d9f9c00](https://github.com/time-loop/clickup/commit/d9f9c00cc6b84738a8fbd567778b0f672a5b97b6))
* **chat:** adding attachment_parent to /search and /bulk comment v3 responses [CLK-538130] ([#44793](https://github.com/time-loop/clickup/issues/44793)) ([e9e8370](https://github.com/time-loop/clickup/commit/e9e83706dd1c7682e215d1e4b4a8f5a80fdc738a))
