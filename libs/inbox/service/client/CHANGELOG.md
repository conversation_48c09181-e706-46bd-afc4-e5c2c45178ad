# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [0.3.193](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.193) (2025-06-03)

## [0.3.192](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.192) (2025-06-02)


### Features

* **work-analytics:** Personal priority notifications [CLK-540146] ([#60526](https://github.com/time-loop/clickup/issues/60526)) ([4af6bc1](https://github.com/time-loop/clickup/commit/4af6bc1e29508c3666862a821f456b4a70cc0c9d))

## [0.3.191](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.191) (2025-05-29)

## [0.3.190](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.190) (2025-05-13)


### Bug Fixes

* **chat:** fix notifications from discussion card [CLK-555321] ([#58072](https://github.com/time-loop/clickup/issues/58072)) ([4dda3ca](https://github.com/time-loop/clickup/commit/4dda3ca8a7e4da6b43e74ccfee5aa51d56c1ac0a))

## [0.3.189](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.189) (2025-05-06)


### Features

* **core-services:** Update Tasks Gateway Clients [CORE-1658] ([#58102](https://github.com/time-loop/clickup/issues/58102)) ([543da0d](https://github.com/time-loop/clickup/commit/543da0d3456904839be3f50c25a9bbbc1715094b))

## [0.3.188](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.188) (2025-05-01)

## [0.3.187](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.187) (2025-04-29)

## [0.3.186](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.186) (2025-04-25)


### Features

* **chat:** Create notification for 'duration' history item in inbox [CHAT-10760] ([#57500](https://github.com/time-loop/clickup/issues/57500)) ([2948a43](https://github.com/time-loop/clickup/commit/2948a430aa4c71af872c031c10326bc385d28345))

## [0.3.185](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.185) (2025-04-24)

## [0.3.184](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.184) (2025-04-16)


### Features

* **chat:** add root and parent handling for reminders in inbox [CHAT-10606] ([#56751](https://github.com/time-loop/clickup/issues/56751)) ([beae4e8](https://github.com/time-loop/clickup/commit/beae4e860adc1c9bfb5567afd96364efe0494ae9))

## [0.3.183](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.183) (2025-04-04)


### Features

* **chat:** sync reminder deletes [CHAT-10410] ([#55872](https://github.com/time-loop/clickup/issues/55872)) ([792de66](https://github.com/time-loop/clickup/commit/792de66c3c538279398d725e15e87623af046b23))

## [0.3.182](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.182) (2025-04-03)


### Features

* **whiteboards:** Add whiteboard notification [CLK-579684] ([#54972](https://github.com/time-loop/clickup/issues/54972)) ([f4dd988](https://github.com/time-loop/clickup/commit/f4dd98858eefa8a645ac1e250d50bcd0069ef375))

## [0.3.181](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.181) (2025-03-27)


### Bug Fixes

* **chat:** fix entity state while processing reminder notifs [CHAT-9516] ([#55194](https://github.com/time-loop/clickup/issues/55194)) ([02f92f0](https://github.com/time-loop/clickup/commit/02f92f05d27e56c63860134dc90bca52a72eb1e4))

## [0.3.180](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.180) (2025-03-25)

## [0.3.179](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.179) (2025-03-20)

## [0.3.178](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.178) (2025-03-19)

## [0.3.177](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.177) (2025-03-18)

## [0.3.176](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.176) (2025-03-14)


### Features

* **chat:** begin reminders push notification work [RDMP-13867] ([#53098](https://github.com/time-loop/clickup/issues/53098)) ([58afb9f](https://github.com/time-loop/clickup/commit/58afb9f3a6c6e8e465da08011f767cfe73c1b499))

## [0.3.175](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.175) (2025-03-06)

## [0.3.174](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.174) (2025-02-20)


### Features

* **chat:** begin scaffolding new reminders [RDMP-13842] ([#52394](https://github.com/time-loop/clickup/issues/52394)) ([efa1911](https://github.com/time-loop/clickup/commit/efa19112dce4810d0d31f1c9efae4c9837e77fa8))

## [0.3.173](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.173) (2025-02-19)

## [0.3.172](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.172) (2025-02-13)

## [0.3.171](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.171) (2025-02-05)

## [0.3.170](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.170) (2025-02-03)


### Features

* **project-mgmt:** add approval parent type to inbox service [CLK-568134] ([#49559](https://github.com/time-loop/clickup/issues/49559)) ([85e3ace](https://github.com/time-loop/clickup/commit/85e3ace47f157e3dd2c96f50ebdd4067f42f11f8))

## [0.3.169](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.169) (2025-01-30)

## [0.3.168](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.168) (2025-01-22)


### Features

* **hierarchy:** Update list task count OCM: [CLK-598355] ([#50198](https://github.com/time-loop/clickup/issues/50198)) ([f004532](https://github.com/time-loop/clickup/commit/f004532853e3201608752f2c457040a56cfa688c))

## [0.3.167](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.167) (2025-01-22)

## [0.3.166](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.166) (2025-01-06)

## [0.3.165](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.165) (2024-12-26)

## [0.3.164](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.164) (2024-12-20)

## [0.3.163](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.163) (2024-12-10)

## [0.3.162](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.162) (2024-12-06)


### Features

* **access-management:** new history item type for approved upgrade access requests [CLK-579191] ([#48545](https://github.com/time-loop/clickup/issues/48545)) ([c6e8ef6](https://github.com/time-loop/clickup/commit/c6e8ef628192eb87f046c70077efe2a69f0fd96f))

## [0.3.161](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.161) (2024-12-03)


### Features

* **access-management:** inbox bundle for upgrade access requested [CLK-577295] ([#48160](https://github.com/time-loop/clickup/issues/48160)) ([2a4f95e](https://github.com/time-loop/clickup/commit/2a4f95ead6b43a4ca69e1059d7819b4cf40a8194))

## [0.3.160](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.160) (2024-12-03)

## [0.3.159](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.159) (2024-11-28)

## [0.3.158](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.158) (2024-11-22)

## [0.3.157](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.157) (2024-11-14)

## [0.3.156](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.156) (2024-11-14)

## [0.3.155](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.155) (2024-11-12)

## [0.3.154](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.154) (2024-10-24)


### Features

* **chat:** Add generic logging endpoint [CHAT-4118] ([#45983](https://github.com/time-loop/clickup/issues/45983)) ([6aacd55](https://github.com/time-loop/clickup/commit/6aacd552adca9f7442d0c35881fb512b45d3af80))

## [0.3.153](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.153) (2024-10-16)

## [0.3.152](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.152) (2024-10-10)


### Features

* **slapdash:** Add notifications for new zoom history items [CLK-553543] ([#45272](https://github.com/time-loop/clickup/issues/45272)) ([dcf66c4](https://github.com/time-loop/clickup/commit/dcf66c4cfa97fc8955a0b540a3d0ff91a30ee64e))

## [0.3.151](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.151) (2024-10-08)


### Features

* **project-mgmt:** adds approval reminder notifications [CLK-539908] ([#44832](https://github.com/time-loop/clickup/issues/44832)) ([68ba33a](https://github.com/time-loop/clickup/commit/68ba33afad9002650d974d8fa9b89150e7bd93f2))

## [0.3.150](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.150) (2024-10-03)

## [0.3.149](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.149) (2024-10-02)

## [0.3.148](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.148) (2024-10-02)

## [0.3.147](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.147) (2024-09-26)

## [0.3.146](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.146) (2024-09-24)

## [0.3.145](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.145) (2024-09-24)

## [0.3.144](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.144) (2024-09-23)

## [0.3.143](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.143) (2024-09-10)

## [0.3.142](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.142) (2024-09-04)


### Features

* **inbox:** Create push, inbox, and email notifs for chat_thread_link task history item [CHAT-2119] ([#41457](https://github.com/time-loop/clickup/issues/41457)) ([efe53d1](https://github.com/time-loop/clickup/commit/efe53d149967d49ad60f5e04e94ab2a5c77b7405))

## [0.3.142](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.142) (2024-08-08)


### Features

* **inbox:** Create push, inbox, and email notifs for chat_thread_link task history item [CHAT-2119] ([#41457](https://github.com/time-loop/clickup/issues/41457)) ([efe53d1](https://github.com/time-loop/clickup/commit/efe53d149967d49ad60f5e04e94ab2a5c77b7405))

## [0.3.142](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.142) (2024-08-07)

## [0.3.141](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.141) (2024-07-31)

## [0.3.140](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.140) (2024-07-31)


### Bug Fixes

* **inbox:** proper treatment for group mentions, assignments with 'short-circuit' enabled [CLK-526734] ([#41256](https://github.com/time-loop/clickup/issues/41256)) ([27600cf](https://github.com/time-loop/clickup/commit/27600cf7b6b6e838b77e2fca5f9760814568fede))

## [0.3.139](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.139) (2024-07-30)

## [0.3.138](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.138) (2024-07-25)


### Features

* **inbox:** Notification settings - allow short circuit to avoid hitting thread_history, part 2 [CLK-526226] ([#41061](https://github.com/time-loop/clickup/issues/41061)) ([8fd3265](https://github.com/time-loop/clickup/commit/8fd3265b4336bb65cde3e64ec5e9f36374df15f0))

## [0.3.137](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.137) (2024-07-24)

## [0.3.136](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.136) (2024-07-18)


### Features

* **inbox:** add uuid to payload and stats collection api [CLK-522382] ([#40806](https://github.com/time-loop/clickup/issues/40806)) ([3921d36](https://github.com/time-loop/clickup/commit/3921d36b402f29ddfb2342f99a62d93e000d604c))

## [0.3.135](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.135) (2024-07-18)


### Features

* **inbox:** make stats/pushNotifications/received bulk api [CLK-522382] ([#40782](https://github.com/time-loop/clickup/issues/40782)) ([6380b43](https://github.com/time-loop/clickup/commit/6380b43477de0173645aab9854094dd4294f3045))

## [0.3.134](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.134) (2024-07-17)


### Features

* **inbox:** endpoint to collect stats of received push notification [CLK-522382] ([#40711](https://github.com/time-loop/clickup/issues/40711)) ([e9caded](https://github.com/time-loop/clickup/commit/e9caded17aa68ff196d32230a4344aebd04944c1))

## [0.3.133](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.133) (2024-07-13)


### Features

* **inbox:** migrate inbox-service to esbuild [CLK-470314] ([#40600](https://github.com/time-loop/clickup/issues/40600)) ([f06679f](https://github.com/time-loop/clickup/commit/f06679f9f779ff82ec0bdd701da266b46edc5680))

## [0.3.132](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.132) (2024-07-10)

## [0.3.131](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.131) (2024-07-02)

## [0.3.130](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.130) (2024-06-22)

## [0.3.129](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.129) (2024-06-20)


### Bug Fixes

* **core-services:** Fix OpenAPI specs of health checks [CLK-515791] ([#39792](https://github.com/time-loop/clickup/issues/39792)) ([9bac3a8](https://github.com/time-loop/clickup/commit/9bac3a88aa9a7d601a329e93d356ebb48c2b1128))

## [0.3.128](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.128) (2024-06-18)

## [0.3.127](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.127) (2024-06-17)


### Features

* **inbox:** Inbox avatars for guest users [CLK-468437] ([#39145](https://github.com/time-loop/clickup/issues/39145)) ([9be2e00](https://github.com/time-loop/clickup/commit/9be2e00dd97edeaac26c06c650a07da599945b79))

## [0.3.126](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.126) (2024-06-13)


### Features

* **inbox:** Generate reminders skeleton for FE dev work! [cu-8xdfcf6e6] ([#39588](https://github.com/time-loop/clickup/issues/39588)) ([69a85a5](https://github.com/time-loop/clickup/commit/69a85a5b7564bb866ace73409634680917763894))

## [0.3.125](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.125) (2024-06-13)

## [0.3.124](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.124) (2024-06-13)

## [0.3.123](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.123) (2024-06-13)

## [0.3.122](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.122) (2024-06-12)

## [0.3.121](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.121) (2024-05-17)

## [0.3.120](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.120) (2024-05-17)

## [0.3.118](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.118) (2024-05-13)

## [0.3.118](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.118) (2024-05-02)

## [0.3.118](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.118) (2024-04-29)

## [0.3.118](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.118) (2024-04-28)

## [0.3.117](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.117) (2024-04-16)

## [0.3.116](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.116) (2024-04-15)


### Features

* **inbox:** Add endpoint to delete ALL ws data in a workspace [CLK-451143] ([#36984](https://github.com/time-loop/clickup/issues/36984)) ([2cd8a02](https://github.com/time-loop/clickup/commit/2cd8a022a664f05f960bf54dd0ed0c36705961f3))

## [0.3.115](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.115) (2024-04-12)

## [0.3.114](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.114) (2024-04-09)


### Features

* **core-services:** esbuild nx plugin preparation [CLK-470314] ([#36927](https://github.com/time-loop/clickup/issues/36927)) ([b1c7c13](https://github.com/time-loop/clickup/commit/b1c7c13bf448d6315e93ace67b5f5fa489f140c5))

## [0.3.113](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.113) (2024-03-29)

## [0.3.112](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.112) (2024-03-29)

## [0.3.111](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.3.111) (2024-03-28)
