# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [0.0.65](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.65) (2025-06-03)

## [0.0.64](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.64) (2025-06-02)

## [0.0.63](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.63) (2025-05-29)


### Bug Fixes

* **core-services:** fix required query param to not required for getBulkSpaces [CORE-1302] ([#58343](https://github.com/time-loop/clickup/issues/58343)) ([8daa9f2](https://github.com/time-loop/clickup/commit/8daa9f2f5b2e111f34b9f9b9aaccb4697b3da433)), closes [#inc-1276-2025-05-06](https://github.com/time-loop/clickup/issues/inc-1276-2025-05-06)

## [0.0.62](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.62) (2025-05-01)


### Features

* **core-services:** update hierarchy clients [CORE-1302] ([#58060](https://github.com/time-loop/clickup/issues/58060)) ([8a9361c](https://github.com/time-loop/clickup/commit/8a9361c746c627af5ed8ec0301e85aba78f7abc1))

## [0.0.61](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.61) (2025-04-29)

## [0.0.60](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.60) (2025-04-24)

## [0.0.59](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.59) (2025-04-03)

## [0.0.58](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.58) (2025-03-27)

## [0.0.57](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.57) (2025-03-25)

## [0.0.56](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.56) (2025-03-20)

## [0.0.55](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.55) (2025-03-19)

## [0.0.54](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.54) (2025-03-18)

## [0.0.53](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.53) (2025-03-14)

## [0.0.52](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.52) (2025-03-06)

## [0.0.51](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.51) (2025-02-19)

## [0.0.50](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.50) (2025-02-13)

## [0.0.49](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.49) (2025-02-05)

## [0.0.48](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.48) (2025-02-03)

## [0.0.47](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.47) (2025-01-30)

## [0.0.46](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.46) (2025-01-22)

## [0.0.45](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.45) (2025-01-06)

## [0.0.44](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.44) (2024-12-26)

## [0.0.43](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.43) (2024-12-20)

## [0.0.42](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.42) (2024-12-10)

## [0.0.41](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.41) (2024-12-06)


### Features

* **fields:** add /stats endpoint to the gateway-service [CLK-574747] ([#48066](https://github.com/time-loop/clickup/issues/48066)) ([3a264bf](https://github.com/time-loop/clickup/commit/3a264bfa1c234a6a44487b214b94df9c69301b16))

## [0.0.40](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.40) (2024-12-04)


### Bug Fixes

* **hierarchy:** Update parent chain to support subfolders [CLK-523378] ([#47550](https://github.com/time-loop/clickup/issues/47550)) ([f16a740](https://github.com/time-loop/clickup/commit/f16a74026db9bbc90cb03fb5e0d746401d11396b))

## [0.0.39](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.39) (2024-12-03)

## [0.0.38](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.38) (2024-12-03)

## [0.0.37](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.37) (2024-11-28)

## [0.0.36](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.36) (2024-11-22)

## [0.0.35](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.35) (2024-11-14)

## [0.0.34](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.34) (2024-11-14)

## [0.0.33](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.33) (2024-11-12)


### Features

* **core-services:** Add hierarchy-service to the API Gateway [CORE-445] ([#46896](https://github.com/time-loop/clickup/issues/46896)) ([0bd7316](https://github.com/time-loop/clickup/commit/0bd73164f97038697a6db565745d4da8a725d5cc))

## [0.0.32](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.32) (2024-10-16)

## [0.0.31](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.31) (2024-10-10)

## [0.0.30](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.30) (2024-10-08)

## [0.0.29](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.29) (2024-10-03)

## [0.0.28](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.28) (2024-10-02)

## [0.0.27](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.27) (2024-10-02)

## [0.0.26](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.26) (2024-09-26)

## [0.0.25](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.25) (2024-09-24)

## [0.0.24](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.24) (2024-09-24)

## [0.0.23](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.23) (2024-09-23)

## [0.0.22](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.22) (2024-09-10)

## [0.0.21](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.21) (2024-09-04)

## [0.0.21](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.21) (2024-08-08)

## [0.0.21](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.21) (2024-08-07)

## [0.0.20](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.20) (2024-07-31)

## [0.0.19](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.19) (2024-07-30)

## [0.0.18](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.18) (2024-07-24)

## [0.0.17](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.17) (2024-07-10)

## [0.0.16](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.16) (2024-07-02)

## [0.0.15](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.15) (2024-06-22)

## [0.0.14](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.14) (2024-06-17)

## [0.0.13](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.13) (2024-06-13)

## [0.0.12](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.12) (2024-06-13)

## [0.0.11](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.11) (2024-06-13)

## [0.0.10](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.10) (2024-06-13)

## [0.0.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.9) (2024-06-12)

## [0.0.8](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.8) (2024-05-17)

## [0.0.7](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.7) (2024-05-17)

## [0.0.5](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.5) (2024-05-13)


### Features

* **docs:** sync blocks in lists [CLK-469951] ([#37246](https://github.com/time-loop/clickup/issues/37246)) ([67b14db](https://github.com/time-loop/clickup/commit/67b14db393834e37a0091aa4967ef75f1e0f4869)), closes [#37247](https://github.com/time-loop/clickup/issues/37247) [#37249](https://github.com/time-loop/clickup/issues/37249)
* **hierarchy:** support custom icons for lists [CLK-489841] ([#38147](https://github.com/time-loop/clickup/issues/38147)) ([3598c1e](https://github.com/time-loop/clickup/commit/3598c1ea86ffc823054540a4af0200819c8f8601))
* **inbox:** include sprint template name in team_sprint_settings [CLK-496743] ([#38193](https://github.com/time-loop/clickup/issues/38193)) ([01b88a9](https://github.com/time-loop/clickup/commit/01b88a9bc3ca3a5c843333524bca8537a16ba861))

## [0.0.5](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.5) (2024-05-02)


### Features

* **docs:** sync blocks in lists [CLK-469951] ([#37246](https://github.com/time-loop/clickup/issues/37246)) ([67b14db](https://github.com/time-loop/clickup/commit/67b14db393834e37a0091aa4967ef75f1e0f4869)), closes [#37247](https://github.com/time-loop/clickup/issues/37247) [#37249](https://github.com/time-loop/clickup/issues/37249)

## [0.0.5](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.5) (2024-04-29)


### Features

* **docs:** sync blocks in lists [CLK-469951] ([#37246](https://github.com/time-loop/clickup/issues/37246)) ([67b14db](https://github.com/time-loop/clickup/commit/67b14db393834e37a0091aa4967ef75f1e0f4869)), closes [#37247](https://github.com/time-loop/clickup/issues/37247) [#37249](https://github.com/time-loop/clickup/issues/37249)

## [0.0.5](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.5) (2024-04-28)


### Features

* **docs:** sync blocks in lists [CLK-469951] ([#37246](https://github.com/time-loop/clickup/issues/37246)) ([67b14db](https://github.com/time-loop/clickup/commit/67b14db393834e37a0091aa4967ef75f1e0f4869)), closes [#37247](https://github.com/time-loop/clickup/issues/37247) [#37249](https://github.com/time-loop/clickup/issues/37249)

## [0.0.4](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.4) (2024-04-16)

## [0.0.3](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.3) (2024-04-15)

## [0.0.2](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.0.2) (2024-04-12)

## 0.0.1 (2024-04-09)


### Features

* **hierarchy:**  add hierarchy client [CLK-484164] ([#36730](https://github.com/time-loop/clickup/issues/36730)) ([845c299](https://github.com/time-loop/clickup/commit/845c299ea022757d87639080db670da0bb3e7f48))
