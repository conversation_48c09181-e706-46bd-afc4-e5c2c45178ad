import { Injectable } from '@nestjs/common';

import { AutomationJobSchedulerService, DelayedJobArgs } from '@clickup/automation/clients';
import type { ExportedObjectInfo } from '@clickup/clickup-page-exports';
import {
    DashboardAutomationPaywallService,
    DashboardExportService,
    DashboardTemporalWorkflowMetrics,
    SCHEDULED_REPORTS_PAYWALL_LIMIT_REACHED_STATUS,
    ScheduledReportsPaywallService,
} from '@clickup/dashboards';
import { WorkflowType } from '@clickup/temporal/shared-types';
import { ConfigService } from '@clickup/utils/config';
import { DBClient } from '@clickup/utils/db';
import { FeatureFlagService } from '@clickup/utils/feature-flag';
import { FormatLogger } from '@clickup/utils/logging';
import { MetricsService } from '@clickup/utils/metrics';
import { iaction } from '@clickup-legacy/automation/lib/types/Action';
import { itrigger } from '@clickup-legacy/automation/lib/types/Trigger';
import { forwardMessageToSort } from '@clickup-legacy/automation/queue/send';
import { dashboardResource } from '@clickup-legacy/automation/resources/dashboard';
import handleExportResultAction, {
    HandleExportResultActionInput,
} from '@clickup-legacy/automation/resources/dashboard/actions/handleExportResult.action';
import { FeatureFlag } from '@clickup-legacy/models/integrations/split/feature-flag.enum';
import { _getGenericViewAsync } from '@clickup-legacy/models/views/viewTaskIDFetch';

import {
    CreateCurrentDateEventArgs,
    CreateCurrentDateEventsByParentArgs,
    CreateOnDueDateEventArgs,
    CreateOnStartDateEventArgs,
    CreateScheduleEventArgs,
    ExportDashboardArgs,
} from '../temporal/workflows';
import { getActions, getTrigger, isValidTrigger } from '../utils/schedules';

const ONE_HOUR = 60 * 60 * 1000;
const ONE_DAY = 24 * ONE_HOUR;
const ONE_WEEK = 7 * ONE_DAY;
const TASK_LIMIT = 10000;

export interface Task {
    id: string;
    name?: string;
    start_date?: string | number;
    due_date?: string | number;
    cf_date?: string | number;
}

@Injectable()
export class ActivitiesService {
    constructor(
        private readonly metricsService: MetricsService,
        private readonly configService: ConfigService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly dbClient: DBClient,
        private readonly logger: FormatLogger,
        private automationJobSchedulerService: AutomationJobSchedulerService,
        private readonly dashboardExportService: DashboardExportService,
        private readonly dashboardAutomationPaywallService: DashboardAutomationPaywallService,
        private readonly scheduledReportsPaywallService: ScheduledReportsPaywallService
    ) {}

    /**
     * Create current date events for tasks in a location
     * @param args
     */
    async getTasksForCurrentDateTrigger(args: CreateCurrentDateEventsByParentArgs): Promise<Task[]> {
        const funcName = this.getTasksForCurrentDateTrigger.name;
        if (
            this.featureFlagService.getBoolean({
                flag: FeatureFlag.ShouldDisableCurrentDateTrigger,
                target: args.workspaceId.toString(),
            })
        ) {
            this.logger.warn('Current Date Trigger is disabled', funcName, {
                workflowStats: {
                    workspaceId: Number(args.workspaceId),
                    parentId: Number(args.parentId),
                    parentType: args.parentType,
                    triggerId: args.triggerId,
                    field: args.field,
                    offsetType: args.offsetType,
                    offsetValue: args.offsetValue,
                    unit: args.unit,
                },
            });
            return [];
        }
        // step 1: get tasks that the current date is X days before or after the field value
        const tasks: Task[] = await this.getTasks(
            args.parentId,
            args.parentType,
            args.userId,
            args.field,
            args.offsetType,
            args.offsetValue,
            args.unit
        );
        this.logger.info(`${funcName} number of tasks`, funcName, {
            workflowStats: {
                workspaceId: Number(args.workspaceId),
                parentId: Number(args.parentId),
                parentType: args.parentType,
                triggerId: args.triggerId,
                field: args.field,
                offsetType: args.offsetType,
                offsetValue: args.offsetValue,
                unit: args.unit,
                taskCount: tasks.length,
            },
        });
        return tasks;
    }

    async createDelayedEventForCurrentDateTrigger(
        task: Task,
        args: CreateCurrentDateEventsByParentArgs,
        taskWorkflowType: string
    ): Promise<void> {
        // run workflow to create the current date event for each qualified task
        const currentDateTS = new Date().getTime();
        let targetDateTS;
        if (args.field === 'start_date') {
            targetDateTS = Number(task.start_date);
        } else if (args.field === 'due_date') {
            targetDateTS = Number(task.due_date);
        } else {
            targetDateTS = Number(task.cf_date);
        }

        const delay = this.calculateDelayForCurrentDateEvent(
            args.offsetType,
            args.offsetValue,
            args.unit,
            currentDateTS,
            Number(targetDateTS)
        );
        const workflowId = `${taskWorkflowType}_${args.workspaceId}_${task.id}_${args.triggerId}`;
        const existingJob: any = await this.automationJobSchedulerService.getDelayedJob(
            workflowId,
            args.workspaceId.toString()
        );
        if (existingJob && existingJob.status?.name === 'RUNNING') {
            await this.automationJobSchedulerService.terminateDelayedJob(
                args.workspaceId.toString(),
                workflowId,
                existingJob.runId
            );
        }
        const wt = this.getWorkflowTypeByValue(taskWorkflowType);
        if (wt) {
            await this.automationJobSchedulerService.createDelayedJob(wt, args.workspaceId.toString(), delay, {
                triggerId: args.triggerId,
                taskId: task.id,
                field: args.field,
                workspaceId: args.workspaceId,
                userId: args.userId,
            });
        }
    }

    /**
     * Create the current date event for a task
     * @param args
     * @param triggerType
     */
    async createEventForCurrentDateTrigger(args: CreateCurrentDateEventArgs, triggerType: string): Promise<void> {
        const funcName = this.createEventForCurrentDateTrigger.name;
        if (
            this.featureFlagService.getBoolean({
                flag: FeatureFlag.ShouldDisableCurrentDateTrigger,
                target: args.workspaceId.toString(),
            })
        ) {
            this.logger.warn('Current Date Trigger is disabled', funcName, {
                workflowStats: {
                    workspaceId: Number(args.workspaceId),
                    triggerId: args.triggerId,
                    field: args.field,
                    taskId: args.taskId,
                },
            });
            return;
        }
        await forwardMessageToSort(
            {
                resource: 'task',
                message: {
                    trigger: triggerType,
                    trigger_id: args.triggerId,
                    task_id: args.taskId,
                    team_id: Number(args.workspaceId),
                    userid: args.userId,
                    payload: {
                        created_by_hist_field: args.field,
                    },
                },
                local_node_sysid: this.configService.get('local_node_sysid'),
                date_created: Date.now().toString(),
                options: {},
            },
            false
        );
    }

    /**
     * Create the on due date event for a task
     * @param args
     */
    async createEventForOnDueDateTrigger(args: CreateOnDueDateEventArgs): Promise<void> {
        await forwardMessageToSort(
            {
                resource: 'task',
                message: {
                    trigger: 'on_due_date',
                    trigger_id: args.triggerId,
                    task_id: args.taskId,
                    team_id: Number(args.workspaceId),
                    userid: args.userId,
                },
                local_node_sysid: this.configService.get('local_node_sysid'),
                date_created: Date.now().toString(),
                options: {},
            },
            false
        );
    }

    /**
     * Create the on start date event for a task
     * @param args
     */
    async createEventForOnStartDateTrigger(args: CreateOnStartDateEventArgs): Promise<void> {
        await forwardMessageToSort(
            {
                resource: 'task',
                message: {
                    trigger: 'on_start_date',
                    trigger_id: args.triggerId,
                    task_id: args.taskId,
                    team_id: Number(args.workspaceId),
                    userid: args.userId,
                },
                local_node_sysid: this.configService.get('local_node_sysid'),
                date_created: Date.now().toString(),
                options: {},
            },
            false
        );
    }

    /**
     * Calculate the delay for the current date event based on the offset value and type
     * @param offsetType
     * @param offsetValue
     * @param unit
     * @param currentDateTS
     * @param targeDateTS
     * @private
     */
    private calculateDelayForCurrentDateEvent(
        offsetType: string,
        offsetValue: number,
        unit: string,
        currentDateTS: number,
        targeDateTS: number
    ): number {
        let unitValue;
        if (unit === 'HOURS') {
            unitValue = ONE_HOUR;
        } else if (unit === 'WEEKS') {
            unitValue = ONE_WEEK;
        } else {
            unitValue = ONE_DAY;
        }
        let delay;
        if (offsetType === 'BEFORE') {
            delay = targeDateTS - offsetValue * unitValue - currentDateTS;
        } else {
            delay = targeDateTS + offsetValue * unitValue - currentDateTS;
        }
        // ensure the delay is not negative
        return Math.max(delay, 0);
    }

    /**
     * Get tasks that the current date is X days before or after the field value
     * @param parentId
     * @param parentType
     * @param userId
     * @param field
     * @param offsetType
     * @param offsetValue
     * @param unit
     */
    private async getTasks(
        parentId: string | number,
        parentType: number,
        userId: number,
        field: string,
        offsetType: string,
        offsetValue: number,
        unit: string
    ): Promise<Task[]> {
        const currentDateTS = new Date().getTime();
        const filters = this.createGenericViewFilters(field, offsetType, offsetValue, unit, currentDateTS);
        const view = {
            name: this.getTasks.name,
            type: this.configService.get('views.view_types.list'),
            parent: {
                id: parentId,
                type: parentType,
            },
            filters,
            exclude_dupes: true,
            settings: { show_subtasks: 3 },
            exclude_multiple_lists: false,
            exclude_sub_multiple_lists: false,
        };
        const options = {
            include_archived: false,
            skipAccess: true,
            return_query: true,
        };
        const genericView = await _getGenericViewAsync(userId, view, options);
        const query = `SELECT id, name, start_date, due_date
                       FROM (${genericView.query}) ggv LIMIT ${TASK_LIMIT}`;
        const params = genericView.params ?? [];
        const { rows } = await this.dbClient.readAsync<Task>(query, params, { useReplica: true });
        if (field.startsWith('cf_')) {
            const idToTaskDateMap = new Map<string, Task>();
            rows.forEach(row => {
                idToTaskDateMap.set(row.id, row);
            });
            return this.enrichTaskDateWithCFDateValues(idToTaskDateMap, field);
        }
        return rows;
    }

    /**
     * Enrich task date with custom field date values
     * @param IdToTaskDateMap
     * @param field
     * @private
     */
    private async enrichTaskDateWithCFDateValues(IdToTaskDateMap: Map<string, Task>, field: string): Promise<Task[]> {
        if (IdToTaskDateMap.size === 0) {
            return [];
        }
        const query = `
            SELECT item_id as task_id, value
            FROM task_mgmt.field_values
            WHERE item_id = ANY ($1)
              AND field_id = $2
        `;
        const params = [Array.from(IdToTaskDateMap.keys()), field.split('_')[1]];
        const { rows } = await this.dbClient.readAsync<{ task_id: string; value: { value: number } }>(query, params, {
            useReplica: true,
        });
        rows.forEach(row => {
            const taskDate = IdToTaskDateMap.get(row.task_id);
            if (taskDate) {
                taskDate.cf_date = row.value.value;
                IdToTaskDateMap.set(row.task_id, taskDate);
            }
        });
        return Array.from(IdToTaskDateMap.values());
    }

    /**
     * Create generic view filters to find tasks that the current date is X days before or after the field value
     * @param field
     * @param offsetType
     * @param offsetValue
     * @param unit
     * @param currentDateTS
     * @private
     */
    private createGenericViewFilters(
        field: string,
        offsetType: string,
        offsetValue: number,
        unit: string,
        currentDateTS: number
    ) {
        let unitValue;
        if (unit === 'HOURS') {
            unitValue = ONE_HOUR;
        } else if (unit === 'WEEKS') {
            unitValue = ONE_WEEK;
        } else {
            unitValue = ONE_DAY;
        }
        let targetDateTS;
        if (field === 'start_date') {
            field = 'startDate';
        } else if (field === 'due_date') {
            field = 'dueDate';
        }
        /*
        if it is X days before the field value, we need to find tasks that the field value is X days after the current date
        if it is X days after the field value, we need to find tasks that the field value is X days before the current date
         */
        if (offsetType === 'BEFORE') {
            targetDateTS = currentDateTS + offsetValue * unitValue;
        } else {
            targetDateTS = currentDateTS - offsetValue * unitValue;
        }
        return {
            show_closed: false,
            search_custom_fields: true,
            op: 'AND',
            fields: [
                {
                    field,
                    op: 'EQ',
                    values: [
                        {
                            dueDate: targetDateTS,
                            dueDateTime: true,
                            op: 'gt',
                        },
                    ],
                },
            ],
        };
    }

    private async removeInvalidSchedule(workflowType: WorkflowType, args: DelayedJobArgs, funcName: string) {
        const scheduleId = this.automationJobSchedulerService.createScheduleId(workflowType, args);
        const existingSchedule = await this.automationJobSchedulerService.getSchedule(
            Number(args.workspaceId),
            scheduleId
        );
        if (existingSchedule) {
            await this.automationJobSchedulerService.deleteSchedule(Number(args.workspaceId), scheduleId);
        }
        this.logger.info(`Invalid schedule trigger`, funcName, {
            workflowStats: {
                workspaceId: Number(args.workspaceId),
                parentId: Number(args.parentId),
                parentType: args.parentType,
                triggerId: args.triggerId,
            },
        });
    }

    async getActions(triggerId: string, resource: string, actionType: string): Promise<iaction[]> {
        const actions = await getActions(this.dbClient, triggerId);
        return (actions || []).filter(action => action.type === actionType && action.resource === resource);
    }

    async createScheduleEvent(args: CreateScheduleEventArgs): Promise<any[]> {
        const funcName = this.createScheduleEvent.name;
        if (
            this.featureFlagService.getBoolean({
                flag: FeatureFlag.ShouldDisableScheduleTrigger,
                target: args.workspaceId.toString(),
            })
        ) {
            this.logger.warn('Schedule Trigger is disabled', funcName, {
                workflowStats: {
                    workspaceId: Number(args.workspaceId),
                    parentId: Number(args.parentId),
                    parentType: args.parentType,
                    triggerId: args.triggerId,
                },
            });
            return [];
        }
        const trigger = await getTrigger(this.dbClient, args.triggerId);
        const isValid = await isValidTrigger(trigger);

        if (!isValid) {
            // when user activate a schedule trigger, we do (re)create the schedule in automationService,
            // we handle trigger deletion, deactivation or type change here to avoid creating dangling schedules
            await this.removeInvalidSchedule(WorkflowType.AutomationCreateScheduleEvent, args, funcName);
            return [];
        }

        // for non-location based, just generate a single message for the entity
        // TODO: support task based schedule triggers where the resource is 'task'
        if (args.resource && args.resource !== 'task') {
            return [
                {
                    trigger: 'schedule',
                    team_id: Number(args.workspaceId),
                    trigger_id: args.triggerId,
                    view_id: args.parentId,
                    userid: args.userId,
                },
            ];
        }
        let taskLimit = TASK_LIMIT;
        // if the trigger has task condition, then we should only run the schedule on those tasks
        const conditions = trigger?.conditions || [];
        if (Array.isArray(conditions) && conditions.length > 0) {
            const taskCondition = conditions.find(c => c.field === 'task_id');
            if (taskCondition && taskCondition.value) {
                const taskIds = Array.isArray(taskCondition.value) ? taskCondition.value : [taskCondition.value];
                this.logger.info(`${funcName} number of tasks from condition`, funcName, {
                    workflowStats: {
                        workspaceId: Number(args.workspaceId),
                        parentId: Number(args.parentId),
                        parentType: args.parentType,
                        triggerId: args.triggerId,
                        taskCount: taskIds.length,
                    },
                });
                return taskIds.map(taskId => ({ id: taskId as string }));
            }
        } else if (Array.isArray(conditions) && conditions.length === 0) {
            const actions = await getActions(this.dbClient, args.triggerId);
            let runOnce = true;
            actions.forEach(action => {
                if (!action.input?.run_once && !action.type.startsWith('launch_ai_agent')) {
                    runOnce = false;
                }
            });
            // if all actions have run_once set to true, then we just need to send one message
            if (runOnce) {
                taskLimit = 1;
            }
        }
        const view = {
            name: funcName,
            type: this.configService.get('views.view_types.list'),
            parent: {
                id: args.parentId,
                type: args.parentType,
            },
            exclude_dupes: true,
            settings: { show_subtasks: 3 },
            exclude_multiple_lists: false,
            exclude_sub_multiple_lists: false,
        };
        const options = {
            include_archived: false,
            skipAccess: true,
            return_query: true,
        };
        const genericView = await _getGenericViewAsync(args.userId, view, options);
        const query = `SELECT id, name
                       FROM (${genericView.query}) ggv LIMIT ${taskLimit}`;
        const params = genericView.params ?? [];
        const { rows: tasks } = await this.dbClient.readAsync<Task>(query, params, { useReplica: true });
        this.logger.info(`${funcName} number of tasks`, funcName, {
            workflowStats: {
                workspaceId: Number(args.workspaceId),
                parentId: Number(args.parentId),
                parentType: args.parentType,
                triggerId: args.triggerId,
                taskCount: tasks.length,
            },
        });
        if (tasks.length === 0) {
            return [
                {
                    trigger: 'schedule',
                    team_id: Number(args.workspaceId),
                    trigger_id: args.triggerId,
                    userid: args.userId,
                },
            ];
        }
        const messages: any[] = [];
        // temporal has 2MB size limit on input/output so let's return as little data as possible
        tasks.forEach(task => {
            messages.push({
                task_id: task.id,
            });
        });
        return messages;
    }

    async exportDashboardRequest(args: ExportDashboardArgs): Promise<void> {
        const funcName = this.exportDashboardRequest.name;
        const workflowStats = {
            workspaceId: Number(args.workspaceId),
            triggerId: args.triggerId,
            userId: args.userId,
        };

        this.metricsService.client.increment(DashboardTemporalWorkflowMetrics.ATTEMPTS_COUNT, 1, {});

        const validationResult = await this.validateDashboardExportRequest(args, funcName, workflowStats);
        if (!validationResult.isValid) {
            this.metricsService.client.increment(DashboardTemporalWorkflowMetrics.VALIDATION_FAILURES_COUNT, 1, {
                errorType: validationResult.error || 'unknown',
            });

            this.logger.warn(`Dashboard export validation failed: ${validationResult.error}`, funcName, {
                workflowStats,
                error: validationResult.error,
            });
            return;
        }

        const startTime = Date.now();

        try {
            await this.dashboardAutomationPaywallService.checkLimit(args.workspaceId);
            await this.scheduledReportsPaywallService.checkIncrementAndNotify(
                validationResult.trigger!.team_id,
                validationResult.trigger!.userid
            );

            this.metricsService.client.increment(DashboardTemporalWorkflowMetrics.REQUESTS_COUNT, 1, {});
            await this.dashboardExportService.exportDashboard(
                {
                    workspaceId: args.workspaceId.toString(),
                    dashboardId: args.parentId,
                    userId: args.userId,
                },
                {
                    automationTriggerId: args.triggerId,
                    recipients: validationResult.input!,
                }
            );
            this.logger.info('Dashboard export service call completed successfully', funcName, { workflowStats });

            this.metricsService.client.increment(DashboardTemporalWorkflowMetrics.SUCCESS_COUNT, 1, {});
            this.metricsService.client.timing(DashboardTemporalWorkflowMetrics.DURATION_MS, Date.now() - startTime, {
                result: DashboardTemporalWorkflowMetrics.SUCCESS_COUNT,
            });
        } catch (error: unknown) {
            this.logAndInstrumentDashboardExportError(error, args, funcName, startTime);

            const dashboardData = await this.getDashboardData(args.parentId);

            await this.forwardMessage('dashboard', {
                trigger: 'schedule',
                trigger_id: args.triggerId,
                userid: args.userId,
                team_id: Number(args.workspaceId),
                parent_id: args.parentId,
                parent_type: validationResult.trigger!.parent_type,
                item_id: args.parentId,
                entity_name: dashboardData?.name,
                payload: this.getDashboardExportErrorMessagePayload(error, args.triggerId),
            });
        }
    }

    private async validateDashboardExportRequest(
        args: ExportDashboardArgs,
        funcName: string,
        workflowStats: { workspaceId: number; triggerId: string; userId: number }
    ): Promise<{
        isValid: boolean;
        input?: HandleExportResultActionInput;
        trigger?: itrigger;
        error?: string;
    }> {
        if (
            this.featureFlagService.getBoolean({
                flag: FeatureFlag.ShouldDisableScheduleTrigger,
                target: args.workspaceId.toString(),
            })
        ) {
            this.logger.warn('Schedule Trigger is disabled', funcName, { workflowStats });
            return { isValid: false, error: 'Schedule Trigger is disabled' };
        }

        const trigger = await getTrigger(this.dbClient, args.triggerId);
        const isValid = await isValidTrigger(trigger);

        // when user activate a schedule trigger, we do (re)create the schedule in automationService,
        // we handle trigger deletion, deactivation or type change here to avoid creating dangling schedules
        if (!isValid) {
            this.logger.warn('Invalid schedule trigger, removing schedule', funcName, { workflowStats });
            await this.removeInvalidSchedule(WorkflowType.AutomationExportDashboard, args, funcName);
            return { isValid: false, error: 'Invalid schedule trigger' };
        }

        // Fetching a handleExportResult action associated with this trigger as the email recipients
        // data is stored in the action input.
        const actions = await this.getActions(args.triggerId, dashboardResource.key, handleExportResultAction.type);

        // There should be exactly one handleExportResult action per trigger
        if (actions.length !== 1) {
            this.logger.error('There can only be one handleExportResult action', funcName, {
                workflowStats,
                actionsCount: actions.length,
            });
            return { isValid: false, error: `Expected 1 handleExportResult action, found ${actions.length}` };
        }

        const action = actions[0];
        const input = action?.input as HandleExportResultActionInput;
        if (!input) {
            this.logger.error('Invalid or missing input in action', funcName, { workflowStats });
            return { isValid: false, error: 'Invalid or missing input in action' };
        }

        if (!args.parentId) {
            this.logger.error('Missing parentId in workflow args', funcName, { workflowStats });
            return { isValid: false, error: 'Missing parentId in workflow args' };
        }

        if (typeof args.parentId !== 'string') {
            this.logger.error('parentId in workflow args must be a string', funcName, { workflowStats });
            return { isValid: false, error: 'parentId in workflow args must be a string' };
        }

        return {
            isValid: true,
            input,
            trigger,
        };
    }

    private logAndInstrumentDashboardExportError(
        error: unknown,
        args: ExportDashboardArgs,
        funcName: string,
        startTime: number
    ) {
        const workflowStats = {
            workspaceId: Number(args.workspaceId),
            triggerId: args.triggerId,
            userId: args.userId,
        };
        let errorMetric;
        if (this.scheduledReportsPaywallService.isScheduledReportsPaywallError(error)) {
            errorMetric = DashboardTemporalWorkflowMetrics.SCHEDULED_REPORTS_PAYWALL_LIMIT_REACHED_COUNT;
            this.logger.info('Scheduled reports paywall limit reached', funcName, { workflowStats, error });
        } else if (this.dashboardAutomationPaywallService.isAutomationPaywallError(error)) {
            errorMetric = DashboardTemporalWorkflowMetrics.AUTOMATION_PAYWALL_LIMIT_REACHED_COUNT;
            this.logger.info('Dashboard automation paywall limit reached', funcName, { workflowStats, error });
        } else {
            errorMetric = DashboardTemporalWorkflowMetrics.ERRORS_COUNT;
            this.logger.error('Unknown error', funcName, { workflowStats, error });
        }

        this.metricsService.client.increment(errorMetric, 1, {});
        this.metricsService.client.timing(DashboardTemporalWorkflowMetrics.DURATION_MS, Date.now() - startTime, {
            result: errorMetric,
        });
    }

    private async getDashboardData(dashboardId: string): Promise<ExportedObjectInfo | undefined> {
        try {
            const dashboardData = await this.dashboardExportService.getDashboardData(dashboardId);
            return dashboardData;
        } catch (error) {
            this.logger.error('Error getting dashboard data', this.getDashboardData.name, { error, dashboardId });
            return undefined;
        }
    }

    private getDashboardExportErrorMessagePayload(error: unknown, triggerId: string) {
        const errorLike = error as { message?: string; status?: number; ECODE?: string };
        const status = errorLike.status || 500;
        return {
            for_trigger_id: triggerId,
            success: false,
            error: errorLike.message || 'Unknown error',
            status,
            code: errorLike.ECODE,
            client_error: status === 404, // gateway returns 404 for Not Found or Authorized
            scheduled_reports_paywall_limit_reached: status === SCHEDULED_REPORTS_PAYWALL_LIMIT_REACHED_STATUS,
        };
    }

    async forwardMessage(resource: string, message: any): Promise<void> {
        await forwardMessageToSort(
            {
                resource,
                message,
                local_node_sysid: this.configService.get('local_node_sysid'),
                date_created: Date.now().toString(),
                options: {},
            },
            false
        );
    }

    /**
     * Get the workflow type by value
     * @param value
     */
    getWorkflowTypeByValue(value: string): WorkflowType | undefined {
        const keys = Object.keys(WorkflowType) as Array<keyof typeof WorkflowType>;
        const values = Object.values(WorkflowType);

        const index = values.indexOf(value as unknown as WorkflowType);
        if (index !== -1) {
            const key = keys[index];
            if (key) {
                return WorkflowType[key];
            }
        }
        return undefined;
    }
}
