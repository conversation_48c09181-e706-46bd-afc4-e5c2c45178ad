# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [0.99.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.99.0) (2025-06-03)

## [0.98.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.98.0) (2025-06-02)

## [0.97.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.97.0) (2025-05-29)

## [0.96.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.96.0) (2025-05-26)

## [0.95.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.95.0) (2025-05-10)


### Features

* **access-management:** CRM authz debugger legacy checks [CLK-684367] ([#58765](https://github.com/time-loop/clickup/issues/58765)) ([456d3b0](https://github.com/time-loop/clickup/commit/456d3b0a3baaf05b00f2ed9e5fbcb3a021e9f56c))

## [0.94.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.94.0) (2025-05-01)

## [0.93.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.93.0) (2025-05-01)


### Features

* **access-management:** Include parent entries in v3 ACL [CLK-665206] ([#58057](https://github.com/time-loop/clickup/issues/58057)) ([e1db736](https://github.com/time-loop/clickup/commit/e1db73639fb1ff70736cb7e09835dfcfe071c5a8))

## [0.92.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.92.0) (2025-04-29)

## [0.91.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.91.0) (2025-04-24)

## [0.90.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.90.0) (2025-04-03)

## [0.89.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.89.0) (2025-03-27)

## [0.88.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.88.0) (2025-03-25)

## [0.87.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.87.0) (2025-03-20)

## [0.86.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.86.0) (2025-03-19)

## [0.85.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.85.0) (2025-03-18)

## [0.84.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.84.0) (2025-03-14)

## [0.83.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.83.0) (2025-03-06)


### Features

* **project-mgmt:** implementation of work schedules [CLK-602101] ([#53342](https://github.com/time-loop/clickup/issues/53342)) ([04b2987](https://github.com/time-loop/clickup/commit/04b2987c5a2023269b390a6fee549c763a5698d7))

## [0.82.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.82.0) (2025-02-19)

## [0.81.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.81.0) (2025-02-13)

## [0.80.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.80.0) (2025-02-05)

## [0.79.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.79.0) (2025-02-03)

## [0.78.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.78.0) (2025-01-30)

## [0.77.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.77.0) (2025-01-22)

## [0.76.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.76.0) (2025-01-22)


### Features

* **user-platform:** Add Logic of exposing User Profile Status in same way as monolith does [CLK-606984] ([#50645](https://github.com/time-loop/clickup/issues/50645)) ([01d034f](https://github.com/time-loop/clickup/commit/01d034f68da4c9820b67d4238ef4be7066e42113))

## [0.75.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.75.0) (2025-01-16)


### Features

* **user-platform:** Add User profile status details for experience api [CLK-602367] ([#50134](https://github.com/time-loop/clickup/issues/50134)) ([778c839](https://github.com/time-loop/clickup/commit/778c8396a2cf01a438630cd29cc59f4e633bde8c))

## [0.74.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.74.0) (2025-01-15)

## [0.73.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.73.0) (2025-01-06)

## [0.72.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.72.0) (2025-01-02)

## [0.71.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.71.0) (2024-12-26)

## [0.70.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.70.0) (2024-12-20)


### Features

* **data-platform:** Update OVM library [CLK-578572] ([#49395](https://github.com/time-loop/clickup/issues/49395)) ([23bfcb1](https://github.com/time-loop/clickup/commit/23bfcb1c3ee1d24614e5840cdb6fde26dc8aca06))

## [0.69.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.69.0) (2024-12-18)


### Features

* **work-analytics:** Export all users with extended CSV [CLK-546228] ([#47454](https://github.com/time-loop/clickup/issues/47454)) ([7e9b725](https://github.com/time-loop/clickup/commit/7e9b7251bac888d236c32add9aa48e8f57914ca8))

## [0.68.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.68.0) (2024-12-11)


### Features

* **access-management:** Adding userIdsToNotifyActions to access request model  [CLK-525776] ([#48735](https://github.com/time-loop/clickup/issues/48735)) ([ed71b50](https://github.com/time-loop/clickup/commit/ed71b50fee3d97595b1355131df07483f14a032e))

## [0.67.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.67.0) (2024-12-10)

## [0.66.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.66.0) (2024-12-06)

## [0.65.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.65.0) (2024-12-03)

## [0.64.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.64.0) (2024-12-03)

## [0.63.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.63.0) (2024-12-02)


### Bug Fixes

* **user-platform:** Regenerate user service client [CLK-565013] ([#48114](https://github.com/time-loop/clickup/issues/48114)) ([5671fa9](https://github.com/time-loop/clickup/commit/5671fa9977cc95e3cec0080152a04fe8301eeee7))

## [0.62.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.62.0) (2024-11-28)

## [0.61.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.61.0) (2024-11-22)

## [0.60.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.60.0) (2024-11-14)


### Bug Fixes

* **project-mgmt:** Add approval parent lookup in authz [CLK-548852] ([#44485](https://github.com/time-loop/clickup/issues/44485)) ([4185baf](https://github.com/time-loop/clickup/commit/4185bafc4eeb313d7af5231cf62cd8b47aa1934b))

## [0.59.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.59.0) (2024-11-14)

## [0.58.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.58.0) (2024-11-12)

## [0.57.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.57.0) (2024-10-29)


### Features

* **access-management:** allow users to request access outside workspace and choose limited member role  [CLK-545849] ([#46271](https://github.com/time-loop/clickup/issues/46271)) ([2152754](https://github.com/time-loop/clickup/commit/21527543660bf982f2397375e8a7969e4c1e4e19))

## [0.56.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.56.0) (2024-10-16)

## [0.55.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.55.0) (2024-10-10)


### Features

* **work-analytics:** Export group members [CLK-546228] ([#44192](https://github.com/time-loop/clickup/issues/44192)) ([24aed6e](https://github.com/time-loop/clickup/commit/24aed6eca39fe5cbe353c55ca6c6dd349cad8017))

## [0.54.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.54.0) (2024-10-10)

## [0.53.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.53.0) (2024-10-08)

## [0.52.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.52.0) (2024-10-03)

## [0.51.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.51.0) (2024-10-02)

## [0.50.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.50.0) (2024-10-02)

## [0.49.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.49.0) (2024-09-26)

## [0.48.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.48.0) (2024-09-24)

## [0.47.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.47.0) (2024-09-24)

## [0.46.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.46.0) (2024-09-23)

## [0.45.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.45.0) (2024-09-13)


### Features

* **access-management:** Include user status with team members [CLK-544679] ([#43622](https://github.com/time-loop/clickup/issues/43622)) ([a023f50](https://github.com/time-loop/clickup/commit/a023f50e930ecf001bb14b017b45102be8dea64e))

## [0.44.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.44.0) (2024-09-12)


### Features

* **access-management:** add support for alter access requests [CHAT-3070] ([#43481](https://github.com/time-loop/clickup/issues/43481)) ([ba934b3](https://github.com/time-loop/clickup/commit/ba934b3df7b5c4065c2a1c032bd9053f344d8677))

## [0.43.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.43.0) (2024-09-10)

## [0.42.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.42.0) (2024-09-04)

## [0.41.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.41.0) (2024-09-04)

## [0.40.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.40.0) (2024-09-03)

## [0.39.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.39.0) (2024-08-30)

## [0.38.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.38.0) (2024-08-29)


### Features

* **access-management:** internal api to close all pending access requests for specified user  [CLK-497487] ([#42718](https://github.com/time-loop/clickup/issues/42718)) ([cd57675](https://github.com/time-loop/clickup/commit/cd576759b02a6b7b40ba365d75dc5748579af289))

## [0.37.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.37.0) (2024-08-29)


### Features

* **access-management:** Fetch deactivated users as clickbot [CHAT-2868] ([#42698](https://github.com/time-loop/clickup/issues/42698)) ([5cb25c6](https://github.com/time-loop/clickup/commit/5cb25c61cfe594b3e3ce0daf3ab5fa5e56c498ea))

## [0.36.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.36.0) (2024-08-16)


### Features

* **access-management:** add user object to access requests [CLK-533052] ([#42048](https://github.com/time-loop/clickup/issues/42048)) ([893e671](https://github.com/time-loop/clickup/commit/893e67164e6ec4cc3298ca9ec7afa932bd742d86))

## [0.35.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.35.0) (2024-08-15)


### Bug Fixes

* **access-management:** add actions array to actions api response [CLK-533051] ([#42005](https://github.com/time-loop/clickup/issues/42005)) ([e77156e](https://github.com/time-loop/clickup/commit/e77156e5066f8fabfc6a19707e82e2d89948f3d5))

## [0.35.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.35.0) (2024-08-08)

## [0.35.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.35.0) (2024-08-07)

## [0.34.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.34.0) (2024-07-31)

## [0.33.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.33.0) (2024-07-30)

## [0.32.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.32.0) (2024-07-25)


### Features

* **core-services:** Add internal /ui/v3/workspaces/{workspace_id}/users/bulk gateway route [CLK-525786] ([#40920](https://github.com/time-loop/clickup/issues/40920)) ([8d8dfdf](https://github.com/time-loop/clickup/commit/8d8dfdf570597de01e4f1f43877136acf56ed3cd))

## [0.31.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.31.0) (2024-07-24)


### Features

* **access-management:** add invitees(with id and email) to workspace invite and share payload [CLK-526076] ([#41015](https://github.com/time-loop/clickup/issues/41015)) ([877f078](https://github.com/time-loop/clickup/commit/877f0780e89d598fd6afd9f6e5977f5bd50fde70))

## [0.30.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.30.0) (2024-07-24)

## [0.29.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.29.0) (2024-07-22)


### Features

* **access-management:** Have patch ACL return same data structure as bulk get acl [CLK-524234] ([#40872](https://github.com/time-loop/clickup/issues/40872)) ([3c5725c](https://github.com/time-loop/clickup/commit/3c5725c646580bcead787b647b7eaa0a140633c7))

## [0.28.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.28.0) (2024-07-18)


### Features

* **access-management:** add creator property to acl resource [CLK-524139] ([#40753](https://github.com/time-loop/clickup/issues/40753)) ([118de42](https://github.com/time-loop/clickup/commit/118de42e54467a6c12cf22217f3069e94b7fd8f2))

## [0.27.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.27.0) (2024-07-18)

## [0.26.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.26.0) (2024-07-10)

## [0.25.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.25.0) (2024-07-02)

## [0.24.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.24.0) (2024-06-25)

## [0.23.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.23.0) (2024-06-22)


### Features

* **access-management:** Role sub-type core changes [CLK-500914] ([#39446](https://github.com/time-loop/clickup/issues/39446)) ([f60f459](https://github.com/time-loop/clickup/commit/f60f459b6f62939523ee1aa5ad1b126a073a11ae)), closes [#39369](https://github.com/time-loop/clickup/issues/39369)

## [0.22.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.22.0) (2024-06-20)


### Bug Fixes

* **core-services:** Fix OpenAPI specs of health checks [CLK-515791] ([#39792](https://github.com/time-loop/clickup/issues/39792)) ([9bac3a8](https://github.com/time-loop/clickup/commit/9bac3a88aa9a7d601a329e93d356ebb48c2b1128))

## [0.21.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.21.0) (2024-06-17)

## [0.20.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.20.0) (2024-06-13)

## [0.19.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.19.0) (2024-06-13)

## [0.18.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.18.0) (2024-06-13)


### Features

* **crm-billing:** Handle new columns in task_mgmt.user_profiles table [CLK-513731] ([#39438](https://github.com/time-loop/clickup/issues/39438)) ([ea74ec6](https://github.com/time-loop/clickup/commit/ea74ec68dcf2b02c8d72ea0977fc4bb598a02a37))

## [0.17.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.17.0) (2024-06-13)

## [0.16.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.16.0) (2024-06-12)

## [0.15.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.15.0) (2024-05-21)


### Features

* **access-management:** fetch requester email for pending invites and add to response request [CLK-500535] ([#38679](https://github.com/time-loop/clickup/issues/38679)) ([4c8a845](https://github.com/time-loop/clickup/commit/4c8a845cc84d70cc789324ccbd305cd2ca855caf))

## [0.14.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.14.0) (2024-05-21)


### Features

* **access-management:** ACL service bulk API returns workspace users [CLK-459260] ([#38534](https://github.com/time-loop/clickup/issues/38534)) ([5a2c1fe](https://github.com/time-loop/clickup/commit/5a2c1fe53a3913c8e195c58aaff70870369349a2))
* **access-management:** Add clickbot and minimal user support to workspace users API [CLK-499780] ([#38607](https://github.com/time-loop/clickup/issues/38607)) ([36f1e7c](https://github.com/time-loop/clickup/commit/36f1e7ce178ab6268e32c2c373e1a458aa0fc336))
* **access-management:** Group API and ACL swagger fixes [CLK-459260] ([#38415](https://github.com/time-loop/clickup/issues/38415)) ([54d0bd6](https://github.com/time-loop/clickup/commit/54d0bd6a28470fe16ef354bb984a444537ec69cd))
* **access-management:** invite and approve action for outside workspace user [CLK-494726] ([#38523](https://github.com/time-loop/clickup/issues/38523)) ([a961dd3](https://github.com/time-loop/clickup/commit/a961dd3e2fc91deca794043f78d3029793215d97))
* **chat:** adding version vector to data of last-read-at service [CLK-493305] ([#38154](https://github.com/time-loop/clickup/issues/38154)) ([61b4825](https://github.com/time-loop/clickup/commit/61b48251a7fe36096c73fc4e276feac88dd60351))


### Bug Fixes

* **access-management:** Fix user-service swagger [CLK-499780] ([#38685](https://github.com/time-loop/clickup/issues/38685)) ([24adbb0](https://github.com/time-loop/clickup/commit/24adbb0b6e463023704c45b6292f77f8185e695d))

## [0.13.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.13.9) (2024-05-14)


### Features

* **access-management:** update access request action payload [CLK-494740] ([#38357](https://github.com/time-loop/clickup/issues/38357)) ([3748101](https://github.com/time-loop/clickup/commit/3748101d31ef88e0f73d50eb2d3e62f41d283bc1))

## [0.13.8](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.13.8) (2024-05-13)


### Bug Fixes

* **access-management:** Fix workspace user experience API 500 error [CLK-494800] ([#38372](https://github.com/time-loop/clickup/issues/38372)) ([f56b99e](https://github.com/time-loop/clickup/commit/f56b99eed8219bb64581b618f71f1198e9392feb))

## [0.13.7](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.13.7) (2024-05-13)

## [0.13.6](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.13.6) (2024-05-10)


### Bug Fixes

* **access-management:** add isArray flag to GetBulkAclResponse acls property [CLK-497459] ([#38271](https://github.com/time-loop/clickup/issues/38271)) ([462efd7](https://github.com/time-loop/clickup/commit/462efd7c68a02df6c5d88246a7cef0714fe70697))

## [0.13.5](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.13.5) (2024-05-08)


### Features

* **access-management:** Authz trace debugging extensions [CLK-494507] ([#38077](https://github.com/time-loop/clickup/issues/38077)) ([3f46548](https://github.com/time-loop/clickup/commit/3f46548efe44f9f0fa941f14122959bbba3fb474))

## [0.13.4](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.13.4) (2024-05-02)


### Features

* **access-management:** Load ACL parents [CLK-490627] ([#37793](https://github.com/time-loop/clickup/issues/37793)) ([c1edfee](https://github.com/time-loop/clickup/commit/c1edfee0c84c3c44b266242f1b65869ecca2e93b))

## [0.13.3](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.13.3) (2024-05-02)

## [0.13.2](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.13.2) (2024-04-29)

## [0.13.1](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.13.1) (2024-04-28)


### Bug Fixes

* **access-management:** minor to patch fix, also update user-service-api  [CLK-492874] ([#37820](https://github.com/time-loop/clickup/issues/37820)) ([8d32019](https://github.com/time-loop/clickup/commit/8d32019de1b7a57c2f9a04b898bf751b2bcf4659))

## [0.13.0](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.13.0) (2024-04-27)


### Features

* **access-management:** expose unpublished expired resource api in user service [CLK-492874] ([#37811](https://github.com/time-loop/clickup/issues/37811)) ([bb8c926](https://github.com/time-loop/clickup/commit/bb8c926bb68cd5e178b1d058481f0f3aac3b069b))


### Bug Fixes

* **access-management:** fix user service publishing issue  [CLK-492874] ([#37819](https://github.com/time-loop/clickup/issues/37819)) ([1195dde](https://github.com/time-loop/clickup/commit/1195dde8222584048c436356d63b6ee9d1519a63))

## [0.12.10](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.12.10) (2024-04-27)


### Features

* **access-management:** expose unpublished expired resource api in user service [CLK-492874] ([#37811](https://github.com/time-loop/clickup/issues/37811)) ([bb8c926](https://github.com/time-loop/clickup/commit/bb8c926bb68cd5e178b1d058481f0f3aac3b069b))

## [0.12.9](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.12.9) (2024-04-16)

## [0.12.8](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.12.8) (2024-04-15)

## [0.12.7](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.12.7) (2024-04-12)


### Features

* **core-services:** migrate required to local services to esbuild [CLK-470314] ([#37135](https://github.com/time-loop/clickup/issues/37135)) ([494aaf2](https://github.com/time-loop/clickup/commit/494aaf2bf28a5421fb7581c95c8f67984f516b7c))

## [0.12.6](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.12.6) (2024-04-12)

## [0.12.5](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.12.5) (2024-04-09)

## [0.12.4](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.12.4) (2024-04-08)


### Bug Fixes

* **access-management:** Fix access requests and workspace membership data model [CLK-486013] ([#36910](https://github.com/time-loop/clickup/issues/36910)) ([00d7520](https://github.com/time-loop/clickup/commit/00d7520487e457e8a7ea1cd59ed14b26488d0dee))

## [0.12.3](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.12.3) (2024-03-29)

## [0.12.2](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.12.2) (2024-03-29)


### Features

* **data-platform:** switch to using axios client for running the upsert [CLK-483404] ([#36585](https://github.com/time-loop/clickup/issues/36585)) ([8a94f7b](https://github.com/time-loop/clickup/commit/8a94f7bbab2990adba78169c4de6a19623808d3c))

## [0.12.1](https://github.com/time-loop/clickup/compare/<EMAIL>-service-client@0.12.1) (2024-03-28)
