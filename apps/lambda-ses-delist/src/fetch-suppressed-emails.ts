import {
    ListSuppressedDestinationsCommand,
    SESv2Client,
    SuppressedDestinationSummary,
    TooManyRequestsException,
} from '@aws-sdk/client-sesv2';

import { retryWithBackoff } from './retry-with-backoff';
import { sleep as defaultSleep } from './sleep';

const SES_API_PAGESIZE_MAX = 1000;

/**
 * Fetches a batch of suppressed emails from the SES suppression list.
 * We assume that the API will not give us more emails than requested,
 * but if it does, we simply pass them through.
 *
 * @param sesClient The SES client to use
 * @param batchSize The number of emails to fetch
 * @param startDate The date to start fetching from
 * @param maxRetries How many times should we retry if AWS fires TooManyRequestsException?
 * @param initialDelay How long should we wait to retry?
 * @param sleep How should we wait?
 * @returns A list of suppressed email addresses
 */
export async function fetchSuppressedDestinations(
    sesClient: SESv2Client,
    batchSize: number,
    startDate: Date,
    maxRetries: number,
    initialDelay: number,
    sleep: (delay: number) => Promise<void> = defaultSleep
): Promise<SuppressedDestinationSummary[]> {
    const results: SuppressedDestinationSummary[] = [];
    let nextToken: string | undefined;

    do {
        const remainingItems = batchSize - results.length;
        const pageSize = Math.min(remainingItems, SES_API_PAGESIZE_MAX);

        const command = new ListSuppressedDestinationsCommand({
            PageSize: pageSize,
            StartDate: startDate,
            NextToken: nextToken,
        });

        try {
            const response = await retryWithBackoff(() => sesClient.send(command), {
                errorType: TooManyRequestsException,
                maxRetries,
                initialDelay,
                sleep,
            });

            const summaries = response.SuppressedDestinationSummaries ?? [];
            results.push(...summaries);

            // Update nextToken for the next iteration
            nextToken = response.NextToken;
        } catch (error) {
            if (error instanceof TooManyRequestsException) {
                throw new Error(`Failed to fetch suppressed emails after ${maxRetries} retries`);
            }
            throw error;
        }
    } while (nextToken && results.length < batchSize);

    return results;
}
