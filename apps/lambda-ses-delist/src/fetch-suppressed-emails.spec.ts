import { ListSuppressedDestinationsCommand, SESv2Client, TooManyRequestsException } from '@aws-sdk/client-sesv2';
import { mockClient } from 'aws-sdk-client-mock';

import { fetchSuppressedDestinations } from './fetch-suppressed-emails';

const sesMock = mockClient(SESv2Client);
const mockSesClient = new SESv2Client({});
const mockDate = new Date('2024-01-01');
const mockBatchSize = 10;
const mockMaxRetries = 3;
const mockInitialDelay = 100000000; // detect cases where we failed to mock sleep
const mockSleep = jest.fn().mockResolvedValue(undefined);

beforeEach(() => {
    sesMock.reset();
    mockSleep.mockClear();
});

it('should return list of suppressed email addresses on success', async () => {
    const mockEmails = ['<EMAIL>', '<EMAIL>'];
    sesMock.on(ListSuppressedDestinationsCommand).resolves({
        SuppressedDestinationSummaries: mockEmails.map(email => ({
            EmailAddress: email,
            Reason: 'BOUNCE',
            LastUpdateTime: new Date(),
        })),
    });

    const result = await fetchSuppressedDestinations(
        mockSesClient,
        mockBatchSize,
        mockDate,
        mockMaxRetries,
        mockInitialDelay,
        mockSleep
    );

    expect(result).toEqual(
        mockEmails.map(email => ({
            EmailAddress: email,
            Reason: 'BOUNCE',
            LastUpdateTime: expect.any(Date),
        }))
    );
    expect(sesMock).toHaveReceivedCommandWith(ListSuppressedDestinationsCommand, {
        PageSize: mockBatchSize,
        StartDate: mockDate,
    });
});

it('should return empty array when no suppressed emails are found', async () => {
    sesMock.on(ListSuppressedDestinationsCommand).resolves({
        SuppressedDestinationSummaries: [],
    });

    const result = await fetchSuppressedDestinations(
        mockSesClient,
        mockBatchSize,
        mockDate,
        mockMaxRetries,
        mockInitialDelay,
        mockSleep
    );

    expect(result).toEqual([]);
});

it('should retry on TooManyRequestsException and eventually succeed', async () => {
    const mockEmails = ['<EMAIL>'];

    sesMock
        .on(ListSuppressedDestinationsCommand)
        .rejectsOnce(new TooManyRequestsException({ $metadata: {}, message: 'Rate limit exceeded' }))
        .resolves({
            SuppressedDestinationSummaries: mockEmails.map(email => ({
                EmailAddress: email,
                Reason: 'BOUNCE',
                LastUpdateTime: new Date(),
            })),
        });

    const result = await fetchSuppressedDestinations(
        mockSesClient,
        mockBatchSize,
        mockDate,
        mockMaxRetries,
        mockInitialDelay,
        mockSleep
    );

    expect(result).toEqual(
        mockEmails.map(email => ({
            EmailAddress: email,
            Reason: 'BOUNCE',
            LastUpdateTime: expect.any(Date),
        }))
    );
    expect(sesMock).toHaveReceivedCommandTimes(ListSuppressedDestinationsCommand, 2);
    expect(mockSleep).toHaveBeenCalledTimes(1);
    expect(mockSleep).toHaveBeenCalledWith(mockInitialDelay);
});

it('should throw error after max retries exceeded', async () => {
    sesMock
        .on(ListSuppressedDestinationsCommand)
        .rejects(new TooManyRequestsException({ $metadata: {}, message: 'Rate limit exceeded' }));

    await expect(
        fetchSuppressedDestinations(mockSesClient, mockBatchSize, mockDate, mockMaxRetries, mockInitialDelay, mockSleep)
    ).rejects.toThrow('Failed to fetch suppressed emails after 3 retries');

    expect(sesMock).toHaveReceivedCommandTimes(ListSuppressedDestinationsCommand, mockMaxRetries + 1);
    expect(mockSleep).toHaveBeenCalledTimes(mockMaxRetries);
    expect(mockSleep).toHaveBeenNthCalledWith(1, mockInitialDelay);
    expect(mockSleep).toHaveBeenNthCalledWith(2, mockInitialDelay * 2);
    expect(mockSleep).toHaveBeenNthCalledWith(3, mockInitialDelay * 4);
}, 10000);

it('should handle multiple pages of results', async () => {
    const mockEmails = Array.from({ length: 15 }, (_, i) => `test${i}@example.com`);
    const firstPageEmails = mockEmails.slice(0, 10);
    const secondPageEmails = mockEmails.slice(10);

    sesMock
        .on(ListSuppressedDestinationsCommand)
        .resolvesOnce({
            SuppressedDestinationSummaries: firstPageEmails.map(email => ({
                EmailAddress: email,
                Reason: 'BOUNCE',
                LastUpdateTime: new Date(),
            })),
            NextToken: 'next-page-token',
        })
        .resolvesOnce({
            SuppressedDestinationSummaries: secondPageEmails.map(email => ({
                EmailAddress: email,
                Reason: 'BOUNCE',
                LastUpdateTime: new Date(),
            })),
        });

    const result = await fetchSuppressedDestinations(
        mockSesClient,
        100,
        mockDate,
        mockMaxRetries,
        mockInitialDelay,
        mockSleep
    );

    expect(result).toHaveLength(15);
    expect(result.map(r => r.EmailAddress)).toEqual(mockEmails);
    expect(sesMock).toHaveReceivedCommandTimes(ListSuppressedDestinationsCommand, 2);
    expect(sesMock).toHaveReceivedNthCommandWith(1, ListSuppressedDestinationsCommand, {
        PageSize: 100,
        StartDate: mockDate,
    });
    expect(sesMock).toHaveReceivedNthCommandWith(2, ListSuppressedDestinationsCommand, {
        PageSize: 90, // ask for the remaining 90
        StartDate: mockDate,
        NextToken: 'next-page-token',
    });
});

it('should stop pagination when batch size is reached', async () => {
    const mockEmails = Array.from({ length: 25 }, (_, i) => `test${i}@example.com`);
    const firstPageEmails = mockEmails.slice(0, 10);
    const secondPageEmails = mockEmails.slice(10, 20);

    sesMock
        .on(ListSuppressedDestinationsCommand)
        .resolvesOnce({
            SuppressedDestinationSummaries: firstPageEmails.map(email => ({
                EmailAddress: email,
                Reason: 'BOUNCE',
                LastUpdateTime: new Date(),
            })),
            NextToken: 'next-page-token',
        })
        .resolvesOnce({
            SuppressedDestinationSummaries: secondPageEmails.map(email => ({
                EmailAddress: email,
                Reason: 'BOUNCE',
                LastUpdateTime: new Date(),
            })),
            NextToken: 'final-page-token',
        });

    const result = await fetchSuppressedDestinations(
        mockSesClient,
        15,
        mockDate,
        mockMaxRetries,
        mockInitialDelay,
        mockSleep
    );

    expect(result).toHaveLength(20);
    expect(result.map(r => r.EmailAddress)).toEqual(mockEmails.slice(0, 20));
    expect(sesMock).toHaveReceivedCommandTimes(ListSuppressedDestinationsCommand, 2);
});

it('should handle last page with fewer items than page size', async () => {
    const mockEmails = Array.from({ length: 12 }, (_, i) => `test${i}@example.com`);
    const firstPageEmails = mockEmails.slice(0, 10);
    const secondPageEmails = mockEmails.slice(10);

    sesMock
        .on(ListSuppressedDestinationsCommand)
        .resolvesOnce({
            SuppressedDestinationSummaries: firstPageEmails.map(email => ({
                EmailAddress: email,
                Reason: 'BOUNCE',
                LastUpdateTime: new Date(),
            })),
            NextToken: 'next-page-token',
        })
        .resolvesOnce({
            SuppressedDestinationSummaries: secondPageEmails.map(email => ({
                EmailAddress: email,
                Reason: 'BOUNCE',
                LastUpdateTime: new Date(),
            })),
        });

    const result = await fetchSuppressedDestinations(
        mockSesClient,
        15,
        mockDate,
        mockMaxRetries,
        mockInitialDelay,
        mockSleep
    );

    expect(result).toHaveLength(12);
    expect(result.map(r => r.EmailAddress)).toEqual(mockEmails);
    expect(sesMock).toHaveReceivedCommandTimes(ListSuppressedDestinationsCommand, 2);
});
