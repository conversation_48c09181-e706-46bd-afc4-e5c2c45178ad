:root[data-gantt-theme='dark'] {
	--dhx-gantt-theme: dark;
	--dhx-gantt-base-colors-disabled: #3d3d3d;
	--dhx-gantt-base-colors-text-light: #AAA;
	--dhx-gantt-base-colors-text-base: rgba(255, 255, 255, 0.90);
	--dhx-gantt-base-colors-background: #141414;
	--dhx-gantt-base-colors-background-alt: #383838;
	--dhx-gantt-base-colors-border: #4B4B4B;
	--dhx-gantt-base-colors-border-light: #4B4B4B;

	--dhx-gantt-navline-font-color: var(--dhx-gantt-base-colors-text-base);
	--dhx-gantt-base-colors-primary: #3B72F8;
	--dhx-gantt-base-colors-error: #EB284F;

	--dhx-gantt-base-colors-select: #2A2A2A;
	--dhx-gantt-base-colors-hover-color: #2A2A2A;
	--dhx-gantt-base-colors-icons: #AAAAAA;

	--dhx-gantt-scale-color: var(--dhx-gantt-base-colors-text-light);

	--dhx-gantt-popup-background: #1B1B1C;
	// --dhx-gantt-lightbox-title-background: var(--dhx-gantt-popup-background);
	// --dhx-gantt-popup-color: var(--dhx-gantt-base-colors-text-base);
	--dhx-gantt-popup-border: 1px solid #4B4B4B;

	--dhx-gantt-btn-font-weight: 600;
	--dhx-gantt-undo-delete-background: var(--dhx-gantt-popup-background);

	--dhx-gantt-link-handle-border: var(--dhx-gantt-base-colors-border);
	--dhx-gantt-link-handle-background: var(--dhx-gantt-base-colors-background);
}