:root[data-gantt-theme='contrast-black'] {
	--dhx-gantt-theme: contrast-black;
	--dhx-gantt-base-colors-disabled: #3d3d3d;
	--dhx-gantt-base-colors-text-light: #cfcfcf;
	--dhx-gantt-base-colors-text-base: #FFFFFF;
	--dhx-gantt-base-colors-background: #141414;
	--dhx-gantt-base-colors-background-alt: #4f4f4f;
	--dhx-gantt-base-colors-border: rgba(255, 255, 255, 0.80);
	--dhx-gantt-base-colors-border-light: rgba(255, 255, 255, 0.80);
	--dhx-gantt-base-colors-text-on-fill: #141414;

	--dhx-gantt-base-colors-primary: #A395FF;

	--dhx-gantt-task-background: var(--dhx-gantt-base-colors-primary);
	--dhx-gantt-project-background: #77D257;
	--dhx-gantt-milestone-background: #FCBA2E;

	--dhx-gantt-task-background: #A395FF;

	--dhx-gantt-task-border: 1px solid rgba(0,0,0, 0.1);

	--dhx-gantt-base-colors-warning: #694E02;
	--dhx-gantt-base-colors-success: #115700;

	--dhx-gantt-base-colors-error: #FFA7A0;
	--dhx-gantt-base-colors-error-text: #141414;


	--dhx-gantt-base-colors-select: #2A2A2A;
	--dhx-gantt-base-colors-hover-color: #2A2A2A;

	--dhx-gantt-base-colors-icons: #AAAAAA;

	--dhx-gantt-scale-color: var(--dhx-gantt-base-colors-text-light);

	--dhx-gantt-popup-background: #1B1B1C;
	--dhx-gantt-undo-delete-background: var(--dhx-gantt-popup-background);
	--dhx-gantt-undo-delete-color: var(--dhx-gantt-base-colors-text-base);
	--dhx-gantt-popup-border: 1px solid #4B4B4B;


	--dhx-gantt-font-size: 16px;
	--dhx-gantt-heading-font-size: 24px;
	--dhx-gantt-important-font-size: 16px;
	--dhx-gantt-regular-font-size: 16px;
	--dhx-gantt-caption-font-size: 16px;

	--dhx-gantt-btn-outline-color-hover:var(--dhx-gantt-base-colors-text-light);
	--dhx-gantt-btn-outline-color-active:var(--dhx-gantt-base-colors-text-light);
}