:root[data-gantt-theme='skyblue'] {
	--dhx-gantt-theme: skyblue;
	--dhx-gantt-font-family: Segoe UI,Arial,san-serif;

	--dhx-gantt-base-colors-primary: #0288D1;

	--dhx-gantt-task-background: var(--dhx-gantt-base-colors-primary);

	--dhx-gantt-base-colors-border: #cecece;

	--dhx-gantt-task-text-font-size: 14px;
	--dhx-gantt-task-text-font-weight: 500;

	--dhx-gantt-heading-font-size: 22px;
	--dhx-gantt-heading-font-weight: 300;
	--dhx-gantt-caption-font-size: 14px;
	--dhx-gantt-caption-font-weight: 400;

	--dhx-gantt-scale-color: #767676;

	--dhx-gantt-border-radius:0;


	--dhx-gantt-box-shadow-s: 0 3px 5px 0 rgba(0,0,0,.1);;
	--dhx-gantt-box-shadow-m: 0px 4px 24px 0px rgba(44, 47, 60, 0.36);
	
	--dhx-gantt-box-shadow-l: 0px 4px 24px 0px rgba(44, 47, 60, 0.56);

	--dhx-gantt-scale-background: #dfedff;
	--dhx-gantt-scale-color: #42464b;
	--dhx-gantt-task-color: #1e2022;
	--dhx-gantt-project-background: #eff6fb;
	--dhx-gantt-task-background: #eff6fb;
	--dhx-gantt-milestone-background: #DB7DC5;
	--dhx-gantt-task-border: 1px solid #3588c5;
	--dhx-gantt-task-progress-color: linear-gradient(0deg, #abcee8 0, #5aa0d3 36%, #bfdaee);


	--dhx-gantt-link-background: #4a8f43;

	--dhx-gantt-lightbox-title-background: #eff6fb;

	.gantt_grid_scale, .gantt_task_scale, .gantt_task_vscroll{
		box-shadow: inset 0 1px 1px #fff;
		background-image: linear-gradient(to bottom, #e4f0ff 0%, #dfedff 50%, #d5e8ff 100%);

	}
}