@font-face {
    font-family: "dhx-gantt-icons";
    /* src: url("./dhx-gantt-icons.woff?d2ea3e087edb3b9fad35a6580bd8acd3") format("woff"),
url("./dhx-gantt-icons.woff2?d2ea3e087edb3b9fad35a6580bd8acd3") format("woff2"); */

    src: url("./iconfont/dhx-gantt-icons.woff") format("woff"),
url("./iconfont/dhx-gantt-icons.woff2") format("woff2");
}

.dhx_gantt_icon {
    &:before{
        font-family: dhx-gantt-icons !important;
        font-style: normal;
        font-weight: normal !important;
        font-variant: normal;
        text-transform: none;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--dhx-gantt-base-colors-icons);
        font-size:var(--dhx-gantt-icon-size, 1.5em);
    }
}

.dhx_gantt_icon.dhx_gantt_icon_arrow_down {
    &:before{
        content: "\f101";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_arrow_up {
    &:before{
        content: "\f102";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_close {
    &:before{
        content: "\f103";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_constraint_left_dotted {
    &:before{
        content: "\f104";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_constraint_left_solid {
    &:before{
        content: "\f105";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_constraint_right_dotted {
    &:before{
        content: "\f106";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_constraint_right_solid {
    &:before{
        content: "\f107";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_delete {
    &:before{
        content: "\f108";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_dots_vertical {
    &:before{
        content: "\f109";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_edit {
    &:before{
        content: "\f10a";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_menu_down {
    &:before{
        content: "\f10b";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_menu_right {
    &:before{
        content: "\f10c";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_minus {
    &:before{
        content: "\f10d";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_plus {
    &:before{
        content: "\f10e";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_rounded_arrow_right {
    &:before{
        content: "\f10f";
    }
}
.dhx_gantt_icon.dhx_gantt_icon_timer {
    &:before{
        content: "\f110";
        max-width: 100%;
        max-height: 100%;
        font-size: 1em;
    }
}

