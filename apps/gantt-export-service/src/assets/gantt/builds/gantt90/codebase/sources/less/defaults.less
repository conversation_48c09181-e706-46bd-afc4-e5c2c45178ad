@lightboxButtonFontSize: 13px;
@quickInfoButtonFontSize: 13px;
@lightboxBg: #fff;
.buttonBg{
    background: @lightboxBg;
}
@linkControlSize : 13px;
@linkControlOffset: 7px;
@taskSideContentPadding: @linkControlSize + @linkControlOffset;

@rowBorderBottom: 1px;
@openBranchIcon:  url("@{baseIconPath}/plus.png");
@closeBranchIcon:  url("@{baseIconPath}/minus.png");
@openedFolderIcon:  url("@{baseIconPath}/folderOpen.png");
@closedFolderIcon:  url("@{baseIconPath}/folderClosed.png");
@fileIcon:  url("@{baseIconPath}/file.png");