:root[data-gantt-theme='broadway'] {
	--dhx-gantt-theme: broadway;
	--dhx-gantt-font-family: Se<PERSON>e <PERSON>,Arial,san-serif;

	--dhx-gantt-base-colors-primary: #0288D1;

	--dhx-gantt-base-colors-select: #fff3a1;
	--dhx-gantt-task-background: var(--dhx-gantt-base-colors-primary);

	--dhx-gantt-base-colors-border: #cecece;
	--dhx-gantt-halfhour-border: 1px solid #e8e8e8;
	--dhx-gantt-task-title-font-size: 12px;

	--dhx-gantt-task-text-font-size: 14px;
	--dhx-gantt-task-text-font-weight: 500;

	--dhx-gantt-heading-font-size: 22px;
	--dhx-gantt-heading-font-weight: 300;
	--dhx-gantt-caption-font-size: 14px;
	--dhx-gantt-caption-font-weight: 400;

	--dhx-gantt-scale-color: #767676;

	--dhx-gantt-border-radius:0;

	--dhx-gantt-box-shadow-s: 0 3px 5px 0 rgba(0,0,0,.1);;
	--dhx-gantt-box-shadow-m: 0px 4px 24px 0px rgba(44, 47, 60, 0.36);
	
	--dhx-gantt-box-shadow-l: 0px 4px 24px 0px rgba(44, 47, 60, 0.56);

	--dhx-gantt-grid-scale-background: #4f4f4f;
	--dhx-gantt-grid-scale-border-vertical:1px solid #c1c1c1;
	--dhx-gantt-grid-scale-color: #e1e1e1;

	--dhx-gantt-timeline-scale-background: #dbdbdb;
	--dhx-gantt-timeline-scale-border-vertical:1px solid #ebebeb;
	--dhx-gantt-timeline-scale-color: #494949;
	--dhx-gantt-grid-body-background: #dbdbdb;

	--dhx-gantt-lightbox-title-background:#4f4f4f;
	--dhx-gantt-lightbox-title-color:#e1e1e1;

	--dhx-gantt-task-color: #FFF;
	--dhx-gantt-link-background: #ffb96d;

	--dhx-gantt-project-background: #65c16f;

	--dhx-gantt-project-progress-color: #46ad51;
	--dhx-gantt-task-progress-color: #04a4f0;
	--dhx-gantt-task-background: #17b2fb;
	--dhx-gantt-milestone-background: #DB7DC5;

	.gantt_grid_data {
		--dhx-gantt-task-row-background: var(--dhx-gantt-grid-body-background);
		--dhx-gantt-task-row-background--odd: var(--dhx-gantt-grid-body-background);
	}


}