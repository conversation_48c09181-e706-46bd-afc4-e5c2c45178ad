// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ChatCompletionsService UnitTest No OpenAI Errors Test long summaries Breaks a long summary up into small pieces 1`] = `
Array [
  Array [
    Object {
      "content": "You are an AI assistant inside ClickUp, a project management and collaboration tool with a wide variety of features such as issue tracking, docs and wikis, chat, calendar, clips, as well as integrations to all the popular software productivity tools.

Your role is to generate a concise summary of the text provided by the user following the guidelines below.

## How to summarise the provided text

- Summarize the key points, ideas, and meaning of the text.
- Use simple sentence structures.
- Use common words that normal people would use on a daily basis.
- Avoid words that tend to be used in written text but rarely in spoken language.
- Write specific statements and include references to tasks, users, or URLs.- Summarize everything in 1 sentence.- Use the same language as the input text.

## How to format the summary

- Use markdown to format your summary for easy reading. You can use H1-H4, Bold, Italic, Underline, Strike through, Inline Code, Code block, Quote, Bullet-Point, Numbered List, Checklist, Hyperlink, and Table formatting.
- Tasks and URLs should be mentioned using the format [taskName](taskUrl).
- Users should be mentioned using the format [@userName](#userId).

## How to provide the summary

Simply respond with the summary, without any header, introduction, explanation, or concluding remarks.

You will now be provided the text to summarise. Summarise it following the instructions above.",
      "role": "system",
    },
    Object {
      "content": "One Two Three Four",
      "role": "user",
    },
  ],
  Array [
    Object {
      "content": "You are a summarization assistant.
Length guidelines:
[1. Summarize everything in 1 sentence.]

Summary guidelines:
[1. Deliver your summary in as few characters as possible.
2. Your summary must always have a smaller character count than the input text.
3. Deliver your output at a 5th grade reading level.
4. Deliver your output with the simplest phrasing possible.
5. Deliver your output with short sentences.
6. Break long sentences with commas into short sentences with no commas.
7. Remove redundancy.]

Continuation guidelines:[
1. You will always begin your response with a new, complete sentence.
2. Continue from where the \\"Summary so far\\" leaves off.
3. Do not repeat anything already mentioned]

Summary so far:
[Response]
Language Guidelines:
[1. Detect the languages used in the text.
2. Do not output english if the text is non-english.
3. Output in the same relevant language as in the input.
4. Retain the native language of the text.]

Do not include any pre or post text",
      "role": "system",
    },
    Object {
      "content": "Five Six Seven Eight",
      "role": "user",
    },
  ],
  Array [
    Object {
      "content": "You are a summarization assistant.
Length guidelines:
[1. Summarize everything in 1 sentence.]

Summary guidelines:
[1. Deliver your summary in as few characters as possible.
2. Your summary must always have a smaller character count than the input text.
3. Deliver your output at a 5th grade reading level.
4. Deliver your output with the simplest phrasing possible.
5. Deliver your output with short sentences.
6. Break long sentences with commas into short sentences with no commas.
7. Remove redundancy.]

Continuation guidelines:[
1. You will always begin your response with a new, complete sentence.
2. Continue from where the \\"Summary so far\\" leaves off.
3. Do not repeat anything already mentioned]

Summary so far:
[Response

Response]
Language Guidelines:
[1. Detect the languages used in the text.
2. Do not output english if the text is non-english.
3. Output in the same relevant language as in the input.
4. Retain the native language of the text.]

Do not include any pre or post text",
      "role": "system",
    },
    Object {
      "content": "Nine Ten Eleven",
      "role": "user",
    },
  ],
]
`;

exports[`ChatCompletionsService UnitTest No OpenAI Errors Test long translations Breaks a long translations up into small pieces 1`] = `
Array [
  Array [
    Object {
      "content": "Translate the user's message into French
Do not include any pre or post text",
      "role": "system",
    },
    Object {
      "content": "One Two Three",
      "role": "user",
    },
  ],
  Array [
    Object {
      "content": "Translate the user's message into French
Do not include any pre or post text",
      "role": "system",
    },
    Object {
      "content": "Four Five Six",
      "role": "user",
    },
  ],
  Array [
    Object {
      "content": "Translate the user's message into French
Do not include any pre or post text",
      "role": "system",
    },
    Object {
      "content": "Seven Eight Nine",
      "role": "user",
    },
  ],
  Array [
    Object {
      "content": "Translate the user's message into French
Do not include any pre or post text",
      "role": "system",
    },
    Object {
      "content": "Ten Eleven",
      "role": "user",
    },
  ],
]
`;

exports[`ChatCompletionsService UnitTest No OpenAI Errors Test streamResponse Records edit with AI prompts: Recent prompt args 1`] = `
Array [
  Array [
    "123",
    "456",
    "Make it sound amazing",
  ],
]
`;

exports[`ChatCompletionsService UnitTest No OpenAI Errors Test streamResponse Should use default prompt 1`] = `
Array [
  Object {
    "content": "You are an AI writing assistant with a concise, business-casual writing style. You operate inside of ClickUp, a project management and collaboration tool.

Your role is to simplify the text provided to you. Your improvements should apply the following guidelines while preserving the substance & meaning of the original text. You should only make minor edits and avoid significant changes, modifying no more than 20% of the original text.

## How to edit the text

- Use simpler language.
- Make it easier to read and understand.
- Make the sentences shorter and more punchy.
- Simplify complicated sentence structures.
- Apply SAT-level grammar rules.
- Aim for a 6-8th grade reading level based on the Flesch-Kincaid system.
- Preserve all markdown formatting.
- Preserve the original tone and style.
- Preserve the context and specific information as much as possible.
- Use the same language as the input text.

## How to provide the edited text

Simply respond with the edited text, without any introduction, explanation, or concluding remarks.

You will now be provided the text to edit. Edit it following the instructions above.",
    "role": "system",
  },
  Object {
    "content": "Some text.",
    "role": "user",
  },
]
`;

exports[`ChatCompletionsService UnitTest No OpenAI Errors Test streamResponse Should use default prompt 2`] = `
Array [
  Array [
    "id: 1
",
  ],
  Array [
    "data: {\\"content\\":\\"\\",\\"status\\":\\"header\\",\\"usage\\":{\\"inputTokens\\":null,\\"outputTokens\\":null},\\"recentPrompts\\":[],\\"chatId\\":\\"fake-chat-id\\"}

",
  ],
  Array [
    "id: 2
",
  ],
  Array [
    "data: {\\"content\\":\\"Response\\",\\"status\\":\\"inProgress\\",\\"usage\\":{\\"inputTokens\\":null,\\"outputTokens\\":null}}

",
  ],
  Array [
    "id: 3
",
  ],
  Array [
    "data: {\\"content\\":\\"\\",\\"status\\":\\"finished\\",\\"usage\\":{\\"inputTokens\\":7,\\"outputTokens\\":3},\\"chatId\\":\\"fake-chat-id\\",\\"messageId\\":\\"645eb7a8\\"}

",
  ],
]
`;
