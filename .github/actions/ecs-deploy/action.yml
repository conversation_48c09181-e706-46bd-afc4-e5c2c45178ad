name: 'Deploy Services'
description: 'Directly deploy to ECS by updating Services and Task Definitions'

outputs:
    did-ecs-deploy:
        description: 'The status of the deployment'
        value: ${{ steps.set-deployment-status.outputs.did-ecs-deploy }}

inputs:
    aws-access-key-id:
        description: 'Amazon access key id'
        required: true
    aws-secret-access-key:
        description: 'Amazon access key'
        required: true
    registry-auth-token:
        description: 'Registry auth token'
        required: true
    deploy-lambda-shards:
        description: 'Deploy Lambda Shards'
        required: true
    deploy-clickup-env:
        description: 'Deploy ClickUp Environment'
        required: true
    deploy-type:
        description: 'Deploy Type'
        required: true
    deploy-service-name:
        description: 'Deploy Service Name'
        required: true
    deploy-hook-name:
        description: 'Deploy Hook Name'
        required: true
    deploy-image-tag:
        description: 'Deploy Image Tag'
        required: true
    deploy-clusters:
        description: 'Override deploy clusters'
        required: false
        default: ''

runs:
    using: 'composite'
    steps:
        ## TODO: These two steps are currently run by the parent job.
        ##       After the Legacy Deployer is removed, they should be moved here.

        # - name: Checkout
        #   uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7

        # - name: Setup node and dependencies
        #   uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
        #   with:
        #       package-manager: 'pnpm'
        #       node-version-file: '.nvmrc'
        #       registry-auth-token: ${{ inputs.registry-auth-token }}

        - name: Deploy Monolith
          shell: bash
          id: deploy-monolith
          if: ${{ inputs.deploy-type == 'monolith' }}
          env:
              FORCE_COLOR: 3 # fix terminal color output
              ECS_DEPLOY_CANARY_SHARDS: ${{ inputs.deploy-lambda-shards }}
              AWS_ACCESS_KEY_ID: ${{ inputs.aws-access-key-id }}
              AWS_SECRET_ACCESS_KEY: ${{ inputs.aws-secret-access-key }}
          run: |
              npx ts-node tools/aws-ecs/deploy/src/index.ts \
              --deploy-clickup-env=${{ inputs.deploy-clickup-env }} \
              --deploy-type=${{ inputs.deploy-type }} \
              --deploy-image-tag=${{ inputs.deploy-image-tag }} \
              --deploy-clusters=${{ inputs.deploy-clusters }}

        - name: Deploy Microservice
          shell: bash
          id: deploy-microservice
          if: ${{ inputs.deploy-type == 'monorepo' }}
          env:
              FORCE_COLOR: 3 # fix terminal color output
              ECS_DEPLOY_CANARY_SHARDS: ${{ inputs.deploy-lambda-shards }}
              AWS_ACCESS_KEY_ID: ${{ inputs.aws-access-key-id }}
              AWS_SECRET_ACCESS_KEY: ${{ inputs.aws-secret-access-key }}
          run: |
              npx ts-node tools/aws-ecs/deploy/src/index.ts \
              --deploy-clickup-env=${{ inputs.deploy-clickup-env }} \
              --deploy-type=${{ inputs.deploy-type }} \
              --deploy-service-name=${{ inputs.deploy-service-name }} \
              --deploy-hook-name=${{ inputs.deploy-hook-name }} \
              --deploy-image-tag=${{ inputs.deploy-image-tag }} \
              --deploy-clusters=${{ inputs.deploy-clusters }}

        - name: Set deployment status
          shell: bash
          id: set-deployment-status
          run: |
              echo "did-ecs-deploy=true" >> $GITHUB_OUTPUT
