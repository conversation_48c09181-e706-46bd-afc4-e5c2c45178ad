# Tags each commit to each main branch with a unique and incrementing semver tag for the main ClickUp application.
# We then use the tag to create a github release with a changelog of all commits since the last release.
# Release notes can be viewed here: https://github.com/time-loop/clickup/releases
# Once we have the version tag we will build and deploy the monolith code under the `src` directory to ECS

name: ECS Monolith

on:
    workflow_call:
        inputs:
            github-event-name:
                description: 'The name of the github event that triggered the workflow'
                required: true
                type: string
            version:
                description: 'The version number that was created for the deploy.'
                required: true
                type: string
            branch-name:
                description: 'The branch name that triggered the workflow initially.'
                required: true
                type: string
            fully-formed-branch-name:
                description: 'The fully formed branch name that triggered the workflow initially.'
                required: true
                type: string
            github-base-ref:
                description: 'The base ref of the PR that triggered this workflow initially'
                type: string
                required: false
            previous-branch-latest-tag-sha:
                description: 'The SHA of the latest tag on the previous branch'
                type: string
                required: false
                default: ''
            is-hotfix:
                description: 'Whether this is a hotfix'
                type: string
                required: false
            # Defaulting to true because we want to build the monolith as a safeguard to ensure the build runs
            has-missing-changes:
                description: 'Whether there are missing changes between the current and previous branch'
                type: string
                required: false
                default: 'true'
            base64_source_ecr_auth_token:
                description: 'The ECR auth token for the source environment'
                type: string
                required: false

env:
    DOCKER_CACHE_ENABLED: false

jobs:
    docker-build:
        name: Build-arm64
        env:
            pnpm-store-cache-name: pnpm-docker-cache-store
            pnpm-store-path-host: pnpm-docker-cache-store # reproducible-containers/buildkit-cache-dance v2.1.3 dosn't support relative paths
            pnpm-store-path-container: /var/cache/pnpm-store
            esbuild-cache-name: cache-esbuild-monolith
            CLICKUP_ESBUILD_CACHE_DIR: /tmp/.esbuild-cache
            SKOPEO_DOCKER_IMAGE: quay.io/skopeo/stable@sha256:3e5efe94472b0a1887b3460e55c9d4eaf7ec62b73395c6ac5916b88390377dee # v1.18.0-immutable
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-8c-16g
        environment: ${{ (startsWith(inputs.branch-name, 'deploy-test/') && 'ecs-deploy-user-usqa') || fromJSON('{"master":"ecs-deploy-user-usqa","staging":"ecs-deploy-user-usstaging","production":"ecs-deploy-user-globalprod"}')[inputs.branch-name] }}
        permissions:
            contents: read
            id-token: write
        outputs:
            docker-image-tag: ${{ steps.push-image.outputs.DST_DOCKER_TAG }}
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

            - name: Generate ECR repo name
              id: generate-ecr-repo-name
              uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
              if: (startsWith(inputs.branch-name, 'deploy-test/') || contains(fromJson('["master", "staging", "production"]'), inputs.branch-name))
              with:
                  script: |
                      let repoType, sourceRepo, targetRepo, DEPLOY_ECR_REPO_NAME, message, ecrRepoSource, ecrRepoTarget;
                      repoType = "monolith";

                      if ("${{ inputs.branch-name }}".startsWith("deploy-test/")){
                            sourceRepo = "skipping";
                            targetRepo = "skipping";
                            ecrRepoSource = "187416477703.dkr.ecr.us-east-1.amazonaws.com/monolith-deploy-us-qa";
                            ecrRepoTarget = "158160757288.dkr.ecr.us-east-1.amazonaws.com/monolith-deploy-us-qa";
                            devLatestTag = "monolith-service-qa";
                            DEPLOY_ECR_REPO_NAME = "monolith-deploy-us-qa";
                            message = "Running for QA. Skipping sourceRepo and targetRepo setting";
                      }

                      switch ("${{ inputs.branch-name }}") {
                        case "master":
                            sourceRepo = "skipping";
                            targetRepo = "skipping";
                            ecrRepoSource = "187416477703.dkr.ecr.us-east-1.amazonaws.com/monolith-deploy-us-qa";
                            ecrRepoTarget = "158160757288.dkr.ecr.us-east-1.amazonaws.com/monolith-deploy-us-qa";
                            devLatestTag = "monolith-service-qa";
                            DEPLOY_ECR_REPO_NAME = "monolith-deploy-us-qa";
                            message = "Running for QA. Skipping sourceRepo and targetRepo setting";
                            break;

                        case "staging":
                            sourceRepo = `187416477703.dkr.ecr.us-east-1.amazonaws.com/monolith-deploy-us-qa`;
                            targetRepo = `424044212339.dkr.ecr.us-east-1.amazonaws.com/monolith-deploy-us-staging`;
                            ecrRepoSource = "424044212339.dkr.ecr.us-east-1.amazonaws.com/monolith-deploy-us-staging";
                            ecrRepoTarget = "158160757288.dkr.ecr.us-east-1.amazonaws.com/monolith-deploy-us-staging";
                            devLatestTag = "monolith-service-staging";
                            DEPLOY_ECR_REPO_NAME = "monolith-deploy-us-staging";
                            message = "Setting source to QA and target to Staging";
                            break;

                        case "production":
                            sourceRepo = `424044212339.dkr.ecr.us-east-1.amazonaws.com/monolith-deploy-us-staging`;
                            targetRepo = `514308641592.dkr.ecr.us-east-1.amazonaws.com/monolith-deploy-global-prod`;
                            ecrRepoSource = "514308641592.dkr.ecr.us-east-1.amazonaws.com/monolith-deploy-global-prod";
                            ecrRepoTarget = "158160757288.dkr.ecr.us-east-1.amazonaws.com/monolith-deploy-global-prod";
                            devLatestTag = "monolith-service-production";
                            DEPLOY_ECR_REPO_NAME = "monolith-deploy-global-prod";
                            message = "Setting source to Staging and target to Production";
                            break;
                      }

                      core.setOutput("DEPLOY_ECR_REPO_NAME", DEPLOY_ECR_REPO_NAME);
                      core.setOutput("source_repo", sourceRepo);
                      core.setOutput("target_repo", targetRepo);
                      core.setOutput("message", message);
                      core.setOutput("ecrRepoSource", ecrRepoSource);
                      core.setOutput("ecrRepoTarget", ecrRepoTarget);
                      core.setOutput("devLatestTag", devLatestTag);

                      console.log("repoType:" + repoType)
                      console.log("DEPLOY_ECR_REPO_NAME:" + DEPLOY_ECR_REPO_NAME)
                      console.log("source_repo:" + sourceRepo)
                      console.log("target_repo:" + targetRepo)
                      console.log("message:" + message)
                      console.log("ecrRepoSource:" + ecrRepoSource)
                      console.log("ecrRepoTarget:" + ecrRepoTarget)
                      console.log("devLatestTag:" + devLatestTag)

            - name: Target ECR Docker Login
              if: ${{ inputs.branch-name == 'master' || inputs.branch-name == 'staging' || inputs.branch-name == 'production' }}
              id: target-ecr-docker-login
              run: |
                  echo "Target ECR Docker Login"
                  aws configure set aws_access_key_id ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws configure set aws_secret_access_key ${{ secrets.AWS_SECRET_ACCESS_KEY }}

                  ecr_auth=$(aws ecr get-login-password --region us-east-1 | base64 -w 0)
                  echo "ecr-auth-token=${ecr_auth}" >> $GITHUB_OUTPUT

            - name: Determine operation
              id: operation
              run: |
                  artifact_deployment_enabled="${{ fromJSON(vars.ENABLE_ARTIFACT_BASED_DEPLOYMENT)[inputs.branch-name] || false }}"
                  echo "artifact_deployment_enabled=$artifact_deployment_enabled" >> $GITHUB_OUTPUT
                  # Set the target ECR auth token regardless of operation type
                  echo "target_ecr_auth_token=${{ steps.target-ecr-docker-login.outputs.ecr-auth-token }}" >> $GITHUB_OUTPUT

                  if [[ "${{ inputs.has-missing-changes }}" == "false" && "${{ inputs.base64_source_ecr_auth_token }}" != "" && "${{ inputs.is-hotfix }}" == "false" && "$artifact_deployment_enabled" == "true" && "${{ inputs.github-event-name }}" == "push" && "${{ inputs.previous-branch-latest-tag-sha }}" != "" ]]; then
                    echo "operation=copy" >> $GITHUB_OUTPUT
                    echo "source_ecr_auth_token=${{ inputs.base64_source_ecr_auth_token }}" >> $GITHUB_OUTPUT
                    echo "image_sha=git-sha-${{ inputs.previous-branch-latest-tag-sha }}" >> $GITHUB_OUTPUT
                  else
                    echo "operation=build" >> $GITHUB_OUTPUT
                    # Explicitly set source_ecr_auth_token to empty when not copying
                    echo "source_ecr_auth_token=" >> $GITHUB_OUTPUT
                  fi
            - name: Debug Outputs
              run: |
                  echo "artifact_deployment_enabled: ${{ steps.operation.outputs.artifact_deployment_enabled }}"
                  echo "operation: ${{ steps.operation.outputs.operation }}"
                  echo "image_sha: ${{ steps.operation.outputs.image_sha }}"
                  [[ -z "${{ steps.operation.outputs.source_ecr_auth_token }}" ]] && echo "source_ecr_auth_token: is not set because it's not staging or production release" || echo "source_ecr_auth_token: is set"
                  [[ -z "${{ steps.operation.outputs.target_ecr_auth_token }}" ]] && echo "target_ecr_auth_token: is not set" || echo "target_ecr_auth_token: is set"

            - name: Source Container Image Existence Check
              if: ${{ steps.operation.outputs.artifact_deployment_enabled == 'true' && steps.operation.outputs.operation == 'copy' && inputs.previous-branch-latest-tag-sha != '' }}
              id: source-container-image-existence-check
              run: |
                  # Set default value
                  echo "image_exists=false"
                  source_auth_token=$(echo -n "${{ steps.operation.outputs.source_ecr_auth_token }}" | base64 -d)

                  if docker run --rm ${{ env.SKOPEO_DOCKER_IMAGE }} \
                      -- skopeo inspect --creds "AWS:${source_auth_token}" docker://${{ steps.generate-ecr-repo-name.outputs.source_repo }}:${{ steps.operation.outputs.image_sha }} > /dev/null 2>&1; then
                      echo "Source container image exists"
                      echo "image_exists=true" >> $GITHUB_OUTPUT
                  else
                      echo "Source container image does not exist"
                      echo "image_exists=false" >> $GITHUB_OUTPUT
                  fi
            - name: Copy Image
              id: copy-image
              if: ${{ steps.operation.outputs.artifact_deployment_enabled == 'true' && steps.operation.outputs.operation == 'copy' && steps.source-container-image-existence-check.outputs.image_exists == 'true' }}
              run: |
                  echo "Copying image"
                  source_auth_token=$(echo -n "${{ steps.operation.outputs.source_ecr_auth_token }}" | base64 -d)
                  target_auth_token=$(echo -n "${{ steps.operation.outputs.target_ecr_auth_token }}" | base64 -d)

                  docker run --rm ${{ env.SKOPEO_DOCKER_IMAGE }} \
                    -- skopeo copy \
                      --src-creds "AWS:${source_auth_token}" \
                      --dest-creds "AWS:${target_auth_token}" \
                      --preserve-digests \
                      --retry-times 3 \
                      docker://${{ steps.generate-ecr-repo-name.outputs.source_repo }}:${{ steps.operation.outputs.image_sha }} \
                      docker://${{ steps.generate-ecr-repo-name.outputs.target_repo }}:version-${{ inputs.version }}

                  DIGESTS=($(docker run --rm ${{ env.SKOPEO_DOCKER_IMAGE }} \
                    -- skopeo inspect \
                    --creds "AWS:${target_auth_token}" \
                    docker://${{ steps.generate-ecr-repo-name.outputs.target_repo }}:version-${{ inputs.version }} \
                    | jq -r '.Digest'))
                  DIGESTSARRAY=$(printf '%s\n' "${DIGESTS[@]}" | jq -R . | jq -s . )
                  ESCAPED_DIGESTSARRAY=$(echo $DIGESTSARRAY | jq -sRr @json)
                  echo $ESCAPED_DIGESTSARRAY
                  echo "ESCAPED_DIGESTSARRAY=$ESCAPED_DIGESTSARRAY" >> $GITHUB_OUTPUT

            - name: Check Docker volume utilization
              if: ${{ (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              run: |
                  bash -c 'if [[ `df /var/lib/docker --output=pcent | sed 1d | tr -d " %"` > 85 ]]; then docker system prune -af ; else df -h /var/lib/docker | sed 1d; fi'

            - name: Setup node and dependencies
              if: ${{  (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Setup Java
              if: ${{  (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
              with:
                  distribution: 'corretto'
                  java-version: '21'
            - name: Setup Gradle
              if: ${{  (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1

            - name: Set up Docker Buildx
              id: setup-buildx
              if: ${{  (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              uses: Wandalen/wretry.action@ffdd254f4eaf1562b8a2c66aeaa37f1ff2231179 # v3.7.3
              with:
                  action: docker/setup-buildx-action@f95db51fddba0c2d1ec667646a06c2ce06100226
                  attempt_delay: 500
                  attempt_limit: 3

            - name: Restore esbuild Cache
              if: ${{  (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              id: cache-esbuild
              uses: tespkg/actions-cache/restore@cba095d7af782a955b8f4fa13396fbf0ab62bd4b # v1.7.1
              timeout-minutes: 1
              continue-on-error: true
              with:
                  bucket: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  path: ${{ env.CLICKUP_ESBUILD_CACHE_DIR }}
                  accessKey: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  secretKey: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  use-fallback: false
                  key: ${{ runner.os }}-test-${{ env.esbuild-cache-name}}-${{ inputs.branch-name }}-${{ github.sha }}
                  restore-keys: |
                      ${{ runner.os }}-test-${{ env.esbuild-cache-name }}-${{ inputs.branch-name }}
                      ${{ runner.os }}-test-${{ env.esbuild-cache-name }}-${{ inputs.github-base-ref }}-${{ github.event.pull_request.base.sha }}
                      ${{ runner.os }}-test-${{ env.esbuild-cache-name }}-${{ inputs.github-base-ref }}

            - name: Set node version
              run: |
                  echo "NODE_VERSION=$(cat .nvmrc | cut -d. -f1)" >> $GITHUB_ENV

            - name: Configure NPM
              if: ${{  (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.ALL_PACKAGE_READ_TOKEN }}" >> ~/.npmrc

            - name: Cache pnpm store for Docker build
              if: ${{  (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              uses: tespkg/actions-cache/restore@a2e3f005921809689270625bc026d387f73017ae # v1
              id: pnpm-store-cache
              continue-on-error: true
              with:
                  bucket: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  path: ${{ env.pnpm-store-path-host }}
                  accessKey: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  secretKey: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  # We don't want to use the fallback cache because it will use too much space of the limited 10GB available
                  use-fallback: false
                  # The repo name is added to the key to prevent collisions between different repos
                  key: ${{ github.event.repository.name }}/${{ runner.os }}-build-${{ env.pnpm-store-cache-name }}-v${{ env.NODE_VERSION }}-${{ hashFiles('pnpm-lock.yaml') }}
                  restore-keys: |
                      ${{ github.event.repository.name }}/${{ runner.os }}-build-${{ env.pnpm-store-cache-name }}-v${{ env.NODE_VERSION }}-

            - name: Output pnpm store info
              if: ${{  (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              uses: gacts/run-and-post-run@d803f6920adc9a47eeac4cb6c93dbc2e2890c684 # v1.4.2
              continue-on-error: true
              with:
                  run: |
                      echo "Size of PNPM Store before install: $(du -sh ${{env.pnpm-store-path-host}} || true)"
                      echo "PNPM Store contents before install: "
                      ls -lah ${{env.pnpm-store-path-host}} || true
                  post: |
                      echo "Size of PNPM Store after install: $(du -sh ${{env.pnpm-store-path-host}} || true)"
                      echo "PNPM Store contents after install: "
                      ls -lah ${{env.pnpm-store-path-host}} || true

            - name: Inject pnpm-store into Docker
              if: ${{  (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              uses: reproducible-containers/buildkit-cache-dance@653a570f730e3b9460adc576db523788ba59a0d7 # 3.2.0
              continue-on-error: true
              with:
                  builder: ${{ fromJSON(steps.setup-buildx.outputs.outputs).name }}
                  cache-map: |
                      {
                          "${{ env.pnpm-store-path-host }}": "${{ env.pnpm-store-path-container }}"
                      }
                  skip-extraction: true

            - name: Configure ecr-cache AWS credentials (us-west-2)
              uses: aws-actions/configure-aws-credentials@f24d7193d98baebaeacc7e2227925dd47cc267f5 # v4.2.0
              with:
                  role-to-assume: arn:aws:iam::158160757288:role/gha-ecr-push
                  role-session-name: gha
                  aws-region: us-west-2

            - name: Login to Amazon ecr-cache (us-west-2)
              run: |
                  aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 158160757288.dkr.ecr.us-west-2.amazonaws.com

            - name: Build image
              if: ${{  (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              uses: nick-fields/retry@7152eba30c6575329ac0576536151aca5a72780e # v3.0.0
              with:
                  max_attempts: 3
                  timeout_minutes: 25
                  command: |
                      BUILD_COMMAND="bash ./tools/docker/clickup/docker-build.sh \
                          --progress=plain \
                          --platform=linux/arm64 \
                          --secret "id=npmrc,src=${HOME}/.npmrc" \
                          --iidfile /tmp/IMAGE_ID \
                          --build-arg PNPM_STORE_PATH="${{ env.pnpm-store-path-container }}" \
                          --build-arg DD_GIT_REPOSITORY_URL="${{ github.repositoryUrl }}" \
                          --build-arg DD_GIT_COMMIT_SHA="${{ github.sha }}""

                      $BUILD_COMMAND

                      test -e /tmp/IMAGE_ID || exit 1
                      echo "DOCKER_IMAGE_ID=$(cat /tmp/IMAGE_ID)" >> $GITHUB_ENV
            - run: docker images
            - name: Healthcheck verification
              if: ${{  (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              uses: ./.github/actions/ecs-monolith-validate
              with:
                  local-image-id: $DOCKER_IMAGE_ID

            - name: Deploy repo login
              uses: ./.github/actions/docker-ecr-login
              id: deploy-repo
              if: ${{ (startsWith(inputs.branch-name, 'deploy-test/') || inputs.branch-name == 'master' || inputs.branch-name == 'staging' || inputs.branch-name == 'production') && (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: 'us-east-1'
                  ecr-repo-name: ${{ steps.generate-ecr-repo-name.outputs.DEPLOY_ECR_REPO_NAME }}
              env:
                  AWS_SESSION_TOKEN: '' # Clear the session token to prevent it from being used in the next step

            - name: Push image to ECR
              id: push-image
              uses: nick-fields/retry@7152eba30c6575329ac0576536151aca5a72780e # v3.0.0
              if: ${{ (startsWith(inputs.branch-name, 'deploy-test/') || inputs.branch-name == 'master' || inputs.branch-name == 'staging' || inputs.branch-name == 'production') && (!(steps.operation.outputs.operation == 'copy') || steps.copy-image.outcome != 'success') }}
              env:
                  DST_DOCKER_REPO: ${{ steps.deploy-repo.outputs.docker-repo-uri }}
              with:
                  timeout_minutes: 15
                  max_attempts: 3
                  command: |
                      set -ex

                      export DST_DOCKER_TAG="version-${{ inputs.deploy-service-name && format('{0}-', inputs.deploy-service-name) || '' }}${{ inputs.version }}"
                      echo DST_DOCKER_TAG="version-${{ inputs.deploy-service-name && format('{0}-', inputs.deploy-service-name) || '' }}${{ inputs.version }}" >> $GITHUB_OUTPUT
                      SRC_DOCKER_IMAGE="${{ env.DOCKER_IMAGE_ID }}"

                      docker tag "${SRC_DOCKER_IMAGE}" "${DST_DOCKER_REPO}:${DST_DOCKER_TAG}"
                      docker push "${DST_DOCKER_REPO}:${DST_DOCKER_TAG}"
                      PUSHED_IMAGE="${DST_DOCKER_REPO}:${DST_DOCKER_TAG}"

                      DIGESTS=($(docker inspect "${PUSHED_IMAGE}" | jq -r '.[0].RepoDigests[] | split("@")[1]'))
                      DIGESTSARRAY=$(printf '%s\n' "${DIGESTS[@]}" | jq -R . | jq -s . )
                      ESCAPED_DIGESTSARRAY=$(echo $DIGESTSARRAY | jq -sRr @json)
                      echo $ESCAPED_DIGESTSARRAY
                      echo "ESCAPED_DIGESTSARRAY=$ESCAPED_DIGESTSARRAY" >> $GITHUB_OUTPUT

            - name: Run replication status check
              id: run-replication-status
              uses: time-loop/github-actions/dist/ecr-image-replication-status@ecr-image-replication-status+0.1.2
              if: (inputs.branch-name == 'master' || inputs.branch-name == 'staging' || inputs.branch-name == 'production')
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  region: 'us-east-1'
                  digests: ${{ steps.copy-image.outputs.ESCAPED_DIGESTSARRAY || steps.push-image.outputs.ESCAPED_DIGESTSARRAY }}
                  repository_name: ${{ steps.generate-ecr-repo-name.outputs.DEPLOY_ECR_REPO_NAME }}

            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              if: ${{ startsWith(inputs.branch-name, 'deploy-test/') || contains(fromJson('["master", "staging", "production"]'), inputs.branch-name) }}
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}

            - name: Trigger remote dev image copy to ECR Repo AWS account
              if: ${{ startsWith(inputs.branch-name, 'deploy-test/') || contains(fromJson('["master", "staging", "production"]'), inputs.branch-name) }}
              uses: time-loop/trigger-workflow-and-wait@ef3ce75d2cc6e31faa418b5516e80b98e3adb006
              timeout-minutes: 20
              continue-on-error: true
              with:
                  owner: time-loop
                  repo: clickup
                  github_token: ${{ steps.app-token.outputs.token }}
                  workflow_file_name: copy-remote-dev-image.yml
                  ref: ${{ github.ref_name }}
                  wait_interval: 0
                  propagate_failure: false
                  trigger_workflow: true
                  wait_workflow: false
                  client_payload: |
                      {
                        "branch-name": "${{ inputs.branch-name}}",
                        "service-name": "monolith",
                        "repo-name": "${{ steps.generate-ecr-repo-name.outputs.DEPLOY_ECR_REPO_NAME }}",
                        "source-repo": "${{ steps.generate-ecr-repo-name.outputs.ecrRepoSource }}",
                        "source-tag": "version-${{ inputs.deploy-service-name && format('{0}-', inputs.deploy-service-name) || '' }}${{ inputs.version }}",
                        "destination-repo": "${{ steps.generate-ecr-repo-name.outputs.ecrRepoTarget }}",
                        "destination-tag": "version-${{ inputs.deploy-service-name && format('{0}-', inputs.deploy-service-name) || '' }}${{ inputs.version }}",
                        "destination-latest-tag": "${{ steps.generate-ecr-repo-name.outputs.devLatestTag }}"
                      }
