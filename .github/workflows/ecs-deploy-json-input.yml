# Re-usable workflow that can deploy either the monolith or a micro service to ECS.
# It is called from .github/workflows/ecs-monolith-deploy.yml or .github/workflows/ecs-service-publish-image.yml
# This is a modified version of ecs-deploy.yml, which a json file as input

run-name: ${{ inputs.run-name }}
on:
    workflow_dispatch:
        inputs:
            json-file:
                description: 'Json file containing all the actual parameters to run the workflow'
                required: true
                type: string
                default: ''
            github-environment:
                description: 'GitHub environment name that will contain AWS secrets'
                required: true
                type: string
            deploy-push-tags:
                description: 'Tags to push separated by space'
                required: false
                type: string
            deploy-clickup-env:
                description: 'The ClickUp environment to deploy to. E.g. "usQa", "globalStaging", "globalProd", etc.'
                required: true
                type: string
            deploy-service-name:
                description: 'If deploy-type = monorepo, this should be the micro service name that is being deployed'
                required: false
                type: string
            deploy-eks-only:
                description: 'Should we deploy to EKS too'
                default: false
                required: false
                type: boolean
            deploy-to-eks:
                description: 'Should we deploy to EKS too'
                default: false
                required: false
                type: boolean
            deploy-eks-services:
                description: 'A list of EKS Services to Deploy'
                default: '[]'
                required: false
                type: string
            deploy-lambda-shards:
                description: 'The shards to apply the lambda deploy to'
                required: true
                type: string
            run-name:
                description: 'The name of this workflow run'
                required: true
                type: string
env:
    JSON_INPUT: ${{ github.event.inputs.json-file }}

jobs:
    parse-json:
        name: Parse JSON input and set variables
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        outputs:
            INPUT_BUILDCACHE_ECR_REPO_NAME: ${{ steps.parse-json-input.outputs.INPUT_BUILDCACHE_ECR_REPO_NAME }}
            INPUT_BUILDCACHE_PULL_TAG: ${{ steps.parse-json-input.outputs.INPUT_BUILDCACHE_PULL_TAG }}
            INPUT_DEPLOY_AWS_REGION: ${{ steps.parse-json-input.outputs.INPUT_DEPLOY_AWS_REGION }}
            INPUT_DEPLOY_ECR_REPO_NAME: ${{ steps.parse-json-input.outputs.INPUT_DEPLOY_ECR_REPO_NAME }}
            INPUT_DEPLOY_LAMBDAS: ${{ steps.parse-json-input.outputs.INPUT_DEPLOY_LAMBDAS }}
            INPUT_DEPLOY_HOOK_NAME: ${{ steps.parse-json-input.outputs.INPUT_DEPLOY_HOOK_NAME }}
            INPUT_DEPLOY_SKIP_CHECK: ${{ steps.parse-json-input.outputs.INPUT_DEPLOY_SKIP_CHECK }}
            INPUT_DEPLOY_TYPE: ${{ steps.parse-json-input.outputs.INPUT_DEPLOY_TYPE }}
            INPUT_MAX_CHECKS: ${{ steps.parse-json-input.outputs.INPUT_MAX_CHECKS }}
            INPUT_CHECK_DELAY: ${{ steps.parse-json-input.outputs.INPUT_CHECK_DELAY }}
            INPUT_VERSION: ${{ steps.parse-json-input.outputs.INPUT_VERSION }}
            EKS_ONLY_DEPLOY: ${{ steps.set-variables.outputs.EKS_ONLY_DEPLOY }}

        steps:
            - id: parse-json-input
              run: |
                  : # ECR repository name
                  echo "INPUT_BUILDCACHE_ECR_REPO_NAME=${{ fromJson(env.JSON_INPUT).buildcache_ecr_repo_name }}" >> $GITHUB_OUTPUT
                  : # Tag to pull automatically
                  : # not required, no default value
                  if [ ! -z "${{ fromJson(env.JSON_INPUT).buildcache_pull_tag }}" ]; then
                      echo "INPUT_BUILDCACHE_PULL_TAG=${{ fromJson(env.JSON_INPUT).buildcache_pull_tag }}" >> $GITHUB_OUTPUT
                  fi
                  : # Amazon region containing the Lambda to deploy a group of services
                  echo "INPUT_DEPLOY_AWS_REGION=${{ fromJson(env.JSON_INPUT).deploy_aws_region }}" >> $GITHUB_OUTPUT
                  : # ECR repository name
                  echo "INPUT_DEPLOY_ECR_REPO_NAME=${{ fromJson(env.JSON_INPUT).deploy_ecr_repo_name }}" >> $GITHUB_OUTPUT
                  : # A list of region-prefixed lambdas to run separated by space. e.g. us-east-1:lambda-name
                  echo "INPUT_DEPLOY_LAMBDAS=${{ fromJson(env.JSON_INPUT).deploy_lambdas }}" >> $GITHUB_OUTPUT
                  : # If true, will skip the cluster health check after lambda invocation
                  : # not required, with default value
                  if [ ! -z "${{ fromJson(env.JSON_INPUT).deploy_skip_check }}" ]; then
                      echo "INPUT_DEPLOY_SKIP_CHECK=${{ fromJson(env.JSON_INPUT).deploy_skip_check }}" >> $GITHUB_OUTPUT
                  else
                      echo "INPUT_DEPLOY_SKIP_CHECK=false" >> $GITHUB_OUTPUT
                  fi
                  : # Are we deploying the monolith, or the monorepo? e.g. "monolith" or "monorepo"
                  echo "INPUT_DEPLOY_TYPE=${{ fromJson(env.JSON_INPUT).deploy_type }}" >> $GITHUB_OUTPUT
                  : # If deploy-type = monorepo, this should be the micro service deploy name that is being deployed
                  echo "INPUT_DEPLOY_HOOK_NAME=${{ fromJson(env.JSON_INPUT).deploy_hook_name }}" >> $GITHUB_OUTPUT
                  : # Number of attempts to perform a healthcheck post-deployment trigger
                  : # not required, with default value
                  if [ ! -z "${{ fromJson(env.JSON_INPUT).max_checks }}" ]; then
                      echo "INPUT_MAX_CHECKS=${{ fromJson(env.JSON_INPUT).max_checks }}" >> $GITHUB_OUTPUT
                  else
                      echo "INPUT_MAX_CHECKS=25" >> $GITHUB_OUTPUT
                  fi
                  : # For every "max-checks" check, wait this amount of MS.
                  : # not required, with default value
                  if [ ! -z "${{ fromJson(env.JSON_INPUT).check_delay }}" ]; then
                      echo "INPUT_CHECK_DELAY=${{ fromJson(env.JSON_INPUT).check_delay }}" >> $GITHUB_OUTPUT
                  else
                      echo "INPUT_CHECK_DELAY=60000" >> $GITHUB_OUTPUT
                  fi
                  : # The version number that was created for the deploy.
                  echo "INPUT_VERSION=${{ fromJson(env.JSON_INPUT).version }}" >> $GITHUB_OUTPUT
            - id: set-variables
              run: |
                  echo "EKS_ONLY_DEPLOY=${{ inputs.deploy-eks-only }}" >> $GITHUB_OUTPUT
    start-deploy:
        name: Copy Images from Buildcache to Deployment ECR Repo
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-1c-2g
        needs: [parse-json]
        environment: ${{ inputs.github-environment }}
        permissions:
            id-token: write
            contents: read
            actions: read
        outputs:
            did-image-push: ${{ steps.copy-manifest.outputs.did-image-push }}
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
            - name: Buildcache Repo Login
              uses: ./.github/actions/docker-ecr-login
              id: buildcache
              with:
                  aws-access-key-id: ${{ secrets.ECR_BUILD_CACHE_AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.ECR_BUILD_CACHE_AWS_SECRET_ACCESS_KEY }}
                  aws-region: ${{ secrets.ECR_BUILD_CACHE_AWS_REGION }}
                  ecr-repo-name: ${{ needs.parse-json.outputs.INPUT_BUILDCACHE_ECR_REPO_NAME }}
            - name: Deploy Repo Login
              uses: ./.github/actions/docker-ecr-login
              id: deploy-repo
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ${{ needs.parse-json.outputs.INPUT_DEPLOY_AWS_REGION }}
                  ecr-repo-name: ${{ needs.parse-json.outputs.INPUT_DEPLOY_ECR_REPO_NAME }}
            - name: Copy Manifest contents
              id: copy-manifest
              uses: nick-fields/retry@7152eba30c6575329ac0576536151aca5a72780e # v3.0.0
              env:
                  SRC_DOCKER_REPO: ${{ steps.buildcache.outputs.docker-repo-uri }}
                  DST_DOCKER_REPO: ${{ steps.deploy-repo.outputs.docker-repo-uri }}
                  # Note: These vars can not be resolved because they use an env var inside the value
                  #   SRC_DOCKER_TAG: ${{ inputs.buildcache-pull-tag }}
                  #   DST_DOCKER_TAGS: ${{ inputs.deploy-push-tags }} version-${{ inputs.deploy-service-name && format('{0}-', inputs.deploy-service-name) || '' }}${{ inputs.version || steps.version.outputs.version }}
              with:
                  timeout_minutes: 15
                  max_attempts: 3
                  command: |
                      set -ex

                      export SRC_DOCKER_TAG="${{ needs.parse-json.outputs.INPUT_BUILDCACHE_PULL_TAG }}"
                      export DST_DOCKER_TAGS="${{ inputs.deploy-push-tags }} version-${{ inputs.deploy-service-name && format('{0}-', inputs.deploy-service-name) || '' }}${{ needs.parse-json.outputs.INPUT_VERSION || steps.version.outputs.version }}"

                      SRC_DOCKER_IMAGE="${SRC_DOCKER_REPO}:${SRC_DOCKER_TAG}"
                      docker pull --platform=linux/arm64 "${SRC_DOCKER_IMAGE}"

                      PUSHED_IMAGE=""
                      for DST_DOCKER_TAG in ${DST_DOCKER_TAGS}; do
                          docker tag "${SRC_DOCKER_IMAGE}" "${DST_DOCKER_REPO}:${DST_DOCKER_TAG}"
                          docker push "${DST_DOCKER_REPO}:${DST_DOCKER_TAG}"
                          PUSHED_IMAGE="${DST_DOCKER_REPO}:${DST_DOCKER_TAG}"
                      done

                      # Remove the source image to avoid duplicate hashes
                      docker image rm "${SRC_DOCKER_IMAGE}"

                      DIGESTS=($(docker inspect "${PUSHED_IMAGE}" | jq -r '.[0].RepoDigests[] | split("@")[1]'))
                      DIGESTSARRAY=$(printf '%s\n' "${DIGESTS[@]}" | jq -R . | jq -s . )
                      ESCAPED_DIGESTSARRAY=$(echo $DIGESTSARRAY | jq -sRr @json)
                      echo $ESCAPED_DIGESTSARRAY
                      echo "ESCAPED_DIGESTSARRAY=$ESCAPED_DIGESTSARRAY" >> $GITHUB_OUTPUT

                      echo "did-image-push=true" >> $GITHUB_OUTPUT

            - name: Run Replication Status Check
              id: run-replication-status
              uses: time-loop/github-actions/dist/ecr-image-replication-status@ecr-image-replication-status+0.1.2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  region: 'us-east-1'
                  digests: ${{ steps.copy-manifest.outputs.ESCAPED_DIGESTSARRAY }}
                  repository_name: ${{ needs.parse-json.outputs.INPUT_DEPLOY_ECR_REPO_NAME }}

    ecs-deploy:
        name: Deploy to ECS
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-4c-8g
        needs: [parse-json, start-deploy]
        if: ${{ needs.parse-json.outputs.EKS_ONLY_DEPLOY == 'false' && !(inputs.deploy-clickup-env == 'usQa' && inputs.deploy-service-name == 'user-data-replicator') }}
        environment: ${{ inputs.github-environment }}
        permissions:
            id-token: write
            contents: read
            actions: read
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  node-version-file: '.nvmrc'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
            # Lambda names are capped at 64 characters.  Currently, the deploy hook lambdas for ECS are named using a
            # number of components (e.g. region, environment, repo name, repo tag) that could cause the name to exceed
            # 64 characters.  In those cases, we'll truncate the lambda name on the CDK side.
            # Here, we ensure that each of the arguments passed in as the deploy-lambdas input is also <= 64 chars.
            - name: Enforce max Lambda name length
              id: enforce-lambda-name-lengths
              shell: bash
              run: |
                  LAMBDA_NAMES=$(
                    echo "${{ needs.parse-json.outputs.INPUT_DEPLOY_LAMBDAS }}" |
                    perl -lae '@lambdas=(); foreach(@F) { ($region, $fn) = split(":",$_); push(@lambdas, $region.":". substr($fn,0,64)) if index(${{ vars.ECS_DEPLOY_IGNORE_REGIONS }}, "[${region}]") == -1; } print join(" ",@lambdas);'
                  )
                  echo "lambda_names=$LAMBDA_NAMES" >> $GITHUB_OUTPUT
            - name: Run Lambdas
              uses: ./.github/actions/aws-lambda-run
              id: 'run-lambda'
              if: ${{ !fromJSON(vars.DIRECT_DEPLOY_MAP)[inputs.deploy-clickup-env] }}
              timeout-minutes: 15
              env:
                  managed-payload: '{"shards": ${{ inputs.deploy-lambda-shards }},"managed": "partial","imageTag": "version-${{ inputs.deploy-service-name && format(''{0}-'', inputs.deploy-service-name) || '''' }}${{ needs.parse-json.outputs.INPUT_VERSION || steps.version.outputs.version }}"}'
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  lambdas: '${{ steps.enforce-lambda-name-lengths.outputs.lambda_names }}'
                  payload: ${{ env.managed-payload }}
                  lambda-read-timeout: '1200'
            - name: Run Deploy
              uses: ./.github/actions/ecs-deploy
              id: 'run-ecs-deploy'
              if: ${{ fromJSON(vars.DIRECT_DEPLOY_MAP)[inputs.deploy-clickup-env] }}
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  deploy-lambda-shards: ${{ inputs.deploy-lambda-shards }}
                  deploy-clickup-env: ${{ inputs.deploy-clickup-env }}
                  deploy-type: ${{ needs.parse-json.outputs.INPUT_DEPLOY_TYPE }}
                  deploy-service-name: ${{ inputs.deploy-service-name }}
                  deploy-hook-name: ${{ needs.parse-json.outputs.INPUT_DEPLOY_HOOK_NAME }}
                  deploy-image-tag: version-${{ inputs.deploy-service-name && format('{0}-', inputs.deploy-service-name) || '' }}${{ needs.parse-json.outputs.INPUT_VERSION || steps.version.outputs.version }}
            - name: Cluster health checks
              shell: bash
              if: ${{ (success() || failure()) && !cancelled() }}
              id: cluster-health-checks
              env:
                  FORCE_COLOR: 3 # fix terminal color output
                  ECS_DEPLOY_IGNORE_CLUSTERS: ${{ vars.ECS_HEALTHCHECK_IGNORE_CLUSTERS }}
                  ECS_DEPLOY_CANARY_SHARDS: ${{ inputs.deploy-lambda-shards }}
              run: |
                  set +e  # revert the default `set -e` and prevent script from exiting immediately on fail

                  AWS_ACCESS_KEY_ID="${{ secrets.AWS_ACCESS_KEY_ID }}" \
                  AWS_SECRET_ACCESS_KEY="${{ secrets.AWS_SECRET_ACCESS_KEY }}" \
                  npx ts-node tools/aws-ecs/cluster-health-check/src/index.ts \
                  --deploy-aws-region=${{ needs.parse-json.outputs.INPUT_DEPLOY_AWS_REGION }} \
                  --deploy-clickup-env=${{ inputs.deploy-clickup-env }} \
                  --deploy-type=${{ needs.parse-json.outputs.INPUT_DEPLOY_TYPE }} \
                  --deploy-service-name=${{ inputs.deploy-service-name }} \
                  --deploy-ecr-region=${{ needs.parse-json.outputs.INPUT_DEPLOY_AWS_REGION }} \
                  --deploy-ecr-repo-name=${{ needs.parse-json.outputs.INPUT_DEPLOY_ECR_REPO_NAME }} \
                  --expected-version=${{ needs.parse-json.outputs.INPUT_VERSION }} \
                  --skip-health-check=${{ needs.parse-json.outputs.INPUT_DEPLOY_SKIP_CHECK }} \
                  --max-checks=${{ needs.parse-json.outputs.INPUT_MAX_CHECKS }} \
                  --check-delay=${{ needs.parse-json.outputs.INPUT_CHECK_DELAY }} \
                  --write-failed-results-to-file=true

                  # Check if failed, and if so, continue to reading the results file
                  test $? -ne 0 || exit 0

                  RESULTS_FILE_NAME=$(test "${{ needs.parse-json.outputs.INPUT_DEPLOY_TYPE }}" = "monorepo" && echo "${{ inputs.deploy-service-name }}" || echo "monolith")
                  RESULTS_FILE_PATH="/tmp/cluster-health-check/${RESULTS_FILE_NAME}.json"
                  SHOULD_RETRY=$(test -f "${RESULTS_FILE_PATH}" && echo 'true' || echo 'false')
                  echo "will-issue-retry=${SHOULD_RETRY}" >> $GITHUB_OUTPUT
                  test "${SHOULD_RETRY}" = 'false' && echo 'No results file found, therefore no retry expected.' && exit 1 || echo "Setting up retry by reading results file ${RESULTS_FILE_PATH}"

                  CLUSTERS_TO_RETRY=$(jq '. | tostring' "${RESULTS_FILE_PATH}")
                  echo "retry-clusters=${CLUSTERS_TO_RETRY}" >> $GITHUB_OUTPUT
            - name: Run Deploy Retry
              uses: ./.github/actions/ecs-deploy
              id: 'run-ecs-deploy-retry'
              if: ${{ steps.cluster-health-checks.outputs.will-issue-retry == 'true' }}
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  deploy-lambda-shards: ${{ inputs.deploy-lambda-shards }}
                  deploy-clickup-env: ${{ inputs.deploy-clickup-env }}
                  deploy-type: ${{ needs.parse-json.outputs.INPUT_DEPLOY_TYPE }}
                  deploy-clusters: ${{ steps.cluster-health-checks.outputs.retry-clusters }}
                  deploy-service-name: ${{ inputs.deploy-service-name }}
                  deploy-hook-name: ${{ needs.parse-json.outputs.INPUT_DEPLOY_HOOK_NAME }}
                  deploy-image-tag: version-${{ inputs.deploy-service-name && format('{0}-', inputs.deploy-service-name) || '' }}${{ needs.parse-json.outputs.INPUT_VERSION || steps.version.outputs.version }}
            - name: Cluster retry health checks
              shell: bash
              if: ${{ (success() || failure()) && !cancelled() && steps.cluster-health-checks.outputs.will-issue-retry == 'true' }}
              id: cluster-retry-health-checks
              env:
                  FORCE_COLOR: 3 # fix terminal color output
                  ECS_DEPLOY_IGNORE_CLUSTERS: ${{ vars.ECS_HEALTHCHECK_IGNORE_CLUSTERS }}
                  ECS_DEPLOY_CANARY_SHARDS: ${{ inputs.deploy-lambda-shards }}
              run: |
                  AWS_ACCESS_KEY_ID="${{ secrets.AWS_ACCESS_KEY_ID }}" \
                  AWS_SECRET_ACCESS_KEY="${{ secrets.AWS_SECRET_ACCESS_KEY }}" \
                  npx ts-node tools/aws-ecs/cluster-health-check/src/index.ts \
                  --deploy-aws-region=${{ needs.parse-json.outputs.INPUT_DEPLOY_AWS_REGION }} \
                  --deploy-clickup-env=${{ inputs.deploy-clickup-env }} \
                  --deploy-type=${{ needs.parse-json.outputs.INPUT_DEPLOY_TYPE }} \
                  --deploy-service-name=${{ inputs.deploy-service-name }} \
                  --deploy-ecr-region=${{ needs.parse-json.outputs.INPUT_DEPLOY_AWS_REGION }} \
                  --deploy-ecr-repo-name=${{ needs.parse-json.outputs.INPUT_DEPLOY_ECR_REPO_NAME }} \
                  --expected-version=${{ needs.parse-json.outputs.INPUT_VERSION }} \
                  --skip-health-check=${{ needs.parse-json.outputs.INPUT_DEPLOY_SKIP_CHECK }} \
                  --max-checks=${{ needs.parse-json.outputs.INPUT_MAX_CHECKS }} \
                  --check-delay=${{ needs.parse-json.outputs.INPUT_CHECK_DELAY }}

    harness-eks-deploy:
        name: Deploy EKS
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-4c-8g
        environment: ${{ inputs.github-environment }}
        needs: [start-deploy, parse-json]
        if: ${{ (success() || failure()) && needs.start-deploy.outputs.did-image-push && inputs.deploy-to-eks && inputs.deploy-eks-services != '[]' }}
        permissions:
            contents: read
            actions: read
        strategy:
            fail-fast: false
            matrix:
                eks-service: ${{ fromJson(inputs.deploy-eks-services) }}
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
            - name: Get Harness Service Identifier
              uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
              id: service-ident
              env:
                  SERVICE_NAME: ${{ matrix.eks-service }}
              with:
                  script: return process.env.SERVICE_NAME.replaceAll("-", "_")
                  result-encoding: string
            - id: parse-canary-shard-string
              run: |
                  echo 'canary-array=${{ fromJSON(toJSON(inputs.deploy-lambda-shards)) }}'
                  echo 'canary-array=${{ fromJSON(toJSON(inputs.deploy-lambda-shards)) }}' >> $GITHUB_OUTPUT
            - id: set-shards
              run: |
                  if [ "${{ vars.EKS_CANARY_DEPLOY_FF }}" = 'false' ]; then
                    echo 'EKS_CANARY_DEPLOY_FF is false, setting shards to ALL'
                    echo "shards=ALL" >> $GITHUB_OUTPUT
                    exit 0
                  fi

                  the_shard=$(echo ${{ fromJSON(steps.parse-canary-shard-string.outputs.canary-array)[0] }} )
                  test -n "${the_shard}" || the_shard='ALL'
                  echo $the_shard
                  echo "shards=${the_shard}" >> $GITHUB_OUTPUT
            - name: Harness EKS Deployment Trigger
              env:
                  trigger_env_name: ${{ fromJSON('{"usQa":"qa","usStaging":"staging","globalProd":"prod"}')[inputs.deploy-clickup-env] }}
                  docker_tag: version-${{ inputs.deploy-service-name && format('{0}-', inputs.deploy-service-name) || '' }}${{ needs.parse-json.outputs.INPUT_VERSION || steps.version.outputs.version }}
                  env_name: ${{ inputs.deploy-clickup-env }}
                  service_ident: ${{ steps.service-ident.outputs.result }}
              uses: fjogeleit/http-request-action@23ad54bcd1178fcff6a0d17538fa09de3a7f0a4d # v1.16.4
              id: harness-eks-trigger
              with:
                  url: https://app.harness.io/gateway/pipeline/api/webhook/custom/v2?accountIdentifier=NYVU0wI4R0ijpWK5Gyl5pQ&orgIdentifier=ClickUp&projectIdentifier=clickupbackendeks&pipelineIdentifier=${{ env.service_ident }}_deploy&triggerIdentifier=${{ env.service_ident }}_${{ env.trigger_env_name }}_trigger
                  method: 'POST'
                  customHeaders: '{"Content-Type": "application/json", "X-Api-Key": "${{ secrets.HARNESS_API_TOKEN }}"}'
                  data: '{"image_tag": "${{ env.docker_tag }}", "env_name": "${{ env.env_name }}", "shards": "${{ steps.set-shards.outputs.shards }}"}'
                  timeout: 10000
                  retry: 3
                  retryWait: 5000
            - name: Harness EKS Deployment Response
              run: echo '${{ steps.harness-eks-trigger.outputs.response }}' | jq .
            - name: Get Harness Pipeline Execution URL
              shell: bash
              run: |
                  echo Event ${{ fromJSON(steps.harness-eks-trigger.outputs.response).data.eventCorrelationId }}
                  echo Pipeline ${{ fromJSON(steps.harness-eks-trigger.outputs.response).data.uiUrl }}
            - name: Harness Pipeline Wait
              if: ${{ inputs.deploy-to-eks }}
              id: harness-pipeline-wait
              uses: ./.github/actions/harness-pipeline-wait
              with:
                  harness-api-token: ${{ secrets.HARNESS_API_TOKEN }}
                  harness-trigger-event-id: ${{ fromJSON(steps.harness-eks-trigger.outputs.response).data.eventCorrelationId }}
                  service-env: ${{ fromJSON('{"usQa":"qa","usStaging":"staging","globalProd":"prod"}')[inputs.deploy-clickup-env] }}
                  service-name: ${{ inputs.deploy-service-name }}

    post-deploy:
        name: Post deploy alerts
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        environment: ${{ inputs.github-environment }}
        needs: [ecs-deploy, harness-eks-deploy]
        permissions:
            contents: read
            actions: read
        steps:
            - name: Alert slack on error
              uses: rtCamp/action-slack-notify@c33737706dea87cd7784c687dadc9adf1be59990 # v2.3.2
              if: ${{ failure() }}
              env:
                  SLACK_WEBHOOK: ${{ secrets.ECS_ALERTS_SLACK_WEBHOOK }}
                  SLACK_MESSAGE: |
                      ECS post deploy health check for the *${{ inputs.deploy-service-name || 'monolith' }}* failed.

                      Something is probably wrong and may need manual intervention in AWS to resolve.

                      Please see the run output for more details: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}
                  SLACK_ICON_EMOJI: ':robot:'
                  SLACK_USERNAME: 'ECS cluster health bot'
                  SLACK_COLOR: 'danger'
                  SLACK_FOOTER: ''
            - name: Page Eng Prod on error
              # Only page Eng Prod if the environment is staging or prod
              if: ${{ failure() && contains(fromJson('["usStaging", "globalProd"]'), inputs.deploy-clickup-env) }}
              # https://github.com/marketplace/actions/pagerduty-alert-remastered
              uses: miparnisari/action-pagerduty-alert@a6a738b712efa0e1a45b1b796c62f60fc30b5d99 # 0.3.2
              with:
                  pagerduty-integration-key: ${{ secrets.PAGERDUTY_INTEGRATION_KEY }}
                  pagerduty-dedup-key: ${{ inputs.deploy-clickup-env }}-${{ github.sha }}
                  incident-summary: ECS healthcheck failed for ${{ inputs.deploy-service-name || 'monolith' }} in environment ${{ inputs.deploy-clickup-env }}
                  incident-environment: ${{ inputs.deploy-clickup-env }}
