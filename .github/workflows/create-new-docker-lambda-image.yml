name: Create new docker lambda image via Github Actions

on:
    workflow_dispatch:
        inputs:
            name:
                description: 'Name of the docker lambda image, should include lambda- prefix'
                required: true
            language:
                description: 'Language of the docker lambda image'
                required: true
                type: choice
                options:
                    - nodejs
                    - python
            domain:
                description: 'Domain of the service'
                required: true
            squad:
                description: 'Squad responsible for the service'
                required: true
            task_id:
                description: 'Task Id (CLK-1234)'
                required: true

permissions: write-all

jobs:
    create-new-docker-lambda-image:
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-2c-4g
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  filter: tree:0
            - name: Install pytest, ruff, and uv
              run: |
                  pipx install pytest && pipx install ruff && pipx install uv
                  echo $HOME/.local/bin >> $GITHUB_PATH
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              with:
                  package-manager: pnpm
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
            - name: Create new service
              env:
                  NEW_IMAGE_NAME: ${{ github.event.inputs.name }}
                  NEW_IMAGE_DOMAIN: ${{ github.event.inputs.domain }}
                  NEW_IMAGE_SQUAD: ${{ github.event.inputs.squad }}
                  NEW_IMAGE_LANGUAGE: ${{ github.event.inputs.language }}
              run: |
                  pnpm nx generate clickup-lambda-${{ env.NEW_IMAGE_LANGUAGE }} "${{ env.NEW_IMAGE_NAME }}" \
                    --domain="${{ env.NEW_IMAGE_DOMAIN }}" \
                    --squad="${{ env.NEW_IMAGE_SQUAD }}"
            - name: Lock dependencies for Python
              if: ${{ github.event.inputs.language == 'python' }}
              env:
                  NEW_IMAGE_NAME: ${{ github.event.inputs.name }}
              run: |
                  pnpm nx "${{ env.NEW_IMAGE_NAME }} lock"
            - name: Lint new service
              env:
                  NEW_IMAGE_NAME: ${{ github.event.inputs.name }}
              run: |
                  pnpm nx lint "${{ env.NEW_IMAGE_NAME }}" --fix
            - name: Set git identity
              run: |-
                  git config user.name "github-actions"
                  git config user.email "<EMAIL>"
            # NOT authing with AWS or ECR since no gateway-client generation required
            - name: Create PR
              id: create-pr
              env:
                  INPUT_TASK_ID: ${{ github.event.inputs.task_id }}
                  NEW_IMAGE_NAME: ${{ github.event.inputs.name }}
                  NEW_IMAGE_SQUAD: ${{ github.event.inputs.squad }}
              uses: peter-evans/create-pull-request@67ccf781d68cd99b580ae25a5c18a1cc84ffff1f # v7.0.6
              with:
                  token: ${{ secrets.PROJEN_GITHUB_TOKEN }}
                  commit-message: 'feat(bot): add new docker lambda image ${{ env.NEW_IMAGE_NAME }} [${{ env.INPUT_TASK_ID }}]'
                  title: 'feat(bot): add new docker lambda image ${{ env.NEW_IMAGE_NAME }} [${{ env.INPUT_TASK_ID }}]'
                  body: 'Adding new docker lambda image `${{ env.NEW_IMAGE_NAME }}` by squad @time-loop/${{ env.NEW_IMAGE_SQUAD }}.'
                  branch: '${{ env.INPUT_TASK_ID }}/create-new-docker-lambda-image-${{ env.NEW_IMAGE_NAME }}'
                  labels: generated-new-docker-lambda-image
                  add-paths: |
                      .github/CODEOWNERS
                      apps/*
                      codecov.yml
                      libs/*
                      tsconfig.base.json
                  base: master
                  author: github-actions <<EMAIL>>
                  committer: github-actions <<EMAIL>>
            - name: Enable Pull Request Automerge
              if: steps.create-pr.outputs.pull-request-operation == 'created'
              uses: peter-evans/enable-pull-request-automerge@a660677d5469627102a1c1e11409dd063606628d # v3.0.0
              with:
                  token: ${{ secrets.PROJEN_GITHUB_TOKEN }}
                  pull-request-number: ${{ steps.create-pr.outputs.pull-request-number }}
                  merge-method: squash
