name: Copy container images

on:
    workflow_dispatch:
        inputs:
            repo_type:
                type: choice
                description: 'Monolith or Monorepo'
                required: true
                options:
                    - monolith
                    - monorepo
            target_branch:
                type: choice
                description: 'Your target branch. Source will be the previous lower environment'
                required: true
                options:
                    - staging
                    - production
            input_sha:
                type: string
                required: true
                description: 'The git sha of the images to copy'
            tag_copied_img_with_version:
                type: string
                required: true
                description: 'The version to put into the target repo'
            deploy-service-name:
                type: string
                required: false
                description: 'The service name to put into the target repo - usually for monorepo services'
    workflow_call:
        inputs:
            repo_type:
                type: string
                required: true
            target_branch:
                type: string
                required: true
            input_sha:
                type: string
                required: true
                description: 'The git sha of the images to copy'
            tag_copied_img_with_version:
                type: string
                required: true
                description: 'The version to put into the target repo'
            deploy-service-name:
                type: string
                required: false
                description: 'The service name to put into the target repo - usually for monorepo services'

jobs:
    setup-variables:
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        outputs:
            source_repo: ${{ steps.set-vars.outputs.source_repo }}
            target_repo: ${{ steps.set-vars.outputs.target_repo }}
            source_env: ${{ steps.set-vars.outputs.source_env }}
            target_env: ${{ steps.set-vars.outputs.target_env }}
            image_sha: ${{ steps.set-vars.outputs.image_sha }}
        steps:
            - name: Set variables
              id: set-vars
              uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
              env:
                  SHA: ${{ inputs.input_sha }}
                  TARGET_BRANCH: ${{ inputs.target_branch }}
                  REPO_TYPE: ${{ inputs.repo_type }}
              with:
                  script: |
                      // Get inputs from environment variables instead of 'with'
                      core.setOutput("image_sha", `git-sha-${process.env.SHA}`);
                      const targetBranch = process.env.TARGET_BRANCH;
                      const repoType = process.env.REPO_TYPE;

                      let sourceRepo, targetRepo, sourceEnv, targetEnv, message;

                      switch (targetBranch) {
                      case "staging":
                          sourceRepo = `187416477703.dkr.ecr.us-east-1.amazonaws.com/${repoType}-deploy-us-qa`;
                          targetRepo = `424044212339.dkr.ecr.us-east-1.amazonaws.com/${repoType}-deploy-us-staging`;
                          sourceEnv = "ecs-deploy-user-usqa";
                          targetEnv = "ecs-deploy-user-usstaging";
                          message = "Setting source to QA and target to Staging";
                          break;

                      case "production":
                          sourceRepo = `424044212339.dkr.ecr.us-east-1.amazonaws.com/${repoType}-deploy-us-staging`;
                          targetRepo = `514308641592.dkr.ecr.us-east-1.amazonaws.com/${repoType}-deploy-global-prod`;
                          sourceEnv = "ecs-deploy-user-usstaging";
                          targetEnv = "ecs-deploy-user-globalprod";
                          message = "Setting source to Staging and target to Production";
                          break;

                      default:
                          core.setFailed(`Error: Invalid branch specified: ${targetBranch}`);
                          process.exit(1);
                      }

                      core.setOutput("source_repo", sourceRepo);
                      core.setOutput("target_repo", targetRepo);
                      core.setOutput("source_env", sourceEnv);
                      core.setOutput("target_env", targetEnv);

                      console.log(message);
            - name: Debug Outputs
              run: |
                  echo "Source Repo: ${{ steps.set-vars.outputs.source_repo }}"
                  echo "Target Repo: ${{ steps.set-vars.outputs.target_repo }}"
                  echo "Source Env: ${{ steps.set-vars.outputs.source_env }}"
                  echo "Target Env: ${{ steps.set-vars.outputs.target_env }}"
                  echo "Image SHA: ${{ steps.set-vars.outputs.image_sha }}"

    check-images:
        needs: setup-variables
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        environment: ${{ needs.setup-variables.outputs.source_env }}
        outputs:
            image_exists: ${{ steps.check-images.outputs.image_exists }}
        steps:
            - name: Check Image Existence
              id: check-images
              run: |
                  aws configure set aws_access_key_id ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws configure set aws_secret_access_key ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws configure set region us-east-1

                  if aws ecr describe-images \
                    --repository-name $(echo "${{ needs.setup-variables.outputs.source_repo }}" | cut -d'/' -f2) \
                    --image-ids imageTag=${{ needs.setup-variables.outputs.image_sha }} \
                    --registry-id $(echo "${{ needs.setup-variables.outputs.source_repo }}" | cut -d'.' -f1) \
                    --query 'imageDetails[0].imageDigest'\
                    --output text; then
                    echo "Image exists"
                    echo "image_exists=true" >> $GITHUB_OUTPUT
                  else
                    echo "Image does not exist"
                    echo "image_exists=false" >> $GITHUB_OUTPUT
                  fi

    get-source-ecr-credentials:
        needs: setup-variables
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        environment: ${{ needs.setup-variables.outputs.source_env }}
        outputs:
            ecr_auth_token: ${{ steps.save_creds.outputs.ecr_auth_token }}
        steps:
            - name: Save Source AWS Credentials
              id: save_creds
              run: |
                  aws configure set aws_access_key_id ${{ secrets.AWS_ACCESS_KEY_ID }} 
                  aws configure set aws_secret_access_key ${{ secrets.AWS_SECRET_ACCESS_KEY }}

                  ecr_auth=$(aws ecr get-login-password --region us-east-1 | base64 -w 0)
                  echo "ecr_auth_token=${ecr_auth}" >> $GITHUB_OUTPUT

    get-target-ecr-credentials:
        needs: setup-variables
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        environment: ${{ needs.setup-variables.outputs.target_env }}
        outputs:
            ecr_auth_token: ${{ steps.save_creds.outputs.ecr_auth_token }}
        steps:
            - name: Save Target AWS Credentials
              id: save_creds
              run: |
                  aws configure set aws_access_key_id ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws configure set aws_secret_access_key ${{ secrets.AWS_SECRET_ACCESS_KEY }}

                  ecr_auth=$(aws ecr get-login-password --region us-east-1 | base64 -w 0)
                  echo "ecr_auth_token=${ecr_auth}" >> $GITHUB_OUTPUT

    copy-container-images:
        if: needs.check-images.outputs.image_exists == 'true'
        needs: [setup-variables, get-source-ecr-credentials, get-target-ecr-credentials, check-images]
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-2c-4g
        container:
            #tag name: v1.17.0-immutable
            image: quay.io/skopeo/stable@sha256:4c3fe9e9eddf1309dbf07a0cb168e839db5e5757271c3ce6c996e3e5e99d002e
        steps:
            - name: Copy Image
              run: |
                  # Copy operation in action
                  source_auth_token=$(echo -n "${{ needs.get-source-ecr-credentials.outputs.ecr_auth_token }}" | base64 -d)
                  target_auth_token=$(echo -n "${{ needs.get-target-ecr-credentials.outputs.ecr_auth_token }}" | base64 -d)

                  skopeo copy \
                    --src-creds "AWS:${source_auth_token}" \
                    --dest-creds "AWS:${target_auth_token}" \
                    --preserve-digests \
                    --retry-times 3 \
                    --quiet \
                    docker://${{ needs.setup-variables.outputs.source_repo }}:${{ needs.setup-variables.outputs.image_sha }} \
                    docker://${{ needs.setup-variables.outputs.target_repo }}:version-${{ inputs.deploy-service-name && format('{0}-', inputs.deploy-service-name) || '' }}${{ inputs.tag_copied_img_with_version }}
