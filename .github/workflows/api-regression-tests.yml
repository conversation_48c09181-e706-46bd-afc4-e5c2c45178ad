# This workflow runs the api regression suite every night on qa -> staging PRs
name: 'API Regression suite tests'

on:
    workflow_dispatch:
        inputs:
            shard-list:
                description: 'List of shards comma separated'
                required: false
                default: ''
            deploy-env:
                description: 'Deploy Environment to test'
                required: false
                default: 'usQa'
            test-tags:
                description: 'Test tags to run'
                required: false
                default: ''
    schedule:
        # Server time is UTC, so we'll want to run this every day at 1:00 UTC (6:00 PM PST)
        - cron: '0 1 * * MON-FRI'

permissions:
    contents: read
    actions: read

jobs:
    set-defaults:
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        outputs:
            matrix: ${{ steps.set-shards.outputs.matrix }}
        steps:
            - name: Set shard list
              id: set-shards
              uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
              with:
                  script: |
                      const defaultShards = 'qa-us-east-2-2,qa-us-east-2-1,qa-us-west-2-6,staging-us-west-2-1,prod-us-west-2-2,prod-us-west-2-3,prod-us-east-2-1,prod-us-east-2-2,prod-eu-west-1-2,prod-eu-west-1-3,prod-ap-southeast-2-2,prod-ap-southeast-1-1';
                      const shardList = ${{ toJson(inputs.shard-list) || 'undefined' }} || defaultShards;
                      let shards = shardList.split(',');
                      const deployEnvMap = {
                          usQa: 'qa',
                          usStaging: 'staging',
                          globalProd: 'prod',
                      };
                      const deployEnv = ${{ toJson(inputs.deploy-env) }};
                      if (deployEnv && deployEnv != '') {
                          const env = deployEnvMap[deployEnv];
                          shards = shards.filter(shard => shard.startsWith(env));
                      }
                      console.log('Shards:', shards);
                      const matrixIncludes = shards.map((shard) => ({shard: shard}));
                      const matrix = {include: matrixIncludes};
                      const matrixJson = JSON.stringify(matrix);
                      core.setOutput('matrix', matrixJson);

    run-api-tests:
        needs: [set-defaults]
        strategy:
            fail-fast: false
            matrix: ${{ fromJson(needs.set-defaults.outputs.matrix) }}
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-amd64-4c-16g
        steps:
            - name: Checkout branch
              uses: actions/checkout@cbb722410c2e876e24abbe8de2cc27693e501dcb
            - name: Setup node
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
            - name: Run API tests
              env:
                  SHARD: ${{ matrix.shard }}
                  API_AUTOMATION_PWD: ${{ secrets.API_AUTOMATION_PWD }}
                  TEST_TAGS: ${{ inputs.test-tags }}
                  CLICKUP_API_KEY: ${{ secrets.CLICKUP_API_TOKEN }}
                  TESTRAIL_HOST: ${{ vars.TESTRAIL_HOST }}
                  TESTRAIL_USERNAME: ${{ vars.TESTRAIL_USERNAME }}
                  TESTRAIL_API_KEY: ${{ secrets.TESTRAIL_API_KEY }}
                  TESTRAIL_PROJECT_ID: ${{ vars.TESTRAIL_PROJECT_ID }}
                  CI: true
              run: |
                  if [ -n "$TEST_TAGS" ]; then
                       pnpm nx api-testing testing-api-context-jest -t=${{ env.TEST_TAGS }}
                   else
                       pnpm nx api-testing testing-api-context-jest
                   fi
