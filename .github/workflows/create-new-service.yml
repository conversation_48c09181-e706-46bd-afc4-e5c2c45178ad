name: Create new backend service via Github Actions

on:
    workflow_dispatch:
        inputs:
            name:
                description: 'Name of the service'
                required: true
            service_type:
                description: 'Type of the service to create'
                required: false
                type: choice
                options:
                    - kafka-consumer
                    - service
                    - temporal-worker
                default: service
            domain:
                description: 'Domain of the service'
                required: true
            squad:
                description: 'Squad responsible for the service'
                required: true
            task_id:
                description: 'Task Id (CLK-1234)'
                required: true

permissions: write-all

jobs:
    create-new-service:
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-2c-4g
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup Java
              uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
              with:
                  distribution: 'corretto'
                  java-version: '21'
            - name: Setup Gradle
              uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              with:
                  package-manager: pnpm
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
            - name: Create new service
              env:
                  NEW_SERVICE_NAME: ${{ github.event.inputs.name }}
                  NEW_SERVICE_DOMAIN: ${{ github.event.inputs.domain }}
                  NEW_SERVICE_SQUAD: ${{ github.event.inputs.squad }}
                  NEW_SERVICE_TYPE: ${{ github.event.inputs.service_type }}
              run: |
                  pnpm nx generate clickup-${{ env.NEW_SERVICE_TYPE }} "${{ env.NEW_SERVICE_NAME }}" \
                    --domain="${{ env.NEW_SERVICE_DOMAIN }}" \
                    --squad="${{ env.NEW_SERVICE_SQUAD }}"
            - name: Lint new service
              env:
                  NEW_SERVICE_NAME: ${{ github.event.inputs.name }}
              run: |
                  pnpm nx lint "${{ env.NEW_SERVICE_NAME }}" --fix
            - name: Set git identity
              run: |-
                  git config user.name "github-actions"
                  git config user.email "<EMAIL>"
            - name: Configure AWS credentials # we need to auth with aws since gateway-client generation requires ECR registry image
              uses: aws-actions/configure-aws-credentials@e3dd6a429d7300a6a4c196c26e071d42e0343502 # v4.0.2
              with:
                  role-to-assume: arn:aws:iam::158160757288:role/gha-ecr-push
                  role-session-name: gha
                  aws-region: us-east-2
            - name: Login to Amazon ECR
              uses: aws-actions/amazon-ecr-login@062b18b96a7aff071d4dc91bc00c4c1a7945b076 # v2.0.1
            - name: Create PR
              id: create-pr
              env:
                  INPUT_TASK_ID: ${{ github.event.inputs.task_id }}
                  NEW_SERVICE_NAME: ${{ github.event.inputs.name }}
                  NEW_SERVICE_SQUAD: ${{ github.event.inputs.squad }}
              uses: peter-evans/create-pull-request@67ccf781d68cd99b580ae25a5c18a1cc84ffff1f # v7.0.6
              with:
                  token: ${{ secrets.PROJEN_GITHUB_TOKEN }}
                  commit-message: 'feat(bot): add new service ${{ env.NEW_SERVICE_NAME }} [${{ env.INPUT_TASK_ID }}]'
                  title: 'feat(bot): add new service ${{ env.NEW_SERVICE_NAME }} [${{ env.INPUT_TASK_ID }}]'
                  body: 'Adding new service `${{ env.NEW_SERVICE_NAME }}` by squad @time-loop/${{ env.NEW_SERVICE_SQUAD }}.'
                  branch: '${{ env.INPUT_TASK_ID }}/create-new-service-${{ env.NEW_SERVICE_NAME }}'
                  labels: generated-new-service
                  add-paths: |
                      .github/CODEOWNERS
                      .github/apps/clickup/microservices.json
                      apps/*
                      codecov.yml
                      libs/*
                      src/config/*.json
                      src/scripts/kafka-topics.json
                      tsconfig.base.json
                  base: master
                  author: github-actions <<EMAIL>>
                  committer: github-actions <<EMAIL>>
            - name: Enable Pull Request Automerge
              if: steps.create-pr.outputs.pull-request-operation == 'created'
              uses: peter-evans/enable-pull-request-automerge@a660677d5469627102a1c1e11409dd063606628d # v3.0.0
              with:
                  token: ${{ secrets.PROJEN_GITHUB_TOKEN }}
                  pull-request-number: ${{ steps.create-pr.outputs.pull-request-number }}
                  merge-method: squash
