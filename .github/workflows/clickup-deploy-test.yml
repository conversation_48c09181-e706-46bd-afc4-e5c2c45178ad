# Deploys deploy-test-service to qa if commit pushed to a branch matching deploy-test/**
name: Test Deploy

on:
    push:
        branches:
            - 'deploy-test/**'

env:
    FORCE_COLOR: 3 # fix terminal color output
    GIT_TAG_PREFIX: 'backend'
    RELEASE_BRANCH_ORDER_CONFIG_PATH: .github/apps/clickup/release-branch-order.yml
    NX_CI_EXECUTION_ID: ${{ github.sha }}-${{ github.run_attempt > 1 && format('{0}-{1}', github.run_id, github.run_attempt) || '' }}
    NX_CI_EXECUTION_ENV: clickup-deploy

permissions:
    contents: read
    actions: write
    issues: write

concurrency:
    group: ${{ github.workflow }}
    cancel-in-progress: false

jobs:
    create-release-tag:
        name: Create release tag
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        permissions:
            contents: write
            pull-requests: read
        outputs:
            new-tag: ${{ steps.create-release-tag.outputs.new-tag }}
            old-tag: ${{ steps.create-release-tag.outputs.old-tag }}
            is-hotfix: ${{ steps.create-release-tag.outputs.is-hotfix }}
            new-version: ${{ steps.create-release-tag.outputs.new-version }}
            old-version: ${{ steps.calculate-old-version.outputs.old-version }}
            previous-branch-latest-tag: ${{ steps.create-release-tag.outputs.previous-branch-latest-tag }}
        steps:
            - name: Create release tag
              id: create-release-tag
              run: |
                  DEPLOY_TEST_VERSION=0.${{ github.run_number }}.${{ github.run_attempt }}-deploytest.${{ github.run_number }}
                  DEPLOY_TEST_TAG=${GIT_TAG_PREFIX}@${DEPLOY_TEST_VERSION}
                  echo "new-tag=${DEPLOY_TEST_TAG}" >> "$GITHUB_OUTPUT"
                  echo "old-tag=${DEPLOY_TEST_TAG}" >> "$GITHUB_OUTPUT"
                  echo "is-hotfix=false" >> "$GITHUB_OUTPUT"
                  echo "new-version=${DEPLOY_TEST_VERSION}" >> "$GITHUB_OUTPUT"
                  echo "old-version=''" >> "$GITHUB_OUTPUT"
                  echo "previous-branch-latest-tag=''" >> "$GITHUB_OUTPUT"
            - name: Calculate old version
              id: calculate-old-version
              env:
                  OLD_TAG: ${{ steps.create-release-tag.outputs.old-tag }}
              run: echo "old-version=${OLD_TAG#*@}" >> $GITHUB_OUTPUT
            - name: Create tag
              uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
              with:
                  script: |
                      github.rest.git.createRef({
                        owner: context.repo.owner,
                        repo: context.repo.repo,
                        ref: 'refs/tags/${{ steps.create-release-tag.outputs.new-tag }}',
                        sha: context.sha
                      })

    install-cache-dependencies:
        name: Install and cache Dependencies
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-5c-10g
        permissions:
            contents: read
        env:
            pnpm-store-cache-name: pnpm-docker-cache-store
            pnpm-store-path-host: pnpm-docker-cache-store # reproducible-containers/buildkit-cache-dance v2.1.3 doesn't support relative paths
            pnpm-store-path-container: /var/cache/pnpm-store
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}

            - name: Set node version
              run: |
                  echo "NODE_VERSION=$(cat .nvmrc | cut -d. -f1)" >> $GITHUB_ENV
            - name: Configure NPM
              run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.ALL_PACKAGE_READ_TOKEN }}" >> ~/.npmrc

            - name: Cache pnpm store for Docker build
              uses: tespkg/actions-cache@a2e3f005921809689270625bc026d387f73017ae # v1
              id: pnpm-store-cache
              continue-on-error: true
              with:
                  bucket: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  path: ${{ env.pnpm-store-path-host }}
                  accessKey: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  secretKey: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  # We don't want to use the fallback cache because it will use too much space of the limited 10GB available
                  use-fallback: false
                  # The repo name is added to the key to prevent collisions between different repos
                  key: ${{ github.event.repository.name }}/${{ runner.os }}-build-${{ env.pnpm-store-cache-name }}-v${{ env.NODE_VERSION }}-${{ hashFiles('pnpm-lock.yaml') }}
                  restore-keys: |
                      ${{ github.event.repository.name }}/${{ runner.os }}-build-${{ env.pnpm-store-cache-name }}-v${{ env.NODE_VERSION }}-

            # we cannot force tespkg/actions-cache to save/overwrite the cache entry, so we need to overwrite it manually
            - name: Check PNPM cache and force upload if necessary
              uses: gacts/run-and-post-run@d803f6920adc9a47eeac4cb6c93dbc2e2890c684 # v1.4.2
              continue-on-error: true
              id: check-cache-dir
              env:
                  CACHE_DIR: ${{ env.pnpm-store-path-host }}
                  AWS_REGION: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  AWS_ACCESS_KEY_ID: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  AWS_SECRET_ACCESS_KEY: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  AWS_S3_ENDPOINT: https://${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  AWS_S3_BUCKET: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  AWS_S3_KEY: ${{ github.event.repository.name }}/${{ runner.os }}-build-${{ env.pnpm-store-cache-name }}-v${{ env.NODE_VERSION }}-${{ hashFiles('pnpm-lock.yaml') }}/cache.tzst
                  AWS_REQUEST_CHECKSUM_CALCULATION: WHEN_REQUIRED
                  AWS_RESPONSE_CHECKSUM_CALCULATION: WHEN_REQUIRED
              with:
                  run: |
                      echo "Size of PNPM Store before install: $(du -sh ${{env.pnpm-store-path-host}} || true)"
                      echo "PNPM Store contents before install: "
                      ls -lah ${{env.pnpm-store-path-host}} || true
                      # also sets valid_cache
                      bash .github/scripts/check-cache-dir.run.sh
                  post: |
                      echo "Size of PNPM Store after install: $(du -sh ${{env.pnpm-store-path-host}} || true)"
                      echo "PNPM Store contents after install: "
                      ls -lah ${{env.pnpm-store-path-host}} || true
                      bash .github/scripts/force-upload-cache.post.sh

            - name: Set up Docker Buildx
              if: steps.pnpm-store-cache.outputs.cache-hit == 'false' || steps.check-cache-dir.outputs.valid_cache == 'false'
              id: setup-buildx
              uses: Wandalen/wretry.action@ffdd254f4eaf1562b8a2c66aeaa37f1ff2231179 # v3.7.3
              with:
                  action: docker/setup-buildx-action@f95db51fddba0c2d1ec667646a06c2ce06100226
                  attempt_delay: 500
                  attempt_limit: 3

            - name: Inject pnpm-store into Docker
              uses: reproducible-containers/buildkit-cache-dance@653a570f730e3b9460adc576db523788ba59a0d7 # 3.2.0
              continue-on-error: true
              # skip if the cache was a hit
              if: steps.pnpm-store-cache.outputs.cache-hit == 'false' || steps.check-cache-dir.outputs.valid_cache == 'false'
              with:
                  builder: ${{ fromJSON(steps.setup-buildx.outputs.outputs).name }}
                  cache-map: |
                      {
                          "${{ env.pnpm-store-path-host }}": "${{ env.pnpm-store-path-container }}"
                      }

            - name: Build cache image
              uses: nick-fields/retry@7152eba30c6575329ac0576536151aca5a72780e # v3.0.0
              # skip if the cache was a hit
              if: steps.pnpm-store-cache.outputs.cache-hit == 'false' || steps.check-cache-dir.outputs.valid_cache == 'false'
              with:
                  max_attempts: 3
                  timeout_minutes: 25
                  command: |
                      BUILD_COMMAND="bash ./tools/docker/clickup/docker-cache-build.sh \
                          --progress=plain \
                          --platform=linux/arm64 \
                          --secret "id=npmrc,src=${HOME}/.npmrc" \
                          --iidfile /tmp/IMAGE_ID \
                          --build-arg PNPM_STORE_PATH="${{ env.pnpm-store-path-container }}""

                      $BUILD_COMMAND

                      test -e /tmp/IMAGE_ID || exit 1
                      echo "DOCKER_IMAGE_ID=$(cat /tmp/IMAGE_ID)" >> $GITHUB_ENV

    read-microservices:
        name: Read microservices
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        permissions:
            contents: read
            actions: read
            pull-requests: read
        outputs:
            build-matrix: ${{ toJSON(fromJSON(steps.set-defaults.outputs.result).build) }}
            deploy-matrix: ${{ toJSON(fromJSON(steps.set-defaults.outputs.result).deploy) }}
        env:
            microservices-path: .github/apps/clickup/microservices.json
        steps:
            #  TODO: use the actual script in clickup-deploy.yml to parse the microservices.json file, and test it as well(but don't use it in this workflow?)
            - name: Set defaults
              uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
              id: set-defaults
              with:
                  script: |
                      const result = JSON.stringify({
                        "build": [
                          {
                            "skip-health-check": false,
                            "is-nest-app": true,
                            "name": "deploy-test-service",
                            "eks-only-service": true,
                            "deploy-name": "deploy-test-service",
                            "eks-services": "[\"deploy-test-service\"]",
                            "eks-enabled": false,
                            "eks-only": true,
                            "envs": "[]",
                            "build": true,
                            "deploy": true
                          }
                        ],
                        "deploy": [
                          {
                            "skip-health-check": false,
                            "is-nest-app": true,
                            "name": "deploy-test-service",
                            "eks-only-service": true,
                            "deploy-name": "deploy-test-service",
                            "eks-services": "[\"deploy-test-service\"]",
                            "eks-enabled": true,
                            "eks-only": true,
                            "envs": "[\"qa\"]",
                            "build": true,
                            "deploy": true
                          }
                        ]
                      });
                      console.log('result: ' + result);
                      return result;
                  result-encoding: string

    build-all-services-push:
        name: Build All Microservice Images
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-2c-4g
        needs: [install-cache-dependencies, create-release-tag, read-microservices]
        if: ${{ needs.read-microservices.outputs.deploy-matrix != '[]' && github.event_name == 'push' }}
        steps:
            - name: Options
              run: |
                  echo "tag:${{ needs.create-release-tag.outputs.new-tag }}"
                  echo "version:${{ needs.create-release-tag.outputs.new-version }}"
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - uses: time-loop/trigger-workflow-and-wait@ef3ce75d2cc6e31faa418b5516e80b98e3adb006
              timeout-minutes: 20
              with:
                  owner: time-loop
                  repo: clickup
                  github_token: ${{ steps.app-token.outputs.token }}
                  workflow_file_name: build-all-services-images.yml
                  ref: ${{ needs.create-release-tag.outputs.new-tag }}
                  wait_interval: ${{ vars.WORKFLOW_WAIT_INTERVAL_SECONDS }}
                  propagate_failure: true
                  trigger_workflow: true
                  wait_workflow: false
                  client_payload: |
                      {
                          "version": "${{ needs.create-release-tag.outputs.new-version }}",
                          "matrix": ${{ toJson(needs.read-microservices.outputs.deploy-matrix) }},
                          "branch-name": "${{ github.ref_name }}",
                          "fully-formed-branch-name": "${{ github.ref }}",
                          "github-event-name": "${{ github.event_name }}",
                          "github-base-ref": "${{ github.base_ref }}",
                          "previous-branch-latest-tag": "${{ needs.create-release-tag.outputs.previous-branch-latest-tag }}",
                          "current-branch-current-tag": "${{ needs.create-release-tag.outputs.old-tag }}",
                          "is-hotfix": "${{ needs.create-release-tag.outputs.is-hotfix }}"
                      }
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  filter: tree:0
            - uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5.5.0
              with:
                  python-version: '3.x'
                  cache: 'pip'
            - run: pip install -r .github/scripts/requirements.txt
            - name: Run Script
              run: python .github/scripts/provide-top-level-build.py ${{ needs.create-release-tag.outputs.new-version }}
              env:
                  ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
                  ENGPRODCI_CLICKUP_GITHUB_APP_ID: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID }}
                  ENGPRODCI_CLICKUP_GITHUB_APP_INSTALLATION_ID: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_INSTALLATION_ID }}
                  RETRY: ${{ github.ref_name == 'master' && '0' || '1' }}

    all-builds-complete:
        name: All builds complete
        needs: [create-release-tag, build-all-services-push]
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        if: ${{ always() && needs.create-release-tag.result == 'success' }}
        steps:
            - name: All builds complete
              run: echo "All builds complete"

    stop-nx-ci-run:
        name: Stop NX CI Run
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        if: vars.USE_BUILD_TEST_NX_DTE == 'true' && always()
        permissions:
            contents: read
            actions: read
        needs: [create-release-tag, build-all-services-push]
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
            - name: Setup Node
              uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
            - name: Stop the CI run
              continue-on-error: true
              run: npx --yes nx-cloud complete-ci-run

    deploy-all-services:
        name: All shards deploy
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-2c-4g
        needs: [create-release-tag, read-microservices, all-builds-complete]
        environment: ${{ (github.ref_name=='production' && 'deploy-all-shards') || 'deploy-no-approval' }}
        timeout-minutes: ${{ (startsWith(github.ref_name, 'deploy-test/') && 30) || fromJSON('{"master":60,"staging":180,"production":180}')[github.ref_name] }}
        if: ${{ !cancelled() && github.event_name == 'push' && needs.create-release-tag.result == 'success' && needs.deployment-ddl-precheck.result != 'failure' }}
        steps:
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - uses: time-loop/trigger-workflow-and-wait@ef3ce75d2cc6e31faa418b5516e80b98e3adb006
              timeout-minutes: 20
              with:
                  owner: time-loop
                  repo: clickup
                  github_token: ${{ steps.app-token.outputs.token }}
                  workflow_file_name: ecs-deploy-all-services.yml
                  ref: ${{ needs.create-release-tag.outputs.new-tag }}
                  wait_interval: ${{ vars.WORKFLOW_WAIT_INTERVAL_SECONDS }}
                  propagate_failure: true
                  trigger_workflow: true
                  wait_workflow: false
                  client_payload: |
                      {
                          "version": "${{ needs.create-release-tag.outputs.new-version }}",
                          "matrix": ${{ toJson(needs.read-microservices.outputs.deploy-matrix) }},
                          "branch-name": "${{ github.ref_name }}",
                          "fully-formed-branch-name": "${{ github.ref }}",
                          "github-event-name": "${{ github.event_name }}",
                          "original-sha": "${{ github.sha }}",
                          "is-canary": "false"
                      }
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  filter: tree:0
            - uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5.5.0
              with:
                  python-version: '3.x'
                  cache: 'pip'
            - run: pip install -r .github/scripts/requirements.txt
            - name: Provide Job Summary for Deploys
              run: python .github/scripts/display_deploy_summary.py ${{ needs.create-release-tag.outputs.new-version }} all_shards
              env:
                  ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
                  ENGPRODCI_CLICKUP_GITHUB_APP_ID: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID }}
                  ENGPRODCI_CLICKUP_GITHUB_APP_INSTALLATION_ID: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_INSTALLATION_ID }}
                  DD_API_KEY: ${{ secrets.ENGPROD_DATADOG_API_KEY }}
                  DD_APP_KEY: ${{ secrets.ENGPROD_DATADOG_APP_KEY }}
                  DD_SITE: datadoghq.com
                  BRANCH_NAME: ${{ github.ref_name }}
                  DEPLOY_SERVICES: ${{ needs.read-microservices.outputs.deploy-matrix }}
