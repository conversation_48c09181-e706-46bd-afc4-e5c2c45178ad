run-name: Build Microservice Images ${{ inputs.version }}

on:
    workflow_call:
        inputs:
            version:
                description: 'The version of the image to be built'
                type: string
                required: true
            matrix:
                description: 'All Services to be built'
                type: string
                required: true
            branch-name:
                description: 'The branch name this is invoked from'
                type: string
                required: true
            fully-formed-branch-name:
                description: 'The fully formed branch name this is invoked from'
                type: string
                required: true
            github-event-name:
                description: 'The event name that triggered this workflow initially'
                type: string
                required: true
            github-base-ref:
                description: 'The base ref of the PR that triggered this workflow initially'
                type: string
                required: true
            previous-branch-latest-tag:
                description: 'The latest tag of the previous branch'
                type: string
                required: false
            current-branch-current-tag:
                description: 'The current tag of the current branch (this is different from current branch new tag)'
                type: string
                required: false
            is-hotfix:
                description: 'Whether this is a hotfix'
                type: boolean
                required: true

    workflow_dispatch:
        inputs:
            version:
                description: 'The version of the image to be built'
                type: string
                required: true
            matrix:
                description: 'All Services to be built'
                type: string
                required: true
            branch-name:
                description: 'The branch name this is invoked from'
                type: string
                required: true
            fully-formed-branch-name:
                description: 'The fully formed branch name this is invoked from'
                type: string
                required: true
            github-event-name:
                description: 'The event name that triggered this workflow initially'
                type: string
                required: true
            github-base-ref:
                description: 'The base ref of the PR that triggered this workflow initially'
                type: string
                required: false
            previous-branch-latest-tag:
                description: 'The latest tag of the previous branch'
                type: string
                required: false
            current-branch-current-tag:
                description: 'The current tag of the current branch (this is different from current branch new tag)'
                type: string
                required: false
            is-hotfix:
                description: 'Whether this is a hotfix'
                type: boolean
                required: true

jobs:
    calculate-diff:
        name: Calculate diff between tags
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        environment: ${{ fromJSON('{"master":"","staging":"ecs-deploy-user-usqa","production":"ecs-deploy-user-usstaging"}')[inputs.branch-name] }}
        if: ${{ fromJSON(vars.ENABLE_ARTIFACT_BASED_DEPLOYMENT)[inputs.branch-name] && inputs.github-event-name == 'push' }}
        continue-on-error: true
        outputs:
            has_missing_changes: ${{ steps.calculate-diff.outputs.has_missing_changes }}
            missing_commits_count: ${{ steps.calculate-diff.outputs.missing_commits_count }}
            missing_files_count: ${{ steps.calculate-diff.outputs.missing_files_count }}
            current_branch_current_tag_sha: ${{ steps.get-shas.outputs.current_branch_current_tag_sha }}
            previous_branch_latest_tag_sha: ${{ steps.get-shas.outputs.previous_branch_latest_tag_sha }}
            base64_source_ecr_auth_token: ${{ steps.save_source_aws_credentials.outputs.ecr_auth_token }}
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  filter: tree:0
            - name: Calculate Git Diff
              id: calculate-diff
              uses: time-loop/github-actions/dist/diff-between-tags@abf507c86b846f24b9dc77d8c9ee4c761d21cf9b # diff-between-tags+0.1.0-alpha.2
              with:
                  source-tag: ${{ inputs.current-branch-current-tag }}
                  target-tag: ${{ inputs.previous-branch-latest-tag }}
            - name: Get and Output Shas
              id: get-shas
              run: |
                  echo "Checking tags"
                  # Use a function to handle the tag check and output consistently
                  # May change this to github-actions repo script in the future
                  check_tag() {
                    local tag_name="$1"
                    local tag_value="$2"
                    local output_name=$(echo "$tag_name" | tr '-' '_')

                    echo "Checking tag: $tag_name"
                    if [[ -n "$tag_value" ]]; then
                      echo "  Value: $tag_value"
                      # Try to get the SHA and capture it
                      local sha=$(git rev-list -1 "$tag_value" 2>/dev/null || echo "")
                      if [[ -n "$sha" ]]; then
                        echo "  SHA: $sha"
                        # Set output variables
                        echo "${output_name}_exists=true" >> $GITHUB_OUTPUT
                        echo "${output_name}_sha=$sha" >> $GITHUB_OUTPUT
                      else
                        echo "  Status: No revision found"
                        echo "${output_name}_exists=false" >> $GITHUB_OUTPUT
                        echo "${output_name}_sha=" >> $GITHUB_OUTPUT
                      fi
                    else
                      echo "  Status: Empty"
                      echo "${output_name}_exists=false" >> $GITHUB_OUTPUT
                      echo "${output_name}_sha=" >> $GITHUB_OUTPUT
                    fi
                    echo ""
                  }

                  # Process all tags using the function
                  check_tag "current-branch-current-tag" "${{ inputs.current-branch-current-tag || '' }}"
                  check_tag "previous-branch-latest-tag" "${{ inputs.previous-branch-latest-tag || '' }}"

            - name: Save Source AWS Credentials
              if: ${{ inputs.branch-name == 'staging' || inputs.branch-name == 'production' }}
              id: save_source_aws_credentials
              run: |
                  aws configure set aws_access_key_id ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws configure set aws_secret_access_key ${{ secrets.AWS_SECRET_ACCESS_KEY }}

                  ecr_auth=$(aws ecr get-login-password --region us-east-1 | base64 -w 0)
                  echo "ecr_auth_token=${ecr_auth}" >> $GITHUB_OUTPUT

            - name: Debug Outputs
              run: |
                  echo "Has missing changes: ${{ steps.calculate-diff.outputs.has_missing_changes }}"
                  echo "Missing commits count: ${{ steps.calculate-diff.outputs.missing_commits_count }}"
                  echo "Missing files count: ${{ steps.calculate-diff.outputs.missing_files_count }}"
                  echo "Current branch current tag SHA: ${{ steps.get-shas.outputs.current_branch_current_tag_sha }}"
                  echo "Previous branch latest tag SHA: ${{ steps.get-shas.outputs.previous_branch_latest_tag_sha }}"
                  echo "Is hotfix: ${{ inputs.is-hotfix }}"
                  echo "Base64 source ECR auth token:  $([ -n "${{ steps.save_source_aws_credentials.outputs.ecr_auth_token }}" ] && echo "is set" || echo "is not set because it's not staging or production release")"
    build-monolith:
        name: Build monolith
        needs: calculate-diff
        if: ${{ !failure() }}
        uses: ./.github/workflows/ecs-monolith-build.yml
        secrets: inherit
        permissions:
            actions: read
            id-token: write
            contents: read
            packages: read
            pull-requests: read
        with:
            github-event-name: ${{ inputs.github-event-name }}
            version: ${{ inputs.version }}
            branch-name: ${{ inputs.branch-name }}
            fully-formed-branch-name: ${{ inputs.fully-formed-branch-name }}
            github-base-ref: ${{ inputs.github-base-ref }}
            previous-branch-latest-tag-sha: ${{ needs.calculate-diff.result == 'success' && needs.calculate-diff.outputs.previous_branch_latest_tag_sha || '' }}
            has-missing-changes: ${{ needs.calculate-diff.result == 'success' && needs.calculate-diff.outputs.has_missing_changes || 'true' }}
            is-hotfix: ${{ inputs.is-hotfix }}
            base64_source_ecr_auth_token: ${{ needs.calculate-diff.result == 'success' && needs.calculate-diff.outputs.base64_source_ecr_auth_token || '' }}

    build-microservices:
        name: Build ${{ matrix.service.name }}
        uses: ./.github/workflows/ecs-service-build-image.yml
        needs: calculate-diff
        if: ${{ !failure() && inputs.matrix != '[]' }}
        secrets: inherit
        permissions:
            actions: read
            id-token: write
            contents: read
            packages: read
            pull-requests: read
        strategy:
            fail-fast: false
            matrix:
                service: ${{ fromJson(inputs.matrix) }}
        with:
            version: ${{ inputs.version }}
            service-name: ${{ matrix.service.name }}
            skip-health-check: ${{ matrix.service.skip-health-check }}
            is-nest-app: ${{ matrix.service.is-nest-app }}
            envs: ${{ matrix.service.envs }}
            branch-name: ${{ inputs.branch-name }}
            fully-formed-branch-name: ${{ inputs.fully-formed-branch-name }}
            github-event-name: ${{ inputs.github-event-name }}
            github-base-ref: ${{ inputs.github-base-ref }}
            previous-branch-latest-tag-sha: ${{ needs.calculate-diff.result == 'success' && needs.calculate-diff.outputs.previous_branch_latest_tag_sha || '' }}
            has-missing-changes: ${{ needs.calculate-diff.result == 'success' && needs.calculate-diff.outputs.has_missing_changes || 'true' }}
            is-hotfix: ${{ inputs.is-hotfix }}
            base64_source_ecr_auth_token: ${{ needs.calculate-diff.result == 'success' && needs.calculate-diff.outputs.base64_source_ecr_auth_token || '' }}
