# Runs a subset of the actions from Build-Test that should be ran for every merge to a main branch (linting, type checking, etc)

name: Merge queue
on:
    merge_group:
        types: [checks_requested]

permissions:
    contents: read
    pull-requests: read

env:
    type-check-cache-version: 1
    NODE_OPTIONS: '--max-old-space-size=7175 --dns-result-order=ipv4first'
    DD_API_KEY: ${{ secrets.DD_API_KEY }}
    nx-cache-version: 1
    SUPPRESS_NO_CONFIG_WARNING: 'true'
    NXCACHE_AWS_DISABLE: true
    nxcache_aws_access_key_id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
    NXCACHE_AWS_SECRET_ACCESS_KEY: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
    NXCACHE_AWS_REGION: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
    NXCACHE_AWS_BUCKET: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME_NX }}
    NXCACHE_AWS_ENDPOINT: https://${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
    NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}
    CI_STRATEGY: build-test
    NX_BRANCH: ${{ github.event.number || github.ref_name }}
    NX_CI_EXECUTION_ID: ${{ github.sha }}-${{ github.run_attempt > 1 && format('{0}-{1}', github.run_id, github.run_attempt) || '' }}
    NX_CI_EXECUTION_ENV: 'Merge Queue Checks'

jobs:
    dummy-jobs:
        strategy:
            matrix:
                # Required pipeline names
                # Must match the ones in https://github.com/time-loop/clickup/settings/branch_protection_rules/5703606
                name:
                    [
                        'Validate PR title',
                        'Require PR description',
                        'Merge main branches',
                        'ClickUp ECS deploy completed',
                        'Validate Check-API-Specs Success',
                    ]
        name: ${{ matrix.name }}
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        steps:
            - run: echo "${{ matrix.name }} pipeline succeeded"

    lint-check:
        name: Lint check
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-amd64-4c-16g
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
              with:
                  # We need to fetch all branches and commits so that Nx affected has a base to compare against.
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: true
                  nx-main-branch: master
                  nx-error-on-no-successful-workflow: false
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Get changed files
              id: changed-files
              uses: tj-actions/changed-files@ed68ef82c095e0d48ec87eccea555d944a631a4c # v46.0.5
              with:
                  files: '**/*.{ts,js,json}'
            - name: Warm the nx project graph
              run: pnpm exec nx show projects > /dev/null
            - name: Run ESLint
              if: steps.changed-files.outputs.all_changed_files_count > 0
              run: pnpm eslint --quiet ${{ steps.changed-files.outputs.all_changed_files }}
            - name: Alert on failure
              if: failure()
              uses: time-loop/github-actions/dist/post-failure-to-clickup-task@post-failure-to-clickup-task+0.3.1
              with:
                  github-token: ${{ secrets.GITHUB_TOKEN }}
                  clickup-token: ${{ secrets.CLICKUP_API_TOKEN }}
                  current-job-name: Merge queue lint check

    type-check:
        name: Type check
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-amd64-8c-32g
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
              with:
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: true
                  nx-main-branch: master
                  nx-error-on-no-successful-workflow: false
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Restore type-check cache
              uses: time-loop/github-actions/dist/pull-request-cache@pull-request-cache+0.3.10
              with:
                  cache-name: type-check-cache-${{ env.type-check-cache-version }}-${{ hashFiles('test/tsconfig.json', 'tsconfig.json', 'tsconfig-nx-test-typecheck.json', 'tsconfig.base.json', 'package.json') }}
                  cache-path: ./dist/out-tsc/
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Run typecheck
              timeout-minutes: 20
              run: pnpm exec nx affected -t typecheck --parallel --output-style=stream
            - name: Alert on failure
              if: failure()
              uses: time-loop/github-actions/dist/post-failure-to-clickup-task@post-failure-to-clickup-task+0.3.1
              with:
                  github-token: ${{ secrets.GITHUB_TOKEN }}
                  clickup-token: ${{ secrets.CLICKUP_API_TOKEN }}
                  current-job-name: Merge queue type check

    build-test-success:
        name: Build-Test Pipeline Succeeded
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        needs: ['dummy-jobs', 'lint-check', 'type-check']
        if: success() || failure()
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Setup Node
              uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
            - name: Stop the CI run
              continue-on-error: true
              run: npx --yes nx-cloud complete-ci-run
            - name: Note Pipeline Success
              env:
                  hasFailure: ${{ contains(needs.*.result, 'failure') }}
              run: |
                  msgPrefix="Build-Test merge queue checks for #${{ github.run_id }} finished"
                  if [[ "$hasFailure" == "false" ]]; then
                      echo "${msgPrefix} successfully."
                      exit 0
                  else
                      echo "${msgPrefix}, but with failures. Check left sidebar summary to see which jobs failed."
                      exit 1
                  fi
