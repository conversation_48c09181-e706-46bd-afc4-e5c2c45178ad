# Automatically concatenate individual DB migration files and commit them to the repo

name: WMS Metadata Auto Fix

on:
    pull_request:
        branches:
            - master
            - staging
            - production
        paths:
            - 'src/scripts/migrations/*/*.sql'

permissions:
    checks: write
    contents: write

jobs:
    regen-db-migrations:
        timeout-minutes: 15
        runs-on: ubuntu-24.04
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  token: ${{ secrets.MACHINE_USER_GITHUB_TOKEN }}
            - uses: pnpm/action-setup@fe02b34f77f8bc703788d5817da081398fad5dd2 # v4.0.0
            - name: Pre Start Docker Compose
              shell: bash
              run: pnpm run ci:test:docker-compose:up &
            - name: Setup node and dependencies
              # This condition is the same as for the `Run build` step. No need to run if the build is going to be skipped
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Wait For Docker Compose To Be Ready
              uses: ./.github/actions/docker-compose-run
              with:
                  command: pnpm run ci:test:docker-compose:wait
            - name: Renegerate metadata
              timeout-minutes: 5
              run: pnpm exec nx run migration-workflow-service:regen-metadata
            - name: Run prettier
              timeout-minutes: 5
              run: pnpm exec prettier --write apps/migration-workflow-service/src/app/datastores/metadata/generated/
            - name: Commit generated metadata files
              timeout-minutes: 5
              uses: stefanzweifel/git-auto-commit-action@8621497c8c39c72f3e2a999a26b4ca1b5058a842 # v5.0.1
              with:
                  commit_message: 'chore: Regenerate WMS metadata'
                  file_pattern: 'apps/migration-workflow-service/src/app/datastores/metadata/generated/*.ts'
              env:
                  GITHUB_TOKEN: ${{ secrets.MACHINE_USER_GITHUB_TOKEN }}
