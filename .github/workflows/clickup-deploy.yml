# Deploys the ClickUp monolith in `src` and all microservices in `apps` to ECS
# This is done in a single parent workflow to make it easier to track deploys

name: Deploy ClickUp to ECS

on:
    pull_request:
        types: [opened, synchronize]
        branches:
            - master
            - staging
            - production
    push:
        branches:
            - master
            - staging
            - production

env:
    FORCE_COLOR: 3 # fix terminal color output
    GIT_TAG_PREFIX: backend
    RELEASE_BRANCH_ORDER_CONFIG_PATH: .github/apps/clickup/release-branch-order.yml
    NX_CI_EXECUTION_ID: ${{ github.sha }}-${{ github.run_attempt > 1 && format('{0}-{1}', github.run_id, github.run_attempt) || '' }}
    NX_CI_EXECUTION_ENV: clickup-deploy

permissions:
    contents: write
    actions: write
    issues: write

concurrency:
    # We always want to run this workflow on every commit to staging / production to make sure the correct git tag is always added
    # Otherwise there is a potential race condition where a main branch is promoted in between 2 hotfix commits and the promotion commit is skipped so the wrong tag is created
    group: ${{ github.workflow }}-${{ github.ref }}-${{ contains(fromJSON('["staging", "production"]'), github.ref_name) && github.run_id || '' }}
    # We only cancel previously running jobs on pull requests, pushes to main branches will always run
    cancel-in-progress: ${{ github.event_name == 'pull_request' }}

jobs:
    check-pr-type:
        name: Check PR Type
        continue-on-error: true
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        steps:
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - name: Get PR Type
              uses: time-loop/github-actions/dist/get-pr-type@get-pr-type+0.8.0
              id: get-pr-type
              with:
                  github-app-id: ${{ secrets.CLICKUP_GITHUB_BOT_APP_ID }}
                  github-private-key: ${{ secrets.CLICKUP_GITHUB_BOT_PRIVATE_KEY }}
                  github-token: ${{ steps.app-token.outputs.token }}
            - name: Echo PR Type
              run: |
                  echo 'PR Type: ${{ steps.get-pr-type.outputs.type }}'
        outputs:
            PR_TYPE: ${{ steps.get-pr-type.outputs.type }}
    create-release-tag:
        name: Create release tag
        needs: [check-pr-type]
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        permissions:
            contents: write
            pull-requests: read
        outputs:
            new-tag: ${{ steps.create-release-tag.outputs.new-tag }}
            old-tag: ${{ steps.create-release-tag.outputs.old-tag }}
            is-hotfix: ${{ steps.create-release-tag.outputs.is-hotfix }}
            new-version: ${{ steps.create-release-tag.outputs.new-version }}
            old-version: ${{ steps.calculate-old-version.outputs.old-version }}
            previous-branch-latest-tag: ${{ steps.create-release-tag.outputs.previous-branch-latest-tag }}
            deploy-pipeline-start-time: ${{ steps.save-deploy-pipeline-start-time.outputs.deploy-pipeline-start-time }}
        steps:
            - name: Save Deploy Pipeline Start Time
              id: save-deploy-pipeline-start-time
              run: echo "deploy-pipeline-start-time=$(date +%s)" >> $GITHUB_OUTPUT
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - name: Create release tag
              uses: time-loop/github-actions/dist/create-release-tag@create-release-tag+3.1.2
              # Can't skip the entire job or it will prevent all dependent jobs from running
              if: github.event_name == 'push'
              id: 'create-release-tag'
              with:
                  github-token: ${{ steps.app-token.outputs.token }}
                  # A github token with admin permissions that can create tags and trigger other workflows
                  github-admin-token: ${{ steps.app-token.outputs.token }}
                  # A prefix to use for all git tags e.g. the tags would look like backend@2.130.0
                  tag-prefix: ${{ env.GIT_TAG_PREFIX }}
                  # The major version number to use for all git tags, increment when we market a new major release of clickup
                  major-version-number: 3
                  # Initial version number to start from. Follows same versioning base as the frontend
                  initial-version: 2.133.0
                  # Path to the config file that defines the order of the release branches
                  release-branch-order-config-path: ${{ env.RELEASE_BRANCH_ORDER_CONFIG_PATH }}
            - name: Calculate old version
              id: calculate-old-version
              if: github.event_name == 'push' && needs.check-pr-type.outputs.PR_TYPE == 'config'
              env:
                  OLD_TAG: ${{ steps.create-release-tag.outputs.old-tag }}
              run: echo "old-version=${OLD_TAG#*@}" >> $GITHUB_OUTPUT
            - name: show Outputs
              run: |
                  echo "new-tag=${{ steps.create-release-tag.outputs.new-tag }}"
                  echo "old-tag=${{ steps.create-release-tag.outputs.old-tag }}"
                  echo "is-hotfix=${{ steps.create-release-tag.outputs.is-hotfix }}"
                  echo "new-version=${{ steps.create-release-tag.outputs.new-version }}"
                  echo "old-version=${{ steps.calculate-old-version.outputs.old-version }}"
                  echo "previous-branch-latest-tag=${{ steps.create-release-tag.outputs.previous-branch-latest-tag }}"

    update-deploy-bot-data:
        name: Update Deploy Bot Data
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        needs: [create-release-tag]
        if: github.event_name == 'push' && github.ref == 'refs/heads/master'
        permissions:
            contents: read
            id-token: write
            pull-requests: read
        steps:
            - name: Setup Node
              uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
            - name: Install aws-sdk
              run: npm i aws-sdk
            - name: configure qa aws credentials
              uses: aws-actions/configure-aws-credentials@e3dd6a429d7300a6a4c196c26e071d42e0343502 # v4.0.2
              with:
                  role-to-assume: arn:aws:iam::187416477703:role/clickup-deploy-bot-role
                  role-duration-seconds: 900
                  aws-region: us-west-2
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - name: Update Deploy 2Bot Data
              id: update-deploy-bot-data
              continue-on-error: true
              env:
                  GITHUB_TOKEN: ${{ steps.app-token.outputs.token }}
              run: |
                  COMMIT_SHA=${{ github.sha }}
                  OWNER_AND_REPO=${{ github.repository }}
                  PR_ID=$(curl --silent --show-error -H "Authorization: token $GITHUB_TOKEN" https://api.github.com/repos/$OWNER_AND_REPO/commits/$COMMIT_SHA/pulls | jq '.[0].number')

                  TAG=${{ needs.create-release-tag.outputs.new-tag }}
                  REPO=${{ github.event.repository.name }}

                  items=$(aws dynamodb query --table-name cu-deploy-bot-data --index-name repo-pr-id-index --key-condition-expression "pk = :pk and pr_id = :pr_id" --expression-attribute-values '{":pk":{"S": "'"${REPO}"'"}, ":pr_id":{"S": "'"${PR_ID}"'"}}'  --projection-expression "sk" --output json | jq -r '.Items[] | .sk.S')

                  for sk in $items
                  do
                      aws dynamodb update-item --table-name cu-deploy-bot-data --key '{"pk": {"S": "'"${REPO}"'"}, "sk": {"S": "'"$sk"'"} }' --update-expression "SET tag = :tag" --expression-attribute-values '{":tag": {"S": "'"${TAG}"'"}}'
                  done

            - name: Report saving outcome
              run: echo "The DynamoDB call outcome was ${{ steps.update-deploy-bot-data.outcome }}"
    create-release-notes:
        name: Create release notes
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        needs: [create-release-tag]
        if: github.event_name == 'push'
        permissions:
            contents: write
            pull-requests: read
        steps:
            - name: Checkout repo
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  filter: tree:0
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - name: Create release notes
              uses: time-loop/github-actions/dist/create-release-notes@create-release-notes+1.2.15
              with:
                  github-token: ${{ steps.app-token.outputs.token }}
                  # All commits between these 2 tags will appear in the release notes
                  last-release-git-tag: ${{ needs.create-release-tag.outputs.old-tag }}
                  new-release-git-tag: ${{ needs.create-release-tag.outputs.new-tag }}
                  # The same prefix you passed to the create-release-tag action
                  tag-prefix: ${{ env.GIT_TAG_PREFIX }}
                  # Determines if this is a hotfix release or a main branch promotion from master -> staging -> production
                  is-hotfix: ${{ needs.create-release-tag.outputs.is-hotfix }}
                  # Path to the config file that defines the order of the release branches
                  release-branch-order-config-path: ${{ env.RELEASE_BRANCH_ORDER_CONFIG_PATH }}
                  # ClickUp chat ID where to send a release notification to
                  release-notification-clickup-view-id: ${{ vars.CLICKUP_CHAT_ID_ENG_RELEASE_ALERTS }}
                  # Bot token used to post to ClickUp chat
                  clickup-token: ${{ secrets.CLICKUP_API_TOKEN }}
    build-monolith-pr:
        name: Build monolith
        uses: ./.github/workflows/ecs-monolith-build.yml
        needs: [check-pr-type, install-cache-dependencies, create-release-tag]
        if: ${{ github.event_name == 'pull_request' && needs.check-pr-type.outputs.PR_TYPE != 'config' }}
        secrets: inherit
        permissions:
            actions: read
            id-token: write
            contents: read
            packages: read
            pull-requests: read
        with:
            github-event-name: ${{ github.event_name }}
            version: ${{ needs.create-release-tag.outputs.new-version }}
            branch-name: ${{ github.ref_name }}
            fully-formed-branch-name: ${{ github.ref }}
            github-base-ref: ${{ github.base_ref }}

    all-builds-complete:
        name: All builds complete
        needs:
            [
                build-monolith-pr,
                create-release-tag,
                build-microservices-pr,
                build-all-services-push,
                build-all-services-config-push,
                deployment-ddl-precheck,
            ]
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        if: ${{ always() && (needs.create-release-tag.result == 'success' || needs.create-release-tag.result == 'skipped') && needs.deployment-ddl-precheck.result != 'failure' }}
        steps:
            - name: All builds complete
              run: echo "All builds complete"
    stop-nx-ci-run:
        name: Stop NX CI Run
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        if: vars.USE_BUILD_TEST_NX_DTE == 'true' && always()
        permissions:
            contents: read
            actions: read
        needs:
            [
                build-monolith-pr,
                create-release-tag,
                build-microservices-pr,
                build-all-services-push,
                build-all-services-config-push,
            ]
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
            - name: Setup Node
              uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
            - name: Stop the CI run
              continue-on-error: true
              run: npx --yes nx-cloud complete-ci-run
    read-microservices:
        name: Read microservices
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-4c-8g
        permissions:
            contents: read
            actions: read
            pull-requests: read
        outputs:
            build-matrix: ${{ toJSON(fromJSON(steps.set-defaults.outputs.result).build) }}
            deploy-matrix: ${{ toJSON(fromJSON(steps.set-defaults.outputs.result).deploy) }}
        env:
            microservices-path: .github/apps/clickup/microservices.json
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  nx-setup: true
                  nx-main-branch: master
            - name: Setup Go and dependencies
              uses: actions/setup-go@3041bf56c941b39c61721a86cd11f3bb1338122a # v5.2.0
              with:
                  go-version: '>=1.24.1'
                  cache: false
            - name: Setup Java
              uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
              with:
                  distribution: 'corretto'
                  java-version: '21'
            - name: Setup Gradle
              uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1
            - name: Find affected services
              id: affected-services
              uses: ./.github/actions/get-nx-affected
              if: github.event_name == 'pull_request' || github.event_name == 'merge_group'
              with:
                  target: build
            - name: Validate microservices
              uses: walbo/validate-json@1c24a27a740a698944ff5b697cb8010a72c55c6b # v1.1.0
              with:
                  files: ${{ env.microservices-path }}
                  schema: .github/apps/clickup/microservices.schema.json
                  schema-version: draft-07
                  fail-on-missing-schema: true
                  print-valid-files: true
            - name: Read microservices
              id: read-microservices
              uses: time-loop/github-actions/dist/load-json-file@load-json-file+0.1.6
              with:
                  json-path: ${{github.workspace}}/.github/apps/clickup/microservices.json
            - name: Set defaults
              uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
              id: set-defaults
              with:
                  script: |
                      const affectedServices = JSON.parse(${{ steps.affected-services.outputs.affected-array || '"[]"' }});

                      // read deployable services from env variables, so we control the services without code change
                      // only have whitelist for staging and production, as we try to deploy all services to QA, which is easier for new service onboarding
                      const stagingDeployableServices = JSON.parse(${{ vars.ENV_STAGING_DEPLOYABLE_SERVICES || '"[]"' }});
                      console.log('STAGING env deployable services: ' + stagingDeployableServices);
                      console.log('STAGING env deployable service count: ' + stagingDeployableServices.length);

                      const productionDeployableServices = JSON.parse(${{ vars.ENV_PRODUCTION_DEPLOYABLE_SERVICES || '"[]"' }});
                      console.log('PRODUCTION env deployable services: ' + productionDeployableServices);
                      console.log('PRODUCTION env deployable service count: ' + productionDeployableServices.length);

                      const qaEksServices = ${{ vars.MONOREPO_QA_EKS_SERVICES || '[]' }};
                      console.log('QA EKS services: ' + qaEksServices);

                      const stagingEksServices = ${{ vars.MONOREPO_STAGING_EKS_SERVICES || '[]' }};
                      console.log('STAGING EKS services: ' + stagingEksServices);

                      const productionEksServices = ${{ vars.MONOREPO_PROD_EKS_SERVICES || '[]' }};
                      console.log('PRODUCTION EKS services: ' + productionEksServices);

                      // list of all services that are whitelisted for deployment
                      const allWhitelistedServices = [
                            ...stagingDeployableServices,
                            ...productionDeployableServices,
                            ...qaEksServices,
                            ...stagingEksServices,
                            ...productionEksServices
                      ].filter((value, index, self) => self.indexOf(value) === index);
                      console.log('All whitelisted services: ' + allWhitelistedServices);

                      // list of all services exluding whitelisted
                      const nonWhitelistedServices = ${{ steps.read-microservices.outputs.json-string }}
                        .map(service => service.name)
                        .filter(service => !allWhitelistedServices.includes(service));
                      console.log('Non whitelisted services: ' + nonWhitelistedServices);

                      const services = ${{ steps.read-microservices.outputs.json-string }}.map(service => {
                          // Envs list must be stringified within the stringified result
                          // in order to be usable as an input to the microservice job.
                          // for push event, we find the deployable env defined
                          const deployEnvs = [];
                          let eksEnabled = false;
                          var eksServices = [];
                          if (service["eks-services"]) {
                             eksServices = service["eks-services"];
                          } else {
                             eksServices.push(service.name);
                          }

                          let eksOnly = false;
                          if (service["eks-only-service"]) {
                            eksOnly = service["eks-only-service"];
                          }

                          if (context.ref == 'refs/heads/master') {
                            eksEnabled = qaEksServices.some(e => eksServices.includes(e));
                            eksServices = eksServices.filter(service => qaEksServices.includes(service));
                            deployEnvs.push("qa");
                          }
                          if (context.ref == 'refs/heads/staging' && stagingDeployableServices.includes(service.name)) {
                            eksEnabled = stagingEksServices.some(e => eksServices.includes(e));
                            eksServices = eksServices.filter(service => stagingEksServices.includes(service));
                            deployEnvs.push("staging");
                          }
                          if (context.ref == 'refs/heads/production' && productionDeployableServices.includes(service.name)) {
                            eksEnabled = productionEksServices.some(e => eksServices.includes(e));
                            eksServices = eksServices.filter(service => productionEksServices.includes(service));
                            deployEnvs.push("production");
                          }

                          const shouldDeploy = (service.envs != "[]" || context.eventName == 'pull_request') &&
                            (affectedServices.includes(service.name) || context.eventName != 'pull_request')

                          var envs = JSON.stringify(deployEnvs);
                          return {
                            // Set defaults for any missing properties otherwise github actions matrix will hang
                            // TODO: use json schema to validate the input and set defaults
                            "skip-health-check": false,
                            "is-nest-app": true,
                            ...service,
                            "deploy-name": service["deploy-name"] ?? service.name,
                            "eks-services": JSON.stringify(eksServices),
                            "eks-enabled": eksEnabled,
                            "eks-only": eksOnly,
                            envs,
                            "build": shouldDeploy,  // true - require success, false - allow failures
                            "deploy": shouldDeploy,
                          };
                      });

                      const result = JSON.stringify({
                        build: services,
                        deploy: services.filter(service => service.deploy),
                      });

                      console.log('result: ' + result);
                      return result;
                  result-encoding: string

    build-microservices-pr:
        name: Build ${{ matrix.service.name }}
        uses: ./.github/workflows/ecs-service-build-image.yml
        needs: [check-pr-type, install-cache-dependencies, create-release-tag, read-microservices]
        if: ${{ needs.read-microservices.outputs.build-matrix != '[]' && github.event_name == 'pull_request' && needs.check-pr-type.outputs.PR_TYPE != 'config' }}
        secrets: inherit
        permissions:
            actions: read
            id-token: write
            contents: read
            packages: read
            pull-requests: read
        strategy:
            fail-fast: false
            matrix:
                service: ${{ fromJson(needs.read-microservices.outputs.build-matrix) }}
        with:
            version: ${{ needs.create-release-tag.outputs.new-version }}
            service-name: ${{ matrix.service.name }}
            skip-health-check: ${{ matrix.service.skip-health-check }}
            is-nest-app: ${{ matrix.service.is-nest-app }}
            envs: ${{ matrix.service.envs }}
            branch-name: ${{ github.ref_name }}
            fully-formed-branch-name: ${{ github.ref }}
            github-event-name: ${{ github.event_name }}
            github-base-ref: ${{ github.base_ref }}
            allow-failure: ${{ !matrix.service.build }}

    install-cache-dependencies:
        name: Install and Cache Dependencies for All Builds
        needs: [check-pr-type]
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-5c-10g
        permissions:
            contents: read
            id-token: write
        env:
            pnpm-store-cache-name: pnpm-docker-cache-store
            pnpm-store-path-host: pnpm-docker-cache-store # reproducible-containers/buildkit-cache-dance v2.1.3 doesn't support relative paths
            pnpm-store-path-container: /var/cache/pnpm-store
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
            - name: Get Changed Files
              id: changed-files
              if: needs.check-pr-type.outputs.PR_TYPE == 'config'
              uses: tj-actions/changed-files@823fcebdb31bb35fdf2229d9f769b400309430d0 # v46.0.3
            - name: Setup node and dependencies for non-docker builds
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Run Config NX Executor
              if: needs.check-pr-type.outputs.PR_TYPE == 'config'
              timeout-minutes: 5
              env:
                  ALL_CHANGED_FILES: ${{ steps.changed-files.outputs.all_changed_files }}
              run: pnpm exec nx run config-plugin:config-build --affectedConfigFiles="$ALL_CHANGED_FILES" --skipTests --outputAffected=config-affected-output.json --excludedFileTypes=md,snap
            - name: Upload Config Affected Output
              id: upload-config-affected-output
              if: needs.check-pr-type.outputs.PR_TYPE == 'config'
              uses: actions/upload-artifact@6f51ac03b9356f520e9adb1b1b7802705f340c2b # v4.5.0
              with:
                  path: config-affected-output.json
                  name: ${{ github.sha }}-config-affected-output
                  retention-days: 1
                  overwrite: true

            - name: Set node version
              run: |
                  echo "NODE_VERSION=$(cat .nvmrc | cut -d. -f1)" >> $GITHUB_ENV

            - name: Configure NPM
              run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.ALL_PACKAGE_READ_TOKEN }}" >> ~/.npmrc

            - name: Cache pnpm store for Docker build
              uses: tespkg/actions-cache@a2e3f005921809689270625bc026d387f73017ae # v1
              id: pnpm-store-cache
              continue-on-error: true
              with:
                  bucket: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  path: ${{ env.pnpm-store-path-host }}
                  accessKey: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  secretKey: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  # We don't want to use the fallback cache because it will use too much space of the limited 10GB available
                  use-fallback: false
                  # The repo name is added to the key to prevent collisions between different repos
                  key: ${{ github.event.repository.name }}/${{ runner.os }}-build-${{ env.pnpm-store-cache-name }}-v${{ env.NODE_VERSION }}-${{ hashFiles('pnpm-lock.yaml') }}
                  restore-keys: |
                      ${{ github.event.repository.name }}/${{ runner.os }}-build-${{ env.pnpm-store-cache-name }}-v${{ env.NODE_VERSION }}-

            # we cannot force tespkg/actions-cache to save/overwrite the cache entry, so we need to overwrite it manually
            - name: Check PNPM cache and force upload if necessary
              uses: gacts/run-and-post-run@d803f6920adc9a47eeac4cb6c93dbc2e2890c684 # v1.4.2
              continue-on-error: true
              id: check-cache-dir
              env:
                  CACHE_DIR: ${{ env.pnpm-store-path-host }}
                  AWS_REGION: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  AWS_ACCESS_KEY_ID: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  AWS_SECRET_ACCESS_KEY: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  AWS_S3_ENDPOINT: https://${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  AWS_S3_BUCKET: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  AWS_S3_KEY: ${{ github.event.repository.name }}/${{ runner.os }}-build-${{ env.pnpm-store-cache-name }}-v${{ env.NODE_VERSION }}-${{ hashFiles('pnpm-lock.yaml') }}/cache.tzst
                  AWS_REQUEST_CHECKSUM_CALCULATION: WHEN_REQUIRED
                  AWS_RESPONSE_CHECKSUM_CALCULATION: WHEN_REQUIRED
              with:
                  run: |
                      echo "Size of PNPM Store before install: $(du -sh ${{env.pnpm-store-path-host}} || true)"
                      echo "PNPM Store contents before install: "
                      ls -lah ${{env.pnpm-store-path-host}} || true
                      # also sets valid_cache
                      bash .github/scripts/check-cache-dir.run.sh
                  post: |
                      echo "Size of PNPM Store after install: $(du -sh ${{env.pnpm-store-path-host}} || true)"
                      echo "PNPM Store contents after install: "
                      ls -lah ${{env.pnpm-store-path-host}} || true
                      bash .github/scripts/force-upload-cache.post.sh

            - name: Set up Docker Buildx
              if: steps.pnpm-store-cache.outputs.cache-hit == 'false' || steps.check-cache-dir.outputs.valid_cache == 'false'
              id: setup-buildx
              uses: Wandalen/wretry.action@ffdd254f4eaf1562b8a2c66aeaa37f1ff2231179 # v3.7.3
              with:
                  action: docker/setup-buildx-action@f95db51fddba0c2d1ec667646a06c2ce06100226
                  attempt_delay: 500
                  attempt_limit: 3

            - name: Configure ecr-cache AWS credentials (us-west-2)
              uses: aws-actions/configure-aws-credentials@f24d7193d98baebaeacc7e2227925dd47cc267f5 # v4.2.0
              with:
                  role-to-assume: arn:aws:iam::158160757288:role/gha-ecr-push
                  role-session-name: gha
                  aws-region: us-west-2

            - name: Login to Amazon ecr-cache (us-west-2)
              run: |
                  aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 158160757288.dkr.ecr.us-west-2.amazonaws.com

            - name: Inject pnpm-store into Docker
              uses: reproducible-containers/buildkit-cache-dance@653a570f730e3b9460adc576db523788ba59a0d7 # 3.2.0
              continue-on-error: true
              # skip if the cache was a hit
              if: steps.pnpm-store-cache.outputs.cache-hit == 'false' || steps.check-cache-dir.outputs.valid_cache == 'false'
              with:
                  builder: ${{ fromJSON(steps.setup-buildx.outputs.outputs).name }}
                  cache-map: |
                      {
                          "${{ env.pnpm-store-path-host }}": "${{ env.pnpm-store-path-container }}"
                      }

            - name: Build cache image
              uses: nick-fields/retry@7152eba30c6575329ac0576536151aca5a72780e # v3.0.0
              # skip if the cache was a hit
              if: steps.pnpm-store-cache.outputs.cache-hit == 'false' || steps.check-cache-dir.outputs.valid_cache == 'false'
              with:
                  max_attempts: 3
                  timeout_minutes: 25
                  command: |
                      BUILD_COMMAND="bash ./tools/docker/clickup/docker-cache-build.sh \
                          --progress=plain \
                          --platform=linux/arm64 \
                          --secret "id=npmrc,src=${HOME}/.npmrc" \
                          --iidfile /tmp/IMAGE_ID \
                          --build-arg PNPM_STORE_PATH="${{ env.pnpm-store-path-container }}""

                      $BUILD_COMMAND

                      test -e /tmp/IMAGE_ID || exit 1
                      echo "DOCKER_IMAGE_ID=$(cat /tmp/IMAGE_ID)" >> $GITHUB_ENV

    build-all-services-push:
        name: Build All Microservice Images
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-2c-4g
        needs: [check-pr-type, install-cache-dependencies, create-release-tag, read-microservices]
        if: ${{ needs.read-microservices.outputs.deploy-matrix != '[]' && github.event_name == 'push' && needs.check-pr-type.outputs.PR_TYPE != 'config' }}
        steps:
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - uses: time-loop/trigger-workflow-and-wait@ef3ce75d2cc6e31faa418b5516e80b98e3adb006
              timeout-minutes: 20
              with:
                  owner: time-loop
                  repo: clickup
                  github_token: ${{ steps.app-token.outputs.token }}
                  workflow_file_name: build-all-services-images.yml
                  ref: ${{ needs.create-release-tag.outputs.new-tag }}
                  wait_interval: ${{ vars.WORKFLOW_WAIT_INTERVAL_SECONDS }}
                  propagate_failure: true
                  trigger_workflow: true
                  wait_workflow: false
                  client_payload: |
                      {
                          "version": "${{ needs.create-release-tag.outputs.new-version }}",
                          "matrix": ${{ toJson(needs.read-microservices.outputs.deploy-matrix) }},
                          "branch-name": "${{ github.ref_name }}",
                          "fully-formed-branch-name": "${{ github.ref }}",
                          "github-event-name": "${{ github.event_name }}",
                          "github-base-ref": "${{ github.base_ref }}",
                          "previous-branch-latest-tag": "${{ needs.create-release-tag.outputs.previous-branch-latest-tag }}",
                          "current-branch-current-tag": "${{ needs.create-release-tag.outputs.old-tag }}",
                          "is-hotfix": "${{ needs.create-release-tag.outputs.is-hotfix }}"
                      }
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  filter: tree:0
            - uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5.5.0
              with:
                  python-version: '3.x'
                  cache: 'pip'
            - run: pip install -r .github/scripts/requirements.txt
            - name: Run Script
              run: python .github/scripts/provide-top-level-build.py ${{ needs.create-release-tag.outputs.new-version }}
              env:
                  ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
                  ENGPRODCI_CLICKUP_GITHUB_APP_ID: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID }}
                  ENGPRODCI_CLICKUP_GITHUB_APP_INSTALLATION_ID: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_INSTALLATION_ID }}
                  RETRY: ${{ github.ref_name == 'master' && '0' || '1' }}

    build-all-services-config-push:
        name: Build All Microservice Config Images
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-2c-4g
        needs: [check-pr-type, install-cache-dependencies, create-release-tag, read-microservices]
        if: ${{ needs.read-microservices.outputs.deploy-matrix != '[]' && github.event_name == 'push' && needs.check-pr-type.outputs.PR_TYPE == 'config' }}
        steps:
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - uses: time-loop/trigger-workflow-and-wait@ef3ce75d2cc6e31faa418b5516e80b98e3adb006
              timeout-minutes: 20
              with:
                  owner: time-loop
                  repo: clickup
                  github_token: ${{ steps.app-token.outputs.token }}
                  workflow_file_name: build-all-services-config-images.yml
                  ref: ${{ needs.create-release-tag.outputs.new-tag }}
                  wait_interval: ${{ vars.WORKFLOW_WAIT_INTERVAL_SECONDS }}
                  propagate_failure: true
                  trigger_workflow: true
                  wait_workflow: false
                  client_payload: |
                      {
                          "version": "${{ needs.create-release-tag.outputs.new-version }}",
                          "version-old": "${{ needs.create-release-tag.outputs.old-version }}",
                          "matrix": ${{ toJson(needs.read-microservices.outputs.deploy-matrix) }},
                          "branch-name": "${{ github.ref_name }}",
                          "fully-formed-branch-name": "${{ github.ref }}",
                          "github-event-name": "${{ github.event_name }}",
                          "github-base-ref": "${{ github.base_ref }}",
                          "github-sha": "${{ github.sha }}",
                          "run-id": "${{ github.run_id }}"
                      }

    deployment-ddl-precheck:
        name: Deployment DDL PreCheck
        # Conditional will ensure the job only runs on envs that have DDL precheck enabled
        # will update the conditional once other envs are ready and finalized
        if: ${{ contains(fromJSON(vars.DDL_PRECHECK_ENVS), github.ref_name) && github.event_name == 'push' }}
        secrets: inherit
        uses: ./.github/workflows/deployment-ddl-precheck.yml

    deploy-all-services-canary:
        name: Canary deploy (Reject button means skipping canary, but you can still proceed with all shards deploy)
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-2c-4g
        needs: [check-pr-type, create-release-tag, read-microservices, all-builds-complete, deployment-ddl-precheck]
        environment: 'deploy-canary-shards'
        if: ${{ !cancelled() && github.event_name == 'push' && needs.create-release-tag.result == 'success' && github.ref_name == 'production' }}
        outputs:
            approved: ${{ steps.set-outputs.approved }}
        steps:
            - name: Save Deploy Job Start Time
              id: save-deploy-job-start-time
              run: echo "deploy-job-start-time=$(date +%s)" >> $GITHUB_OUTPUT
            - name: Set Outputs
              id: set-outputs
              run: |
                  echo "approved=true" >> $GITHUB_OUTPUT
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - uses: time-loop/trigger-workflow-and-wait@ef3ce75d2cc6e31faa418b5516e80b98e3adb006
              timeout-minutes: 20
              with:
                  owner: time-loop
                  repo: clickup
                  github_token: ${{ steps.app-token.outputs.token }}
                  workflow_file_name: ecs-deploy-all-services.yml
                  ref: ${{ needs.create-release-tag.outputs.new-tag }}
                  wait_interval: ${{ vars.WORKFLOW_WAIT_INTERVAL_SECONDS }}
                  propagate_failure: true
                  trigger_workflow: true
                  wait_workflow: false
                  client_payload: |
                      {
                          "version": "${{ needs.create-release-tag.outputs.new-version }}",
                          "matrix": ${{ toJson(needs.read-microservices.outputs.deploy-matrix) }},
                          "branch-name": "${{ github.ref_name }}",
                          "fully-formed-branch-name": "${{ github.ref }}",
                          "github-event-name": "${{ github.event_name }}",
                          "original-sha": "${{ github.sha }}",
                          "is-canary": "true"
                      }
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  filter: tree:0
            - uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5.5.0
              with:
                  python-version: '3.x'
                  cache: 'pip'
            - run: pip install -r .github/scripts/requirements.txt
            - name: Provide Job Summary for Deploys
              timeout-minutes: 90
              run: python .github/scripts/display_deploy_summary.py ${{ needs.create-release-tag.outputs.new-version }} canary
              env:
                  ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
                  ENGPRODCI_CLICKUP_GITHUB_APP_ID: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID }}
                  ENGPRODCI_CLICKUP_GITHUB_APP_INSTALLATION_ID: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_INSTALLATION_ID }}
                  DD_API_KEY: ${{ secrets.ENGPROD_DATADOG_API_KEY }}
                  DD_APP_KEY: ${{ secrets.ENGPROD_DATADOG_APP_KEY }}
                  DD_SITE: datadoghq.com
                  BRANCH_NAME: ${{ github.ref_name }}
                  DEPLOY_SERVICES: ${{ needs.read-microservices.outputs.deploy-matrix }}
            - name: Send event to datadog
              if: ${{ (success() || failure()) && !cancelled() }}
              uses: time-loop/github-actions/dist/emit-event-to-datadog@emit-event-to-datadog+0.1.0
              with:
                  datadog-api-key: ${{ secrets.ENGPROD_DATADOG_API_KEY }}
                  datadog-app-key: ${{ secrets.ENGPROD_DATADOG_APP_KEY }}
                  title: Deployment Event
                  text: Deployment completed for ${{ needs.create-release-tag.outputs.new-version }}
                  tags: |-
                      version:${{ needs.create-release-tag.outputs.new-version }}
                      run-id:${{ github.run_id }}
                      env:${{ fromJSON('{"master":"qa","staging":"staging","production":"prod"}')[github.ref_name] }}
                      is-canary:true
                      pr-type:${{ needs.check-pr-type.outputs.PR_TYPE }}
            - name: Send All Shards Deploy duration to datadog
              if: ${{ (success() || failure()) && !cancelled() }}
              continue-on-error: true
              run: |
                  export END_TIME=$(date +%s)
                  bash .github/scripts/submit-dd-deploy-duration.sh
              env:
                  DD_API_KEY: ${{ secrets.ENGPROD_DATADOG_API_KEY }}
                  RELEASE_VERSION: ${{ needs.create-release-tag.outputs.new-version }}
                  ENV: ${{ fromJSON('{"master":"qa","staging":"staging","production":"prod"}')[github.ref_name] }}
                  START_TIME: ${{ steps.save-deploy-job-start-time.outputs.deploy-job-start-time }}
                  STATUS: ${{ job.status }}
                  RUN_ID: ${{ github.run_id }}-${{ github.run_attempt }}
                  WORKFLOW_NAME: ${{ needs.check-pr-type.outputs.PR_TYPE != 'config' && github.workflow || format('{0}-config', github.workflow) }}
                  DURATION_TYPE: canary

    deploy-all-services:
        name: All shards deploy
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-2c-4g
        needs:
            [
                check-pr-type,
                create-release-tag,
                read-microservices,
                all-builds-complete,
                deploy-all-services-canary,
                deployment-ddl-precheck,
            ]
        environment: ${{ (github.ref_name=='production' && 'deploy-all-shards') || 'deploy-no-approval' }}
        timeout-minutes: ${{ fromJSON('{"master":60,"staging":180,"production":180}')[github.ref_name] }}
        if: ${{ !cancelled() && github.event_name == 'push' && needs.create-release-tag.result == 'success' }}
        steps:
            - name: Save Deploy Job Start Time
              id: save-deploy-job-start-time
              run: echo "deploy-job-start-time=$(date +%s)" >> $GITHUB_OUTPUT
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - uses: time-loop/trigger-workflow-and-wait@ef3ce75d2cc6e31faa418b5516e80b98e3adb006
              timeout-minutes: 20
              with:
                  owner: time-loop
                  repo: clickup
                  github_token: ${{ steps.app-token.outputs.token }}
                  workflow_file_name: ecs-deploy-all-services.yml
                  ref: ${{ needs.create-release-tag.outputs.new-tag }}
                  wait_interval: ${{ vars.WORKFLOW_WAIT_INTERVAL_SECONDS }}
                  propagate_failure: true
                  trigger_workflow: true
                  wait_workflow: false
                  client_payload: |
                      {
                          "version": "${{ needs.create-release-tag.outputs.new-version }}",
                          "matrix": ${{ toJson(needs.read-microservices.outputs.deploy-matrix) }},
                          "branch-name": "${{ github.ref_name }}",
                          "fully-formed-branch-name": "${{ github.ref }}",
                          "github-event-name": "${{ github.event_name }}",
                          "original-sha": "${{ github.sha }}",
                          "is-canary": "false"
                      }
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  filter: tree:0
            - uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5.5.0
              with:
                  python-version: '3.x'
                  cache: 'pip'
            - run: pip install -r .github/scripts/requirements.txt
            - name: Provide Job Summary for Deploys
              timeout-minutes: 90
              run: python .github/scripts/display_deploy_summary.py ${{ needs.create-release-tag.outputs.new-version }} all_shards
              env:
                  ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
                  ENGPRODCI_CLICKUP_GITHUB_APP_ID: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID }}
                  ENGPRODCI_CLICKUP_GITHUB_APP_INSTALLATION_ID: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_INSTALLATION_ID }}
                  DD_API_KEY: ${{ secrets.ENGPROD_DATADOG_API_KEY }}
                  DD_APP_KEY: ${{ secrets.ENGPROD_DATADOG_APP_KEY }}
                  DD_SITE: datadoghq.com
                  BRANCH_NAME: ${{ github.ref_name }}
                  DEPLOY_SERVICES: ${{ needs.read-microservices.outputs.deploy-matrix }}
            - name: Send event to datadog
              if: ${{ (success() || failure()) && !cancelled() }}
              uses: time-loop/github-actions/dist/emit-event-to-datadog@emit-event-to-datadog+0.1.0
              with:
                  datadog-api-key: ${{ secrets.ENGPROD_DATADOG_API_KEY }}
                  datadog-app-key: ${{ secrets.ENGPROD_DATADOG_APP_KEY }}
                  title: Deployment Event
                  text: Deployment completed for ${{ needs.create-release-tag.outputs.new-version }}
                  tags: |-
                      version:${{ needs.create-release-tag.outputs.new-version }}
                      run-id: ${{ github.run_id }}
                      env:${{ fromJSON('{"master":"qa","staging":"staging","production":"prod"}')[github.ref_name] }}
                      is-canary:false
                      pr-type:${{ needs.check-pr-type.outputs.PR_TYPE }}
            - name: Send All Shards Deploy duration to datadog
              if: ${{ (success() || failure()) && !cancelled() }}
              continue-on-error: true
              run: |
                  export END_TIME=$(date +%s)
                  bash .github/scripts/submit-dd-deploy-duration.sh
              env:
                  DD_API_KEY: ${{ secrets.ENGPROD_DATADOG_API_KEY }}
                  RELEASE_VERSION: ${{ needs.create-release-tag.outputs.new-version }}
                  ENV: ${{ fromJSON('{"master":"qa","staging":"staging","production":"prod"}')[github.ref_name] }}
                  START_TIME: ${{ steps.save-deploy-job-start-time.outputs.deploy-job-start-time }}
                  STATUS: ${{ job.status }}
                  RUN_ID: ${{ github.run_id }}-${{ github.run_attempt }}
                  WORKFLOW_NAME: ${{ needs.check-pr-type.outputs.PR_TYPE != 'config' && github.workflow || format('{0}-config', github.workflow) }}
                  DURATION_TYPE: all_shards

    # We add this job so that we can make the workflow a required check in the branch protection settings
    deploy-completed:
        name: ClickUp ECS deploy completed
        needs:
            [
                check-pr-type,
                create-release-tag,
                build-monolith-pr,
                build-microservices-pr,
                build-all-services-push,
                build-all-services-config-push,
                deploy-all-services,
            ]
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        if: always() # Always run, so we never skip this required check
        outputs:
            branch-name: ${{ github.ref_name }}
        steps:
            - name: Checkout repo
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  filter: tree:0
            - name: Note Pipeline Success
              # TODO: We should do something useful here in the future. DD events?
              run: |
                  msgPrefix="Deploy #${{ github.run_id }} finished"
                  failure="${{ contains(needs.*.result, 'failure') }}"
                  cancelled="${{ contains(needs.*.result, 'cancelled') }}"
                  echo "Previous jobs failure: ${failure}"
                  echo "Previous jobs cancelled: ${cancelled}"
                  if [[ "$failure" != "true" ]] && [[ "$cancelled" != "true" ]]; then
                    echo "${msgPrefix}, all jobs succeeded."
                    exit 0
                  else
                    echo "${msgPrefix}, but with failures. Check left sidebar summary to see which jobs failed."
                    exit 1
                  fi
            - name: Send Full Deploy Pipeline duration to datadog
              if: ${{ (success() || failure()) && !cancelled() && github.event_name == 'push' }}
              continue-on-error: true
              run: |
                  export END_TIME=$(date +%s)
                  bash .github/scripts/submit-dd-deploy-duration.sh
              env:
                  DD_API_KEY: ${{ secrets.ENGPROD_DATADOG_API_KEY }}
                  RELEASE_VERSION: ${{ needs.create-release-tag.outputs.new-version }}
                  ENV: ${{ fromJSON('{"master":"qa","staging":"staging","production":"prod"}')[github.ref_name] }}
                  START_TIME: ${{ needs.create-release-tag.outputs.deploy-pipeline-start-time }}
                  STATUS: ${{ needs.deploy-all-services.result }}
                  RUN_ID: ${{ github.run_id }}-${{ github.run_attempt }}
                  WORKFLOW_NAME: ${{ needs.check-pr-type.outputs.PR_TYPE != 'config' && github.workflow || format('{0}-config', github.workflow) }}
                  DURATION_TYPE: full_pipeline
    api-regression-tests:
        name: API Regression Tests
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-1c-2g
        needs: [deploy-completed]
        if: |
            always() &&
            github.event_name == 'push' &&
            (github.ref_name == 'master' || github.ref_name == 'staging' || github.ref_name == 'production')
        env:
            DEPLOY_ENV: ${{ github.ref_name == 'master' && 'usQa' || github.ref_name == 'staging' && 'usStaging' || 'globalProd' }}
        steps:
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - name: Debug Values
              run: |
                  echo "=== Debug Values ==="
                  echo "Branch name: ${{ github.ref_name }}"
                  echo "Event name: ${{ github.event_name }}"
                  echo "Deploy ENV: ${{ env.DEPLOY_ENV }}"
            - name: Create API regression test payload
              id: create-api-regression-test-yml-payload
              uses: actions/github-script@5ee2b97722aeebb4c5d5c867345a6745cd868065
              with:
                  script: |
                      const deployEnv = '${{ env.DEPLOY_ENV }}';
                      const testTags = '@critical';

                      const payload = {
                          "deploy-env": deployEnv,
                          "test-tags": testTags
                      };

                      console.log('Generated payload:', payload);
                      core.setOutput('payload', JSON.stringify(payload));

            - name: Kickoff Regression Tests
              uses: henryjw/trigger-workflow-and-wait@ef3ce75d2cc6e31faa418b5516e80b98e3adb006
              with:
                  owner: time-loop
                  repo: clickup
                  github_token: ${{ steps.app-token.outputs.token }}
                  workflow_file_name: api-regression-tests.yml
                  ref: ${{ github.ref_name }}
                  wait_interval: ${{ vars.WORKFLOW_WAIT_INTERVAL_SECONDS }}
                  trigger_workflow: true
                  wait_workflow: false
                  client_payload: ${{ steps.create-api-regression-test-yml-payload.outputs.payload }}
