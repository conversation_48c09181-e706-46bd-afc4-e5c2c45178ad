# Lints all code on a Pull request and will commit changes if lint issues can be fixed automatically with eslints --fix flag

name: Lint

on:
    pull_request:
        branches:
            - master
            - staging
            - production

permissions:
    checks: write
    contents: write

jobs:
    lint:
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-amd64-4c-8g

        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  token: ${{ secrets.MACHINE_USER_GITHUB_TOKEN }}

            - uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: true
                  nx-main-branch: master
                  nx-error-on-no-successful-workflow: false
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}

            - name: Configure NPM
              run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.ALL_PACKAGE_READ_TOKEN }}" >> ~/.npmrc

            - name: Install Prettier
              run: pnpm ci:install --ignore-scripts --no-optional

            - name: Lint
              uses: wearerequired/lint-action@548d8a7c4b04d3553d32ed5b6e91eb171e10e7bb # v2.3.0
              with:
                  prettier: true
                  prettier_extensions: ts,js,yml,json,md
                  auto_fix: true
