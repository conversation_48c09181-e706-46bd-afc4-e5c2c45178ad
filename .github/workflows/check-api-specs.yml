name: Check API Specs

on:
    pull_request:
        types:
            - opened
            - labeled
            - unlabeled
            - synchronize
            - reopened
        branches:
            - master
            - staging
            - production
    push:
        branches:
            - master
            - staging
            - production

env:
    nx-cache-version: 1
    SUPPRESS_NO_CONFIG_WARNING: 'true'
    NXCACHE_AWS_DISABLE: true # we default to disabling it so steps can opt in.
    NXCACHE_AWS_ACCESS_KEY_ID: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
    NXCACHE_AWS_SECRET_ACCESS_KEY: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
    NXCACHE_AWS_REGION: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
    NXCACHE_AWS_BUCKET: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME_NX }}
    NXCACHE_AWS_ENDPOINT: https://${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
    NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}
    NX_BRANCH: ${{ github.event.number || github.ref_name }}
    NX_CI_EXECUTION_ID: ${{ github.sha }}-${{ github.run_attempt > 1 && format('{0}-{1}', github.run_id, github.run_attempt) || '' }}
    NX_CI_EXECUTION_ENV: 'Generate Swagger Files'

jobs:
    check-pr-type:
        name: Check PR Type
        continue-on-error: true
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        permissions:
            actions: write
            contents: write
            pull-requests: write
        steps:
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
            - name: Get PR Type
              uses: time-loop/github-actions/dist/get-pr-type@get-pr-type+0.8.0
              id: get-pr-type
              with:
                  github-app-id: ${{ secrets.CLICKUP_GITHUB_BOT_APP_ID }}
                  github-private-key: ${{ secrets.CLICKUP_GITHUB_BOT_PRIVATE_KEY }}
                  github-token: ${{ steps.app-token.outputs.token }}
            - name: Echo PR Type
              run: |
                  echo 'PR Type: ${{ steps.get-pr-type.outputs.type }}'
            - name: Exit Workflow
              uses: ./.github/actions/cancel-workflow
              if: ${{ steps.get-pr-type.outputs.type == 'config' }}
              with:
                  GITHUB_TOKEN: ${{ steps.app-token.outputs.token }}

    clear-changes-report:
        name: Clear Changes Report
        needs: [check-pr-type]
        if: github.event_name == 'pull_request'
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        permissions:
            pull-requests: write
        steps:
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - name: Find previous comment
              uses: peter-evans/find-comment@3eae4d37986fb5a8592848f6a574fdf654e61f9e # v3.1.0
              id: fc
              with:
                  token: ${{ steps.app-token.outputs.token }}
                  issue-number: ${{ github.event.pull_request.number }}
                  comment-author: 'github-actions[bot]'
                  body-includes: Breaking Changes in Public API Specification
            - name: Delete comment
              if: ${{ steps.fc.outputs.comment-id != '' }}
              uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
              with:
                  github-token: ${{ steps.app-token.outputs.token }}
                  script: |
                      github.rest.issues.deleteComment({
                        owner: context.repo.owner,
                        repo: context.repo.repo,
                        comment_id: ${{ steps.fc.outputs.comment-id }},
                      })

    generate-swagger-files:
        name: Generate Swagger Files
        needs: [check-pr-type]
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-4c-16g
        permissions:
            contents: read
            actions: read
            pull-requests: read
        env:
            jest-cache-name: cache-jest-cache-generate-swagger
        steps:
            - name: Get swagger JSON file changes
              if: github.event_name == 'pull_request'
              id: swagger-json-files-changed
              uses: time-loop/github-actions/dist/changed-pr-files@changed-pr-files+1.0.11
              with:
                  github-token: ${{ secrets.MACHINE_USER_GITHUB_TOKEN }}
                  files: |
                      tools/openapi-generator/swagger-files/*.json
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  token: ${{ secrets.MACHINE_USER_GITHUB_TOKEN }}
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Restore Jest cache
              id: cache-jest
              uses: tespkg/actions-cache@cba095d7af782a955b8f4fa13396fbf0ab62bd4b # v1.7.1
              timeout-minutes: 1
              continue-on-error: true
              with:
                  bucket: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  path: .jest-cache
                  accessKey: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  secretKey: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  use-fallback: false
                  key: ${{ runner.os }}-test-${{ env.jest-cache-name }}-${{ github.ref_name }}-${{ github.sha }}
                  restore-keys: |
                      ${{ runner.os }}-test-${{ env.jest-cache-name }}-${{ github.ref_name }}
                      ${{ runner.os }}-test-${{ env.jest-cache-name }}-${{ github.base_ref }}-${{ github.event.pull_request.base.sha }}
                      ${{ runner.os }}-test-${{ env.jest-cache-name }}-${{ github.base_ref }}
            - name: Generate Swagger json
              run: pnpm run ci:swagger:generate
              env:
                  NXCACHE_AWS_DISABLE: false
            - name: Generate service clients
              continue-on-error: true
              env:
                  CHANGED_SWAGGER_JSON_FILES: ${{ steps.swagger-json-files-changed.outputs.changed-files-matching-filter }}
              run: pnpm run ci:service-client:generate
            - name: Print changed files
              run: git status --porcelain
            - name: Push Swagger + generated service client changes
              id: auto-commit
              timeout-minutes: 5
              uses: stefanzweifel/git-auto-commit-action@8621497c8c39c72f3e2a999a26b4ca1b5058a842 # v5.0.1
              continue-on-error: true
              with:
                  commit_message: 'build(bot): generated files changed'
                  commit_options: '--no-verify'
                  file_pattern: 'libs/*/service/client/* tools/openapi-generator/swagger-files/*.json tools/openapi-generator/swagger-files/*.yaml'
                  commit_user_name: github-actions[bot]
                  commit_user_email: github-actions[bot]@users.noreply.github.com
            - name: Complete the Nx Cloud distributed CI run
              if: always()
              continue-on-error: true
              run: npx --yes nx-cloud complete-ci-run
            - name: Cancel workflow if changes have been detected
              if: steps.auto-commit.outputs.changes_detected == 'true'
              run: exit 1

    should-check-specs:
        name: Should Check Specs
        needs: [generate-swagger-files]
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        outputs:
            changes: ${{ steps.find-changed-specs.outputs.changes }}
        steps:
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
            - name: Find changed specs
              id: find-changed-specs
              uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 # v3.0.2
              with:
                  token: ${{ steps.app-token.outputs.token }}
                  filters: |
                      public-api:
                          - 'tools/openapi-generator/swagger-files/gateway-service-public-api-swagger.json'

    generate-public-api-changes-report:
        name: Generate Public API Changes Report
        needs: [should-check-specs]
        if: github.event_name == 'pull_request' && contains(fromJson(needs.should-check-specs.outputs.changes), 'public-api')
        uses: ./.github/workflows/generate-api-changes-report.yml
        with:
            spec-name: gateway-service-public-api-swagger
            spec-title: Public API

    check-public-api-breaking-changes:
        name: Check Public API Breaking Changes
        needs: [generate-public-api-changes-report]
        if: github.event_name == 'pull_request'
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        steps:
            - name: Check for breaking changes
              if: |
                  always() &&
                  needs.generate-public-api-changes-report.outputs.has-breaking-changes == 'true' &&
                  !contains(github.event.pull_request.labels.*.name, 'allow api breaking changes')
              run: |
                  echo "Breaking changes detected and 'allow api breaking changes' label is not set"
                  exit 1

    check-public-api-linting:
        name: Check Public API Linting
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-2c-4g
        needs: [should-check-specs]
        if: github.event_name == 'pull_request' && contains(fromJson(needs.should-check-specs.outputs.changes), 'public-api')
        steps:
            - name: Checkout the repository
              uses: actions/checkout@cbb722410c2e876e24abbe8de2cc27693e501dcb
            - name: Run Spectral
              uses: stoplightio/spectral-action@aec81028b28c9dc3de7b89bc25f4239bacfe9c19
              with:
                  file_glob: 'tools/openapi-generator/swagger-files/gateway-service-public-api-swagger.json'
                  spectral_ruleset: 'apps/gateway-service/src/openapi/.publicapi.spectral.yaml'

    validate-check-api-specs-success:
        name: Validate Check-API-Specs Success
        needs: [check-public-api-linting, check-public-api-breaking-changes, generate-swagger-files]
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        if: always()
        steps:
            - name: Exit with failure if any dependent jobs failed or were cancelled
              if: contains(needs.*.result, 'failure')
              run: exit 1
