name: jest-coverage
on:
    workflow_dispatch: # Allows manual triggering of the workflow
        inputs:
            services_for_coverage:
                default: 'websocket-distributor-service, websocket-ovm-consumer'
                required: false
                type: string

env:
    PGUSER: postgres
    NODE_OPTIONS: '--max-old-space-size=7175 --dns-result-order=ipv4first'
    DD_API_KEY: ${{ secrets.DD_API_KEY }}
    DD_CIVISIBILITY_AGENTLESS_ENABLED: true
    DD_ENV: ci
    nx-cache-version: 1
    SUPPRESS_NO_CONFIG_WARNING: 'true'
    NXCACHE_AWS_DISABLE: true # we default to disabling it so steps can opt in.
    NXCACHE_AWS_ACCESS_KEY_ID: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
    NXCACHE_AWS_SECRET_ACCESS_KEY: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
    NXCACHE_AWS_REGION: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
    NXCACHE_AWS_BUCKET: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME_NX }}
    NXCACHE_AWS_ENDPOINT: https://${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
    NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}
    NX_CI_EXECUTION_ID: ${{ github.sha }}-${{ github.run_attempt > 1 && format('{0}-{1}', github.run_id, github.run_attempt) || '' }}
    NX_CI_EXECUTION_ENV: 'jest-coverage'

jobs:
    nx-start:
        name: nx-start
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-8c-32g
        steps:
            - run: echo ${GITHUB_REF##*/}
            - run: echo $GITHUB_REF
            - run: echo $GITHUB_REF_NAME
            - name: Start nx cloud (nx agents)
              # Continue if there is a network error from the nx api when starting the agent
              continue-on-error: true
              env:
                  ALL_PACKAGE_READ_TOKEN: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}
                  NX_CI_EXECUTION_ENV: 'Build-Test DTE'
                  NX_COMMIT_SHA: ${{ github.sha }}
                  NX_COMMIT_REF: ${{ github.ref }}
                  NX_CLOUD_API: https://clickup.gc.ent.nx.app
                  NX_GH_EVENT_DETECTION: true
              run: echo "{}" > nx.json && npx nx-cloud@latest start-ci-run --distribute-on=".nx/workflows/distribution-config.yaml" --stop-agents-after="test" --stop-agents-on-failure=false --with-env-vars="ALL_PACKAGE_READ_TOKEN,DD_API_KEY,DD_CIVISIBILITY_AGENTLESS_ENABLED,DD_ENV,DD_SERVICE,GITHUB_ACTION,GITHUB_SERVER_URL,GITHUB_RUN_ID,GITHUB_RUN_NUMBER,GITHUB_RUN_ATTEMPT,GITHUB_WORKFLOW,GITHUB_WORKSPACE,GITHUB_REPOSITORY,GITHUB_SHA,GITHUB_HEAD_REF,GITHUB_REF,GITHUB_JOB"
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  node-version: '22'
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: true
                  nx-main-branch: master
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Initialize the Nx Cloud distributed CI run and stop agents when the build tasks are done
              run: pnpm exec nx-cloud start-ci-run --distribute-on="manual" --require-explicit-completion --stop-agents-after="test" --stop-agents-on-failure=false

    base-install-dependencies:
        name: base-install-dependencies
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        permissions:
            contents: read
            actions: read
        outputs:
            run-attempt: ${{ github.run_attempt }}
        steps:
            - run: echo ${GITHUB_BASE_REF##*/}
            - run: echo $GITHUB_BASE_REF
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  ref: master
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: false
                  nx-main-branch: master
                  nx-error-on-no-successful-workflow: false
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}

    base-coverage:
        name: base-coverage
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-4c-16g
        needs: [nx-start, base-install-dependencies]
        permissions:
            contents: read
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
              with:
                  ref: master # hard-code to master while testing workflow, ideally this is the base_ref based on upstream branch.
            - name: Set build hash
              id: package-build-hash
              env:
                  HASH: ${{ hashFiles('pnpm-lock.yaml', 'src', 'tsconfig.json', 'tsconfig.base.json', 'tools/esbuild/src') }}
              run: echo "hash=${HASH}" >> $GITHUB_OUTPUT
            - name: Restore build if rebuild not needed
              id: cache-build
              uses: tespkg/actions-cache@cba095d7af782a955b8f4fa13396fbf0ab62bd4b # v1.7.1
              timeout-minutes: 1
              continue-on-error: true
              env:
                  cache-name: cache-build
              with:
                  bucket: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  path: dist
                  accessKey: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  secretKey: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  use-fallback: false
                  key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ steps.package-build-hash.outputs.hash }}
            - name: Setup node and dependencies
              # This condition is the same as for the `Run build` step. No need to run if the build is going to be skipped
              if: ${{ steps.cache-build.outputs.cache-hit != 'true' }}
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Run commands in parallel
              id: run
              timeout-minutes: 60
              env:
                  DD_API_KEY: ${{ secrets.DD_API_KEY }}
                  DD_CIVISIBILITY_AGENTLESS_ENABLED: true
                  DD_ENV: ci
                  NX_DISABLE_DB: true
                  NX_PLUGIN_NO_TIMEOUTS: true
              run: |
                  set -e
                  pids=()
                  # initialize an associative array to store PIDs and their associated commands
                  declare -A pid_to_service
                  COVERAGE_BLOB=""
                  IFS=',' read -r -a SERVICES <<< "${{ inputs.services_for_coverage }}"

                  # function to run commands and store the PID
                  function run_coverage() {
                      local service=$1
                      echo "run_coverage $service"
                      # don't use nx cache since the coverage file isn't generated if cache hit occurs
                      pnpm nx test ${service} --coverage --coverageReporters='json-summary' --skip-nx-cache &  # run the command in the background
                      pid=$!      # get the PID of the background process
                      echo "Running coverage for ${service} at PID: $pid"
                      pids+=($pid)  # store the PID of the background process

                      pid_to_service[$pid]="${service}"  # store the service associated with the PID
                  }

                  function log_coverage() {
                      local service=$1
                      echo "log_coverage $service"
                      COVERAGE=$(cat coverage/apps/${service}/coverage-summary.json | jq "{\"${service}\": .total.lines}")
                      COVERAGE_BLOB="${COVERAGE_BLOB}${COVERAGE}"
                  }

                  # get coverage
                  for i in "${!SERVICES[@]}"; do
                      run_coverage ${SERVICES[$i]}
                  done

                  # wait for all background processes to finish
                  exit_code=0
                  for pid in ${pids[*]}; do
                      if ! wait $pid; then
                          echo "Process with PID $pid failed. Service: ${pid_to_service[$pid]}"  # Print a helpful error message
                          exit_code=1 # exit with an error status if any process fails
                      fi
                  done

                  # log coverage
                  for i in "${!SERVICES[@]}"; do
                      log_coverage ${SERVICES[$i]}
                  done

                  COVERAGE_FOR_ALL_SERVICES=$(echo "${COVERAGE_BLOB}" | jq -sc 'add | tojson')
                  echo "coverage=${COVERAGE_FOR_ALL_SERVICES}"
                  echo "coverage=${COVERAGE_FOR_ALL_SERVICES}" >> "$GITHUB_OUTPUT"

                  exit $exit_code
        outputs:
            coverage: ${{ fromJSON(steps.run.outputs.coverage) }}

    install-dependencies:
        name: install-dependencies
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        permissions:
            contents: read
            actions: read
        outputs:
            run-attempt: ${{ github.run_attempt }}
        steps:
            - run: echo ${GITHUB_REF##*/}
            - run: echo $GITHUB_REF
            - run: echo $GITHUB_REF_NAME
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  ref: ${{ github.ref_name }}
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: false
                  nx-main-branch: master
                  nx-error-on-no-successful-workflow: false
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}

    coverage:
        name: coverage
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-4c-16g
        needs: [nx-start, install-dependencies]
        permissions:
            contents: read
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
              with:
                  ref: ${{ github.ref_name }}
            - name: Set build hash
              id: package-build-hash
              env:
                  HASH: ${{ hashFiles('pnpm-lock.yaml', 'src', 'tsconfig.json', 'tsconfig.base.json', 'tools/esbuild/src') }}
              run: echo "hash=${HASH}" >> $GITHUB_OUTPUT
            - name: Restore build if rebuild not needed
              id: cache-build
              uses: tespkg/actions-cache@cba095d7af782a955b8f4fa13396fbf0ab62bd4b # v1.7.1
              timeout-minutes: 1
              continue-on-error: true
              env:
                  cache-name: cache-build
              with:
                  bucket: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  path: dist
                  accessKey: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  secretKey: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  use-fallback: false
                  key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ steps.package-build-hash.outputs.hash }}
            - name: Setup node and dependencies
              # This condition is the same as for the `Run build` step. No need to run if the build is going to be skipped
              if: ${{ steps.cache-build.outputs.cache-hit != 'true' }}
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Run commands in parallel
              id: run
              timeout-minutes: 60
              env:
                  DD_API_KEY: ${{ secrets.DD_API_KEY }}
                  DD_CIVISIBILITY_AGENTLESS_ENABLED: true
                  DD_ENV: ci
                  NX_DISABLE_DB: true
                  NX_PLUGIN_NO_TIMEOUTS: true
              run: |
                  set -e
                  pids=()
                  # initialize an associative array to store PIDs and their associated commands
                  declare -A pid_to_service
                  COVERAGE_BLOB=""
                  IFS=',' read -r -a SERVICES <<< "${{ inputs.services_for_coverage }}"

                  # function to run commands and store the PID
                  function run_coverage() {
                      local service=$1
                      # don't use nx cache since the coverage file isn't generated if cache hit occurs
                      pnpm nx test ${service} --coverage --coverageReporters='json-summary' --skip-nx-cache &  # run the command in the background
                      pid=$!      # get the PID of the background process
                      echo "Running coverage for ${service} at PID: $pid"
                      pids+=($pid)  # store the PID of the background process

                      pid_to_service[$pid]="${service}"  # store the service associated with the PID
                  }

                  function log_coverage() {
                      local service=$1
                      COVERAGE=$(cat coverage/apps/${service}/coverage-summary.json | jq "{\"${service}\": .total.lines}")
                      COVERAGE_BLOB="${COVERAGE_BLOB}${COVERAGE}"
                  }

                  # get coverage
                  for i in "${!SERVICES[@]}"; do
                      run_coverage ${SERVICES[$i]}
                  done

                  # wait for all background processes to finish
                  exit_code=0
                  for pid in ${pids[*]}; do
                      if ! wait $pid; then
                          echo "Process with PID $pid failed. Service: ${pid_to_service[$pid]}"  # Print a helpful error message
                          exit_code=1 # exit with an error status if any process fails
                      fi
                  done

                  # log coverage
                  for i in "${!SERVICES[@]}"; do
                      log_coverage ${SERVICES[$i]}
                  done

                  COVERAGE_FOR_ALL_SERVICES=$(echo "${COVERAGE_BLOB}" | jq -sc 'add | tojson')
                  echo "coverage=${COVERAGE_FOR_ALL_SERVICES}"
                  echo "coverage=${COVERAGE_FOR_ALL_SERVICES}" >> "$GITHUB_OUTPUT"

                  exit $exit_code
        outputs:
            coverage: ${{ fromJSON(steps.run.outputs.coverage) }}

    calc-coverage:
        name: calc-coverage
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-4c-16g
        needs: [base-coverage, coverage]
        permissions:
            contents: read
        steps:
            - name: Calculate Coverage Change
              env:
                  COVERAGE_BASE: ${{ toJSON(needs.base-coverage.outputs.coverage) }}
                  COVERAGE: ${{ toJSON(needs.coverage.outputs.coverage) }}
              run: |
                  set -e
                  IFS=',' read -r -a SERVICES <<< "${{ inputs.services_for_coverage }}"

                  sudo apt update && sudo apt install bc -y

                  # calculate difference for service
                  calc_coverage_for_service() {
                        local service=$1
                        echo "calc_coverage_for_service $service"
                        COVERAGE_BASE_PCT=$(echo ${COVERAGE_BASE} | jq "fromjson | .\"${service}\" | .pct")
                        COVERAGE_PCT=$(echo ${COVERAGE} | jq "fromjson | .\"${service}\" | .pct")
                        GAP=$(echo "$COVERAGE_BASE_PCT - $COVERAGE_PCT" | bc)
                        echo "${service} has a coverage gap of $COVERAGE_BASE_PCT - $COVERAGE_PCT = $GAP percent"
                  }

                  # calc coverage
                  for i in "${!SERVICES[@]}"; do
                      calc_coverage_for_service ${SERVICES[$i]}
                  done

    nx-stop:
        name: Stop NX Non-DTE CI Run
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        permissions:
            contents: read
            actions: read
        needs: [calc-coverage]
        env:
            NX_CI_EXECUTION_ENV: 'Build-Test Non-DTE'
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Setup Node
              uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
            - name: Stop the CI run
              continue-on-error: true
              run: npx --yes nx-cloud complete-ci-run
