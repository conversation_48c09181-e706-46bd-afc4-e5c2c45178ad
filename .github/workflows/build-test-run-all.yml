# Runs all automated tests for the monolith and micro services e.g. unit tests, integration tests, type checking, linting, etc.
# to set baseline every day

name: Build-Test Run All
on:
    workflow_dispatch: # Allows manual triggering of the workflow
    schedule:
        - cron: '0 17 * * *' # Run every day at 9am UTC, which is 1am PST. The gist is to run the tests at a time when the CI is not busy

env:
    type-check-cache-version: 1
    PGUSER: postgres
    NODE_OPTIONS: '--max-old-space-size=7175 --dns-result-order=ipv4first'
    DD_API_KEY: ${{ secrets.DD_API_KEY }}
    DD_CIVISIBILITY_AGENTLESS_ENABLED: true
    DD_ENV: ci
    nx-cache-version: 1
    SUPPRESS_NO_CONFIG_WARNING: 'true'
    NXCACHE_AWS_DISABLE: true # we default to disabling it so steps can opt in.
    NXCACHE_AWS_ACCESS_KEY_ID: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
    NXCACHE_AWS_SECRET_ACCESS_KEY: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
    NXCACHE_AWS_REGION: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
    NXCACHE_AWS_BUCKET: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME_NX }}
    NXCACHE_AWS_ENDPOINT: https://${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
    NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}
    NX_BRANCH: ${{ github.event.number || github.ref_name }}
    NX_CI_EXECUTION_ID: ${{ github.sha }}-${{ github.run_attempt > 1 && format('{0}-{1}', github.run_id, github.run_attempt) || '' }}
    NX_CI_EXECUTION_ENV: 'Build-Test Non-DTE Run All'
    NX_DISABLE_NX_CACHE: true # This is needed for baseline testing

jobs:
    install-dependencies:
        name: Install Dependencies
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        permissions:
            contents: read
            actions: read
        outputs:
            run-attempt: ${{ github.run_attempt }}
        steps:
            - run: echo ${GITHUB_REF##*/}
            - run: echo $GITHUB_REF
            - run: echo $GITHUB_REF_NAME
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  # We need to fetch all branches and commits so that Nx affected has a base to compare against.
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: true
                  nx-main-branch: master
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  nx-error-on-no-successful-workflow: false

    nx-dte-main:
        name: Nx Cloud - Main Job
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-8c-32g
        if: vars.USE_BUILD_TEST_NX_DTE == 'true'
        env:
            NX_CI_EXECUTION_ENV: 'Build-Test DTE'
        steps:
            - run: echo ${GITHUB_REF##*/}
            - run: echo $GITHUB_REF
            - run: echo $GITHUB_REF_NAME
            - name: Start nx cloud (nx agents)
              if:
                  vars.NX_AGENTS == 'enabled'
                  # Continue if there is a network error from the nx api when starting the agent
              continue-on-error: true
              env:
                  DD_API_KEY: ${{ secrets.DD_API_KEY }}
                  DD_CIVISIBILITY_AGENTLESS_ENABLED: true
                  DD_ENV: ci
                  ALL_PACKAGE_READ_TOKEN: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}
                  NX_CI_EXECUTION_ENV: 'Build-Test DTE'
                  NX_COMMIT_SHA: ${{ github.sha }}
                  NX_COMMIT_REF: ${{ github.ref }}
                  NX_CLOUD_API: https://clickup.gc.ent.nx.app
                  NX_GH_EVENT_DETECTION: true
              run: echo "{}" > nx.json && npx nx-cloud@latest start-ci-run --distribute-on=".nx/workflows/distribution-config.yaml" ${{ env.NX_CLOUD_COMMON_ARGS }} --stop-agents-after="ci-e2e-test,test,lint,build,ci-shard-functional-test,ci-shard-integration-test,ci-shard-unit-test" --stop-agents-on-failure=false --with-env-vars="ALL_PACKAGE_READ_TOKEN,DD_API_KEY,DD_CIVISIBILITY_AGENTLESS_ENABLED,DD_ENV,DD_SERVICE,GITHUB_ACTION,GITHUB_SERVER_URL,GITHUB_RUN_ID,GITHUB_RUN_NUMBER,GITHUB_RUN_ATTEMPT,GITHUB_WORKFLOW,GITHUB_WORKSPACE,GITHUB_REPOSITORY,GITHUB_SHA,GITHUB_HEAD_REF,GITHUB_REF,GITHUB_JOB"
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
              with:
                  # We need to fetch all branches and commits so that Nx affected has a base to compare against.
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 10
              with:
                  node-version: '22'
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: true
                  nx-main-branch: master
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  nx-error-on-no-successful-workflow: false
            - name: Setup Java
              uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
              with:
                  distribution: 'corretto'
                  java-version: '21'
            - name: Setup Gradle
              uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1
            - name: Check out the default branch
              run: git branch --track main origin/master

            - name: Initialize the Nx Cloud distributed CI run and stop agents when the build tasks are done
              if: vars.NX_AGENTS != 'enabled'
              run: pnpm exec nx-cloud start-ci-run --distribute-on="manual" --require-explicit-completion --stop-agents-after="ci-e2e-test,test,lint,build,ci-shard-functional-test,ci-shard-integration-test,ci-shard-unit-test" --stop-agents-on-failure=false --with-env-vars="CHUNK_NUMBER,TOTAL_CHUNKS"
            - name: Restore type-check cache
              uses: time-loop/github-actions/dist/pull-request-cache@pull-request-cache+0.3.10
              with:
                  cache-name: type-check-cache-${{ env.type-check-cache-version }}-${{ hashFiles('test/tsconfig.json', 'tsconfig.json', 'tsconfig-nx-test-typecheck.json', 'tsconfig.base.json', 'package.json') }}
                  cache-path: ./dist/out-tsc/
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Run commands in parallel
              timeout-minutes: 60
              env:
                  DD_API_KEY: ${{ secrets.DD_API_KEY }}
                  DD_CIVISIBILITY_AGENTLESS_ENABLED: true
                  DD_ENV: ci
                  NX_CLOUD_DISTRIBUTED_EXECUTION: true
                  NX_CLOUD_DISTRIBUTED_EXECUTION_AGENT_COUNT: 20
                  NX_DISABLE_DB: true
                  NX_PLUGIN_NO_TIMEOUTS: true
              run: |
                  set -x
                  pids=()
                  # initialize an associative array to store PIDs and their associated commands
                  declare -A pid_to_command

                  # function to run commands and store the PID
                  function run_command() {
                    local command=$1
                    local required=${2:-true}
                    $command &  # run the command in the background
                    pid=$!      # get the PID of the background process
                    echo "Running command: $1 at PID: $pid"
                    pids+=($pid)  # store the PID of the background process
                    pid_to_command[$pid]="$1"  # store the command associated with the PID
                    pid_is_required[$pid]=$required # store whether the command is required
                  }

                  # Display DD env variables
                  echo "DD_CIVISIBILITY_AGENTLESS_ENABLED: $DD_CIVISIBILITY_AGENTLESS_ENABLED"
                  echo "DD_ENV: $DD_ENV"
                  echo "DD_SERVICE: $DD_SERVICE"
                  # Show NODE_OPTIONS
                  echo "NODE_OPTIONS: $NODE_OPTIONS"

                  # warm the affected graph but don't show the output to make it noisy
                  pnpm exec nx show projects > /dev/null

                  # list of commands to be run on main has env flag NX_CLOUD_DISTRIBUTED_EXECUTION set to false
                  run_command "pnpm exec nx-cloud record -- nx format:check --base=$NX_BASE --head=$NX_HEAD"

                  # list of commands to be run on agents
                  run_command "pnpm exec nx run-many -t test --exclude='app-template' --runInBand --coverage --parallel=4"
                  run_command "pnpm exec nx run-many -t gradle-test --parallel=4"
                  run_command "pnpm nx run-many -t build --exclude='gateway-*' --exclude='client' --exclude='nest-playground' --parallel 4"
                  run_command "pnpm exec nx run-many -t ci-shard-functional-test --runInBand"

                  # the following will only run on 16-core agents, hence the higher parallelism
                  run_command "pnpm exec nx run-many -t lint --parallel=64 --quiet"
                  run_command "pnpm exec nx run-many -t ci-e2e-test --parallel 7 --configuration=ci"
                  run_command "pnpm exec nx run-many -t ci-shard-integration-test --runInBand --parallel=5"
                  run_command "pnpm exec nx run-many -t ci-shard-unit-test --runInBand --coverage --parallel=4"

                  # run this on the main runner, as it will have compute that can be utilized while it's waiting for other runners to finish
                  run_command "pnpm exec nx run-many -t check-package-lockfile -t typecheck --parallel 1 --no-dte"

                  # wait for all background processes to finish
                  exit_code=0
                  for pid in ${pids[*]}; do
                      if ! wait $pid; then
                          if [ "${pid_is_required[$pid]}" = false ]; then
                              echo "Process with PID $pid failed, but it was not required. Command: ${pid_to_command[$pid]}"
                              continue
                          fi
                          echo "Process with PID $pid failed. Command: ${pid_to_command[$pid]}"  # Print a helpful error message
                          exit_code=1 # exit with an error status if any process fails
                      fi
                  done

                  exit $exit_code
              # Temporarily disabled until we can fix it failing: https://clickup.gc.ent.nx.app/cipes/67ecef50fd359f41ce8cd60e
            #            - name: Merge code coverage reports
            #              run: pnpm exec nx run-many -p monolith-test-jest -t ci-shard-unit-test--process-code-coverage,ci-shard-integration-test--process-code-coverage --no-dte
            - name:
                  Complete the Nx Cloud distributed CI run
                  # It's important that we always run this step, otherwise in the case of any failures in preceding non-Nx steps, the agents will keep running and waste billable minutes
              if: always()
              continue-on-error: true
              run: npx --yes nx-cloud complete-ci-run

    nx-dte-agents-4:
        name: NX Agent ${{ matrix.agent }} - 4 Core
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-4c-16g
        needs: [install-dependencies]
        if: vars.NX_AGENTS != 'enabled'
        strategy:
            matrix:
                agent: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]
        env:
            jest-cache-name: nx-agent-jest-cache-${{ matrix.agent }}-4-core
            esbuild-cache-name: nx-agent-esbuild-cache-${{ matrix.agent }}-4-core
            CLICKUP_ESBUILD_CACHE_DIR: /tmp/.esbuild-cache
            NX_CI_EXECUTION_ENV: 'Build-Test DTE'
        steps:
            - name: Check run attempt
              if: ${{ needs.install-dependencies.outputs.run-attempt != github.run_attempt }}
              run: |
                  echo 'To re-run this pipeline, please use "Re-run all jobs" instead of "Re-run failed jobs"'
                  exit 1
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Install pytest, ruff, and uv # https://github.com/actions/setup-python/blob/main/docs/advanced-usage.md#caching-packages
              run: |
                  pipx install pytest && pipx install ruff && pipx install uv
                  echo $HOME/.local/bin >> $GITHUB_PATH
            - uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5.5.0
              with:
                  python-version: '3.13'
            - name: Setup Java
              uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
              with:
                  distribution: 'corretto'
                  java-version: '21'
            - name: Setup Gradle
              uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1
            - run: go version
            - run: java --version
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  nx-error-on-no-successful-workflow: false
            - name: Pre Start Docker Compose
              shell: bash
              run: docker compose --profile=ci up --detach --quiet-pull &
            - name: Pull docker-compose images for --profile=ci
              run: docker compose --profile=ci pull --include-deps
              timeout-minutes: 5
              continue-on-error: true
            - name: Wait For Docker Compose To Be Ready
              uses: ./.github/actions/docker-compose-run
              with:
                  command: pnpm run ci:test:docker-compose:wait
            - name: Start Nx Agent ${{ matrix.agent }}
              timeout-minutes: 40
              env:
                  NXCACHE_AWS_DISABLE: false
                  AWS_REGION: us-west-2
                  AWS_ACCESS_KEY_ID: test
                  AWS_SECRET_ACCESS_KEY: test
                  # dns-result-order=ipv4first is needed to address a breaking change in node 17 where localhost resolves to ipv6 first instead of ipv4
                  # Somehow this makes testcontainers randomly flake in the CI when running these tests
                  NODE_OPTIONS: '--max-old-space-size=5120 --max_semi_space_size=128 --dns-result-order=ipv4first --experimental-global-webcrypto'
                  NX_AGENT_NAME: Agent ${{ matrix.agent }} - 4 Core
              run: pnpm exec nx-cloud start-agent --targets="!ci-e2e-test,!ci-shard-integration-test*,!ci-shard-unit-test*,!lint"

    nx-dte-agents-16:
        name: NX Agent ${{ matrix.agent }} - 16 Core
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-16c-64g
        needs: [install-dependencies]
        if: vars.NX_AGENTS != 'enabled'
        strategy:
            matrix:
                agent: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        env:
            jest-cache-name: nx-agent-jest-cache-${{ matrix.agent }}-16-core
            esbuild-cache-name: nx-agent-esbuild-cache-${{ matrix.agent }}-16-core
            CLICKUP_ESBUILD_CACHE_DIR: /tmp/.esbuild-cache
            NX_CI_EXECUTION_ENV: 'Build-Test DTE'
        steps:
            - name: Check run attempt
              if: ${{ needs.install-dependencies.outputs.run-attempt != github.run_attempt }}
              run: |
                  echo 'To re-run this pipeline, please use "Re-run all jobs" instead of "Re-run failed jobs"'
                  exit 1
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Pre Start Docker Compose
              shell: bash
              run: docker compose --profile=ci up --detach --quiet-pull &
            - name: Install pytest, ruff, and uv # https://github.com/actions/setup-python/blob/main/docs/advanced-usage.md#caching-packages
              run: |
                  pipx install pytest && pipx install ruff && pipx install uv
                  echo $HOME/.local/bin >> $GITHUB_PATH
            - uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5.5.0
              with:
                  python-version: '3.13'
            - name: Setup Java
              uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
              with:
                  distribution: 'corretto'
                  java-version: '21'
            - name: Setup Gradle
              uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1
            - run: go version
            - run: java --version
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  nx-error-on-no-successful-workflow: false
            - name: Pull docker-compose images for --profile=ci
              run: docker compose --profile=ci pull --include-deps
              timeout-minutes: 5
              continue-on-error: true
            - name: Wait For Docker Compose To Be Ready
              uses: ./.github/actions/docker-compose-run
              with:
                  command: pnpm run ci:test:docker-compose:wait
            - name: Start Nx Agent ${{ matrix.agent }}
              timeout-minutes: 40
              env:
                  NXCACHE_AWS_DISABLE: false
                  AWS_REGION: us-west-2
                  AWS_ACCESS_KEY_ID: test
                  AWS_SECRET_ACCESS_KEY: test
                  # dns-result-order=ipv4first is needed to address a breaking change in node 17 where localhost resolves to ipv6 first instead of ipv4
                  # Somehow this makes testcontainers randomly flake in the CI when running these tests
                  NODE_OPTIONS: '--max-old-space-size=4196 --max_semi_space_size=128 --dns-result-order=ipv4first --experimental-global-webcrypto'
                  NX_AGENT_NAME: Agent ${{ matrix.agent }} - 16 Core
              run: pnpm exec nx-cloud start-agent --targets="!ci-shard-functional-test*,!test"

    should-skip-monolith-tests:
        name: Should Skip Monolith Tests
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        permissions:
            pull-requests: read
            contents: read
        outputs:
            should-skip-monolith-tests: ${{ steps.check-generated-new-service.outputs.generated || (startsWith(github.head_ref, 'smartling-') && github.event.pull_request.user.login == 'smartling-github-connector[bot]') }}
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: check if PR is a new generated service
              id: check-generated-new-service
              if: contains(github.event.pull_request.labels.*.name, 'generated-new-service')
              run: echo "generated=true" >> $GITHUB_OUTPUT

    build:
        name: Build
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-4c-16g
        needs: [install-dependencies, should-skip-monolith-tests]
        permissions:
            contents: read
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Set build hash
              id: package-build-hash
              env:
                  HASH: ${{ hashFiles('pnpm-lock.yaml', 'src', 'tsconfig.json', 'tsconfig.base.json', 'tools/esbuild/src') }}
              run: echo "hash=${HASH}" >> $GITHUB_OUTPUT
            - name: Restore build if rebuild not needed
              id: cache-build
              uses: tespkg/actions-cache@cba095d7af782a955b8f4fa13396fbf0ab62bd4b # v1.7.1
              timeout-minutes: 1
              continue-on-error: true
              env:
                  cache-name: cache-build
              with:
                  bucket: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  path: dist
                  accessKey: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  secretKey: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  use-fallback: false
                  key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ steps.package-build-hash.outputs.hash }}
            - name: Setup node and dependencies
              # This condition is the same as for the `Run build` step. No need to run if the build is going to be skipped
              if: ${{ steps.cache-build.outputs.cache-hit != 'true' || needs.should-skip-monolith-tests.outputs.should-skip-monolith-tests != 'true' }}
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  nx-error-on-no-successful-workflow: false
            - name: Setup Java
              uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
              with:
                  distribution: 'corretto'
                  java-version: '21'
            - name: Setup Gradle
              uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1
            - name: Run build
              if: ${{ steps.cache-build.outputs.cache-hit != 'true' || needs.should-skip-monolith-tests.outputs.should-skip-monolith-tests != 'true' }}
              run: pnpm exec nx run @clickup/source:build
            - name: Zip everything
              run: tar --exclude='.git' -zcf "/tmp/build-$GITHUB_SHA.tar.gz" ./dist
            - name: Upload build.tar.gz
              uses: actions/upload-artifact@6f51ac03b9356f520e9adb1b1b7802705f340c2b # v4.5.0
              with:
                  name: build-${{ github.sha }}.tar.gz
                  path: /tmp/build-${{ github.sha }}.tar.gz
                  retention-days: 1
                  if-no-files-found: error

    public-api-test:
        name: Public API Test
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-4c-16g
        needs: [build, should-skip-monolith-tests]
        if: needs.should-skip-monolith-tests.outputs.should-skip-monolith-tests != 'true'
        permissions:
            contents: read
        env:
            NX_CI_EXECUTION_ENV: 'Build-Test Non-DTE'
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  nx-error-on-no-successful-workflow: false
            - name: Pre Start Docker Compose
              shell: bash
              run: pnpm run ci:test:docker-compose:up &
            - name: Wait For Docker Compose To Be Ready
              uses: ./.github/actions/docker-compose-run
              with:
                  command: pnpm run ci:test:docker-compose:wait
            - name: Download build.tar.gz
              uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
              with:
                  name: build-${{ github.sha }}.tar.gz
                  path: /tmp/
            - name: Unpack
              run: tar --overwrite --strip-components=1 -zxf "/tmp/build-$GITHUB_SHA.tar.gz"
            - name: Run Newman Public Api Integration Tests
              run: pnpm exec nx-cloud record -- pnpm run ci:integration-test:public-api
              env:
                  AWS_ACCESS_KEY_ID: ${{ secrets.TRAVISCI_ACCESS_KEY_ID }}
                  AWS_SECRET_ACCESS_KEY: ${{ secrets.TRAVISCI_SECRET_ACCESS_KEY }}

    workspace-migration-smoke-test:
        name: Workspace Migration Smoke Test
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-4c-16g
        # do not run unless the PR is targeting wms-master, wms-staging or wms-production base branch
        needs: [build]
        permissions:
            contents: read
            actions: read
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
              with:
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 10
              id: setup-node-and-dependencies
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: true
                  nx-main-branch: master
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  nx-error-on-no-successful-workflow: false
            - name: Start Docker Compose
              run: pnpm run ci:docker:wms-req:up
              timeout-minutes: 15
            - name: Download build.tar.gz
              uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
              with:
                  name: build-${{ github.sha }}.tar.gz
                  path: /tmp/
            - name: Unpack
              run: tar --overwrite --strip-components=1 -zxf "/tmp/build-$GITHUB_SHA.tar.gz"
            - name: Run workspace migration smoke tests
              run: pnpm run ci:test:workspace-migration:smoke
              env:
                  NXCACHE_AWS_DISABLE: false
                  AWS_REGION: us-west-2
                  AWS_ACCESS_KEY_ID: test
                  AWS_SECRET_ACCESS_KEY: test

    fvt-test:
        name: FVT Test
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-4c-16g
        needs: [build, should-skip-monolith-tests]
        if: needs.should-skip-monolith-tests.outputs.should-skip-monolith-tests != 'true'
        timeout-minutes: 10
        permissions:
            contents: read
        env:
            NX_CI_EXECUTION_ENV: 'Build-Test Non-DTE'
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  nx-error-on-no-successful-workflow: false
            - name: Pre Start Docker Compose
              shell: bash
              run: pnpm run ci:test:docker-compose:up &
            - name: Wait For Docker Compose To Be Ready
              uses: ./.github/actions/docker-compose-run
              with:
                  command: pnpm run ci:test:docker-compose:wait
            - name: Download Build Artifact
              uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
              with:
                  name: build-${{ github.sha }}.tar.gz
                  path: /tmp/
            - name: Unpack
              run: tar --overwrite --strip-components=1 -zxf "/tmp/build-$GITHUB_SHA.tar.gz"
            - name: Run Mocha Integration Tests
              uses: ./.github/actions/run-monolith-tests
              with:
                  timeout_minutes: 15
                  max_attempts: 1
                  command: pnpm exec nx-cloud record -- pnpm run ci:integration-test:mocha
                  aws_access_key_id: ${{ secrets.TRAVISCI_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.TRAVISCI_SECRET_ACCESS_KEY }}

    stop-non-dte-ci-run:
        name: Stop NX Non-DTE CI Run
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        if: vars.USE_BUILD_TEST_NX_DTE == 'true' && always()
        permissions:
            contents: read
            actions: read
        needs: [public-api-test, fvt-test]
        env:
            NX_CI_EXECUTION_ENV: 'Build-Test Non-DTE'
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Setup Node
              uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
            - name: Stop the CI run
              continue-on-error: true
              run: npx --yes nx-cloud complete-ci-run
    # Only sets the output to 'true' when all dependent tests have passed.
    validate-success:
        name: Validate Build-Test Success
        # Dependent on all test jobs
        needs: [nx-dte-main, should-skip-monolith-tests, fvt-test, public-api-test, workspace-migration-smoke-test]
        if: |
            (always() && !cancelled()) &&
            !contains(needs.*.result, 'failure') &&
            !contains(needs.*.result, 'cancelled')
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        outputs:
            success: ${{ steps.setoutput.outputs.success }}
        steps:
            - id: setoutput
              run: echo "success=true" >> $GITHUB_OUTPUT

    # Used so we only need to depend on one job from the Build-Test pipeline in GitHub Branch Protection Rules.
    # Helps minimize required maintenance when changing the pipeline.
    notify-success:
        name: Build-Test Pipeline Succeeded
        # Dependent on all test jobs
        needs: [validate-success]
        if: always() # Always run, so we never skip this required check
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        steps:
            - name: Note Pipeline Success
              # TODO: We should do something useful here in the future. DD events?
              run: |
                  msgPrefix="Build pipeline \#${{ github.run_number }} finished"
                  passed="${{ needs.validate-success.outputs.success == 'true' }}"
                  if [[ "$passed" == "true" ]]; then
                    echo "${msgPrefix}, artifact passed all tests."
                    exit 0
                  else
                    echo "${msgPrefix}, but with failures."
                    if [[ ${{ github.event_name }} == 'schedule' ]]; then
                      curl -X POST -H "Authorization: ${{ secrets.CLICKUP_API_TOKEN }}" -H "Content-Type: application/json" -d '{"content": "Build pipeline ${{ github.run_number }} finished, but with failures."}' https://api.clickup-stg.com/api/v2/view/${{ vars.CLICKUP_CHAT_ID_ENGPROD_PUBLIC }}/comment
                      exit 1
                    else
                      exit 1
                    fi
                  fi
