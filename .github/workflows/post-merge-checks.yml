# Runs a subset of the actions from Build-Test that should be ran for every merge to a main branch (linting,type checking,bumping of build budgets etc)

name: <PERSON><PERSON>ge checks
on:
    push:
        branches:
            - master
            - staging
            - production

concurrency:
    group: post-merge-${{ github.ref }}
    cancel-in-progress: false

env:
    NODE_OPTIONS: '--max-old-space-size=7175 --dns-result-order=ipv4first'
    DD_API_KEY: ${{ secrets.DD_API_KEY }}
    nx-cache-version: 1
    SUPPRESS_NO_CONFIG_WARNING: 'true'
    NXCACHE_AWS_DISABLE: true
    nxcache_aws_access_key_id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
    NXCACHE_AWS_SECRET_ACCESS_KEY: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
    NXCACHE_AWS_REGION: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
    NXCACHE_AWS_BUCKET: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME_NX }}
    NXCACHE_AWS_ENDPOINT: https://${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
    NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}
    CI_STRATEGY: build-test
    NX_BRANCH: ${{ github.event.number || github.ref_name }}
    NX_CI_EXECUTION_ID: ${{ github.sha }}-${{ github.run_attempt > 1 && format('{0}-{1}', github.run_id, github.run_attempt) || '' }}
    NX_CI_EXECUTION_ENV: 'Postmerge checks'

jobs:
    post-merge-main:
        name: Main Post Merge Job
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-amd64-8c-32g
        if: ${{ fromJSON(vars.RUN_POST_MERGE_CHECKS)[github.ref_name] == 'true' }}
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
              with:
                  # We need to fetch all branches and commits so that Nx affected has a base to compare against.
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: true
                  nx-error-on-no-successful-workflow: false
                  nx-main-branch: master
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}

            - name: Check out the default branch
              run: git branch --track main origin/master

            - name: Initialize the Nx Cloud distributed CI run and stop agents when the build tasks are done
              run: pnpm exec nx-cloud start-ci-run --distribute-on="manual" --require-explicit-completion --stop-agents-after="lint,typecheck" --stop-agents-on-failure=false --with-env-vars="CHUNK_NUMBER,TOTAL_CHUNKS"

            - name: Run commands in parallel
              timeout-minutes: 30
              env:
                  DISABLE_V8_COMPILE_CACHE: 1
                  NX_CLOUD_DISTRIBUTED_EXECUTION: true
                  NX_CLOUD_DISTRIBUTED_EXECUTION_AGENT_COUNT: 1
                  NX_DISABLE_DB: true
              run: |
                  set -x
                  pids=()
                  # initialize an associative array to store PIDs and their associated commands
                  declare -A pid_to_command

                  # function to run commands and store the PID
                  function run_command() {
                    local command=$1
                    local required=${2:-true}
                    $command &  # run the command in the background
                    pid=$!      # get the PID of the background process
                    echo "Running command: $1 at PID: $pid"
                    pids+=($pid)  # store the PID of the background process
                    pid_to_command[$pid]="$1"  # store the command associated with the PID
                    pid_is_required[$pid]=$required # store whether the command is required
                  }

                  # warm the affected graph but don't show the output to make it noisy
                  pnpm exec nx show projects > /dev/null

                  # list of commands to be run on main has env flag NX_CLOUD_DISTRIBUTED_EXECUTION set to false
                  # run_command "pnpm exec nx-cloud record -- nx format:check --base=$NX_BASE --head=$NX_HEAD"

                  # list of commands to be run
                  run_command "pnpm exec nx affected -t typecheck --parallel 1 --no-dte"
                  run_command "pnpm exec nx affected -t lint --parallel=20 --quiet"

                  # wait for all background processes to finish
                  exit_code=0
                  for pid in ${pids[*]}; do
                      if ! wait $pid; then
                          if [ "${pid_is_required[$pid]}" = false ]; then
                              echo "Process with PID $pid failed, but it was not required. Command: ${pid_to_command[$pid]}"
                              continue
                          fi
                          echo "Process with PID $pid failed. Command: ${pid_to_command[$pid]}"  # Print a helpful error message
                          exit_code=1 # exit with an error status if any process fails
                      fi
                  done

                  exit $exit_code
            - name:
                  Complete the Nx Cloud distributed CI run
                  # It's important that we always run this step, otherwise in the case of any failures in preceding non-Nx steps, the agents will keep running and waste billable minutes
              if: always()
              continue-on-error: true
              run: npx --yes nx-cloud complete-ci-run

    nx-dte-agents-16:
        name: NX Agent ${{ matrix.agent }} - 16 Core
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-amd64-16c-64g
        if: ${{ fromJSON(vars.RUN_POST_MERGE_CHECKS)[github.ref_name] == 'true' }}
        strategy:
            matrix:
                agent: [1]
        env:
            esbuild-cache-name: nx-agent-esbuild-cache-${{ matrix.agent }}-16-core
            CLICKUP_ESBUILD_CACHE_DIR: /tmp/.esbuild-cache
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Install python dependencies
              run: sudo apt update && sudo apt install -y libpq-dev
            - name: Install pytest, ruff, and uv # https://github.com/actions/setup-python/blob/main/docs/advanced-usage.md#caching-packages
              run: |
                  pipx install pytest && pipx install ruff && pipx install uv
                  echo $HOME/.local/bin >> $GITHUB_PATH
            - uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5.5.0
              with:
                  python-version: '3.13'
            - name: Setup Java
              uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
              with:
                  distribution: 'corretto'
                  java-version: '21'
            - name: Setup Gradle
              uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1
            - run: go version
            - run: java --version
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Start Nx Agent ${{ matrix.agent }}
              timeout-minutes: 20
              env:
                  NODE_OPTIONS: '--max-old-space-size=4196 --max_semi_space_size=128'
                  NX_AGENT_NAME: Agent ${{ matrix.agent }} - 16 Core
                  NXCACHE_AWS_DISABLE: false
              run: pnpm exec nx-cloud start-agent

    nest-build-budgets:
        name: Nest Build Budgets
        if: github.event_name == 'push' && github.ref_name == 'master'
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-amd64-8c-32g
        permissions:
            contents: read
        env:
            NXCACHE_AWS_DISABLE: false
            NODE_OPTIONS: '--max-old-space-size=16384 --dns-result-order=ipv4first'
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  # Check out the repo with the machine user token so that we can directly commit the changes to the protected branch with the EndBug/add-and-commit action
                  token: ${{ secrets.MACHINE_USER_GITHUB_TOKEN }}
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Update bundle budgets
              run: |
                  pnpm exec nx show projects > /dev/null
                  pnpm ci:budget:update:master
            - name: Commit and push bundle budget changes
              continue-on-error: true
              uses: EndBug/add-and-commit@a94899bca583c204427a224a7af87c02f9b325d5 # v9.1.4
              with:
                  default_author: github_actions
                  message: 'chore(bot): update budgets [ci skip]'
                  add: 'apps/**/.size-limit.json'
                  pull: '--no-rebase' # probably not needed, but addressing a breaking change in v8 of the action

    notify-on-failures:
        name: Post Merge Checks Notification
        needs: [post-merge-main, nest-build-budgets]
        if: always()
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        steps:
            - name: Note Pipeline Success
              id: check-status
              run: |
                  if [[ "${{ needs.post-merge-main.result || 'skipped' }}" != "failure" && "${{ needs.nest-build-budgets.result }}" != "failure" && "${{ needs.post-merge-main.result || 'skipped' }}" != "cancelled" && "${{ needs.nest-build-budgets.result }}" != "cancelled" ]]; then
                    echo "Post-merge checks completed successfully."
                    echo "status=success" >> $GITHUB_ENV
                  else
                    echo "Post-merge checks failed."
                    echo "status=failure" >> $GITHUB_ENV
                  fi
            - name: Get current run URL
              id: get_run_url
              run: echo "RUN_URL=${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}" >> $GITHUB_ENV
            - name: Mention PR Author on offending PR
              if: env.status == 'failure' && vars.ALERT_POST_MERGE_FAILURES == 'true'
              id: get-author
              env:
                  CLICKUP_API_TOKEN: ${{ secrets.CLICKUP_API_TOKEN }}
                  CLICKUP_CHAT_ID_ENG_BACKEND: ${{ vars.CLICKUP_CHAT_ID_ENG_BACKEND }}
              uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
              with:
                  result-encoding: string
                  script: |
                      const commit = await github.rest.repos.getCommit({
                        owner: context.repo.owner,
                        repo: context.repo.repo,
                        ref: context.sha,
                      });
                      const runUrl = process.env.RUN_URL;

                      const author = commit.data.author.login;
                      console.log(`Author: ${author}`);

                      const pulls = await github.rest.repos.listPullRequestsAssociatedWithCommit({
                        owner: context.repo.owner,
                        repo: context.repo.repo,
                        commit_sha: context.sha,
                      });

                      console.log(`Pulls: ${JSON.stringify(pulls.data, null, 2)}`);

                      const issue_number = pulls.data.length > 0 ? pulls.data[0].number : null;

                      console.log(`Issue Number: ${issue_number}`);

                      if (issue_number) {
                        await github.rest.issues.createComment({
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          issue_number,
                          body: `@${author} Post-merge checks failed. Run URL: ${runUrl}. Please take a look.`,
                        });
                      }
                      const clickupToken = process.env.CLICKUP_API_TOKEN;
                      const viewId = process.env.CLICKUP_CHAT_ID_ENG_BACKEND;
                      const pullRequestUrl = pulls.data[0].html_url;
                      const branchName = process.env.GITHUB_REF_NAME;
                      const commentText = `Post Merge checks failed on ${branchName}!\nPlease fix to avoid blocking others\nFailing PR: ${pullRequestUrl}\nAuthor: gh user: ${author}\nRun URL: ${runUrl}`;

                      const response = await fetch(`https://api.clickup-stg.com/api/v2/view/${viewId}/comment`, {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json',
                          'Authorization': `${clickupToken}`,
                        },
                        body: JSON.stringify({ comment_text: commentText }),
                      });

                      if (!response.ok) {
                        throw new Error(`Failed to post comment to ClickUp: ${response.statusText}`);
                      }

                      console.log('Comment posted to ClickUp successfully.');
