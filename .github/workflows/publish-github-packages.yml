# Automatically publishes new versions of packages to the GitHub packages registry when new changes are merged to master, so that the code can be used in other repos
# See the docs for this nx plugin for more info on how publishing from the CI works: https://github.com/jscutlery/semver
# See these docs for how to configure a package for publishing to the GitHub packages registry: https://staging.clickup.com/333/v/dc/ad-3597/ad-691924

name: Publish github packages

on:
    push:
        branches:
            - master
        paths:
            - 'libs/ai/service/client/**'
            - 'libs/attachment/service/client/**'
            - 'libs/docs/service/client/**'
            - 'libs/fp-jobs/service/client/**'
            - 'libs/field/service/client/**'
            - 'libs/inbox/service/client/**'
            - 'libs/ovm/object-version/**'
            - 'libs/task/service/client/**'
            - 'libs/user/service/client/**'
            - 'libs/workspace/service/client/**'
            - 'libs/whiteboards/service/client/**'
            - 'libs/whiteboards-v3/service/client/**'
            - 'libs/home-cards/service/client/**'
            - 'libs/comment/service/client/**'
            - 'libs/field/contracts/**'
            - '.github/workflows/publish-github-packages.yml'

    workflow_dispatch:
        inputs:
            lib-name:
                description: 'Library name to publish'
                required: true
                type: string

jobs:
    publish:
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-4c-8g
        permissions:
            contents: read
            actions: read
        steps:
            - name: Checkout current branch
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  # Use machine user token so we can publish the package.json version bump directly to the main branch
                  token: ${{ secrets.MACHINE_USER_GITHUB_TOKEN }}
            - name: Setup Git
              run: |
                  git config --local user.email "github-actions[bot]@users.noreply.github.com"
                  git config --local user.name "github-actions[bot]"
            - name: Is merge commit
              uses: time-loop/github-actions/dist/is-merge-commit@is-merge-commit+0.1.3
              id: is-merge-commit
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              if: ${{ !fromJSON(steps.is-merge-commit.outputs.is-merge-commit) || github.event_name == 'workflow_dispatch' }}
              with:
                  package-manager: pnpm
                  node-version-file: '.nvmrc'
                  install-arguments: '--ignore-scripts'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
            # We can't use the ALL_PACKAGE_READ_TOKEN npm token for publishing so we reset the npmrc and
            # then use the PROJEN_GITHUB_TOKEN for auth with github packages as it does have publishing access
            - name: GitHub Packages authorization for publish
              run: |
                  rm -f ~/.npmrc
                  echo '//npm.pkg.github.com/:_authToken=${{ secrets.PROJEN_GITHUB_TOKEN }}' >> ~/.npmrc
            - name: Publish
              # Skip on merge commits as this generally always marks commits as affected
              if: ${{ !fromJSON(steps.is-merge-commit.outputs.is-merge-commit) && github.event_name == 'push' }}
              run: pnpm nx affected --target=version --parallel=1 --base=HEAD~1
            - name: Manually publish individual lib
              if: github.event_name == 'workflow_dispatch'
              env:
                  LIB_NAME: ${{ inputs.lib-name }}
              run: pnpm nx version $LIB_NAME
            # semver plugin has pushing built in, but if new commits landed after it runs then the remote will reject it
            - name: Push tag
              # If a package fails to build, we should still push up the tags that were created already to prevent the workflow getting into a broken state
              if: ${{ always() && (!fromJSON(steps.is-merge-commit.outputs.is-merge-commit) || github.event_name == 'workflow_dispatch') }}
              run: |
                  git pull --no-rebase
                  git push --follow-tags
