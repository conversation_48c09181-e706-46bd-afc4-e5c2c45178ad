# Automatically concatenate individual DB migration files and commit them to the repo

name: DB Migrations Auto Fix

on:
    pull_request:
        branches:
            - master
            - staging
            - production
        paths:
            - 'src/scripts/migrations/*/*.sql'

permissions:
    checks: write
    contents: write

jobs:
    regen-db-migrations:
        runs-on: ubuntu-24.04
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  token: ${{ secrets.MACHINE_USER_GITHUB_TOKEN }}
            - uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: true
                  nx-main-branch: master
                  nx-error-on-no-successful-workflow: false
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Configure NPM
              run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.ALL_PACKAGE_READ_TOKEN }}" >> ~/.npmrc
            - name: Install Node packages
              run: pnpm ci:install --ignore-scripts --no-optional
            - name: Regenerate DB migration files
              run: ./src/scripts/migrations/regen-db-migrations.js
            - name: Commit regenerated DB migration files
              uses: stefanzweifel/git-auto-commit-action@8621497c8c39c72f3e2a999a26b4ca1b5058a842 # v5.0.1
              with:
                  commit_message: 'chore: Regenerate DB migration files'
                  file_pattern: 'src/scripts/*.sql'
              env:
                  GITHUB_TOKEN: ${{ secrets.MACHINE_USER_GITHUB_TOKEN }}
