# Runs a subset of the actions from Build-Test that should be ran for every merge to a main branch (linting,type checking,bumping of build budgets etc)

name: Code Quality
on:
    push:
        branches:
            - master
            - staging
            - production

env:
    NODE_OPTIONS: '--max-old-space-size=7175 --dns-result-order=ipv4first'
    DD_API_KEY: ${{ secrets.DD_API_KEY }}
    nx-cache-version: 1
    SUPPRESS_NO_CONFIG_WARNING: 'true'
    NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}
    NX_BRANCH: ${{ github.event.number || github.ref_name }}
    NX_CI_EXECUTION_ID: ${{ github.sha }}-${{ github.run_attempt > 1 && format('{0}-{1}', github.run_id, github.run_attempt) || '' }}
    NX_CI_EXECUTION_ENV: 'Code Quality Checks'

jobs:
    nx-dte-orchestrator:
        name: NX DTE Orchestrator
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-8c-32g
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
              with:
                  # We need to fetch all branches and commits so that Nx affected has a base to compare against.
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: true
                  nx-error-on-no-successful-workflow: false
                  nx-main-branch: master
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}

            - name: Check out the default branch
              run: git branch --track main origin/master

            - name: Initialize the Nx Cloud distributed CI run and stop agents when the build tasks are done
              run: pnpm exec nx-cloud start-ci-run --distribute-on="manual" --require-explicit-completion --stop-agents-after="madge-analysis" --stop-agents-on-failure=false --with-env-vars="ALL_PACKAGE_READ_TOKEN,DD_API_KEY,DD_CIVISIBILITY_AGENTLESS_ENABLED,DD_ENV,DD_SERVICE,GITHUB_ACTION,GITHUB_SERVER_URL,GITHUB_RUN_ID,GITHUB_RUN_NUMBER,GITHUB_RUN_ATTEMPT,GITHUB_WORKFLOW,GITHUB_WORKSPACE,GITHUB_REPOSITORY,GITHUB_SHA,GITHUB_HEAD_REF,GITHUB_REF,GITHUB_JOB,GITHUB_REF_NAME"

            - name: Run commands in parallel
              timeout-minutes: 20
              env:
                  NX_CLOUD_DISTRIBUTED_EXECUTION: true
                  NX_CLOUD_DISTRIBUTED_EXECUTION_AGENT_COUNT: 8
                  NX_DISABLE_DB: true
              run: |
                  set -x
                  pids=()
                  # initialize an associative array to store PIDs and their associated commands
                  declare -A pid_to_command

                  # function to run commands and store the PID
                  function run_command() {
                    local command=$1
                    local required=${2:-true}
                    $command &  # run the command in the background
                    pid=$!      # get the PID of the background process
                    echo "Running command: $1 at PID: $pid"
                    pids+=($pid)  # store the PID of the background process
                    pid_to_command[$pid]="$1"  # store the command associated with the PID
                    pid_is_required[$pid]=$required # store whether the command is required
                  }

                  # warm the affected graph but don't show the output to make it noisy
                  pnpm exec nx show projects > /dev/null

                  # list of commands to be run
                  run_command "pnpm exec nx affected -t madge-analysis --parallel=14 --configuration=ci"

                  # wait for all background processes to finish
                  exit_code=0
                  for pid in ${pids[*]}; do
                      if ! wait $pid; then
                          if [ "${pid_is_required[$pid]}" = false ]; then
                              echo "Process with PID $pid failed, but it was not required. Command: ${pid_to_command[$pid]}"
                              continue
                          fi
                          echo "Process with PID $pid failed. Command: ${pid_to_command[$pid]}"  # Print a helpful error message
                          exit_code=1 # exit with an error status if any process fails
                      fi
                  done

                  exit $exit_code
            - name:
                  Complete the Nx Cloud distributed CI run
                  # It's important that we always run this step, otherwise in the case of any failures in preceding non-Nx steps, the agents will keep running and waste billable minutes
              if: always()
              continue-on-error: true
              run: npx --yes nx-cloud complete-ci-run

    nx-dte-agents-8:
        name: NX Agent ${{ matrix.agent }} - 8 Core
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-8c-32g
        strategy:
            matrix:
                agent: [1, 2, 3, 4, 5, 6, 7, 8]
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Install pytest, ruff, and uv # https://github.com/actions/setup-python/blob/main/docs/advanced-usage.md#caching-packages
              run: pipx install pytest && pipx install ruff && pipx install uv
            - uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5.5.0
              with:
                  python-version: '3.13'
            - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5
              with:
                  go-version: '>=1.24.1'
            - run: go version
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 5
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Start Nx Agent ${{ matrix.agent }}
              timeout-minutes: 20
              env:
                  NODE_OPTIONS: '--max-old-space-size=4196 --max_semi_space_size=128'
                  NX_AGENT_NAME: Agent ${{ matrix.agent }} - 8 Core
                  NXCACHE_AWS_DISABLE: false
              run: pnpm exec nx-cloud start-agent
