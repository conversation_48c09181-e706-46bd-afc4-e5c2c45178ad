name: Create Test Quality Workflows

on:
    workflow_dispatch:
        inputs:
            git_ref:
                description: 'Git reference (branch name, tag or commit SHA)'
                required: true
                default: 'master'
            disable_formatting:
                description: 'Disable formatting'
                required: false
                default: 'false'
            concurrency:
                description: 'Number of parallel jobs (1-10)'
                required: true
                default: '3'
                type: choice
                options:
                    - '1'
                    - '2'
                    - '3'
                    - '4'
                    - '5'
                    - '6'
                    - '7'
                    - '8'
                    - '9'
                    - '10'

jobs:
    create-test-branches:
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-4c-8g
        env:
            NX_DISABLE_FORMATTING: ${{ github.event.inputs.disable_formatting }}
        steps:
            - name: Initial checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  ref: ${{ github.event.inputs.git_ref }}
                  fetch-depth: 1
            - name: Get commit SHA
              id: git_info
              run: |
                  # Always get the SHA of the current checkout which will be our input git_ref
                  echo "SHA=$(git rev-parse HEAD)" >> $GITHUB_ENV
                  echo "REF=${{ github.event.inputs.git_ref }}" >> $GITHUB_ENV

            - name: Checkout exact commit
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  ref: ${{ env.SHA }}
                  fetch-depth: 1
            - name: Setup node and dependencies
              if: ${{ env.NX_DISABLE_FORMATTING != 'true' }}
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Configure Git
              run: |
                  git config user.name "GitHub Actions Bot"
                  git config user.email "<EMAIL>"

            - name: Get current date
              id: date
              run: |
                  # Using 24-hour format (00-23) for consistent sorting
                  echo "YEAR=$(date +'%Y')" >> $GITHUB_ENV
                  echo "MONTH=$(date +'%m')" >> $GITHUB_ENV
                  echo "DAY=$(date +'%d')" >> $GITHUB_ENV
                  echo "HOUR=$(date +'%H')" >> $GITHUB_ENV  # 24-hour format (00-23)
                  echo "MINUTE=$(date +'%M')" >> $GITHUB_ENV
                  echo "SECOND=$(date +'%S')" >> $GITHUB_ENV

            - name: Create snapshot base branch
              id: create_base
              run: |
                  # Format timestamp
                  TIMESTAMP="${{ env.YEAR }}${{ env.MONTH }}${{ env.DAY }}_${{ env.HOUR }}${{ env.MINUTE }}${{ env.SECOND }}"

                  # Format branch name using the resolved SHA and REF
                  if [ -n "${{ env.REF }}" ] && [ "${{ env.REF }}" != "master" ]; then
                    BASE_BRANCH="quality/snapshots/${TIMESTAMP}/ref-${{ env.REF }}/base"
                  else
                    BASE_BRANCH="quality/snapshots/${TIMESTAMP}/sha-${{ env.SHA }}/base"
                  fi

                  echo "base_branch=${BASE_BRANCH}" >> "$GITHUB_OUTPUT"
                  git checkout -b "${BASE_BRANCH}" ${{ env.SHA }} || exit 1
                  git push origin "${BASE_BRANCH}" || exit 1
                  echo "Created base snapshot branch: ${BASE_BRANCH}"

            - name: Create parallel job branches
              id: create_branches
              run: |
                  CONCURRENCY="${{ github.event.inputs.concurrency }}"
                  BASE_BRANCH="${{ steps.create_base.outputs.base_branch }}"
                  BASE_PREFIX="${BASE_BRANCH%/base}"  # Remove '/base' suffix

                  # Create a JSON array of branch names
                  BRANCH_JSON="["

                  for ((i=1; i<=$CONCURRENCY; i++)); do
                    # Create branch name for this job
                    JOB_BRANCH="${BASE_PREFIX}/jobs/runner-${i}-of-${CONCURRENCY}"
                    
                    if [ $i -gt 1 ]; then
                      BRANCH_JSON="${BRANCH_JSON},"
                    fi
                    BRANCH_JSON="${BRANCH_JSON}\"${JOB_BRANCH}\""

                    echo "Creating branch: $JOB_BRANCH"

                    # Create branch from base snapshot
                    git checkout -b "$JOB_BRANCH" "$BASE_BRANCH" || exit 1

                    # Modify nx.json file
                    if [ -f "nx.json" ]; then
                      # Preserve formatting by using jq with raw output and monochrome
                      if ! jq -M --arg branch "$JOB_BRANCH" '. * {"automatedCacheBust": $branch}' nx.json > nx.json.tmp; then
                        echo "Error: Failed to modify nx.json" >&2
                        exit 1
                      fi
                      mv nx.json.tmp nx.json

                      if [ "${{ env.NX_DISABLE_FORMATTING }}" != "true" ]; then
                        # Run nx format:write only on nx.json with explicit base
                        if ! NX_BASE="$BASE_BRANCH" NX_HEAD="$JOB_BRANCH" pnpm exec nx format:write --files nx.json; then
                          echo "Error: Failed to run nx format:write" >&2
                          exit 1
                        fi
                      fi

                      # Commit and push changes
                      git add nx.json
                      git commit -m "automation commit for code snapshot quality checks"
                      if ! git push origin "$JOB_BRANCH"; then
                        echo "Error: Failed to push branch $JOB_BRANCH" >&2
                        exit 1
                      fi

                      echo "Modified and pushed nx.json for branch: $JOB_BRANCH"
                    else
                      echo "Warning: nx.json file not found!"
                    fi

                    # Go back to base branch for next iteration
                    git checkout "$BASE_BRANCH" || exit 1
                  done

                  BRANCH_JSON="${BRANCH_JSON}]"
                  echo "branches=${BRANCH_JSON}" >> "$GITHUB_OUTPUT"

            - name: Trigger test pipelines
              env:
                  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
              run: |
                  CONCURRENCY="${{ github.event.inputs.concurrency }}"
                  BASE_BRANCH="${{ steps.create_base.outputs.base_branch }}"
                  BASE_PREFIX="${BASE_BRANCH%/base}"  # Remove '/base' suffix
                  REPO="${{ github.repository }}"


                  echo "🔍 Quality Test Overview"
                  echo "======================="
                  echo "Base Branch: ${BASE_BRANCH}"
                  echo "Total Runners: ${CONCURRENCY}"
                  echo "Workflow Reference: ${WORKFLOW_REF}"
                  echo ""
                  echo "📊 Build Test Runs:"
                  echo "----------------"

                  for ((i=1; i<=$CONCURRENCY; i++)); do
                    JOB_BRANCH="${BASE_PREFIX}/jobs/runner-${i}-of-${CONCURRENCY}"
                    
                    echo "Triggering build-test workflow for branch: $JOB_BRANCH"
                    
                    # Trigger the workflow using gh cli for better error handling
                    if ! gh workflow run build-test.yml -r "$JOB_BRANCH" \
                        -f base_ref="$BASE_BRANCH" \
                        -f head_ref="$JOB_BRANCH"; then
                      echo "Error: Failed to trigger workflow for branch $JOB_BRANCH" >&2
                      exit 1
                    fi
                    
                    echo "✅ Runner $i of $CONCURRENCY"
                    echo "  Branch: $JOB_BRANCH"
                    echo "  Build-Test: https://github.com/$REPO/actions/workflows/build-test.yml?query=branch:${JOB_BRANCH}"
                    echo ""

                    # Add delay between triggers, except for the last one
                    if [ $i -lt $CONCURRENCY ]; then
                      echo "Waiting 5 seconds before triggering next workflow..."
                      sleep 5
                    fi
                  done

                  echo "🔗 Quick Links"
                  echo "------------"
                  echo "• Base snapshot branch: ${BASE_BRANCH}"
                  echo "• Snapshot prefix: ${BASE_PREFIX}"

            - name: Create overview issue
              if: success()
              env:
                  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
              run: |
                  CONCURRENCY="${{ github.event.inputs.concurrency }}"
                  BASE_BRANCH="${{ steps.create_base.outputs.base_branch }}"
                  BASE_PREFIX="${BASE_BRANCH%/base}"
                  REPO="${{ github.repository }}"
                  TRIGGER_USER="${{ github.actor }}"
                  INPUT_REF="${{ github.event.inputs.git_ref }}"

                  ISSUE_TITLE="Quality Test Run Overview - $(date +'%Y-%m-%d %H:%M:%S')"
                  ISSUE_BODY=$(cat << EOF
                  # 🔍 Quality Test Overview

                  ### Base Information
                  - **Input Reference:** \`$INPUT_REF\`
                  - **Resolved SHA:** \`${{ env.SHA }}\`
                  - **Base Branch:** \`$BASE_BRANCH\`
                  - **Total Runners:** $CONCURRENCY
                  - **Triggered By:** @$TRIGGER_USER

                  ### 📊 Build Test Runs
                  EOF
                  )

                  for ((i=1; i<=$CONCURRENCY; i++)); do
                    JOB_BRANCH="${BASE_PREFIX}/jobs/runner-${i}-of-${CONCURRENCY}"
                    ISSUE_BODY+=$(cat << EOF

                  #### Runner $i of $CONCURRENCY
                  - **Branch:** \`$JOB_BRANCH\`
                  - **Build-Test Link:** [View Build Test Run](https://github.com/$REPO/actions/workflows/build-test.yml?query=branch:${JOB_BRANCH})
                  EOF
                    )
                  done

                  # Get the quality snapshot prefix for filtering
                  TIMESTAMP=$(echo "$BASE_PREFIX" | grep -o 'quality/snapshots/[0-9]\{8\}_[0-9]\{6\}')

                  ISSUE_BODY+=$(cat << EOF

                  ### 🔗 Quick Links
                  - [View All Created Branches](https://github.com/$REPO/branches/all?query=${TIMESTAMP})
                  - [Base Branch Tree](https://github.com/$REPO/tree/$BASE_BRANCH)
                  - [View Job Branches](https://github.com/$REPO/branches/all?query=${BASE_PREFIX}/jobs)

                  ### 📝 Additional Information
                  - Created by: Quality Test Workflow
                  - Timestamp: $(date +'%Y-%m-%d %H:%M:%S %Z')
                  EOF
                  )

                  echo "Creating overview issue..."
                  ISSUE_URL=$(gh issue create --title "$ISSUE_TITLE" --body "$ISSUE_BODY")

                  echo ""
                  echo "📋 Overview Issue"
                  echo "---------------"
                  echo "• View complete overview at: $ISSUE_URL"
