# Prevents us from having to manually close PRs with no activity
# See the docs for more info on how this action works: https://github.com/actions/stale

name: Mark and close stale pull requests

on:
    schedule:
        - cron: '30 1 * * *'

jobs:
    stale:
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        permissions:
            pull-requests: write
            issues: write
        steps:
            - uses: actions/stale@28ca1036281a5e5922ead5184a1bbf96e5fc984e # v9.0.0
              with:
                  repo-token: ${{ secrets.GITHUB_TOKEN }}
                  days-before-stale: 15
                  days-before-close: 7
                  stale-pr-message: 'This PR is stale because it has been open 15 days with no activity. Remove the "no pr activity" label or this will PR be closed in 7 days.'
                  stale-pr-label: 'no pr activity'
                  operations-per-run: 500
                  exempt-pr-labels: 'dependencies'
                  remove-stale-when-updated: false # https://github.com/actions/stale/issues/1008
