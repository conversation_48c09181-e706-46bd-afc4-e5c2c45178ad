# Build and deploys CRM services to ECS.
# The CRM has its own release cycle from the main application and is deployed separately from the monolith and microservices

name: Deploy CRM to Amazon ECS

on:
    pull_request:
        types: [opened, synchronize]
        branches:
            - master
            - crm-staging
            - crm-production
    push:
        branches:
            - master
            - crm-staging
            - crm-production

concurrency:
    group: ${{ github.workflow }}-${{ github.ref }}

env:
    FORCE_COLOR: 3 # fix terminal color output from internal actions
    GIT_TAG_PREFIX: crm
    RELEASE_BRANCH_ORDER_CONFIG_PATH: .github/apps/crm/release-branch-order.yml
    NX_CI_EXECUTION_ID: ${{ github.sha }}-${{ github.run_attempt > 1 && format('{0}-{1}', github.run_id, github.run_attempt) || '' }}
    NX_CI_EXECUTION_ENV: crm-deploy
jobs:
    deploy-matrix:
        name: Set Deploy Matrix
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        permissions:
            contents: read
        outputs:
            matrix: ${{ steps.json.outputs.json-string == '' &&  steps.master-json.outputs.json-string || steps.json.outputs.json-string }}
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
            - name: Check .github/crm-environments/REF_NAME.json existence
              id: check_crm_environment_file
              uses: andstor/file-existence-action@076e0072799f4942c8bc574a82233e1e4d13e9d6 # v3.0.0
              with:
                  files: '${{github.workspace}}/.github/crm-environments/${{github.ref_name}}.json'
            - name: Load matrix from REF_NAME
              id: json
              if: steps.check_crm_environment_file.outputs.files_exists == 'true'
              uses: time-loop/github-actions/dist/load-json-file@load-json-file+0.1.6
              with:
                  json-path: ${{github.workspace}}/.github/crm-environments/${{github.ref_name}}.json
            - name: Load matrix from master
              id: master-json
              if: steps.check_crm_environment_file.outputs.files_exists == 'false'
              uses: time-loop/github-actions/dist/load-json-file@load-json-file+0.1.6
              with:
                  json-path: ${{github.workspace}}/.github/crm-environments/master.json
    create-release-tag:
        name: Create release tag
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        permissions:
            contents: write
            pull-requests: read
        outputs:
            new-tag: ${{ steps.create-release-tag.outputs.new-tag }}
            old-tag: ${{ steps.create-release-tag.outputs.old-tag }}
            is-hotfix: ${{ steps.create-release-tag.outputs.is-hotfix }}
            new-version: ${{ steps.create-release-tag.outputs.new-version }}
        steps:
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - name: Create release tag
              if: github.event_name == 'push'
              uses: time-loop/github-actions/dist/create-release-tag@create-release-tag+3.1.2
              id: 'create-release-tag'
              with:
                  github-token: ${{ steps.app-token.outputs.token }}
                  # A github token with admin permissions that can create tags and trigger other workflows
                  github-admin-token: ${{ steps.app-token.outputs.token }}
                  tag-prefix: ${{ env.GIT_TAG_PREFIX }}
                  release-branch-order-config-path: ${{ env.RELEASE_BRANCH_ORDER_CONFIG_PATH }}
    create-release-notes:
        name: Create release notes
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        needs: [create-release-tag]
        if: github.event_name == 'push' && github.ref == 'refs/heads/master'
        permissions:
            contents: write
            pull-requests: read
        steps:
            - name: Checkout repo
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  filter: tree:0
            - uses: actions/create-github-app-token@dff4b11d10ecc84d937fdd0653d8343a88c5b9c4
              id: app-token
              with:
                  app-id: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_ID  }}
                  private-key: ${{ secrets.ENGPRODCI_CLICKUP_GITHUB_APP_PRIVATE_KEY }}
            - name: Create release notes
              uses: time-loop/github-actions/dist/create-release-notes@create-release-notes+1.2.15
              with:
                  github-token: ${{ steps.app-token.outputs.token }}
                  # All commits between these 2 tags will appear in the release notes
                  last-release-git-tag: ${{ needs.create-release-tag.outputs.old-tag }}
                  new-release-git-tag: ${{ needs.create-release-tag.outputs.new-tag }}
                  # The same prefix you passed to the create-release-tag action
                  tag-prefix: ${{ env.GIT_TAG_PREFIX }}
                  # Determines if this is a hotfix release or a main branch promotion from master -> crm-staging -> crm-production
                  is-hotfix: ${{ needs.create-release-tag.outputs.is-hotfix }}
                  release-notification-slack-webhook: ${{ secrets.CRM_RELEASE_PR_SLACK_WEBHOOK }}
                  release-branch-order-config-path: ${{ env.RELEASE_BRANCH_ORDER_CONFIG_PATH }}
                  commit-paths: |
                      ./apps/crm-legacy-service/
                      ./src/libs
                      ./libs/crm
    build:
        name: Build
        needs: [deploy-matrix, create-release-tag]
        strategy:
            fail-fast: false
            matrix:
                platform: [linux/arm64/v8]
                services: [crm-legacy-service]
                deployments: ${{fromJson(needs.deploy-matrix.outputs.matrix)}}
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-8c-16g
        permissions:
            contents: read
            id-token: write
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
            - name: Setup Java
              uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
              with:
                  distribution: 'corretto'
                  java-version: '21'
            - name: Setup Gradle
              uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@e3dd6a429d7300a6a4c196c26e071d42e0343502 # v4.0.2
              with:
                  aws-access-key-id: ${{ secrets[matrix.deployments.access_key_id] }}
                  aws-secret-access-key: ${{ secrets[matrix.deployments.secret_access_key] }}
                  aws-region: ${{ matrix.deployments.region }}
            - name: Set up Docker Buildx
              uses: Wandalen/wretry.action@ffdd254f4eaf1562b8a2c66aeaa37f1ff2231179 # v3.7.3
              with:
                  action: docker/setup-buildx-action@f95db51fddba0c2d1ec667646a06c2ce06100226
                  attempt_delay: 500
                  attempt_limit: 3
            - name: Login to Amazon ECR
              id: login-ecr
              uses: aws-actions/amazon-ecr-login@062b18b96a7aff071d4dc91bc00c4c1a7945b076 # v2.0.1

            - name: Configure ecr-cache AWS credentials (us-west-2)
              uses: aws-actions/configure-aws-credentials@f24d7193d98baebaeacc7e2227925dd47cc267f5 # v4.2.0
              with:
                  role-to-assume: arn:aws:iam::158160757288:role/gha-ecr-push
                  role-session-name: gha
                  aws-region: us-west-2

            - name: Login to Amazon ecr-cache (us-west-2)
              run: |
                  aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 158160757288.dkr.ecr.us-west-2.amazonaws.com

            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  node-version-file: .nvmrc
                  cache-name: 'cache-node-modules-${{ matrix.platform }}'
            - name: Configure NPM
              shell: bash
              run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.ALL_PACKAGE_READ_TOKEN }}" >> ~/.npmrc
            - name: Deploy to ECR
              # arm64 builds segfault quite a lot, apparently
              uses: nick-fields/retry@7152eba30c6575329ac0576536151aca5a72780e # 3.0.0
              env:
                  DOCKER_BUILDKIT: 1
                  DOCKER_IMAGE: ${{ steps.login-ecr.outputs.registry }}/${{format(matrix.deployments.repositoryTemplate, matrix.services)}}
                  DOCKER_TAG_VERSION: version-${{ matrix.services }}-${{ needs.create-release-tag.outputs.new-version }}
                  DOCKER_IMAGE_VERSION: ${{ needs.create-release-tag.outputs.new-version }}
                  DOCKER_TAG_SHA: sha-${{ matrix.services }}-${{ github.sha }}

              with:
                  timeout_minutes: 30
                  max_attempts: 5
                  # can't use `retry_on_exit_code: 139` because nx masks the exit code
                  command: |
                      pnpm nx build-docker ${{ matrix.services }} \
                          --npmrc-path=${HOME} \
                          --platform=${{ matrix.platform }} \
                          --image=$DOCKER_IMAGE \
                          --image-version=$DOCKER_IMAGE_VERSION \
                          --image-tag-version=$DOCKER_TAG_VERSION-arm64 \
                          --image-tag-sha=$DOCKER_TAG_SHA-arm64
    deploy:
        name: Deploy
        needs: [deploy-matrix, create-release-tag, build]
        if: github.event_name == 'push' && needs.deploy-matrix.outputs.matrix != '[]'
        strategy:
            fail-fast: false
            matrix:
                services: [crm-legacy-service]
                deployments: ${{fromJson(needs.deploy-matrix.outputs.matrix)}}
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        permissions:
            contents: read
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@e3dd6a429d7300a6a4c196c26e071d42e0343502 # v4.0.2
              with:
                  aws-access-key-id: ${{ secrets[matrix.deployments.access_key_id] }}
                  aws-secret-access-key: ${{ secrets[matrix.deployments.secret_access_key] }}
                  aws-region: ${{ matrix.deployments.region }}
            - name: Login to Amazon ECR
              id: login-ecr
              uses: aws-actions/amazon-ecr-login@062b18b96a7aff071d4dc91bc00c4c1a7945b076 # v2.0.1
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies+1.6.20
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  node-version-file: .nvmrc
                  cache-name: 'cache-node-modules-amd64'
            - name: Configure NPM
              shell: bash
              run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.ALL_PACKAGE_READ_TOKEN }}" >> ~/.npmrc
            - name: Deploy to ECR
              env:
                  DOCKER_BUILDKIT: 1
                  DOCKER_IMAGE: ${{ steps.login-ecr.outputs.registry }}/${{format(matrix.deployments.repositoryTemplate, matrix.services)}}
                  DOCKER_TAG_VERSION: version-${{ matrix.services }}-${{ needs.create-release-tag.outputs.new-version }}
              run: |
                  docker manifest create "$DOCKER_IMAGE:latest" \
                  --amend "$DOCKER_IMAGE:$DOCKER_TAG_VERSION-arm64"

                  docker manifest push "$DOCKER_IMAGE:latest"
    stop-nx-ci-run:
        name: Stop NX CI Run
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        if: vars.USE_BUILD_TEST_NX_DTE == 'true' && always()
        permissions:
            contents: read
            actions: read
        needs: [build]
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
            - name: Setup Node
              uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
            - name: Stop the CI run
              continue-on-error: true
              run: npx --yes nx-cloud complete-ci-run
