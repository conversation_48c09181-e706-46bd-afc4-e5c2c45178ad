# Runs all automated tests for the monolith and micro services e.g. unit tests, integration tests, type checking, linting, etc.

name: Build-Test
on:
    pull_request:
        types: [opened, synchronize, reopened]
        branches:
            - master
            - staging
            - production
            - crm-production
            - crm-staging
            - wms-master
            - wms-production
            - wms-staging
            - gt_**

    workflow_dispatch: # Allows manual triggering of the workflow
        inputs:
            base_ref:
                description: 'Base snapshot branch'
                required: true
            head_ref:
                description: 'Branch to test'
                required: true
concurrency:
    # On pushes to main branches we want to run the tests for all commits
    # so that we can provide accurate coverage diffs within codecov on PRs
    # Otherwise if a PR doesn't have coverage to compare against for the PRs base commit the coverage diff will be wrong
    group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name == 'push' && github.run_id || '' }}
    cancel-in-progress: ${{ github.event_name == 'pull_request' }}

env:
    type-check-cache-version: 1
    PGUSER: postgres
    NODE_OPTIONS: '--max-old-space-size=7175 --dns-result-order=ipv4first'
    DD_API_KEY: ${{ secrets.DD_API_KEY }}
    DD_CIVISIBILITY_AGENTLESS_ENABLED: true
    DD_ENV: ci
    nx-cache-version: 1
    SUPPRESS_NO_CONFIG_WARNING: 'true'
    NXCACHE_AWS_DISABLE: true # we default to disabling it so steps can opt in.
    NXCACHE_AWS_ACCESS_KEY_ID: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
    NXCACHE_AWS_SECRET_ACCESS_KEY: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
    NXCACHE_AWS_REGION: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
    NXCACHE_AWS_BUCKET: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME_NX }}
    NXCACHE_AWS_ENDPOINT: https://${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
    NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}
    NX_BRANCH: ${{ github.event.number || github.ref_name }}
    NX_CI_EXECUTION_ID: ${{ github.sha }}-${{ github.run_attempt > 1 && format('{0}-{1}', github.run_id, github.run_attempt) || '' }}
    NX_CI_EXECUTION_ENV: 'Build-Test Non-DTE'
    # Set NX base and head for both PR and workflow dispatch modes
    NX_BASE: ${{ github.event.pull_request && github.event.pull_request.base.sha || github.event.inputs.base_ref }}
    NX_HEAD: ${{ github.event.pull_request && github.event.pull_request.head.sha || github.event.inputs.head_ref }}
    SHOULD_SKIP_MONOLITH_TEST: ${{ contains(github.event.pull_request.labels.*.name, 'generated-new-service') || (startsWith(github.head_ref, 'smartling-') && github.event.pull_request.user.login == 'smartling-github-connector[bot]') }}

jobs:
    check-pr-type:
        name: Check PR Type
        continue-on-error: true
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        steps:
            - name: Get PR Type
              uses: time-loop/github-actions/dist/get-pr-type@get-pr-type+0.8.0
              id: get-pr-type
              with:
                  github-app-id: ${{ secrets.CLICKUP_GITHUB_BOT_APP_ID }}
                  github-private-key: ${{ secrets.CLICKUP_GITHUB_BOT_PRIVATE_KEY }}
                  github-token: ${{ secrets.GITHUB_TOKEN }}
            - name: Echo PR Type
              run: |
                  echo 'PR Type: ${{ steps.get-pr-type.outputs.type }}'
        outputs:
            PR_TYPE: ${{ steps.get-pr-type.outputs.type }}

    install-dependencies:
        name: Install Dependencies
        if: ${{ needs.check-pr-type.outputs.PR_TYPE != 'config' }}
        needs: [check-pr-type]
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-2c-4g
        permissions:
            contents: read
            actions: read
        outputs:
            run-attempt: ${{ github.run_attempt }}
        steps:
            - run: echo ${GITHUB_REF##*/}
            - run: echo $GITHUB_REF
            - run: echo $GITHUB_REF_NAME
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  # We need to fetch all branches and commits so that Nx affected has a base to compare against.
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies****.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: true
                  nx-main-branch: master
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}

    generate-branch-config-snapshots:
        name: Generate Branch Config Snapshots
        if: ${{ needs.check-pr-type.outputs.PR_TYPE == 'config' }}
        needs: [check-pr-type]
        permissions:
            actions: write
            contents: write
            pull-requests: write
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-amd64-16c-64g
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  # We need to fetch all branches and commits so that Nx affected has a base to compare against.
                  fetch-depth: 0
                  filter: tree:0
            - name: Get Changed Files
              id: changed-files
              uses: tj-actions/changed-files@823fcebdb31bb35fdf2229d9f769b400309430d0 # v46.0.3
            - name: Setup node and dependencies
              id: setup-node-and-dependencies
              timeout-minutes: 10
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies****.20
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-error-on-no-successful-workflow: false
                  nx-setup: true
                  nx-main-branch: master
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Run Config NX Affected
              timeout-minutes: 5
              env:
                  ALL_CHANGED_FILES: ${{ steps.changed-files.outputs.all_changed_files }}
              run: pnpm exec nx run config-plugin:config-build --affectedConfigFiles="$ALL_CHANGED_FILES" --skipTests --outputAffected=config-affected-output.json --excludedFileTypes=md,snap
            - name: Upload Config Affected Output
              id: upload-config-affected-output
              uses: actions/upload-artifact@6f51ac03b9356f520e9adb1b1b7802705f340c2b # v4.5.0
              with:
                  path: config-affected-output.json
                  name: ${{ github.sha }}-config-affected-output-in-build-test
                  retention-days: 1
                  overwrite: true
            - name: Run Config NX Executor
              timeout-minutes: 15
              env:
                  ALL_CHANGED_FILES: ${{ steps.changed-files.outputs.all_changed_files }}
              run: pnpm exec nx run config-plugin:config-build --affectedConfigFiles="$ALL_CHANGED_FILES" --updateSnapshots=true --excludedFileTypes=md,snap
            - name: Upload Snapshots
              id: upload-branch-snapshots
              uses: actions/upload-artifact@6f51ac03b9356f520e9adb1b1b7802705f340c2b # v4.5.0
              with:
                  path: tools/config-plugin/src/executors/config-test/__snapshots__
                  name: ${{ github.sha }}-branch
                  retention-days: 1
                  overwrite: true

    generate-target-config-snapshots:
        name: Generate Target Config Snapshots
        if: ${{ needs.check-pr-type.outputs.PR_TYPE == 'config' }}
        needs: [check-pr-type]
        permissions:
            actions: write
            contents: write
            pull-requests: write
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-amd64-16c-64g
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  ref: master
            - name: Get Changed Files
              id: changed-files
              uses: tj-actions/changed-files@823fcebdb31bb35fdf2229d9f769b400309430d0 # v46.0.3
            - name: Setup node and dependencies
              id: setup-node-and-dependencies
              timeout-minutes: 10
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies****.20
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-error-on-no-successful-workflow: false
                  nx-setup: true
                  nx-main-branch: master
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Run Config NX Executor
              timeout-minutes: 15
              env:
                  ALL_CHANGED_FILES: ${{ steps.changed-files.outputs.all_changed_files }}
              run: pnpm exec nx run config-plugin:config-build --affectedConfigFiles="$ALL_CHANGED_FILES" --updateSnapshots=true --excludedFileTypes=md,snap
            - name: Upload Snapshots
              id: upload-target-snapshots
              uses: actions/upload-artifact@6f51ac03b9356f520e9adb1b1b7802705f340c2b # v4.5.0
              with:
                  path: tools/config-plugin/src/executors/config-test/__snapshots__
                  name: ${{ github.sha }}-target
                  retention-days: 1
                  overwrite: true

    nx-config-diff:
        name: NX Config Diff
        needs: [generate-branch-config-snapshots, generate-target-config-snapshots]
        permissions:
            actions: write
            contents: write
            pull-requests: write
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-4c-16g
        steps:
            - name: Checkout
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
            - name: Download Config Affected Output
              id: download-config-affected-output
              uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
              with:
                  path: config-affected-output.json
                  name: ${{ github.sha }}-config-affected-output-in-build-test
            - name: Download Branch Snapshots
              id: download-branch-snapshots
              uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
              with:
                  path: config-branch-snapshots
                  name: ${{ github.sha }}-branch
            - name: Download Target Snapshots
              id: download-target-snapshots
              continue-on-error: true
              uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
              with:
                  path: config-target-snapshots
                  name: ${{ github.sha }}-target
            - name: Force Target Snapshots
              if: steps.download-target-snapshots.outcome == 'failure'
              id: force-target-snapshots
              run: |
                  mkdir -p config-target-snapshots
            - name: Print Diff Results
              id: print-diff-results
              continue-on-error: true
              run: |
                  CONFIG_AFFECTED_OUTPUT=$(cat config-affected-output.json/config-affected-output.json)
                  CONFIG_DIFF_OUTPUT=$(git diff --no-index config-target-snapshots config-branch-snapshots) || true
                  echo "CONFIG_DIFF_OUTPUT_SIZE_IN_KB=$(echo "scale=0; $(echo ${#CONFIG_DIFF_OUTPUT}) / 1024" | bc)" >> $GITHUB_ENV
                  echo "GITHUB_RUN_LINK=https://github.com/time-loop/clickup/actions/runs/${{ github.run_id }}" >> $GITHUB_ENV

                  echo 'Config Affected Results 🚀'
                  echo '==========================='
                  echo 'Each service in the services array will be redeployed with new config changes in the given environment and regions.'
                  echo "$CONFIG_AFFECTED_OUTPUT"

                  echo 'Config Diff Results 🚀'
                  echo '======================='
                  echo "$CONFIG_DIFF_OUTPUT"
            - name: Output Diff Results in Comment
              id: output-diff-results-comment
              continue-on-error: true
              uses: peter-evans/create-or-update-comment@71345be0265236311c031f5c7866368bd1eff043 # v4.0.0
              with:
                  issue-number: ${{ github.event.pull_request.number }}
                  body: |
                      ### Config Diff Results 🚀
                      Your config change output is posted in the Action summary.

                      Please review the changes in the summary section: ${{ env.GITHUB_RUN_LINK }}
            - name: Output Diff Results in Summary
              id: output-diff-results-summary
              continue-on-error: true
              run: |
                  CONFIG_AFFECTED_OUTPUT=$(cat config-affected-output.json/config-affected-output.json)
                  echo '### Config Affected Results 🚀' >> $GITHUB_STEP_SUMMARY
                  echo 'Each service in the `services` array will be redeployed with new config changes in the given environment and regions.' >> $GITHUB_STEP_SUMMARY
                  echo '```json' >> $GITHUB_STEP_SUMMARY
                  echo "$CONFIG_AFFECTED_OUTPUT" >> $GITHUB_STEP_SUMMARY
                  echo '```' >> $GITHUB_STEP_SUMMARY

                  if [ $CONFIG_DIFF_OUTPUT_SIZE_IN_KB -lt 800 ]; then
                      CONFIG_DIFF_OUTPUT=$(git diff --no-index config-target-snapshots config-branch-snapshots) || true
                      echo '### Config Diff Results 🚀' >> $GITHUB_STEP_SUMMARY
                      echo '```diff' >> $GITHUB_STEP_SUMMARY
                      echo "$CONFIG_DIFF_OUTPUT" >> $GITHUB_STEP_SUMMARY
                      echo '```' >> $GITHUB_STEP_SUMMARY
                  else
                      echo '### Config Diff Results 🚀' >> $GITHUB_STEP_SUMMARY
                      echo "The output is too large ($CONFIG_DIFF_OUTPUT_SIZE_IN_KB KB) to be displayed here." >> $GITHUB_STEP_SUMMARY
                      echo "Please see the full output in \`NX Config Diff\` > \`Print Diff Results\`" >> $GITHUB_STEP_SUMMARY
                  fi

    nx-dte-main:
        name: Nx Cloud - Main Job
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-8c-32g
        needs: [check-pr-type]
        if: |-
            vars.USE_BUILD_TEST_NX_DTE == 'true' &&
            !(github.event_name == 'pull_request' && startsWith(github.event.pull_request.title, 'breakfix')) &&
            !(needs.check-pr-type.outputs.PR_TYPE == 'config')
        env:
            NX_CI_EXECUTION_ENV: 'Build-Test DTE'
        steps:
            - run: echo ${GITHUB_REF##*/}
            - run: echo $GITHUB_REF
            - run: echo $GITHUB_REF_NAME
            - run: echo $NX_CI_EXECUTION_ID
            - name: Start nx cloud (nx agents)
              if:
                  vars.NX_AGENTS == 'enabled'
                  # Continue if there is a network error from the nx api when starting the agent
              continue-on-error: true
              env:
                  DD_API_KEY: ${{ secrets.DD_API_KEY }}
                  DD_CIVISIBILITY_AGENTLESS_ENABLED: true
                  DD_ENV: ci
                  ALL_PACKAGE_READ_TOKEN: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}
                  NX_CI_EXECUTION_ENV: 'Build-Test DTE'
                  NX_COMMIT_SHA: ${{ github.sha }}
                  NX_COMMIT_REF: ${{ github.ref }}
                  NX_CLOUD_API: https://clickup.gc.ent.nx.app
                  NX_GH_EVENT_DETECTION: true
              run: echo "{}" > nx.json && npx nx-cloud@latest start-ci-run --distribute-on=".nx/workflows/distribution-config.yaml" ${{ env.NX_CLOUD_COMMON_ARGS }} --stop-agents-after="ci-e2e-test,test,lint,build,ci-shard-functional-test,ci-shard-integration-test,ci-shard-unit-test" --stop-agents-on-failure=false --with-env-vars="ALL_PACKAGE_READ_TOKEN,DD_API_KEY,DD_CIVISIBILITY_AGENTLESS_ENABLED,DD_ENV,DD_SERVICE,GITHUB_ACTION,GITHUB_SERVER_URL,GITHUB_RUN_ID,GITHUB_RUN_NUMBER,GITHUB_RUN_ATTEMPT,GITHUB_WORKFLOW,GITHUB_WORKSPACE,GITHUB_REPOSITORY,GITHUB_SHA,GITHUB_HEAD_REF,GITHUB_REF,GITHUB_JOB"
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
              with:
                  # We need to fetch all branches and commits so that Nx affected has a base to compare against.
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies****.20
              timeout-minutes: 10
              with:
                  node-version: '22'
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: true
                  nx-main-branch: master
                  use-s3: true
                  nx-error-on-no-successful-workflow: ${{ github.event_name == 'pull_request' }}
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Setup Java
              uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
              with:
                  distribution: 'corretto'
                  java-version: '21'
              timeout-minutes: 1
            - name: Setup Gradle
              uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1
            - name: Check out the default branch
              run: |
                  if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
                    # Ensure we have all history
                    git fetch --unshallow origin || true
                    # Now fetch the base ref
                    git fetch origin ${{ github.event.inputs.base_ref }}
                    # Get the SHA of the base ref
                    BASE_SHA=$(git rev-parse FETCH_HEAD)
                    # Set NX_BASE to base snapshot SHA
                    echo "NX_BASE=$BASE_SHA" >> $GITHUB_ENV
                    # Set NX_HEAD to current SHA
                    echo "NX_HEAD=$(git rev-parse HEAD)" >> $GITHUB_ENV
                  else
                    git branch --track main origin/master
                  fi

            - name: Initialize the Nx Cloud distributed CI run and stop agents when the build tasks are done
              if: vars.NX_AGENTS != 'enabled'
              run: pnpm exec nx-cloud start-ci-run --distribute-on="manual" --require-explicit-completion --stop-agents-after="ci-e2e-test,test,lint,build,ci-shard-functional-test,ci-shard-integration-test,ci-shard-unit-test" --stop-agents-on-failure=false --with-env-vars="CHUNK_NUMBER,TOTAL_CHUNKS"
            - name: Restore type-check cache
              uses: time-loop/github-actions/dist/pull-request-cache@pull-request-cache+0.3.10
              with:
                  cache-name: type-check-cache-${{ env.type-check-cache-version }}-${{ hashFiles('test/tsconfig.json', 'tsconfig.json', 'tsconfig-nx-test-typecheck.json', 'tsconfig.base.json', 'package.json') }}
                  cache-path: ./dist/out-tsc/
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Run commands in parallel
              timeout-minutes: 60
              env:
                  DD_API_KEY: ${{ secrets.DD_API_KEY }}
                  DD_CIVISIBILITY_AGENTLESS_ENABLED: true
                  DD_ENV: ci
                  NX_CLOUD_DISTRIBUTED_EXECUTION: true
                  NX_CLOUD_DISTRIBUTED_EXECUTION_AGENT_COUNT: 20
                  NX_DISABLE_DB: true
                  NX_PLUGIN_NO_TIMEOUTS: true
              run: |
                  set -x
                  pids=()
                  # initialize an associative array to store PIDs and their associated commands
                  declare -A pid_to_command

                  # function to run commands and store the PID
                  function run_command() {
                    local command=$1
                    local required=${2:-true}
                    $command &  # run the command in the background
                    pid=$!      # get the PID of the background process
                    echo "Running command: $1 at PID: $pid"
                    pids+=($pid)  # store the PID of the background process
                    pid_to_command[$pid]="$1"  # store the command associated with the PID
                    pid_is_required[$pid]=$required # store whether the command is required
                  }

                  # Display DD env variables
                  echo "DD_CIVISIBILITY_AGENTLESS_ENABLED: $DD_CIVISIBILITY_AGENTLESS_ENABLED"
                  echo "DD_ENV: $DD_ENV"
                  echo "DD_SERVICE: $DD_SERVICE"
                  # Show NODE_OPTIONS
                  echo "NODE_OPTIONS: $NODE_OPTIONS"

                  # warm the affected graph but don't show the output to make it noisy
                  pnpm exec nx show projects > /dev/null

                  # list of commands to be run on main has env flag NX_CLOUD_DISTRIBUTED_EXECUTION set to false
                  run_command "pnpm exec nx-cloud record -- nx format:check --base=$NX_BASE --head=$NX_HEAD"

                  # list of commands to be run on agents
                  run_command "pnpm exec nx affected -t test --exclude='app-template' --runInBand"
                  run_command "pnpm exec nx affected -t gradle-test"
                  run_command "pnpm nx affected -t build --exclude='gateway-*' --exclude='client' --exclude='nest-playground'"
                  run_command "pnpm exec nx affected -t ci-shard-functional-test"

                  # the following will only run on 16-core agents, hence the higher parallelism
                  run_command "pnpm exec nx affected -t lint --quiet"
                  run_command "pnpm exec nx affected -t ci-e2e-test --configuration=ci"
                  run_command "pnpm exec nx affected -t ci-shard-integration-test --runInBand"
                  run_command "pnpm exec nx affected -t ci-shard-unit-test"

                  # run this on the main runner, as it will have compute that can be utilized while it's waiting for other runners to finish
                  run_command "pnpm exec nx affected -t check-package-lockfile -t typecheck --parallel 1 --no-dte"

                  # wait for all background processes to finish
                  exit_code=0
                  for pid in ${pids[*]}; do
                      if ! wait $pid; then
                          if [ "${pid_is_required[$pid]}" = false ]; then
                              echo "Process with PID $pid failed, but it was not required. Command: ${pid_to_command[$pid]}"
                              continue
                          fi
                          echo "Process with PID $pid failed. Command: ${pid_to_command[$pid]}"  # Print a helpful error message
                          exit_code=1 # exit with an error status if any process fails
                      fi
                  done

                  exit $exit_code
              # Temporarily disabled until we can fix it failing: https://clickup.gc.ent.nx.app/cipes/67ecef50fd359f41ce8cd60e
            #            - name: Merge code coverage reports
            #              run: pnpm exec nx run-many -p monolith-test-jest -t ci-shard-unit-test--process-code-coverage,ci-shard-integration-test--process-code-coverage --no-dte
            - name:
                  Complete the Nx Cloud distributed CI run
                  # It's important that we always run this step, otherwise in the case of any failures in preceding non-Nx steps, the agents will keep running and waste billable minutes
              if: always()
              continue-on-error: true
              run: npx --yes nx-cloud complete-ci-run
            - name: Archive coverage reports
              if: always()
              run: |
                  mkdir -p coverage/monolith-test-jest/ci-unit-test
                  # ls all the coverage reports
                  ls -l coverage/monolith-test-jest/ci-unit-test/ || true
                  rm -rf coverage/monolith-test-jest/ci-unit-test/chunk-* || true
                  rm -rf coverage/temp || true
                  ls -l coverage/monolith-test-jest/ci-unit-test/
                  tar -czf coverage.tar.gz ./coverage
            - name: Find and prepare failed tests
              if: always()
              id: find-failed-tests
              run: |
                  # Find all failed-tests.json files recursively in the coverage directory
                  FAILED_TEST_FILES=$(find ./coverage -name "failed-tests.json" -type f 2>/dev/null || echo "")
                  if [ -n "$FAILED_TEST_FILES" ]; then
                    # Create directory for all failed test files
                    mkdir -p ./collected-failed-tests
                    
                    # Process each failed test file
                    for FILE in $FAILED_TEST_FILES; do
                      # Copy the file to the collection directory
                      cp "$FILE" "./collected-failed-tests/$(basename "$FILE")"
                      echo "Found and copied $FILE"
                    done
                    
                    echo "FOUND_FAILED_TESTS=true" >> $GITHUB_ENV
                  else
                    echo "No failed-tests.json files found in coverage directory"
                    echo "FOUND_FAILED_TESTS=false" >> $GITHUB_ENV
                  fi

                  # Also check for root level failed-tests.json for backward compatibility
                  if [ -f ./failed-tests.json ]; then
                    mkdir -p ./collected-failed-tests
                    cp ./failed-tests.json ./collected-failed-tests/root-failed-tests.json
                    echo "FOUND_FAILED_TESTS=true" >> $GITHUB_ENV
                    echo "Found and copied root failed-tests.json"
                  fi
            - name: Save failed tests
              if: always() && env.FOUND_FAILED_TESTS == 'true'
              uses: actions/upload-artifact@b4b15b8c7c6ac21ea08fcf65892d2ee8f75cf882 # v4.4.3
              continue-on-error: true
              with:
                  name: failed-tests-main
                  path: ./collected-failed-tests/*.json
                  retention-days: 1
            - name: Save nx coverage reports
              uses: actions/upload-artifact@6f51ac03b9356f520e9adb1b1b7802705f340c2b # v4.5.0
              with:
                  name: nx-coverage-reports
                  path: coverage.tar.gz
                  retention-days: 1
    nx-dte-agents-4:
        name: NX Agent ${{ matrix.agent }} - 4 Core
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-4c-16g
        needs: [install-dependencies]
        if: |-
            vars.NX_AGENTS != 'enabled' &&
            !(github.event_name == 'pull_request' && startsWith(github.event.pull_request.title, 'breakfix'))
        strategy:
            matrix:
                agent: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]
        env:
            jest-cache-name: nx-agent-jest-cache-${{ matrix.agent }}-4-core
            esbuild-cache-name: nx-agent-esbuild-cache-${{ matrix.agent }}-4-core
            CLICKUP_ESBUILD_CACHE_DIR: /tmp/.esbuild-cache
            NX_CI_EXECUTION_ENV: 'Build-Test DTE'
        steps:
            - name: Check run attempt
              if: ${{ needs.install-dependencies.outputs.run-attempt != github.run_attempt }}
              run: |
                  echo 'To re-run this pipeline, please use "Re-run all jobs" instead of "Re-run failed jobs"'
                  exit 1
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Install pytest, ruff, and uv # https://github.com/actions/setup-python/blob/main/docs/advanced-usage.md#caching-packages
              run: |
                  pipx install pytest && pipx install ruff && pipx install uv
                  echo $HOME/.local/bin >> $GITHUB_PATH
            - uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5.5.0
              with:
                  python-version: '3.13'
            - name: Setup Java
              uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
              with:
                  distribution: 'corretto'
                  java-version: '21'
            - name: Setup Gradle
              uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1
            - run: go version
            - run: java --version
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies****.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Pre Start Docker Compose
              shell: bash
              run: docker compose --profile=ci up --detach --quiet-pull &
            - name: Pull docker-compose images for --profile=ci
              run: docker compose --profile=ci pull --include-deps
              timeout-minutes: 5
              continue-on-error: true
            - name: Wait For Docker Compose To Be Ready
              uses: ./.github/actions/docker-compose-run
              with:
                  command: pnpm run ci:test:docker-compose:wait
            - name: Start Nx Agent ${{ matrix.agent }}
              timeout-minutes: 40
              env:
                  NXCACHE_AWS_DISABLE: false
                  AWS_REGION: us-west-2
                  AWS_ACCESS_KEY_ID: test
                  AWS_SECRET_ACCESS_KEY: test
                  # dns-result-order=ipv4first is needed to address a breaking change in node 17 where localhost resolves to ipv6 first instead of ipv4
                  # Somehow this makes testcontainers randomly flake in the CI when running these tests
                  NODE_OPTIONS: '--max-old-space-size=5120 --max_semi_space_size=128 --dns-result-order=ipv4first --experimental-global-webcrypto'
                  NX_AGENT_NAME: Agent ${{ matrix.agent }} - 4 Core
              run: pnpm exec nx-cloud start-agent --targets="!ci-e2e-test,!ci-shard-integration-test*,!ci-shard-unit-test*,!lint"
            - name: Find and prepare failed tests
              id: find-failed-tests
              run: |
                  # Find all failed-tests.json files recursively in the coverage directory
                  FAILED_TEST_FILES=$(find ./coverage -name "failed-tests.json" -type f 2>/dev/null || echo "")
                  if [ -n "$FAILED_TEST_FILES" ]; then
                    # Create directory for all failed test files
                    mkdir -p ./collected-failed-tests
                    
                    # Process each failed test file
                    for FILE in $FAILED_TEST_FILES; do
                      DIR_PATH=$(dirname "$FILE")
                      # Create a unique name based on the path
                      NORMALIZED_PATH=$(echo "$DIR_PATH" | sed 's/[\/\.]/-/g')
                      HASH=$(openssl rand -hex 6)
                      DEST_FILE="./collected-failed-tests/failed-tests-${NORMALIZED_PATH}-${HASH}.json"
                      
                      # Copy the file with the unique name
                      cp "$FILE" "$DEST_FILE"
                      echo "Found and copied $FILE to $DEST_FILE"
                    done
                    
                    echo "FOUND_FAILED_TESTS=true" >> $GITHUB_ENV
                  else
                    echo "No failed-tests.json files found in coverage directory"
                    echo "FOUND_FAILED_TESTS=false" >> $GITHUB_ENV
                  fi

                  # Also check for root level failed-tests.json for backward compatibility
                  if [ -f ./failed-tests.json ]; then
                    mkdir -p ./collected-failed-tests
                    HASH=$(openssl rand -hex 6)
                    cp ./failed-tests.json ./collected-failed-tests/failed-tests-root-${HASH}.json
                    echo "FOUND_FAILED_TESTS=true" >> $GITHUB_ENV
                    echo "Found and copied root failed-tests.json"
                  fi
            - name: Save failed tests
              if: env.FOUND_FAILED_TESTS == 'true'
              uses: actions/upload-artifact@b4b15b8c7c6ac21ea08fcf65892d2ee8f75cf882 # v4.4.3
              continue-on-error: true
              with:
                  name: failed-tests-${{ github.job }}-${{ matrix.agent }}
                  path: ./collected-failed-tests/*.json
                  retention-days: 1

    nx-dte-agents-16:
        name: NX Agent ${{ matrix.agent }} - 16 Core
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-16c-64g
        needs: [install-dependencies]
        if: |-
            vars.NX_AGENTS != 'enabled' &&
            !(github.event_name == 'pull_request' && startsWith(github.event.pull_request.title, 'breakfix'))
        strategy:
            matrix:
                agent: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        env:
            jest-cache-name: nx-agent-jest-cache-${{ matrix.agent }}-16-core
            esbuild-cache-name: nx-agent-esbuild-cache-${{ matrix.agent }}-16-core
            CLICKUP_ESBUILD_CACHE_DIR: /tmp/.esbuild-cache
            NX_CI_EXECUTION_ENV: 'Build-Test DTE'
        steps:
            - name: Check run attempt
              if: ${{ needs.install-dependencies.outputs.run-attempt != github.run_attempt }}
              run: |
                  echo 'To re-run this pipeline, please use "Re-run all jobs" instead of "Re-run failed jobs"'
                  exit 1
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Pre Start Docker Compose
              shell: bash
              run: docker compose --profile=ci up --detach --quiet-pull &
            - name: Install pytest, ruff, and uv # https://github.com/actions/setup-python/blob/main/docs/advanced-usage.md#caching-packages
              run: |
                  pipx install pytest && pipx install ruff && pipx install uv
                  echo $HOME/.local/bin >> $GITHUB_PATH
            - uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5.5.0
              with:
                  python-version: '3.13'
            - name: Setup Java
              uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
              with:
                  distribution: 'corretto'
                  java-version: '21'
            - name: Setup Gradle
              uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1
            - run: go version
            - run: java --version
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies****.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Pull docker-compose images for --profile=ci
              run: docker compose --profile=ci pull --include-deps
              timeout-minutes: 5
              continue-on-error: true
            - name: Wait For Docker Compose To Be Ready
              uses: ./.github/actions/docker-compose-run
              with:
                  command: pnpm run ci:test:docker-compose:wait
            - name: Start Nx Agent ${{ matrix.agent }}
              timeout-minutes: 40
              env:
                  NXCACHE_AWS_DISABLE: false
                  AWS_REGION: us-west-2
                  AWS_ACCESS_KEY_ID: test
                  AWS_SECRET_ACCESS_KEY: test
                  # dns-result-order=ipv4first is needed to address a breaking change in node 17 where localhost resolves to ipv6 first instead of ipv4
                  # Somehow this makes testcontainers randomly flake in the CI when running these tests
                  NODE_OPTIONS: '--max-old-space-size=4196 --max_semi_space_size=128 --dns-result-order=ipv4first --experimental-global-webcrypto'
                  NX_AGENT_NAME: Agent ${{ matrix.agent }} - 16 Core
              run: pnpm exec nx-cloud start-agent --targets="!ci-shard-functional-test*,!test"
            - name: Find and prepare failed tests
              id: find-failed-tests
              run: |
                  # Find all failed-tests.json files recursively in the coverage directory
                  FAILED_TEST_FILES=$(find ./coverage -name "failed-tests.json" -type f 2>/dev/null || echo "")
                  if [ -n "$FAILED_TEST_FILES" ]; then
                    # Create directory for all failed test files
                    mkdir -p ./collected-failed-tests
                    
                    # Process each failed test file
                    for FILE in $FAILED_TEST_FILES; do
                      DIR_PATH=$(dirname "$FILE")
                      # Create a unique name based on the path
                      NORMALIZED_PATH=$(echo "$DIR_PATH" | sed 's/[\/\.]/-/g')
                      HASH=$(openssl rand -hex 6)
                      DEST_FILE="./collected-failed-tests/failed-tests-${NORMALIZED_PATH}-${HASH}.json"
                      
                      # Copy the file with the unique name
                      cp "$FILE" "$DEST_FILE"
                      echo "Found and copied $FILE to $DEST_FILE"
                    done
                    
                    echo "FOUND_FAILED_TESTS=true" >> $GITHUB_ENV
                  else
                    echo "No failed-tests.json files found in coverage directory"
                    echo "FOUND_FAILED_TESTS=false" >> $GITHUB_ENV
                  fi

                  # Also check for root level failed-tests.json for backward compatibility
                  if [ -f ./failed-tests.json ]; then
                    mkdir -p ./collected-failed-tests
                    HASH=$(openssl rand -hex 6)
                    cp ./failed-tests.json ./collected-failed-tests/failed-tests-root-${HASH}.json
                    echo "FOUND_FAILED_TESTS=true" >> $GITHUB_ENV
                    echo "Found and copied root failed-tests.json"
                  fi
            - name: Save failed tests
              if: env.FOUND_FAILED_TESTS == 'true'
              uses: actions/upload-artifact@b4b15b8c7c6ac21ea08fcf65892d2ee8f75cf882 # v4.4.3
              continue-on-error: true
              with:
                  name: failed-tests-${{ github.job }}-${{ matrix.agent }}
                  path: ./collected-failed-tests/*.json
                  retention-days: 1

    crm-legacy-service-integration-test:
        name: CRM Legacy Service Integration Test
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-amd64-8c-32g
        needs: [install-dependencies]
        continue-on-error: true
        env:
            jest-cache-name: crm-legacy-service-integration-test-jest-cache
            esbuild-cache-name: crm-legacy-service-integration-test
            CLICKUP_ESBUILD_CACHE_DIR: /tmp/.esbuild-cache
            NX_CLOUD_DISTRIBUTED_EXECUTION: true # this disables from running in DTE
        if: |-
            !(github.event_name == 'pull_request' && startsWith(github.event.pull_request.title, 'breakfix'))
        steps:
            # run curl command to get public ip address - see: CLK-551291
            - name: Get Public IP Address
              run: |-
                  echo "Public IP Address: $(curl -s https://api.ipify.org -m 5)"
              continue-on-error: true
            - name: Get Public IPv6 Address
              run: |-
                  echo "Public IPv6 Address: $(curl -s https://api64.ipify.org -m 5)"
              continue-on-error: true
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies****.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Restore Jest Cache
              id: cache-jest
              uses: tespkg/actions-cache@cba095d7af782a955b8f4fa13396fbf0ab62bd4b # v1.7.1
              timeout-minutes: 1
              continue-on-error: true
              with:
                  bucket: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  path: .jest-cache
                  accessKey: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  secretKey: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  use-fallback: false
                  key: ${{ runner.os }}-test-${{ env.jest-cache-name }}-${{ github.ref_name }}-${{ matrix.agent }}-${{ github.sha }}
                  restore-keys: |
                      ${{ runner.os }}-test-${{ env.jest-cache-name }}-${{ github.ref_name }}-${{ matrix.agent }}-
                      ${{ runner.os }}-test-${{ env.jest-cache-name }}-${{ github.base_ref }}-${{ matrix.agent }}-${{ github.event.pull_request.base.sha }}
                      ${{ runner.os }}-test-${{ env.jest-cache-name }}-${{ github.base_ref }}-${{ matrix.agent }}-
            - name: Restore esbuild Cache
              id: cache-esbuild
              uses: tespkg/actions-cache@cba095d7af782a955b8f4fa13396fbf0ab62bd4b # v1.7.1
              timeout-minutes: 1
              continue-on-error: true
              with:
                  bucket: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  path: ${{ env.CLICKUP_ESBUILD_CACHE_DIR }}
                  accessKey: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  secretKey: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  use-fallback: false
                  key: ${{ runner.os }}-test-${{ env.esbuild-cache-name}}-${{ github.ref_name }}-${{ github.sha }}
                  restore-keys: |
                      ${{ runner.os }}-test-${{ env.esbuild-cache-name }}-${{ github.ref_name }}
                      ${{ runner.os }}-test-${{ env.esbuild-cache-name }}-${{ github.base_ref }}-${{ github.event.pull_request.base.sha }}
                      ${{ runner.os }}-test-${{ env.esbuild-cache-name }}-${{ github.base_ref }}
            - name: Pull docker-compose images for --profile=ci
              run: docker compose --profile=ci pull --include-deps
              timeout-minutes: 5
              continue-on-error: true
            - name: Run CRM Legacy Service Integration Tests
              timeout-minutes: 40
              env:
                  NXCACHE_AWS_DISABLE: false
                  AWS_REGION: us-west-2
                  AWS_ACCESS_KEY_ID: test
                  AWS_SECRET_ACCESS_KEY: test
                  # dns-result-order=ipv4first is needed to address a breaking change in node 17 where localhost resolves to ipv6 first instead of ipv4
                  # Somehow this makes testcontainers randomly flake in the CI when running these tests
                  NODE_OPTIONS: '--max-old-space-size=4196 --max_semi_space_size=128 --dns-result-order=ipv4first --experimental-global-webcrypto'
              run: pnpm exec nx run crm-legacy-service:integration-test --no-cloud --maxWorkers=6

    build:
        name: Build
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-4c-16g
        needs: [install-dependencies]
        permissions:
            contents: read
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Set build hash
              id: package-build-hash
              env:
                  HASH: ${{ hashFiles('pnpm-lock.yaml', 'src', 'tsconfig.json', 'tsconfig.base.json', 'tools/esbuild/src') }}
              run: echo "hash=${HASH}" >> $GITHUB_OUTPUT
            - name: Restore build if rebuild not needed
              id: cache-build
              uses: tespkg/actions-cache@cba095d7af782a955b8f4fa13396fbf0ab62bd4b # v1.7.1
              timeout-minutes: 1
              continue-on-error: true
              env:
                  cache-name: cache-build
              with:
                  bucket: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  path: dist
                  accessKey: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  secretKey: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
                  use-fallback: false
                  key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ steps.package-build-hash.outputs.hash }}
            - name: Setup node and dependencies
              # This condition is the same as for the `Run build` step. No need to run if the build is going to be skipped
              if: ${{ steps.cache-build.outputs.cache-hit != 'true' || env.SHOULD_SKIP_MONOLITH_TEST != 'true' }}
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies****.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Setup Java
              uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
              with:
                  distribution: 'corretto'
                  java-version: '21'
              timeout-minutes: 1
            - name: Setup Gradle
              uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1
            - name: Run build
              if: ${{ steps.cache-build.outputs.cache-hit != 'true' || env.SHOULD_SKIP_MONOLITH_TEST != 'true' }}
              run: pnpm exec nx run @clickup/source:build
            - name: Zip everything
              run: tar --exclude='.git' -zcf "/tmp/build-$GITHUB_SHA.tar.gz" ./dist
            - name: Upload build.tar.gz
              uses: actions/upload-artifact@6f51ac03b9356f520e9adb1b1b7802705f340c2b # v4.5.0
              with:
                  name: build-${{ github.sha }}.tar.gz
                  path: /tmp/build-${{ github.sha }}.tar.gz
                  retention-days: 1
                  if-no-files-found: error

    nest-build-budgets:
        name: Nest Build Budgets
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-amd64-8c-32g
        needs: [install-dependencies]
        permissions:
            contents: read
        env:
            NXCACHE_AWS_DISABLE: false
            NODE_OPTIONS: '--max-old-space-size=16384 --dns-result-order=ipv4first'
        steps:
            - name: Checkout (Non-master)
              if: github.event_name != 'push' || github.ref_name != 'master'
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  fetch-depth: 0
                  filter: tree:0
            - name: Checkout (master)
              if: github.event_name == 'push' && github.ref_name == 'master'
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                  # Check out the repo with the machine user token so that we can directly commit the changes to the protected branch with the EndBug/add-and-commit action
                  token: ${{ secrets.MACHINE_USER_GITHUB_TOKEN }}
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies****.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Check bundle budgets
              # Only check bundle budgets on PRs to main branches, not on PRs that promote from one main branch to another
              if: github.event_name == 'pull_request' && github.head_ref != 'staging'
              run: pnpm ci:budget:check
            - name: Update bundle budgets
              if: github.event_name == 'push' && github.ref_name == 'master'
              run: pnpm ci:budget:update:master
            - name: Commit and push bundle budget changes
              uses: EndBug/add-and-commit@a94899bca583c204427a224a7af87c02f9b325d5 # v9.1.4
              if: github.event_name == 'push' && github.ref_name == 'master'
              with:
                  default_author: github_actions
                  message: 'chore(bot): update budgets [ci skip]'
                  add: 'apps/**/.size-limit.json'
                  pull: '--no-rebase' # probably not needed, but addressing a breaking change in v8 of the action

    public-api-test:
        name: Public API Test
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-4c-16g
        needs: [build]
        # env.SHOULD_SKIP_MONOLITH_TEST is not available at job.if level, so we duplicate the condition here
        if: |-
            !(contains(github.event.pull_request.labels.*.name, 'generated-new-service') || (startsWith(github.head_ref, 'smartling-') && github.event.pull_request.user.login == 'smartling-github-connector[bot]'))
            && !(github.event_name == 'pull_request' && startsWith(github.event.pull_request.title, 'breakfix'))
        permissions:
            contents: read
        env:
            NX_CI_EXECUTION_ENV: 'Build-Test Non-DTE'
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies****.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Pre Start Docker Compose
              shell: bash
              run: pnpm run ci:test:docker-compose:up &
            - name: Wait For Docker Compose To Be Ready
              uses: ./.github/actions/docker-compose-run
              with:
                  command: pnpm run ci:test:docker-compose:wait
            - name: Download build.tar.gz
              uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
              with:
                  name: build-${{ github.sha }}.tar.gz
                  path: /tmp/
            - name: Unpack
              run: tar --overwrite --strip-components=1 -zxf "/tmp/build-$GITHUB_SHA.tar.gz"
            - name: Run Newman Public Api Integration Tests
              run: pnpm exec nx-cloud record -- pnpm run ci:integration-test:public-api
              env:
                  AWS_ACCESS_KEY_ID: ${{ secrets.TRAVISCI_ACCESS_KEY_ID }}
                  AWS_SECRET_ACCESS_KEY: ${{ secrets.TRAVISCI_SECRET_ACCESS_KEY }}

    workspace-migration-test:
        name: Workspace Migration Tests
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-8c-16g
        # do not run unless the PR is targeting wms-master, wms-staging or wms-production base branch
        if: github.event_name == 'pull_request' && (github.base_ref == 'wms-master' || github.base_ref == 'wms-staging' || github.base_ref == 'wms-production')
        needs: [build]
        permissions:
            contents: read
            actions: read
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
              with:
                  fetch-depth: 0
                  filter: tree:0
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies****.20
              timeout-minutes: 10
              id: setup-node-and-dependencies
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  nx-setup: true
                  nx-main-branch: master
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Check if affected
              id: affected
              shell: bash
              run: |
                  if grep -q "migration-workflow-service" <<< "$(pnpm nx show projects --affected)"; then
                    echo "is-affected=true" >> $GITHUB_OUTPUT
                  fi
            - name: Start Docker Compose
              if: steps.affected.outputs.is-affected == 'true'
              run: pnpm run ci:docker:wms-req:up
              timeout-minutes: 15
            - name: Download build.tar.gz
              if: steps.affected.outputs.is-affected == 'true'
              uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
              with:
                  name: build-${{ github.sha }}.tar.gz
                  path: /tmp/
            - name: Unpack
              if: steps.affected.outputs.is-affected == 'true'
              run: tar --overwrite --strip-components=1 -zxf "/tmp/build-$GITHUB_SHA.tar.gz"
            - name: Run workspace migration tests
              if: steps.affected.outputs.is-affected == 'true'
              run: pnpm run ci:test:workspace-migration
              env:
                  NXCACHE_AWS_DISABLE: false
                  AWS_REGION: us-west-2
                  AWS_ACCESS_KEY_ID: test
                  AWS_SECRET_ACCESS_KEY: test

    fvt-test:
        name: FVT Test
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-build-dc-arm64-4c-16g
        needs: [build]
        # env.SHOULD_SKIP_MONOLITH_TEST is not available at job.if level, so we duplicate the condition here
        if: |-
            !(contains(github.event.pull_request.labels.*.name, 'generated-new-service') || (startsWith(github.head_ref, 'smartling-') && github.event.pull_request.user.login == 'smartling-github-connector[bot]'))
            && !(github.event_name == 'pull_request' && startsWith(github.event.pull_request.title, 'breakfix'))
        timeout-minutes: 10
        permissions:
            contents: read
        env:
            NX_CI_EXECUTION_ENV: 'Build-Test Non-DTE'
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Setup node and dependencies
              uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies****.20
              timeout-minutes: 10
              with:
                  package-manager: 'pnpm'
                  registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
                  use-s3: true
                  s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
                  s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
                  s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
                  s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
                  s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
            - name: Pre Start Docker Compose
              shell: bash
              run: pnpm run ci:test:docker-compose:up &
            - name: Wait For Docker Compose To Be Ready
              uses: ./.github/actions/docker-compose-run
              with:
                  command: pnpm run ci:test:docker-compose:wait
            - name: Download Build Artifact
              uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
              with:
                  name: build-${{ github.sha }}.tar.gz
                  path: /tmp/
            - name: Unpack
              run: tar --overwrite --strip-components=1 -zxf "/tmp/build-$GITHUB_SHA.tar.gz"
            - name: Run Mocha Integration Tests
              uses: ./.github/actions/run-monolith-tests
              with:
                  timeout_minutes: 15
                  max_attempts: 1
                  command: pnpm exec nx-cloud record -- pnpm run ci:integration-test:mocha
                  aws_access_key_id: ${{ secrets.TRAVISCI_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.TRAVISCI_SECRET_ACCESS_KEY }}

    # Skipping code coverage : INC-1148
    # upload-code-coverage:
    #     name: Upload Code Coverage
    #     needs: [nx-dte-main, should-skip-monolith-tests]
    #     if: |-
    #         (always() && !cancelled()) &&
    #         !contains(needs.*.result, 'failure') &&
    #         !contains(needs.*.result, 'cancelled')
    #         && !(github.event_name == 'pull_request' && startsWith(github.event.pull_request.title, 'breakfix'))
    #         && needs.check-pr-type.outputs.PR_TYPE != 'config'
    #     runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-amd64-2c-4g
    #     permissions:
    #         contents: read
    #     steps:
    #         - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    #           name: Checkout
    #         - name: Setup node and dependencies
    #           uses: time-loop/github-actions/dist/setup-node-and-dependencies@setup-node-and-dependencies****.20
    #           timeout-minutes: 5
    #           with:
    #               package-manager: 'pnpm'
    #               registry-auth-token: ${{ secrets.ALL_PACKAGE_READ_TOKEN }}
    #               use-s3: true
    #               s3-actions-cache-bucket-name: ${{ vars.R2_ACTIONS_CACHE_BUCKET_NAME }}
    #               s3-actions-cache-bucket-region: ${{ vars.R2_ACTIONS_CACHE_BUCKET_REGION }}
    #               s3-actions-cache-bucket-endpoint: ${{ vars.R2_ACTIONS_CACHE_BUCKET_HOSTNAME }}
    #               s3-actions-cache-bucket-access-key-id: ${{ vars.R2_ACTIONS_CACHE_BUCKET_ACCESS_KEY_ID }}
    #               s3-actions-cache-bucket-secret-access-key: ${{ secrets.R2_ACTIONS_CACHE_BUCKET_SECRET_ACCESS_KEY }}
    #         - name: Download Nx unit test coverage reports
    #           uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
    #           continue-on-error: true
    #           id: download-all-nx-coverage
    #           with:
    #               name: nx-coverage-reports
    #               path: ./nx-coverage-reports
    #         - name: Extract NX test coverage reports
    #           if: steps.download-all-nx-coverage.outputs.download-path
    #           run: |
    #               tar -xzf ./nx-coverage-reports/coverage.tar.gz -C ./nx-coverage-reports
    #               rm -rf ./nx-coverage-reports/coverage.tar.gz
    #         - name: Merge monolith reports with nx monolith test reports
    #           if: steps.download-all-nx-coverage.outputs.download-path && needs.should-skip-monolith-tests.outputs.should-skip-monolith-tests != 'true'
    #           run: |
    #               pnpm exec istanbul-merge --out ./coverage/monolith-combined.merge.json ./coverage/nx-coverage-reports/coverage/monolith-test-jest/**/*.json \
    #               && mv ./coverage/monolith-combined.merge.json ./coverage/monolith-combined.json
    #         - name: Delete separate monolith coverage reports
    #           if: needs.should-skip-monolith-tests.outputs.should-skip-monolith-tests != 'true'
    #           run: rm -rf .nyc_output
    #         - name: Upload monolith coverage to Codecov
    #           if: needs.should-skip-monolith-tests.outputs.should-skip-monolith-tests != 'true'
    #           uses: codecov/codecov-action@1e68e06f1dbfde0e4cefc87efeba9e4643565303 # v5.1.2
    #           with:
    #               token: ${{ secrets.CODECOV_TOKEN }}
    #               files: ./coverage/monolith-combined.json
    #               flags: monolith
    #         - name: Upload Nx unit test coverage to Codecov
    #           uses: time-loop/github-actions/dist/upload-codecov-flags@upload-codecov-flags+5.0.10
    #           # Only run this step if there were some Nx coverage reports to upload
    #           if: steps.download-all-nx-coverage.outputs.download-path
    #           env:
    #               FORCE_COLOR: 3 # fix terminal color output
    #               CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
    #           with:
    #               nx-coverage-directory: ./nx-coverage-reports/coverage/unit
    #         - name: Upload Nx e2e test coverage to Codecov
    #           uses: time-loop/github-actions/dist/upload-codecov-flags@upload-codecov-flags+5.0.10
    #           # Only run this step if there were some Nx coverage reports to upload
    #           if: steps.download-all-nx-coverage.outputs.download-path
    #           env:
    #               FORCE_COLOR: 3 # fix terminal color output
    #               CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
    #           with:
    #               nx-coverage-directory: nx-coverage-reports/coverage/ci-e2e-test
    #         - name: Upload Nx integration test coverage to Codecov
    #           uses: time-loop/github-actions/dist/upload-codecov-flags@upload-codecov-flags+5.0.10
    #           # Only run this step if there were some Nx coverage reports to upload
    #           if: steps.download-all-nx-coverage.outputs.download-path
    #           env:
    #               FORCE_COLOR: 3 # fix terminal color output
    #               CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
    #           with:
    #               nx-coverage-directory: nx-coverage-reports/coverage/ci-shard-integration-test
    #         - name: Delete Nx coverage reports after upload
    #           if: steps.download-all-nx-coverage.outputs.download-path
    #           uses: geekyeggo/delete-artifact@f275313e70c08f6120db482d7a6b98377786765b # v5.1.0
    #           with:
    #               name: nx-coverage-reports
    #               failOnError: false

    stop-non-dte-ci-run:
        name: Stop NX Non-DTE CI Run
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        if: vars.USE_BUILD_TEST_NX_DTE == 'true' && always()
        permissions:
            contents: read
            actions: read
        needs: [public-api-test, fvt-test]
        env:
            NX_CI_EXECUTION_ENV: 'Build-Test Non-DTE'
        steps:
            - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              name: Checkout
            - name: Setup Node
              uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
            - name: Stop the CI run
              continue-on-error: true
              run: npx --yes nx-cloud complete-ci-run
    # Only sets the output to 'true' when all dependent tests have passed.
    validate-success:
        name: Validate Build-Test Success
        # Dependent on all test jobs
        needs: [nx-dte-main, fvt-test, public-api-test, nest-build-budgets]
        if: |
            (always() && !cancelled()) &&
            !contains(needs.*.result, 'failure') &&
            !contains(needs.*.result, 'cancelled')
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        outputs:
            success: ${{ steps.setoutput.outputs.success }}
        steps:
            - id: setoutput
              run: echo "success=true" >> $GITHUB_OUTPUT

    # Delete build artifacts after the pipeline has run to save space
    delete-build-artifacts:
        name: Delete Build Artifacts
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        needs: [build, public-api-test, fvt-test, workspace-migration-test]
        if: |
            (always() && !cancelled()) &&
            !contains(needs.*.result, 'failure') &&
            !contains(needs.*.result, 'cancelled')
        steps:
            - name: Delete build artifacts
              uses: geekyeggo/delete-artifact@f275313e70c08f6120db482d7a6b98377786765b # v5.1.0
              with:
                  name: build-${{ github.sha }}.tar.gz
                  failOnError: false

    # Only sets the output to 'true' when all dependent tests have passed.
    validate-config-success:
        name: Validate Config Build-Test Success
        # Dependent on all test jobs
        needs: [nx-config-diff]
        if: |
            (always() && !cancelled()) &&
            !contains(needs.*.result, 'failure')
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        outputs:
            success: ${{ steps.setoutput.outputs.success }}
        steps:
            - id: setoutput
              run: echo "success=true" >> $GITHUB_OUTPUT

    # Used so we only need to depend on one job from the Build-Test pipeline in GitHub Branch Protection Rules.
    # Helps minimize required maintenance when changing the pipeline.
    notify-success:
        name: Build-Test Pipeline Succeeded
        # Dependent on all test jobs
        needs: [validate-success, validate-config-success]
        if: always() # Always run, so we never skip this required check
        runs-on: ${{ vars.RUNNER_FAILOVER_PREFIX }}-default-arm64-1c-2g
        steps:
            - name: Note Pipeline Success
              # TODO: We should do something useful here in the future. DD events?
              run: |
                  msgPrefix="Build pipeline \#${{ github.run_number }} finished"
                  passed="${{ needs.validate-success.outputs.success == 'true' && needs.validate-config-success.outputs.success == 'true' }}"
                  if [[ "$passed" == "true" ]]; then
                    echo "${msgPrefix}, artifact passed all tests."
                    exit 0
                  else
                    echo "${msgPrefix}, but with failures."
                    if [[ ${{ github.event_name }} == 'schedule' ]]; then
                      curl -X POST -H "Authorization: ${{ secrets.CLICKUP_API_TOKEN }}" -H "Content-Type: application/json" -d '{"content": "Build pipeline ${{ github.run_number }} finished, but with failures."}' https://api.clickup-stg.com/api/v2/view/${{ vars.CLICKUP_CHAT_ID_ENGPROD_PUBLIC }}/comment
                      exit 1
                    else
                      exit 1
                    fi
                  fi
